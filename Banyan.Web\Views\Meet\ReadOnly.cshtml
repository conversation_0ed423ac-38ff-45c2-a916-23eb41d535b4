﻿@using Banyan.Domain
@{
    Layout = "/Views/Shared/_Layout.cshtml";
    List<Banyan.Domain.Role> classList = (List<Banyan.Domain.Role>)ViewData["rolelist"];
    List<Banyan.Domain.Member> memberList = (List<Banyan.Domain.Member>)ViewData["creatorList"];
    List<Banyan.Domain.Member> staffList = (List<Banyan.Domain.Member>)ViewData["staffList"];
    var id = ViewData["Id"];
    var member = ViewData["manager"] as Banyan.Domain.Member;
}
<link href="~/content/css/views/meet/meet-edit.css" rel="stylesheet" />
<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li><a href="@(Url.Action("Meets","Meet"))">会议管理</a></li>
                <li> @((int)ViewData["Id"] == 0 ? "新建" : "查看")会议</li>
            </ol>
        </div>
        <div class="block-content block-content-full" style="display:none;" id="page-app">
            <form class="form-horizontal" method="post" id="project-form" name="project-form">
                <input disabled type="hidden" name="id" id="id" v-model="model.Id" />
                <input disabled type="hidden" name="Creator" value="@(member.Id)" />
                <input disabled type="hidden" name="status" id="status" v-model="model.Status" />
                <div class="form-group">
                    <label class="col-md-2 control-label " for="title">会议名称</label>
                    <div class="col-md-6 content-line">
                        {{model.Title}}
                    </div>
                </div>

                @*<div class="form-group">
                        <label class="col-md-2 control-label " for="manager">会议负责人</label>
                        <div class="col-md-6">
                            <select disabled class="form-control select2" id="manager" size="1">
                                @foreach (var mi in memberList)
                                {
                                    <option value="@(mi.RealName)">@(mi.RealName)</option>
                                }
                            </select>
                        </div>
                    </div>*@

                <div class="form-group">
                    <label class="col-md-2 control-label " for="StartTime">开始时间</label>
                    <div class="col-md-4 content-line">
                        {{model.StartTime}}
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label " for="duration">预计时长</label>
                    <div class="col-md-6 content-line">
                        {{model.Duration}}分钟
                    </div>
                </div>


                <div class="form-group">
                    <label class="col-md-2 control-label " for="InternalPTCP">参会人员</label>
                    <div class="col-md-6 content-line">
                        {{internalPTCP}}
                        @*<span v-if="model.Participant">
                            | 外部人员：{{model.Participant}}
                        </span>*@
                    </div>
                </div>
                <div class="form-group" v-if="model.Summary">
                    <label class="col-md-2 control-label" for="duration">会议说明</label>
                    <div class="col-md-6 content-line">
                        {{model.Summary}}
                    </div>
                </div>
                @*<div class="form-group">
                        <label class="col-md-2 control-label" for="Participant">外部参会人</label>
                        <div class="col-md-6">
                            <input disabled class="form-control" type="text" id="Participant" name="Participant" rows="3" data-rule="required;" v-model="model.Participant" />
                        </div>
                    </div>*@
                <div class="form-group">
                    <label class="col-md-2 control-label " for="searchType">讨论事项</label>

                    <div class="tab-content block-content-mini" v-if="attachLogin.length > 0 || attachDD.length > 0 || attachResearch.length > 0 || attachOther.length > 0">
                        <div class="col-md-8">
                            <table class="table table-striped" style="width:100%;">
                                <thead>
                                    <tr>
                                        <th class="hidden-xs" style="width: 15%;">类型</th>
                                        <th>主题/链接</th>
                                        <th>主讲人</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(item,index) in attachLogin" style="width:100%;">
                                        <td class="hidden-xs" style="width: 15%;">
                                            <span v-if="item.SourceType == 5" class="label label-info">其他项目报告</span>
                                            <span v-else class="label label-info">Login项目</span>
                                        </td>
                                        <td v-if="item.SourceId != -1"><a class="link" :data-idx="item.SourceId" data-type="login" v-on:click="preview">{{item.Name}}</a></td>
                                        <td v-else>{{item.Name}}</td>
                                        <td>
                                            {{item.Speakers}}
                                        </td>

                                    </tr>

                                    <tr v-for="(item,index) in attachDD" style="width:100%;">
                                        <td class="hidden-xs" style="width: 15%;">
                                            <span class="label label-info">DD报告</span>
                                        </td>
                                        <td><a class="link" :data-idx="item.SourceId" data-type="dd" v-on:click="preview">{{item.Name}}</a></td>
                                        <td>
                                            {{item.Speakers}}
                                        </td>

                                    </tr>

                                    <tr v-for="(item,index) in attachResearch" style="width:100%;">
                                        <td class="hidden-xs" style="width: 15%;">
                                            <span class="label label-info">行业研究</span>
                                        </td>
                                        <td><a class="link" :data-idx="item.SourceId" data-type="research" v-on:click="preview">{{item.Name}}</a></td>
                                        <td>
                                            {{item.Speakers}}
                                        </td>

                                    </tr>

                                    <tr v-for="(item,index) in attachOther" style="width:100%;">
                                        <td class="hidden-xs" style="width: 15%;">
                                            <span class="label label-info">其他事项</span>
                                        </td>
                                        <td>{{item.Name}}</td>
                                        <td>
                                            {{item.Speakers}}
                                        </td>

                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="form-group" v-if="(dataListBP && dataListBP.length > 0) || (dataListDD && dataListDD.length > 0) || (fileList && fileList.length > 0)   ">
                    <label class="col-md-2 control-label" for="NextStep2">会议附件 </label>
                    <div class="block-content tab-content block-content-mini">
                        <div  v-if="model.IsOperate" v-for="(itemRecord, idxRecord) in dataListBP" class="demo-gallery">
                            <span>【会议纪要】{{BP[idxRecord].AtName}}</span>
                            <br />
                            <ul class="lightgalleryDD list-unstyled row">
                                <li class="col-xs-6 col-sm-4 col-md-3" v-for="(item, index) in itemRecord" data-responsive="" :data-src="item" data-sub-html="">
                                    <a href="javascript:;">
                                        <img class="img-responsive" :src="item">
                                        <div class="demo-gallery-poster">
                                            <img src="/content/js/plugins/lightgallery/img/zoom.png" />
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div v-for="(itemDD, idxDD) in dataListDD" class="demo-gallery">
                            <span>{{DD[idxDD].AtName}}</span>
                            <br />
                            <ul class="lightgalleryDD list-unstyled row">
                                <li class="col-xs-6 col-sm-4 col-md-3" v-for="(item, index) in itemDD" data-responsive="" :data-src="item" data-sub-html="">
                                    <a href="javascript:;">
                                        <img class="img-responsive" :src="item">
                                        <div class="demo-gallery-poster">
                                            <img src="/content/js/plugins/lightgallery/img/zoom.png" />
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <label class="col-md-2 control-label" for="NextStep3"></label>
                    <div v-if="fileList && fileList.length > 0" class="block-content tab-content block-content-mini">
                        <div>
                            <div v-for="(item,index) in fileList" style="width:100%;">
                                <span> <a :href="item.AtUrl">{{ item.AtName }}</a> </span>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="form-group" v-if="model.IsOperate">
                    <label class="col-md-2 control-label" for="Comment">会议纪要</label>
                    <div class="col-md-6 content-line">
                        <div id="posit">
                            <div style="white-space: pre-wrap">{{model.Comment}}</div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-8 col-md-offset-2">
                        <button class="btn  btn-warning" @@click="saveData(-1)" type="button">返回</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section scripts{
    <script src="/content/js/plugins/jquery.ui.widget.js"></script>
    <script src="/content/js/plugins/jquery.iframe-transport.js"></script>
    <script src="/content/js/plugins/jquery.fileupload.js"></script>

    <link href="/content/js/plugins/select2/select2.min.css" rel="stylesheet" />
    <link href="/content/js/plugins/select2/select2-bootstrap.min.css" rel="stylesheet" />
    <script type="text/javascript" src="/content/js/plugins/select2/select2.min.js"></script>
    <script src="~/Content/js/plugins/city-select/js/lazyload-min.js"></script>

    <script src="/content/js/core/ajaxfileupload.js"></script>
    <link href="~/Content/js/plugins/lightgallery/css/normalize.css" rel="stylesheet" />
    <link href="~/Content/js/plugins/lightgallery/css/lightgallery.min.css" rel="stylesheet" />

    <script src="~/Content/js/plugins/lightgallery/js/picturefill.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lightgallery.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-fullscreen.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-thumbnail.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-video.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-autoplay.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-zoom.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-hash.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-pager.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/jquery.mousewheel.min.js"></script>
    <script type="text/javascript">
        editing = @((int)MeetStatus.editing)
        id = @(ViewData["Id"])
    </script>
    <script src="~/Content/js/views/meet/edit-common-v2.js">
    </script>
}
