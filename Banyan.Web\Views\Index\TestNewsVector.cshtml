@using Banyan.Domain
@using Banyan.Apps
@using Newtonsoft.Json
@{
    ViewBag.Name = "新闻向量化字段测试";
    Layout = "/Views/Shared/_Layout.cshtml";
    
    // 获取一条新闻记录用于测试
    var newsBLL = new NewsBLL();
    var news = newsBLL.GetList("").FirstOrDefault();
}

<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li><a href="/Index/NewsVector">新闻向量化浏览</a></li>
                <li>新闻向量化字段测试</li>
            </ol>
        </div>
        <div class="block-content">
            <div class="alert alert-info">
                <h4>新闻向量化字段测试</h4>
                <p>此页面用于测试新闻向量化相关字段是否已正确添加到数据库中。</p>
            </div>
            
            @if (news != null)
            {
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">新闻基本信息</h3>
                    </div>
                    <div class="panel-body">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 200px;">字段名</th>
                                <th>字段值</th>
                            </tr>
                            <tr>
                                <td>ID</td>
                                <td>@news.Id</td>
                            </tr>
                            <tr>
                                <td>标题</td>
                                <td>@news.Title</td>
                            </tr>
                            <tr>
                                <td>发布时间</td>
                                <td>@news.PubTime.ToString("yyyy-MM-dd HH:mm:ss")</td>
                            </tr>
                            <tr>
                                <td>来源</td>
                                <td>@news.Source</td>
                            </tr>
                            <tr>
                                <td>分类</td>
                                <td>@news.Classify</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <h3 class="panel-title">向量化相关字段</h3>
                    </div>
                    <div class="panel-body">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 200px;">字段名</th>
                                <th>字段值</th>
                                <th style="width: 200px;">字段状态</th>
                            </tr>
                            <tr>
                                <td>NewsVector</td>
                                <td>
                                    @if (!string.IsNullOrEmpty(news.NewsVector))
                                    {
                                        <div style="max-height: 100px; overflow-y: auto;">
                                            @news.NewsVector.Substring(0, Math.Min(news.NewsVector.Length, 200))
                                            @if (news.NewsVector.Length > 200)
                                            {
                                                <span>...</span>
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        <span class="text-muted">未设置</span>
                                    }
                                </td>
                                <td>
                                    @if (!string.IsNullOrEmpty(news.NewsVector))
                                    {
                                        <span class="label label-success">字段存在且有值</span>
                                    }
                                    else
                                    {
                                        <span class="label label-warning">字段存在但无值</span>
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td>VectorStatus</td>
                                <td>
                                    @{
                                        string statusText = "";
                                        switch (news.VectorStatus)
                                        {
                                            case 0:
                                                statusText = "未向量化";
                                                break;
                                            case 1:
                                                statusText = "已向量化";
                                                break;
                                            case 2:
                                                statusText = "向量化失败";
                                                break;
                                            default:
                                                statusText = "未知状态";
                                                break;
                                        }
                                    }
                                    @news.VectorStatus (@statusText)
                                </td>
                                <td>
                                    <span class="label label-success">字段存在</span>
                                </td>
                            </tr>
                            <tr>
                                <td>VectorError</td>
                                <td>
                                    @if (!string.IsNullOrEmpty(news.VectorError))
                                    {
                                        @news.VectorError
                                    }
                                    else
                                    {
                                        <span class="text-muted">未设置</span>
                                    }
                                </td>
                                <td>
                                    @if (!string.IsNullOrEmpty(news.VectorError))
                                    {
                                        <span class="label label-success">字段存在且有值</span>
                                    }
                                    else
                                    {
                                        <span class="label label-warning">字段存在但无值</span>
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td>VectorUpdateTime</td>
                                <td>@news.VectorUpdateTime.ToString("yyyy-MM-dd HH:mm:ss")</td>
                                <td>
                                    <span class="label label-success">字段存在</span>
                                </td>
                            </tr>
                            <tr>
                                <td>TagAnalysis</td>
                                <td>
                                    @if (!string.IsNullOrEmpty(news.TagAnalysis))
                                    {
                                        <div style="max-height: 100px; overflow-y: auto;">
                                            @news.TagAnalysis.Substring(0, Math.Min(news.TagAnalysis.Length, 200))
                                            @if (news.TagAnalysis.Length > 200)
                                            {
                                                <span>...</span>
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        <span class="text-muted">未设置</span>
                                    }
                                </td>
                                <td>
                                    @if (!string.IsNullOrEmpty(news.TagAnalysis))
                                    {
                                        <span class="label label-success">字段存在且有值</span>
                                    }
                                    else
                                    {
                                        <span class="label label-warning">字段存在但无值</span>
                                    }
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="alert alert-success">
                    <h4>测试结果</h4>
                    <p>所有向量化相关字段已成功添加到News模型中。</p>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <a href="/Index/NewsVector" class="btn btn-primary">返回新闻向量化浏览</a>
                        <a href="/Index/NewsVectorDetails/@news.Id" class="btn btn-info">查看此新闻向量详情</a>
                    </div>
                </div>
            }
            else
            {
                <div class="alert alert-danger">
                    <h4>测试失败</h4>
                    <p>未能获取新闻数据，请确保数据库中存在新闻记录。</p>
                </div>
            }
        </div>
    </div>
</div>