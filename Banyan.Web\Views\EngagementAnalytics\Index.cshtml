@model Banyan.Web.Controllers.EngagementAnalyticsDashboardViewModel
@{
    ViewBag.Title = "Engagement Analytics Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <h2>Engagement Analytics Dashboard</h2>
    
    @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
    {
        <div class="alert alert-danger">
            @ViewBag.ErrorMessage
        </div>
    }
    
    <!-- Date Range Filter -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Date Range Filter</h5>
        </div>
        <div class="card-body">
            <form method="get" action="@Url.Action("Index")" class="form-inline">
                <div class="form-group mr-2">
                    <label for="startDate" class="mr-2">Start Date:</label>
                    <input type="date" id="startDate" name="startDate" class="form-control" value="@Model.StartDate.ToString("yyyy-MM-dd")" />
                </div>
                <div class="form-group mr-2">
                    <label for="endDate" class="mr-2">End Date:</label>
                    <input type="date" id="endDate" name="endDate" class="form-control" value="@Model.EndDate.ToString("yyyy-MM-dd")" />
                </div>
                <button type="submit" class="btn btn-primary">Apply Filter</button>
            </form>
        </div>
    </div>
    
    <!-- Summary Statistics -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Summary Statistics</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Total Engagements</h5>
                            <h2 class="card-text">@(Model.Summary?.TotalEngagements ?? 0)</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Unique Users</h5>
                            <h2 class="card-text">@(Model.Summary?.UniqueUsers ?? 0)</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Web Engagements</h5>
                            <h2 class="card-text">@(Model.Summary?.WebEngagements ?? 0)</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Email Engagements</h5>
                            <h2 class="card-text">@(Model.Summary?.EmailEngagements ?? 0)</h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Effectiveness Metrics -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Effectiveness Metrics</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Click-Through Rate</h5>
                            <h2 class="card-text">@(Model.EffectivenessMetrics?.ClickThroughRate.ToString("P2") ?? "0.00%")</h2>
                            <p class="card-text text-muted">
                                @(Model.EffectivenessMetrics?.ClickedRecommendations ?? 0) clicks / 
                                @(Model.EffectivenessMetrics?.TotalRecommendations ?? 0) recommendations
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Email Effectiveness</h5>
                            <h2 class="card-text">@(Model.EffectivenessMetrics?.EmailEffectivenessRate.ToString("P2") ?? "0.00%")</h2>
                            <p class="card-text text-muted">
                                @(Model.EffectivenessMetrics?.EmailEngagements ?? 0) clicks / 
                                @(Model.EffectivenessMetrics?.TotalNewsInDigests ?? 0) news items
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">User Retention</h5>
                            <h2 class="card-text">@(Model.EffectivenessMetrics?.RetentionRate.ToString("P2") ?? "0.00%")</h2>
                            <p class="card-text text-muted">
                                @(Model.EffectivenessMetrics?.RetainedUsers ?? 0) retained / 
                                @(Model.EffectivenessMetrics?.UniqueUsers ?? 0) users
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Daily Engagement Chart -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Daily Engagement</h5>
        </div>
        <div class="card-body">
            <canvas id="dailyEngagementChart" height="100"></canvas>
        </div>
    </div>
    
    <div class="row">
        <!-- Source Distribution Chart -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Engagement Source Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="sourceDistributionChart" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Content Type Distribution Chart -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Content Type Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="contentTypeDistributionChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Hourly Distribution Chart -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Hourly Engagement Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="hourlyDistributionChart" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Day of Week Distribution Chart -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Day of Week Engagement Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="dayOfWeekDistributionChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Top Engaged News -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Top Engaged News</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>News ID</th>
                            <th>Title</th>
                            <th>Engagement Count</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.Summary?.TopEngagedNews != null)
                        {
                            foreach (var news in Model.Summary.TopEngagedNews)
                            {
                                <tr>
                                    <td>@news.NewsId</td>
                                    <td>@news.NewsTitle</td>
                                    <td>@news.EngagementCount</td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="3" class="text-center">No data available</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Top Engaged Users -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Top Engaged Users</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>User Name</th>
                            <th>Engagement Count</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.Summary?.TopEngagedUsers != null)
                        {
                            foreach (var user in Model.Summary.TopEngagedUsers)
                            {
                                <tr>
                                    <td>@user.UserName</td>
                                    <td>@user.EngagementCount</td>
                                    <td>
                                        <a href="@Url.Action("UserDetails", new { userName = user.UserName, startDate = Model.StartDate.ToString("yyyy-MM-dd"), endDate = Model.EndDate.ToString("yyyy-MM-dd") })" class="btn btn-sm btn-info">
                                            View Details
                                        </a>
                                    </td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="3" class="text-center">No data available</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Export Button -->
    <div class="mb-4">
        <a href="@Url.Action("ExportCsv", new { startDate = Model.StartDate.ToString("yyyy-MM-dd"), endDate = Model.EndDate.ToString("yyyy-MM-dd") })" class="btn btn-success">
            <i class="fa fa-download"></i> Export to CSV
        </a>
        <a href="@Url.Action("Records", new { startDate = Model.StartDate.ToString("yyyy-MM-dd"), endDate = Model.EndDate.ToString("yyyy-MM-dd") })" class="btn btn-primary">
            <i class="fa fa-list"></i> View All Records
        </a>
    </div>
</div>

@section scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function () {
            // Daily Engagement Chart
            var dailyEngagementCtx = document.getElementById('dailyEngagementChart').getContext('2d');
            var dailyEngagementChart = new Chart(dailyEngagementCtx, {
                type: 'line',
                data: {
                    labels: @Html.Raw(Json.Encode(Model.DetailedMetrics?.DailyEngagementCounts?.Keys.Select(d => d.ToString("MM/dd")).ToArray())),
                    datasets: [{
                        label: 'Daily Engagements',
                        data: @Html.Raw(Json.Encode(Model.DetailedMetrics?.DailyEngagementCounts?.Values.ToArray())),
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // Source Distribution Chart
            var sourceDistributionCtx = document.getElementById('sourceDistributionChart').getContext('2d');
            var sourceDistributionChart = new Chart(sourceDistributionCtx, {
                type: 'pie',
                data: {
                    labels: @Html.Raw(Json.Encode(Model.DetailedMetrics?.SourceDistribution?.Keys.ToArray())),
                    datasets: [{
                        data: @Html.Raw(Json.Encode(Model.DetailedMetrics?.SourceDistribution?.Values.ToArray())),
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)'
                        ]
                    }]
                },
                options: {
                    responsive: true
                }
            });
            
            // Content Type Distribution Chart
            var contentTypeDistributionCtx = document.getElementById('contentTypeDistributionChart').getContext('2d');
            var contentTypeDistributionChart = new Chart(contentTypeDistributionCtx, {
                type: 'pie',
                data: {
                    labels: @Html.Raw(Json.Encode(Model.DetailedMetrics?.ContentTypeDistribution?.Keys.ToArray())),
                    datasets: [{
                        data: @Html.Raw(Json.Encode(Model.DetailedMetrics?.ContentTypeDistribution?.Values.ToArray())),
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)'
                        ]
                    }]
                },
                options: {
                    responsive: true
                }
            });
            
            // Hourly Distribution Chart
            var hourlyDistributionCtx = document.getElementById('hourlyDistributionChart').getContext('2d');
            var hourlyDistributionChart = new Chart(hourlyDistributionCtx, {
                type: 'bar',
                data: {
                    labels: @Html.Raw(Json.Encode(Model.DetailedMetrics?.HourlyDistribution?.Keys.Select(h => h.ToString() + ":00").ToArray())),
                    datasets: [{
                        label: 'Engagements by Hour',
                        data: @Html.Raw(Json.Encode(Model.DetailedMetrics?.HourlyDistribution?.Values.ToArray())),
                        backgroundColor: 'rgba(75, 192, 192, 0.7)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // Day of Week Distribution Chart
            var dayOfWeekDistributionCtx = document.getElementById('dayOfWeekDistributionChart').getContext('2d');
            var dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            var dayOfWeekDistributionChart = new Chart(dayOfWeekDistributionCtx, {
                type: 'bar',
                data: {
                    labels: dayNames,
                    datasets: [{
                        label: 'Engagements by Day of Week',
                        data: @Html.Raw(Json.Encode(dayNames.Select(d => {
                            var dayOfWeek = Array.IndexOf(dayNames, d);
                            return Model.DetailedMetrics?.DayOfWeekDistribution?.ContainsKey((DayOfWeek)dayOfWeek) == true ? 
                                Model.DetailedMetrics.DayOfWeekDistribution[(DayOfWeek)dayOfWeek] : 0;
                        }).ToArray())),
                        backgroundColor: 'rgba(153, 102, 255, 0.7)',
                        borderColor: 'rgba(153, 102, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
    </script>
}