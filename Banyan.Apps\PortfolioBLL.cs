﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.Linq;
using System.Globalization;

namespace Banyan.Apps
{
    public class PortfolioBLL : BaseDAL<PortfolioBasicInfo>
    {
        private readonly AjaxResult ajaxResult = null;
        private CultureInfo ch = new CultureInfo("zh-CN");
        private PortfolioEquityBLL portfolioEquityBll = new PortfolioEquityBLL();
        private Fund2PortfolioSummaryBLL fund2PortfolioSummaryBll = new Fund2PortfolioSummaryBLL();

        public PortfolioBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        public int existName(string name)
        {
            if (GetModel($"Name='{name}'") != null)
            {
                return 1;
            }
            return 0;
        }
        public AjaxResult Update(PortfolioBasicInfo model)
        {
            AjaxResult ajaxResult = new AjaxResult();
            var user = new MemberBLL().GetLogOnUser();
            Update(new PortfolioBasicInfo(), "contributionProjectID", $"contributionProjectID={model.contributionProjectID}");
            ajaxResult.code = Update(model, "contributionProjectID") ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }
        public List<string> getCompanyNameList()
        {
            var companyList = base.GetList();
            return companyList.Select(val => val.Name).ToList();
        }

        public List<PortfolioBasicInfo> GetListByRight()
        {
            Member user = new MemberBLL().GetLogOnUser();
            int count;
            return searchCommon(new NameValueCollection(), user, 1, int.MaxValue, out count);
        }
        public List<string> GetNameListByRight()
        {
            var res = GetListByRight();
            return res.Select(val => val.Name).ToList();
        }
        public string getFullNameByName(string name)
        {
            var p = base.GetModel($"Name='{name}'");
            if (p == null)
            {
                return string.Empty;
            }
            return p.OfficialName;
        }

        public List<string> getCityList()
        {
            var list = GetList();
            var cityList = list.Select(val => val.City).Where(val => !val.IsEmpty()).ToList();
            var set = new HashSet<string>();
            foreach (var c in cityList)
            {
                var str = c.Split(new char[] { '，', '、' });
                foreach (var s in str)
                {
                    set.Add(s);
                }
            }
            return set.ToList();
        }

        public void updateLog(string page, string action, string description, Member user, string project = "")
        {
            SysLog log = new SysLog
            {
                Page = page,
                Action = action,
                Project = project,
                Description = description,
                CreatedBy = user == null ? "" : user.RealName,
                CreatorId = user == null ? 0 : user.Id,
            };
            SysLogBLL logBLL = new SysLogBLL();
            logBLL.Add(log);
        }

        public string getIdByName(string name)
        {
            var res = base.GetModel($"Name='{name}'");
            return res == null ? string.Empty : res.portfolioID;
        }
        public Dictionary<string, string> nameIdDic()
        {
            return GetList("Name is not NULL").ToDictionary(a => a.Name, a => a.portfolioID);
        }

        public List<KeyValuePair<string, string>> nameIdValuePair()
        {
            var list = GetList("Name is not NULL");
            
            // 创建智能键生成函数，只添加不同的名称部分
            var dic = list.ToDictionary(a => CreateUniqueKey(a), a => a.portfolioID);
            
            List<KeyValuePair<string, string>> lst = new List<KeyValuePair<string, string>>(dic);
            lst.Sort(delegate (KeyValuePair<string, string> s1, KeyValuePair<string, string> s2)
            {
                return string.Compare(s1.Key, s2.Key, ch, CompareOptions.None);
            });
            return lst;
        }

        /// <summary>
        /// 创建唯一的键，只包含不同的名称部分
        /// </summary>
        /// <param name="item">投资组合项</param>
        /// <returns>唯一的键</returns>
        private string CreateUniqueKey(PortfolioBasicInfo item)
        {
            var parts = new List<string>();
            
            // 添加非空的名称部分
            if (!string.IsNullOrWhiteSpace(item.abbName))
                parts.Add(item.abbName);
            
            if (!string.IsNullOrWhiteSpace(item.Name))
                parts.Add(item.Name);
                
            if (!string.IsNullOrWhiteSpace(item.abbNameChi))
                parts.Add(item.abbNameChi);
            
            // 去重并连接
            return string.Join(" ", parts.Distinct());
        }

        public OrderedDictionary nameIdDicOrdered()
        {
            var dic = new OrderedDictionary();
            var list = GetList("Name is not NULL");
            foreach (var i in list)
            {
                dic.Add(i.Name, i.portfolioID);
            }
            return dic;
        }
        public Dictionary<string, string> IdNameDic()
        {
            return GetList("").ToDictionary(a => a.portfolioID, a => a.Name);
        }
        private List<PortfolioBasicInfo> searchCommon(NameValueCollection paramValues, Member user,
      int pageIndex, int pageSize, out int count, bool searchNameOnly = false)
        {
            string sort;
            string strWhere = getStrWhere(paramValues, user, out sort, searchNameOnly);

            string Fund = WebHelper.GetValue("Fund", string.Empty, paramValues);
            List<PortfolioBasicInfo> PortfolioList;

            List<string> fundList = new List<string>();
            if (!MemberBLL.adminOrSuper(user) && user.Levels != (int)MemberLevels.DealALL && Fund.IsEmpty())
            {
                Fund = user.Funds;
                fundList = new List<string>(user.Funds.Split(','));
            }
            var dic = new Fund2PortfolioSummaryBLL().portfolioIDFundDic();
            if (Fund.IsEmpty())
            {
                PortfolioList = GetList(strWhere, pageSize, pageIndex, "*", sort);
                PortfolioList = PortfolioList.Select(val =>
                {
                    if (dic.ContainsKey(val.portfolioID))
                    {
                        val.fundName += string.Join(",", dic[val.portfolioID]);
                    }
                    return val;
                }).ToList();

                count = GetCount(strWhere);
            }
            else
            {
                PortfolioList = GetList(strWhere, 10000, 0, "*", sort);
                PortfolioList = PortfolioList.Select(val =>
                {
                    if (dic.ContainsKey(val.portfolioID))
                    {
                        val.fundName += string.Join(",", dic[val.portfolioID]);
                    }
                    return val;
                }).ToList();
                PortfolioList = PortfolioList.Where(a => {
                    if (dic.ContainsKey(a.portfolioID))
                    {

                        if (fundList.Count == 0)
                        {
                            return dic[a.portfolioID].Contains(Fund);
                        }
                        else
                        {
                            return fundList.Select((val) => dic[a.portfolioID].Contains(val))
                            .Aggregate(false, (res, val) => res |= val);
                        }

                    }
                    else
                    {
                        Logger.Info(a.Name + " not in fund");
                        return false;
                    }
                }
                ).ToList();
                count = PortfolioList.Count();
                int start = (pageIndex - 1) * pageSize;
                if (start > count) start = 0;
                PortfolioList = PortfolioList.GetRange(start, start + pageSize > count ? count - start : pageSize);
            }
            return PortfolioList;
        }
        private string getStrWhere(NameValueCollection paramValues, Member user, out string sort, bool searchNameOnly = false, bool investOnly = false)
        {
            string strWhere = "(portfolioID != 'P00001' AND portfolioID != 'P00002' ";
            if (!investOnly)
            {
                strWhere += " AND PrivateOrPublic<> 'Writeoff' AND PrivateOrPublic<> 'Public' ";
            }
            strWhere += ") ";
            if (!MemberBLL.adminOrSuper(user) && user.CompanyName.Equals("高榕资本"))
            {
                List<PortfolioBasicInfo> companyList;
                if (user.Levels == 10 || user.Levels == 9 || user.Levels == 11)
                {
                    List<Fund2PortfolioSummary> list;
                    if (user.Levels == 10)
                    {
                        list = fund2PortfolioSummaryBll.GetList($"fundFamillyName like '%RMB%'");
                    }
                    else if (user.Levels == 9)
                    {
                        list = fund2PortfolioSummaryBll.GetList($"fundFamillyName like '%USD%'");
                    }
                    else
                    {
                        list = fund2PortfolioSummaryBll.GetList();
                    }

                    List<PortfolioBasicInfo> portfoliolist = list.Select(val =>
                    {
                        var tmp = new PortfolioBasicInfo();
                        tmp.Name = val.Name;
                        tmp.fundName = val.fundName;
                        tmp.portfolioID = val.portfolioID;
                        return tmp;
                    }).ToList();

                    companyList = portfoliolist.Distinct(new PortfolioBaicInfoComparer()).ToList();
                }
                else
                {
                    companyList = getManagedCompanies(user.RealName, investOnly);
                }
                if (companyList.Count == 0)
                {
                    strWhere += $" AND Name='no companies should be seen' ";
                }
                else
                {
                    var companyNames = companyList.Aggregate("", (res, val) => {
                        if (res.IsEmpty())
                        {
                            return $"'{val.Name}'";
                        }
                        return $"{res},'{val.Name}'";
                    });
                    strWhere += $" AND Name in ({companyNames}) ";
                }
            }

            string Name = WebHelper.GetValue("Name", string.Empty, paramValues);
            Name = Name.Replace("\'", string.Empty);
            if (!string.IsNullOrWhiteSpace(Name))
            {
                if (searchNameOnly)
                {
                    strWhere += $@"AND (Name like '%{Name}%' OR abbName like '%{Name}%') ";
                }
                else
                {
                    strWhere += $@"AND (Name like '%{Name}%' OR abbName like '%{Name}%' OR fullName like '%{Name}%' OR fullNameChi like '%{Name}%' OR CEO like '%{Name}%' OR portfolioManager like '%{Name}%' OR oneLineDesc like '%{Name}%' OR oneLineDescChi like '%{Name}%' ) ";
                }
            }

            sort = "portfolioID Desc";
            return strWhere;
        }

        public AjaxResult GetPageListLimited(NameValueCollection paramValues, bool simple = true)
        {
            Member user = new MemberBLL().GetLogOnUser();
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10000, paramValues);
            int count;
            var PortfolioList = searchCommon(paramValues, user, pageIndex, pageSize, out count);
            if (simple)
            {
                PortfolioList = PortfolioList.Select(val =>
                {
                    var tmp = new PortfolioBasicInfo();
                    tmp.portfolioID = val.portfolioID;
                    tmp.Name = val.Name;
                    tmp.fundName = val.fundName;
                    tmp.OfficialName = val.OfficialName;
                    tmp.abbName = val.abbName;
                    return tmp;
                }).ToList();
            }
            var PortfolioDic = PortfolioList.ToDictionary(p => p.Name, p => p);


            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = PortfolioList;
            ajaxResult.count = count;
            return ajaxResult;
        }

        public AjaxResult GetInvestedList(NameValueCollection paramValues)
        {
            Member user = new MemberBLL().GetLogOnUser();
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            string sort;

            string mock = WebHelper.GetValue("user", string.Empty, paramValues);

            if (mock != string.Empty)
            {
                user.RealName = mock;
                user.Levels = (int)MemberLevels.Normal;
                user.CompanyName = "高榕资本";
            }
            string strWhere = getStrWhere(paramValues, user, out sort, true, true);


            sort = $"invest desc, {sort}";

            var strWhere2 = $"WITH t1 AS (select portfolioID,invest, case when Name=abbName COLLATE Chinese_PRC_CI_AS or abbName = '' or abbName is NULL then Name else Name+'/'+abbName COLLATE Chinese_PRC_CI_AS end as Name from ( select *, row_number() over ( order by {sort} ) as row " + @"
                        from (
		                        SELECT *,"
+ $" case when groupMember like '%{user.RealName}%' then 1 when portfolioManager like '%{user.RealName}%' then 2 else 0 "
+ @"                              END AS invest
                                FROM
                                    PortfolioBasicInfo pbi" +
                                $" WHERE {strWhere}  ) as subquery ) a";
            var strAll = $" {strWhere2} where row between " +
                $"{(pageIndex - 1) * pageSize + 1} and {pageIndex * pageSize} )"
                + @"SELECT 
                        t1.Name as createdBy,
                        t1.portfolioID,
                        t1.invest as rid,
                        pei.EIID,
                        pei.round,
                        case WHEN (select sum(shareOwnedNo) from PortfolioShareStructure where portfolioID = pei.portfolioID group by portfolioID) = 0 
                        THEN 0 
                        ELSE Round((shareOwnedNo / (select sum(shareOwnedNo) from PortfolioShareStructure where portfolioID = pei.portfolioID group by portfolioID) * 100), 2) end as payable,
                        FORMAT(COALESCE(pei.cost, 0), 'N0') as conversionRatio,
                        FORMAT(COALESCE(pei.shareOwnedNo, 0), 'N0') as convertFromNoteID,
                        case when pei.currency = 'CNY' then '￥' when pei.currency='USD' then '$' else pei.currency end as currency,
                        case when pei.investType='Equity Investment' or pei.investType='Equity Interest' then '投资' 
                             when pei.investType = 'Shares Split' then '拆股' 
                             when pei.investType = 'Share Repurchase' then '回购' 
                             when pei.investType='Loan To Equity' then '债转股' 
                             else pei.investType end as investType,
                        FORMAT(pei.closeDate, 'yyyy MM-dd') as modifiedBy,
                        fb.fundFamillyName as fundID,
                        pei.remarks 
                    FROM 
                        t1
                    left join PortfolioEquityInvestment pei 
                    on t1.portfolioID = pei.portfolioID 
                    left join FundBasicInfo fb 
                    on pei.fundID = fb.fundID 
                    where pei.investType = 'Equity Investment' 
                        OR pei.investType = 'Equity Interest' 
                        OR pei.investType='Loan To Equity'
                        OR pei.investType = 'Shares Split'
                        OR pei.investType = 'Dividends'
                        OR pei.investType = 'Bonus Share'
                        OR pei.investType is NULL 
                        OR pei.investType = 'Share Repurchase'
                        OR pei.investType = 'Convert To Equity Interest' ";
            if (user.RealName == "钟秋月" || user.RealName == "隋赫")
            {
                strAll += @" OR pei.investType = 'Liquidation'
                        OR pei.investType = 'Shares Sale' ";
            }
            strAll += @" UNION all
                    SELECT 
                        t1.Name as createdBy,
                        t1.portfolioID,
                        t1.invest as rid,
                        pn.noteID as EIID,
                        'Loan' as round,
                        0 as payable,
                        FORMAT(COALESCE(pn.noteNum, 0), 'N0') as conversionRatio,
                        '' as convertFromNoteID,
                        case when pn.currency = 'CNY' then '￥' when pn.currency='USD' then '$' else pn.currency end as currency,
                        case when pn.noteType='Bridging Loans' then '贷款' when pn.noteType='Loan Converted' then '转股'  else pn.noteType end as investType,
                        FORMAT(pn.closeDate, 'yyyy MM-dd') as modifiedBy,
                        fb.fundFamillyName as fundID,
                        pn.interestDesc as remarks
                    FROM 
                        t1
                    inner join PortfolioNote pn 
                    on t1.portfolioID = pn.portfolioID
                    left join FundBasicInfo fb 
                    on pn.fundID = fb.fundID 
                    order by invest desc, portfolioID desc, fundID desc,modifiedBy desc";

            var PortfolioList = portfolioEquityBll.GetListBySql(strAll);

            var res = new List<List<PortfolioEquityInvestment>>();
            string lastPid = null;
            decimal totalSharePercent = 0;
            var lastList = new List<PortfolioEquityInvestment>();

            foreach (var item in PortfolioList)
            {
                if (item.portfolioID == lastPid)
                {
                    var last = lastList.Last();
                    if (last.fundID == item.fundID && last.investType == item.investType && last.round == item.round)
                    {
                        decimal lastConversionRatio = Convert.ToDecimal(last.conversionRatio.Replace(",", ""));
                        decimal itemConversionRatio = Convert.ToDecimal(item.conversionRatio.Replace(",", ""));
                        decimal sum = lastConversionRatio + itemConversionRatio;
                        last.conversionRatio = sum.ToString("N0");
                        last.payable += item.payable;
                    }
                    else
                    {
                        lastList.Add(item);
                    }
                    totalSharePercent += item.payable;
                }
                else
                {
                    if (lastList.Any())
                    {
                        lastList.Last().convertFromNoteID = totalSharePercent.ToString("0.00");
                        res.Add(lastList);
                    }
                    lastList = new List<PortfolioEquityInvestment> { item };
                    lastPid = item.portfolioID;
                    totalSharePercent = item.payable;
                }
            }
            if (lastList.Any())
            {
                lastList.Last().convertFromNoteID = totalSharePercent.ToString("0.00");
                res.Add(lastList);
            }

            int count = GetList(strWhere).Count;

            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = res;
            ajaxResult.count = count;
            return ajaxResult;
        }

        public List<PortfolioBasicInfo> getManagedCompanies(string investor, bool investOnly = false)
        {
            var str = $"portfolioManager like '%{investor}%' OR groupMember like '%{investor}%' OR postInvestManager like '%{investor}%'  OR postInvestMember like '%{investor}%' Or buyBackManager like '%{investor}%' ";
            if (investOnly)
            {

                str = $"portfolioManager like '%{investor}%' OR groupMember like '%{investor}%' Or buyBackManager like '%{investor}%' ";
            }
            var res = GetListNoPage(str);
            //if (investor == "钟秋月")
            //{
            //    var list = fund2PortfolioSummaryBll.GetList("fundID='F0009'");
            //    List<PortfolioBasicInfo> portfoliolist = list.Select(val =>
            //    {
            //        var tmp = new PortfolioBasicInfo();
            //        tmp.Name = val.Name;
            //        tmp.fundName = val.fundName;
            //        tmp.portfolioID = val.portfolioID;
            //        return tmp;
            //    }).ToList();
            //    res.AddRange(portfoliolist);
            //    return res.Distinct(new PortfolioBaicInfoComparer()).ToList();
            //}
            if (investOnly)
            {
                return res;
            }

            if (investor == "韩锐")
            {
                var list = fund2PortfolioSummaryBll.GetListBySector("New Consumption");
                List<PortfolioBasicInfo> portfoliolist = list.Select(val =>
                {
                    var tmp = new PortfolioBasicInfo();
                    tmp.Name = val.Name;
                    tmp.fundName = val.fundName;
                    tmp.portfolioID = val.portfolioID;
                    return tmp;
                }).ToList();
                res.AddRange(portfoliolist);
                return res.Distinct(new PortfolioBaicInfoComparer()).ToList();
            }
            else
            {
                return res;
            }
        }

        public List<PortfolioBasicInfo> superSelectManagedCompanies(string investor)
        {
            var portfolios = GetListNoPage($"charindex('{investor}', (case when NewManager is not null and trim(NewManager) != '' then NewManager else portfolioManager END) ) > 0");//portfolioManager like '%{investor}%' ");
            return portfolios;
        }

        public string portfolioManagerAllStr()
        {
            var portfolios = GetListNoPage($" Name is not NULL ");

            return portfolios.Aggregate("", (res, val) =>
            {
                if (val != null)
                {
                    if (!val.NewManager.IsEmpty())
                    {
                        res += val.NewManager + ",";
                    }
                    else
                    {
                        res += val.portfolioManager + ",";
                    }
                }
                return res;
            });
        }


    }
}