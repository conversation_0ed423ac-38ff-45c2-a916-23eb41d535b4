﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8A3C6556-408C-4E9F-93F1-6FCC666F79B8}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Banyan.Apps</RootNamespace>
    <AssemblyName>Banyan.Apps</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <RuntimeIdentifiers>win;win-x86;win-x64</RuntimeIdentifiers>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DAL.Base">
      <HintPath>..\lib\DAL.Base.dll</HintPath>
    </Reference>
    <Reference Include="Google.Protobuf, Version=3.23.1.0, Culture=neutral, PublicKeyToken=a7d26565bac4d604, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Protobuf.3.23.1\lib\net45\Google.Protobuf.dll</HintPath>
    </Reference>
    <Reference Include="Grpc.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=d754f35622e28bad, processorArchitecture=MSIL">
      <HintPath>..\packages\Grpc.Core.2.46.6\lib\net45\Grpc.Core.dll</HintPath>
    </Reference>
    <Reference Include="Grpc.Core.Api, Version=2.0.0.0, Culture=neutral, PublicKeyToken=d754f35622e28bad, processorArchitecture=MSIL">
      <HintPath>..\packages\Grpc.Core.Api.2.46.6\lib\net45\Grpc.Core.Api.dll</HintPath>
    </Reference>
    <Reference Include="hyjiacan.py4n">
      <HintPath>..\Banyan.Web\bin\hyjiacan.py4n.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.4.0\lib\netstandard2.0\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.3\lib\netstandard2.0\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.4.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.5.2\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="Newtonsoft.Json, Version=11.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.11.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Utility">
      <HintPath>..\lib\Utility.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.TestPlatform.TestFramework, Version=14.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\MSTest.TestFramework.2.2.10\lib\net45\Microsoft.VisualStudio.TestPlatform.TestFramework.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions, Version=14.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\MSTest.TestFramework.2.2.10\lib\net45\Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Moq, Version=4.16.0.0, Culture=neutral, PublicKeyToken=69f491c39445e920, processorArchitecture=MSIL">
      <HintPath>..\packages\Moq.4.16.1\lib\net45\Moq.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AnnualReportBLL.cs" />
    <Compile Include="AttachmentFMSBLL.cs" />
    <Compile Include="Configs\RecommendationScoringConfig.cs" />
    <Compile Include="Configs\VectorServiceConfig.cs" />
    <Compile Include="EmailDigestService.cs" />
    <Compile Include="EngagementAnalytics.cs" />
    <Compile Include="EngagementTracker.cs" />
    <Compile Include="llm\prompts.cs" />
    <Compile Include="NewsRecommendationEngine.cs" />
    <Compile Include="NewsPrecomputeService.cs" />
    <Compile Include="EngagementRecordsBLL.cs" />
    <Compile Include="EmailDigestRecords.cs" />
    <Compile Include="NewsTag.cs" />
    <Compile Include="NewsTagAnalysis.cs" />
    <Compile Include="NewsVectorizationSchedulerManager.cs" />
    <Compile Include="NewsVectorizationSchedulerTest.cs" />
    <Compile Include="NewsRecommendationsBLL.cs" />
    <Compile Include="RecommendationCacheManager.cs" />
    <Compile Include="RecommendationScheduler.cs" />
    <Compile Include="ReportsBLL.cs" />
    <Compile Include="NewsBLL.cs" />
    <Compile Include="BuybackBLL.cs" />
    <Compile Include="ContributionExtBLL.cs" />
    <Compile Include="investForIRRBLL.cs" />
    <Compile Include="PortfolioExitBLL.cs" />
    <Compile Include="PortfolioBLL.cs" />
    <Compile Include="FundBasicInfoBLL.cs" />
    <Compile Include="Fund2PortfolioSummaryBLL.cs" />
    <Compile Include="ContributionBLL.cs" />
    <Compile Include="Meet_project_attachBLL.cs" />
    <Compile Include="MeetAttachBLL.cs" />
    <Compile Include="MeetBLL.cs" />
    <Compile Include="AttachmentBLL.cs" />
    <Compile Include="PortfolioEquityBLL.cs" />
    <Compile Include="ExitScoreBLL.cs" />
    <Compile Include="ExitScoreStageBLL.cs" />
    <Compile Include="Project_Active_ClosedBLL.cs" />
    <Compile Include="Project_ActiveStatusBLL.cs" />
    <Compile Include="RestrictedTradingListBLL.cs" />
    <Compile Include="Project_ActiveBLL.cs" />
    <Compile Include="ResearchAttachBLL.cs" />
    <Compile Include="ResearchBLL.cs" />
    <Compile Include="ProjectScoreBLL.cs" />
    <Compile Include="ProjectScoreStageBLL.cs" />
    <Compile Include="ProjectMemoBLL.cs" />
    <Compile Include="rpc\ProjectSearch.cs" />
    <Compile Include="rpc\ProjectSearchGrpc.cs" />
    <Compile Include="rpc\RpcClient.cs" />
    <Compile Include="Service\BaseService.cs" />
    <Compile Include="Service\ProjectService.cs" />
    <Compile Include="Service\ScoreService.cs" />
    <Compile Include="SummaryBLL.cs" />
    <Compile Include="SysLogBLL.cs" />
    <Compile Include="ScoreBLL.cs" />
    <Compile Include="MemberBLL.cs" />
    <Compile Include="PraiseDetailBLL.cs" />
    <Compile Include="ProjectBLL.cs" />
    <Compile Include="ArticleBLL.cs" />
    <Compile Include="CollectDetailBLL.cs" />
    <Compile Include="CommentBLL.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RoleBLL.cs" />
    <Compile Include="Tests\NewsPrecomputeServiceTests.cs" />
    <Compile Include="Tests\NewsRecommendationEngineTests.cs" />
    <Compile Include="Tests\RecommendationCacheManagerTests.cs" />
    <Compile Include="Tests\RecommendationErrorHandlingTests.cs" />
    <Compile Include="Tests\RecommendationPerformanceTests.cs" />
    <Compile Include="Tests\RecommendationSchedulerTests.cs" />
    <Compile Include="UserInterestProfileUpdater.cs" />
    <Compile Include="UserInterestTagBLL.cs" />
    <Compile Include="UserInterestVectorRetrieval.cs" />
    <Compile Include="UserTagRelationBLL.cs" />
    <Compile Include="UserProfileBLL.cs" />
    <Compile Include="NewsVectorizationService.cs" />
    <Compile Include="NewsVectorizationScheduler.cs" />
    <Compile Include="NewsVectorSearch.cs" />
    <Compile Include="VectorizationResult.cs" />
    <Compile Include="VectorService.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Banyan.Code\Banyan.Code.csproj">
      <Project>{a2469fa5-ee0a-40df-ba22-2ed091b6e251}</Project>
      <Name>Banyan.Code</Name>
    </ProjectReference>
    <ProjectReference Include="..\Banyan.Domain\Banyan.Domain.csproj">
      <Project>{fa32fef4-2ab2-4031-b78e-1c9ebd219784}</Project>
      <Name>Banyan.Domain</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="rpc\project_search.proto" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="EmailTemplates\NewsItemTemplate.html" />
    <Content Include="EmailTemplates\WeeklyDigestTemplate.html" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\packages\Grpc.Core.2.46.6\build\net45\Grpc.Core.targets" Condition="Exists('..\packages\Grpc.Core.2.46.6\build\net45\Grpc.Core.targets')" />
</Project>