using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using Banyan.Apps.Configs;

namespace Banyan.Apps
{
    /// <summary>
    /// 兴趣标签业务逻辑类
    /// 支持标签级别的向量化存储和计算
    /// </summary>
    public class UserInterestTagBLL : BaseDAL<UserInterestTag>
    {
        private readonly Log _logger;
        private readonly ICache _cache;

        // 配置常量已迁移到 VectorServiceConfig 类中

        public UserInterestTagBLL()
        {
            _logger = LogFactory.GetLogger("UserInterestTagBLL");
            _cache = CacheFactory.Cache();
        }

        /// <summary>
        /// 根据名称获取标签
        /// </summary>
        /// <param name="tagName">标签名称</param>
        /// <returns>标签对象</returns>
        public UserInterestTag GetTagByName(string tagName)
        {
            try
            {
                if (string.IsNullOrEmpty(tagName))
                    return null;

                return this.GetModel($"Name='{tagName}' AND IsActive=1");
            }
            catch (Exception ex)
            {
                _logger.Error($"根据名称获取标签失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 根据ID获取标签
        /// </summary>
        /// <param name="tagId">标签ID</param>
        /// <returns>标签对象</returns>
        public UserInterestTag GetTagById(int tagId)
        {
            try
            {
                if (tagId <= 0)
                    return null;

                return this.GetModel($"Id={tagId} AND IsActive=1");
            }
            catch (Exception ex)
            {
                _logger.Error($"根据ID获取标签失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建或更新标签
        /// </summary>
        /// <param name="tag">标签对象</param>
        /// <returns>操作结果</returns>
        public bool CreateOrUpdateTag(UserInterestTag tag)
        {
            try
            {
                if (tag == null)
                    return false;

                // 检查是否已存在
                var existingTag = GetTagByName(tag.Name);
                if (existingTag != null)
                {
                    // 更新现有标签
                    tag.Id = existingTag.Id;
                    tag.UpdateTime = DateTime.Now;
                    tag.LastUsedTime = DateTime.Now;
                    
                    // 如果标签内容发生变化，需要重新生成向量
                    if (existingTag.Name != tag.Name || existingTag.Keywords != tag.Keywords)
                    {
                        tag.VectorUpdateTime = DateTime.MinValue; // 标记需要重新生成向量
                    }
                    
                    return this.Update(tag);
                }
                else
                {
                    // 创建新标签
                    tag.CreateTime = DateTime.Now;
                    tag.UpdateTime = DateTime.Now;
                    tag.LastUsedTime = DateTime.Now;
                    tag.IsActive = 1;
                    tag.VectorUpdateTime = DateTime.MinValue; // 标记需要生成向量
                    
                    var result = this.Add(tag);
                    if (result != null)
                    {
                        tag.Id = Convert.ToInt32(result);
                        // 异步生成标签向量
                        _ = Task.Run(async () => await GenerateTagVectorAsync(tag.Id));
                        return true;
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"创建或更新标签失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Gets or creates a tag ID based on the tag name
        /// </summary>
        /// <param name="tagName">Tag name</param>
        /// <returns>Tag ID</returns>
        public async Task<int> GetOrCreateTagIdAsync(string tagName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(tagName))
                {
                    _logger.Warn("Cannot get or create tag: Empty tag name");
                    return 0;
                }

                // Clean up the tag name
                tagName = tagName.Trim();
                
                // Check if tag already exists
                var existingTag = GetTagByName(tagName);
                if (existingTag != null)
                {
                    // Update last used time
                    existingTag.LastUsedTime = DateTime.Now;
                    existingTag.ClickCount++;
                    await Task.Run(() => Update(existingTag));
                    
                    _logger.Info($"Found existing tag: {tagName}, ID: {existingTag.Id}");
                    return existingTag.Id;
                }
                
                // Create new tag
                var newTag = new UserInterestTag
                {
                    Name = tagName,
                    Category = "Auto-Generated",
                    Keywords = tagName,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now,
                    LastUsedTime = DateTime.Now,
                    IsActive = 1,
                    ClickCount = 1,
                    VectorUpdateTime = DateTime.MinValue // Mark for vector generation
                };
                
                var result = await Task.Run(() => Add(newTag));
                if (result != null)
                {
                    int tagId = Convert.ToInt32(result);
                    _logger.Info($"Created new tag: {tagName}, ID: {tagId}");
                    
                    // Generate vector asynchronously
                    _ = Task.Run(async () => await GenerateTagVectorAsync(tagId));
                    
                    return tagId;
                }
                
                _logger.Error($"Failed to create new tag: {tagName}");
                return 0;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error getting or creating tag {tagName}: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 获取热门标签
        /// </summary>
        /// <param name="limit">限制数量</param>
        /// <returns>热门标签列表</returns>
        public List<UserInterestTag> GetPopularTags(int limit = 20)
        {
            try
            {
                return this.GetList("IsActive=1", limit, 1, "*", "ClickCount DESC, LastUsedTime DESC");
            }
            catch (Exception ex)
            {
                _logger.Error($"获取热门标签失败: {ex.Message}");
                return new List<UserInterestTag>();
            }
        }

        /// <summary>
        /// 根据分类获取标签
        /// </summary>
        /// <param name="category">分类</param>
        /// <returns>标签列表</returns>
        public List<UserInterestTag> GetTagsByCategory(string category)
        {
            try
            {
                if (string.IsNullOrEmpty(category))
                    return new List<UserInterestTag>();

                return this.GetList($"Category='{category}' AND IsActive=1", int.MaxValue, 1, "*", "ClickCount DESC");
            }
            catch (Exception ex)
            {
                _logger.Error($"根据分类获取标签失败: {ex.Message}");
                return new List<UserInterestTag>();
            }
        }

        /// <summary>
        /// 更新标签点击次数
        /// </summary>
        /// <param name="tagId">标签ID</param>
        /// <returns>操作结果</returns>
        public bool UpdateTagClickCount(int tagId)
        {
            try
            {
                var tag = GetTagById(tagId);
                if (tag == null)
                    return false;

                tag.ClickCount++;
                tag.LastUsedTime = DateTime.Now;
                tag.UpdateTime = DateTime.Now;

                return this.Update(tag);
            }
            catch (Exception ex)
            {
                _logger.Error($"更新标签点击次数失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取标签统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public object GetTagStats()
        {
            try
            {
                var allTags = this.GetList("IsActive=1", int.MaxValue, 1, "*", "");
                var totalTags = allTags.Count;
                var totalClicks = allTags.Sum(t => t.ClickCount);
                var avgClicks = totalTags > 0 ? (double)totalClicks / totalTags : 0;

                var categoryStats = allTags
                    .GroupBy(t => t.Category)
                    .Select(g => new
                    {
                        Category = g.Key,
                        Count = g.Count(),
                        TotalClicks = g.Sum(t => t.ClickCount),
                        AvgClicks = g.Count() > 0 ? (double)g.Sum(t => t.ClickCount) / g.Count() : 0
                    })
                    .ToList();

                return new
                {
                    TotalTags = totalTags,
                    TotalClicks = totalClicks,
                    AverageClicks = Math.Round(avgClicks, 2),
                    CategoryStats = categoryStats
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"获取标签统计信息失败: {ex.Message}");
                return null;
            }
        }

        #region 标签向量化功能

        /// <summary>
        /// 生成标签向量
        /// </summary>
        /// <param name="tagId">标签ID</param>
        /// <returns>生成结果</returns>
        public async Task<bool> GenerateTagVectorAsync(int tagId)
        {
            try
            {
                _logger.Info($"开始生成标签向量，标签ID: {tagId}");
                
                var tag = GetTagById(tagId);
                if (tag == null)
                {
                    _logger.Warn($"标签不存在，标签ID: {tagId}");
                    return false;
                }

                // 构建标签向量化文本
                var vectorText = BuildTagVectorText(tag);
                
                // 调用统一的向量服务生成向量
                var vectorService = new VectorService();
                var vector = await vectorService.GetTextEmbeddingAsync(vectorText);
                
                if (vector != null && vector.Length == VectorServiceConfig.VECTOR_DIMENSION)
                {
                    // 更新标签的向量信息
                    tag.InterestVector = VectorToString(vector);
                    tag.VectorUpdateTime = DateTime.Now;
                    
                    // 保存到数据库
                    var success = this.Update(tag);
                    if (success)
                    {
                        // 缓存标签向量
                        await CacheTagVectorAsync(tagId, vector);
                        _logger.Info($"标签向量生成成功，标签ID: {tagId}，标签名称: {tag.Name}");
                        return true;
                    }
                    else
                    {
                        _logger.Error($"保存标签向量失败，标签ID: {tagId}");
                        return false;
                    }
                }
                else
                {
                    _logger.Error($"标签向量生成失败，标签ID: {tagId}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"生成标签向量时发生错误: {ex.Message}，标签ID: {tagId}");
                return false;
            }
        }

        /// <summary>
        /// 获取标签向量
        /// </summary>
        /// <param name="tagId">标签ID</param>
        /// <returns>标签向量</returns>
        public async Task<double[]> GetTagVectorAsync(int tagId)
        {
            try
            {
                _logger.Info($"获取标签向量，标签ID: {tagId}");
                
                // 1. 优先从缓存获取
                var cacheKey = $"tag_vector:{tagId}";
                var cachedVector = _cache.GetCache<double[]>(cacheKey);
                if (cachedVector != null)
                {
                    _logger.Info($"✓ 从缓存获取标签向量成功，标签ID: {tagId}");
                    return cachedVector;
                }
                
                // 2. 从数据库获取标签
                var tag = GetTagById(tagId);
                if (tag == null)
                {
                    _logger.Warn($"标签不存在，标签ID: {tagId}");
                    return null;
                }
                
                // 3. 检查是否有向量数据
                if (string.IsNullOrEmpty(tag.InterestVector))
                {
                    _logger.Info($"标签向量为空，需要生成，标签ID: {tagId}");
                    var success = await GenerateTagVectorAsync(tagId);
                    if (success)
                    {
                        // 重新获取生成的向量
                        return await GetTagVectorFromDatabaseAsync(tagId);
                    }
                    return null;
                }
                
                // 4. 解析向量字符串
                var vector = StringToVector(tag.InterestVector);
                if (vector != null)
                {
                    _logger.Info($"✓ 从数据库获取标签向量成功，标签ID: {tagId}，维度: {vector.Length}");
                    
                    // 缓存向量
                    await CacheTagVectorAsync(tagId, vector);
                    return vector;
                }
                else
                {
                    _logger.Error($"解析标签向量失败，标签ID: {tagId}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"获取标签向量失败: {ex.Message}，标签ID: {tagId}");
                return null;
            }
        }

        /// <summary>
        /// 批量获取标签向量
        /// </summary>
        /// <param name="tagIds">标签ID列表</param>
        /// <returns>标签向量字典</returns>
        public async Task<Dictionary<int, double[]>> GetTagVectorsBatchAsync(List<int> tagIds)
        {
            var result = new Dictionary<int, double[]>();
            
            try
            {
                _logger.Info($"批量获取标签向量，标签数量: {tagIds.Count}");
                
                // 分批处理，避免内存压力
                const int batchSize = 50;
                for (int i = 0; i < tagIds.Count; i += batchSize)
                {
                    var batch = tagIds.Skip(i).Take(batchSize);
                    
                    foreach (var tagId in batch)
                    {
                        try
                        {
                            var vector = await GetTagVectorAsync(tagId);
                            if (vector != null)
                            {
                                result[tagId] = vector;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.Error($"获取标签 {tagId} 向量失败: {ex.Message}");
                        }
                    }
                    
                    // 批次间延迟，避免过载
                    if (i + batchSize < tagIds.Count)
                    {
                        await Task.Delay(100);
                    }
                }
                
                _logger.Info($"批量获取标签向量完成，成功获取: {result.Count}/{tagIds.Count}");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"批量获取标签向量失败: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 计算标签相似度
        /// </summary>
        /// <param name="tagId1">标签ID1</param>
        /// <param name="tagId2">标签ID2</param>
        /// <returns>相似度分数</returns>
        public async Task<double> CalculateTagSimilarityAsync(int tagId1, int tagId2)
        {
            try
            {
                var vector1 = await GetTagVectorAsync(tagId1);
                var vector2 = await GetTagVectorAsync(tagId2);
                
                if (vector1 == null || vector2 == null)
                {
                    return 0.0;
                }
                
                return CalculateCosineSimilarity(vector1, vector2);
            }
            catch (Exception ex)
            {
                _logger.Error($"计算标签相似度失败: {ex.Message}");
                return 0.0;
            }
        }

        /// <summary>
        /// 查找相似标签
        /// </summary>
        /// <param name="tagId">目标标签ID</param>
        /// <param name="limit">返回数量限制</param>
        /// <param name="threshold">相似度阈值</param>
        /// <returns>相似标签列表</returns>
        public async Task<List<UserInterestTag>> FindSimilarTagsAsync(int tagId, int limit = 10, double threshold = 0.5)
        {
            try
            {
                var targetVector = await GetTagVectorAsync(tagId);
                if (targetVector == null)
                {
                    return new List<UserInterestTag>();
                }
                
                var allTags = GetPopularTags(100); // 获取热门标签作为候选
                var similarTags = new List<UserInterestTag>();
                
                foreach (var tag in allTags)
                {
                    if (tag.Id == tagId) continue; // 跳过自己
                    
                    var tagVector = await GetTagVectorAsync(tag.Id);
                    if (tagVector != null)
                    {
                        var similarity = CalculateCosineSimilarity(targetVector, tagVector);
                        if (similarity >= threshold)
                        {
                            similarTags.Add(tag);
                        }
                    }
                }
                
                // 按相似度排序并返回限制数量
                return similarTags
                    .OrderByDescending(t => CalculateCosineSimilarity(targetVector, GetTagVectorAsync(t.Id).Result))
                    .Take(limit)
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.Error($"查找相似标签失败: {ex.Message}");
                return new List<UserInterestTag>();
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 构建标签向量化文本
        /// </summary>
        /// <param name="tag">标签对象</param>
        /// <returns>向量化文本</returns>
        private string BuildTagVectorText(UserInterestTag tag)
        {
            var textBuilder = new System.Text.StringBuilder();
            
            // 主要文本：标签名称
            textBuilder.Append(tag.Name);
            
            // 添加关键词
            if (!string.IsNullOrEmpty(tag.Keywords))
            {
                textBuilder.Append(" ").Append(tag.Keywords);
            }
            
            // 添加分类信息
            if (!string.IsNullOrEmpty(tag.Category))
            {
                textBuilder.Append(" ").Append(tag.Category);
            }
            
            return textBuilder.ToString().Trim();
        }



        /// <summary>
        /// 将向量转换为字符串
        /// </summary>
        /// <param name="vector">向量数组</param>
        /// <returns>向量字符串</returns>
        private string VectorToString(double[] vector)
        {
            if (vector == null || vector.Length == 0)
                return string.Empty;
                
            return string.Join(",", vector.Select(v => v.ToString("F6")));
        }

        /// <summary>
        /// 从字符串解析向量
        /// </summary>
        /// <param name="vectorString">向量字符串</param>
        /// <returns>向量数组</returns>
        private double[] StringToVector(string vectorString)
        {
            if (string.IsNullOrEmpty(vectorString))
                return null;
                
            try
            {
                var vectorStrings = vectorString.Split(',');
                var vector = new double[vectorStrings.Length];
                
                for (int i = 0; i < vectorStrings.Length; i++)
                {
                    if (double.TryParse(vectorStrings[i], out double value))
                    {
                        vector[i] = value;
                    }
                }
                
                return vector;
            }
            catch (Exception ex)
            {
                _logger.Error($"解析向量失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 计算余弦相似度
        /// </summary>
        /// <param name="vector1">向量1</param>
        /// <param name="vector2">向量2</param>
        /// <returns>相似度分数</returns>
        private double CalculateCosineSimilarity(double[] vector1, double[] vector2)
        {
            if (vector1 == null || vector2 == null || vector1.Length != vector2.Length)
            {
                return 0.0;
            }

            double dotProduct = 0.0;
            double norm1 = 0.0;
            double norm2 = 0.0;

            for (int i = 0; i < vector1.Length; i++)
            {
                dotProduct += vector1[i] * vector2[i];
                norm1 += vector1[i] * vector1[i];
                norm2 += vector2[i] * vector2[i];
            }

            if (norm1 == 0 || norm2 == 0)
            {
                return 0.0;
            }

            return dotProduct / (Math.Sqrt(norm1) * Math.Sqrt(norm2));
        }

        /// <summary>
        /// 缓存标签向量
        /// </summary>
        /// <param name="tagId">标签ID</param>
        /// <param name="vector">向量</param>
        private async Task CacheTagVectorAsync(int tagId, double[] vector)
        {
            if (VectorServiceConfig.ENABLE_VECTOR_CACHE && vector != null)
            {
                var cacheKey = $"tag_vector:{tagId}";
                _cache.WriteCache(vector, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.VECTOR_CACHE_DAYS));
                _logger.Info($"标签向量已缓存，标签ID: {tagId}");
            }
        }

        /// <summary>
        /// 从数据库获取标签向量
        /// </summary>
        /// <param name="tagId">标签ID</param>
        /// <returns>标签向量</returns>
        private async Task<double[]> GetTagVectorFromDatabaseAsync(int tagId)
        {
            try
            {
                var tag = GetTagById(tagId);
                if (tag == null || string.IsNullOrEmpty(tag.InterestVector))
                {
                    return null;
                }
                
                var vector = StringToVector(tag.InterestVector);
                if (vector != null)
                {
                    await CacheTagVectorAsync(tagId, vector);
                    return vector;
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger.Error($"从数据库获取标签向量失败: {ex.Message}");
                return null;
            }
        }

        #endregion
    }
} 