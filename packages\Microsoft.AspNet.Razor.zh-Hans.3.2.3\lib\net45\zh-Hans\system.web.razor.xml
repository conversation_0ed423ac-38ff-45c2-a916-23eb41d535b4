﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.Razor</name>
  </assembly>
  <members>
    <member name="T:System.Web.Razor.CSharpRazorCodeLanguage">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示基于 C# 语法的 Razor 代码语言。</summary>
    </member>
    <member name="M:System.Web.Razor.CSharpRazorCodeLanguage.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.CSharpRazorCodeLanguage" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Razor.CSharpRazorCodeLanguage.CodeDomProviderType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取代码提供程序的类型。</summary>
      <returns>代码提供程序的类型。</returns>
    </member>
    <member name="M:System.Web.Razor.CSharpRazorCodeLanguage.CreateCodeGenerator(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。基于 C# 代码语言创建新的 Razor 代码生成器。</summary>
      <returns>基于 C# 代码语言新创建的 Razor 代码生成器。</returns>
      <param name="className">所生成代码的类名称。</param>
      <param name="rootNamespaceName">所生成代码的根命名空间名称。</param>
      <param name="sourceFileName">源代码文件名。</param>
      <param name="host">Razor 引擎主机。</param>
    </member>
    <member name="M:System.Web.Razor.CSharpRazorCodeLanguage.CreateCodeParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。针对 C# 代码语言创建新的代码分析器。</summary>
      <returns>针对 C# 代码语言新创建的代码分析器。</returns>
    </member>
    <member name="P:System.Web.Razor.CSharpRazorCodeLanguage.LanguageName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 C# 代码语言的名称。</summary>
      <returns>C# 代码语言的名称。值为“csharp”。</returns>
    </member>
    <member name="T:System.Web.Razor.DocumentParseCompleteEventArgs">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="M:System.Web.Razor.DocumentParseCompleteEventArgs.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="P:System.Web.Razor.DocumentParseCompleteEventArgs.GeneratorResults">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="P:System.Web.Razor.DocumentParseCompleteEventArgs.SourceChange">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="P:System.Web.Razor.DocumentParseCompleteEventArgs.TreeStructureChanged">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.GeneratorResults">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示代码生成的结果。</summary>
    </member>
    <member name="M:System.Web.Razor.GeneratorResults.#ctor(System.Boolean,System.Web.Razor.Parser.SyntaxTree.Block,System.Collections.Generic.IList{System.Web.Razor.Parser.SyntaxTree.RazorError},System.CodeDom.CodeCompileUnit,System.Collections.Generic.IDictionary{System.Int32,System.Web.Razor.Generator.GeneratedCodeMapping})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.GeneratorResults" /> 类的新实例。</summary>
      <param name="success">如果代码生成成功，则为 true；否则为 false。</param>
      <param name="document">文档。</param>
      <param name="parserErrors">分析器错误。</param>
      <param name="generatedCode">生成的代码。</param>
      <param name="designTimeLineMappings">设计时所生成代码映射的字典。</param>
    </member>
    <member name="M:System.Web.Razor.GeneratorResults.#ctor(System.Web.Razor.Parser.SyntaxTree.Block,System.Collections.Generic.IList{System.Web.Razor.Parser.SyntaxTree.RazorError},System.CodeDom.CodeCompileUnit,System.Collections.Generic.IDictionary{System.Int32,System.Web.Razor.Generator.GeneratedCodeMapping})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.GeneratorResults" /> 类的新实例。</summary>
      <param name="document">文档。</param>
      <param name="parserErrors">分析器错误。</param>
      <param name="generatedCode">生成的代码。</param>
      <param name="designTimeLineMappings">设计时所生成代码映射的字典。</param>
    </member>
    <member name="M:System.Web.Razor.GeneratorResults.#ctor(System.Web.Razor.ParserResults,System.CodeDom.CodeCompileUnit,System.Collections.Generic.IDictionary{System.Int32,System.Web.Razor.Generator.GeneratedCodeMapping})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.GeneratorResults" /> 类的新实例。</summary>
      <param name="parserResults">分析器结果。</param>
      <param name="generatedCode">生成的代码。</param>
      <param name="designTimeLineMappings">设计时所生成代码映射的字典。</param>
    </member>
    <member name="P:System.Web.Razor.GeneratorResults.DesignTimeLineMappings">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置设计时所生成代码映射的字典。</summary>
      <returns>设计时所生成代码映射的字典。</returns>
    </member>
    <member name="P:System.Web.Razor.GeneratorResults.GeneratedCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置生成的代码。</summary>
      <returns>生成的代码。</returns>
    </member>
    <member name="T:System.Web.Razor.ParserResults">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Razor 文档的分析结果。</summary>
    </member>
    <member name="M:System.Web.Razor.ParserResults.#ctor(System.Boolean,System.Web.Razor.Parser.SyntaxTree.Block,System.Collections.Generic.IList{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.ParserResults" /> 类的新实例。</summary>
      <param name="success">如果分析成功，则为 true；否则为 false。</param>
      <param name="document">文档语法树中的根节点。</param>
      <param name="errors">在分析期间所发生错误的列表。</param>
    </member>
    <member name="M:System.Web.Razor.ParserResults.#ctor(System.Web.Razor.Parser.SyntaxTree.Block,System.Collections.Generic.IList{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.ParserResults" /> 类的新实例。</summary>
      <param name="document">文档语法树中的根节点。</param>
      <param name="parserErrors">在分析期间所发生错误的列表。</param>
    </member>
    <member name="P:System.Web.Razor.ParserResults.Document">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置文档语法树中的根节点。</summary>
      <returns>文档语法树中的根节点。</returns>
    </member>
    <member name="P:System.Web.Razor.ParserResults.ParserErrors">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置在分析期间所发生错误的列表。</summary>
      <returns>在分析期间所发生错误的列表。</returns>
    </member>
    <member name="P:System.Web.Razor.ParserResults.Success">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置指示分析是否成功的值。</summary>
      <returns>如果分析成功，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Web.Razor.PartialParseResult">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.PartialParseResult.Accepted">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.PartialParseResult.AutoCompleteBlock">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.PartialParseResult.Provisional">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.PartialParseResult.Rejected">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.PartialParseResult.SpanContextChanged">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.RazorCodeLanguage">
      <summary>表示所有 Razor 代码语言的基础。此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="M:System.Web.Razor.RazorCodeLanguage.#ctor">
      <summary>初始化 <see cref="T:System.Web.Razor.RazorCodeLanguage" /> 类的新实例。此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="P:System.Web.Razor.RazorCodeLanguage.CodeDomProviderType">
      <summary>获取 CodeDOM 提供程序的类型。此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
      <returns>CodeDOM 提供程序的类型。</returns>
    </member>
    <member name="M:System.Web.Razor.RazorCodeLanguage.CreateCodeGenerator(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>创建 Razor 代码语言的代码生成器。此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
      <returns>Razor 代码语言的代码生成器。</returns>
      <param name="className">类名。</param>
      <param name="rootNamespaceName">根命名空间的名称。</param>
      <param name="sourceFileName">源文件名称。</param>
      <param name="host">Razor 引擎主机。</param>
    </member>
    <member name="M:System.Web.Razor.RazorCodeLanguage.CreateCodeParser">
      <summary>创建 Razor 代码语言的代码分析器。此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
      <returns>Razor 代码语言的代码分析器。</returns>
    </member>
    <member name="M:System.Web.Razor.RazorCodeLanguage.GetLanguageByExtension(System.String)">
      <summary>使用指定文件扩展名获取 Razor 代码的语言。此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
      <returns>Razor 代码的语言。</returns>
      <param name="fileExtension">文件扩展名。</param>
    </member>
    <member name="P:System.Web.Razor.RazorCodeLanguage.LanguageName">
      <summary>获取当前 Razor 代码的语言名称，即“csharp”或“vb”。此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
      <returns>当前 Razor 代码的语言名称。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorCodeLanguage.Languages">
      <summary>获取 Razor 代码支持的语言列表。此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
      <returns>Razor 代码支持的语言列表。</returns>
    </member>
    <member name="T:System.Web.Razor.RazorDirectiveAttribute">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Razor 指令的特性。</summary>
    </member>
    <member name="M:System.Web.Razor.RazorDirectiveAttribute.#ctor(System.String,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.RazorDirectiveAttribute" /> 类的新实例。</summary>
      <param name="name">特性的名称。</param>
      <param name="value">特性的值。</param>
    </member>
    <member name="M:System.Web.Razor.RazorDirectiveAttribute.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定此实例是否等于指定的对象。</summary>
      <returns>如果对象等于此实例，则为 true；否则为 false。</returns>
      <param name="obj">要与此实例比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.RazorDirectiveAttribute.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的哈希代码。</summary>
      <returns>此实例的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorDirectiveAttribute.Name">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置特性的名称。</summary>
      <returns>特性的名称。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorDirectiveAttribute.TypeId">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取特性的唯一类型 ID。</summary>
      <returns>特性的唯一类型 ID。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorDirectiveAttribute.Value">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置特性的值。</summary>
      <returns>特性的值。</returns>
    </member>
    <member name="T:System.Web.Razor.RazorEditorParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。编辑器为避免每次更改文本后都要对整个文档重新进行分析而使用的分析器。</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEditorParser.#ctor(System.Web.Razor.RazorEngineHost,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。构造编辑器分析器。</summary>
      <param name="host">
        <see cref="T:System.Web.Razor.RazorEngineHost" />，用于定义生成的代码所在的环境。</param>
      <param name="sourceFileName">要在行杂注中使用的物理路径。</param>
    </member>
    <member name="M:System.Web.Razor.RazorEditorParser.CheckForStructureChanges(System.Web.Razor.Text.TextChange)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定更改是否将导致文档结构的变化，如果不会导致，则将更改应用于现有树。如果结构发生变化，则会自动启动重新分析。</summary>
      <returns>指示增量分析结果的 <see cref="T:System.Web.Razor.PartialParseResult" /> 值。</returns>
      <param name="change">要应用于分析树的更改。</param>
    </member>
    <member name="P:System.Web.Razor.RazorEditorParser.CurrentParseTree">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取当前分析树。</summary>
      <returns>当前分析树。</returns>
    </member>
    <member name="M:System.Web.Razor.RazorEditorParser.Dispose">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。释放由 <see cref="T:System.Web.Razor.RazorEditorParser" /> 的当前实例所使用的所有资源。</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEditorParser.Dispose(System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。释放 <see cref="T:System.Web.Razor.RazorEditorParser" /> 类使用的非托管资源并选择性地释放托管资源。</summary>
      <param name="disposing">如果为 true，则同时释放托管资源和非托管资源；如果为 false，则仅释放非托管资源。</param>
    </member>
    <member name="E:System.Web.Razor.RazorEditorParser.DocumentParseComplete">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。当文档的重新分析已全部完成时触发事件。</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEditorParser.FileName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置要分析的文档的文件名。</summary>
      <returns>要分析的文档的文件名。</returns>
    </member>
    <member name="M:System.Web.Razor.RazorEditorParser.GetAutoCompleteString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。检索自动完成字符串。</summary>
      <returns>自动完成字符串。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEditorParser.Host">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置用于分析的主机。</summary>
      <returns>用于分析的主机。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEditorParser.LastResultProvisional">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置指示是否已经暂时接受上次分析结果以进行下一分部分析的值。</summary>
      <returns>如果已经暂时接受上次分析结果进行下一分部分析，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Web.Razor.RazorEngineHost">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示针对 Razor 引擎主机生成的代码。</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.RazorEngineHost" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.#ctor(System.Web.Razor.RazorCodeLanguage)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.RazorEngineHost" /> 类的新实例。</summary>
      <param name="codeLanguage">指定的代码语言。</param>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.#ctor(System.Web.Razor.RazorCodeLanguage,System.Func{System.Web.Razor.Parser.ParserBase})">
      <summary>初始化 <see cref="T:System.Web.Razor.RazorEngineHost" /> 类的新实例。</summary>
      <param name="codeLanguage">指定的代码语言。</param>
      <param name="markupParserFactory">标记分析器工厂。</param>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.CodeLanguage">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取代码生成器支持的语言。</summary>
      <returns>代码生成器支持的语言。</returns>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.CreateMarkupParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用指定语言分析器为 <see cref="T:System.Web.Razor.RazorEngineHost" /> 创建标记分析器。</summary>
      <returns>要使用指定语言分析器为 <see cref="T:System.Web.Razor.RazorEngineHost" /> 创建的标记分析器。</returns>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.DecorateCodeGenerator(System.Web.Razor.Generator.RazorCodeGenerator)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。以语言特定的 Razor 代码生成器形式返回方法。</summary>
      <returns>语言特定的 Razor 代码生成器形式的方法。</returns>
      <param name="incomingCodeGenerator">C# 或 Visual Basic 代码生成器。</param>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.DecorateCodeParser(System.Web.Razor.Parser.ParserBase)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用指定语言分析器以语言特定的 Razor 代码分析器形式返回方法。</summary>
      <returns>使用指定语言分析器返回的语言特定 Razor 代码分析器形式的方法。</returns>
      <param name="incomingCodeParser">C# 或 Visual Basic 代码分析器。</param>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.DecorateMarkupParser(System.Web.Razor.Parser.ParserBase)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用指定语言分析器返回用于修饰标记分析器的方法。</summary>
      <returns>使用指定语言分析器来修饰标记分析器时所用的方法。</returns>
      <param name="incomingMarkupParser">C# 或 Visual Basic 代码分析器。</param>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.DefaultBaseClass">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置主机的默认基类。</summary>
      <returns>主机的默认基类。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.DefaultClassName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置主机的默认类名。</summary>
      <returns>主机的默认类名。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.DefaultNamespace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置主机的默认命名空间。</summary>
      <returns>主机的默认命名空间。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.DesignTimeMode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置用于指示模式是否设计主机时间的值。</summary>
      <returns>如果模式为主机设计时间，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.EnableInstrumentation">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置启用检测的主机。</summary>
      <returns>启用检测的主机。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.GeneratedClassContext">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置为主机生成的类上下文。</summary>
      <returns>为主机生成的类上下文。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.InstrumentedSourceFilePath">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置检测到的主机源文件路径。</summary>
      <returns>检测到的主机源文件路径。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.IsIndentingWithTabs">
      <summary>获取或设置设计时编辑器是使用制表符还是使用空格进行缩进。</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.NamespaceImports">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取主机的命名空间导入。</summary>
      <returns>主机的命名空间导入。</returns>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.PostProcessGeneratedCode(System.CodeDom.CodeCompileUnit,System.CodeDom.CodeNamespace,System.CodeDom.CodeTypeDeclaration,System.CodeDom.CodeMemberMethod)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此方法以发布主机的所有已处理的生成代码。</summary>
      <param name="codeCompileUnit">代码编译单元。</param>
      <param name="generatedNamespace">生成的命名空间。</param>
      <param name="generatedClass">生成的类。</param>
      <param name="executeMethod">执行方法。</param>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.PostProcessGeneratedCode(System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此方法以发布主机的所有已处理的生成代码。</summary>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.StaticHelpers">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置主机的静态帮助器。</summary>
      <returns>主机的静态帮助器。</returns>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.TabSize">
      <summary>使用制表符缩进时托管编辑器所用的制表符大小。</summary>
    </member>
    <member name="T:System.Web.Razor.RazorTemplateEngine">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Razor 模板引擎的入口点。</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.#ctor(System.Web.Razor.RazorEngineHost)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.RazorTemplateEngine" /> 类的新实例。</summary>
      <param name="host">主机。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.CreateCodeGenerator(System.String,System.String,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建代码生成器。</summary>
      <returns>创建的 <see cref="T:System.Web.Razor.Generator.RazorCodeGenerator" />。</returns>
      <param name="className">所生成类的名称。</param>
      <param name="rootNamespace">所生成类将驻留的命名空间。</param>
      <param name="sourceFileName">要在行杂注中使用的文件名。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.CreateParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建 <see cref="T:System.Web.Razor.Parser.RazorParser" />。</summary>
      <returns>创建的 <see cref="T:System.Web.Razor.Parser.RazorParser" />。</returns>
    </member>
    <member name="F:System.Web.Razor.RazorTemplateEngine.DefaultClassName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示模板的默认类名。</summary>
    </member>
    <member name="F:System.Web.Razor.RazorTemplateEngine.DefaultNamespace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示模板的默认命名空间。</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.IO.TextReader)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析由 TextBuffer 指定的模板，为其生成代码，并返回构造的 CodeDOM 树。</summary>
      <returns>生成的分析树和生成的 Code DOM 树。</returns>
      <param name="input">要分析的输入文本。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.IO.TextReader,System.Nullable{System.Threading.CancellationToken})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析由 TextBuffer 指定的模板，为其生成代码，并返回构造的 CodeDOM 树。</summary>
      <returns>生成的分析树和生成的 Code DOM 树。</returns>
      <param name="input">要分析的输入文本。</param>
      <param name="cancelToken">用于取消分析器的令牌。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.IO.TextReader,System.String,System.String,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析由 TextBuffer 指定的模板，为其生成代码，并返回构造的 CodeDOM 树。</summary>
      <returns>生成的分析树和生成的 Code DOM 树。</returns>
      <param name="input">要分析的输入文本。</param>
      <param name="className">所生成类的名称，用于覆盖在主机中指定的内容。</param>
      <param name="rootNamespace">所生成类将驻留的命名空间。</param>
      <param name="sourceFileName">要在行杂注中使用的文件名。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.IO.TextReader,System.String,System.String,System.String,System.Nullable{System.Threading.CancellationToken})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析由 TextBuffer 指定的模板，为其生成代码，并返回构造的 CodeDOM 树。</summary>
      <returns>生成的分析树和生成的 Code DOM 树。</returns>
      <param name="input">要分析的输入文本。</param>
      <param name="className">所生成类的名称，用于覆盖在主机中指定的内容。</param>
      <param name="rootNamespace">所生成类将驻留的命名空间。</param>
      <param name="sourceFileName">要在行杂注中使用的文件名。</param>
      <param name="cancelToken">用于取消分析器的令牌。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.Web.Razor.Text.ITextBuffer)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析由 TextBuffer 指定的模板，为其生成代码，并返回构造的 CodeDOM 树。</summary>
      <returns>生成的分析树和生成的 Code DOM 树。</returns>
      <param name="input">要分析的输入文本。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.Web.Razor.Text.ITextBuffer,System.Nullable{System.Threading.CancellationToken})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析由 TextBuffer 指定的模板，为其生成代码，并返回构造的 CodeDOM 树。</summary>
      <returns>生成的分析树和生成的 Code DOM 树。</returns>
      <param name="input">要分析的输入文本。</param>
      <param name="cancelToken">用于取消分析器的令牌。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.Web.Razor.Text.ITextBuffer,System.String,System.String,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析由 TextBuffer 指定的模板，为其生成代码，并返回构造的 CodeDOM 树。</summary>
      <returns>生成的分析树和生成的 Code DOM 树。</returns>
      <param name="input">要分析的输入文本。</param>
      <param name="className">所生成类的名称，用于覆盖在主机中指定的内容。</param>
      <param name="rootNamespace">所生成类将驻留的命名空间。</param>
      <param name="sourceFileName">要在行杂注中使用的文件名。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.Web.Razor.Text.ITextBuffer,System.String,System.String,System.String,System.Nullable{System.Threading.CancellationToken})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析由 TextBuffer 指定的模板，为其生成代码，并返回构造的 CodeDOM 树。</summary>
      <returns>生成的分析树和生成的 Code DOM 树。</returns>
      <param name="input">要分析的输入文本。</param>
      <param name="className">所生成类的名称，用于覆盖在主机中指定的内容。</param>
      <param name="rootNamespace">所生成类将驻留的命名空间。</param>
      <param name="sourceFileName">要在行杂注中使用的文件名。</param>
      <param name="cancelToken">用于取消分析器的令牌。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCodeCore(System.Web.Razor.Text.ITextDocument,System.String,System.String,System.String,System.Nullable{System.Threading.CancellationToken})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成代码核心。</summary>
      <returns>所生成核心的结果。</returns>
      <param name="input">要分析的输入文本。</param>
      <param name="className">所生成类的名称，用于覆盖在主机中指定的内容。</param>
      <param name="rootNamespace">所生成类将驻留的命名空间。</param>
      <param name="sourceFileName">要在行杂注中使用的文件名。</param>
      <param name="cancelToken">用于取消分析器的令牌。</param>
    </member>
    <member name="P:System.Web.Razor.RazorTemplateEngine.Host">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置用于定义生成的模板代码所在的环境的 <see cref="T:System.Web.Razor.RazorEngineHost" />。</summary>
      <returns>
        <see cref="T:System.Web.Razor.RazorEngineHost" />，用于定义生成的模板代码所在的环境。</returns>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.ParseTemplate(System.IO.TextReader)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析由 TextBuffer 指定的模板并返回其结果。</summary>
      <returns>生成的分析树。</returns>
      <param name="input">要分析的输入文本。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.ParseTemplate(System.IO.TextReader,System.Nullable{System.Threading.CancellationToken})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析由 TextBuffer 指定的模板并返回其结果。</summary>
      <returns>生成的分析树。</returns>
      <param name="input">要分析的输入文本。</param>
      <param name="cancelToken">用于取消分析器的令牌。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.ParseTemplate(System.Web.Razor.Text.ITextBuffer)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析由 TextBuffer 指定的模板并返回其结果。</summary>
      <returns>生成的分析树。</returns>
      <param name="input">要分析的输入文本。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.ParseTemplate(System.Web.Razor.Text.ITextBuffer,System.Nullable{System.Threading.CancellationToken})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析由 TextBuffer 指定的模板并返回其结果。</summary>
      <returns>生成的分析树。</returns>
      <param name="input">要分析的输入文本。</param>
      <param name="cancelToken">用于取消分析器的令牌。</param>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.ParseTemplateCore(System.Web.Razor.Text.ITextDocument,System.Nullable{System.Threading.CancellationToken})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析模板核心。</summary>
      <returns>生成的分析树。</returns>
      <param name="input">要分析的输入文本。</param>
      <param name="cancelToken">用于取消分析器的令牌。</param>
    </member>
    <member name="T:System.Web.Razor.StateMachine`1">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示计算机的状态。</summary>
      <typeparam name="TReturn">泛型类型 Return。</typeparam>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.StateMachine`1" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Razor.StateMachine`1.CurrentState">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置计算机的当前状态。</summary>
      <returns>计算机的当前状态。</returns>
    </member>
    <member name="P:System.Web.Razor.StateMachine`1.StartState">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取计算机的起始状态。</summary>
      <returns>计算机的起始状态。</returns>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Stay">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在过渡期间保持在计算机中。</summary>
      <returns>计算机状态的过渡。</returns>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Stay(`0)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在具有指定输出的过渡期间保持在计算机中。</summary>
      <returns>过渡的输出。</returns>
      <param name="output">输出。</param>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Stop">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。过渡时禁用计算机。</summary>
      <returns>要停止的计算机。</returns>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Transition(System.Web.Razor.StateMachine{`0}.State)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示状态的新过渡。</summary>
      <returns>状态的新过渡。</returns>
      <param name="newState">新状态。</param>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Transition(`0,System.Web.Razor.StateMachine{`0}.State)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示具有指定输出的状态的新过渡。</summary>
      <returns>具有指定输出的状态的新过渡。</returns>
      <param name="output">输出。</param>
      <param name="newState">新状态。</param>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Turn">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。描述状态的转换过程。</summary>
      <returns>状态的转换过程。</returns>
    </member>
    <member name="T:System.Web.Razor.StateMachine`1.State">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.StateMachine`1.StateResult">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示状态结果。</summary>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.StateResult.#ctor(System.Web.Razor.StateMachine{`0}.State)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.StateMachine`1.StateResult" /> 类的新实例。</summary>
      <param name="next">下一个输出。</param>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.StateResult.#ctor(`0,System.Web.Razor.StateMachine{`0}.State)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.StateMachine`1.StateResult" /> 类的新实例。</summary>
      <param name="output">输出。</param>
      <param name="next">下一个状态。</param>
    </member>
    <member name="P:System.Web.Razor.StateMachine`1.StateResult.HasOutput">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置指示状态是否具有输出的值。</summary>
      <returns>如果状态具有输出，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.StateMachine`1.StateResult.Next">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置计算机的下一个状态。</summary>
      <returns>计算机的下一个状态。</returns>
    </member>
    <member name="P:System.Web.Razor.StateMachine`1.StateResult.Output">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置输出。</summary>
      <returns>表示输出的 <see cref="T:System.Web.Razor.StateMachine`1.State" />。</returns>
    </member>
    <member name="T:System.Web.Razor.VBRazorCodeLanguage">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 VB Razor 代码的语言生成器和提供程序。</summary>
    </member>
    <member name="M:System.Web.Razor.VBRazorCodeLanguage.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.VBRazorCodeLanguage" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Razor.VBRazorCodeLanguage.CodeDomProviderType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 CodeDomProvider 的类型。</summary>
      <returns>CodeDomProvider 的类型。</returns>
    </member>
    <member name="M:System.Web.Razor.VBRazorCodeLanguage.CreateCodeGenerator(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建代码语言生成器。</summary>
      <returns>代码语言生成器。</returns>
      <param name="className">类的名称。</param>
      <param name="rootNamespaceName">根命名空间名称。</param>
      <param name="sourceFileName">源文件名称。</param>
      <param name="host">
        <see cref="T:System.Web.Razor.RazorEngineHost" />。</param>
    </member>
    <member name="M:System.Web.Razor.VBRazorCodeLanguage.CreateCodeParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在 <see cref="T:System.Web.Razor.Parser.ParserBase" /> 中创建代码分析器。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.ParserBase" /> 中的代码分析器。</returns>
    </member>
    <member name="P:System.Web.Razor.VBRazorCodeLanguage.LanguageName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取语言名称。</summary>
      <returns>语言名称。</returns>
    </member>
    <member name="T:System.Web.Razor.Editor.EditorHints">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Editor.EditorHints.LayoutPage">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Editor.EditorHints.None">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Editor.EditorHints.VirtualPath">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Editor.EditResult">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示编辑器的编辑结果。</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.EditResult.#ctor(System.Web.Razor.PartialParseResult,System.Web.Razor.Parser.SyntaxTree.SpanBuilder)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Editor.EditResult" /> 类的新实例。</summary>
      <param name="result">分部分析结果。</param>
      <param name="editedSpan">编辑的跨度生成器。</param>
    </member>
    <member name="P:System.Web.Razor.Editor.EditResult.EditedSpan">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置 <see cref="T:System.Web.Razor.Parser.SyntaxTree.SpanBuilder" /> 的编辑跨度。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.SyntaxTree.SpanBuilder" /> 的编辑跨度。</returns>
    </member>
    <member name="P:System.Web.Razor.Editor.EditResult.Result">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置分部分析结果。</summary>
      <returns>分部分析结果。</returns>
    </member>
    <member name="T:System.Web.Razor.Editor.ImplicitExpressionEditHandler">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。提供隐式表达式的编辑处理程序。</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.ImplicitExpressionEditHandler.#ctor(System.Func{System.String,System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}},System.Collections.Generic.ISet{System.String},System.Boolean)">
      <summary>初始化 <see cref="T:System.Web.Razor.Editor.ImplicitExpressionEditHandler" /> 类的新实例。</summary>
      <param name="tokenizer">标记器。</param>
      <param name="keywords">关键字。</param>
      <param name="acceptTrailingDot">如果接受尾随点号，则为 true；否则为 false。</param>
    </member>
    <member name="P:System.Web.Razor.Editor.ImplicitExpressionEditHandler.AcceptTrailingDot">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置用于指示表达式是否接受尾随点号的值。</summary>
      <returns>如果表达式接受尾随点号，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Editor.ImplicitExpressionEditHandler.CanAcceptChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示可以接受更改的分析。</summary>
      <returns>分部分析结果。</returns>
      <param name="target">目标。</param>
      <param name="normalizedChange">规范化更改。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.ImplicitExpressionEditHandler.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示指定对象是否等于当前对象。</summary>
      <returns>如果指定对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.ImplicitExpressionEditHandler.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。检索此当前实例的哈希代码。</summary>
      <returns>此当前实例的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Editor.ImplicitExpressionEditHandler.Keywords">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置与表达式关联的关键字。</summary>
      <returns>与表达式关联的关键字。</returns>
    </member>
    <member name="M:System.Web.Razor.Editor.ImplicitExpressionEditHandler.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此当前实例的字符串表示形式。</summary>
      <returns>此当前实例的字符串表示形式。</returns>
    </member>
    <member name="T:System.Web.Razor.Editor.SingleLineMarkupEditHandler">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示网页的处理程序编辑器。</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SingleLineMarkupEditHandler.#ctor(System.Func{System.String,System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}})">
      <summary>初始化 <see cref="T:System.Web.Razor.Editor.SingleLineMarkupEditHandler" /> 类的新实例。</summary>
      <param name="tokenizer">标记器符号。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SingleLineMarkupEditHandler.#ctor(System.Func{System.String,System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}},System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters)">
      <summary>初始化 <see cref="T:System.Web.Razor.Editor.SingleLineMarkupEditHandler" /> 类的新实例。</summary>
      <param name="tokenizer">标记器符号。</param>
      <param name="accepted">接受的字符。</param>
    </member>
    <member name="T:System.Web.Razor.Editor.SpanEditHandler">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。提供跨度编辑的处理方法。</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.#ctor(System.Func{System.String,System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Editor.SpanEditHandler" /> 类的新实例。</summary>
      <param name="tokenizer">用于将字符串分析为令牌的方法。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.#ctor(System.Func{System.String,System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}},System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Editor.SpanEditHandler" /> 类的新实例。</summary>
      <param name="tokenizer">用于将字符串分析为令牌的方法。</param>
      <param name="accepted">
        <see cref="T:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters" /> 枚举的值之一。</param>
    </member>
    <member name="P:System.Web.Razor.Editor.SpanEditHandler.AcceptedCharacters">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置用于指定已接受字符的值。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters" /> 枚举的值之一。</returns>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.ApplyChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将文本更改应用于跨度。</summary>
      <returns>应用操作的结果。</returns>
      <param name="target">要应用更改的跨度。</param>
      <param name="change">要应用的更改。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.ApplyChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange,System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将文本更改应用于跨度。</summary>
      <returns>应用操作的结果。</returns>
      <param name="target">要应用更改的跨度。</param>
      <param name="change">要应用的更改。</param>
      <param name="force">如果接受分步结果，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.CanAcceptChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定跨度是否可以接受指定的更改。</summary>
      <returns>如果跨度可以接受指定的更改，则为 true；否则为 false。</returns>
      <param name="target">要检查的跨度。</param>
      <param name="normalizedChange">要应用的更改。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.CreateDefault">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建新的默认跨度编辑处理程序。</summary>
      <returns>新创建的默认跨度编辑处理程序。</returns>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.CreateDefault(System.Func{System.String,System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建新的默认跨度编辑处理程序。</summary>
      <returns>新创建的默认跨度编辑处理程序。</returns>
      <param name="tokenizer">用于将字符串分析为令牌的方法。</param>
    </member>
    <member name="P:System.Web.Razor.Editor.SpanEditHandler.EditorHints">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置编辑器提示。</summary>
      <returns>编辑器提示。</returns>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定此实例是否等于指定的对象。</summary>
      <returns>如果对象等于此实例，则为 true；否则为 false。</returns>
      <param name="obj">要与此实例比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的哈希代码。</summary>
      <returns>此实例的哈希代码。</returns>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.GetOldText(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。从跨度内容获取旧文本。</summary>
      <returns>跨度内容中的旧文本。</returns>
      <param name="target">要从中获取旧文本的跨度。</param>
      <param name="change">包含旧文本位置的文本更改。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.IsAtEndOfFirstLine(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的更改是否位于跨度内容第一行的末尾。</summary>
      <returns>如果指定的更改位于跨度内容第一行的末尾，则为 true；否则为 false。</returns>
      <param name="target">要检查的跨度。</param>
      <param name="change">要检查的更改。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.IsAtEndOfSpan(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的更改是否位于跨度的末尾。</summary>
      <returns>如果指定的更改位于跨度的末尾，则为 true；否则为 false。</returns>
      <param name="target">要检查的跨度。</param>
      <param name="change">要检查的更改。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.IsEndDeletion(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的更改是否位于跨度内容的末尾以待删除。</summary>
      <returns>如果指定的更改位于跨度的末尾以待删除，则为 true；否则为 false。</returns>
      <param name="target">要检查的跨度。</param>
      <param name="change">要检查的更改。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.IsEndInsertion(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的更改是否位于跨度内容的末尾以待插入。</summary>
      <returns>如果指定的更改位于跨度的末尾以待插入，则为 true；否则为 false。</returns>
      <param name="target">要检查的跨度。</param>
      <param name="change">要检查的更改。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.IsEndReplace(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的更改是否位于跨度内容的末尾以待替换。</summary>
      <returns>如果指定的更改位于跨度的末尾以待替换，则为 true；否则为 false。</returns>
      <param name="target">要检查的跨度。</param>
      <param name="change">要检查的更改。</param>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.OwnsChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定跨度是否拥有指定的更改。</summary>
      <returns>如果跨度拥有指定的更改，则为 true；否则为 false。</returns>
      <param name="target">要检查的跨度。</param>
      <param name="change">要检查的更改。</param>
    </member>
    <member name="P:System.Web.Razor.Editor.SpanEditHandler.Tokenizer">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置用于将字符串分析为令牌的方法。</summary>
      <returns>用于将字符串分析为令牌的方法。</returns>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回跨度编辑处理程序的字符串表示形式。</summary>
      <returns>跨度编辑处理程序的字符串表示形式。</returns>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.UpdateSpan(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用规范化更改更新跨度。</summary>
      <returns>指定目标的新跨度生成器。</returns>
      <param name="target">要更新的跨度。</param>
      <param name="normalizedChange">规范化更改。</param>
    </member>
    <member name="T:System.Web.Razor.Generator.AddImportCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示针对 Razor 添加的导入代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.AddImportCodeGenerator.#ctor(System.String,System.Int32)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.AddImportCodeGenerator" /> 类的新实例。</summary>
      <param name="ns">字符串命名空间。</param>
      <param name="namespaceKeywordLength">关键字命名空间的长度。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.AddImportCodeGenerator.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定两个对象实例是否相等。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.AddImportCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用添加的导入代码生成器生成具有指定参数的代码。</summary>
      <param name="target">目标跨度。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.AddImportCodeGenerator.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的哈希代码。</summary>
      <returns>此实例的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.AddImportCodeGenerator.Namespace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取要添加导入代码生成器的生成器的字符串命名空间。</summary>
      <returns>要添加导入代码生成器的生成器的字符串命名空间。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.AddImportCodeGenerator.NamespaceKeywordLength">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置代码生成器的关键字命名空间的长度。</summary>
      <returns>代码生成器的关键字命名空间的长度。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.AddImportCodeGenerator.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回表示当前对象的字符串。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.AttributeBlockCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示块代码生成器的特性。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.#ctor(System.String,System.Web.Razor.Text.LocationTagged{System.String},System.Web.Razor.Text.LocationTagged{System.String})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.AttributeBlockCodeGenerator" /> 类的新实例。</summary>
      <param name="name">名称。</param>
      <param name="prefix">前缀字符串。</param>
      <param name="suffix">后缀字符串。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于当前对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用指定参数生成用于结束块的代码。</summary>
      <param name="target">目标块。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用指定参数生成用于开始块的代码。</summary>
      <param name="target">目标块。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此代码生成器的哈希代码。</summary>
      <returns>此代码生成器的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.AttributeBlockCodeGenerator.Name">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 <see cref="T:System.Web.Razor.Generator.AttributeBlockCodeGenerator" /> 的字符串名称。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Generator.AttributeBlockCodeGenerator" /> 的字符串名称。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.AttributeBlockCodeGenerator.Prefix">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取代码生成器的前缀。</summary>
      <returns>代码生成器的前缀。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.AttributeBlockCodeGenerator.Suffix">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取代码生成器的后缀。</summary>
      <returns>代码生成器的后缀。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回表示当前对象的字符串。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.BlockCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示此 Razor 语法的块代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.BlockCodeGenerator.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.BlockCodeGenerator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.BlockCodeGenerator.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于当前对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.BlockCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。针对此 Razor 语法生成块代码生成器的结尾。</summary>
      <param name="target">目标块。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.BlockCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。针对此 Razor 语法生成块代码生成器的开头。</summary>
      <param name="target">目标块。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.BlockCodeGenerator.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回块代码生成器的哈希代码。</summary>
      <returns>块代码生成器的哈希代码。</returns>
    </member>
    <member name="F:System.Web.Razor.Generator.BlockCodeGenerator.Null">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示块代码生成器的 null 值。</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示用于代码生成的事件参数完成。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs.#ctor(System.String,System.String,System.CodeDom.CodeCompileUnit)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs" /> 类的新实例。</summary>
      <param name="virtualPath">虚拟路径字符串。</param>
      <param name="physicalPath">物理路径字符串。</param>
      <param name="generatedCode">生成的代码编译单元。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs.GeneratedCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取完成事件参数所需的已生成代码。</summary>
      <returns>完成事件参数所需的已生成代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs.PhysicalPath">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取代码生成的物理路径。</summary>
      <returns>代码生成的物理路径。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs.VirtualPath">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取代码生成的虚拟路径。</summary>
      <returns>代码生成的虚拟路径。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.CodeGeneratorContext">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示代码生成器的上下文。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.AddCodeMapping(System.Web.Razor.Text.SourceLocation,System.Int32,System.Int32)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。添加映射到集合的新生成代码。</summary>
      <returns>新添加的代码映射的集合索引。</returns>
      <param name="sourceLocation">所生成代码映射的源位置。</param>
      <param name="generatedCodeStart">所生成代码映射的代码起始位置。</param>
      <param name="generatedCodeLength">所生成代码映射的长度。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.AddContextCall(System.Web.Razor.Parser.SyntaxTree.Span,System.String,System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。为指定方法中的上下文调用生成代码语句。</summary>
      <param name="contentSpan">内容跨度。</param>
      <param name="methodName">执行上下文调用的方法的名称。</param>
      <param name="isLiteral">如果指定方法参数为文本，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.AddDesignTimeHelperStatement(System.CodeDom.CodeSnippetStatement)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。添加在指定的代码语句中插入 Razor 设计时帮助器方法的代码语句。</summary>
      <param name="statement">接收代码插入的代码语句。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.AddStatement(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将指定的代码语句添加到目标方法的正文中。</summary>
      <param name="generatedCode">用于添加目标方法的代码语句。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.AddStatement(System.String,System.CodeDom.CodeLinePragma)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将指定的代码语句添加到目标方法的正文中。</summary>
      <param name="body">用于添加目标方法的代码语句。</param>
      <param name="pragma">行杂注。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.BufferStatementFragment(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将指定的片段附加到当前缓冲的语句。</summary>
      <param name="fragment">要添加的片段。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.BufferStatementFragment(System.String,System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将指定的片段附加到当前缓冲的语句。</summary>
      <param name="fragment">要添加的片段。</param>
      <param name="sourceSpan">
        <paramref name="fragment" /> 的源跨度。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.BufferStatementFragment(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将指跨度内容附加到当前缓冲的语句。</summary>
      <param name="sourceSpan">要添加其内容的源跨度。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.ChangeStatementCollector(System.Action{System.String,System.CodeDom.CodeLinePragma})">
      <summary>分配新语句收集器并返回用于还原旧语句收集器的可释放操作。</summary>
      <returns>用于还原旧语句收集器的可释放操作。</returns>
      <param name="collector">新语句收集器。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.CodeMappings">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置所生成代码映射的字典收集。</summary>
      <returns>所生成代码映射的字典收集。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.CompileUnit">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置将用于保存程序图的代码编译单元。</summary>
      <returns>将用于保存程序图的代码编译单元。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.Create(System.Web.Razor.RazorEngineHost,System.String,System.String,System.String,System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建 <see cref="T:System.Web.Razor.Generator.CodeGeneratorContext" /> 类的新实例。</summary>
      <returns>代码生成器上下文的新创建的实例。</returns>
      <param name="host">Razor 引擎主机。</param>
      <param name="className">所生成类的类型声明的类名称。</param>
      <param name="rootNamespace">所生成命名空间声明的名称。</param>
      <param name="sourceFile">源文件。</param>
      <param name="shouldGenerateLinePragmas">如果要启用行杂注生成，则为 true；否则为 false。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.CurrentBufferedStatement">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取当前缓冲的语句。</summary>
      <returns>当前缓冲的语句。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.EnsureExpressionHelperVariable">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将表达式帮助器变量添加到生成的类（如果尚未添加）。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.FlushBufferedStatement">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。刷新当前缓冲的语句。</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.GeneratedClass">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置生成类的类型声明。</summary>
      <returns>生成类的类型声明。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.GenerateLinePragma(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成指定源的行杂注。</summary>
      <returns>指定源的行杂注。</returns>
      <param name="target">源跨度。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.GenerateLinePragma(System.Web.Razor.Parser.SyntaxTree.Span,System.Int32)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成源的行杂注。</summary>
      <returns>指定源的行杂注。</returns>
      <param name="target">源跨度。</param>
      <param name="generatedCodeStart">代码的起始索引。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.GenerateLinePragma(System.Web.Razor.Parser.SyntaxTree.Span,System.Int32,System.Int32)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成源的行杂注。</summary>
      <returns>指定源的行杂注。</returns>
      <param name="target">源跨度。</param>
      <param name="generatedCodeStart">代码的起始索引。</param>
      <param name="codeLength">代码的长度。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.GenerateLinePragma(System.Web.Razor.Text.SourceLocation,System.Int32,System.Int32)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成源的行杂注。</summary>
      <returns>指定源的行杂注。</returns>
      <param name="start">源位置。</param>
      <param name="generatedCodeStart">代码的起始索引。</param>
      <param name="codeLength">代码的长度。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.Host">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置 Razor 引擎主机。</summary>
      <returns>Razor 引擎主机。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.MarkEndOfGeneratedCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。标记所生成代码的末尾。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.MarkStartOfGeneratedCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。标记所生成代码的开头。</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.Namespace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置生成的命名空间声明。</summary>
      <returns>生成的命名空间声明。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.SourceFile">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置源文件。</summary>
      <returns>源文件。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.TargetMethod">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置生成的成员方法。</summary>
      <returns>生成的成员方法。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.TargetWriterName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置文本编写器的名称。</summary>
      <returns>文本编写器的名称。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.CSharpRazorCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 C# 语言的 Razor 代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CSharpRazorCodeGenerator.#ctor(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.CSharpRazorCodeGenerator" /> 类的新实例。</summary>
      <param name="className">所生成类的类型声明的类名称。</param>
      <param name="rootNamespaceName">所生成命名空间声明的名称。</param>
      <param name="sourceFileName">源文件。</param>
      <param name="host">Razor 引擎主机。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.CSharpRazorCodeGenerator.Initialize(System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化此代码生成器的上下文。</summary>
      <param name="context">此代码生成器的上下文。</param>
    </member>
    <member name="T:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示块代码生成器的动态特性。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.#ctor(System.Web.Razor.Text.LocationTagged{System.String},System.Int32,System.Int32,System.Int32)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator" /> 类的新实例。</summary>
      <param name="prefix">前缀。</param>
      <param name="offset">偏移值。</param>
      <param name="line">行值。</param>
      <param name="col">列。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.#ctor(System.Web.Razor.Text.LocationTagged{System.String},System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator" /> 类的新实例。</summary>
      <param name="prefix">字符串前缀。</param>
      <param name="valueStart">start 值。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于当前对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用指定参数生成用于结束块的代码。</summary>
      <param name="target">目标块。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用指定参数生成用于开始块的代码。</summary>
      <param name="target">目标块。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的哈希代码。</summary>
      <returns>此实例的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.Prefix">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取代码生成器的命名空间前缀。</summary>
      <returns>代码生成器的命名空间前缀。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回表示当前对象的字符串。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.ValueStart">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取动态特性块代码生成器的 start 值。</summary>
      <returns>动态特性块代码生成器的 start 值。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.ExpressionCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示表达式的代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.ExpressionCodeGenerator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示该实例与指定对象是否相等。</summary>
      <returns>如果 <paramref name="obj" /> 与该实例属于相同类型且表示相同值，则为 true；否则为 false。</returns>
      <param name="obj">要与当前实例比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。为表达式生成代码。</summary>
      <param name="target">其内容表示表达式的源跨度。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成块的结束代码。</summary>
      <param name="target">用于生成结束代码的目标块。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成块的起始代码。</summary>
      <param name="target">用于生成起始代码的目标块。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的哈希代码。</summary>
      <returns>此实例的哈希代码。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的字符串表示形式。</summary>
      <returns>此实例的字符串表示形式。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.ExpressionRenderingMode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.ExpressionRenderingMode.InjectCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.ExpressionRenderingMode.WriteToOutput">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.GeneratedClassContext">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示生成的类上下文。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.#ctor(System.String,System.String,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.GeneratedClassContext" /> 类的新实例。</summary>
      <param name="executeMethodName">执行方法名称。</param>
      <param name="writeMethodName">写入方法名称。</param>
      <param name="writeLiteralMethodName">写入文本方法名称。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.#ctor(System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.GeneratedClassContext" /> 类的新实例。</summary>
      <param name="executeMethodName">执行方法名称。</param>
      <param name="writeMethodName">写入方法名称。</param>
      <param name="writeLiteralMethodName">写入文本方法名称。</param>
      <param name="writeToMethodName">写入到方法名称。</param>
      <param name="writeLiteralToMethodName">将文本写入到方法名称。</param>
      <param name="templateTypeName">模板类型名称。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.#ctor(System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.GeneratedClassContext" /> 类的新实例。</summary>
      <param name="executeMethodName">执行方法名称。</param>
      <param name="writeMethodName">写入方法名称。</param>
      <param name="writeLiteralMethodName">写入文本方法名称。</param>
      <param name="writeToMethodName">写入到方法名称。</param>
      <param name="writeLiteralToMethodName">将文本写入到方法名称。</param>
      <param name="templateTypeName">模板类型名称。</param>
      <param name="defineSectionMethodName">定义区域方法名称。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.#ctor(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.GeneratedClassContext" /> 类的新实例。</summary>
      <param name="executeMethodName">执行方法名称。</param>
      <param name="writeMethodName">写入方法名称。</param>
      <param name="writeLiteralMethodName">写入文本方法名称。</param>
      <param name="writeToMethodName">写入到方法名称。</param>
      <param name="writeLiteralToMethodName">将文本写入到方法名称。</param>
      <param name="templateTypeName">模板类型名称。</param>
      <param name="defineSectionMethodName">定义区域方法名称。</param>
      <param name="beginContextMethodName">开始上下文方法名称。</param>
      <param name="endContextMethodName">结束上下文方法名称。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.AllowSections">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取指示上下文是否允许区域的值。</summary>
      <returns>如果上下文允许区域，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.AllowTemplates">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取指示上下文是否允许模板的值。</summary>
      <returns>如果上下文允许模板，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.BeginContextMethodName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置所生成上下文之前的方法的名称。</summary>
      <returns>所生成上下文之前的方法的名称。</returns>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.Default">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。定义默认生成的上下文。</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultExecuteMethodName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。定义执行方法的默认名称。</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultLayoutPropertyName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。定义布局属性的默认名称。</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultWriteAttributeMethodName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。定义写入特性方法的默认名称。</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultWriteAttributeToMethodName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。定义方法的写入至特性的默认名称。</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultWriteLiteralMethodName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指定写入文本方法的默认名称。</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultWriteMethodName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指定写入方法的默认名称。</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.DefineSectionMethodName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置用于定义上下文区域的方法的名称。</summary>
      <returns>用于定义上下文区域的方法的名称。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.EndContextMethodName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置所生成上下文之后的方法的名称。</summary>
      <returns>所生成上下文之后的方法的名称。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于当前对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要比较的对象。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.ExecuteMethodName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置将在上下文中调用的方法的名称。</summary>
      <returns>将在上下文中调用的方法的名称。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此当前实例的哈希代码。</summary>
      <returns>此当前实例的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.LayoutPropertyName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置布局的属性名称。</summary>
      <returns>布局的属性名称。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.op_Equality(System.Web.Razor.Generator.GeneratedClassContext,System.Web.Razor.Generator.GeneratedClassContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定两个 <see cref="T:System.Web.Razor.Generator.GeneratedClassContext" /> 对象是否相等。</summary>
      <returns>如果两个 <see cref="T:System.Web.Razor.Generator.GeneratedClassContext" /> 对象相等，则为 true；否则为 false。</returns>
      <param name="left">要比较的第一个对象。</param>
      <param name="right">要比较的第二个对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.op_Inequality(System.Web.Razor.Generator.GeneratedClassContext,System.Web.Razor.Generator.GeneratedClassContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定两个 <see cref="T:System.Web.Razor.Generator.GeneratedClassContext" /> 对象是否不相等。</summary>
      <returns>如果两个 <see cref="T:System.Web.Razor.Generator.GeneratedClassContext" /> 对象不相等，则为 true；否则为 false。</returns>
      <param name="left">要比较的第一个对象。</param>
      <param name="right">要比较的第二个对象。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.ResolveUrlMethodName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置用于解析上下文 URL 的方法的名称。</summary>
      <returns>用于解析上下文 URL 的方法的名称。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.SupportsInstrumentation">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取指示生成的类是否支持检测的值。</summary>
      <returns>如果生成的类支持检测，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.TemplateTypeName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置模板的类型名称。</summary>
      <returns>模板的类型名称。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteAttributeMethodName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置用于写入特性的方法的名称。</summary>
      <returns>用于写入特性的方法的名称。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteAttributeToMethodName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置将特性写入到的方法的名称。</summary>
      <returns>将特性写入到的方法的名称。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteLiteralMethodName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置将上下文文本写入到的方法的名称。</summary>
      <returns>将上下文文本写入到的方法的名称。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteLiteralToMethodName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置将上下文文本写入到的方法的名称。</summary>
      <returns>将上下文文本写入到的方法的名称。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteMethodName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置将在上下文中执行写入的方法的名称。</summary>
      <returns>将在上下文中执行写入的方法的名称。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteToMethodName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置将在上下文中执行写入的方法的名称。</summary>
      <returns>将在上下文中执行写入的方法的名称。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.GeneratedCodeMapping">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示生成的代码映射对象。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.GeneratedCodeMapping" /> 类的新实例。</summary>
      <param name="startLine">起始行。</param>
      <param name="startColumn">起始列。</param>
      <param name="startGeneratedColumn">起始生成列。</param>
      <param name="codeLength">代码长度。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.GeneratedCodeMapping" /> 类的新实例。</summary>
      <param name="startOffset">初始偏移量。</param>
      <param name="startLine">起始行。</param>
      <param name="startColumn">起始列。</param>
      <param name="startGeneratedColumn">起始生成列。</param>
      <param name="codeLength">代码长度。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedCodeMapping.CodeLength">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置所生成映射代码的长度。</summary>
      <returns>所生成映射代码的长度。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于当前生成的代码映射对象。</summary>
      <returns>如果指定对象等于当前生成的代码映射对象，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回所生成代码映射对象的哈希代码。</summary>
      <returns>所生成代码映射对象的哈希代码。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.op_Equality(System.Web.Razor.Generator.GeneratedCodeMapping,System.Web.Razor.Generator.GeneratedCodeMapping)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定生成的两个指定代码映射对象是否具有相同值。</summary>
      <returns>如果两个指定的生成代码映射对象具有相同值，则为 true；否则为 false。</returns>
      <param name="left">左侧的生成代码映射对象。</param>
      <param name="right">右侧的生成代码映射对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.op_Inequality(System.Web.Razor.Generator.GeneratedCodeMapping,System.Web.Razor.Generator.GeneratedCodeMapping)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定两个指定的生成代码映射对象是否具有不同的值。</summary>
      <returns>如果两个指定的生成代码映射对象具有不同的值，则为 true；否则为 false。</returns>
      <param name="right">右侧的生成代码映射对象。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedCodeMapping.StartColumn">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置所生成代码映射的起始列。</summary>
      <returns>所生成代码映射的起始列。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedCodeMapping.StartGeneratedColumn">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置生成的源文件中某一生成代码映射的起始列。</summary>
      <returns>生成的源文件中某一生成代码映射的起始列。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedCodeMapping.StartLine">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取所生成代码映射的起始行。</summary>
      <returns>所生成代码映射的起始行。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedCodeMapping.StartOffset">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置所生成代码映射的初始偏移量。</summary>
      <returns>所生成代码映射的初始偏移量。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.ToString">
      <summary>返回表示当前对象的字符串。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.HelperCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示帮助器代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.#ctor(System.Web.Razor.Text.LocationTagged{System.String},System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.HelperCodeGenerator" /> 类的新实例。</summary>
      <param name="signature">签名。</param>
      <param name="headerComplete">如果要完成标题，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于当前对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要比较的对象。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.HelperCodeGenerator.Footer">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置此代码的脚注。</summary>
      <returns>此代码的脚注。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在代码之后生成块。</summary>
      <param name="target">要生成的块。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在代码之前生成块。</summary>
      <param name="target">要生成的块。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取当前实例的哈希代码。</summary>
      <returns>当前实例的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.HelperCodeGenerator.HeaderComplete">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置指示此代码的标题是否已完成的值。</summary>
      <returns>如果此代码的标题已完成，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.HelperCodeGenerator.Signature">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置此代码的签名。</summary>
      <returns>此代码的签名。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回当前实例的字符串表示形式。</summary>
      <returns>当前实例的字符串表示形式。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.HybridCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示混合代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.HybridCodeGenerator.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.HybridCodeGenerator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.HybridCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。从由参数标识的开关生成数据模型的代码。</summary>
      <param name="target">目标对象。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.HybridCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成结束块代码。</summary>
      <param name="target">目标对象。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.HybridCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成开始块代码。</summary>
      <param name="target">目标对象。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="T:System.Web.Razor.Generator.IBlockCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示网页的 <see cref="T:System.Web.Razor.Generator.IBlockCodeGenerator" />。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.IBlockCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。针对 Razor 生成结束块代码。</summary>
      <param name="target">目标块。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.IBlockCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。针对 Razor 生成开始块代码。</summary>
      <param name="target">目标块。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="T:System.Web.Razor.Generator.ISpanCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示代码生成器的阶段。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ISpanCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用指定的目标和上下文生成数据模型的代码。</summary>
      <param name="target">目标对象。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="T:System.Web.Razor.Generator.LiteralAttributeCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示文本特性的代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.#ctor(System.Web.Razor.Text.LocationTagged{System.String},System.Web.Razor.Text.LocationTagged{System.String})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。.初始化 <see cref="T:System.Web.Razor.Generator.LiteralAttributeCodeGenerator" /> 类的新实例。</summary>
      <param name="prefix">文本特性的前缀。</param>
      <param name="value">文本特性的值。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.#ctor(System.Web.Razor.Text.LocationTagged{System.String},System.Web.Razor.Text.LocationTagged{System.Web.Razor.Generator.SpanCodeGenerator})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。.初始化 <see cref="T:System.Web.Razor.Generator.LiteralAttributeCodeGenerator" /> 类的新实例。</summary>
      <param name="prefix">文本特性的前缀。</param>
      <param name="valueGenerator">文本特性的值生成器。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于此实例。</summary>
      <returns>如果指定对象等于此实例，则为 true；否则为 false。</returns>
      <param name="obj">要与此实例比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成文本特性的代码。</summary>
      <param name="target">其内容表示文本特性的源跨度。</param>
      <param name="context">代码生成器的上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取当前实例的哈希代码。</summary>
      <returns>当前实例的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.Prefix">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置文本特性的前缀。</summary>
      <returns>文本特性的前缀。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的字符串表示形式。</summary>
      <returns>此实例的字符串表示形式。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.Value">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置文本特性的值。</summary>
      <returns>文本特性的值。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.ValueGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置文本特性的值生成器。</summary>
      <returns>文本特性的值生成器。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.MarkupCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示标记的代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.MarkupCodeGenerator.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.MarkupCodeGenerator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.MarkupCodeGenerator.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于此实例。</summary>
      <returns>如果指定对象等于此实例，则为 true；否则为 false。</returns>
      <param name="obj">要与此实例比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.MarkupCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成标记的代码。</summary>
      <param name="target">其内容表示标记的源跨度。</param>
      <param name="context">代码生成器的上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.MarkupCodeGenerator.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的哈希代码。</summary>
      <returns>此实例的哈希代码。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.MarkupCodeGenerator.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的字符串表示形式。</summary>
      <returns>此实例的字符串表示形式。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.RazorCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Razor 代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.#ctor(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.RazorCodeGenerator" /> 类的新实例。</summary>
      <param name="className">类名。</param>
      <param name="rootNamespaceName">根命名空间名称。</param>
      <param name="sourceFileName">源文件名称。</param>
      <param name="host">主机。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.ClassName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置此代码的类名。</summary>
      <returns>此代码的类名。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.Context">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取此代码生成器的上下文。</summary>
      <returns>此代码生成器的上下文。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.DesignTimeMode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置指示代码生成器是否处于设计时模式的值。</summary>
      <returns>如果代码生成器处于设计时模式，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.GenerateLinePragmas">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置指示生成器是否应在 Razor 代码中生成行杂注的值。</summary>
      <returns>如果生成器应在 Razor 代码中生成行杂注，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.Host">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置 Razor 引擎主机。</summary>
      <returns>Razor 引擎主机。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.Initialize(System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化当前 <see cref="T:System.Web.Razor.Generator.RazorCodeGenerator" /> 实例。</summary>
      <param name="context">上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.OnComplete">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。引发 Complete 事件。</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.RootNamespaceName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置根命名空间的名称。</summary>
      <returns>根命名空间的名称。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.SourceFileName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置源文件的名称。</summary>
      <returns>源文件名。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.VisitEndBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。访问结束块。</summary>
      <param name="block">要访问的块。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.VisitSpan(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。访问跨度。</summary>
      <param name="span">要访问的跨度。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.VisitStartBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。访问开始块。</summary>
      <param name="block">要访问的块。</param>
    </member>
    <member name="T:System.Web.Razor.Generator.RazorCommentCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示网页的 Razor 注释代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCommentCodeGenerator.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.RazorCommentCodeGenerator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCommentCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用指定参数生成开始块代码。</summary>
      <param name="target">目标块。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="T:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Razor 指令特性的代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.#ctor(System.String,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator" /> 类的新实例。</summary>
      <param name="name">指令特性的名称。</param>
      <param name="value">指令特性的值。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于此实例。</summary>
      <returns>如果指定对象等于此实例，则为 true；否则为 false。</returns>
      <param name="obj">要与此实例比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成指令特性的代码。</summary>
      <param name="target">其内容表示要生成的指令特性的源跨度。</param>
      <param name="context">代码生成器的上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的哈希代码。</summary>
      <returns>此实例的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.Name">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置指令特性的名称。</summary>
      <returns>指令特性的名称。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的字符串表示形式。</summary>
      <returns>此实例的字符串表示形式。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.Value">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置指令特性的值。</summary>
      <returns>指令特性的值。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.ResolveUrlCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示解析 URL 代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ResolveUrlCodeGenerator.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.ResolveUrlCodeGenerator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ResolveUrlCodeGenerator.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示该实例与指定对象是否相等。</summary>
      <returns>如果 <paramref name="obj" /> 与该实例属于相同类型且表示相同值，则为 true；否则为 false。</returns>
      <param name="obj">要与当前实例比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.ResolveUrlCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。为 URL 生成代码。</summary>
      <param name="target">目标对象。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.ResolveUrlCodeGenerator.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的哈希代码。</summary>
      <returns>此实例的哈希代码。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.ResolveUrlCodeGenerator.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的完全限定类型名称。</summary>
      <returns>完全限定类型名称。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.SectionCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示区域代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.#ctor(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.SectionCodeGenerator" /> 类的新实例。</summary>
      <param name="sectionName">区域代码的名称。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于当前对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在区域代码之后生成块。</summary>
      <param name="target">要生成的目标。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在区域代码之前生成块。</summary>
      <param name="target">要生成的目标。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。检索此当前实例的哈希代码。</summary>
      <returns>此当前实例的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.SectionCodeGenerator.SectionName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置区域的名称。</summary>
      <returns>区域的名称。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此当前实例的字符串表示形式。</summary>
      <returns>此当前实例的字符串表示形式。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.SetBaseTypeCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示集合基类型的代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.#ctor(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.SetBaseTypeCodeGenerator" /> 类的新实例。</summary>
      <param name="baseType">集合基类型。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.BaseType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置集合基类型。</summary>
      <returns>集合基类型。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于此实例。</summary>
      <returns>如果指定对象等于此实例，则为 true；否则为 false。</returns>
      <param name="obj">要与此实例比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成此集合基类型的代码。</summary>
      <param name="target">包含要生成代码的集合基类型的源跨度。</param>
      <param name="context">代码生成器的上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取此当前实例的哈希代码。</summary>
      <returns>此当前实例的哈希代码。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.ResolveType(System.Web.Razor.Generator.CodeGeneratorContext,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。解析给定的集合基类型。</summary>
      <returns>已解析的集合基类型。</returns>
      <param name="context">代码生成器的上下文。</param>
      <param name="baseType">要解析的集合基类型。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的字符串表示形式。</summary>
      <returns>此实例的字符串表示形式。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.SetLayoutCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示用于设置 Web Razor 布局的代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetLayoutCodeGenerator.#ctor(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.SetLayoutCodeGenerator" /> 类的新实例。</summary>
      <param name="layoutPath">布局路径。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SetLayoutCodeGenerator.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于当前对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SetLayoutCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成布局代码。</summary>
      <param name="target">要生成代码的目标。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SetLayoutCodeGenerator.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。检索此当前实例的哈希代码。</summary>
      <returns>此当前实例的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.SetLayoutCodeGenerator.LayoutPath">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置布局代码的路径。</summary>
      <returns>布局代码的路径。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.SetLayoutCodeGenerator.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此当前实例的字符串表示形式。</summary>
      <returns>此当前实例的字符串表示形式。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示值的 SetVBOptionCodeGenerator 转换。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetVBOptionCodeGenerator.#ctor(System.String,System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 类的新实例。</summary>
      <param name="optionName">选项名称。</param>
      <param name="value">如果对象具有值，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SetVBOptionCodeGenerator.Explicit(System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将 <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 显式转换为开/关值。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 显式转换为开/关值。</returns>
      <param name="onOffValue">如果 <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 转换为开/关值，则为 true；否则为 false。</param>
    </member>
    <member name="F:System.Web.Razor.Generator.SetVBOptionCodeGenerator.ExplicitCodeDomOptionName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示显式代码 Dom 选项名称。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetVBOptionCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。为指定参数生成代码。</summary>
      <param name="target">目标。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="P:System.Web.Razor.Generator.SetVBOptionCodeGenerator.OptionName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取代码生成器的选项名称。</summary>
      <returns>代码生成器的选项名称。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.SetVBOptionCodeGenerator.Strict(System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将 <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 严格转换为开/关值。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 显式转换为开/关值。</returns>
      <param name="onOffValue">如果 <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 严格转换为开/关值，则为 true；否则为 false。</param>
    </member>
    <member name="F:System.Web.Razor.Generator.SetVBOptionCodeGenerator.StrictCodeDomOptionName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示严格代码 Dom 选项名称。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetVBOptionCodeGenerator.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回表示当前对象的字符串。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="P:System.Web.Razor.Generator.SetVBOptionCodeGenerator.Value">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取一个值，该值指示 <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 是否具有值。</summary>
      <returns>如果 <see cref="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator" /> 具有值，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.SpanCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Razor 的跨度代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SpanCodeGenerator.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.SpanCodeGenerator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SpanCodeGenerator.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于当前对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SpanCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。为指定目标和上下文参数生成代码。</summary>
      <param name="target">目标跨度。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.SpanCodeGenerator.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回跨度代码生成器的哈希代码。</summary>
      <returns>跨度代码生成器的哈希代码。</returns>
    </member>
    <member name="F:System.Web.Razor.Generator.SpanCodeGenerator.Null">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示跨度代码生成器的 null 值。</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.StatementCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示语句的代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.StatementCodeGenerator.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.StatementCodeGenerator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.StatementCodeGenerator.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于此实例。</summary>
      <returns>如果指定对象等于此实例，则为 true；否则为 false。</returns>
      <param name="obj">要与此实例比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.StatementCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成语句的代码。</summary>
      <param name="target">其内容包含要生成的语句的跨度源。</param>
      <param name="context">代码生成器的上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.StatementCodeGenerator.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此当前实例的哈希代码。</summary>
      <returns>此当前实例的哈希代码。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.StatementCodeGenerator.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的字符串表示形式。</summary>
      <returns>此实例的字符串表示形式。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.TemplateBlockCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Razor 的模板块代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.TemplateBlockCodeGenerator.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.TemplateBlockCodeGenerator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.TemplateBlockCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成用于结束模板块代码生成器的块的代码。</summary>
      <param name="target">目标块。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.TemplateBlockCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成用于开始模板块代码生成器的块的代码。</summary>
      <param name="target">目标块。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="T:System.Web.Razor.Generator.TypeMemberCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示类型成员代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.TypeMemberCodeGenerator.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.TypeMemberCodeGenerator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.TypeMemberCodeGenerator.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于当前对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.TypeMemberCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用给定目标和上下文生成代码。</summary>
      <param name="target">要生成代码的目标。</param>
      <param name="context">代码生成器上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Generator.TypeMemberCodeGenerator.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。检索此当前实例的哈希代码。</summary>
      <returns>此当前实例的哈希代码。</returns>
    </member>
    <member name="M:System.Web.Razor.Generator.TypeMemberCodeGenerator.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此代码的字符串表示形式。</summary>
      <returns>此代码的字符串表示形式。</returns>
    </member>
    <member name="T:System.Web.Razor.Generator.VBRazorCodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 VB 的 Razor 代码生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.VBRazorCodeGenerator.#ctor(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Generator.VBRazorCodeGenerator" /> 类的新实例。</summary>
      <param name="className">类的名称。</param>
      <param name="rootNamespaceName">根命名空间。</param>
      <param name="sourceFileName">资产源的文件名。</param>
      <param name="host">主机。</param>
    </member>
    <member name="T:System.Web.Razor.Parser.BalancingModes">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.BalancingModes.AllowCommentsAndTemplates">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.BalancingModes.AllowEmbeddedTransitions">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.BalancingModes.BacktrackOnFailure">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.BalancingModes.NoErrorOnFailure">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.BalancingModes.None">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.CallbackVisitor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示在访问完成时执行回调的访问者。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.#ctor(System.Action{System.Web.Razor.Parser.SyntaxTree.Span})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.CallbackVisitor" /> 类的新实例。</summary>
      <param name="spanCallback">跨度访问的委托。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.#ctor(System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.CallbackVisitor" /> 类的新实例。</summary>
      <param name="spanCallback">跨度访问的委托。</param>
      <param name="errorCallback">错误访问的委托。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.#ctor(System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError},System.Action{System.Web.Razor.Parser.SyntaxTree.BlockType},System.Action{System.Web.Razor.Parser.SyntaxTree.BlockType})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.CallbackVisitor" /> 类的新实例。</summary>
      <param name="spanCallback">跨度访问的委托。</param>
      <param name="errorCallback">错误访问的委托。</param>
      <param name="startBlockCallback">开始块访问的委托。</param>
      <param name="endBlockCallback">结束块访问的委托。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.#ctor(System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError},System.Action{System.Web.Razor.Parser.SyntaxTree.BlockType},System.Action{System.Web.Razor.Parser.SyntaxTree.BlockType},System.Action)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.CallbackVisitor" /> 类的新实例。</summary>
      <param name="spanCallback">跨度访问的委托。</param>
      <param name="errorCallback">错误访问的委托。</param>
      <param name="startBlockCallback">开始块访问的委托。</param>
      <param name="endBlockCallback">结束块访问的委托。</param>
      <param name="completeCallback">要为完成事件执行的委托。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.OnComplete">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.CallbackVisitor.SynchronizationContext">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置此回调访问者的同步上下文。</summary>
      <returns>此回调访问者的同步上下文。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.VisitEndBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。执行要访问结束块的访问者回调。</summary>
      <param name="block">要访问的结束块。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.VisitError(System.Web.Razor.Parser.SyntaxTree.RazorError)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。执行要访问错误的访问者回调。</summary>
      <param name="err">要访问的 Razor 错误。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.VisitSpan(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。执行要访问跨度的访问者回调。</summary>
      <param name="span">要访问的跨度。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.VisitStartBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。执行要访问开始块的访问者回调。</summary>
      <param name="block">要访问的开始块。</param>
    </member>
    <member name="T:System.Web.Razor.Parser.CSharpCodeParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 C sharp 代码分析器。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.CSharpCodeParser" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.AcceptIf(System.Web.Razor.Tokenizer.Symbols.CSharpKeyword)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定分析器是否接受“IF”关键字。</summary>
      <returns>如果分析器接受“IF”关键字，则为 true；否则为 false。</returns>
      <param name="keyword">要接受的关键字。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.AssertDirective(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。断言指令代码。</summary>
      <param name="directive">要断言的指令代码。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.At(System.Web.Razor.Tokenizer.Symbols.CSharpKeyword)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定代码是否包含“AT”关键字。</summary>
      <returns>如果代码包含“AT”关键字，则为 true；否则为 false。</returns>
      <param name="keyword">关键字。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.BaseTypeDirective(System.String,System.Func{System.String,System.Web.Razor.Generator.SpanCodeGenerator})">
      <summary>指示基类型指令。</summary>
      <param name="noTypeNameError">无类型名称错误。</param>
      <param name="createCodeGenerator">创建代码生成器。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.FunctionsDirective">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示函数指令。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.HandleEmbeddedTransition">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示处理嵌入过渡的代码。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.HelperDirective">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示帮助器指令。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.InheritsDirective">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示应用程序将从中派生视图从而可确保正确类型检查的类。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.InheritsDirectiveCore">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。继承指令核心。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.IsAtEmbeddedTransition(System.Boolean,System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定代码是否处于嵌入的过渡之中。</summary>
      <returns>如果代码在嵌入的过渡中，则为 true；否则为 false。</returns>
      <param name="allowTemplatesAndComments">如果允许模板和注释，则为 true；否则为 false。</param>
      <param name="allowTransitions">如果允许过渡，则为 true；否则为 false。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.IsNested">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置指示是否嵌套代码的值。</summary>
      <returns>如果嵌套代码，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.IsSpacingToken(System.Boolean,System.Boolean)">
      <summary>指示行和注释是否为间距令牌。</summary>
      <returns>指示间距令牌的函数。</returns>
      <param name="includeNewLines">如果要包括新行，则为 true；否则为 false。</param>
      <param name="includeComments">如果要包括注释，则为 true；否则为 false。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.Keywords">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置 C sharp 语言关键字。</summary>
      <returns>C sharp 语言关键字。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.Language">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取用于分析的特定语言。</summary>
      <returns>用于分析的特定语言。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.LayoutDirective">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示布局指令。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.MapDirectives(System.Action,System.String[])">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。映射给定的指令。</summary>
      <param name="handler">处理程序。</param>
      <param name="directives">指令。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.OtherParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取用于代码的其他分析器。</summary>
      <returns>用于代码的其他分析器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.OutputSpanBeforeRazorComment">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在注释之前跨越分析的输出。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.ParseBlock">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。阻止分析。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.ReservedDirective(System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示保留的指令。</summary>
      <param name="topLevel">确定指令是否为顶级。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.SectionDirective">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示区域指令。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.SessionStateDirective">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示会话状态指令。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.SessionStateDirectiveCore">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示会话状态指令核心。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.SessionStateTypeDirective(System.String,System.Func{System.String,System.String,System.Web.Razor.Generator.SpanCodeGenerator})">
      <summary>指示会话状态类型的指令。</summary>
      <param name="noValueError">无值错误。</param>
      <param name="createCodeGenerator">创建代码生成器。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.TryGetDirectiveHandler(System.String,System.Action@)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取指令处理程序。</summary>
      <returns>如果成功，则为 true；否则为 false。</returns>
      <param name="directive">指令。</param>
      <param name="handler">处理程序。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.ValidSessionStateValue">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定会话状态的值是否有效。</summary>
      <returns>如果会话状态的值有效，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.CSharpCodeParser.Block">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示此 CSharpCode 分析器的块。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.Block.#ctor(System.String,System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.CSharpCodeParser.Block" /> 类的新实例。</summary>
      <param name="name">字符串名称。</param>
      <param name="start">源位置的开始。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.Block.#ctor(System.Web.Razor.Tokenizer.Symbols.CSharpSymbol)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.CSharpCodeParser.Block" /> 类的新实例。</summary>
      <param name="symbol">CSharp 符号。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.Block.Name">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置块的字符串名称。</summary>
      <returns>块的字符串名称。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.Block.Start">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置开始块的源位置。</summary>
      <returns>开始块的源位置。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.CSharpLanguageCharacteristics">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 CSharp 语言中的不同语言特征。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.CreateMarkerSymbol(System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建代码中的标记符号。</summary>
      <returns>代码中的标记符号。</returns>
      <param name="location">源位置。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建代码中的符号。</summary>
      <returns>代码中的符号。</returns>
      <param name="location">源位置。</param>
      <param name="content">内容值。</param>
      <param name="type">html 符号类型。</param>
      <param name="errors">错误列表。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.CreateTokenizer(System.Web.Razor.Text.ITextDocument)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建语言标记器。</summary>
      <returns>语言标记器。</returns>
      <param name="source">文本文档的源。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.FlipBracket(System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。翻转代码中的括号。</summary>
      <returns>代码中的括号。</returns>
      <param name="bracket">括号。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.GetKeyword(System.Web.Razor.Tokenizer.Symbols.CSharpKeyword)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取代码中的关键字。</summary>
      <returns>代码中的关键字。</returns>
      <param name="keyword">关键字。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.GetKnownSymbolType(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取代码中的 <see cref="T:System.Web.Razor.Parser.CSharpLanguageCharacteristics" />。</summary>
      <returns>代码中的 <see cref="T:System.Web.Razor.Parser.CSharpLanguageCharacteristics" />。</returns>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType" />。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.GetSample(System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取代码中的示例符号。</summary>
      <returns>代码中的示例符号。</returns>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType" />。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.GetSymbolSample(System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取代码中的示例符号。</summary>
      <returns>代码中的示例符号。</returns>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType" />。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpLanguageCharacteristics.Instance">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 <see cref="T:System.Web.Razor.Parser.CSharpLanguageCharacteristics" /> 类的实例。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.CSharpLanguageCharacteristics" /> 类的实例。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.HtmlLanguageCharacteristics">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 html 中的不同语言特征。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.CreateMarkerSymbol(System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建 Html 中的标记符号。</summary>
      <returns>Html 中的标记符号。</returns>
      <param name="location">源位置。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建 Html 中的符号。</summary>
      <returns>Html 中的符号。</returns>
      <param name="location">源位置。</param>
      <param name="content">内容值。</param>
      <param name="type">html 符号类型。</param>
      <param name="errors">错误列表。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.CreateTokenizer(System.Web.Razor.Text.ITextDocument)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建 html 标记器。</summary>
      <returns>html 标记器。</returns>
      <param name="source">文本文档的源。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.FlipBracket(System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。翻转 html 中的括号。</summary>
      <returns>html 中的括号。</returns>
      <param name="bracket">括号。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.GetKnownSymbolType(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 html 中的 <see cref="T:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType" />。</summary>
      <returns>html 中的 <see cref="T:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType" />。</returns>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType" />。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.GetSample(System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 html 中的示例符号。</summary>
      <returns>html 中的示例符号。</returns>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType" />。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.HtmlLanguageCharacteristics.Instance">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 <see cref="T:System.Web.Razor.Parser.HtmlLanguageCharacteristics" /> 类的实例。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.HtmlLanguageCharacteristics" /> 类的实例。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.HtmlMarkupParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示专用于分析 HTML 标记的分析器。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.HtmlMarkupParser" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.BuildSpan(System.Web.Razor.Parser.SyntaxTree.SpanBuilder,System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用指定的跨度生成器生成给定内容的跨度。</summary>
      <param name="span">用于生成跨度的跨度生成器。</param>
      <param name="start">开始位置。</param>
      <param name="content">跨度内容。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.IsSpacingToken(System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回用于确定 HTML 间距使用的令牌的函数委托。</summary>
      <returns>用于确定 HTML 间距使用的令牌的函数委托。</returns>
      <param name="includeNewLines">如果指示将新行视为间距令牌，则为 true；否则为 false。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.HtmlMarkupParser.Language">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取定义 HTML 语言特征的实例。</summary>
      <returns>定义 HTML 语言特征的实例。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.HtmlMarkupParser.OtherParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取用于分析 HTML 标记的其他分析器。</summary>
      <returns>用于分析 HTML 标记的其他分析器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.OutputSpanBeforeRazorComment">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在 Razor 注释之前生成跨度。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.ParseBlock">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析下一个 HTML 块。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.ParseDocument">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析 HTML 文档。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.ParseSection(System.Tuple{System.String,System.String},System.Boolean)">
      <summary>分析具有嵌套序列所指定标记的区域。</summary>
      <param name="nestingSequences">指定标记嵌套序列的元组。</param>
      <param name="caseSensitive">如果指示区分大小写分析，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.SkipToAndParseCode(System.Func{System.Web.Razor.Tokenizer.Symbols.HtmlSymbol,System.Boolean})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在满足指定条件之前跳过分析。</summary>
      <param name="condition">定义条件的函数委托。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.SkipToAndParseCode(System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在遇到指定 HTML 符号类型之前跳过分析。</summary>
      <param name="type">HTML 符号类型。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.HtmlMarkupParser.VoidElements">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取被视为 void 的 HTML 标记。</summary>
      <returns>被视为 void 的 HTML 标记。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.LanguageCharacteristics`3">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。提供用于定义 Razor 代码语言行为的方法。</summary>
      <typeparam name="TTokenizer">Razor 语言的代码标记器的类型。</typeparam>
      <typeparam name="TSymbol">语言符号的类型。</typeparam>
      <typeparam name="TSymbolType">语言符号的枚举类型。</typeparam>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.LanguageCharacteristics`3" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.CreateMarkerSymbol(System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。以指定的源位置作为起始标记创建代码语言符号。</summary>
      <returns>代码语言的符号。</returns>
      <param name="location">作为起始标记的源位置。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,`2,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。以指定的源位置作为起始标记创建代码语言符号。</summary>
      <returns>代码语言的符号。</returns>
      <param name="location">作为起始标记的源位置。</param>
      <param name="content">内容。</param>
      <param name="type">语言符号的枚举类型。</param>
      <param name="errors">错误的集合。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.CreateTokenizer(System.Web.Razor.Text.ITextDocument)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。为指定的源文档创建 Razor 代码语言标记器。</summary>
      <returns>指定源文档的 Razor 代码语言标记器。</returns>
      <param name="source">源文档。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.FlipBracket(`2)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回指定括号的配对括号。</summary>
      <returns>指定括号的配对括号。</returns>
      <param name="bracket">要翻转的括号。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.GetKnownSymbolType(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取给定符号类型的特定语言符号类型。</summary>
      <returns>给定符号类型的特定语言符号类型。</returns>
      <param name="type">要获取的符号类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.GetSample(`2)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取给定语言符号类型的实际符号。</summary>
      <returns>给定语言符号类型的实际符号。</returns>
      <param name="type">要获取的语言符号类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsCommentBody(`1)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定符号是否为注释正文类型。</summary>
      <returns>如果符号为注释正文类型，则为 true；否则为 false。</returns>
      <param name="symbol">要检查的符号。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsCommentStar(`1)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定符号是否为注释星形类型。</summary>
      <returns>如果符号为注释星形类型，则为 true；否则为 false。</returns>
      <param name="symbol">要检查的符号。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsCommentStart(`1)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定符号是否为注释起始类型。</summary>
      <returns>如果符号为注释起始类型，则为 true；否则为 false。</returns>
      <param name="symbol">要检查的符号。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsIdentifier(`1)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定符号是否为标识符类型。</summary>
      <returns>如果符号为标识符类型，则为 true；否则为 false。</returns>
      <param name="symbol">要检查的符号。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsKeyword(`1)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定符号是否为关键字类型。</summary>
      <returns>如果符号为关键字类型，则为 true；否则为 false。</returns>
      <param name="symbol">要检查的符号。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsKnownSymbolType(`1,System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定符号类型是否为已知符号类型。</summary>
      <returns>如果符号类型为已知符号类型，则为 true；否则为 false。</returns>
      <param name="symbol">要检查其类型的符号。</param>
      <param name="type">符号的已知类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsNewLine(`1)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定符号是否为换行符类型。</summary>
      <returns>如果符号为换行符类型，则为 true；否则为 false。</returns>
      <param name="symbol">要检查的符号。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsTransition(`1)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定符号是否为过渡类型。</summary>
      <returns>如果符号为过渡类型，则为 true；否则为 false。</returns>
      <param name="symbol">要检查的符号。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsUnknown(`1)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定符号是否为未知类型。</summary>
      <returns>如果符号为未知类型，则为 true；否则为 false。</returns>
      <param name="symbol">要检查的符号。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsWhiteSpace(`1)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定符号是否为空格类型。</summary>
      <returns>如果符号为空格类型，则为 true；否则为 false。</returns>
      <param name="symbol">要检查的符号。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.KnowsSymbolType(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定符号是否为未知类型。</summary>
      <returns>如果符号为未知类型，则为 true；否则为 false。</returns>
      <param name="type">符号的已知类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.SplitSymbol(`1,System.Int32,`2)">
      <summary>在指定的索引处拆分代码语言符号的内容。</summary>
      <returns>代码语言符号元组。</returns>
      <param name="symbol">要拆分其内容的符号。</param>
      <param name="splitAt">拆分位置所在的索引。</param>
      <param name="leftType">语言符号的枚举类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.TokenizeString(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将指定的字符串拆分为令牌。</summary>
      <returns>令牌的集合。</returns>
      <param name="content">要标记的字符串。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.TokenizeString(System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将指定的字符串拆分为令牌。</summary>
      <returns>令牌的集合。</returns>
      <param name="start">作为标记器起始标记的源位置。</param>
      <param name="input">要标记的字符串。</param>
    </member>
    <member name="T:System.Web.Razor.Parser.ParserBase">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Razor 的分析器基类。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserBase.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.ParserBase" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserBase.BuildSpan(System.Web.Razor.Parser.SyntaxTree.SpanBuilder,System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。为分析器库生成跨度。</summary>
      <param name="span">跨度生成器。</param>
      <param name="start">源位置的开始。</param>
      <param name="content">内容。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserBase.Context">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置 <see cref="T:System.Web.Razor.Parser.ParserContext" />。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.ParserContext" />。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserBase.IsMarkupParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取一个值，该值指示分析器是否为标记分析器。</summary>
      <returns>如果分析器为标记分析器，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserBase.OtherParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取其他分析器 <see cref="T:System.Web.Razor.Parser.ParserBase" />。</summary>
      <returns>其他分析器 <see cref="T:System.Web.Razor.Parser.ParserBase" />。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserBase.ParseBlock">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。阻止分析器。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserBase.ParseDocument">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建文档进行分析。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserBase.ParseSection(System.Tuple{System.String,System.String},System.Boolean)">
      <summary>分析元素排序列表中的区域。</summary>
      <param name="nestingSequences">嵌套序列对。</param>
      <param name="caseSensitive">如果区分大小写，则为 true；否则为 false。</param>
    </member>
    <member name="T:System.Web.Razor.Parser.ParserContext">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示其上下文可以切换到代码或标记的分析器。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.#ctor(System.Web.Razor.Text.ITextDocument,System.Web.Razor.Parser.ParserBase,System.Web.Razor.Parser.ParserBase,System.Web.Razor.Parser.ParserBase)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.ParserContext" /> 类的新实例。</summary>
      <param name="source">源文档。</param>
      <param name="codeParser">上下文的代码分析器。</param>
      <param name="markupParser">上下文的标记分析器。</param>
      <param name="activeParser">上下文的活动分析器。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.ActiveParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置上下文的活动分析器。</summary>
      <returns>上下文的活动分析器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.AddSpan(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在块生成器堆栈的末尾添加指定的跨度。</summary>
      <param name="span">要添加的跨度。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.CodeParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置上下文的代码分析器。</summary>
      <returns>上下文的代码分析器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.CompleteParse">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析最后一个跨度并返回包含新生成块的分析结果。</summary>
      <returns>包含新生成块的分析结果。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.CurrentBlock">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取当前块生成器。</summary>
      <returns>当前的块生成器。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.CurrentCharacter">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取源的当前字符。</summary>
      <returns>源的当前字符。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.DesignTimeMode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置用于指示分析器是否处于设计模式的值。</summary>
      <returns>如果分析器处于设计模式，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.EndBlock">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。从块生成器堆栈的最后一项创建结束块。</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.EndOfFile">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取用于指示源状态是否是文件末尾的值。</summary>
      <returns>如果源状态是文件末尾，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.Errors">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置在分析期间所发生错误的列表。</summary>
      <returns>错误列表。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.IsWithin(System.Web.Razor.Parser.SyntaxTree.BlockType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的块类型是否存在于块生成器列表中。</summary>
      <returns>如果指定的块类型存在于块生成器列表中，则为 true；否则为 false。</returns>
      <param name="type">要检查的块类型。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.LastAcceptedCharacters">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取最后接受的字符。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters" /> 枚举的值之一。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.LastSpan">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置最后一个跨度。</summary>
      <returns>最后一个跨度。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.MarkupParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置上下文的标记分析器。</summary>
      <returns>上下文的标记分析器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.OnError(System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在分析遇到错误时发生。</summary>
      <param name="location">源位置。</param>
      <param name="message">错误消息。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.OnError(System.Web.Razor.Text.SourceLocation,System.String,System.Object[])">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在分析遇到错误时发生。</summary>
      <param name="location">源位置。</param>
      <param name="message">错误消息。</param>
      <param name="args">有关源位置的其他信息。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.Source">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置源文档的文本读取器。</summary>
      <returns>源文档的文本读取器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.StartBlock">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在块生成器堆栈的末尾添加新块生成器并返回用于返回结束块的可释放操作。</summary>
      <returns>返回结束块的可释放操作。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.StartBlock(System.Web.Razor.Parser.SyntaxTree.BlockType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在块生成器堆栈的末尾添加新块生成器并返回用于返回结束块的可释放操作。</summary>
      <returns>返回结束块的可释放操作。</returns>
      <param name="blockType">新块生成器的类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.SwitchActiveParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。或者切换代码分析器或标记分析器作为活动分析器。</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.WhiteSpaceIsSignificantToAncestorBlock">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置用于指示空格是否对上级块很重要的值。</summary>
      <returns>如果空格对上级块很重要，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.ParserHelpers">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。为分析器提供帮助器方法。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsCombining(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的字符值是间距组合标记或非间距标记。</summary>
      <returns>如果指定的字符值是间距组合标记或非间距标记，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsConnecting(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的字符值是否为连接符标点。</summary>
      <returns>如果指定的字符值为连接符标点，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsDecimalDigit(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的字符值是否为十进制数。</summary>
      <returns>如果指定的字符值为十进制数，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsEmailPart(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的字符值用在电子邮件地址中是否有效。</summary>
      <returns>如果指定的字符值用在电子邮件地址中有效，则为 true；否则为 false。</returns>
      <param name="character">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsFormatting(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的字符值是否用于格式化文本布局或格式化文本操作。</summary>
      <returns>如果指定的字符值用于格式化文本布局或格式化文本操作，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsHexDigit(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的字符值是否为十六进制数。</summary>
      <returns>如果指定的字符为十六进制数，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsIdentifier(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的字符串值是否为标识符。</summary>
      <returns>如果指定的字符串值为标识符，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsIdentifier(System.String,System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的字符串值是否为标识符。</summary>
      <returns>如果指定的字符串值为标识符，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
      <param name="requireIdentifierStart">如果要求标识符以字母或下划线 (_) 开头，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsIdentifierPart(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的字符值用在标识符中是否有效。</summary>
      <returns>如果指定的字符用在标识符中有效，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsIdentifierStart(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的字符值用作标识符的起始字符是否有效。</summary>
      <returns>如果指定的字符值用作标识符的起始字符有效，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsLetter(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的字符值是否为字母。</summary>
      <returns>如果指定的字符为字母，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsLetterOrDecimalDigit(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的字符值是否为字母或十进制数。</summary>
      <returns>如果指定的字符为字母或十进制数，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsNewLine(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的值是否为换行符。</summary>
      <returns>如果指定的字符为换行符，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsNewLine(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的值是否为换行符。</summary>
      <returns>如果指定的字符为换行符，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsTerminatingCharToken(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的字符值是否为终止字符令牌。</summary>
      <returns>如果指定的字符值为终止字符令牌，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsTerminatingQuotedStringToken(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的字符值是否为带引号的终止字符串。</summary>
      <returns>如果指定的字符值为带引号的终止字符串，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsWhitespace(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的字符值是否为空格。</summary>
      <returns>如果指定的字符值为空格，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsWhitespaceOrNewLine(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的字符值是否为空格或换行符。</summary>
      <returns>如果指定的字符值为空格或换行符，则为 true；否则为 false。</returns>
      <param name="value">要检查的值。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.SanitizeClassName(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。净化指定的输入名称，使其成为有效的类名值。</summary>
      <returns>净化的类名。</returns>
      <param name="inputName">要检查的值。</param>
    </member>
    <member name="T:System.Web.Razor.Parser.ParserVisitor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示分析器访问者。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.ParserVisitor" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserVisitor.CancelToken">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置取消令牌。</summary>
      <returns>取消标记。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.OnComplete">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示访问者方法已完成执行。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.ThrowIfCanceled">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.VisitBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。访问指定块。</summary>
      <param name="block">要访问的块。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.VisitEndBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在分析之后访问指定块。</summary>
      <param name="block">要访问的块。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.VisitError(System.Web.Razor.Parser.SyntaxTree.RazorError)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。访问给定的 Razor 错误。</summary>
      <param name="err">要访问的错误。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.VisitSpan(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。访问指定的跨度。</summary>
      <param name="span">要访问的跨度。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.VisitStartBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在分析之前访问指定块。</summary>
      <param name="block">要访问的块。</param>
    </member>
    <member name="T:System.Web.Razor.Parser.ParserVisitorExtensions">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。提供分析器访问者的扩展方法。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitorExtensions.Visit(System.Web.Razor.Parser.ParserVisitor,System.Web.Razor.ParserResults)"></member>
    <member name="T:System.Web.Razor.Parser.RazorParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Razor 分析器。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.#ctor(System.Web.Razor.Parser.ParserBase,System.Web.Razor.Parser.ParserBase)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.RazorParser" /> 类的新实例。</summary>
      <param name="codeParser">代码分析器。</param>
      <param name="markupParser">标记分析器。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.CreateParseTask(System.IO.TextReader,System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建用于分析指定对象的任务。</summary>
      <returns>创建的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="input">要分析的对象。</param>
      <param name="spanCallback">跨度回调。</param>
      <param name="errorCallback">错误回调。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.CreateParseTask(System.IO.TextReader,System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError},System.Threading.CancellationToken)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建用于分析指定对象的任务。</summary>
      <returns>创建的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="input">要分析的对象。</param>
      <param name="spanCallback">跨度回调。</param>
      <param name="errorCallback">错误回调。</param>
      <param name="cancelToken">取消标记。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.CreateParseTask(System.IO.TextReader,System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError},System.Threading.SynchronizationContext)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建用于分析指定对象的任务。</summary>
      <returns>创建的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="input">要分析的对象。</param>
      <param name="spanCallback">跨度回调。</param>
      <param name="errorCallback">错误回调。</param>
      <param name="context">上下文。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.CreateParseTask(System.IO.TextReader,System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError},System.Threading.SynchronizationContext,System.Threading.CancellationToken)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建用于分析指定对象的任务。</summary>
      <returns>创建的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="input">要分析的对象。</param>
      <param name="spanCallback">跨度回调。</param>
      <param name="errorCallback">错误回调。</param>
      <param name="context">上下文。</param>
      <param name="cancelToken">取消标记。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.CreateParseTask(System.IO.TextReader,System.Web.Razor.Parser.ParserVisitor)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建用于分析指定对象的任务。</summary>
      <returns>创建的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="input">要分析的对象。</param>
      <param name="consumer">使用者。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.RazorParser.DesignTimeMode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取设计时模式。</summary>
      <returns>设计时模式。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.Parse(System.IO.TextReader)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析指定的对象。</summary>
      <returns>分析器结果。</returns>
      <param name="input">要分析的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.Parse(System.IO.TextReader,System.Web.Razor.Parser.ParserVisitor)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析指定的对象。</summary>
      <param name="input">要分析的对象。</param>
      <param name="visitor">访问者。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.Parse(System.Web.Razor.Text.ITextDocument)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析指定的对象。</summary>
      <returns>分析器结果。</returns>
      <param name="input">要分析的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.Parse(System.Web.Razor.Text.LookaheadTextReader)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析指定的对象。</summary>
      <returns>分析器结果。</returns>
      <param name="input">要分析的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.Parse(System.Web.Razor.Text.LookaheadTextReader,System.Web.Razor.Parser.ParserVisitor)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析指定的对象。</summary>
      <param name="input">要分析的对象。</param>
      <param name="visitor">访问者。</param>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxConstants">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.EndCommentSequence">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.StartCommentSequence">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.TextTagName">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.TransitionCharacter">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.TransitionString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxConstants.CSharp">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.ClassKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.ElseIfKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.FunctionsKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.HelperKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.InheritsKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.LayoutKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.NamespaceKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.SectionKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.SessionStateKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.UsingKeywordLength">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxConstants.VB">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.CodeKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.EndCodeKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.EndFunctionsKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.EndHelperKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.EndKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.EndSectionKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.ExplicitKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.FunctionsKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.HelperKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.ImportsKeywordLength">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.LayoutKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.OffKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.SectionKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.SelectCaseKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.SessionStateKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.StrictKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.TokenizerBackedParser`3">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示标记器支持的分析器。</summary>
      <typeparam name="TTokenizer">标记器的类型。</typeparam>
      <typeparam name="TSymbol">符号的类型。</typeparam>
      <typeparam name="TSymbolType">SymbolType 的类型。</typeparam>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.TokenizerBackedParser`3" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Accept(System.Collections.Generic.IEnumerable{`1})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。接受符号的列表。</summary>
      <param name="symbols">符号的列表。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Accept(`1)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。接受指定的符号。</summary>
      <param name="symbol">要接受的符号。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptAll(`2[])">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定分析器是否接受标记器的所有类型。</summary>
      <returns>如果分析器接受标记器的所有类型，则为 true；否则为 false。</returns>
      <param name="types">类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptAndMoveNext">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定分析器是否接受并移至下一个标记器。</summary>
      <returns>如果分析器接受并移至下一个标记器，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptSingleWhiteSpaceCharacter">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定分析器是否接受单个空格字符。</summary>
      <returns>如果分析器接受单个空格字符，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptUntil(`2)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。接受令牌，直到找到给定类型的令牌为止。</summary>
      <param name="type">令牌的类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptUntil(`2,`2)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。接受令牌，直到找到给定类型的令牌为止，并且进行备份，以便下一个令牌是给定的类型。</summary>
      <param name="type1">第一个令牌的类型。</param>
      <param name="type2">第二个令牌的类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptUntil(`2,`2,`2)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。接受给定的令牌，直到找到给定类型的令牌为止。</summary>
      <param name="type1">第一个令牌的类型。</param>
      <param name="type2">第二个令牌的类型。</param>
      <param name="type3">第三个令牌的类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptUntil(`2[])">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。接受令牌，直到找到给定类型的令牌为止。</summary>
      <param name="types">令牌的类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhile(System.Func{`1,System.Boolean})">
      <summary>满足条件时接受令牌。</summary>
      <param name="condition">条件。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhile(`2)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。未找到给定类型的令牌时接受令牌。</summary>
      <param name="type">令牌的类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhile(`2,`2)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。找到给定类型的令牌时接受令牌。</summary>
      <param name="type1">第一个令牌的类型。</param>
      <param name="type2">第二个令牌的类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhile(`2,`2,`2)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。找到给定类型的令牌时接受令牌。</summary>
      <param name="type1">第一个令牌的类型。</param>
      <param name="type2">第二个令牌的类型。</param>
      <param name="type3">第三个令牌的类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhile(`2[])">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。找到给定类型的令牌时接受令牌。</summary>
      <param name="types">类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhiteSpaceInLines">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定分析器是否接受行中的空格。</summary>
      <returns>如果分析器接受行中的空格，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AddMarkerSymbolIfNecessary">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。如果必要，添加标记符号。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AddMarkerSymbolIfNecessary(System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。如果必要，添加标记符号。</summary>
      <param name="location">要添加符号的位置。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.At(`2)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定令牌是否为指定的类型。</summary>
      <returns>如果令牌为指定的类型，则为 true；否则为 false。</returns>
      <param name="type">类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AtIdentifier(System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定令牌是否为指定的标识符。</summary>
      <returns>如果令牌为指定的标识符，则为 true；否则为 false。</returns>
      <param name="allowKeywords">如果允许关键字，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Balance(System.Web.Razor.Parser.BalancingModes)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定分析是否平衡。</summary>
      <returns>如果分析平衡，则为 true；否则为 false。</returns>
      <param name="mode">平衡模式。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Balance(System.Web.Razor.Parser.BalancingModes,`2,`2,System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定分析是否平衡。</summary>
      <returns>如果分析平衡，则为 true；否则为 false。</returns>
      <param name="mode">平衡模式。</param>
      <param name="left">左侧分析。</param>
      <param name="right">右侧分析。</param>
      <param name="start">模式启动。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.BuildSpan(System.Web.Razor.Parser.SyntaxTree.SpanBuilder,System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成指定的跨度。</summary>
      <param name="span">要生成的跨度。</param>
      <param name="start">要生成跨度的起始位置。</param>
      <param name="content">跨度的内容。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.ConfigureSpan(System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。配置跨度。</summary>
      <param name="config">配置。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.ConfigureSpan(System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder,System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder}})">
      <summary>配置跨度。</summary>
      <param name="config">配置。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.CurrentLocation">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取当前实例的当前位置。</summary>
      <returns>当前实例的当前位置。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.CurrentSymbol">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取此实例的当前符号。</summary>
      <returns>此实例的当前符号。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.EndOfFile">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取指示标记器是否位于文件结尾的值。</summary>
      <returns>如果标记器位于文件结尾，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.EnsureCurrent">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定是否确保当前分析器。</summary>
      <returns>如果确保当前分析器，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Expected(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示具有给定类型的预期令牌。</summary>
      <param name="type">类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Expected(`2[])">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示具有给定类型的预期令牌。</summary>
      <param name="types">类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.HandleEmbeddedTransition">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。处理嵌入的过渡。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Initialize(System.Web.Razor.Parser.SyntaxTree.SpanBuilder)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化指定的跨度。</summary>
      <param name="span">要初始化的跨度。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.IsAtEmbeddedTransition(System.Boolean,System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定此实例是否处于嵌入的过渡之中。</summary>
      <returns>如果此实例在嵌入的过渡中，则为 true；否则为 false。</returns>
      <param name="allowTemplatesAndComments">如果允许模板和注释，则为 true；否则为 false。</param>
      <param name="allowTransitions">如果允许过渡，则为 true；否则为 false。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.Language">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取用于分析的语言。</summary>
      <returns>用于分析的语言。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.NextIs(System.Func{`1,System.Boolean})">
      <summary>确定具有给定条件的令牌是否通过。</summary>
      <returns>如果具有给定条件的令牌将通过，则为 true；否则为 false。</returns>
      <param name="condition">条件。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.NextIs(`2)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定具有给定类型的令牌是否通过。</summary>
      <returns>如果具有给定类型的令牌将通过，则为 true；否则为 false。</returns>
      <param name="type">令牌的类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.NextIs(`2[])">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定具有给定类型的令牌是否通过。</summary>
      <returns>如果具有给定类型的令牌将通过，则为 true；否则为 false。</returns>
      <param name="types">类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.NextToken">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定分析器是否转到下一个令牌。</summary>
      <returns>如果分析器转到下一个令牌，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Optional(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定分析具有给定类型的令牌是否是可选的。</summary>
      <returns>如果分析具有给定类型的令牌是可选的，则为 true；否则为 false。</returns>
      <param name="type">令牌的类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Optional(`2)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定分析具有给定类型的令牌是否是可选的。</summary>
      <returns>如果分析具有给定类型的令牌是可选的，则为 true；否则为 false。</returns>
      <param name="type">令牌的类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Output(System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。输出具有所接受字符的令牌。</summary>
      <param name="accepts">接受的字符。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Output(System.Web.Razor.Parser.SyntaxTree.SpanKind)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。输出具有跨度类型的令牌。</summary>
      <param name="kind">跨度类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Output(System.Web.Razor.Parser.SyntaxTree.SpanKind,System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。输出具有某种跨度类型和所接受字符的令牌。</summary>
      <param name="kind">跨度类型。</param>
      <param name="accepts">接受的字符。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.OutputSpanBeforeRazorComment">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在 Razor 注释之前输出跨度。</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.PreviousSymbol">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取此实例的上一符号。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PushSpanConfig">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。推送跨度配置。</summary>
      <returns>关闭配置的 <see cref="T:System.IDisposable" />。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PushSpanConfig(System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。推送跨度配置。</summary>
      <returns>关闭配置的 <see cref="T:System.IDisposable" />。</returns>
      <param name="newConfig">新配置。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PushSpanConfig(System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder,System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder}})">
      <summary>推送跨度配置。</summary>
      <returns>关闭配置的 <see cref="T:System.IDisposable" />。</returns>
      <param name="newConfig">新配置。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PutBack(System.Collections.Generic.IEnumerable{`1})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将过渡放回。</summary>
      <param name="symbols">符号。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PutBack(`1)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将过渡放回。</summary>
      <param name="symbol">符号。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PutCurrentBack">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将当前过渡放回。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.RazorComment">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。显示 Razor 注释。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.ReadWhile(System.Func{`1,System.Boolean})">
      <summary>未满足条件时读取令牌。</summary>
      <returns>要读取的令牌。</returns>
      <param name="condition">条件。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Required(`2,System.Boolean,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定是否需要预期令牌。</summary>
      <returns>如果需要预期令牌，则为 true；否则为 false。</returns>
      <param name="expected">预期令牌。</param>
      <param name="errorIfNotFound">如果未找到时显示错误，则为 true；否则为 false。</param>
      <param name="errorBase">错误库。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.Span">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置与此实例关联的 <see cref="T:System.Web.Razor.Parser.SyntaxTree.SpanBuilder" />。</summary>
      <returns>与此实例关联的 <see cref="T:System.Web.Razor.Parser.SyntaxTree.SpanBuilder" />。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.SpanConfig">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置跨度配置。</summary>
      <returns>跨度配置。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.Tokenizer">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取标记器。</summary>
      <returns>标记器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Was(`2)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定是否已解析具有给定类型的令牌。</summary>
      <returns>如果已解析具有给定类型的令牌，则为 true；否则为 false。</returns>
      <param name="type">令牌的类型。</param>
    </member>
    <member name="T:System.Web.Razor.Parser.VBCodeParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Visual Basic 代码分析器。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.VBCodeParser" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.AcceptVBSpaces">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。接受 VB 代码中的空格。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.Assert(System.Web.Razor.Tokenizer.Symbols.VBKeyword)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。检查条件并显示代码中的关键字。</summary>
      <param name="keyword">关键字。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.AssertDirective(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。断言给定的指令。</summary>
      <param name="directive">要断言的指令。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.At(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指令是否为“AT”指令。</summary>
      <returns>如果指令为“AT”指令，则为 true；否则为 false。</returns>
      <param name="directive">指令。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.At(System.Web.Razor.Tokenizer.Symbols.VBKeyword)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定给定关键字是否为“AT”。</summary>
      <returns>如果给定关键字为“AT”，则为 true；否则为 false。</returns>
      <param name="keyword">关键字。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.EndTerminatedDirective(System.String,System.Web.Razor.Parser.SyntaxTree.BlockType,System.Web.Razor.Generator.SpanCodeGenerator,System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。结束已终止的指令。</summary>
      <returns>用于结束已终止指令的函数。</returns>
      <param name="directive">指令。</param>
      <param name="blockType">块类型。</param>
      <param name="codeGenerator">代码生成器。</param>
      <param name="allowMarkup">如果允许标记，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.EndTerminatedDirectiveBody(System.String,System.Web.Razor.Text.SourceLocation,System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指令主体的终止是否已结束。</summary>
      <returns>如果指令主体的终止已结束，则为 true；否则为 false。</returns>
      <param name="directive">指令。</param>
      <param name="blockStart">块开始。</param>
      <param name="allowAllTransitions">如果允许所有过渡，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.EndTerminatedStatement(System.Web.Razor.Tokenizer.Symbols.VBKeyword,System.Boolean,System.Boolean)">
      <summary>结束语句的终止。</summary>
      <returns>用于结束终止的函数。</returns>
      <param name="keyword">关键字。</param>
      <param name="supportsExit">如果终止支持退出，则为 true；否则为 false。</param>
      <param name="supportsContinue">如果终止支持继续，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.EndTerminatedStatement(System.Web.Razor.Tokenizer.Symbols.VBKeyword,System.Boolean,System.Boolean,System.String)">
      <summary>结束语句的终止。</summary>
      <returns>用于结束终止的函数。</returns>
      <param name="keyword">关键字。</param>
      <param name="supportsExit">如果终止支持退出，则为 true；否则为 false。</param>
      <param name="supportsContinue">如果终止支持继续，则为 true；否则为 false。</param>
      <param name="blockName">块名称。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.HandleEmbeddedTransition">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。处理嵌入的过渡。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.HandleEmbeddedTransition(System.Web.Razor.Tokenizer.Symbols.VBSymbol)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。处理嵌入的过渡。</summary>
      <param name="lastWhiteSpace">最后一个空格。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.HandleExitOrContinue(System.Web.Razor.Tokenizer.Symbols.VBKeyword)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示处理 Exit 或 Continue 关键字的代码。</summary>
      <param name="keyword">关键字。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.HandleTransition(System.Web.Razor.Tokenizer.Symbols.VBSymbol)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示处理过渡的代码。</summary>
      <param name="lastWhiteSpace">最后一个空格。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.HelperDirective">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示代码是否为帮助器指令。</summary>
      <returns>如果代码为帮助器指令，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.ImportsStatement">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定代码是否导入语句。</summary>
      <returns>如果代码导入语句，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.InheritsStatement">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定代码是否继承语句。</summary>
      <returns>如果代码继承语句，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.IsAtEmbeddedTransition(System.Boolean,System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定代码是否处于嵌入的过渡之中。</summary>
      <returns>如果代码在嵌入的过渡中，则为 true；否则为 false。</returns>
      <param name="allowTemplatesAndComments">如果允许模板和注释，则为 true；否则为 false。</param>
      <param name="allowTransitions">如果允许过渡，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.IsDirectiveDefined(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定代码是否为定义的指令。</summary>
      <returns>如果代码为定义的指令，则为 true；否则为 false。</returns>
      <param name="directive">指令。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.VBCodeParser.Keywords">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取与代码关联的关键字。</summary>
      <returns>与代码关联的关键字。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.KeywordTerminatedStatement(System.Web.Razor.Tokenizer.Symbols.VBKeyword,System.Web.Razor.Tokenizer.Symbols.VBKeyword,System.Boolean,System.Boolean)">
      <summary>指示终止语句的关键字。</summary>
      <returns>终止语句的函数。</returns>
      <param name="start">开始。</param>
      <param name="terminator">终止符。</param>
      <param name="supportsExit">如果终止支持退出，则为 true；否则为 false。</param>
      <param name="supportsContinue">如果终止支持继续，则为 true；否则为 false。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.VBCodeParser.Language">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取分析器的语言。</summary>
      <returns>分析器的语言。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.LayoutDirective">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示代码是否为布局指令。</summary>
      <returns>如果代码为布局指令，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.MapDirective(System.String,System.Func{System.Boolean})">
      <summary>映射给定的指令。</summary>
      <param name="directive">指令。</param>
      <param name="action">指示是否映射给定指令的操作。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.MapKeyword(System.Web.Razor.Tokenizer.Symbols.VBKeyword,System.Func{System.Boolean})">
      <summary>映射给定的关键字。</summary>
      <param name="keyword">关键字。</param>
      <param name="action">指示是否映射给定关键字的操作。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.NestedBlock">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示嵌套块。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.Optional(System.Web.Razor.Tokenizer.Symbols.VBKeyword)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定代码中的关键字是否可选。</summary>
      <returns>如果代码中的关键字可选，则为 true；否则为 false。</returns>
      <param name="keyword">关键字。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.OptionStatement">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定代码是否为选项语句。</summary>
      <returns>如果代码为选项语句，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.VBCodeParser.OtherParser">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取其他分析器。</summary>
      <returns>其他分析器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.OtherParserBlock">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示分析器块。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.OtherParserBlock(System.String,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示分析器块。</summary>
      <param name="startSequence">开始序列。</param>
      <param name="endSequence">结束序列。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.OutputSpanBeforeRazorComment">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。在 Razor 注释之前跨越输出。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.ParseBlock">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。阻止分析。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.ReadVBSpaces">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。读取 Visual Basic 空格的列表。</summary>
      <returns>Visual Basic 空格的列表。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.Required(System.Web.Razor.Tokenizer.Symbols.VBSymbolType,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定是否需要预期符号。</summary>
      <returns>如果需要预期符号，则为 true；否则为 false。</returns>
      <param name="expected">预期符号。</param>
      <param name="errorBase">错误库。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.ReservedWord">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示代码是否为保留字。</summary>
      <returns>如果代码为保留字，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.SectionDirective">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示代码是否为区域指令。</summary>
      <returns>如果代码为区域指令，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.SessionStateDirective">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示代码是否具有会话状态指令。</summary>
      <returns>如果代码具有会话状态指令，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.VBLanguageCharacteristics">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Visual Basic 语言的特征。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.CreateMarkerSymbol(System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建 Visual Basic 标记符号。</summary>
      <returns>创建的 Visual Basic 标记符号。</returns>
      <param name="location">要创建符号的位置。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建 Visual Basic 符号。</summary>
      <returns>创建的 <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbol" />。</returns>
      <param name="location">要创建符号的位置。</param>
      <param name="content">内容。</param>
      <param name="type">符号的类型。</param>
      <param name="errors">错误。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.CreateTokenizer(System.Web.Razor.Text.ITextDocument)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建 Visual Basic 标记器。</summary>
      <returns>创建的 <see cref="T:System.Web.Razor.Tokenizer.VBTokenizer" />。</returns>
      <param name="source">要从中创建标记器的源。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.FlipBracket(System.Web.Razor.Tokenizer.Symbols.VBSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。翻转给定的括号。</summary>
      <returns>Visual Basic 符号的类型。</returns>
      <param name="bracket">要翻转的括号。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.GetKnownSymbolType(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。检索已知符号的类型。</summary>
      <returns>已知符号的类型。</returns>
      <param name="type">要检索的类型。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.GetSample(System.Web.Razor.Tokenizer.Symbols.VBSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取具有给定类型的示例符号。</summary>
      <returns>具有给定类型的示例符号。</returns>
      <param name="type">符号的类型。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.VBLanguageCharacteristics.Instance">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取此 <see cref="T:System.Web.Razor.Parser.VBLanguageCharacteristics" /> 的实例。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.VBLanguageCharacteristics" /> 的实例。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.AllWhiteSpace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.Any">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.AnyExceptNewline">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.NewLine">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.None">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.NonWhiteSpace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.WhiteSpace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示自动完成编辑处理程序类。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.#ctor(System.Func{System.String,System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}})">
      <summary>初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler" /> 类的新实例。</summary>
      <param name="tokenizer">标记器。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.#ctor(System.Func{System.String,System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}},System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters)">
      <summary>初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler" /> 类的新实例。</summary>
      <param name="tokenizer">标记器。</param>
      <param name="accepted">接受的字符。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.AutoCompleteAtEndOfSpan">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置指示自动完成函数是否在此跨度末尾的值。</summary>
      <returns>如果自动完成函数在此跨度末尾，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.AutoCompleteString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置要自动完成的字符串值。</summary>
      <returns>要自动完成的字符串值。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.CanAcceptChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示可以接受更改的分析结果。</summary>
      <param name="target">目标的阶段。</param>
      <param name="normalizedChange">规范化 <see cref="T:System.Web.Razor.Text.TextChange" />。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示该实例与指定对象是否相等。</summary>
      <returns>如果 <paramref name="obj" /> 与该实例属于相同类型且表示相同值，则为 true；否则为 false。</returns>
      <param name="obj">对象。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的哈希代码。</summary>
      <returns>32 位有符号整数，它是此实例的哈希代码。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的完全限定类型名称。</summary>
      <returns>包含完全限定类型名称的字符串。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.Block">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示用于创建网页的块。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.#ctor(System.Web.Razor.Parser.SyntaxTree.BlockBuilder)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.Block" /> 类的新实例。</summary>
      <param name="source">块生成器的源。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.Accept(System.Web.Razor.Parser.ParserVisitor)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。接受块的分析器访问者。</summary>
      <param name="visitor">分析器访问者。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.Children">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取要查看块子项的 SyntaxTreeNode 集合。</summary>
      <returns>要查看块子项的 SyntaxTreeNode 集合。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.CodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取要为元素生成代码的 IBlockCodeGenerator。</summary>
      <returns>要为元素生成代码的 IBlockCodeGenerator。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定的对象是否等于当前块。</summary>
      <returns>如果指定的对象等于当前块，则为 true；否则为 false。</returns>
      <param name="obj">要与当前对象比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.EquivalentTo(System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回指示块是否等于相同元素的值。</summary>
      <returns>如果块等于相同元素，则为 true；否则为 false。</returns>
      <param name="node">语法树节点。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.FindFirstDescendentSpan">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。查找块的第一个附属跨度。</summary>
      <returns>块的第一个附属跨度。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.FindLastDescendentSpan">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。查找块的最后一个附属跨度。</summary>
      <returns>块的最后一个附属跨度。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.Flatten">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。扁平化块的指定类型集合。</summary>
      <returns>要扁平化的块的指定类型集合。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的哈希代码。</summary>
      <returns>此实例的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.IsBlock">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取指示对象是否为块级对象的值。</summary>
      <returns>如果对象是块级对象，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.Length">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取块的长度值。</summary>
      <returns>块的长度值。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.LocateOwner(System.Web.Razor.Text.TextChange)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。查找块的所有者。</summary>
      <returns>要查找的块的所有者。</returns>
      <param name="change">文本更改。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.Name">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取块的字符串名称。</summary>
      <returns>块的字符串名称。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.Start">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取要标识块的指定位置的起点。</summary>
      <returns>要标识块的指定位置的起点。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回表示当前对象的字符串。</summary>
      <returns>表示当前对象的字符串。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.Type">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取代码块的类型。</summary>
      <returns>代码块的类型。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.BlockBuilder">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示网页的块生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.BlockBuilder" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.#ctor(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.BlockBuilder" /> 类的新实例。</summary>
      <param name="original">原始块生成器。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.Build">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。构建此实例的块。</summary>
      <returns>此实例的块生成。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.Children">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取块生成器的子元素集合。</summary>
      <returns>块生成器的子元素集合。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.CodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置块生成器的代码生成器。</summary>
      <returns>块生成器的代码生成器。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.Name">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置块生成器的字符串名称。</summary>
      <returns>块生成器的字符串名称。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.Reset">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将块生成器重置为其原始位置。</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.Type">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置可以分配 null 的块类型。</summary>
      <returns>可以分配 null 的块类型。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.BlockType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Comment">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Directive">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Expression">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Functions">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Helper">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Markup">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Section">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Statement">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Template">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.RazorError">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Razor 中的分析错误。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.#ctor(System.String,System.Int32,System.Int32,System.Int32)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.RazorError" /> 类的新实例。</summary>
      <param name="message">错误消息。</param>
      <param name="absoluteIndex">源位置的绝对索引。</param>
      <param name="lineIndex">源位置的行索引。</param>
      <param name="columnIndex">源位置的列索引。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.#ctor(System.String,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.RazorError" /> 类的新实例。</summary>
      <param name="message">错误消息。</param>
      <param name="absoluteIndex">源位置的绝对索引。</param>
      <param name="lineIndex">源位置的行索引。</param>
      <param name="columnIndex">源位置的列索引。</param>
      <param name="length">错误的长度。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.#ctor(System.String,System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.RazorError" /> 类的新实例。</summary>
      <param name="message">错误消息。</param>
      <param name="location">错误的源位置。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.#ctor(System.String,System.Web.Razor.Text.SourceLocation,System.Int32)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.RazorError" /> 类的新实例。</summary>
      <param name="message">错误消息。</param>
      <param name="location">错误的源位置。</param>
      <param name="length">错误的长度。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于此实例。</summary>
      <returns>如果指定对象等于此实例，则为 true；否则为 false。</returns>
      <param name="obj">要与此实例比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.Equals(System.Web.Razor.Parser.SyntaxTree.RazorError)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于此实例。</summary>
      <returns>如果指定对象等于此实例，则为 true；否则为 false。</returns>
      <param name="other">要与此实例比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回当前实例的哈希代码。</summary>
      <returns>当前实例的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.RazorError.Length">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置错误的长度。</summary>
      <returns>错误的长度。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.RazorError.Location">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取错误的源位置。</summary>
      <returns>错误的源位置。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.RazorError.Message">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置错误消息。</summary>
      <returns>错误消息。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此错误实例的字符串表示形式。</summary>
      <returns>此错误实例的字符串表示形式。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.Span">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示包含块节点所有内容的 Razor 分析树节点。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.#ctor(System.Web.Razor.Parser.SyntaxTree.SpanBuilder)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.Span" /> 类的新实例。</summary>
      <param name="builder">要用于此跨度的生成器。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.Accept(System.Web.Razor.Parser.ParserVisitor)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。接受指定访问者进行的访问。</summary>
      <param name="visitor">执行访问的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.Change(System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。更改此跨度的跨度生成器。</summary>
      <param name="changes">将与此更改一起执行的委托。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.ChangeStart(System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。设置此跨度的起始字符位置。</summary>
      <param name="newStart">要为此跨度设置的新起始位置。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.CodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置跨度的代码生成器。</summary>
      <returns>跨度的代码生成器。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Content">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置跨度的内容。</summary>
      <returns>跨度的内容。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.EditHandler">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置跨度编辑的处理程序。</summary>
      <returns>跨度编辑的处理程序。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于此跨度。</summary>
      <returns>如果指定对象等于此跨度，则为 true；否则为 false。</returns>
      <param name="obj">要与此跨度比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.EquivalentTo(System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定节点是否等于此跨度。</summary>
      <returns>如果指定节点等于此跨度，则为 true；否则为 false。</returns>
      <param name="node">要与此跨度比较的节点。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此当前跨度的哈希代码。</summary>
      <returns>此当前跨度的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.IsBlock">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置用于指示此节点是否为块节点的值。</summary>
      <returns>false。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Kind">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置此跨度的类型。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Parser.SyntaxTree.SpanKind" /> 枚举的值之一。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Length">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置跨度内容的长度。</summary>
      <returns>跨度内容的长度。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Next">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置树节点中的下一个跨度。</summary>
      <returns>树节点中的下一个跨度。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Previous">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置树节点中的上一个跨度。</summary>
      <returns>树节点中的上一个跨度。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.ReplaceWith(System.Web.Razor.Parser.SyntaxTree.SpanBuilder)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用指定的跨度生成器替换此跨度的跨度生成器。</summary>
      <param name="builder">要用于此跨度的新生成器。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Start">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置跨度的起始字符位置。</summary>
      <returns>跨度的起始字符位置。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Symbols">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置用于生成跨度代码的符号。</summary>
      <returns>用于生成跨度代码的符号。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此当前跨度的字符串表示形式。</summary>
      <returns>此当前跨度的字符串表示形式。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.SpanBuilder">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示语法树的跨度生成器。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.SpanBuilder" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.#ctor(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.SpanBuilder" /> 类的新实例。</summary>
      <param name="original">原始跨度。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Accept(System.Web.Razor.Tokenizer.Symbols.ISymbol)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。接受跨度生成器的给定符号。</summary>
      <param name="symbol">符号生成器。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Build">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。构建此实例的跨度生成器。</summary>
      <returns>此实例的跨度生成器。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.ClearSymbols">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。清除跨度生成器的符号。</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.CodeGenerator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置跨度代码生成器。</summary>
      <returns>跨度代码生成器。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.EditHandler">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置生成器的跨度编辑处理程序。</summary>
      <returns>生成器的跨度编辑处理程序。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Kind">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置跨度生成器的跨度类型。</summary>
      <returns>跨度生成器的跨度类型。</returns>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Reset">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。重置跨度生成器。</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Start">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置跨度生成器的源位置。</summary>
      <returns>跨度生成器的源位置。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Symbols">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取泛型只读集合的符号。</summary>
      <returns>泛型只读集合的符号。</returns>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.SpanKind">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.SpanKind.Code">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.SpanKind.Comment">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.SpanKind.Markup">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.SpanKind.MetaCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.SpanKind.Transition">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示语法树的节点。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.Accept(System.Web.Razor.Parser.ParserVisitor)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。接受树节点的访问者。</summary>
      <param name="visitor">分析器访问者。</param>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.EquivalentTo(System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示语法树节点是否等于给定的节点。</summary>
      <returns>如果语法树节点等于给定的节点，则为 true；否则为 false。</returns>
      <param name="node">给定的节点。</param>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.IsBlock">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取指示语法树节点是否为块级对象的值。</summary>
      <returns>如果语法树节点为块级对象，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.Length">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取语法树节点的长度。</summary>
      <returns>语法树节点的长度。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.Parent">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取当前树节点的父树节点。</summary>
      <returns>当前树节点的父树节点。</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.Start">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取语法树节点的特定源位置。</summary>
      <returns>语法树节点的特定源位置。</returns>
    </member>
    <member name="T:System.Web.Razor.Text.BufferingTextReader">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。提供文本读取器的预期缓冲区。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.#ctor(System.IO.TextReader)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Text.BufferingTextReader" /> 类的新实例。</summary>
      <param name="source">缓冲区的文本读取器。</param>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.BeginLookahead">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。开始此 <see cref="T:System.Web.Razor.Text.BufferingTextReader" /> 的预期缓冲操作。</summary>
      <returns>结束预期缓冲的可释放操作</returns>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.CancelBacktrack">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。丢弃与预期缓冲操作相关的返回上下文。</summary>
    </member>
    <member name="P:System.Web.Razor.Text.BufferingTextReader.CurrentCharacter">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取缓冲区中的当前字符。</summary>
      <returns>缓冲区中的当前字符。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.BufferingTextReader.CurrentLocation">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置缓冲区中字符的当前位置。</summary>
      <returns>缓冲区中字符的当前位置。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.Dispose(System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。释放该类的当前实例使用的非托管资源，并有选择性地释放托管资源。</summary>
      <param name="disposing">若为 true，则同时释放托管资源和非托管资源；若为 false，则仅释放非托管资源。</param>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.ExpandBuffer">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。从文本读取器中读取下一个字符并将该字符追加到预期缓冲区中。</summary>
      <returns>如果已从文本读取器中读取字符，，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.NextCharacter">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将缓冲区位置提前到下一个字符。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.Peek">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回缓冲区中的当前字符。</summary>
      <returns>缓冲区中的当前字符。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.Read">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回缓冲区中的当前字符并将缓冲区位置提前到下一个字符。</summary>
      <returns>缓冲区中的当前字符。</returns>
    </member>
    <member name="T:System.Web.Razor.Text.ITextBuffer">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="P:System.Web.Razor.Text.ITextBuffer.Length">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.ITextBuffer.Peek">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="P:System.Web.Razor.Text.ITextBuffer.Position">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.ITextBuffer.Read">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Text.ITextDocument">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="P:System.Web.Razor.Text.ITextDocument.Location">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Text.LocationTagged`1">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示标记的位置。</summary>
      <typeparam name="T">所标记位置的类型。</typeparam>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.#ctor(`0,System.Int32,System.Int32,System.Int32)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Text.LocationTagged`1" /> 类的新实例。</summary>
      <param name="value">源的值。</param>
      <param name="offset">偏移量。</param>
      <param name="line">行。</param>
      <param name="col">源的列位置。</param>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.#ctor(`0,System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Text.LocationTagged`1" /> 类的新实例。</summary>
      <param name="value">源的值。</param>
      <param name="location">源的位置。</param>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于当前对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回当前实例的哈希代码。</summary>
      <returns>当前实例的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.LocationTagged`1.Location">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置源的位置。</summary>
      <returns>源的位置。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.op_Equality(System.Web.Razor.Text.LocationTagged{`0},System.Web.Razor.Text.LocationTagged{`0})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定两个 <see cref="T:System.Web.Razor.Text.LocationTagged{`0}" /> 对象是否相等。</summary>
      <returns>如果两个对象相等，则为 true；否则为 false。</returns>
      <param name="left">要比较的第一个对象。</param>
      <param name="right">要比较的第二个对象。</param>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.op_Implicit(System.Web.Razor.Text.LocationTagged{`0})~`0">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将指定的值转换为 <see cref="T:System.Web.Razor.Text.LocationTagged`1" /> 对象。</summary>
      <returns>如果转换成功，则为 true；否则为 false。</returns>
      <param name="value">要转换的值。</param>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.op_Inequality(System.Web.Razor.Text.LocationTagged{`0},System.Web.Razor.Text.LocationTagged{`0})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定两个 <see cref="T:System.Web.Razor.Text.LocationTagged{`0}" /> 对象是否不相等。</summary>
      <returns>如果两个对象不相等，则为 true；否则为 false。</returns>
      <param name="left">要比较的第一个对象。</param>
      <param name="right">要比较的第二个对象。</param>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回当前实例的字符串表示形式。</summary>
      <returns>表示当前实例的字符串。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.ToString(System.String,System.IFormatProvider)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回当前实例的字符串表示形式。</summary>
      <returns>表示当前实例的字符串。</returns>
      <param name="format">格式。</param>
      <param name="formatProvider">格式提供程序。</param>
    </member>
    <member name="P:System.Web.Razor.Text.LocationTagged`1.Value">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置源的值。</summary>
      <returns>源的值。</returns>
    </member>
    <member name="T:System.Web.Razor.Text.LookaheadTextReader">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadTextReader.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadTextReader.BeginLookahead">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadTextReader.CancelBacktrack">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="P:System.Web.Razor.Text.LookaheadTextReader.CurrentLocation">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Text.LookaheadToken">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示用于查找 razor 的令牌。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadToken.#ctor(System.Action)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Text.LookaheadToken" /> 类的新实例。</summary>
      <param name="cancelAction">要取消的操作。</param>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadToken.Accept">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。接受令牌。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadToken.Dispose">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。释放 <see cref="T:System.Web.Razor.Text.LookaheadToken" /> 类的当前实例使用的资源。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadToken.Dispose(System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。释放 <see cref="T:System.Web.Razor.Text.LookaheadToken" /> 使用的非托管资源并选择性地释放托管资源。</summary>
      <param name="disposing">若为 true，则同时释放托管资源和非托管资源；若为 false，则仅释放非托管资源。</param>
    </member>
    <member name="T:System.Web.Razor.Text.SeekableTextReader">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示读取器</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SeekableTextReader.#ctor(System.IO.TextReader)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Text.SeekableTextReader" /> 类的新实例。</summary>
      <param name="source">源读取器。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SeekableTextReader.#ctor(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Text.SeekableTextReader" /> 类的新实例。</summary>
      <param name="content">字符串内容。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SeekableTextReader.#ctor(System.Web.Razor.Text.ITextBuffer)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Text.SeekableTextReader" /> 类的新实例。</summary>
      <param name="buffer">文本缓冲。</param>
    </member>
    <member name="P:System.Web.Razor.Text.SeekableTextReader.Length">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取要读取的文本的长度。</summary>
      <returns>要读取的文本的长度。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.SeekableTextReader.Location">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取文本读取器的位置源。</summary>
      <returns>文本读取器的位置源。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.SeekableTextReader.Peek">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。读取下一个字符但不更改读取器的状态或字符源。</summary>
      <returns>表示要读取的下一个字符的整数。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.SeekableTextReader.Position">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置要定位文本读取器的位置。</summary>
      <returns>要定位文本读取器的位置。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.SeekableTextReader.Read">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。从文本读取器中读取下一个字符并将字符位置提前一个字符。</summary>
      <returns>文本读取器中的下一个字符，如果没有其他字符，则为 -1。</returns>
    </member>
    <member name="T:System.Web.Razor.Text.SourceLocation">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示源位置。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.#ctor(System.Int32,System.Int32,System.Int32)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Text.SourceLocation" /> 类的新实例。</summary>
      <param name="absoluteIndex">绝对索引。</param>
      <param name="lineIndex">行索引。</param>
      <param name="characterIndex">字符索引。</param>
    </member>
    <member name="P:System.Web.Razor.Text.SourceLocation.AbsoluteIndex">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取源位置的绝对索引。</summary>
      <returns>源位置的绝对索引。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.Add(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。添加两个 <see cref="T:System.Web.Razor.Text.SourceLocation" /> 对象。</summary>
      <returns>两个 <see cref="T:System.Web.Razor.Text.SourceLocation" /> 对象之和。</returns>
      <param name="left">要添加的第一个对象。</param>
      <param name="right">要添加的第二个对象。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.Advance(System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将指定对象提前到给定的位置。</summary>
      <returns>源位置。</returns>
      <param name="left">要将对象提前到的位置。</param>
      <param name="text">提前到给定位置的文本。</param>
    </member>
    <member name="P:System.Web.Razor.Text.SourceLocation.CharacterIndex">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取源位置的字符索引。</summary>
      <returns>源位置的字符索引。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.CompareTo(System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将当前对象与另一个对象进行比较。</summary>
      <returns>所比较对象的值。</returns>
      <param name="other">要比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于当前对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.Equals(System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定当前 <see cref="T:System.Web.Razor.Text.SourceLocation" /> 对象是否等于另一个 <see cref="T:System.Web.Razor.Text.SourceLocation" /> 对象。</summary>
      <returns>如果当前对象等于另一个对象，则为 true；否则为 false。</returns>
      <param name="other">要比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的哈希代码。</summary>
      <returns>此实例的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.SourceLocation.LineIndex">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取源位置的行索引。</summary>
      <returns>源位置的行索引。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_Addition(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。添加两个 <see cref="T:System.Web.Razor.Text.SourceLocation" /> 对象。</summary>
      <returns>代表两个对象之和的 <see cref="T:System.Web.Razor.Text.SourceLocation" />。</returns>
      <param name="left">要添加的对象。</param>
      <param name="right">要添加的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_Equality(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定两个对象是否相等。</summary>
      <returns>如果两个对象相等，则为 true；否则为 false。</returns>
      <param name="left">要比较的第一个对象。</param>
      <param name="right">要比较的第二个对象。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_GreaterThan(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定第一个对象是否大于第二个对象。</summary>
      <returns>如果第一个对象大于第二个对象，则为 true；否则为 false。</returns>
      <param name="left">第一个对象。</param>
      <param name="right">第二个对象。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_Inequality(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定两个 <see cref="T:System.Web.Razor.Text.SourceLocation" /> 对象是否不相等。</summary>
      <returns>如果两个对象不相等，则为 true；否则为 false。</returns>
      <param name="left">要比较的对象。</param>
      <param name="right">要比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_LessThan(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定第一个对象是否小于第二个对象。</summary>
      <returns>如果第一个对象大于第二个对象，则为 true；否则为 false。</returns>
      <param name="left">第一个对象。</param>
      <param name="right">第二个对象。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_Subtraction(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
      <returns>返回 <see cref="T:System.Web.Razor.Text.SourceLocation" />。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.Subtract(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将第一个对象与第二个对象相减。</summary>
      <returns>两个对象之差。</returns>
      <param name="left">第一个对象。</param>
      <param name="right">第二个对象。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回源位置的字符串表示形式。</summary>
      <returns>源位置的字符串表示形式。</returns>
    </member>
    <member name="F:System.Web.Razor.Text.SourceLocation.Undefined">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Text.SourceLocation.Zero">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Text.SourceLocationTracker">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。提供源位置跟踪程序。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocationTracker.#ctor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Text.SourceLocationTracker" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocationTracker.#ctor(System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Text.SourceLocationTracker" /> 类的新实例。</summary>
      <param name="currentLocation">源的当前位置。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocationTracker.CalculateNewLocation(System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。计算源的新位置。</summary>
      <returns>新的源位置。</returns>
      <param name="lastPosition">最后一个位置。</param>
      <param name="newContent">新内容。</param>
    </member>
    <member name="P:System.Web.Razor.Text.SourceLocationTracker.CurrentLocation">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置源的当前位置。</summary>
      <returns>源的当前位置。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocationTracker.UpdateLocation(System.Char,System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。更新源位置。</summary>
      <param name="characterRead">要读取的字符。</param>
      <param name="nextCharacter">要更新的字符。</param>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocationTracker.UpdateLocation(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。更新源的位置。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Text.SourceLocationTracker" /> 对象。</returns>
      <param name="content">源的内容。</param>
    </member>
    <member name="T:System.Web.Razor.Text.TextBufferReader">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。提供文本缓冲区的读取器。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.#ctor(System.Web.Razor.Text.ITextBuffer)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Text.TextBufferReader" /> 类的新实例。</summary>
      <param name="buffer">要读取的文本缓冲区。</param>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.BeginLookahead">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。开始读取当前文本缓冲区。</summary>
      <returns>停止文本缓冲区的 <see cref="T:System.IDisposable" /> 实例。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.CancelBacktrack">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。取消返回。</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextBufferReader.CurrentLocation">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取文本缓冲区的当前位置。</summary>
      <returns>文本缓冲区的当前位置。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.Dispose(System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。释放 <see cref="T:System.Web.Razor.Text.TextBufferReader" /> 类使用的非托管资源并选择性地释放托管资源。</summary>
      <param name="disposing">若为 true，则同时释放托管资源和非托管资源；若为 false，则仅释放非托管资源。</param>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.Peek">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回要读取的下一个文本缓冲区。</summary>
      <returns>要读取的下一个文本缓冲区。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.Read">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。读取当前文本缓冲区。</summary>
      <returns>当前文本缓冲区。</returns>
    </member>
    <member name="T:System.Web.Razor.Text.TextChange">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。描述文本更改操作。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.#ctor(System.Int32,System.Int32,System.Web.Razor.Text.ITextBuffer,System.Int32,System.Int32,System.Web.Razor.Text.ITextBuffer)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Text.TextChange" /> 类的新实例。</summary>
      <param name="oldPosition">在更改之前的那一刻文本更改在快照中的位置。</param>
      <param name="oldLength">旧文本的长度。</param>
      <param name="oldBuffer">旧文本缓冲区。</param>
      <param name="newPosition">在更改之后的那一刻文本更改在快照中的位置。</param>
      <param name="newLength">新文本的长度。</param>
      <param name="newBuffer">新文本缓冲区。</param>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.ApplyChange(System.String,System.Int32)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。应用指定的文本更改。</summary>
      <returns>包含文本值的字符串。</returns>
      <param name="content">文本的内容。</param>
      <param name="changeOffset">更改偏移量。</param>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.ApplyChange(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。应用指定的文本更改。</summary>
      <returns>包含文本值的字符串。</returns>
      <param name="span">文本更改的跨度。</param>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于当前对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取此文本更改的哈希代码。</summary>
      <returns>此文本更改的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.IsDelete">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取一个值，该值指示此文本更改是否为删除。</summary>
      <returns>如果此文本更改是删除，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.IsInsert">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取一个值，该值指示此文本更改是否为插入。</summary>
      <returns>如果此文本更改是插入，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.IsReplace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取一个值，该值指示此文本更改是否为替换。</summary>
      <returns>如果此文本更改是替换，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.NewBuffer">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置新的文本缓冲区。</summary>
      <returns>新文本缓冲区。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.NewLength">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置新文本的长度。</summary>
      <returns>新文本的长度。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.NewPosition">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置在更改之后的那一刻文本更改在快照中的位置。</summary>
      <returns>在更改之后的那一刻文本更改在快照中的位置。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.NewText">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置替换了旧文本的文本。</summary>
      <returns>替换了旧文本的文本。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.Normalize">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此文本更改的规范化值。</summary>
      <returns>此文本更改的规范化值。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.OldBuffer">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置旧文本缓冲区。</summary>
      <returns>旧文本缓冲区。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.OldLength">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置旧文本的长度。</summary>
      <returns>旧文本的长度。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.OldPosition">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置在更改之前的那一刻文本更改在快照中的位置。</summary>
      <returns>在更改之前的那一刻文本更改在快照中的位置。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.OldText">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置被替换的文本。</summary>
      <returns>被替换的文本。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.op_Equality(System.Web.Razor.Text.TextChange,System.Web.Razor.Text.TextChange)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定两个文本更改是否相等。</summary>
      <returns>如果两个文本更改相等，则为 true；否则为 false。</returns>
      <param name="left">左文本更改。</param>
      <param name="right">右文本更改。</param>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.op_Inequality(System.Web.Razor.Text.TextChange,System.Web.Razor.Text.TextChange)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定两个文本更改是否不相等。</summary>
      <returns>如果两个文本更改不相等，则为 true；否则为 false。</returns>
      <param name="left">左文本更改。</param>
      <param name="right">右文本更改。</param>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回文本更改的字符串表示形式。</summary>
      <returns>文本更改的字符串表示形式。</returns>
    </member>
    <member name="T:System.Web.Razor.Text.TextChangeType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Text.TextChangeType.Insert">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Text.TextChangeType.Remove">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Text.TextDocumentReader">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。提供文本文档的读取器。</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextDocumentReader.#ctor(System.Web.Razor.Text.ITextDocument)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Text.TextDocumentReader" /> 类的新实例。</summary>
      <param name="source">要读取的源。</param>
    </member>
    <member name="P:System.Web.Razor.Text.TextDocumentReader.Length">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取文档的长度。</summary>
      <returns>文档的长度。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextDocumentReader.Location">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取文档的位置。</summary>
      <returns>文档的位置。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.TextDocumentReader.Peek">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回要读取的下一个文本文档。</summary>
      <returns>要读取的下一个文本文档。</returns>
    </member>
    <member name="P:System.Web.Razor.Text.TextDocumentReader.Position">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置文本文档的位置。</summary>
      <returns>文本文档的位置。</returns>
    </member>
    <member name="M:System.Web.Razor.Text.TextDocumentReader.Read">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。读取指定的文本文档。</summary>
      <returns>文本文档。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.CSharpHelpers">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。提供 CSharp 标记器的帮助器功能。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.CSharpHelpers.IsIdentifierPart(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定字符是否可用于标识符。</summary>
      <returns>如果指定字符可用于标识符，则为 true；否则为 false。</returns>
      <param name="character">要检查的字符。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.CSharpHelpers.IsIdentifierStart(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定字符是否可用作标识符起始字符。</summary>
      <returns>如果指定字符可用作标识符起始字符，则为 true；否则为 false。</returns>
      <param name="character">要检查的字符。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.CSharpHelpers.IsRealLiteralSuffix(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定字符是否是实数的文本后缀。</summary>
      <returns>如果指定字符是实数的文本后缀，则为 true；否则为 false。</returns>
      <param name="character">要检查的字符。</param>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.CSharpTokenizer">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 CSharp 标记器。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.CSharpTokenizer.#ctor(System.Web.Razor.Text.ITextDocument)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.CSharpTokenizer" /> 类的新实例。</summary>
      <param name="source">源。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.CSharpTokenizer.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建 CSharp 标记器符号。</summary>
      <returns>CSharp 标记器符号。</returns>
      <param name="start">源位置的开始。</param>
      <param name="content">内容。</param>
      <param name="type">CSharp 符号类型。</param>
      <param name="errors">Razor 错误集合。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.CSharpTokenizer.RazorCommentStarType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType" /> 的星形类型。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType" /> 的星形类型。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.CSharpTokenizer.RazorCommentTransitionType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType" /> 的 Razor 注释过渡类型。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType" /> 的 Razor 注释过渡类型。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.CSharpTokenizer.RazorCommentType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType" /> 的 Razor 注释类型。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType" /> 的 Razor 注释类型。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.CSharpTokenizer.StartState">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取计算机的状态。</summary>
      <returns>计算机的状态。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.HtmlTokenizer">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Razor 的 html 标记器。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.HtmlTokenizer.#ctor(System.Web.Razor.Text.ITextDocument)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.HtmlTokenizer" /> 类的新实例。</summary>
      <param name="source">文本文档的源。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.HtmlTokenizer.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。为 html 标记器的指定参数创建符号。</summary>
      <returns>要为 html 标记器的指定参数创建的符号。</returns>
      <param name="start">源位置。</param>
      <param name="content">内容字符串。</param>
      <param name="type">html 符号的类型。</param>
      <param name="errors">Razor 错误。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.HtmlTokenizer.RazorCommentStarType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 Razor 注释星形类型的 html 符号。</summary>
      <returns>Razor 注释星形类型的 html 符号。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.HtmlTokenizer.RazorCommentTransitionType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 Razor 注释过渡类型的 html 符号。</summary>
      <returns>Razor 注释过渡类型的 html 符号。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.HtmlTokenizer.RazorCommentType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 Razor 注释类型的 html 符号。</summary>
      <returns>Razor 注释类型的 html 符号。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.HtmlTokenizer.StartState">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 html 计算机的起始状态。</summary>
      <returns>html 计算机的起始状态。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.ITokenizer">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.ITokenizer.NextSymbol">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Tokenizer`2">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
      <typeparam name="TSymbol">语言符号的类型。</typeparam>
      <typeparam name="TSymbolType">语言符号的枚举类型。</typeparam>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.#ctor(System.Web.Razor.Text.ITextDocument)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.Tokenizer`2" /> 类的新实例。</summary>
      <param name="source">源。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.AfterRazorCommentTransition">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回 Razor 注释过渡之后的结果。</summary>
      <returns>结果。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.At(System.String,System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定预期缓冲区是否包含预期的字符串。</summary>
      <returns>如果预期缓冲区包含预期的字符串，则为 true；否则为 false。</returns>
      <param name="expected">要检查的字符串。</param>
      <param name="caseSensitive">若指示比较区分大小写，则为 true；否则为 false。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.Buffer">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置标记器的缓冲区。</summary>
      <returns>标记器的缓冲区。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.CharOrWhiteSpace(System.Char)">
      <summary>返回一个函数委托，该委托接受字符参数并返回一个指示该字符参数是等于指定字符还是空格的值。</summary>
      <returns>函数委托。</returns>
      <param name="character">用于比较的字符。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,`1,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用指定内容创建标记器的语言符号类型。</summary>
      <returns>标记器的语言符号类型。</returns>
      <param name="start">源位置的开始。</param>
      <param name="content">内容值。</param>
      <param name="type">符号类型。</param>
      <param name="errors">Razor 错误。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.CurrentCharacter">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取标记器中的当前字符。</summary>
      <returns>当前字符。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.CurrentErrors">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取当前 Razor 错误的列表。</summary>
      <returns>当前错误的列表。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.CurrentLocation">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取当前源位置。</summary>
      <returns>当前源位置。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.CurrentStart">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取源位置的当前开始。</summary>
      <returns>源位置的当前开始。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.EndOfFile">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取指示标记器当前位置是否位于文件结尾的值。</summary>
      <returns>如果标记器当前位置位于文件结尾，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.EndSymbol(System.Web.Razor.Text.SourceLocation,`1)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回标记器使用的语言结束符号类型。</summary>
      <returns>语言结束符号类型。</returns>
      <param name="start">源位置的开始。</param>
      <param name="type">语言符号的枚举类型。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.EndSymbol(`1)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回标记器使用的语言结束符号类型。</summary>
      <returns>语言结束符号类型。</returns>
      <param name="type">语言符号的枚举类型。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.HaveContent">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取指示标记器是否具有内容的值。</summary>
      <returns>如果标记器具有内容，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.MoveNext">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。从代码读取器读取到下一个字符。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.NextSymbol">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。显示要使用的下一个符号。</summary>
      <returns>要使用的下一个符号。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.Peek">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。读取代码中的下一个符号。</summary>
      <returns>要读取的下一个符号。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.RazorCommentBody">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。分析 Razor 注释正文。</summary>
      <returns>表示结果状态的对象。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.RazorCommentStarType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 Razor 注释的星形类型。</summary>
      <returns>Razor 注释的星形类型。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.RazorCommentTransitionType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 Razor 注释的过渡类型。</summary>
      <returns>Razor 注释的过渡类型。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.RazorCommentType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 Razor 注释的类型。</summary>
      <returns>Razor 注释的类型。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.Reset">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将标记器状态设置为其初始状态。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.ResumeSymbol(`0)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。继续使用以前的语言符号类型。</summary>
      <param name="previous">以前的语言符号类型。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.Single(`1)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用单一符号类型。</summary>
      <returns>单一符号类型。</returns>
      <param name="type">符号的类型。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.Source">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取文本文档的源。</summary>
      <returns>源文档的源。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.StartSymbol">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此类中使用的起始符号。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.System#Web#Razor#Tokenizer#ITokenizer#NextSymbol">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回下一个语言符号类型。</summary>
      <returns>下一个语言符号类型。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.TakeAll(System.String,System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将在预期缓冲器中找到的字符串获取到标记器缓冲区中。</summary>
      <returns>如果预期缓冲区包含预期的字符串，则为 true；否则为 false。</returns>
      <param name="expected">要匹配的字符串。</param>
      <param name="caseSensitive">若指示比较区分大小写，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.TakeCurrent">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。接受当前字符进入缓冲区中。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.TakeString(System.String,System.Boolean)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。接受给定的输入字符串进入缓冲区中。</summary>
      <returns>如果接受整个输入字符串，则为 true；如果只接受子字符串，则为 false。</returns>
      <param name="input">输入字符串。</param>
      <param name="caseSensitive">若指示比较区分大小写，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.TakeUntil(System.Func{System.Char,System.Boolean})">
      <summary>分析源文档，直到满足谓词指定的条件或到达文件结尾。</summary>
      <returns>如果满足谓词条件，则为 true；如果到达文件结尾，则为 false。</returns>
      <param name="predicate">指定处理条件的谓词。</param>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.TokenizerView`3">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示标记器视图的指定参数。</summary>
      <typeparam name="TTokenizer">类型标记器。</typeparam>
      <typeparam name="TSymbol">类型符号。</typeparam>
      <typeparam name="TSymbolType">令牌符号类型。</typeparam>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.TokenizerView`3.#ctor(`0)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.TokenizerView`3" /> 类的新实例。</summary>
      <param name="tokenizer">标记器视图。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.TokenizerView`3.Current">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 TSymbol 的当前视图。</summary>
      <returns>TSymbol 的当前视图。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.TokenizerView`3.EndOfFile">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取指示视图是否可以达到文件结尾的值。</summary>
      <returns>如果视图可以达到文件结尾，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.TokenizerView`3.Next">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定标记器是否移至下一个视图。</summary>
      <returns>如果标记器移至下一个视图，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.TokenizerView`3.PutBack(`1)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将指定符号放入标记器视图中。</summary>
      <param name="symbol">符号。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.TokenizerView`3.Source">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取标记器视图的文本文档源。</summary>
      <returns>标记器视图的文本文档源。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.TokenizerView`3.Tokenizer">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取要查看 Razor 符号的标记器。</summary>
      <returns>要查看 Razor 符号的标记器。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.VBHelpers">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。将 VB 中的字符集表示为帮助器。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.VBHelpers.IsDoubleQuote(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示一个指示指定字符是否用双引号 (") 引起来的值。</summary>
      <returns>如果字符用双引号 (") 引起来，则为 true；否则为 false。</returns>
      <param name="character">字符。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.VBHelpers.IsOctalDigit(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示一个指示字符是否为八进制数字的值。</summary>
      <returns>如果字符为八进制数字，则为 true；否则为 false。</returns>
      <param name="character">字符。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.VBHelpers.IsSingleQuote(System.Char)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示一个指示指定字符是否用单引号 (') 引起来的值。</summary>
      <returns>如果字符用单引号 (') 引起来，则为 true；否则为 false。</returns>
      <param name="character">字符。</param>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.VBTokenizer">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。允许应用程序将 VB 符号分解成令牌。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.VBTokenizer.#ctor(System.Web.Razor.Text.ITextDocument)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.VBTokenizer" /> 类的新实例。</summary>
      <param name="source">文本的源。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.VBTokenizer.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。创建符号的域。</summary>
      <returns>符号的域。</returns>
      <param name="start">源位置。</param>
      <param name="content">内容值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType" />。</param>
      <param name="errors">Razor 错误。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.VBTokenizer.RazorCommentStarType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 VB 符号类型。</summary>
      <returns>VB 符号类型。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.VBTokenizer.RazorCommentTransitionType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 VB 符号的过渡样式。</summary>
      <returns>VB 符号的过渡样式。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.VBTokenizer.RazorCommentType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType" /> 的 Razor 类型注释。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType" /> 的 Razor 类型注释。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.VBTokenizer.StartState">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取计算机的起始状态。</summary>
      <returns>计算机的起始状态。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Abstract">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.As">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Base">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Bool">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Break">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Byte">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Case">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Catch">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Char">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Checked">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Class">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Const">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Continue">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Decimal">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Default">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Delegate">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Do">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Double">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Else">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Enum">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Event">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Explicit">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Extern">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.False">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Finally">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Fixed">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Float">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.For">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Foreach">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Goto">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.If">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Implicit">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.In">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Int">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Interface">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Internal">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Is">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Lock">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Long">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Namespace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.New">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Null">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Object">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Operator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Out">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Override">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Params">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Private">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Protected">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Public">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Readonly">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Ref">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Return">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Sbyte">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Sealed">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Short">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Sizeof">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Stackalloc">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Static">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.String">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Struct">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Switch">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.This">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Throw">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.True">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Try">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Typeof">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Uint">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Ulong">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Unchecked">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Unsafe">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Ushort">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Using">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Virtual">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Void">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Volatile">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.While">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Razor 标记器的 C sharp 符号。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol" /> 类的新实例。</summary>
      <param name="offset">符号的偏移量。</param>
      <param name="line">行。</param>
      <param name="column">列</param>
      <param name="content">符号的内容。</param>
      <param name="type">符号的类型。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol" /> 类的新实例。</summary>
      <param name="offset">符号的偏移量。</param>
      <param name="line">行。</param>
      <param name="column">列</param>
      <param name="content">符号的内容。</param>
      <param name="type">符号的类型。</param>
      <param name="errors">错误列表。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol" /> 类的新实例。</summary>
      <param name="start">符号开始的位置。</param>
      <param name="content">符号的内容。</param>
      <param name="type">符号的类型。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol" /> 类的新实例。</summary>
      <param name="start">符号开始的位置。</param>
      <param name="content">符号的内容。</param>
      <param name="type">符号的类型。</param>
      <param name="errors">错误列表。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于当前对象。</summary>
      <returns>如果指定的对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">要比较的对象。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.EscapedIdentifier">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置一个值，该值指示符号是否具有转义标识符。</summary>
      <returns>如果符号具有转义标识符，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此当前实例的哈希代码。</summary>
      <returns>此当前实例的哈希代码。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.Keyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置语言关键字。</summary>
      <returns>语言关键字。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.And">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.AndAssign">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Arrow">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Assign">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.CharacterLiteral">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Colon">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Comma">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Comment">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Decrement">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.DivideAssign">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Dot">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.DoubleAnd">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.DoubleColon">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.DoubleOr">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Equals">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.GreaterThan">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.GreaterThanEqual">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Hash">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Identifier">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Increment">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.IntegerLiteral">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Keyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LeftBrace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LeftBracket">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LeftParenthesis">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LeftShift">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LeftShiftAssign">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LessThan">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LessThanEqual">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Minus">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.MinusAssign">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Modulo">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.ModuloAssign">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.MultiplyAssign">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.NewLine">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Not">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.NotEqual">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.NullCoalesce">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Or">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.OrAssign">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Plus">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.PlusAssign">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.QuestionMark">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RazorComment">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RazorCommentStar">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RazorCommentTransition">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RealLiteral">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RightBrace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RightBracket">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RightParenthesis">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RightShift">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RightShiftAssign">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Semicolon">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Slash">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Star">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.StringLiteral">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Tilde">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Transition">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Unknown">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.WhiteSpace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Xor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.XorAssign">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Html 符号。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol" /> 类的新实例。</summary>
      <param name="offset">符号的位置。</param>
      <param name="line">找到符号的具体行。</param>
      <param name="column">找到符号的列号。</param>
      <param name="content">内容值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType" />。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol" /> 类的新实例。</summary>
      <param name="offset">符号的位置。</param>
      <param name="line">找到符号的具体行。</param>
      <param name="column">找到符号的列号。</param>
      <param name="content">内容值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType" />。</param>
      <param name="errors">Razor 错误。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol" /> 类的新实例。</summary>
      <param name="start">源位置的开始。</param>
      <param name="content">内容值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType" />。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol" /> 类的新实例。</summary>
      <param name="start">源位置的开始。</param>
      <param name="content">内容值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType" />。</param>
      <param name="errors">Razor 错误。</param>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Bang">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.CloseAngle">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Colon">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.DoubleHyphen">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.DoubleQuote">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Equals">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.LeftBracket">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.NewLine">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.OpenAngle">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.QuestionMark">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.RazorComment">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.RazorCommentStar">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.RazorCommentTransition">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.RightBracket">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.SingleQuote">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Solidus">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Text">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Transition">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Unknown">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.WhiteSpace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.ISymbol">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Web Razor 符号的界面。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.ISymbol.ChangeStart(System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。更改符号的位置。</summary>
      <param name="newStart">符号的新位置。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.ISymbol.Content">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取符号的内容。</summary>
      <returns>符号的内容。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.ISymbol.OffsetStart(System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示符号的起始偏移量。</summary>
      <param name="documentStart">文档开始的位置。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.ISymbol.Start">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取符号的位置。</summary>
      <returns>符号的位置。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.CommentBody">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.CommentStar">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.CommentStart">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.Identifier">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.Keyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.NewLine">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.Transition">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.Unknown">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.WhiteSpace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示符号的新实例。</summary>
      <typeparam name="TType">泛型类型。</typeparam>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.#ctor(System.Web.Razor.Text.SourceLocation,System.String,`0,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1" /> 类的新实例。</summary>
      <param name="start">源位置。</param>
      <param name="content">内容值。</param>
      <param name="type">类型。</param>
      <param name="errors">Razor 错误。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.ChangeStart(System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。更改计算机的开始。</summary>
      <param name="newStart">新的开始。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.Content">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1" /> 的内容。</summary>
      <returns>
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1" /> 的内容。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。确定指定对象是否等于当前对象。</summary>
      <returns>如果指定对象等于当前对象，则为 true；否则为 false。</returns>
      <param name="obj">对象。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.Errors">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 Razor 错误。</summary>
      <returns>Razor 错误。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。根据当前 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1" /> 对象检索哈希代码。</summary>
      <returns>当前 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1" /> 对象的哈希。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.OffsetStart(System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。开始源位置的时间偏移量。</summary>
      <param name="documentStart">文档开始。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.Start">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取源位置的起点。</summary>
      <returns>源位置的起点。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.ToString">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。生成当前 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1" /> 对象的字符串表示形式。</summary>
      <returns>当前 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1" /> 对象的字符串表示形式。</returns>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.Type">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取从基类型继承的类型。</summary>
      <returns>从基类型继承的类型。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 Web 标记器的符号扩展。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions.GetContent(System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol},System.Web.Razor.Text.SourceLocation)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取此 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions" /> 类的内容。</summary>
      <returns>此 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions" /> 类的内容。</returns>
      <param name="symbols">要提供的符号。</param>
      <param name="spanStart">跨度的起始索引。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions.GetContent(System.Web.Razor.Parser.SyntaxTree.SpanBuilder)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取此 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions" /> 类的内容。</summary>
      <returns>此 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions" /> 类的内容。</returns>
      <param name="span">与给定跨度的交集</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions.GetContent(System.Web.Razor.Parser.SyntaxTree.SpanBuilder,System.Func{System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol},System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol}})">
      <summary>获取此 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions" /> 类的内容。</summary>
      <returns>此 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions" /> 类的内容。</returns>
      <param name="span">与给定跨度的交集</param>
      <param name="filter">所选符号的列表。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions.GetContent(System.Web.Razor.Tokenizer.Symbols.ISymbol)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取此 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions" /> 类的内容。</summary>
      <returns>此 <see cref="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions" /> 类的内容。</returns>
      <param name="symbol">提供的符号。</param>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.VBKeyword">
      <summary>枚举 Visual Basic 关键字列表。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.AddHandler">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.AddressOf">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Alias">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.And">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.AndAlso">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.As">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Boolean">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ByRef">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Byte">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ByVal">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Call">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Case">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Catch">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CBool">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CByte">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CChar">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CDate">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CDbl">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CDec">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Char">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CInt">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Class">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CLng">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CObj">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Const">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Continue">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CSByte">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CShort">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CSng">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CStr">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CUInt">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CULng">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CUShort">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Date">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Decimal">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Declare">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Default">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Delegate">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Dim">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.DirectCast">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Do">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Double">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Each">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Else">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ElseIf">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.End">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.EndIf">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Enum">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Erase">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Error">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Event">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Exit">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.False">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Finally">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.For">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Friend">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Function">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Get">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.GetType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.GetXmlNamespace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Global">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.GoSub">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.GoTo">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Handles">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.If">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Implements">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Imports">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.In">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Inherits">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Integer">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Interface">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Is">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.IsNot">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Let">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Lib">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Like">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Long">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Loop">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Me">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Mod">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Module">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.MustInherit">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.MustOverride">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.MyBase">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.MyClass">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Namespace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Narrowing">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.New">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Next">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Not">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Nothing">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.NotInheritable">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.NotOverridable">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Object">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Of">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.On">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Operator">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Option">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Optional">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Or">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.OrElse">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Overloads">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Overridable">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Overrides">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ParamArray">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Partial">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Private">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Property">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Protected">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Public">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.RaiseEvent">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ReadOnly">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ReDim">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Rem">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.RemoveHandler">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Resume">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Return">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.SByte">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Select">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Set">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Shadows">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Shared">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Short">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Single">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Static">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Step">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Stop">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.String">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Structure">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Sub">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.SyncLock">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Then">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Throw">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.To">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.True">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Try">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.TryCast">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.TypeOf">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.UInteger">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ULong">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.UShort">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Using">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Variant">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Wend">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.When">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.While">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Widening">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.With">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.WithEvents">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.WriteOnly">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Xor">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.VBSymbol">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。表示 VB 符号组件。</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbol" /> 类的新实例。</summary>
      <param name="offset">偏移值。</param>
      <param name="line">行值。</param>
      <param name="column">列值。</param>
      <param name="content">内容字符串值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType" />。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbol" /> 类的新实例。</summary>
      <param name="offset">偏移值。</param>
      <param name="line">行值。</param>
      <param name="column">列值。</param>
      <param name="content">内容字符串值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType" />。</param>
      <param name="errors">Razor 错误列表。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbol" /> 类的新实例。</summary>
      <param name="start">源位置的开始。</param>
      <param name="content">内容字符串值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType" />。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。初始化 <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbol" /> 类的新实例。</summary>
      <param name="start">源位置的开始。</param>
      <param name="content">内容字符串值。</param>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType" />。</param>
      <param name="errors">Razor 错误列表。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.Equals(System.Object)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示一个值，该值指示当前对象是否等于新对象。</summary>
      <returns>如果当前对象等于新对象，则为 true；否则为 false。</returns>
      <param name="obj">要比较的对象。</param>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.GetHashCode">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回此实例的哈希代码。</summary>
      <returns>要返回的哈希代码。</returns>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.GetSample(System.Web.Razor.Tokenizer.Symbols.VBSymbolType)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取对象中的指定数据示例。</summary>
      <returns>对象中的指定数据示例。</returns>
      <param name="type">
        <see cref="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType" />。</param>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.VBSymbol.Keyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取或设置 VB 中使用的关键字。</summary>
      <returns>使用的关键字。</returns>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Add">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Bang">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.CharacterLiteral">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Colon">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Comma">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Comment">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Concatenation">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.DateLiteral">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Divide">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Dollar">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Dot">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Equal">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Exponentiation">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.FloatingPointLiteral">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.GreaterThan">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Hash">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Identifier">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.IntegerDivide">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.IntegerLiteral">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Keyword">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.LeftBrace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.LeftBracket">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.LeftParenthesis">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.LessThan">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.LineContinuation">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Multiply">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.NewLine">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.QuestionMark">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RazorComment">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RazorCommentStar">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RazorCommentTransition">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RightBrace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RightBracket">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RightParenthesis">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.StringLiteral">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Subtract">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Transition">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Unknown">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.WhiteSpace">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。</summary>
    </member>
  </members>
</doc>