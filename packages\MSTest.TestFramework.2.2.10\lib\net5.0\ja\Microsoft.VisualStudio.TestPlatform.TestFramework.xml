<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod">
            <summary>
            実行用の TestMethod。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestMethodName">
            <summary>
            テスト メソッドの名前を取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestClassName">
            <summary>
            テスト クラスの名前を取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ReturnType">
            <summary>
            テスト メソッドの戻り値の型を取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ParameterTypes">
            <summary>
            テスト メソッドのパラメーターを取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.MethodInfo">
            <summary>
            テスト メソッドの methodInfo を取得します。
            </summary>
            <remarks>
            This is just to retrieve additional information about the method.
            Do not directly invoke the method using MethodInfo. Use ITestMethod.Invoke instead.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.Invoke(System.Object[])">
            <summary>
            テスト メソッドを呼び出します。
            </summary>
            <param name="arguments">
            テスト メソッドに渡す引数。(データ ドリブンの場合など)
            </param>
            <returns>
            テスト メソッド呼び出しの結果。
            </returns>
            <remarks>
            This call handles asynchronous test methods as well.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAllAttributes(System.Boolean)">
            <summary>
            テスト メソッドのすべての属性を取得します。
            </summary>
            <param name="inherit">
            親クラスで定義されている属性が有効かどうか。
            </param>
            <returns>
            すべての属性。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAttributes``1(System.Boolean)">
            <summary>
            特定の型の属性を取得します。
            </summary>
            <typeparam name="AttributeType"> System.Attribute type. </typeparam>
            <param name="inherit">
            親クラスで定義されている属性が有効かどうか。
            </param>
            <returns>
            指定した種類の属性。
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Helper">
            <summary>
            ヘルパー。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNull(System.Object,System.String,System.String)">
            <summary>
            null でない確認パラメーター。
            </summary>
            <param name="param">
            パラメーター。
            </param>
            <param name="parameterName">
            パラメーター名。
            </param>
            <param name="message">
            メッセージ。
            </param>
            <exception cref="T:System.ArgumentNullException"> Throws argument null exception when parameter is null. </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNullOrEmpty(System.String,System.String,System.String)">
            <summary>
            null または空でない確認パラメーター。
            </summary>
            <param name="param">
            パラメーター。
            </param>
            <param name="parameterName">
            パラメーター名。
            </param>
            <param name="message">
            メッセージ。
            </param>
            <exception cref="T:System.ArgumentException"> Throws ArgumentException when parameter is null. </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod">
            <summary>
            データ ドリブン テストのデータ行にアクセスする方法の列挙型。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Sequential">
            <summary>
            行は順番に返されます。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random">
            <summary>
            行はランダムに返されます。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute">
            <summary>
            テスト メソッドのインライン データを定義する属性。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="data1"> データ オブジェクト。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object,System.Object[])">
            <summary>
            引数の配列を受け入れる <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="data1"> データ オブジェクト。 </param>
            <param name="moreData"> 追加のデータ。 </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.Data">
            <summary>
            テスト メソッドを呼び出すデータを取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.DisplayName">
            <summary>
            カスタマイズするために、テスト結果の表示名を取得または設定します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            <summary>
            assert inconclusive 例外。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String,System.Exception)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="msg"> メッセージ。 </param>
            <param name="ex"> 例外。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="msg"> メッセージ。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> クラスの新しいインスタンスを初期化します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException">
            <summary>
            InternalTestFailureException クラス。テスト ケースの内部エラーを示すために使用されます
            </summary>
            <remarks>
            This class is only added to preserve source compatibility with the V1 framework.
            For all practical purposes either use AssertFailedException/AssertInconclusiveException.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String,System.Exception)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="msg"> 例外メッセージ。 </param>
            <param name="ex"> 例外。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="msg"> 例外メッセージ。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> クラスの新しいインスタンスを初期化します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute">
            <summary>
            指定した型の例外を予期するよう指定する属性
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type)">
            <summary>
            予期される型を指定して、<see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> クラスの新しいインスタンスを初期化する
            </summary>
            <param name="exceptionType">予期される例外の型</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type,System.String)">
            <summary>
            予期される型と、テストで例外がスローされない場合に含めるメッセージとを指定して
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="exceptionType">予期される例外の型</param>
            <param name="noExceptionMessage">
            例外がスローされなかったことが原因でテストが失敗した場合に、テスト結果に含まれるメッセージ
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.ExceptionType">
            <summary>
            予期される例外の型を示す値を取得する
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.AllowDerivedTypes">
            <summary>
            予期される例外の型から派生した型を予期される型として使用できるかどうかを示す値を
            取得または設定する
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.NoExceptionMessage">
            <summary>
            例外がスローされなかったためにテストが失敗した場合にテスト結果に含めるメッセージを取得する
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.Verify(System.Exception)">
            <summary>
            単体テストでスローされる例外の型が予期される型であることを検証する
            </summary>
            <param name="exception">単体テストでスローされる例外</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute">
            <summary>
            単体テストからの例外を予期するように指定する属性の基底クラス
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor">
            <summary>
            既定の例外なしメッセージを指定して <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> クラスの新しいインスタンスを初期化する
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor(System.String)">
            <summary>
            例外なしメッセージを指定して <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> クラスの新しいインスタンスを初期化します
            </summary>
            <param name="noExceptionMessage">
            例外がスローされなかったことが原因でテストが失敗した場合に、
            テスト結果に含まれるメッセージ
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.NoExceptionMessage">
            <summary>
            例外がスローされなかったためにテストが失敗した場合にテスト結果に含めるメッセージを取得する
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.SpecifiedNoExceptionMessage">
            <summary>
            例外がスローされなかったためにテストが失敗した場合にテスト結果に含めるメッセージを取得する
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.GetDefaultNoExceptionMessage(System.String)">
            <summary>
            既定の例外なしメッセージを取得する
            </summary>
            <param name="expectedExceptionAttributeTypeName">ExpectedException 属性の型名</param>
            <returns>既定の例外なしメッセージ</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.Verify(System.Exception)">
            <summary>
            例外が予期されているかどうかを判断します。メソッドが戻る場合は、
            例外が予期されていたと解釈されます。メソッドが例外をスローする場合は、
            例外が予期されていなかったと解釈され、スローされた例外のメッセージが
            テスト結果に含められます。便宜上、<see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert"/> クラスを使用できます。
            <see cref="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive"/> が使用され、アサーションが失敗すると、
            テスト成果は [結果不確定] に設定されます。
            </summary>
            <param name="exception">単体テストでスローされる例外</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.RethrowIfAssertException(System.Exception)">
            <summary>
            AssertFailedException または AssertInconclusiveException である場合に、例外を再スローする
            </summary>
            <param name="exception">アサーション例外である場合に再スローされる例外</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper">
            <summary>
            このクラスは、ジェネリック型を使用する型の単体テストを実行するユーザーを支援するように設計されています。
            GenericParameterHelper は、次のようないくつかの共通ジェネリック型制約を
            満たしています:
            1. パブリックの既定のコンストラクター
            2. 共通インターフェイスを実装します: IComparable、IEnumerable
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor">
            <summary>
            C# ジェネリックの 'newable' 制約を満たす
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <remarks>
            This constructor initializes the Data property to a random value.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor(System.Int32)">
            <summary>
            Data プロパティをユーザー指定の値に初期化する <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> クラスの
            新しいインスタンスを初期化します。
            </summary>
            <param name="data">任意の整数値</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Data">
            <summary>
            データを取得または設定する
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Equals(System.Object)">
            <summary>
            2 つの GenericParameterHelper オブジェクトの値の比較を実行する
            </summary>
            <param name="obj">次との比較を実行するオブジェクト</param>
            <returns>オブジェクトの値が 'this' GenericParameterHelper オブジェクトと同じ値である場合は true。
            それ以外の場合は、false。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetHashCode">
            <summary>
            このオブジェクトのハッシュコードを返します。
            </summary>
            <returns>ハッシュ コード。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.CompareTo(System.Object)">
            <summary>
            2 つの <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> オブジェクトのデータを比較します。
            </summary>
            <param name="obj">比較対象のオブジェクト。</param>
            <returns>
            このインスタンスと値の相対値を示す符号付きの数値。
            </returns>
            <exception cref="T:System.NotSupportedException">
            Thrown when the object passed in is not an instance of <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetEnumerator">
            <summary>
            長さが Data プロパティから派生している IEnumerator オブジェクト
            を返します。
            </summary>
            <returns>IEnumerator オブジェクト</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Clone">
            <summary>
            現在のオブジェクトに相当する GenericParameterHelper 
            オブジェクトを返します。
            </summary>
            <returns>複製されたオブジェクト。</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger">
            <summary>
            ユーザーが診断用に単体テストからトレースをログ記録/書き込みできるようにします。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessageHandler">
            <summary>
            LogMessage のハンドラー。
            </summary>
            <param name="message">ログに記録するメッセージ。</param>
        </member>
        <member name="E:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.OnLogMessage">
            <summary>
            リッスンするイベント。単体テスト ライターがメッセージを書き込むときに発生します。
            主にアダプターによって消費されます。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessage(System.String,System.Object[])">
            <summary>
            テスト ライターがメッセージをログ記録するために呼び出す API。
            </summary>
            <param name="format">プレースホルダーを含む文字列形式。</param>
            <param name="args">プレースホルダーのパラメーター。</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute">
            <summary>
            TestCategory 属性。単体テストのカテゴリを指定するために使用されます。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute"/> クラスの新しいインスタンスを初期化し、カテゴリをテストに適用します。
            </summary>
            <param name="testCategory">
            テスト カテゴリ。
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.TestCategories">
            <summary>
            テストに適用されているテスト カテゴリを取得します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute">
            <summary>
            "Category" 属性の基底クラス
            </summary>
            <remarks>
            The reason for this attribute is to let the users create their own implementation of test categories.
            - test framework (discovery, etc) deals with TestCategoryBaseAttribute.
            - The reason that TestCategories property is a collection rather than a string,
              is to give more flexibility to the user. For instance the implementation may be based on enums for which the values can be OR'ed
              in which case it makes sense to have single attribute rather than multiple ones on the same test.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute"/> クラスの新しいインスタンスを初期化します。
            カテゴリをテストに適用します。TestCategories で返される文字列は
            テストをフィルター処理する /category コマンドで使用されます
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.TestCategories">
            <summary>
            テストに適用されているテスト カテゴリを取得します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            <summary>
            AssertFailedException クラス。テスト ケースのエラーを示すために使用されます
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String,System.Exception)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="msg"> メッセージ。 </param>
            <param name="ex"> 例外。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="msg"> メッセージ。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> クラスの新しいインスタンスを初期化します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert">
            <summary>
            単体テスト内のさまざまな条件をテストするヘルパー クラスの
            コレクション。テスト対象の条件を満たしていない場合は、
            例外がスローされます。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.That">
            <summary>
            Assert 機能の単一インスタンスを取得します。
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void IsOfType&lt;T&gt;(this Assert assert, object obj)"
            Users could then use a syntax similar to the default assertions which in this case is "Assert.That.IsOfType&lt;Dog&gt;(animal);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean)">
            <summary>
            指定した条件が true であるかどうかをテストして、条件が false の場合は
            例外をスローします。
            </summary>
            <param name="condition">
            テストで true であることが予期される条件。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String)">
            <summary>
            指定した条件が true であるかどうかをテストして、条件が false の場合は
            例外をスローします。
            </summary>
            <param name="condition">
            テストで true であることが予期される条件。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="condition"/>
            false の場合。メッセージはテスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String,System.Object[])">
            <summary>
            指定した条件が true であるかどうかをテストして、条件が false の場合は
            例外をスローします。
            </summary>
            <param name="condition">
            テストで true であることが予期される条件。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="condition"/>
            false の場合。メッセージはテスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean)">
            <summary>
            指定した条件が false であるかどうかをテストして、
            条件が true である場合は例外をスローします。
            </summary>
            <param name="condition">
            テストで false であると予期される条件。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String)">
            <summary>
            指定した条件が false であるかどうかをテストして、
            条件が true である場合は例外をスローします。
            </summary>
            <param name="condition">
            テストで false であると予期される条件。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="condition"/>
            true の場合。メッセージはテスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String,System.Object[])">
            <summary>
            指定した条件が false であるかどうかをテストして、
            条件が true である場合は例外をスローします。
            </summary>
            <param name="condition">
            テストで false であると予期される条件。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="condition"/>
            true の場合。メッセージはテスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object)">
            <summary>
            指定したオブジェクトが null であるかどうかをテストして、
            null でない場合は例外をスローします。
            </summary>
            <param name="value">
            テストで null であると予期されるオブジェクト。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String)">
            <summary>
            指定したオブジェクトが null であるかどうかをテストして、
            null でない場合は例外をスローします。
            </summary>
            <param name="value">
            テストで null であると予期されるオブジェクト。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="value"/>
            null でない場合。メッセージはテスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String,System.Object[])">
            <summary>
            指定したオブジェクトが null であるかどうかをテストして、
            null でない場合は例外をスローします。
            </summary>
            <param name="value">
            テストで null であると予期されるオブジェクト。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="value"/>
            null でない場合。メッセージはテスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object)">
            <summary>
            指定したオブジェクトが null 以外であるかどうかをテストして、
            null である場合は例外をスローします。
            </summary>
            <param name="value">
            テストで null 出ないと予期されるオブジェクト。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String)">
            <summary>
            指定したオブジェクトが null 以外であるかどうかをテストして、
            null である場合は例外をスローします。
            </summary>
            <param name="value">
            テストで null 出ないと予期されるオブジェクト。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="value"/>
            null である場合。メッセージはテスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String,System.Object[])">
            <summary>
            指定したオブジェクトが null 以外であるかどうかをテストして、
            null である場合は例外をスローします。
            </summary>
            <param name="value">
            テストで null 出ないと予期されるオブジェクト。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="value"/>
            null である場合。メッセージはテスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object)">
            <summary>
            指定した両方のオブジェクトが同じオブジェクトを参照するかどうかをテストして、
            2 つの入力が同じオブジェクトを参照しない場合は例外をスローします。
            </summary>
            <param name="expected">
            比較する最初のオブジェクト。これはテストで予期される値です。
            </param>
            <param name="actual">
            比較する 2 番目のオブジェクト。これはテストのコードで生成される値です。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String)">
            <summary>
            指定した両方のオブジェクトが同じオブジェクトを参照するかどうかをテストして、
            2 つの入力が同じオブジェクトを参照しない場合は例外をスローします。
            </summary>
            <param name="expected">
            比較する最初のオブジェクト。これはテストで予期される値です。
            </param>
            <param name="actual">
            比較する 2 番目のオブジェクト。これはテストのコードで生成される値です。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と同じではない場合 <paramref name="expected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            指定した両方のオブジェクトが同じオブジェクトを参照するかどうかをテストして、
            2 つの入力が同じオブジェクトを参照しない場合は例外をスローします。
            </summary>
            <param name="expected">
            比較する最初のオブジェクト。これはテストで予期される値です。
            </param>
            <param name="actual">
            比較する 2 番目のオブジェクト。これはテストのコードで生成される値です。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と同じではない場合 <paramref name="expected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object)">
            <summary>
             指定したオブジェクトが別のオブジェクトを参照するかどうかをテストして、
            2 つの入力が同じオブジェクトを参照する場合は例外をスローします。
            </summary>
            <param name="notExpected">
            比較する最初のオブジェクト。これはテストで次と一致しないと予期される
            値です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目のオブジェクト。これはテストのコードで生成される値です。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String)">
            <summary>
             指定したオブジェクトが別のオブジェクトを参照するかどうかをテストして、
            2 つの入力が同じオブジェクトを参照する場合は例外をスローします。
            </summary>
            <param name="notExpected">
            比較する最初のオブジェクト。これはテストで次と一致しないと予期される
            値です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目のオブジェクト。これはテストのコードで生成される値です。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            と同じである場合 <paramref name="notExpected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
             指定したオブジェクトが別のオブジェクトを参照するかどうかをテストして、
            2 つの入力が同じオブジェクトを参照する場合は例外をスローします。
            </summary>
            <param name="notExpected">
            比較する最初のオブジェクト。これはテストで次と一致しないと予期される
            値です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目のオブジェクト。これはテストのコードで生成される値です。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            と同じである場合 <paramref name="notExpected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0)">
            <summary>
            指定した値どうしが等しいかどうかをテストして、
            2 つの値が等しくない場合は例外をスローします。論理値が等しい場合であっても、異なる数値型は
            等しくないものとして処理されます。42L は 42 とは等しくありません。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            比較する最初の値。これはテストで予期される値です。
            </param>
            <param name="actual">
            比較する 2 番目の値。これはテストのコードで生成される値です。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String)">
            <summary>
            指定した値どうしが等しいかどうかをテストして、
            2 つの値が等しくない場合は例外をスローします。論理値が等しい場合であっても、異なる数値型は
            等しくないものとして処理されます。42L は 42 とは等しくありません。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            比較する最初の値。これはテストで予期される値です。
            </param>
            <param name="actual">
            比較する 2 番目の値。これはテストのコードで生成される値です。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しくない場合 <paramref name="expected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            指定した値どうしが等しいかどうかをテストして、
            2 つの値が等しくない場合は例外をスローします。論理値が等しい場合であっても、異なる数値型は
            等しくないものとして処理されます。42L は 42 とは等しくありません。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            比較する最初の値。これはテストで予期される値です。
            </param>
            <param name="actual">
            比較する 2 番目の値。これはテストのコードで生成される値です。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しくない場合 <paramref name="expected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0)">
            <summary>
            指定した値どうしが等しくないかどうかをテストして、
            2 つの値が等しい場合は例外をスローします。論理値が等しい場合であっても、異なる数値型は
            等しくないものとして処理されます。42L は 42 とは等しくありません。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            比較する最初の値。これはテストで次と一致しないと予期される
            値です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目の値。これはテストのコードで生成される値です。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String)">
            <summary>
            指定した値どうしが等しくないかどうかをテストして、
            2 つの値が等しい場合は例外をスローします。論理値が等しい場合であっても、異なる数値型は
            等しくないものとして処理されます。42L は 42 とは等しくありません。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            比較する最初の値。これはテストで次と一致しないと予期される
            値です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目の値。これはテストのコードで生成される値です。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しい場合 <paramref name="notExpected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            指定した値どうしが等しくないかどうかをテストして、
            2 つの値が等しい場合は例外をスローします。論理値が等しい場合であっても、異なる数値型は
            等しくないものとして処理されます。42L は 42 とは等しくありません。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            比較する最初の値。これはテストで次と一致しないと予期される
            値です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目の値。これはテストのコードで生成される値です。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しい場合 <paramref name="notExpected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object)">
            <summary>
            指定したオブジェクトどうしが等しいかどうかをテストして、
            2 つのオブジェクトが等しくない場合は例外をスローします。論理値が等しい場合であっても、異なる数値型は
            等しくないものとして処理されます。42L は 42 とは等しくありません。
            </summary>
            <param name="expected">
            比較する最初のオブジェクト。これはテストで予期されるオブジェクトです。
            </param>
            <param name="actual">
            比較する 2 番目のオブジェクト。これはテストのコードで生成されるオブジェクトです。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String)">
            <summary>
            指定したオブジェクトどうしが等しいかどうかをテストして、
            2 つのオブジェクトが等しくない場合は例外をスローします。論理値が等しい場合であっても、異なる数値型は
            等しくないものとして処理されます。42L は 42 とは等しくありません。
            </summary>
            <param name="expected">
            比較する最初のオブジェクト。これはテストで予期されるオブジェクトです。
            </param>
            <param name="actual">
            比較する 2 番目のオブジェクト。これはテストのコードで生成されるオブジェクトです。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しくない場合 <paramref name="expected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            指定したオブジェクトどうしが等しいかどうかをテストして、
            2 つのオブジェクトが等しくない場合は例外をスローします。論理値が等しい場合であっても、異なる数値型は
            等しくないものとして処理されます。42L は 42 とは等しくありません。
            </summary>
            <param name="expected">
            比較する最初のオブジェクト。これはテストで予期されるオブジェクトです。
            </param>
            <param name="actual">
            比較する 2 番目のオブジェクト。これはテストのコードで生成されるオブジェクトです。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しくない場合 <paramref name="expected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object)">
            <summary>
            指定したオブジェクトどうしが等しくないかどうかをテストして、
            2 つのオブジェクトが等しい場合は例外をスローします。論理値が等しい場合であっても、異なる数値型は
            等しくないものとして処理されます。42L は 42 とは等しくありません。
            </summary>
            <param name="notExpected">
            比較する最初のオブジェクト。これはテストで次と一致しないと予期される
            値です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目のオブジェクト。これはテストのコードで生成されるオブジェクトです。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String)">
            <summary>
            指定したオブジェクトどうしが等しくないかどうかをテストして、
            2 つのオブジェクトが等しい場合は例外をスローします。論理値が等しい場合であっても、異なる数値型は
            等しくないものとして処理されます。42L は 42 とは等しくありません。
            </summary>
            <param name="notExpected">
            比較する最初のオブジェクト。これはテストで次と一致しないと予期される
            値です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目のオブジェクト。これはテストのコードで生成されるオブジェクトです。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しい場合 <paramref name="notExpected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            指定したオブジェクトどうしが等しくないかどうかをテストして、
            2 つのオブジェクトが等しい場合は例外をスローします。論理値が等しい場合であっても、異なる数値型は
            等しくないものとして処理されます。42L は 42 とは等しくありません。
            </summary>
            <param name="notExpected">
            比較する最初のオブジェクト。これはテストで次と一致しないと予期される
            値です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目のオブジェクト。これはテストのコードで生成されるオブジェクトです。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しい場合 <paramref name="notExpected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single)">
            <summary>
            指定した浮動小数どうしが等しいかどうかをテストして、
            等しくない場合は例外をスローします。
            </summary>
            <param name="expected">
            比較する最初の浮動小数。これはテストで予期される浮動小数です。
            </param>
            <param name="actual">
            比較する 2 番目の浮動小数。これはテストのコードで生成される浮動小数です。
            </param>
            <param name="delta">
            必要な精度。次の場合にのみ、例外がスローされます
            <paramref name="actual"/> 次と異なる場合 <paramref name="expected"/>
            次の値を超える差異がある場合 <paramref name="delta"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            指定した浮動小数どうしが等しいかどうかをテストして、
            等しくない場合は例外をスローします。
            </summary>
            <param name="expected">
            比較する最初の浮動小数。これはテストで予期される浮動小数です。
            </param>
            <param name="actual">
            比較する 2 番目の浮動小数。これはテストのコードで生成される浮動小数です。
            </param>
            <param name="delta">
            必要な精度。次の場合にのみ、例外がスローされます
            <paramref name="actual"/> 次と異なる場合 <paramref name="expected"/>
            次の値を超える差異がある場合 <paramref name="delta"/>。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            と異なる <paramref name="expected"/> 次の値を超える差異がある場合
            <paramref name="delta"/>。メッセージはテスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            指定した浮動小数どうしが等しいかどうかをテストして、
            等しくない場合は例外をスローします。
            </summary>
            <param name="expected">
            比較する最初の浮動小数。これはテストで予期される浮動小数です。
            </param>
            <param name="actual">
            比較する 2 番目の浮動小数。これはテストのコードで生成される浮動小数です。
            </param>
            <param name="delta">
            必要な精度。次の場合にのみ、例外がスローされます
            <paramref name="actual"/> 次と異なる場合 <paramref name="expected"/>
            次の値を超える差異がある場合 <paramref name="delta"/>。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            と異なる <paramref name="expected"/> 次の値を超える差異がある場合
            <paramref name="delta"/>。メッセージはテスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single)">
            <summary>
            指定した浮動小数どうしが等しくないかどうかをテストして、
            等しい場合は例外をスローします。
            </summary>
            <param name="notExpected">
            比較する最初の浮動小数。これはテストで次と一致しないと予期される
            浮動小数です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目の浮動小数。これはテストのコードで生成される浮動小数です。
            </param>
            <param name="delta">
            必要な精度。次の場合にのみ、例外がスローされます
            <paramref name="actual"/> 次と異なる場合 <paramref name="notExpected"/>
            最大でも次の値の差異がある場合 <paramref name="delta"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            指定した浮動小数どうしが等しくないかどうかをテストして、
            等しい場合は例外をスローします。
            </summary>
            <param name="notExpected">
            比較する最初の浮動小数。これはテストで次と一致しないと予期される
            浮動小数です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目の浮動小数。これはテストのコードで生成される浮動小数です。
            </param>
            <param name="delta">
            必要な精度。次の場合にのみ、例外がスローされます
            <paramref name="actual"/> 次と異なる場合 <paramref name="notExpected"/>
            最大でも次の値の差異がある場合 <paramref name="delta"/>。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しい場合 <paramref name="notExpected"/> または次の値未満の差異がある場合
            <paramref name="delta"/>。メッセージはテスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            指定した浮動小数どうしが等しくないかどうかをテストして、
            等しい場合は例外をスローします。
            </summary>
            <param name="notExpected">
            比較する最初の浮動小数。これはテストで次と一致しないと予期される
            浮動小数です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目の浮動小数。これはテストのコードで生成される浮動小数です。
            </param>
            <param name="delta">
            必要な精度。次の場合にのみ、例外がスローされます
            <paramref name="actual"/> 次と異なる場合 <paramref name="notExpected"/>
            最大でも次の値の差異がある場合 <paramref name="delta"/>。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しい場合 <paramref name="notExpected"/> または次の値未満の差異がある場合
            <paramref name="delta"/>。メッセージはテスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double)">
            <summary>
            指定した倍精度浮動小数点数どうしが等しいかどうかをテストして、
            等しくない場合は例外をスローします。
            </summary>
            <param name="expected">
            比較する最初の倍精度浮動小数点型。これはテストで予期される倍精度浮動小数点型です。
            </param>
            <param name="actual">
            比較する 2 番目の倍精度浮動小数点型。これはテストのコードで生成される倍精度浮動小数点型です。
            </param>
            <param name="delta">
            必要な精度。次の場合にのみ、例外がスローされます
            <paramref name="actual"/> 次と異なる場合 <paramref name="expected"/>
            次の値を超える差異がある場合 <paramref name="delta"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            指定した倍精度浮動小数点数どうしが等しいかどうかをテストして、
            等しくない場合は例外をスローします。
            </summary>
            <param name="expected">
            比較する最初の倍精度浮動小数点型。これはテストで予期される倍精度浮動小数点型です。
            </param>
            <param name="actual">
            比較する 2 番目の倍精度浮動小数点型。これはテストのコードで生成される倍精度浮動小数点型です。
            </param>
            <param name="delta">
            必要な精度。次の場合にのみ、例外がスローされます
            <paramref name="actual"/> 次と異なる場合 <paramref name="expected"/>
            次の値を超える差異がある場合 <paramref name="delta"/>。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            と異なる <paramref name="expected"/> 次の値を超える差異がある場合
            <paramref name="delta"/>。メッセージはテスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            指定した倍精度浮動小数点数どうしが等しいかどうかをテストして、
            等しくない場合は例外をスローします。
            </summary>
            <param name="expected">
            比較する最初の倍精度浮動小数点型。これはテストで予期される倍精度浮動小数点型です。
            </param>
            <param name="actual">
            比較する 2 番目の倍精度浮動小数点型。これはテストのコードで生成される倍精度浮動小数点型です。
            </param>
            <param name="delta">
            必要な精度。次の場合にのみ、例外がスローされます
            <paramref name="actual"/> 次と異なる場合 <paramref name="expected"/>
            次の値を超える差異がある場合 <paramref name="delta"/>。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            と異なる <paramref name="expected"/> 次の値を超える差異がある場合
            <paramref name="delta"/>。メッセージはテスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double)">
            <summary>
            Tests whether the specified doubles are unequal and throws an exception
            if they are equal.
            </summary>
            <param name="notExpected">
            比較する最初の倍精度浮動小数点型。これはテストで次と一致しないと予期される
            倍精度浮動小数点型です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目の倍精度浮動小数点型。これはテストのコードで生成される倍精度浮動小数点型です。
            </param>
            <param name="delta">
            必要な精度。次の場合にのみ、例外がスローされます
            <paramref name="actual"/> 次と異なる場合 <paramref name="notExpected"/>
            最大でも次の値の差異がある場合 <paramref name="delta"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Tests whether the specified doubles are unequal and throws an exception
            if they are equal.
            </summary>
            <param name="notExpected">
            比較する最初の倍精度浮動小数点型。これはテストで次と一致しないと予期される
            倍精度浮動小数点型です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目の倍精度浮動小数点型。これはテストのコードで生成される倍精度浮動小数点型です。
            </param>
            <param name="delta">
            必要な精度。次の場合にのみ、例外がスローされます
            <paramref name="actual"/> 次と異なる場合 <paramref name="notExpected"/>
            最大でも次の値の差異がある場合 <paramref name="delta"/>。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しい場合 <paramref name="notExpected"/> または次の値未満の差異がある場合
            <paramref name="delta"/>。メッセージはテスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Tests whether the specified doubles are unequal and throws an exception
            if they are equal.
            </summary>
            <param name="notExpected">
            比較する最初の倍精度浮動小数点型。これはテストで次と一致しないと予期される
            倍精度浮動小数点型です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目の倍精度浮動小数点型。これはテストのコードで生成される倍精度浮動小数点型です。
            </param>
            <param name="delta">
            必要な精度。次の場合にのみ、例外がスローされます
            <paramref name="actual"/> 次と異なる場合 <paramref name="notExpected"/>
            最大でも次の値の差異がある場合 <paramref name="delta"/>。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しい場合 <paramref name="notExpected"/> または次の値未満の差異がある場合
            <paramref name="delta"/>。メッセージはテスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean)">
            <summary>
            指定した文字列が等しいかどうかをテストして、
            等しくない場合は例外をスローします。比較にはインバリアント カルチャが使用されます。
            </summary>
            <param name="expected">
            比較する最初の文字列。これはテストで予期される文字列です。
            </param>
            <param name="actual">
            比較する 2 番目の文字列。これはテストのコードで生成される文字列です。
            </param>
            <param name="ignoreCase">
            大文字と小文字を区別する比較か、大文字と小文字を区別しない比較かを示すブール値。(true
            は大文字と小文字を区別しない比較を示します。)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            指定した文字列が等しいかどうかをテストして、
            等しくない場合は例外をスローします。比較にはインバリアント カルチャが使用されます。
            </summary>
            <param name="expected">
            比較する最初の文字列。これはテストで予期される文字列です。
            </param>
            <param name="actual">
            比較する 2 番目の文字列。これはテストのコードで生成される文字列です。
            </param>
            <param name="ignoreCase">
            大文字と小文字を区別する比較か、大文字と小文字を区別しない比較かを示すブール値。(true
            は大文字と小文字を区別しない比較を示します。)
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しくない場合 <paramref name="expected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            指定した文字列が等しいかどうかをテストして、
            等しくない場合は例外をスローします。比較にはインバリアント カルチャが使用されます。
            </summary>
            <param name="expected">
            比較する最初の文字列。これはテストで予期される文字列です。
            </param>
            <param name="actual">
            比較する 2 番目の文字列。これはテストのコードで生成される文字列です。
            </param>
            <param name="ignoreCase">
            大文字と小文字を区別する比較か、大文字と小文字を区別しない比較かを示すブール値。(true
            は大文字と小文字を区別しない比較を示します。)
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しくない場合 <paramref name="expected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            指定した文字列が等しいかどうかをテストして、
            等しくない場合は例外をスローします。
            </summary>
            <param name="expected">
            比較する最初の文字列。これはテストで予期される文字列です。
            </param>
            <param name="actual">
            比較する 2 番目の文字列。これはテストのコードで生成される文字列です。
            </param>
            <param name="ignoreCase">
            大文字と小文字を区別する比較か、大文字と小文字を区別しない比較かを示すブール値。(true
            は大文字と小文字を区別しない比較を示します。)
            </param>
            <param name="culture">
            カルチャ固有の比較情報を提供する CultureInfo オブジェクト。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            指定した文字列が等しいかどうかをテストして、
            等しくない場合は例外をスローします。
            </summary>
            <param name="expected">
            比較する最初の文字列。これはテストで予期される文字列です。
            </param>
            <param name="actual">
            比較する 2 番目の文字列。これはテストのコードで生成される文字列です。
            </param>
            <param name="ignoreCase">
            大文字と小文字を区別する比較か、大文字と小文字を区別しない比較かを示すブール値。(true
            は大文字と小文字を区別しない比較を示します。)
            </param>
            <param name="culture">
            カルチャ固有の比較情報を提供する CultureInfo オブジェクト。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しくない場合 <paramref name="expected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            指定した文字列が等しいかどうかをテストして、
            等しくない場合は例外をスローします。
            </summary>
            <param name="expected">
            比較する最初の文字列。これはテストで予期される文字列です。
            </param>
            <param name="actual">
            比較する 2 番目の文字列。これはテストのコードで生成される文字列です。
            </param>
            <param name="ignoreCase">
            大文字と小文字を区別する比較か、大文字と小文字を区別しない比較かを示すブール値。(true
            は大文字と小文字を区別しない比較を示します。)
            </param>
            <param name="culture">
            カルチャ固有の比較情報を提供する CultureInfo オブジェクト。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しくない場合 <paramref name="expected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean)">
            <summary>
            指定した文字列が等しくないかどうかをテストして、
            等しい場合は例外をスローします。比較にはインバリアント カルチャが使用されます。
            </summary>
            <param name="notExpected">
            比較する最初の文字列。これはテストで次と一致しないと予期される
            文字列です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目の文字列。これはテストのコードで生成される文字列です。
            </param>
            <param name="ignoreCase">
            大文字と小文字を区別する比較か、大文字と小文字を区別しない比較かを示すブール値。(true
            は大文字と小文字を区別しない比較を示します。)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            指定した文字列が等しくないかどうかをテストして、
            等しい場合は例外をスローします。比較にはインバリアント カルチャが使用されます。
            </summary>
            <param name="notExpected">
            比較する最初の文字列。これはテストで次と一致しないと予期される
            文字列です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目の文字列。これはテストのコードで生成される文字列です。
            </param>
            <param name="ignoreCase">
            大文字と小文字を区別する比較か、大文字と小文字を区別しない比較かを示すブール値。(true
            は大文字と小文字を区別しない比較を示します。)
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しい場合 <paramref name="notExpected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            指定した文字列が等しくないかどうかをテストして、
            等しい場合は例外をスローします。比較にはインバリアント カルチャが使用されます。
            </summary>
            <param name="notExpected">
            比較する最初の文字列。これはテストで次と一致しないと予期される
            文字列です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目の文字列。これはテストのコードで生成される文字列です。
            </param>
            <param name="ignoreCase">
            大文字と小文字を区別する比較か、大文字と小文字を区別しない比較かを示すブール値。(true
            は大文字と小文字を区別しない比較を示します。)
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しい場合 <paramref name="notExpected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            指定した文字列が等しくないかどうかをテストして
            等しい場合は例外をスローします。
            </summary>
            <param name="notExpected">
            比較する最初の文字列。これはテストで次と一致しないと予期される
            文字列です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目の文字列。これはテストのコードで生成される文字列です。
            </param>
            <param name="ignoreCase">
            大文字と小文字を区別する比較か、大文字と小文字を区別しない比較かを示すブール値。(true
            は大文字と小文字を区別しない比較を示します。)
            </param>
            <param name="culture">
            カルチャ固有の比較情報を提供する CultureInfo オブジェクト。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            指定した文字列が等しくないかどうかをテストして
            等しい場合は例外をスローします。
            </summary>
            <param name="notExpected">
            比較する最初の文字列。これはテストで次と一致しないと予期される
            文字列です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目の文字列。これはテストのコードで生成される文字列です。
            </param>
            <param name="ignoreCase">
            大文字と小文字を区別する比較か、大文字と小文字を区別しない比較かを示すブール値。(true
            は大文字と小文字を区別しない比較を示します。)
            </param>
            <param name="culture">
            カルチャ固有の比較情報を提供する CultureInfo オブジェクト。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しい場合 <paramref name="notExpected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            指定した文字列が等しくないかどうかをテストして
            等しい場合は例外をスローします。
            </summary>
            <param name="notExpected">
            比較する最初の文字列。これはテストで次と一致しないと予期される
            文字列です <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目の文字列。これはテストのコードで生成される文字列です。
            </param>
            <param name="ignoreCase">
            大文字と小文字を区別する比較か、大文字と小文字を区別しない比較かを示すブール値。(true
            は大文字と小文字を区別しない比較を示します。)
            </param>
            <param name="culture">
            カルチャ固有の比較情報を提供する CultureInfo オブジェクト。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しい場合 <paramref name="notExpected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type)">
            <summary>
            指定したオブジェクトが予期した型のインスタンスであるかどうかをテストして、
            予期した型がオブジェクトの継承階層にない場合は
            例外をスローします。
            </summary>
            <param name="value">
            テストで特定の型であると予期されるオブジェクト。
            </param>
            <param name="expectedType">
            次の予期される型 <paramref name="value"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            指定したオブジェクトが予期した型のインスタンスであるかどうかをテストして、
            予期した型がオブジェクトの継承階層にない場合は
            例外をスローします。
            </summary>
            <param name="value">
            テストで特定の型であると予期されるオブジェクト。
            </param>
            <param name="expectedType">
            次の予期される型 <paramref name="value"/>。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="value"/>
            次のインスタンスではない場合 <paramref name="expectedType"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            指定したオブジェクトが予期した型のインスタンスであるかどうかをテストして、
            予期した型がオブジェクトの継承階層にない場合は
            例外をスローします。
            </summary>
            <param name="value">
            テストで特定の型であると予期されるオブジェクト。
            </param>
            <param name="expectedType">
            次の予期される型 <paramref name="value"/>。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="value"/>
            次のインスタンスではない場合 <paramref name="expectedType"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type)">
            <summary>
            指定したオブジェクトが間違った型のインスタンスでないかどうかをテストして、
            指定した型がオブジェクトの継承階層にある場合は
            例外をスローします。
            </summary>
            <param name="value">
            テストで特定の型でないと予期されるオブジェクト。
            </param>
            <param name="wrongType">
            次である型 <paramref name="value"/> 必要のない。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            指定したオブジェクトが間違った型のインスタンスでないかどうかをテストして、
            指定した型がオブジェクトの継承階層にある場合は
            例外をスローします。
            </summary>
            <param name="value">
            テストで特定の型でないと予期されるオブジェクト。
            </param>
            <param name="wrongType">
            次である型 <paramref name="value"/> 必要のない。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="value"/>
            次のインスタンスである場合 <paramref name="wrongType"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            指定したオブジェクトが間違った型のインスタンスでないかどうかをテストして、
            指定した型がオブジェクトの継承階層にある場合は
            例外をスローします。
            </summary>
            <param name="value">
            テストで特定の型でないと予期されるオブジェクト。
            </param>
            <param name="wrongType">
            次である型 <paramref name="value"/> 必要のない。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="value"/>
            次のインスタンスである場合 <paramref name="wrongType"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail">
            <summary>
            AssertFailedException をスローします。
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String)">
            <summary>
            AssertFailedException をスローします。
            </summary>
            <param name="message">
            例外に含まれるメッセージ。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String,System.Object[])">
            <summary>
            AssertFailedException をスローします。
            </summary>
            <param name="message">
            例外に含まれるメッセージ。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive">
            <summary>
            AssertInconclusiveException をスローします。
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String)">
            <summary>
            AssertInconclusiveException をスローします。
            </summary>
            <param name="message">
            例外に含まれるメッセージ。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String,System.Object[])">
            <summary>
            AssertInconclusiveException をスローします。
            </summary>
            <param name="message">
            例外に含まれるメッセージ。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Equals(System.Object,System.Object)">
            <summary>
            静的な Equals オーバーロードは、2 つの型のインスタンスを比較して参照の等価性を調べる
            ために使用されます。2 つのインスタンスを比較して等価性を調べるためにこのメソッドを使用
            することはでき<b>ません</b>。このオブジェクトは<b>常に</b> Assert.Fail を使用してスロー
            します。単体テストでは、Assert.AreEqual および関連するオーバーロードをご使用ください。
            </summary>
            <param name="objA"> オブジェクト A </param>
            <param name="objB"> オブジェクト B </param>
            <returns> 常に false。 </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action)">
            <summary>
            デリゲート <paramref name="action"/> によって指定されたコードが型 <typeparamref name="T"/> (派生型ではない) の指定されたとおりの例外をスローするかどうか、
            およびコードが例外をスローしない場合や <typeparamref name="T"/> 以外の型の例外をスローする場合に
            <code>
            AssertFailedException
            </code>
            をスローするかどうかをテストします。
            </summary>
            <param name="action">
            テスト対象であり、例外をスローすると予期されるコードにデリゲートします。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            スローされることが予期される例外の種類。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String)">
            <summary>
            デリゲート <paramref name="action"/> によって指定されたコードが型 <typeparamref name="T"/> (派生型ではない) の指定されたとおりの例外をスローするかどうか、
            およびコードが例外をスローしない場合や <typeparamref name="T"/> 以外の型の例外をスローする場合に
            <code>
            AssertFailedException
            </code>
            をスローするかどうかをテストします。
            </summary>
            <param name="action">
            テスト対象であり、例外をスローすると予期されるコードにデリゲートします。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="action"/>
            型の例外をスローしません <typeparamref name="T"/>。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            スローされることが予期される例外の種類。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object})">
            <summary>
            デリゲート <paramref name="action"/> によって指定されたコードが型 <typeparamref name="T"/> (派生型ではない) の指定されたとおりの例外をスローするかどうか、
            およびコードが例外をスローしない場合や <typeparamref name="T"/> 以外の型の例外をスローする場合に
            <code>
            AssertFailedException
            </code>
            をスローするかどうかをテストします。
            </summary>
            <param name="action">
            テスト対象であり、例外をスローすると予期されるコードにデリゲートします。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            スローされることが予期される例外の種類。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String)">
            <summary>
            デリゲート <paramref name="action"/> によって指定されたコードが型 <typeparamref name="T"/> (派生型ではない) の指定されたとおりの例外をスローするかどうか、
            およびコードが例外をスローしない場合や <typeparamref name="T"/> 以外の型の例外をスローする場合に
            <code>
            AssertFailedException
            </code>
            をスローするかどうかをテストします。
            </summary>
            <param name="action">
            テスト対象であり、例外をスローすると予期されるコードにデリゲートします。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="action"/>
            型の例外をスローしません <typeparamref name="T"/>。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            スローされることが予期される例外の種類。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String,System.Object[])">
            <summary>
            デリゲート <paramref name="action"/> によって指定されたコードが型 <typeparamref name="T"/> (派生型ではない) の指定されたとおりの例外をスローするかどうか、
            およびコードが例外をスローしない場合や <typeparamref name="T"/> 以外の型の例外をスローする場合に
            <code>
            AssertFailedException
            </code>
            をスローするかどうかをテストします。
            </summary>
            <param name="action">
            テスト対象であり、例外をスローすると予期されるコードにデリゲートします。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="action"/>
            型の例外をスローしません <typeparamref name="T"/>。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throw exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            スローされることが予期される例外の種類。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String,System.Object[])">
            <summary>
            デリゲート <paramref name="action"/> によって指定されたコードが型 <typeparamref name="T"/> (派生型ではない) の指定されたとおりの例外をスローするかどうか、
            およびコードが例外をスローしない場合や <typeparamref name="T"/> 以外の型の例外をスローする場合に
            <code>
            AssertFailedException
            </code>
            をスローするかどうかをテストします。
            </summary>
            <param name="action">
            テスト対象であり、例外をスローすると予期されるコードにデリゲートします。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="action"/>
            型の例外をスローしません <typeparamref name="T"/>。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            スローされることが予期される例外の種類。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task})">
            <summary>
            デリゲート <paramref name="action"/> によって指定されたコードが型 <typeparamref name="T"/> (派生型ではない) の指定されたとおりの例外をスローするかどうか、
            およびコードが例外をスローしない場合や <typeparamref name="T"/> 以外の型の例外をスローする場合に
            <code>
            AssertFailedException
            </code>
            をスローするかどうかをテストします。
            </summary>
            <param name="action">
            テスト対象であり、例外をスローすると予期されるコードにデリゲートします。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            その <see cref="T:System.Threading.Tasks.Task"/> (デリゲートを実行中)。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            デリゲート <paramref name="action"/> によって指定されたコードが型 <typeparamref name="T"/> (派生型ではない) の指定されたとおりの例外をスローするかどうか、
            およびコードが例外をスローしない場合や <typeparamref name="T"/> 以外の型の例外をスローする場合に <code>AssertFailedException</code> をスローするかどうかをテストします。
            </summary>
            <param name="action">テスト対象であり、例外をスローすると予期されるコードにデリゲートします。</param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="action"/>
            以下の型の例外をスローしない場合<typeparamref name="T"/>。
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            その <see cref="T:System.Threading.Tasks.Task"/> (デリゲートを実行中)。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String,System.Object[])">
            <summary>
            デリゲート <paramref name="action"/> によって指定されたコードが型 <typeparamref name="T"/> (派生型ではない) の指定されたとおりの例外をスローするかどうか、
            およびコードが例外をスローしない場合や <typeparamref name="T"/> 以外の型の例外をスローする場合に <code>AssertFailedException</code> をスローするかどうかをテストします。
            </summary>
            <param name="action">テスト対象であり、例外をスローすると予期されるコードにデリゲートします。</param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="action"/>
            以下の型の例外をスローしない場合<typeparamref name="T"/>。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            その <see cref="T:System.Threading.Tasks.Task"/> (デリゲートを実行中)。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNullChars(System.String)">
            <summary>
            null 文字 ('\0') を "\\0" に置き換えます。
            </summary>
            <param name="input">
            検索する文字列。
            </param>
            <returns>
            "\\0" で置き換えられた null 文字を含む変換された文字列。
            </returns>
            <remarks>
            This is only public and still present to preserve compatibility with the V1 framework.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.HandleFail(System.String,System.String,System.Object[])">
            <summary>
            AssertionFailedException を作成して、スローするヘルパー関数
            </summary>
            <param name="assertionName">
            例外をスローするアサーションの名前
            </param>
            <param name="message">
            アサーション エラーの条件を記述するメッセージ
            </param>
            <param name="parameters">
            パラメーター。
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.CheckParameterNotNull(System.Object,System.String,System.String,System.String,System.Object[])">
            <summary>
            有効な条件であるかパラメーターを確認します
            </summary>
            <param name="param">
            パラメーター。
            </param>
            <param name="assertionName">
            アサーション名。
            </param>
            <param name="parameterName">
            パラメーター名
            </param>
            <param name="message">
            無効なパラメーター例外のメッセージ
            </param>
            <param name="parameters">
            パラメーター。
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNulls(System.Object)">
            <summary>
            安全にオブジェクトを文字列に変換し、null 値と null 文字を処理します。
            null 値は "(null)" に変換されます。null 文字は "\\0" に変換されます。
            </summary>
            <param name="input">
            文字列に変換するオブジェクト。
            </param>
            <returns>
            変換された文字列。
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert">
            <summary>
            文字列のアサート。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.That">
            <summary>
            CollectionAssert 機能の単一インスタンスを取得します。
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void ContainsWords(this StringAssert cusomtAssert, string value, ICollection substrings)"
            Users could then use a syntax similar to the default assertions which in this case is "StringAssert.That.ContainsWords(value, substrings);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String)">
            <summary>
            指定した文字列に指定したサブ文字列が含まれているかどうかをテストして、
            テスト文字列内にサブ文字列が含まれていない場合は例外を
            スローします。
            </summary>
            <param name="value">
            次を含むと予期される文字列 <paramref name="substring"/>。
            </param>
            <param name="substring">
            次の内部で発生することが予期される文字列 <paramref name="value"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String)">
            <summary>
            指定した文字列に指定したサブ文字列が含まれているかどうかをテストして、
            テスト文字列内にサブ文字列が含まれていない場合は例外を
            スローします。
            </summary>
            <param name="value">
            次を含むと予期される文字列 <paramref name="substring"/>。
            </param>
            <param name="substring">
            次の内部で発生することが予期される文字列 <paramref name="value"/>。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="substring"/>
            次にない場合 <paramref name="value"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String,System.Object[])">
            <summary>
            指定した文字列に指定したサブ文字列が含まれているかどうかをテストして、
            テスト文字列内にサブ文字列が含まれていない場合は例外を
            スローします。
            </summary>
            <param name="value">
            次を含むと予期される文字列 <paramref name="substring"/>。
            </param>
            <param name="substring">
            次の内部で発生することが予期される文字列 <paramref name="value"/>。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="substring"/>
            次にない場合 <paramref name="value"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String)">
            <summary>
            指定した文字列の先頭が指定したサブ文字列であるかどうかをテストして
            テスト文字列の先頭がサブ文字列でない場合は
            例外をスローします。
            </summary>
            <param name="value">
            先頭が次であると予期される文字列 <paramref name="substring"/>。
            </param>
            <param name="substring">
            次のプレフィックスであると予期される文字列 <paramref name="value"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String)">
            <summary>
            指定した文字列の先頭が指定したサブ文字列であるかどうかをテストして
            テスト文字列の先頭がサブ文字列でない場合は
            例外をスローします。
            </summary>
            <param name="value">
            先頭が次であると予期される文字列 <paramref name="substring"/>。
            </param>
            <param name="substring">
            次のプレフィックスであると予期される文字列 <paramref name="value"/>。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="value"/>
            先頭が次ではない場合 <paramref name="substring"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            指定した文字列の先頭が指定したサブ文字列であるかどうかをテストして
            テスト文字列の先頭がサブ文字列でない場合は
            例外をスローします。
            </summary>
            <param name="value">
            先頭が次であると予期される文字列 <paramref name="substring"/>。
            </param>
            <param name="substring">
            次のプレフィックスであると予期される文字列 <paramref name="value"/>。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="value"/>
            先頭が次ではない場合 <paramref name="substring"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String)">
            <summary>
            指定した文字列の末尾が指定したサブ文字列であるかどうかをテストして、
            テスト文字列の末尾がサブ文字列でない場合は
            例外をスローします。
            </summary>
            <param name="value">
            末尾が次であることが予期される文字列 <paramref name="substring"/>。
            </param>
            <param name="substring">
            次のサフィックスであると予期される文字列 <paramref name="value"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String)">
            <summary>
            指定した文字列の末尾が指定したサブ文字列であるかどうかをテストして、
            テスト文字列の末尾がサブ文字列でない場合は
            例外をスローします。
            </summary>
            <param name="value">
            末尾が次であることが予期される文字列 <paramref name="substring"/>。
            </param>
            <param name="substring">
            次のサフィックスであると予期される文字列 <paramref name="value"/>。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="value"/>
            末尾が次ではない場合 <paramref name="substring"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            指定した文字列の末尾が指定したサブ文字列であるかどうかをテストして、
            テスト文字列の末尾がサブ文字列でない場合は
            例外をスローします。
            </summary>
            <param name="value">
            末尾が次であることが予期される文字列 <paramref name="substring"/>。
            </param>
            <param name="substring">
            次のサフィックスであると予期される文字列 <paramref name="value"/>。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="value"/>
            末尾が次ではない場合 <paramref name="substring"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            指定した文字列が正規表現と一致するかどうかをテストして、
            文字列が表現と一致しない場合は例外をスローします。
            </summary>
            <param name="value">
            次と一致すると予期される文字列 <paramref name="pattern"/>。
            </param>
            <param name="pattern">
            次である正規表現 <paramref name="value"/> is
            一致することが予期される。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            指定した文字列が正規表現と一致するかどうかをテストして、
            文字列が表現と一致しない場合は例外をスローします。
            </summary>
            <param name="value">
            次と一致すると予期される文字列 <paramref name="pattern"/>。
            </param>
            <param name="pattern">
            次である正規表現 <paramref name="value"/> is
            一致することが予期される。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="value"/>
            一致しない場合 <paramref name="pattern"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            指定した文字列が正規表現と一致するかどうかをテストして、
            文字列が表現と一致しない場合は例外をスローします。
            </summary>
            <param name="value">
            次と一致すると予期される文字列 <paramref name="pattern"/>。
            </param>
            <param name="pattern">
            次である正規表現 <paramref name="value"/> is
            一致することが予期される。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="value"/>
            一致しない場合 <paramref name="pattern"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            指定した文字列が正規表現と一致しないかどうかをテストして、
            文字列が表現と一致する場合は例外をスローします。
            </summary>
            <param name="value">
            次と一致しないと予期される文字列 <paramref name="pattern"/>。
            </param>
            <param name="pattern">
            次である正規表現 <paramref name="value"/> is
            一致しないと予期される。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            指定した文字列が正規表現と一致しないかどうかをテストして、
            文字列が表現と一致する場合は例外をスローします。
            </summary>
            <param name="value">
            次と一致しないと予期される文字列 <paramref name="pattern"/>。
            </param>
            <param name="pattern">
            次である正規表現 <paramref name="value"/> is
            一致しないと予期される。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="value"/>
            一致する場合 <paramref name="pattern"/>。メッセージはテスト結果に
            表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            指定した文字列が正規表現と一致しないかどうかをテストして、
            文字列が表現と一致する場合は例外をスローします。
            </summary>
            <param name="value">
            次と一致しないと予期される文字列 <paramref name="pattern"/>。
            </param>
            <param name="pattern">
            次である正規表現 <paramref name="value"/> is
            一致しないと予期される。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="value"/>
            一致する場合 <paramref name="pattern"/>。メッセージはテスト結果に
            表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert">
            <summary>
            単体テスト内のコレクションと関連付けられている
            さまざまな条件をテストするヘルパー クラスのコレクション。テスト対象の条件を満たしていない場合は、
            例外がスローされます。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.That">
            <summary>
            CollectionAssert 機能の単一インスタンスを取得します。
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void AreEqualUnordered(this CollectionAssert cusomtAssert, ICollection expected, ICollection actual)"
            Users could then use a syntax similar to the default assertions which in this case is "CollectionAssert.That.AreEqualUnordered(list1, list2);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object)">
            <summary>
            指定したコレクションに指定した要素が含まれているかどうかをテストして、
            要素がコレクションにない場合は例外をスローします。
            </summary>
            <param name="collection">
            要素を検索するコレクション。
            </param>
            <param name="element">
            コレクション内にあると予期される要素。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            指定したコレクションに指定した要素が含まれているかどうかをテストして、
            要素がコレクションにない場合は例外をスローします。
            </summary>
            <param name="collection">
            要素を検索するコレクション。
            </param>
            <param name="element">
            コレクション内にあると予期される要素。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="element"/>
            次にない場合 <paramref name="collection"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            指定したコレクションに指定した要素が含まれているかどうかをテストして、
            要素がコレクションにない場合は例外をスローします。
            </summary>
            <param name="collection">
            要素を検索するコレクション。
            </param>
            <param name="element">
            コレクション内にあると予期される要素。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="element"/>
            次にない場合 <paramref name="collection"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object)">
            <summary>
            指定したコレクションに指定した要素が含まれていないかどうかをテストして、
            要素がコレクション内にある場合は例外をスローします。
            </summary>
            <param name="collection">
            要素を検索するコレクション。
            </param>
            <param name="element">
            コレクション内に存在しないことが予期される要素。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            指定したコレクションに指定した要素が含まれていないかどうかをテストして、
            要素がコレクション内にある場合は例外をスローします。
            </summary>
            <param name="collection">
            要素を検索するコレクション。
            </param>
            <param name="element">
            コレクション内に存在しないことが予期される要素。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="element"/>
            が次にある場合 <paramref name="collection"/>。メッセージはテスト結果に
            表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            指定したコレクションに指定した要素が含まれていないかどうかをテストして、
            要素がコレクション内にある場合は例外をスローします。
            </summary>
            <param name="collection">
            要素を検索するコレクション。
            </param>
            <param name="element">
            コレクション内に存在しないことが予期される要素。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="element"/>
            が次にある場合 <paramref name="collection"/>。メッセージはテスト結果に
            表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection)">
            <summary>
            指定したコレクション内のすべてのアイテムが null 以外であるかどうかをテストして、
            いずれかの要素が null である場合は例外をスローします。
            </summary>
            <param name="collection">
            要素を検索するコレクション。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String)">
            <summary>
            指定したコレクション内のすべてのアイテムが null 以外であるかどうかをテストして、
            いずれかの要素が null である場合は例外をスローします。
            </summary>
            <param name="collection">
            要素を検索するコレクション。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="collection"/>
            null 要素を含む場合。メッセージはテスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            指定したコレクション内のすべてのアイテムが null 以外であるかどうかをテストして、
            いずれかの要素が null である場合は例外をスローします。
            </summary>
            <param name="collection">
            要素を検索するコレクション。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="collection"/>
            null 要素を含む場合。メッセージはテスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection)">
            <summary>
            指定したコレクション内のすべてのアイテムが一意であるかどうかをテストして、
            コレクション内のいずれかの 2 つの要素が等しい場合はスローします。
            </summary>
            <param name="collection">
            重複する要素を検索するコレクション。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String)">
            <summary>
            指定したコレクション内のすべてのアイテムが一意であるかどうかをテストして、
            コレクション内のいずれかの 2 つの要素が等しい場合はスローします。
            </summary>
            <param name="collection">
            重複する要素を検索するコレクション。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="collection"/>
            少なくとも 1 つの重複する要素が含まれています。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            指定したコレクション内のすべてのアイテムが一意であるかどうかをテストして、
            コレクション内のいずれかの 2 つの要素が等しい場合はスローします。
            </summary>
            <param name="collection">
            重複する要素を検索するコレクション。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="collection"/>
            少なくとも 1 つの重複する要素が含まれています。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            コレクションが別のコレクションのサブセットであるかどうかをテストして、
            スーパーセットにない要素がサブセットに入っている場合は
            例外をスローします。
            </summary>
            <param name="subset">
            次のサブセットであると予期されるコレクション <paramref name="superset"/>。
            </param>
            <param name="superset">
            次のスーパーセットであると予期されるコレクション <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            コレクションが別のコレクションのサブセットであるかどうかをテストして、
            スーパーセットにない要素がサブセットに入っている場合は
            例外をスローします。
            </summary>
            <param name="subset">
            次のサブセットであると予期されるコレクション <paramref name="superset"/>。
            </param>
            <param name="superset">
            次のスーパーセットであると予期されるコレクション <paramref name="subset"/>
            </param>
            <param name="message">
            次にある要素が次の条件である場合に、例外に含まれるメッセージ
            <paramref name="subset"/> 次に見つからない場合 <paramref name="superset"/>.
            メッセージはテスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            コレクションが別のコレクションのサブセットであるかどうかをテストして、
            スーパーセットにない要素がサブセットに入っている場合は
            例外をスローします。
            </summary>
            <param name="subset">
            次のサブセットであると予期されるコレクション <paramref name="superset"/>。
            </param>
            <param name="superset">
            次のスーパーセットであると予期されるコレクション <paramref name="subset"/>
            </param>
            <param name="message">
            次にある要素が次の条件である場合に、例外に含まれるメッセージ
            <paramref name="subset"/> 次に見つからない場合 <paramref name="superset"/>.
            メッセージはテスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            コレクションが別のコレクションのサブセットでないかどうかをテストして、
            サブセット内のすべての要素がスーパーセットにもある場合は
            例外をスローします。
            </summary>
            <param name="subset">
            のサブセットではないと予期されるコレクション <paramref name="superset"/>。
            </param>
            <param name="superset">
            次のスーパーセットであるとは予期されないコレクション <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            コレクションが別のコレクションのサブセットでないかどうかをテストして、
            サブセット内のすべての要素がスーパーセットにもある場合は
            例外をスローします。
            </summary>
            <param name="subset">
            のサブセットではないと予期されるコレクション <paramref name="superset"/>。
            </param>
            <param name="superset">
            次のスーパーセットであるとは予期されないコレクション <paramref name="subset"/>
            </param>
            <param name="message">
            次にあるすべての要素が次である場合に、例外に含まれるメッセージ
            <paramref name="subset"/> 次にもある場合 <paramref name="superset"/>.
            メッセージはテスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            コレクションが別のコレクションのサブセットでないかどうかをテストして、
            サブセット内のすべての要素がスーパーセットにもある場合は
            例外をスローします。
            </summary>
            <param name="subset">
            のサブセットではないと予期されるコレクション <paramref name="superset"/>。
            </param>
            <param name="superset">
            次のスーパーセットであるとは予期されないコレクション <paramref name="subset"/>
            </param>
            <param name="message">
            次にあるすべての要素が次である場合に、例外に含まれるメッセージ
            <paramref name="subset"/> 次にもある場合 <paramref name="superset"/>.
            メッセージはテスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            2 つのコレクションに同じ要素が含まれているかどうかをテストして、
            いずれかのコレクションにもう一方のコレクション内にない要素が含まれている場合は例外を
            スローします。
            </summary>
            <param name="expected">
            比較する最初のコレクション。これにはテストで予期される
            要素が含まれます。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで
            生成されるコレクションです。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            2 つのコレクションに同じ要素が含まれているかどうかをテストして、
            いずれかのコレクションにもう一方のコレクション内にない要素が含まれている場合は例外を
            スローします。
            </summary>
            <param name="expected">
            比較する最初のコレクション。これにはテストで予期される
            要素が含まれます。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで
            生成されるコレクションです。
            </param>
            <param name="message">
            要素が 2 つのコレクションのどちらかのみに見つかった場合に
            例外に含まれるメッセージ。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            2 つのコレクションに同じ要素が含まれているかどうかをテストして、
            いずれかのコレクションにもう一方のコレクション内にない要素が含まれている場合は例外を
            スローします。
            </summary>
            <param name="expected">
            比較する最初のコレクション。これにはテストで予期される
            要素が含まれます。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで
            生成されるコレクションです。
            </param>
            <param name="message">
            要素が 2 つのコレクションのどちらかのみに見つかった場合に
            例外に含まれるメッセージ。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            2 つのコレクションに異なる要素が含まれているかどうかをテストして、
            順番に関係なく、2 つのコレクションに同一の要素が含まれている場合は例外を
            スローします。
            </summary>
            <param name="expected">
            比較する最初のコレクション。これには実際のコレクションと異なると
            テストで予期される要素が含まれます。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで
            生成されるコレクションです。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            2 つのコレクションに異なる要素が含まれているかどうかをテストして、
            順番に関係なく、2 つのコレクションに同一の要素が含まれている場合は例外を
            スローします。
            </summary>
            <param name="expected">
            比較する最初のコレクション。これには実際のコレクションと異なると
            テストで予期される要素が含まれます。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで
            生成されるコレクションです。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と同じ要素を含む場合 <paramref name="expected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            2 つのコレクションに異なる要素が含まれているかどうかをテストして、
            順番に関係なく、2 つのコレクションに同一の要素が含まれている場合は例外を
            スローします。
            </summary>
            <param name="expected">
            比較する最初のコレクション。これには実際のコレクションと異なると
            テストで予期される要素が含まれます。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで
            生成されるコレクションです。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と同じ要素を含む場合 <paramref name="expected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type)">
            <summary>
            指定したコレクション内のすべての要素が指定した型のインスタンスであるかどうかをテストして、
            指定した型が 1 つ以上の要素
            の継承階層にない場合は例外をスローします。
            </summary>
            <param name="collection">
            テストで特定の型であると予期される要素を
            含むコレクション。
            </param>
            <param name="expectedType">
            次の各要素の予期される型 <paramref name="collection"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String)">
            <summary>
            指定したコレクション内のすべての要素が指定した型のインスタンスであるかどうかをテストして、
            指定した型が 1 つ以上の要素
            の継承階層にない場合は例外をスローします。
            </summary>
            <param name="collection">
            テストで特定の型であると予期される要素を
            含むコレクション。
            </param>
            <param name="expectedType">
            次の各要素の予期される型 <paramref name="collection"/>。
            </param>
            <param name="message">
            次にある要素が次の条件である場合に、例外に含まれるメッセージ
            <paramref name="collection"/> 次のインスタンスではない場合
            <paramref name="expectedType"/>。メッセージはテスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String,System.Object[])">
            <summary>
            指定したコレクション内のすべての要素が指定した型のインスタンスであるかどうかをテストして、
            指定した型が 1 つ以上の要素
            の継承階層にない場合は例外をスローします。
            </summary>
            <param name="collection">
            テストで特定の型であると予期される要素を
            含むコレクション。
            </param>
            <param name="expectedType">
            次の各要素の予期される型 <paramref name="collection"/>。
            </param>
            <param name="message">
            次にある要素が次の条件である場合に、例外に含まれるメッセージ
            <paramref name="collection"/> 次のインスタンスではない場合
            <paramref name="expectedType"/>。メッセージはテスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            指定したコレクションが等しいかどうかをテストして、
            2 つのコレクションが等しくない場合は例外をスローします。等値は、順序と数が同じである同じ要素を含むものとして
            定義されています。同じ値への異なる参照は
            等しいものとして見なされます。
            </summary>
            <param name="expected">
            比較する最初のコレクション。これはテストで予期されるコレクションです。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで生成される
            コレクションです。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            指定したコレクションが等しいかどうかをテストして、
            2 つのコレクションが等しくない場合は例外をスローします。等値は、順序と数が同じである同じ要素を含むものとして
            定義されています。同じ値への異なる参照は
            等しいものとして見なされます。
            </summary>
            <param name="expected">
            比較する最初のコレクション。これはテストで予期されるコレクションです。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで生成される
            コレクションです。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しくない場合 <paramref name="expected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            指定したコレクションが等しいかどうかをテストして、
            2 つのコレクションが等しくない場合は例外をスローします。等値は、順序と数が同じである同じ要素を含むものとして
            定義されています。同じ値への異なる参照は
            等しいものとして見なされます。
            </summary>
            <param name="expected">
            比較する最初のコレクション。これはテストで予期されるコレクションです。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで生成される
            コレクションです。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しくない場合 <paramref name="expected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            指定したコレクションが等しくないかどうかをテストして、
            2 つのコレクションが等しい場合は例外をスローします。等値は、順序と数が同じである同じ要素を含むものとして
            定義されています。同じ値への異なる参照は
            等しいものとして見なされます。
            </summary>
            <param name="notExpected">
            比較する最初のコレクション。これはテストで次と一致しないことが予期される
            コレクションです <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで生成される
            コレクションです。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            指定したコレクションが等しくないかどうかをテストして、
            2 つのコレクションが等しい場合は例外をスローします。等値は、順序と数が同じである同じ要素を含むものとして
            定義されています。同じ値への異なる参照は
            等しいものとして見なされます。
            </summary>
            <param name="notExpected">
            比較する最初のコレクション。これはテストで次と一致しないことが予期される
            コレクションです <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで生成される
            コレクションです。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しい場合 <paramref name="notExpected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            指定したコレクションが等しくないかどうかをテストして、
            2 つのコレクションが等しい場合は例外をスローします。等値は、順序と数が同じである同じ要素を含むものとして
            定義されています。同じ値への異なる参照は
            等しいものとして見なされます。
            </summary>
            <param name="notExpected">
            比較する最初のコレクション。これはテストで次と一致しないことが予期される
            コレクションです <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで生成される
            コレクションです。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しい場合 <paramref name="notExpected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            指定したコレクションが等しいかどうかをテストして、
            2 つのコレクションが等しくない場合は例外をスローします。等値は、順序と数が同じである同じ要素を含むものとして
            定義されています。同じ値への異なる参照は
            等しいものとして見なされます。
            </summary>
            <param name="expected">
            比較する最初のコレクション。これはテストで予期されるコレクションです。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで生成される
            コレクションです。
            </param>
            <param name="comparer">
            コレクションの要素を比較する場合に使用する比較の実装。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            指定したコレクションが等しいかどうかをテストして、
            2 つのコレクションが等しくない場合は例外をスローします。等値は、順序と数が同じである同じ要素を含むものとして
            定義されています。同じ値への異なる参照は
            等しいものとして見なされます。
            </summary>
            <param name="expected">
            比較する最初のコレクション。これはテストで予期されるコレクションです。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで生成される
            コレクションです。
            </param>
            <param name="comparer">
            コレクションの要素を比較する場合に使用する比較の実装。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しくない場合 <paramref name="expected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            指定したコレクションが等しいかどうかをテストして、
            2 つのコレクションが等しくない場合は例外をスローします。等値は、順序と数が同じである同じ要素を含むものとして
            定義されています。同じ値への異なる参照は
            等しいものとして見なされます。
            </summary>
            <param name="expected">
            比較する最初のコレクション。これはテストで予期されるコレクションです。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで生成される
            コレクションです。
            </param>
            <param name="comparer">
            コレクションの要素を比較する場合に使用する比較の実装。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しくない場合 <paramref name="expected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            指定したコレクションが等しくないかどうかをテストして、
            2 つのコレクションが等しい場合は例外をスローします。等値は、順序と数が同じである同じ要素を含むものとして
            定義されています。同じ値への異なる参照は
            等しいものとして見なされます。
            </summary>
            <param name="notExpected">
            比較する最初のコレクション。これはテストで次と一致しないことが予期される
            コレクションです <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで生成される
            コレクションです。
            </param>
            <param name="comparer">
            コレクションの要素を比較する場合に使用する比較の実装。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            指定したコレクションが等しくないかどうかをテストして、
            2 つのコレクションが等しい場合は例外をスローします。等値は、順序と数が同じである同じ要素を含むものとして
            定義されています。同じ値への異なる参照は
            等しいものとして見なされます。
            </summary>
            <param name="notExpected">
            比較する最初のコレクション。これはテストで次と一致しないことが予期される
            コレクションです <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで生成される
            コレクションです。
            </param>
            <param name="comparer">
            コレクションの要素を比較する場合に使用する比較の実装。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しい場合 <paramref name="notExpected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            指定したコレクションが等しくないかどうかをテストして、
            2 つのコレクションが等しい場合は例外をスローします。等値は、順序と数が同じである同じ要素を含むものとして
            定義されています。同じ値への異なる参照は
            等しいものとして見なされます。
            </summary>
            <param name="notExpected">
            比較する最初のコレクション。これはテストで次と一致しないことが予期される
            コレクションです <paramref name="actual"/>。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。これはテストのコードで生成される
            コレクションです。
            </param>
            <param name="comparer">
            コレクションの要素を比較する場合に使用する比較の実装。
            </param>
            <param name="message">
            次の場合に、例外に含まれるメッセージ <paramref name="actual"/>
            次と等しい場合 <paramref name="notExpected"/>。メッセージは
            テスト結果に表示されます。
            </param>
            <param name="parameters">
            の書式を設定する場合に使用するパラメーターの配列 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOfHelper(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            最初のコレクションが 2 番目のコレクションのサブセットであるかどうかを
            決定します。いずれかのセットに重複する要素が含まれている場合は、
            サブセット内の要素の出現回数は
            スーパーセット内の出現回数以下である必要があります。
            </summary>
            <param name="subset">
            テストで次に含まれると予期されるコレクション <paramref name="superset"/>。
            </param>
            <param name="superset">
            テストで次を含むと予期されるコレクション <paramref name="subset"/>。
            </param>
            <returns>
            次の場合は true <paramref name="subset"/> 次のサブセットの場合
            <paramref name="superset"/>、それ以外の場合は false。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.GetElementCounts(System.Collections.ICollection,System.Int32@)">
            <summary>
            指定したコレクションの各要素の出現回数を含む
            辞書を構築します。
            </summary>
            <param name="collection">
            処理するコレクション。
            </param>
            <param name="nullCount">
            コレクション内の null 要素の数。
            </param>
            <returns>
            指定したコレクション内の各要素の
            出現回数を含むディレクトリ。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.FindMismatchedElement(System.Collections.ICollection,System.Collections.ICollection,System.Int32@,System.Int32@,System.Object@)">
            <summary>
            2 つのコレクション間で一致しない要素を検索します。
            一致しない要素とは、予期されるコレクションでの出現回数が
            実際のコレクションでの出現回数と異なる要素のことです。
            コレクションは、同じ数の要素を持つ、null ではない
            さまざまな参照と見なされます。このレベルの検証を行う責任は
            呼び出し側にあります。一致しない要素がない場合、
            関数は false を返し、out パラメーターは使用されません。
            </summary>
            <param name="expected">
            比較する最初のコレクション。
            </param>
            <param name="actual">
            比較する 2 番目のコレクション。
            </param>
            <param name="expectedCount">
            次の予期される発生回数
            <paramref name="mismatchedElement"/> または一致しない要素がない場合は
            0 です。
            </param>
            <param name="actualCount">
            次の実際の発生回数
            <paramref name="mismatchedElement"/> または一致しない要素がない場合は
            0 です。
            </param>
            <param name="mismatchedElement">
            一致しない要素 (null の場合があります)、または一致しない要素がない場合は 
            null です。
            </param>
            <returns>
            一致しない要素が見つかった場合は true、それ以外の場合は false。
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.ObjectComparer">
            <summary>
            object.Equals を使用してオブジェクトを比較する
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException">
            <summary>
            フレームワーク例外の基底クラス。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> クラスの新しいインスタンスを初期化します。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String,System.Exception)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="msg"> メッセージ。 </param>
            <param name="ex"> 例外。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="msg"> メッセージ。 </param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages">
            <summary>
              ローカライズされた文字列などを検索するための、厳密に型指定されたリソース クラス。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ResourceManager">
            <summary>
              このクラスで使用されているキャッシュされた ResourceManager インスタンスを返します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Culture">
            <summary>
              厳密に型指定されたこのリソース クラスを使用して、現在のスレッドの
              CurrentUICulture プロパティをすべてのリソース ルックアップで無視します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AccessStringInvalidSyntax">
            <summary>
              "アクセス文字列は無効な構文を含んでいます。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ActualHasMismatchedElements">
            <summary>
              "予期されたコレクションでは、&lt;{2}&gt; が {1} 回発生します。実際のコレクションでは、{3} 回発生します。{0}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AllItemsAreUniqueFailMsg">
            <summary>
              "重複する項目が見つかりました:&lt;{1}&gt;。{0}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualCaseFailMsg">
            <summary>
              "&lt;{1}&gt; が必要です。実際の値: &lt;{2}&gt; では大文字と小文字が異なります。{0}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDeltaFailMsg">
            <summary>
              "指定する値 &lt;{1}&gt; と実際の値 &lt;{2}&gt; との間には &lt;{3}&gt; 以内の差が必要です。{0}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDifferentTypesFailMsg">
            <summary>
              "&lt;{1} ({2})&gt; が必要ですが、&lt;{3} ({4})&gt; が指定されました。{0}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualFailMsg">
            <summary>
              "&lt;{1}&gt; が必要ですが、&lt;{2}&gt; が指定されました。{0}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualDeltaFailMsg">
            <summary>
              "指定する値 &lt;{1}&gt; と実際の値 &lt;{2}&gt; との間には &lt;{3}&gt; を超える差が必要です。{0}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualFailMsg">
            <summary>
              "&lt;{1}&gt; 以外の任意の値が必要ですが、&lt;{2}&gt; が指定されています。{0}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreSameGivenValues">
            <summary>
              "AreSame() に値型を渡すことはできません。オブジェクトに変換された値は同じになりません。AreEqual() を使用することを検討してください。{0}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AssertionFailed">
            <summary>
              "{0} に失敗しました。{1}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AsyncUITestMethodNotSupported">
            <summary>
              "UITestMethodAttribute が指定された非同期の TestMethod はサポートされていません。非同期を削除するか、TestMethodAttribute を使用してください。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsEmpty">
            <summary>
              "両方のコレクションが空です。{0}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameElements">
            <summary>
              "両方のコレクションが同じ要素を含んでいます。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameReference">
            <summary>
              "両方のコレクションの参照が、同じコレクション オブジェクトにポイントしています。{0}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothSameElements">
            <summary>
              "両方のコレクションが同じ要素を含んでいます。{0}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.CollectionEqualReason">
            <summary>
              "{0}({1})" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_NullInMessages">
            <summary>
              "(null)" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_ObjectString">
            <summary>
              Looks up a localized string similar to (object).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ContainsFail">
            <summary>
              "文字列 '{0}' は文字列 '{1}' を含んでいません。{2}。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DataDrivenResultDisplayName">
            <summary>
              "{0} ({1})" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DoNotUseAssertEquals">
            <summary>
              "アサーションには Assert.Equals を使用せずに、Assert.AreEqual とオーバーロードを使用してください。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementNumbersDontMatch">
            <summary>
              "コレクション内の要素数が一致しません。&lt;{1}&gt; が必要ですが &lt;{2}&gt; が指定されています。{0}。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementsAtIndexDontMatch">
            <summary>
              "インデックス {0} の要素が一致しません。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch">
            <summary>
              "インデックス {1} の要素は、必要な型ではありません。&lt;{2}&gt; が必要ですが、&lt;{3}&gt; が指定されています。{0}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch2">
            <summary>
              "インデックス {1} の要素は null です。必要な型:&lt;{2}&gt;。{0}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EndsWithFail">
            <summary>
              "文字列 '{0}' は文字列 '{1}' で終わりません。{2}。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EqualsTesterInvalidArgs">
            <summary>
               "無効な引数 - EqualsTester は null を使用することはできません。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ErrorInvalidCast">
            <summary>
              "型 {0} のオブジェクトを {1} に変換できません。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InternalObjectNotValid">
            <summary>
              "参照された内部オブジェクトは、現在有効ではありません。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidParameterToAssert">
            <summary>
              "パラメーター '{0}' は無効です。{1}。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidPropertyType">
            <summary>
              "プロパティ {0} は型 {1} を含んでいますが、型 {2} が必要です。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsInstanceOfFailMsg">
            <summary>
              "{0} には型 &lt;{1}&gt; が必要ですが、型 &lt;{2}&gt; が指定されました。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsMatchFail">
            <summary>
              "文字列 '{0}' は、パターン '{1}' と一致しません。{2}。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotInstanceOfFailMsg">
            <summary>
              "正しくない型は &lt;{1}&gt; であり、実際の型は &lt;{2}&gt; です。{0}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotMatchFail">
            <summary>
              "文字列 '{0}' はパターン '{1}' と一致します。{2}。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoDataRow">
            <summary>
              "DataRowAttribute が指定されていません。DataTestMethodAttribute では少なくとも 1 つの DataRowAttribute が必要です。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoExceptionThrown">
            <summary>
              "例外がスローされませんでした。{1} の例外が予期されていました。{0}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NullParameterToAssert">
            <summary>
              "パラメーター '{0}' は無効です。値を null にすることはできません。{1}。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NumberOfElementsDiff">
            <summary>
              "要素数が異なります。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorConstructorNotFound">
            <summary>
              "指定されたシグネチャを使用するコンストラクターが見つかりませんでした。
                 プライベート アクセサーを再生成しなければならないか、
                 またはメンバーがプライベートであり、基底クラスで定義されている可能性があります。後者である場合、メンバーを
                 PrivateObject のコンストラクターに定義する型を渡す必要があります。" に類似したローカライズされた文字列を検索します。
               
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorMemberNotFound">
            <summary>
              "指定されたメンバー ({0}) が見つかりませんでした。プライベート アクセサーを再生成しなければならないか、
                 またはメンバーがプライベートであり、基底クラスで定義されている可能性があります。後者である場合、メンバーを
                 定義する型を PrivateObject のコンストラクターに渡す必要があります。" 
                 に類似したローカライズされた文字列を検索します。
               
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.StartsWithFail">
            <summary>
              "文字列 '{0}' は文字列 '{1}' で始まりません。{2}。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_ExpectedExceptionTypeMustDeriveFromException">
            <summary>
              "予期される例外の型は System.Exception または System.Exception の派生型である必要があります。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_FailedToGetExceptionMessage">
            <summary>
              "(例外が発生したため、型 {0} の例外のメッセージを取得できませんでした。)" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoException">
            <summary>
              "テスト メソッドは予期された例外 {0} をスローしませんでした。{1}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoExceptionDefault">
            <summary>
              "テスト メソッドは例外をスローしませんでした。テスト メソッドで定義されている属性 {0} で例外が予期されていました。" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongException">
            <summary>
              "テスト メソッドは、例外 {0} をスローしましたが、例外 {1} が予期されていました。例外メッセージ: {2}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongExceptionDerivedAllowed">
            <summary>
              "テスト メソッドは、例外 {0} をスローしましたが、例外 {1} またはその派生型が予期されていました。例外メッセージ: {2}" に類似したローカライズされた文字列を検索します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.WrongExceptionThrown">
             <summary>
               "例外 {2} がスローされましたが、例外 {1} が予期されていました。{0}
            例外メッセージ: {3}
            スタック トレース: {4}" に類似したローカライズされた文字列を検索します。
             </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome">
            <summary>
            単体テストの成果
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Failed">
            <summary>
            テストを実行しましたが、問題が発生しました。
            問題には例外または失敗したアサーションが関係している可能性があります。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Inconclusive">
            <summary>
            テストが完了しましたが、成功したか失敗したかは不明です。
            中止したテストに使用される場合があります。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Passed">
            <summary>
            問題なくテストが実行されました。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.InProgress">
            <summary>
            現在テストを実行しています。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Error">
            <summary>
            テストを実行しようとしているときにシステム エラーが発生しました。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Timeout">
            <summary>
            テストがタイムアウトしました。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Aborted">
            <summary>
            ユーザーによってテストが中止されました。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Unknown">
            <summary>
            テストは不明な状態です
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper">
            <summary>
            単体テストのフレームワークのヘルパー機能を提供する
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper.GetExceptionMsg(System.Exception)">
            <summary>
            すべての内部例外のメッセージなど、例外メッセージを
            再帰的に取得します
            </summary>
            <param name="ex">次のメッセージを取得する例外</param>
            <returns>エラー メッセージ情報を含む文字列</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> クラスで使用できるタイムアウトの列挙型。
            列挙型の型は一致している必要があります
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout.Infinite">
            <summary>
            無限。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute">
            <summary>
            テスト クラス属性。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute.GetTestMethodAttribute(Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute)">
            <summary>
            このテストの実行を可能するテスト メソッド属性を取得します。
            </summary>
            <param name="testMethodAttribute">このメソッドで定義されているテスト メソッド属性インスタンス。</param>
            <returns>The <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/>。このテストを実行するために使用されます。</returns>
            <remarks>Extensions can override this method to customize how all methods in a class are run.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute">
            <summary>
            テスト メソッド属性。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            テスト メソッドを実行します。
            </summary>
            <param name="testMethod">実行するテスト メソッド。</param>
            <returns>テストの結果を表す TestResult オブジェクトの配列。</returns>
            <remarks>Extensions can override this method to customize running a TestMethod.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestInitializeAttribute">
            <summary>
            テスト初期化属性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCleanupAttribute">
            <summary>
            テスト クリーンアップ属性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute">
            <summary>
            Ignore 属性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute">
            <summary>
            テストのプロパティ属性。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.#ctor(System.String,System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="name">
            名前。
            </param>
            <param name="value">
            値。
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Name">
            <summary>
            名前を取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Value">
            <summary>
            値を取得します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute">
            <summary>
            クラス初期化属性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute">
            <summary>
            クラス クリーンアップ属性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyInitializeAttribute">
            <summary>
            アセンブリ初期化属性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyCleanupAttribute">
            <summary>
            アセンブリ クリーンアップ属性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute">
            <summary>
            テストの所有者
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="owner">
            所有者。
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.Owner">
            <summary>
            所有者を取得します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute">
            <summary>
            優先順位属性。単体テストの優先順位を指定するために使用されます。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.#ctor(System.Int32)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="priority">
            優先順位。
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.Priority">
            <summary>
            優先順位を取得します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute">
            <summary>
            テストの説明
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.#ctor(System.String)">
            <summary>
            テストを記述する <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="description">説明。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.Description">
            <summary>
            テストの説明を取得します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute">
            <summary>
            CSS プロジェクト構造の URI
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.#ctor(System.String)">
            <summary>
            CSS プロジェクト構造の URI の <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="cssProjectStructure">CSS プロジェクト構造の URI。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.CssProjectStructure">
            <summary>
            CSS プロジェクト構造の URI を取得します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute">
            <summary>
            CSS イテレーション URI
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.#ctor(System.String)">
            <summary>
            CSS イテレーション URI の <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="cssIteration">CSS イテレーション URI。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.CssIteration">
            <summary>
            CSS イテレーション URI を取得します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute">
            <summary>
            WorkItem 属性。このテストに関連付けられている作業項目の指定に使用されます。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.#ctor(System.Int32)">
            <summary>
            WorkItem 属性の <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="id">作業項目に対する ID。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.Id">
            <summary>
            関連付けられている作業項目に対する ID を取得します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute">
            <summary>
            タイムアウト属性。単体テストのタイムアウトを指定するために使用されます。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(System.Int32)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="timeout">
            タイムアウト。
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout)">
            <summary>
            事前設定するタイムアウトを指定して <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> クラスの新しいインスタンスを初期化する
            </summary>
            <param name="timeout">
            タイムアウト
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.Timeout">
            <summary>
            タイムアウトを取得します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult">
            <summary>
            アダプターに返される TestResult オブジェクト。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/> クラスの新しいインスタンスを初期化します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DisplayName">
            <summary>
            結果の表示名を取得または設定します。複数の結果が返される場合に便利です。
            null の場合は、メソッド名が DisplayName として使用されます。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Outcome">
            <summary>
            テスト実行の成果を取得または設定します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestFailureException">
            <summary>
            テストが失敗した場合にスローされる例外を取得または設定します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogOutput">
            <summary>
            テスト コードでログに記録されたメッセージの出力を取得または設定します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogError">
            <summary>
            テスト コードでログに記録されたメッセージの出力を取得または設定します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DebugTrace">
            <summary>
            テスト コードでデバッグ トレースを取得または設定します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestContextMessages">
            <summary>
            Gets or sets the debug traces by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Duration">
            <summary>
            テスト実行の期間を取得または設定します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DatarowIndex">
            <summary>
            データ ソース内のデータ行インデックスを取得または設定します。データ ドリブン テストの一続きのデータ行の
            それぞれの結果に対してのみ設定されます。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ReturnValue">
            <summary>
            テスト メソッドの戻り値を取得または設定します。(現在は、常に null です)。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ResultFiles">
            <summary>
            テストで添付された結果ファイルを取得または設定します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute">
            <summary>
            データ ドリブン テストの接続文字列、テーブル名、行アクセス方法を指定します。
            </summary>
            <example>
            [DataSource("Provider=SQLOLEDB.1;Data Source=source;Integrated Security=SSPI;Initial Catalog=EqtCoverage;Persist Security Info=False", "MyTable")]
            [DataSource("dataSourceNameFromConfigFile")]
            </example>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultProviderName">
            <summary>
            DataSource の既定のプロバイダー名。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultDataAccessMethod">
            <summary>
            既定のデータ アクセス方法。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String,System.String,Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> クラスの新しいインスタンスを初期化します。このインスタンスは、データ ソースにアクセスするためのデータ プロバイダー、接続文字列、データ テーブル、データ アクセス方法を指定して初期化されます。
            </summary>
            <param name="providerInvariantName">System.Data.SqlClient などデータ プロバイダーの不変名</param>
            <param name="connectionString">
            データ プロバイダー固有の接続文字列。
            警告: 接続文字列には機微なデータ (パスワードなど) を含めることができます。
            接続文字列はソース コードのプレーンテキストとコンパイルされたアセンブリに保存されます。
            ソース コードとアセンブリへのアクセスを制限して、この秘匿性の高い情報を保護します。
            </param>
            <param name="tableName">データ テーブルの名前。</param>
            <param name="dataAccessMethod">データにアクセスする順番をしています。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> クラスの新しいインスタンスを初期化します。このインスタンスは接続文字列とテーブル名を指定して初期化されます。
            OLEDB データ ソースにアクセスするには接続文字列とデータ テーブルを指定します。
            </summary>
            <param name="connectionString">
            データ プロバイダー固有の接続文字列。
            警告: 接続文字列には機微なデータ (パスワードなど) を含めることができます。
            接続文字列はソース コードのプレーンテキストとコンパイルされたアセンブリに保存されます。
            ソース コードとアセンブリへのアクセスを制限して、この秘匿性の高い情報を保護します。
            </param>
            <param name="tableName">データ テーブルの名前。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> クラスの新しいインスタンスを初期化します。このインスタンスは設定名に関連付けられているデータ プロバイダーと接続文字列を使用して初期化されます。
            </summary>
            <param name="dataSourceSettingName">app.config ファイルの &lt;microsoft.visualstudio.qualitytools&gt; セクションにあるデータ ソースの名前。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ProviderInvariantName">
            <summary>
            データ ソースのデータ プロバイダーを表す値を取得します。
            </summary>
            <returns>
            データ プロバイダー名。データ プロバイダーがオブジェクトの初期化時に指定されていなかった場合は、System.Data.OleDb の既定のプロバイダーが返されます。
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ConnectionString">
            <summary>
            データ ソースの接続文字列を表す値を取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.TableName">
            <summary>
            データを提供するテーブル名を示す値を取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataAccessMethod">
             <summary>
             データ ソースへのアクセスに使用するメソッドを取得します。
             </summary>
            
             <returns>
             次のいずれか<see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod"/> 値。以下の場合 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> 初期化されていない場合は、これは既定値を返します <see cref="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random"/>。
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataSourceSettingName">
            <summary>
            app.config ファイルの &lt;microsoft.visualstudio.qualitytools&gt; セクションで見つかるデータ ソースの名前を取得します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute">
            <summary>
            データをインラインで指定できるデータ ドリブン テストの属性。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            すべてのデータ行を検索して、実行します。
            </summary>
            <param name="testMethod">
            テスト メソッド。
            </param>
            <returns>
            次の配列 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.RunDataDrivenTest(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod,Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute[])">
            <summary>
            データ ドリブン テスト メソッドを実行します。
            </summary>
            <param name="testMethod"> 実行するテスト メソッド。 </param>
            <param name="dataRows"> データ行. </param>
            <returns> 実行の結果。 </returns>
        </member>
    </members>
</doc>
