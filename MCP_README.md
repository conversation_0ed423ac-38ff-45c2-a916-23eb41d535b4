# MCP 智能工具调用功能

## 功能概述

MCP（Model Context Protocol）智能工具调用功能已成功集成到投资管理聊天系统中。该功能允许AI根据用户提问内容自动检索和调用个人权限内的项目信息，无需用户手动选择项目。

## 功能特点

### 🤖 智能分析
- AI自动分析用户问题，判断是否需要查询项目信息
- 支持多种项目相关问题类型识别
- **智能查询类型识别**: 自动区分"特定搜索"和"统计分析"
- 智能提取搜索关键词

### 🔍 自动检索
- **特定搜索**: 根据关键词精确匹配相关项目
- **统计分析**: 获取近期所有项目数据供AI分析
- 尊重用户权限，只检索有权限访问的项目
- 智能调整返回数量，统计分析获取更多数据

### 📊 无缝集成
- 无需用户手动操作，自动增强对话上下文
- 实时显示工具调用状态
- 与现有聊天功能完全兼容

## 使用方法

### 开启MCP功能
1. 在聊天界面底部找到"智能工具"按钮
2. 点击按钮开启MCP功能（按钮变为绿色表示已开启）
3. 正常提问即可，AI会自动判断是否需要调用工具

### 支持的问题类型

MCP工具会在检测到以下类型问题时自动激活：

1. **具体项目查询**
   - "XXX公司的项目怎么样？"
   - "帮我分析一下智能制造相关的项目"

2. **项目分析评估**
   - "哪些AI项目的风险较高？"
   - "对比一下新能源项目的表现"

3. **项目状态跟踪**
   - "本月有哪些项目需要重点关注？"
   - "最近的项目进展如何？"

4. **财务投资信息**
   - "估值变化较大的项目有哪些？"
   - "需要追加投资的项目"

5. **项目搜索筛选**
   - "找一下区块链相关的投资项目"
   - "医疗健康领域的项目统计"

6. **时间条件查询** ⭐ **新功能**
   - "本周创建的项目有哪些？"
   - "最近三个月的AI项目分析"
   - "今年的投资项目总结"
   - "2024年的新能源项目表现"

7. **统计分析查询** ⭐ **新功能**
   - "分析一下当前项目组合的行业分布情况"
   - "统计一下投资项目的阶段分布"
   - "分析项目组合的风险状况"
   - "评估整体投资组合的表现"

### 工具调用反馈

当MCP工具被激活时，聊天界面会显示：
- 🔍 智能检索状态信息
- 检索到的项目数量
- 工具调用成功/失败提示

## 技术实现

### 后端实现
- **ProcessMCPRequest**: 核心MCP处理方法
- **SearchProjectsForMCP**: 项目搜索接口
- **FormatProjectForMCP**: 项目信息格式化

### 前端实现
- **mcpEnabled**: MCP开关状态
- **mcpStatus**: 工具调用状态显示
- **tool_status**: SSE状态消息处理

### 工作流程
1. 用户输入问题
2. AI分析是否需要项目信息
3. **智能识别查询类型**（特定搜索 vs 统计分析）
4. 如需要，提取搜索关键词和时间条件
5. 解析时间条件为具体的日期范围
6. 根据查询类型调用相应的搜索策略：
   - 特定搜索：关键词匹配 + 时间过滤
   - 统计分析：时间过滤获取所有数据
7. 智能调整返回数据量（统计分析返回更多）
8. 格式化项目信息
9. 增强用户提示内容
10. 继续正常对话流程

### 时间条件解析 ⭐ **新功能**

MCP现在支持智能的时间条件解析，可以将自然语言的时间描述转换为具体的日期范围：

#### 支持的时间表达
- **周期性**: 本周、上周、本月、上月、今年、去年
- **相对时间**: 最近一个月、最近三个月、最近半年、最近一年
- **具体天数**: 近30天、近90天等
- **具体年份**: 2024年、2023年等

#### 时间解析示例
| 用户输入 | 解析结果 | 说明 |
|---------|---------|------|
| "本周的项目" | 本周一至今天 | 当前周的开始到现在 |
| "最近三个月" | 三个月前至今天 | 相对当前日期的三个月范围 |
| "2024年的投资" | 2024-01-01 至 2024-12-31 | 完整年份范围 |
| "上个月的新项目" | 上月1日至上月末日 | 完整的上个月范围 |

## 优势特点

### 🎯 智能准确
- AI驱动的问题分析，准确识别需求
- 基于权限的安全检索
- 智能关键词提取和匹配

### ⚡ 高效便捷
- 无需手动选择项目
- 自动化的信息检索和整合
- 实时状态反馈

### 🔒 安全可靠
- 严格遵循用户权限控制
- 错误处理和降级机制
- 不影响正常聊天功能

### 🔧 易于扩展
- 模块化设计，易于添加新的工具类型
- 标准化的MCP接口
- 灵活的配置选项

## 注意事项

1. **权限控制**: 只能访问当前用户有权限的项目
2. **性能考虑**: 限制检索项目数量，避免过多内容影响响应速度
3. **模型推荐**: 大量项目信息建议使用"qwen3-30b-a3b (极速)"模型
4. **错误处理**: MCP工具失败不会影响正常聊天功能
5. **时间精度**: 时间条件基于项目的发布时间(PubTime)进行筛选
6. **时间解析**: 无法识别的时间表达将被忽略，使用常规关键词搜索

## 未来扩展

可以考虑添加更多智能工具：
- 会议信息检索
- 文档搜索
- 数据分析工具
- 报告生成工具

---

*该功能基于Model Context Protocol (MCP)标准实现，为投资管理提供更智能的信息检索和分析能力。* 