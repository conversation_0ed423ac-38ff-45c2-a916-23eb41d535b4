using System;
using System.Collections.Generic;

namespace Banyan.Domain
{
    /// <summary>
    /// News recommendation result - used for caching and API responses only
    /// Not persisted to database
    /// </summary>
    [Serializable]
    public class NewsRecommendations
    {
        public int Id; // should remove
        public int UserId { get; set; }

        public int NewsId { get; set; }

        public double RelevanceScore { get; set; }

        public DateTime GeneratedTime { get; set; }

        public bool IsRead { get; set; }

        /// <summary>
        /// Whether the recommendation has been clicked by the user
        /// </summary>
        public bool IsClicked { get; set; }

        /// <summary>
        /// Associated news article (populated when needed)
        /// </summary>
        public List<News> News { get; set; }

        /// <summary>
        /// Associated user profile (populated when needed)
        /// </summary>
        public UserProfile UserProfile { get; set; }
    }
}