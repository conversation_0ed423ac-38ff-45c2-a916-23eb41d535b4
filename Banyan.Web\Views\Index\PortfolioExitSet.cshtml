﻿@using Banyan.Domain
@{
    Layout = "/Views/Shared/_Layout.cshtml";
    List<Banyan.Domain.Role> classList = (List<Banyan.Domain.Role>)ViewData["rolelist"];
    List<Banyan.Domain.Member> memberList = (List<Banyan.Domain.Member>)ViewData["creatorList"];
    var exitManagerList = (List<Member>)ViewData["exitManagerList"];
    int id = (int)ViewData["id"];
    var companyList = ViewData["CompanyList"] as List<string>;
    var preview = (bool)ViewData["preview"];
    //var pingLLM = (bool)ViewData["pingLLM"];
    var member = ViewData["manager"] as Banyan.Domain.Member;
    var fundList = (List<string>)ViewData["fundList"];
    var isOperate = member.Levels == (byte)Banyan.Domain.MemberLevels.Administrator || member.Levels == (byte)Banyan.Domain.MemberLevels.SuperUser;
    bool isEditor = (bool)ViewData["isEditor"] || id == 0;
}
<link href="~/content/css/views/meet/meet-edit.css" rel="stylesheet" />
<style>
    .text-detail{
        white-space: pre-line;
    }
</style>
<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li><a href="@(Url.Action("PortfolioExit", "Index"))">项目退出管理</a></li>
                @if (preview)
                {
                    <li>项目退出详情</li>
                }
                else
                {
                    <li>@((int)ViewData["id"] == 0 ? "新建" : "编辑")项目退出</li>
                }
            </ol>
        </div>
        <div class="block-content block-content-full" style="display:none;" id="page-app">
            <form class="form-horizontal" method="post" id="project-form" name="project-form">
                <input type="hidden" name="id" id="id" v-model="model.id" />
                <input type="hidden" name="portfolioID" value="model.portfolioID" />
                <input type="hidden" name="Creator" value="model.creator" />
                <input type="hidden" name="status" id="status" v-model="model.Status" />

                <vselect2single wide="true" type="text" id="Name" name="Name" v-model="model.Name" label="项目名称" elm="name">
                    @foreach (var c in companyList)
                    {
                        <option value='@c'>@c</option>
                    }
                </vselect2single>

                <vselect v-if="!!model.Name" wide="true" elm="dealID" v-model="model.dealID" label="选择对应退出交易(若无可不选)">
                    <option value="">-- 请选择此次对应退出交易 (请注意，非此前买入交易) --</option>
                    <option v-for="item in dealList" :value="item.ProjectID" :selected="item.ProjectID==model.dealID">
                        {{item.modifiedDateStr}} {{item.firstInvest}} {{item.abbName}} {{item.remark}} {{item.investFund}}   交易金额:{{item.investedAmount}} {{item.otherInvestor}}
                    </option>
                </vselect>
                <div class="form-group" v-if="model.discussContent">
                    <label class="col-md-2 control-label" for="ToRoleId">最新交易进度</label>
                    <div class="col-md-6 block-content tab-content block-content-mini">
                        {{model.discussType}} {{model.discussDateStr}} {{model.discussContent}}
                    </div>
                </div>
                <vselect2 wide="true" elm="exitFund" v-model="model.exitFund" name='exitFund' label="退出主体">
                    @foreach (var mi in fundList)
                    {
                        <option value="@(mi)">@(mi)</option>
                    }
                </vselect2>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="ToRoleId">项目组</label>
                    <div class="col-md-6">
                        <select class="form-control" id="group" size="1" data-rule="required;" v-model="model.ToRoleId">
                            @foreach (var ci in classList)
                            {
                                <option value="@(ci.Id)">@(ci.RoleName)</option>
                            }
                        </select>
                    </div>
                </div>

                @*<div v-if="!!model.Name" class="form-group">
            <label class="col-md-2 control-label" for="dealId">选择交易</label>
            <div class="col-md-6">
                <select class="form-control" id="dealId" size="1" v-model="model.dealID">
                    <option value="">-- 请选择交易 --</option>
                    <option v-for="item in dealList" :value="item">
                        {{item.createdDateStr}} {{item.firstInvest}} {{item.abbName}} {{item.remark}} {{item.investFund}}   交易金额:{{item.investedAmount}} {{item.otherInvestor}}
                    </option>
                </select>
            </div>
        </div>*@


                <vselect wide="true" elm="exitType" v-model="model.exitType" label="退出方式">
                    <option value="">--请选择--</option>
                    @*<option value="1">上市筹划中</option>*@
                    <option value="1">老股出售</option>
                    <option value="2">并购</option>
                    @*<option value="4">Ｓ基金</option>*@
                    <option value="3">回购、减资</option>
                    @*<option value="4">分红</option>*@
                    <option value="5">诉讼类项目</option>
                    <option value="6">清盘类项目</option>
                    <option value="7">上市公司股票出售</option>
                    <option value="8">转股退出</option>
                </vselect>

                @if (isEditor)
                {
                    <div class="form-group">
                        <label class="col-md-2 control-label" for="isprivate">是否私密（上市退出）</label>
                        <div class="col-md-2">
                            <select class="form-control" id="isprivate" v-model="model.IsPrivate" ssize="1">
                                <option value=false>否</option>
                                <option value=true>是（创建人或新增编辑人管理打分）</option>
                            </select>
                        </div>
                    </div>
                }
                <vselect2 wide="true" elm="exitManager" v-model="model.exitManager" label="退出负责人(可编辑)">
                    @foreach (var mi in exitManagerList)
                    {
                        <option value="@(mi.RealName)">@(mi.RealName)</option>
                    }
                </vselect2>
                <vselect2 wide="true" elm="exitMember" v-model="model.exitMember" label="退出成员(只读)">
                    @foreach (var mi in exitManagerList)
                    {
                        <option value="@(mi.RealName)">@(mi.RealName)</option>
                    }
                </vselect2>
                <vselect2 wide="true" elm="exitContributor" v-model="model.exitContributor" label="退出贡献人(不可读)">
                    @foreach (var mi in exitManagerList)
                    {
                        <option value="@(mi.RealName)">@(mi.RealName)</option>
                    }
                </vselect2>
                <vselect2 wide="true" elm="viewer" v-model="model.viewer" label="新增只读权限">
                    @foreach (var mi in exitManagerList)
                    {
                        <option value="@(mi.RealName)">@(mi.RealName)</option>
                    }
                </vselect2>
                <vselect2 wide="true" elm="editor" v-model="model.editor" label="新增可编辑权限">
                    @foreach (var mi in exitManagerList)
                    {
                        <option value="@(mi.RealName)">@(mi.RealName)</option>
                    }
                </vselect2>
                <vtextarea v-model="model.exitContribution" wide="true" label="具体贡献内容"> </vtextarea>
                <vinput v-model="model.exitAmount" wide="true" label="退出金额(千分位格式加币种)"> </vinput>
                <vinput v-model="model.exitCost" wide="true" label="退出成本(千分位格式加币种)"> </vinput>
                <vinput v-model="model.exitRatio" wide="true" label="退出部分倍数"> </vinput>
                @if (preview)
                {
                    <div class="form-group">
                        <label class="col-md-2 control-label">投前团队意见</label>
                        <div class="col-md-6">
                            <div class="text-detail">{{model.investOpinion}} </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-2 control-label">退出安排</label>
                        <div class="col-md-6">
                            <div class="text-detail">{{model.exitPlan}} </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-2 control-label">法务退出意见</label>
                        <div class="col-md-6">
                            <div class="text-detail">{{model.legalOpinion}} </div>
                        </div>
                    </div>
                }
                else
                {
                    <vtextarea v-model="model.investOpinion" wide="true" label="投前团队意见"></vtextarea>
                    <vtextarea v-model="model.exitPlan" rows="6" wide="true" label="退出安排"> </vtextarea>
                    <vtextarea v-model="model.legalOpinion" rows="6" wide="true" label="法务退出意见"></vtextarea>
                }


                <vtextarea v-model="model.remark" wide="true" label="备注"> </vtextarea>
                <vselect wide="true" elm="exitStatus" v-model="model.exitStatus" label="退出是否完成">
                    <option value="0"> -- 待选择（打分通过后需选择） -- </option>
                    <option value="1">已按计划完成</option>
                    <option value="2">未按计划完成</option>
                </vselect>
                <vtextarea name="exitStatusExplain" v-model="model.exitStatusExplain" v-if="model.exitStatus == 2" wide="true" label="未按计划完成情况说明"></vtextarea>

                <div class="form-group">
                    @if (!preview)
                    {
                        <div class="block-content tab-content block-content-mini">
                            <label class="col-md-2 control-label" for="NextStep">文件上传 ></label>
                            <div class="col-md-6 alert alert-info alert-dismissable">
                                供IC讨论的退出资料作为附件上传/留痕，内容包括但不限于:投资协议中我方权利、此次交易方案谈判过程关键点描述、最终交易方案、合规意见等
                                <p>支持文件格式：<a class="alert-link" href="javascript:void(0)">doc</a>、<a class="alert-link" href="javascript:void(0)">docx</a>、<a class="alert-link" href="javascript:void(0)">ppt</a>、<a class="alert-link" href="javascript:void(0)">pptx</a>和<a class="alert-link" href="javascript:void(0)">pdf</a>，支持图片格式：<a class="alert-link" href="javascript:void(0)">gif</a>,<a class="alert-link" href="javascript:void(0)">jpg</a>,<a class="alert-link" href="javascript:void(0)">jpeg</a>,<a class="alert-link" href="javascript:void(0)">png</a>,<a class="alert-link" href="javascript:void(0)">bmp</a>，<a class="alert-link" href="javascript:void(0)">excel</a>文件请在下面单独上传；如遇ppt文件转换图片变形，建议在本地转换成pdf文件再上传，文件大小不超过20M。</p>
                            </div>
                        </div>

                        <label class="col-md-2 control-label" for="NextStep2">上传资料</label>


                        <div class="block-content tab-content block-content-mini">
                            <file2picbtn :id="id"
                                         converturl="/adminapi/docconvert"
                                         savedocurl="/adminapi/addportfolioexitdoc"
                                         removeurl="/adminapi/delportfolioexitdoc"
                                         v-bind:filelist.sync="dataListEXIT"
                                         v-bind:imglist.sync="EXIT"
                                         type="EXIT"
                                         elm="docfile-dd">

                            </file2picbtn>
                        </div>
                    }
                    else
                    {
                        <label class="col-md-2 control-label" for="NextStep2">上传资料</label>
                        <div class="block-content tab-content block-content-mini">
                            <div v-for="(itemDD, idxDD) in dataListEXIT" class="demo-gallery">
                                <span>{{EXIT[idxDD].AtName}} </span>
                                <br />
                                <ul class="lightgalleryDD list-unstyled row">
                                    <li class="col-xs-6 col-sm-4 col-md-3" v-for="(item, index) in itemDD"
                                        data-responsive="" :data-src="item" data-sub-html="">
                                        <a href="javascript:;">
                                            <img class="img-responsive" :src="item">
                                            <div class="demo-gallery-poster">
                                                <img src="/content/js/plugins/lightgallery/img/zoom.png" />
                                            </div>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    }


                    <label class="col-md-2 control-label" for="NextStep3">Excel数据文件</label>
                    <div class="block-content tab-content block-content-mini">
                        @if (preview)
                        {
                            <div v-if="fileList && fileList.length > 0">
                                <div v-for="(item,index) in fileList" style="width:100%;">
                                    <span> <a :href="item.AtUrl">{{ item.AtName }}</a> </span>
                                </div>
                            </div>
                        }
                        else
                        { <fileuploadbtn :id="id"
                                         removeurl="/adminapi/delportfolioexitdoc"
                                         savedocurl="/adminapi/addportfolioexitdoc"
                                         v-bind:filelist.sync="fileList" elm="filesource">
                            </fileuploadbtn>
                        }
                    </div>


                </div>
                @if (!preview)
                {
                    <div class="form-group">
                        <div class="col-md-8 col-md-offset-2">
                            <button class="btn btn-info" @@click="saveData()" type="button"><i class="fa fa-save push-5-r"></i>保 存</button>
                            <button class="btn btn-minw btn-warning" @@click="saveData(-1)" type="button">取消并返回</button>
                        </div>
                    </div>
                }
            </form>
        </div>
    </div>
</div>

@section scripts{
    <script src="/content/js/plugins/jquery.ui.widget.js"></script>
    <script src="/content/js/plugins/jquery.iframe-transport.js"></script>
    <script src="/content/js/plugins/jquery.fileupload.js"></script>

    <script src="~/content/js/component/fileuploadbtn.js"></script>

    <link href="/content/js/plugins/select2/select2.min.css" rel="stylesheet" />
    <link href="/content/js/plugins/select2/select2-bootstrap.min.css" rel="stylesheet" />
    <script type="text/javascript" src="/content/js/plugins/select2/select2.min.js"></script>
    <script src="/content/js/core/ajaxfileupload.js"></script>
    <link href="~/Content/js/plugins/lightgallery/css/normalize.css" rel="stylesheet" />
    <link href="~/Content/js/plugins/lightgallery/css/lightgallery.min.css" rel="stylesheet" />

    <script src="~/Content/js/plugins/lightgallery/js/picturefill.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lightgallery.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-fullscreen.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-thumbnail.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-video.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-autoplay.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-zoom.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-hash.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-pager.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/jquery.mousewheel.min.js"></script>
    <script src="~/Content/js/form-component.js"></script>
    <script type="text/javascript">
        function getParameter(el) {
            var obj = {};
            $(el).each(function (index, item) {
                // 判断元素的类型
                if (item.type == "radio") {
                    // 获取到单选框选中的值
                    var radio_val = $("input[name=" + $(item).attr('name') + "]:checked").val();
                    if (radio_val) {
                        obj[$(item).attr("name")] = radio_val;
                    }
                }
            });
            return obj;
        }


    </script>
    <script type="text/javascript">
         @if (!preview)
         {
            <text> window.onbeforeunload = function (e) {
                 e.returnValue = '有未保存的修改'
                 return "有未保存的修改"
             }</text>
         }

        var layer, laydate, isdebug = false;
        layui.use(['layer', 'laydate', 'slider'],
            function() {
                var layer = layui.layer, laydate = layui.laydate, slider=layui.slider;
                //laydate.render({
                //    elem: '#pubtime',
                //    done: function(value, date, endDate) {
                //        app.$data.model.PubTime = value;
                //    }
                //});
                slider.render({
                    elem: '#slider',
                    height: 100,
                    tips: true,
                    input: true,
                    min: 1,
                    theme: "#3c7ac9",
                    change: function (value) {
                        app.$data.percentage = value
                    }
                });
            });



        $(document).ready(function () {
            $('#importbtn').click(function () {
                $('#import').click();
            });

            setTimeout(function(){
                $(".lightgalleryDD").lightGallery({
                    speed: 40,
                    download: false,
                    showThumbByDefault: false
                })
            }, 400);

            try {
                $('.select2').select2({
                    language: "zh-CN",
                    width: "100%",
                    height: "32px",
                    theme: "classic"
                });
            } catch (e) {
                if (confirm("页面加载失败，刷新重试?")) {
                    history.go(0)
                }
            }
        })




        function trimVal(val) {
            return $.trim(val);
        }

        var app = new Vue({
            el: '#page-app',
            data: {
                id: @(ViewData["id"]),
                model: {exitManager: '',dealID:""  },
                notes: "",
                progress: 0,
                dealList: [],
                yearList: [],
                imgFileList: [],
                dataImageFileList: [],
                fileList: [],
                EXIT: [],
                dataListEXIT: [],
                loadState: -1,
                percentage: 1,
                scoreStageList: [],
                scorePass: false
            },
            computed: {
                portfolioName() {
                    return this.model.Name;
                }
            },
            watch: {
                portfolioName(newVal, oldVal) {
                    this.updatePortfolio(newVal)
                }
            },
            methods: {
                @* 初始化年份 *@
                initYear: function() {
                    var currYear = (new Date()).getFullYear();
                    var yearList = [];
                    for (var i = 0; currYear - i >= 1990; i++) {
                        yearList.push(currYear - i);
                    }
                    this.yearList = yearList;
                },
                updateDeal(id) {
                    var that = this;
                    $.post('/adminapi/getDealsByPortfolioID',
                        { id },
                        function (res) {
                            if (!res) {
                                return;
                            }
                            that.dealList = res;
                        }).error(function (xhr, errorText, errorType) {

                        });
                },
                updatePortfolio(portfolioName) {
                    var that = this;
                    $.post('/adminapi/getPortfolio',
                        { name: portfolioName },
                        function (res) {
                            if (!res) {
                                return;
                            }
                            that.updateDeal(res.portfolioID)
                            if (res.ExitManager) {
                                that.$set(that.model, 'exitManager', res.ExitManager);
                            }
                        }).error(function (xhr, errorText, errorType) {

                        });
                },
                @* 初始化页面数据 *@
                initData: function() {
                    var that = this;
                    $.post('/adminapi/portfolioexitdetail',
                        { id: that.id },
                        function(res) {
                            if (res.code == 0) {
                                res.data.PubTime = res.data.PubTimeStr;
                                //$("#groupMember").val(res.data.groupMember.split(',')).trigger('change');

                                that.model = res.data;
                                that.initAttachment();
                            } else {
                                that.loadState = -999;
                            }
                        }).error(function(xhr, errorText, errorType) {
                        that.loadState = -999;
                    });
                },
                initTemplate() {
                    var that = this
                    $.post('/adminapi/userSingleGroup',
                        { id: that.id, init: 1 },
                        function (res) {
                            if (res.code == 0 && res.data != null) {
                               $("#group").val([res.data]).trigger('change');
                            } else {
                                that.loadState = -999;
                            }
                        }).error(function (xhr, errorText, errorType) {
                               that.loadState = -999;
                        });
                    setTimeout(function () {
                        $("#interalPTCP").val(["@member.RealName"]).trigger('change');
                        $("#manager").val(["@member.RealName"]).trigger('change');
                    })
                },

                initAttachment() {
                    var that = this;
                    $.post('/adminapi/PortfolioExitDocs',
                        { id: that.id },
                        function(res) {
                            if (res.code == 0) {
                                that.dataListEXIT = [];
                                that.dataImageFileList = [];
                                that.fileList = res.data || [];
                                that.fileList = that.fileList.filter(function(file) {
                                   if (file.AtSuffix === "EXIT") {
                                        that.EXIT.push(file)
                                        that.dataListEXIT.push(file.AtUrl.split(','));
                                        return false
                                    }
                                    return true
                                })
                            } else {
                                that.loadState = -999;
                            }
                        }).error(function(xhr, errorText, errorType) {
                        that.loadState = -999;
                    });
                },
                @* 参数验证 *@
                paramCheck() {
                    var errVal, that = this;
                    if (trimVal(that.model.Name) == '') {
                        errVal = '请输入项目名称';
                    } else if (trimVal(that.model.ToRoleId) == '' || that.model.ToRoleId <= 0) {
                        errVal = '请选择项目组';
                    }
                    //else if (trimVal(that.model.PubTime) == '') {
                    //    errVal = '请选择日期';
                    //}

                    if (errVal) {
                        layer.msg(errVal);
                        return false;
                    }
                    return true;
                },
                paramCheckSimple() {
                    var errVal, that = this;
                    if (trimVal(that.model.Name) == '') {
                        errVal = '请输入项目名称！';
                    }
                    else if (trimVal(that.model.exitFund) == '') {
                        errVal = '请选择退出主体！';
                    }
                    else if (that.model.exitStatus == 2 && trimVal(that.model.exitStatusExplain) == "") {
                        errVal = '请填写未按计划完成退出的情况说明！';
                    }
                    //else if (trimVal(that.model.ToRoleId) == '' || that.model.ToRoleId <= 0) {
                    //    errVal = '请选择项目组';
                    //}
                    //else if (trimVal(that.model.PubTime) == '') {
                    //    errVal = '请选择日期';
                    //}
                    //else if (trimVal(that.model.exitManager) == '') {
                    //    errVal = '请选择退出负责人';
                    //}

                    if (errVal) {
                        layer.msg(errVal);
                        return false;
                    }
                    return true;
                },
                paramAttach() {
                    var list = [], that = this;
                    that.fileList.forEach(function(v, i) {
                        if (!v.Id) {
                            list.push(v);
                        }
                    });
                    that.imgFileList.forEach(function (v, i) {
                        if (!v.Id) {
                            v.AtSuffix = "Image";
                            list.push(v);
                        }
                    });
                    return list;
                },

                @* 保存请求 *@
                savePost(status) {
                    var that = this;
                    that.loadState = 0;
                    if (isdebug) {
                        that.loadState = -1;
                        return;
                    }
                    var attach = [];
                    if (that.id <= 0) {
                        attach = that.paramAttach();
                        String.prototype.replaceAll = function(s1, s2) {
                            return this.replace(new RegExp(s1, "gm"), s2);
                        }

                        for (var i = 0; i < that.EXIT.length; i++) {
                            if (that.EXIT[i].AtSuffix == "EXIT") {
                                var EXIT = {
                                    AtUrl: that.EXIT[i].AtUrl.replaceAll(window.location.origin, ""),
                                    AtSuffix: that.EXIT[i].AtSuffix,
                                    AtName: that.EXIT[i].AtName,
                                    Content: that.EXIT[i].Content
                                }
                                attach.push(EXIT)
                            }
                        }
                    }


                    $.post('/adminapi/portfolioexitsave',
                        { model: that.model, attach: JSON.stringify(attach), },
                        function(res) {
                            if (!res || res.code != 0) {
                                that.loadState = -1;
                                layer.msg(res.msg || "服务器繁忙，请稍后重试...");
                                return;
                            }
                            window.onbeforeunload = null

                            layer.msg("发布成功");
                            setTimeout(function() {
                                    window.location.href = '/index/portfolioexit';
                                },
                                1200);

                        }).error(function(xhr, errorText, errorType) {
                        that.loadState = -1;
                        layer.msg("服务器繁忙，请稍后重试...");
                    });
                },
                @* 保存数据 *@
                saveData(status) {
                    var that = this;
                    if (that.loadState > -1)
                        return;

                    if (status < 0) {
                        window.history.go(-1);
                        return;
                    }
                    that.model.Status = status;


                    if (that.paramCheckSimple()) {
                        that.savePost(status);
                    }

                },

                genGroup(name, value, checked, displayName) {
                    return `<div><input type="radio" class="query-group" name=${name} id=${value} value=${value} ${checked && "checked"}></input>
                                  <label for=${value}
 style="margin-right: 10px">${displayName}</label></div>`
                },

            },
            created: function () {
                var that = this
                that.initYear();
                if (that.id > 0) {
                    that.initData();
                } else {
                    that.initTemplate();
                }

                $("#page-app").show();
                //this.initGallery();
            }
        });
    </script>
}