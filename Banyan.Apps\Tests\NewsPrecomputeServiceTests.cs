using System;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Banyan.Apps;

namespace Banyan.Apps.Tests
{
    /// <summary>
    /// NewsPrecomputeService 测试类
    /// </summary>
    [TestClass]
    public class NewsPrecomputeServiceTests
    {
        /// <summary>
        /// 测试单例模式
        /// </summary>
        [TestMethod]
        public void TestSingletonPattern()
        {
            // Arrange & Act
            var instance1 = NewsPrecomputeService.Instance;
            var instance2 = NewsPrecomputeService.Instance;
            
            // Assert
            Assert.IsNotNull(instance1);
            Assert.IsNotNull(instance2);
            Assert.AreSame(instance1, instance2, "应该返回同一个实例");
        }
        
        /// <summary>
        /// 测试获取预计算相似新闻（空结果）
        /// </summary>
        [TestMethod]
        public void TestGetPrecomputedSimilarNews_EmptyResult()
        {
            // Arrange
            var service = NewsPrecomputeService.Instance;
            int nonExistentNewsId = -1;
            
            // Act
            var result = service.GetPrecomputedSimilarNews(nonExistentNewsId);
            
            // Assert
            Assert.IsNull(result, "不存在的新闻ID应该返回null");
        }
        
        /// <summary>
        /// 测试预计算方法不会抛出异常
        /// </summary>
        [TestMethod]
        public async Task TestPrecomputeHotNewsRecommendations_NoException()
        {
            // Arrange
            var service = NewsPrecomputeService.Instance;
            
            // Act & Assert
            try
            {
                var result = await service.PrecomputeHotNewsRecommendationsAsync(1, 1, 0.5);
                Assert.IsNotNull(result, "预计算结果不应该为null");
                Assert.IsTrue(result.StartTime <= result.EndTime, "开始时间应该早于或等于结束时间");
            }
            catch (Exception ex)
            {
                Assert.Fail($"预计算方法不应该抛出异常: {ex.Message}");
            }
        }
    }
}