﻿
@{
    Layout = null;
    var model = ViewData["model"] as Banyan.Domain.Attachment;
    Banyan.Domain.Member member = ViewData["manager"] as Banyan.Domain.Member;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="divport" content="width=device-width" />
    <title>预览</title>
    <link href="~/Content/js/plugins/lightgallery/css/normalize.css" rel="stylesheet" />
    <link href="~/Content/js/plugins/lightgallery/css/lightgallery.min.css" rel="stylesheet" />
    <style>
 .demo-gallery > ul {
                margin-bottom: 0;
            }

            .demo-gallery {
                padding: 0 15px 15px;
            }

        .water-mark {
            font-size: 14px;
            color: #c2c2c2; /* 颜色会动态重写，根据配置 */
            position: fixed;
            padding: 0 15px;
            transform: translate(-50%, -50%);
            transform: rotate(-30deg);
            -ms-transform: rotate(-30deg); /* IE 9 */
            -moz-transform: rotate(-30deg); /* Firefox */
            -webkit-transform: rotate(-30deg); /* Safari 和 Chrome */
            -o-transform: rotate(-30deg);
            opacity: 0.3;
            overflow: hidden;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            pointer-events: none;
            z-index: 99991017;
        }

        .demo-gallery > ul > li {
            background-color: rgba(0, 0, 0, 0.01);
            float: left;
            @* margin-bottom: 15px; *@
            width: 100%;
            list-style: none;
        }

            .demo-gallery > ul > li a {
                border: 3px solid #FFF;
                border-radius: 3px;
                display: block;
                overflow: hidden;
                position: relative;
                margin: 0 auto;
                max-width: 1100px;
                /*float: left;*/
            }

                        .demo-gallery > ul > li a > img {
                            -webkit-transition: -webkit-transform 0.15s ease 0s;
                            -moz-transition: -moz-transform 0.15s ease 0s;
                            -o-transition: -o-transform 0.15s ease 0s;
                            transition: transform 0.15s ease 0s;
                            -webkit-transform: scale3d(1, 1, 1);
                            transform: scale3d(1, 1, 1);
                            height: 100%;
                            width: 100%;
                        }

                        .demo-gallery > ul > li a:hover > img {
                            -webkit-transform: scale3d(1.1, 1.1, 1.1);
                            transform: scale3d(1.1, 1.1, 1.1);
                        }

                        .demo-gallery > ul > li a:hover .demo-gallery-poster > img {
                            opacity: 1;
                        }

                        .demo-gallery > ul > li a .demo-gallery-poster {
                            bottom: 0;
                            left: 0;
                            position: absolute;
                            right: 0;
                            top: 0;
                            -webkit-transition: background-color 0.15s ease 0s;
                            -o-transition: background-color 0.15s ease 0s;
                            transition: background-color 0.15s ease 0s;
                        }

                            .demo-gallery > ul > li a .demo-gallery-poster > img {
                                left: 50%;
                                margin-left: -10px;
                                margin-top: -10px;
                                opacity: 0;
                                position: absolute;
                                top: 50%;
                                -webkit-transition: opacity 0.3s ease 0s;
                                -o-transition: opacity 0.3s ease 0s;
                                transition: opacity 0.3s ease 0s;
                            }

                .demo-gallery .justified-gallery > a > img {
                    -webkit-transition: -webkit-transform 0.15s ease 0s;
                    -moz-transition: -moz-transform 0.15s ease 0s;
                    -o-transition: -o-transform 0.15s ease 0s;
                    transition: transform 0.15s ease 0s;
                    -webkit-transform: scale3d(1, 1, 1);
                    transform: scale3d(1, 1, 1);
                    height: 100%;
                    width: 100%;
                }

                .demo-gallery .justified-gallery > a:hover > img {
                    -webkit-transform: scale3d(1.1, 1.1, 1.1);
                    transform: scale3d(1.1, 1.1, 1.1);
                }

                .demo-gallery .justified-gallery > a:hover .demo-gallery-poster > img {
                    opacity: 1;
                }

                .demo-gallery .justified-gallery > a .demo-gallery-poster {
                    background-color: rgba(0, 0, 0, 0.01);
                    bottom: 0;
                    left: 0;
                    position: absolute;
                    right: 0;
                    top: 0;
                    -webkit-transition: background-color 0.15s ease 0s;
                    -o-transition: background-color 0.15s ease 0s;
                    transition: background-color 0.15s ease 0s;
                }

                    .demo-gallery .justified-gallery > a .demo-gallery-poster > img {
                        left: 50%;
                        margin-left: -10px;
                        margin-top: -10px;
                        opacity: 0;
                        position: absolute;
                        top: 50%;
                        -webkit-transition: opacity 0.3s ease 0s;
                        -o-transition: opacity 0.3s ease 0s;
                        transition: opacity 0.3s ease 0s;
                    }
/*
                .demo-gallery .justified-gallery > a:hover .demo-gallery-poster {
                    background-color: rgba(0, 0, 0, 0.5);
                }*/

                .demo-gallery .video .demo-gallery-poster img {
                    height: 48px;
                    margin-left: -24px;
                    margin-top: -24px;
                    opacity: 0.8;
                    width: 48px;
                }

                .demo-gallery.dark > ul > li a {
                    border: 3px solid #04070a;
                }

            .home .demo-gallery {
                padding-bottom: 80px;
            }

    </style>
</head>
<body oncontextmenu = "return false" oncopy = "return false" onselectstart = "return false">
    <div id="detail">
        <div class="block" v-if="dataList.length > 0">
            <div class="demo-gallery">
                <ul id="lightgallery" class="list-unstyled row">
                    <li class="col-xs-6 col-sm-4 col-md-3" v-for="(item, index) in dataList" data-responsive="" :data-src="item" data-sub-html="">
                        <a href="javascript:;">
                            <img class="img-responsive" :src="item">
                            <div class="demo-gallery-poster">
                                <img src="/content/js/plugins/lightgallery/img/zoom-black.svg" />
                            </div>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <template v-for="item in waterArray">
            <p class="water-mark" :style="{left: item.wid + '%', top: item.hei + '%'}">@(member.RealName)</p>
        </template>
    </div>
    <script type="text/javascript" src="~/content/js/core/jquery.min.js"></script>
    <script type="text/javascript" src="~/content/js/plugins/layui/layui.js"></script>
    <script type="text/javascript" src="~/content/js/vue/vue.min.js"></script>
    <script src="/content/js/plugins/jquery.ui.widget.js"></script>
    <script src="/content/js/plugins/jquery.iframe-transport.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/picturefill.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lightgallery.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-fullscreen.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-thumbnail.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-video.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-autoplay.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-zoom.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-hash.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-pager.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/jquery.mousewheel.min.js"></script>

    <script type="text/javascript">
    window.addEventListener('keydown', function (e) {
        if (e.keyCode == 83 && (navigator.platform.match('Mac') ? e.metaKey : e.ctrlKey)) {
            e.preventDefault();
        }
    })
    var app = new Vue({
        name: 'detail',
        el: '#detail',
        data: function () {
            return {
                dataList: [],
                waterArray: []
            }
        },
        methods: {
            initWaterArr: function () {
                var that = this;
                var waterarr = [], wid = 5, hei = 5;
                for (var i = 0; i < 5; i++) {
                    for (var j = 0; j < 5; j++) {
                        waterarr.push({
                            wid: wid + i * 19,
                            hei: hei + j * 16
                        });
                    }
                }
                that.waterArray = waterarr;
            }
        },
        mounted: function () {
            var that = this;
            that.dataList = "@(model.AtUrl)".split(",");
            setTimeout(function () {
                $('#lightgallery').lightGallery({
                    speed: 40,
                    download: false,
                    showThumbByDefault: false
                });
            }, 1000);
        },
        created: function () {
            var that = this;
            that.initWaterArr();
        }
    })
    </script>
</body>
</html>
