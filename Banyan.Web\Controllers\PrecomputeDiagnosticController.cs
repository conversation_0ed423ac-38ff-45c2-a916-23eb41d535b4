using System;
using System.Threading.Tasks;
using System.Web.Mvc;
using Banyan.Apps;
using Banyan.Code;
using Newtonsoft.Json;

namespace Banyan.Web.Controllers
{
    /// <summary>
    /// 预计算诊断控制器
    /// 提供预计算系统的监控、诊断和管理功能
    /// </summary>
    public class PrecomputeDiagnosticController : Controller
    {
        private readonly PrecomputeDiagnosticService _diagnosticService;
        private readonly NewsPrecomputeService _precomputeService;
        private readonly PrecomputeTestService _testService;

        public PrecomputeDiagnosticController()
        {
            _diagnosticService = PrecomputeDiagnosticService.Instance;
            _precomputeService = NewsPrecomputeService.Instance;
            _testService = PrecomputeTestService.Instance;
        }
        
        /// <summary>
        /// 诊断首页
        /// </summary>
        /// <returns></returns>
        public ActionResult Index()
        {
            return View();
        }
        
        /// <summary>
        /// 获取预计算统计信息
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> GetStatistics()
        {
            try
            {
                var statistics = await _precomputeService.GetPrecomputeStatisticsAsync();
                return Json(new { success = true, data = statistics }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error($"获取预计算统计信息失败: {ex.Message}", ex);
                return Json(new { success = false, message = ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }
        
        /// <summary>
        /// 运行完整诊断
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> RunFullDiagnostic()
        {
            try
            {
                var report = await _diagnosticService.RunFullDiagnosticAsync();
                return Json(new { success = true, data = report }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error($"运行完整诊断失败: {ex.Message}", ex);
                return Json(new { success = false, message = ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }
        
        /// <summary>
        /// 手动触发用户推荐预计算
        /// </summary>
        /// <param name="recommendationCount">推荐数量</param>
        /// <param name="threshold">相似度阈值</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> TriggerUserPrecompute(int recommendationCount = 20, double threshold = 0.4)
        {
            try
            {
                Logger.Info($"手动触发用户推荐预计算，推荐数量: {recommendationCount}，阈值: {threshold}");
                
                var result = await _precomputeService.PrecomputeUserRecommendationsAsync(recommendationCount, threshold);
                
                return Json(new { 
                    success = true, 
                    data = result,
                    message = $"用户推荐预计算完成，成功: {result.SuccessCount}，失败: {result.FailedCount}，耗时: {result.Duration.TotalSeconds:F2}秒"
                });
            }
            catch (Exception ex)
            {
                Logger.Error($"手动触发用户推荐预计算失败: {ex.Message}", ex);
                return Json(new { success = false, message = ex.Message });
            }
        }
        
        /// <summary>
        /// 手动触发热门新闻预计算
        /// </summary>
        /// <param name="hotNewsCount">热门新闻数量</param>
        /// <param name="similarCount">相似新闻数量</param>
        /// <param name="threshold">相似度阈值</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> TriggerHotNewsPrecompute(int hotNewsCount = 50, int similarCount = 10, double threshold = 0.5)
        {
            try
            {
                Logger.Info($"手动触发热门新闻预计算，热门新闻数量: {hotNewsCount}，相似新闻数量: {similarCount}，阈值: {threshold}");
                
                var result = await _precomputeService.PrecomputeHotNewsRecommendationsAsync(hotNewsCount, similarCount, threshold);
                
                return Json(new { 
                    success = true, 
                    data = result,
                    message = $"热门新闻预计算完成，成功: {result.SuccessCount}，失败: {result.FailedCount}，耗时: {result.Duration.TotalSeconds:F2}秒"
                });
            }
            catch (Exception ex)
            {
                Logger.Error($"手动触发热门新闻预计算失败: {ex.Message}", ex);
                return Json(new { success = false, message = ex.Message });
            }
        }
        
        /// <summary>
        /// 手动触发综合预计算
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> TriggerComprehensivePrecompute()
        {
            try
            {
                Logger.Info("手动触发综合预计算");
                
                var result = await _precomputeService.PrecomputeAllAsync();
                
                return Json(new { 
                    success = true, 
                    data = result,
                    message = $"综合预计算完成，总耗时: {result.Duration.TotalSeconds:F2}秒，" +
                             $"新闻预计算成功: {result.NewsPrecomputeResult.SuccessCount}，" +
                             $"用户推荐预计算成功: {result.UserPrecomputeResult.SuccessCount}"
                });
            }
            catch (Exception ex)
            {
                Logger.Error($"手动触发综合预计算失败: {ex.Message}", ex);
                return Json(new { success = false, message = ex.Message });
            }
        }
        
        /// <summary>
        /// 强制刷新指定用户的推荐
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> ForceRefreshUserRecommendations(int userId)
        {
            try
            {
                if (userId <= 0)
                {
                    return Json(new { success = false, message = "用户ID无效" });
                }
                
                Logger.Info($"强制刷新用户 {userId} 的推荐");
                
                var success = await _precomputeService.ForceRefreshUserRecommendationsAsync(userId);
                
                if (success)
                {
                    return Json(new { 
                        success = true, 
                        message = $"成功强制刷新用户 {userId} 的推荐"
                    });
                }
                else
                {
                    return Json(new { 
                        success = false, 
                        message = $"强制刷新用户 {userId} 的推荐失败"
                    });
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"强制刷新用户 {userId} 推荐失败: {ex.Message}", ex);
                return Json(new { success = false, message = ex.Message });
            }
        }
        
        /// <summary>
        /// 清除所有预计算缓存
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> ClearAllCache()
        {
            try
            {
                Logger.Info("手动清除所有预计算缓存");
                
                var success = await _precomputeService.ClearAllPrecomputedCacheAsync();
                
                if (success)
                {
                    return Json(new { 
                        success = true, 
                        message = "成功清除所有预计算缓存"
                    });
                }
                else
                {
                    return Json(new { 
                        success = false, 
                        message = "清除预计算缓存失败"
                    });
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"清除所有预计算缓存失败: {ex.Message}", ex);
                return Json(new { success = false, message = ex.Message });
            }
        }
        
        /// <summary>
        /// 清除指定用户的预计算缓存
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult ClearUserCache(int userId)
        {
            try
            {
                if (userId <= 0)
                {
                    return Json(new { success = false, message = "用户ID无效" });
                }
                
                Logger.Info($"清除用户 {userId} 的预计算缓存");
                
                var success = _precomputeService.ClearUserPrecomputedRecommendations(userId);
                
                if (success)
                {
                    return Json(new { 
                        success = true, 
                        message = $"成功清除用户 {userId} 的预计算缓存"
                    });
                }
                else
                {
                    return Json(new { 
                        success = false, 
                        message = $"清除用户 {userId} 的预计算缓存失败"
                    });
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"清除用户 {userId} 预计算缓存失败: {ex.Message}", ex);
                return Json(new { success = false, message = ex.Message });
            }
        }
        
        /// <summary>
        /// 检查Redis连接状态
        /// </summary>
        /// <returns></returns>
        public ActionResult CheckRedisConnection()
        {
            try
            {
                var isConnected = _precomputeService.CheckRedisConnection();

                return Json(new {
                    success = true,
                    data = new { isConnected = isConnected },
                    message = isConnected ? "Redis连接正常" : "Redis连接异常"
                }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error($"检查Redis连接状态失败: {ex.Message}", ex);
                return Json(new { success = false, message = ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 运行完整的预计算系统测试
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> RunCompleteTest()
        {
            try
            {
                Logger.Info("开始运行完整的预计算系统测试");

                var testResult = await _testService.RunCompleteTestAsync();

                return Json(new {
                    success = testResult.OverallSuccess,
                    data = testResult,
                    message = testResult.OverallSuccess ?
                        $"预计算系统测试通过，耗时: {testResult.Duration.TotalSeconds:F2}秒" :
                        $"预计算系统测试失败: {testResult.ErrorMessage}"
                }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error($"运行预计算系统测试失败: {ex.Message}", ex);
                return Json(new { success = false, message = ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }
    }
}
