﻿
@{
    Layout = null;
}

<!DOCTYPE html>
<!--[if IE 9]>
    <html class="ie9 no-focus" lang="en"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus" lang="en">
<!--<![endif]-->
<head>
    <meta charset="utf-8">
    <title>高榕创投IMS-GAORONG VENTURES</title>
    <meta name="description" content="">
    <meta name="author" content="">
    <meta name="robots" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

    <link rel="shortcut icon" href="/content/img/favicons/favicon.ico">
    <link rel="icon" type="image/png" href="/content/img/favicons/favicon.ico" sizes="16x16">

    <link href="/content/css/bootstrap.min.css" rel="stylesheet" />
    <link href="/content/css/oneui.min.css" rel="stylesheet" />
    <style>
        .login-all-wrapper {
            margin-top: 50px;
        }
        #login_container {
            opacity:.55;
        }
        #login_container:hover {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="bg-white pulldown" id="app" style="display:none;">
        <div class="content content-boxed overflow-hidden block-opt-refresh">
            <div class="row">
                <div id="ie-warn" style="display:none;color: red; text-align:center;font-weight:bold">不兼容IE浏览器，请使用Chrome、FireFox、Edge浏览器访问!</div>
                <div id="cookie-warn" style="display:none;color: red; text-align:center;font-weight:bold">您的浏览器不支持cookies或cookies被禁用，要使用本网站，您必须在浏览器设置中启用cookies！</div>

                <div class="text-center" style="margin-top: 30px">
                    <img class="brand-img" src="https://ronghuifiles.blob.core.chinacloudapi.cn/ronghui/newlogo.png" style="width: 176px;" />
                    <p class="text-muted push-15-t"></p>
                </div>
                <div class="login-all-wrapper col-sm-8 col-sm-offset-2 col-md-10 col-md-offset-1" onkeydown="if(event.keyCode=='13') loginform.submit.click();">
                    <div class="">
                        <div class="col-md-5 col-xs-12 push-30-t push-50 animated fadeIn col-md-push-6">
                            <form autocomplete="off" class="form-horizontal" id="log-form" name="loginform" method="post">
                                <div class="form-group">
                                    <div class="col-xs-12">
                                        <div class="form-material form-material-primary">
                                            <input class="form-control" type="number" v-model="model.phone" title="手机号码">
                                            <label for="login-phone">手机号码</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group" v-if="model.loginway == 0">
                                    <div class="col-xs-12">
                                        <div class="form-material form-material-primary">
                                            <input class="form-control" autocomplete="off" type="password" v-model="model.passwd" title="登录密码">
                                            <label for="login-passwd">登录密码</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group" v-else-if="model.loginway == 1">
                                    <div class="col-xs-12">
                                        <div class="input-group form-material form-material-primary">
                                            <input class="form-control" v-model="model.verifycode" title="验证码">
                                            <label for="login-verifycode">短信验证码</label>
                                            <span class="input-group-addon" style="padding-right:0;cursor:pointer;" v-on:click="getsmscode()">
                                                <i class="fa fa-refresh fa-spin" style="margin-right:5px;" v-show="loadcode"></i>
                                                <template v-if="countdown <= 0">
                                                    获取验证码
                                                </template>
                                                <template v-else>
                                                    {{countdown}}秒后可重发
                                                </template>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="col-xs-5">
                                        <div class="font-s13 push-5-t">
                                            <a href="javascript:;" v-show="model.loginway == 0" v-on:click="changeway(1)">短信验证登录</a>
                                            <a href="javascript:;" v-show="model.loginway == 1" v-on:click="changeway(0)">账号密码登录</a>
                                        </div>

                                    </div>
                                    <div class="col-xs-4">
                                        <div class="font-s13 push-5-t">
                                            <!-- span style="cursor:pointer;" @@click="forgetPwd(1)">密码修改与找回</span-->
                                            <a href="https://passwd.gaorongvc.cn/?action=sendsms">密码创建或重置</a>
                                        </div>
                                    </div>
                                    <div class="col-xs-3">
                                        <div class="font-s13 text-right push-5-t">
                                            <!-- span style="cursor:pointer;" @@click="forgetPwd(1)">密码修改与找回</span-->
                                            <a href="https://passwd.gaorongvc.cn">密码修改</a>
                                        </div>
                                    </div>
                                </div>

                                <input id="subtn" name="submit" style="display: none" v-on:click="login">
                                <div class="form-group push-30-t">
                                    <div class="col-xs-12 col-sm-7 col-sm-offset-3 col-md-4 col-md-offset-4">
                                        <a class="btn btn-block btn-primary" href="javascript:;" v-on:click="login">登 录</a>
                                    </div>
                                </div>

                            </form>
                        </div>
                        <div class="col-md-6 col-xs-12 col-md-pull-5" id="login_container" style="text-align:center;">
                        </div>
                    </div>
                    </div>

                </div>
        </div>
    </div>
    <div class="pulldown push-30-t text-center animated fadeInUp">
        <small class="text-muted"><span class="js-year-copy">2018</span> &copy; GAORONG VENTURES</small>
    </div>

    <script type="text/javascript" src="/content/js/oneui.min.js"></script>
    <script type="text/javascript" src="/content/js/commonhp2.js"></script>
    <script type="text/javascript" src="/content/js/plugins/bootstrap-notify/bootstrap-notify.min.js"></script>
    <script type="text/javascript" src="/content/js/vue/vue.min.js"></script>
    <script type="text/javascript">
      var returnurl = null;
        // 获取URL参数returnurl
        (function() {
            function getQueryString(name) {
                var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return decodeURIComponent(r[2]);
                return null;
            }
            returnurl = getQueryString('returnurl');
        })();

        if (!!window.ActiveXObject || "ActiveXObject" in window)
            document.getElementById("ie-warn").style.display = "block";

        !function (a, b, c) { function d(a) { var c = "default"; a.self_redirect === !0 ? c = "true" : a.self_redirect === !1 && (c = "false"); var d = b.createElement("iframe"), e = "https://open.weixin.qq.com/connect/qrconnect?appid=" + a.appid + "&scope=" + a.scope + "&redirect_uri=" + a.redirect_uri + "&state=" + a.state + "&login_type=jssdk&self_redirect=" + c + '&styletype=' + (a.styletype || '') + '&sizetype=' + (a.sizetype || '') + '&bgcolor=' + (a.bgcolor || '') + '&rst=' + (a.rst || ''); e += a.style ? "&style=" + a.style : "", e += a.href ? "&href=" + a.href : "", d.src = e, d.frameBorder = "0", d.allowTransparency = "true", d.scrolling = "no", d.width = "q00px", d.height = "240px"; var f = b.getElementById(a.id); f.innerHTML = "", f.appendChild(d) } a.WxLogin = d }(window, document);

      

        function genQRCode() {
            try {
                var obj = new WxLogin({
                    self_redirect: false,
                    id: "login_container",
                    appid: "wxe2c6ee8162e702fb",
                    scope: "snsapi_login",
                    redirect_uri: "https%3A%2F%2Fims.gaorongvc.com/Login/Wechat",
                    state: "",
                    style: "",
                    href: window.location.protocol + '//' + window.location.host + "/content/css/login2.css"
                });
            } catch (e) {
                console.log(e);
            }
        }
        genQRCode();

        function SetCookie(name, value)//两个参数，一个是cookie的名子，一个是值  
        {
            var Hours = 1; //此 cookie 将被保存 30 天  
            var exp = new Date(); //new Date("December 31, 9998");  
            exp.setTime(exp.getTime() + Hours * 60 * 60 * 1000);
            document.cookie = name + "=" + escape(value) + ";expires=" + exp.toGMTString();
        }
        function getCookie(name)//取cookies函数     
        {
            var arr = document.cookie.match(new RegExp("(^| )" + name + "=([^;]*)(;|$)"));
            if (arr != null) return unescape(arr[2]); return null;

        } 
        function delCookie(name)//删除cookie  
        {
            var exp = new Date();
            exp.setTime(exp.getTime() - 1);
            var cval = getCookie(name);
            if (cval != null) document.cookie = name + "=" + cval + ";expires=" + exp.toGMTString();
        }  
        var interval = null;
        var app = new Vue({
            el: '#app',
            data: {
                loadstate: -1,
                countdown: 0,
                loadcode: false,
                model: {
                    phone: '',
                    passwd: '',
                    verifycode: '',
                    loginway: 0,
                }
            },
            methods: {
                login: function () {
                    var that = this;
                    if (that.loadstate > -1)
                        return false;
                    if (!isphone(that.model.phone)) {
                        notify('请填写有效的手机号');
                        return false;
                    }

                    if (that.model.loginway == 0 && (that.model.passwd.length < 8 || that.model.passwd.length > 20)) {
                        notify('请填写8-20位密码，若密码过短请及时修改');
                        return false;
                    }

                    if (that.model.loginway == 1 && !iscodelong(that.model.verifycode)) {
                        notify('请填写合法验证码');
                        return false;
                    }

                    that.loadstate = 0;
                    $.post('/login/login', that.model, function (res) {
                        if (res.code == 0) {
                            notify('登录成功', 'success');
                            setTimeout(function () {
                                if(returnurl) {
                                    window.location.href = returnurl;
                                } else {
                                    window.location.href = '/';
                                }
                            }, 1500);
                        } else {
                            that.loadstate = -1;
                            notify(res.msg);
                        }
                    }).error(function (xhr, errorText, errorType) {
                        that.loadstate = -1;
                        notify('登录失败', 'danger');
                    });
                },
                changeway: function (way) {
                    this.model.loginway = way;
                },
                getsmscode: function () {
                    var that = this;
                    if (that.loadcode && that.countdown > 0)
                        return false;

                    var phone = that.model.phone;
 
                    if (!isphone(phone)) {
                        notify('请填写有效的手机号');
                        return false;
                    }

                    that.loadcode = true;
                    $.post('/api/sendsmscode', { phone: phone }, function (res) {
                        that.loadcode = false;
                        if (res.code == 0) {
                            notify('短信已发送', 'success');

                            clearInterval(interval);
                            that.countdown = 60;

                            interval = setInterval(function () {
                                that.countdown--;
                                if (that.countdown <= 0)
                                    clearInterval(interval);
                            }, 1000);
                        } else
                            notify(res.msg);
                    }).error(function (xhr, errorText, errorType) {
                        that.loadcode = false;
                        notify('短信发送失败', 'danger');
                    });
                },
                
                forgetPwd: function (val) {
                    location.href = "https://passwd.gaorongvc.cn";
                },
            },
            created: function () {
                $("#app").show();
                SetCookie('cookie_test', '1'); 
                var cookie_test = getCookie('cookie_test');
                if ('1' != cookie_test) {
                    document.getElementById("cookie-warn").style.display = "block";
                }
            }
        });
    </script>
</body>
</html>