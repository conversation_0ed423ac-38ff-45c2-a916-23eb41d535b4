/**
 * Recommendation System Styles
 */

/* Card Styles */
.news-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
    border: 1px solid #f0f0f0;
    height: 100%;
    display: flex;
    flex-direction: column;
    animation: fadeIn 0.3s ease-out;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.news-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px 0;
    border: none;
}

.news-card .card-body {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    padding: 10px;
}

.news-title {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 15px;
    line-height: 1.4;
    color: white;
    height: 36px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.news-title a {
    color: white;
    text-decoration: none;
    transition: all 0.2s ease;
}

.news-title a:hover {
    color: #f8f9fa;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.news-summary {
    height: 80px;
    overflow: hidden;
    margin-bottom: 15px;
    flex-grow: 1;
}

.news-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    color: #6c757d;
    font-size: 0.9rem;
    justify-content: space-between;
    align-items: center;
    /* margin-top: auto; */
}

.news-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.news-tags {
    margin-top: 20px;
}

.news-actions {
    margin-top: auto;
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.news-actions .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.news-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.relevance-score, .similarity-score {
    margin-top: 20px;
}

.relevance-score .progress, .similarity-score .progress {
    height: 8px;
    border-radius: 10px;
    background-color: #f8f9fa;
    overflow: hidden;
    margin-bottom: 0;
}

.relevance-score .progress-bar, .similarity-score .progress-bar {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    transition: width 0.6s ease;
}

.similarity-score .progress-bar.bg-success {
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
}

.relevance-text, .similarity-text {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 0.9rem;
    font-weight: 500;
}

.similarity-text .score-value {
    color: #667eea;
    font-weight: 700;
}

/* Filter Panel */
.filter-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
    overflow: hidden;
}

.filter-header {
    background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dee2e6;
}

.filter-header h4 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.filter-content {
    padding: 20px;
    transition: all 0.3s ease;
}

.filter-content.collapsed {
    display: none;
}

.date-range-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-separator {
    color: #6c757d;
    font-weight: 500;
}

.custom-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: all 0.2s ease;
}

.custom-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.filter-actions .btn {
    border-radius: 8px;
    font-weight: 500;
}

.filter-panel {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Results Section */
.results-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.results-header {
    background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dee2e6;
}

.results-header h4 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.view-options .btn {
    border-radius: 6px;
}

.results-container {
    padding: 20px;
    min-height: 400px;
}

.card-view-mode {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.list-view-mode {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* List View Styles */
.news-card.list-view {
    display: flex;
    flex-direction: row;
    align-items: stretch;
}

.news-card.list-view .card-header {
    writing-mode: vertical-rl;
    text-orientation: mixed;
    min-width: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.news-card.list-view .card-body {
    flex: 1;
}

/* Loading Indicator */
#loadingIndicator {
    padding: 30px;
    text-align: center;
}

#loadingIndicator .spinner-border {
    width: 3rem;
    height: 3rem;
    margin-bottom: 15px;
}

.loading-container, .no-results-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    text-align: center;
}

.loading-content, .no-results-content {
    max-width: 400px;
}

.loading-spinner {
    margin-bottom: 20px;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.no-results-content i {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 20px;
}

/* Recommendation Widget */
.recommendation-widget {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 15px;
    margin-bottom: 20px;
}

.widget-header {
    margin-bottom: 15px;
}

.widget-header h5 {
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.widget-header p {
    font-size: 0.85rem;
    margin-bottom: 0;
}

.recommendation-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.recommendation-list.horizontal {
    flex-direction: row;
    flex-wrap: wrap;
}

.recommendation-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s ease;
}

.recommendation-item:last-child {
    border-bottom: none;
}

.recommendation-item:hover {
    background-color: #f8f9fa;
}

.recommendation-item.horizontal {
    display: flex;
    align-items: center;
    width: 100%;
}

.recommendation-item.vertical {
    display: block;
}

.recommendation-title {
    margin-bottom: 5px;
    font-size: 1rem;
    line-height: 1.4;
}

.recommendation-meta {
    display: flex;
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.recommendation-source {
    margin-right: 10px;
}

.recommendation-score {
    margin-top: 5px;
}

.recommendation-score .progress {
    height: 3px;
}

.widget-footer {
    margin-top: 10px;
    text-align: right;
}

.more-link {
    font-size: 0.9rem;
}

.no-recommendations {
    text-align: center;
    padding: 20px;
    color: #6c757d;
}

.widget-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #6c757d;
}

.widget-loading .spinner-border {
    margin-right: 10px;
}

.widget-error {
    text-align: center;
    padding: 15px;
    color: #dc3545;
}

/* Animation Effects */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}



/* Tag and Badge Styles */
.tag-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    margin-right: 8px;
    margin-bottom: 8px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    background-color: #e9ecef;
    color: #495057;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid transparent;
}

.tag-badge:hover {
    background-color: #667eea;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.tag-badge.main-tag {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    border-color: #667eea;
}

.tag-badge.secondary-tag {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: #fff;
    border-color: #6c757d;
}

.tag-badge.semantic-tag {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: #fff;
    border-color: #28a745;
}

.matched-tags-container {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 2px;
    padding-left: 8px;
    border: 1px solid #e9ecef;
}

.matched-tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.matched-tag-item {
    margin-bottom: 5px;
}

.tag-category-label {
    font-size: 0.7rem;
    background-color: rgba(255, 255, 255, 0.3);
    padding: 2px 5px;
    border-radius: 3px;
    margin-left: 4px;
}

.tag-weight-badge {
    font-size: 0.8rem;
    background-color: rgba(0, 0, 0, 0.2);
    padding: 1px 4px;
    border-radius: 3px;
    margin-left: 4px;
}

.no-tags {
    color: #6c757d;
    font-style: italic;
    font-size: 0.9rem;
}

.matched-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.matched-tag i {
    font-size: 0.8rem;
}

.more-tags {
    background-color: #e9ecef;
    color: #495057;
    cursor: pointer;
}

.more-tags:hover {
    background-color: #dee2e6;
}

/* Header Styles */
.recommendation-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 20px 30px 10px;
    margin-bottom: 30px;
    color: white;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.header-content {
    display: flex;
    align-items: center;
    /* margin-bottom: 20px; */
}

.header-icon {
    font-size: 3rem;
    margin-right: 20px;
    opacity: 0.9;
}

.header-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-subtitle {
    font-size: 1.1rem;
    margin: 5px 0 0 0;
    opacity: 0.9;
}

.header-stats {
    display: flex;
    justify-content: space-around;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .news-title {
        height: auto;
        max-height: 50px;
    }

    .news-summary {
        height: auto;
        max-height: 80px;
    }

    .filter-panel .form-inline {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-panel .form-group {
        margin-bottom: 10px;
        width: 100%;
    }

    .recommendation-list.horizontal {
        flex-direction: column;
    }

    .header-content {
        flex-direction: column;
        text-align: center;
    }

    .matched-tags-list {
        flex-direction: column;
        gap: 5px;
    }

    .header-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .header-stats {
        flex-direction: column;
        gap: 15px;
    }

    .date-range-container {
        flex-direction: column;
        gap: 5px;
    }

    .card-view-mode {
        grid-template-columns: 1fr;
    }

    .news-card.list-view {
        flex-direction: column;
    }

    .news-card.list-view .card-header {
        writing-mode: initial;
        text-orientation: initial;
        min-width: auto;
    }
}

/* Pagination Loading Indicators */
#bottomLoadingIndicator {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

#bottomLoadingIndicator .spinner-border {
    width: 2rem;
    height: 2rem;
    border-width: 0.2em;
    color: #667eea;
}

#bottomLoadingIndicator p {
    color: #6c757d;
    margin: 0;
    font-size: 0.9rem;
}

#endMessage {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    margin: 20px 0;
    border: 1px solid #dee2e6;
}

#endMessage i {
    color: #28a745;
}

#endMessage p {
    color: #495057;
    margin: 0;
    font-weight: 500;
}

#retryButton {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

#retryButton .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

#retryButton .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Tag Details Modal */
.tag-list {
    max-height: 300px;
    overflow-y: auto;
}

.tag-item {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.tag-item:last-child {
    border-bottom: none;
}

.tag-name {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.tag-category {
    font-size: 0.75rem;
    padding: 2px 5px;
    border-radius: 3px;
    background-color: #e9ecef;
}

.tag-weight {
    text-align: right;
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 3px;
}

.category-industry {
    background-color: #007bff;
    color: #fff;
}

.category-technology {
    background-color: #28a745;
    color: #fff;
}

.category-finance {
    background-color: #fd7e14;
    color: #fff;
}

.category-market {
    background-color: #6f42c1;
    color: #fff;
}

.chart-container {
    width: 100%;
    height: 350px;
    margin-top: 20px;
    border-radius: 8px;
    overflow: hidden;
}