﻿@using Banyan.Domain
@model Banyan.Domain.Article
@{
    Layout = "/Views/Shared/_Layout.cshtml";
    var classList = (List<Banyan.Domain.Role>)ViewData["rolelist"];
    var member = ViewData["manager"] as Banyan.Domain.Member;
}

<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li>
                    <i class="si si-pointer"></i>
                </li>
                <li><a href="@(Url.Action("Articles","Article"))">专家访谈管理</a></li>
                <li>@(Model.Id == 0 ? "新建" : "编辑")专家访谈</li>
            </ol>
        </div>
        <div class="block-content block-content-full">
            <form class="form-horizontal" method="post" id="article-form" name="article-form">
                <input type="hidden" name="id" id="id" value="@(Model.Id)" />
                <input type="hidden" name="viewcount" value="@(Model.ViewCount)" />
                <input type="hidden" name="collectecount" value="@(Model.CollectCount)" />
                <input type="hidden" name="commentcount" value="@(Model.CommentCount)" />
                <input type="hidden" name="status" id="status" value="@(Model.Status)" />
                <input type="hidden" name="coverurl" id="coverurl" value="@(Model.CoverUrl)" />
                <input type="hidden" name="pdfurl" id="pdfurl" value="@(Model.PdfUrl)" />
                <input type="hidden" name="Sort" id="pdfurl" value="@(Model.Sort)">
                <input type="hidden" id="isprivate" name="isprivate" value="@(Model.IsPrivate)">
                <input type="hidden" id="editorname" name="editorname" value="@(Model.EditorName)">
                <div class="form-group">
                    <label class="col-md-2 control-label" for="title">*访谈对象</label>
                    <div class="col-md-6">
                        <input class="form-control" type="text" id="title" name="title" data-rule="required;" value="@(Model.Title)" placeholder="访谈对象">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="ToRoleId">*项目组</label>
                    <div class="col-md-6">
                        <select class="form-control" id="ToRoleId" name="ToRoleId" size="1">
                            @if (member != null &&!string.IsNullOrEmpty(member.Groups))
                            {
                                string[] groups = member.Groups.Split(',');
                                foreach (var ci in classList)
                                {
                                    if (ci.Id > 0 && groups.Contains(ci.Id.ToString()))
                                    {
                                        <option value="@(ci.Id)">@(ci.RoleName)</option>
                                    }
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="pubtime-label">*日期</label>
                    <div class="col-md-4">
                        <input class="form-control" type="text" id="pubtime" name="pubtime" data-rule="required;" value="@(Model.PubTime.ToString("yyyy-MM-dd"))" placeholder="日期">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="summary">*访谈对象背景</label>
                    <div class="col-md-6">
                        <textarea class="form-control" id="summary" name="summary" rows="3" data-rule="required;" placeholder="访谈对象背景">@(Model.Summary)</textarea>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="content">Key Takeaway</label>
                    <div class="col-md-6">
                        <div id="posit">
                            <textarea class="form-control" id="editor-element" name="content" rows="3" data-target="#posit">@(Html.Raw(Model.Content))</textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-8 col-md-offset-2">
                        <button class="btn btn-primary" id="dopub" type="button"><i class="fa fa-check push-5-r"></i>发 布</button>
                        <button class="btn btn-info" id="dosave" type="button"><i class="fa fa-save push-5-r"></i>保 存</button>
                        <button class="btn btn-minw btn-warning" id="goback" type="button">取消并返回</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section scripts{
    <link href="~/Content/js/plugins/kindeditor/themes/default/default.css" rel="stylesheet" />
    <script src="~/Content/js/plugins/kindeditor/kindeditor-all-min.js"></script>
    <script src="~/Content/js/plugins/kindeditor/lang/zh-CN.js"></script>

    <link href="~/Content/js/plugins/nice-validator/jquery.validator.css" rel="stylesheet" />
    <script src="~/Content/js/plugins/nice-validator/jquery.validator.min.js"></script>
    <script src="~/Content/js/plugins/nice-validator/local/zh-CN.js"></script>

    <script type="text/javascript">
        KindEditor.ready(function (K) {
            editor = K.create('#editor-element', {
                uploadJson: '/content/js/plugins/kindeditor/asp.net/upload_json.ashx',
                imageSizeLimit: '5MB',
                imageUploadLimit: 30,
                fileManagerJson: '/content/js/plugins/kindeditor/asp.net/file_manager_json.ashx',
                allowFileManager: true,
                urlType:'domain',
                afterCreate: function () {
                    var self = this;
                    K.ctrl(document, 13, function () {
                        self.sync();
                    });
                    K.ctrl(self.edit.doc, 13, function () {
                        self.sync();
                    });
                },
                afterUpload: function (data) {
                    this.sync();
                },
                afterBlur: function () {
                    this.sync();
                },
                height: '375px',
            });
        });

        var layer;
        layui.use(['layer', 'laydate', 'upload'], function () {
            var layer = layui.layer, upload = layui.upload, laydate = layui.laydate;

            laydate.render({
                elem: '#pubtime'
                , type: 'date'
            });

            var uploadInst = upload.render({
                elem: '#cover-file'
                , url: '/content/js/plugins/kindeditor/asp.net/upload_json.ashx'
                , before: function (obj) {
                    obj.preview(function (index, file, result) {
                        $('#cover-img-url').removeClass('hidden');
                        $('#cover-img-url').attr('src', result);
                    });
                }
                , done: function (res) {
                    if (res.error == 0) {
                        $('#coverurl').val(res.url);
                        $('#cover-img-url').removeClass('hidden');
                        $('#cover-img-url').attr('src', res.url);
                    } else {
                        return layer.msg('上传失败');
                    }
                }
            });

            var uploadPdf = upload.render({
                elem: '#pdf-file'
                , url: '/content/js/plugins/kindeditor/asp.net/upload_json.ashx?filetype=pdf'
                , done: function (res) {
                    if (res.error == 0)
                    {
                        $('#pdfurl').val(res.url);
                        $('#pdf-img-url').removeClass('hidden');
                        return layer.msg('上传成功');
                    } else {
                        return layer.msg(res.message);
                    }
                }
            });
        });

        $(function () {
            $('#article-form').validator({
                theme: 'yellow_right_effect',
                timely: 1,
                rules: {
                    ToRoleId: function (element, params) {
                        if ($('#ToRoleId').val() == '0')
                            return '请选择项目分类';
                    },
                },
                valid: function (form) {
                    $.ajax({
                        type: 'POST',
                        url: '@(Url.Action("articlesave", "adminapi"))',
                        data: $('#article-form').serialize(),
                        success: function (data) {
                            if (data.code == 0) {
                                window.location.href = '@(Url.Action("articles", "article"))';
                            } else {
                                layer.msg(data.msg);
                            }
                        },
                        error: function () {
                            layer.msg("很抱歉，请求异常！");
                        }
                    });
                }
            });

            $('#goback').on('click', function () {
                window.history.go(-1);
            });

            $('#dosave').on('click', function () {
                if ($('#ToRoleId').val() == '0')
                    layer.msg("请选择项目分类");
                $('#status').val(@((int)ArticleStatus.editing));
                $.ajax({
                    type: 'POST',
                    url: '@(Url.Action("articlesave", "adminapi"))',
                    data: $('#article-form').serialize(),
                    success: function (result) {
                        var editID = result.data.Id;
                        $('#id').val(editID);
                        $('#status').val(@((int)ProjectStatus.editing));
                        layer.msg("当前编辑保存成功，编辑完请按发布按钮！");
                    },
                    error: function () {
                        layer.msg("很抱歉，请求异常！");
                    }
                });
                return;
            });

            $('#dopub').on('click', function () {
                $('#status').val(@((int)ArticleStatus.normal));
                $('#article-form').submit();
                return;
            });
        });
    </script>
}
