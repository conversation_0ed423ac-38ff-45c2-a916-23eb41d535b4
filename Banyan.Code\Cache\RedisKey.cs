﻿namespace Banyan.Code
{
    public class RedisKey
    {
        public static readonly string article_model = "article_model_{0}"; //{0}：文章Id

        public static readonly string smscode_verify = "smscode_verify_{0}_{1}"; //{0}：手机号 {1}：类型

        public static readonly string article_views = "smscode_views_temp_{0}"; //{0}：文章Id

        public static readonly string manager_model = "manager_model_{0}"; //{0}：管理员Id

        public static readonly string member_model = "member_model_{0}"; //{0}：用户Id
        public static readonly string member_list = "member_list";
        public static readonly string member_list_exclusive = "member_list_exclusive_{0}"; //{0} limit medical

        public static readonly string all_staff_list = "all_staff_list_{0}"; //{0} 是否去掉离职
        public static readonly string all_staff_list_exclusive = "all_staff_list_{0}_{1}"; //{0} 是否去掉离职

        public static readonly string role_model = "role_model_{0}"; //{0}：角色Id

        public static readonly string role_valid_list = "role_valid_list";

        public static readonly string classify_model = "classify_model_{0}"; //{0}：分类Id

        public static readonly string access_token_info = "access_token_info"; //{0}：分类Id

        public static readonly string Project_model = "project_model_{0}"; //{0}：项目Id

        public static readonly string Project_views = "smscode_views_temp_{0}"; //{0}：文章Id

        public static readonly string Meet_model = "meet_model_{0}";  //{0}：会议Id
        public static readonly string Research_model = "research_model_{0}";  //{0}：会议Id
        public static readonly string Research_attachment = "research_attachment_{0}";
        public static readonly string portfolio_model = "portfolio_model_{0}"; //{0}：portfolio Id

        public static readonly string daily_project_view_count = "{0}daily{1}_project_view"; //{0}：portfolio Id
        public static readonly string daily_project_view_detail = "{0}daily{1}_project_detail"; //{0}：portfolio Id

        public static readonly string daily_summary_view_count = "{0}daily{1}_summary_view"; //{0}：portfolio Id

        public static readonly string ViewPortfolioInfo_model = "ViewPortfolioInfo_model_{0}"; //{0}：portfolio id
        public static readonly string Project_Active_model = "Project_Active_model_{0}"; //{0}：portfolio id
        public static readonly string PortfolioBasicInfo_model = "PortfolioBasicInfo_model_{0}"; //{0}：portfolio id
        public static readonly string Project_ActiveStatus_model = "Project_ActiveStatus_model_{0}"; //{0}： id

        public static readonly string attachment_comb = "attachment_{0}";
        public static readonly string attachment_comb_fms = "attachment_fms_{0}";
        public static readonly string attachment_comb_research = "attachment_comb_research_{0}_{1}";
    }

    public enum VerifyType
    {
        identity = 1,
    }
}
