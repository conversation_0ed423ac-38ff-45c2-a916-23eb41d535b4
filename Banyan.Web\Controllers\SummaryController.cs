﻿using Banyan.Apps;
using System.Web.Mvc;

namespace Banyan.Web.Controllers
{

    public class SummaryController : BaseController
    {
        public ActionResult Summary()
        {
            var rolelist = new RoleBLL().GetList();
            ViewData["rolelist"] = rolelist;
            return View();
        }

        public ActionResult Charts()
        {
            ViewData["creatorList"] = new MemberBLL().GetAllList();
            return View();
        }

        public ActionResult Preview(string id = "")
        {
            ViewBag.Id = id;
            return View();
        }

        public ActionResult PerformanceReport(string id = "")
        {
            ViewData["creatorList"] = new MemberBLL().GetAllListLimitPartner();
            return View();
        }
    }
}