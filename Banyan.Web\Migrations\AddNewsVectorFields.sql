-- Migration script to add vector-related fields to News table
-- Date: 2025-07-17

-- Check if NewsVector column exists, if not add it
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[News]') AND name = 'NewsVector')
BEGIN
    ALTER TABLE News ADD NewsVector NVARCHAR(MAX);
    PRINT 'Added NewsVector column to News table';
END

-- Check if VectorUpdateTime column exists, if not add it
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[News]') AND name = 'VectorUpdateTime')
BEGIN
    ALTER TABLE News ADD VectorUpdateTime DATETIME DEFAULT GETDATE();
    PRINT 'Added VectorUpdateTime column to News table';
END

-- Check if TagAnalysis column exists, if not add it
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[News]') AND name = 'TagAnalysis')
BEGIN
    ALTER TABLE News ADD TagAnalysis NVARCHAR(MAX);
    PRINT 'Added TagAnalysis column to News table';
END

-- Check if VectorStatus column exists, if not add it
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[News]') AND name = 'VectorStatus')
BEGIN
    ALTER TABLE News ADD VectorStatus INT DEFAULT 0;
    PRINT 'Added VectorStatus column to News table';
END

-- Check if VectorError column exists, if not add it
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[News]') AND name = 'VectorError')
BEGIN
    ALTER TABLE News ADD VectorError NVARCHAR(500);
    PRINT 'Added VectorError column to News table';
END

-- Create index on VectorStatus for faster queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_News_VectorStatus' AND object_id = OBJECT_ID('News'))
BEGIN
    CREATE INDEX IX_News_VectorStatus ON News(VectorStatus);
    PRINT 'Created index IX_News_VectorStatus on News table';
END

-- Create index on VectorUpdateTime for faster queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_News_VectorUpdateTime' AND object_id = OBJECT_ID('News'))
BEGIN
    CREATE INDEX IX_News_VectorUpdateTime ON News(VectorUpdateTime);
    PRINT 'Created index IX_News_VectorUpdateTime on News table';
END

PRINT 'Migration completed successfully';