-- Migration script to add recommendation-related tables
-- Date: 2025-07-21

-- Create NewsRecommendations table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[NewsRecommendations]') AND type in (N'U'))
BEGIN
    CREATE TABLE NewsRecommendations (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        UserId INT NOT NULL,
        NewsId INT NOT NULL,
        RelevanceScore FLOAT NOT NULL,
        GeneratedTime DATETIME DEFAULT GETDATE(),
        IsRead BIT DEFAULT 0,
        IsClicked BIT DEFAULT 0,
        CONSTRAINT FK_NewsRecommendations_Users FOREIGN KEY (UserId) REFERENCES UserProfile(Id),
        CONSTRAINT FK_NewsRecommendations_News FOREIGN KEY (NewsId) REFERENCES News(Id)
    );
    PRINT 'Created NewsRecommendations table';
    
    -- Create indexes for NewsRecommendations table
    CREATE INDEX IX_NewsRecommendations_UserId ON NewsRecommendations(UserId);
    PRINT 'Created index IX_NewsRecommendations_UserId';
    
    CREATE INDEX IX_NewsRecommendations_NewsId ON NewsRecommendations(NewsId);
    PRINT 'Created index IX_NewsRecommendations_NewsId';
    
    CREATE INDEX IX_NewsRecommendations_GeneratedTime ON NewsRecommendations(GeneratedTime);
    PRINT 'Created index IX_NewsRecommendations_GeneratedTime';
    
    CREATE INDEX IX_NewsRecommendations_RelevanceScore ON NewsRecommendations(RelevanceScore DESC);
    PRINT 'Created index IX_NewsRecommendations_RelevanceScore';
END

-- Create EngagementRecords table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[EngagementRecords]') AND type in (N'U'))
BEGIN
    CREATE TABLE EngagementRecords (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        UserName NVARCHAR(100) NOT NULL,
        NewsId INT NOT NULL,
        NewsTitle NVARCHAR(255) NOT NULL,
        Timestamp DATETIME DEFAULT GETDATE(),
        Source NVARCHAR(50) NOT NULL,
        CONSTRAINT FK_EngagementRecords_News FOREIGN KEY (NewsId) REFERENCES News(Id)
    );
    PRINT 'Created EngagementRecords table';
    
    -- Create indexes for EngagementRecords table
    CREATE INDEX IX_EngagementRecords_UserName ON EngagementRecords(UserName);
    PRINT 'Created index IX_EngagementRecords_UserName';
    
    CREATE INDEX IX_EngagementRecords_NewsId ON EngagementRecords(NewsId);
    PRINT 'Created index IX_EngagementRecords_NewsId';
    
    CREATE INDEX IX_EngagementRecords_Timestamp ON EngagementRecords(Timestamp);
    PRINT 'Created index IX_EngagementRecords_Timestamp';
    
    CREATE INDEX IX_EngagementRecords_Source ON EngagementRecords(Source);
    PRINT 'Created index IX_EngagementRecords_Source';
END

-- Create EmailDigestRecords table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[EmailDigestRecords]') AND type in (N'U'))
BEGIN
    CREATE TABLE EmailDigestRecords (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        UserId INT NOT NULL,
        SentTime DATETIME DEFAULT GETDATE(),
        NewsCount INT NOT NULL,
        NewsIds NVARCHAR(MAX) NOT NULL,
        CONSTRAINT FK_EmailDigestRecords_Users FOREIGN KEY (UserId) REFERENCES UserProfile(Id)
    );
    PRINT 'Created EmailDigestRecords table';
    
    -- Create indexes for EmailDigestRecords table
    CREATE INDEX IX_EmailDigestRecords_UserId ON EmailDigestRecords(UserId);
    PRINT 'Created index IX_EmailDigestRecords_UserId';
    
    CREATE INDEX IX_EmailDigestRecords_SentTime ON EmailDigestRecords(SentTime);
    PRINT 'Created index IX_EmailDigestRecords_SentTime';
END

PRINT 'Migration completed successfully';