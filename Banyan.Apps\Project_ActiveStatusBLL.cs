﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;

namespace Banyan.Apps
{
    public class Project_ActiveStatusBLL : BaseDAL<Project_ActiveStatus>
    {
        private readonly AjaxResult ajaxResult = null;

        public Project_ActiveStatusBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        public override object Add(Project_ActiveStatus model)
        {
            ClearCache(model);
            return base.Add(model);
        }

        public override bool Update(Project_ActiveStatus model)
        {
            ClearCache(model);
            return base.Update(model);
        }

        public override bool Update(Project_ActiveStatus model, string fldList)
        {
            ClearCache(model);
            return base.Update(model, fldList);
        }

        public bool ClearCache(Project_ActiveStatus model)
        {
            try
            {
                RedisUtil.Remove(string.Format(RedisKey.Project_ActiveStatus_model, model.statusID));
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取项目缓存
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Project_ActiveStatus GetModelByCache(string id)
        {
            string cacheKey = string.Format(RedisKey.Project_ActiveStatus_model, id);
            Project_ActiveStatus model = RedisUtil.Get<Project_ActiveStatus>(cacheKey);
            if (model == null)
            {
                model = base.GetModel($"ProjectId='{id}'");
                RedisUtil.Set<Project_ActiveStatus>(cacheKey, model, TimeSpan.FromMinutes(10));
            }
            return model;
        }

        /// <summary>
        /// 操作日志更新
        /// </summary>
        /// <param name="page"></param>
        /// <param name="action"></param>
        /// <param name="description"></param>
        /// <param name="userName"></param>
        public void updateLog(string page, string action, string description, Member user)
        {
            SysLog log = new SysLog
            {
                Page = page,
                Action = action,
                Description = description,
                CreatedBy = user == null ? "" : user.RealName,
                Ip = Utility.WebHelper.GetIP(),
                CreatorId = user == null ? 0 : user.Id,
            };
            SysLogBLL logBLL = new SysLogBLL();
            logBLL.Add(log);
        }
    }
}
