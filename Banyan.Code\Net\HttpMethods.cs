﻿using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using System.Threading;


namespace Banyan.Code
{
    public class HttpMethods
    {
        public class MyDataObject
        {
            public string data { get; set; }
            // 根据实际JSON结构添加更多属性
        }
        public class Message
        {
            [JsonProperty("role")]
            public string Role { get; set; }

            [JsonProperty("content")]
            public string Content { get; set; }
        }
        public class Choice
        {
            [JsonProperty("index")]
            public int Index { get; set; }

            [JsonProperty("logprobs")]
            public object Logprobs { get; set; }

            [JsonProperty("delta")]
            public Message Delta { get; set; }

            [JsonProperty("finish_reason")]
            public string FinishReason { get; set; }

            [JsonProperty("message")]
            public Message Message { get; set; }
        }
        public class ChatCompletion
        {
            [JsonProperty("id")]
            public string Id { get; set; }

            [JsonProperty("object")]
            public string Object { get; set; }

            [JsonProperty("created")]
            public long Created { get; set; }

            [JsonProperty("model")]
            public string Model { get; set; }

            [JsonProperty("choices")]
            public List<Choice> Choices { get; set; }


        }

        public static async Task HttpLLMStreamPost(object jsonData = null, Hashtable headht = null, Action<string> callback = null, CancellationToken cancellationToken = default, string user = "")
        {
            var url = "https://llm.gaorongvc.cn/v1/chat/completions";
            
            // Log the start of LLM request with user and content information
            try
            {
                string requestContent = jsonData != null ? JsonConvert.SerializeObject(jsonData) : "null";
                Logger.Info($"LLM_REQUEST: Stream Started - User: {user}, Content Length: {requestContent.Length}, URL: {url}", user);
                
                // Log a truncated version of the content for debugging (first 500 chars)
                string truncatedContent = requestContent.Length > 500 ? requestContent.Substring(0, 500) + "..." : requestContent;
                Logger.Info($"LLM_REQUEST: Stream Content: {truncatedContent}", user);
            }
            catch (Exception logEx)
            {
                Logger.Error($"LLM_REQUEST: Error logging stream request: {logEx.Message}", logEx, user);
            }

            HttpWebRequest request = WebRequest.Create(url) as HttpWebRequest;

            if (url.StartsWith("https", StringComparison.OrdinalIgnoreCase))
            {
                ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);
                request.ProtocolVersion = HttpVersion.Version10;
            }

            request.Headers.Add("Authorization", "Bearer sk-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ1");
            request.Method = "POST";
            request.Accept = "text/event-stream"; // 重要：请求流式响应
            request.Timeout = Timeout.Infinite; // 流式请求需要无限超时

            if (jsonData != null)
            {
                string json = JsonConvert.SerializeObject(jsonData);
                request.ContentType = "application/json";
                using (Stream requestStream = await request.GetRequestStreamAsync())
                {
                    StreamWriter writer = new StreamWriter(requestStream, new UTF8Encoding(false));
                    await writer.WriteAsync(json);
                    await writer.FlushAsync();
                }
            }

            if (headht != null)
            {
                foreach (DictionaryEntry item in headht)
                {
                    request.Headers.Add(item.Key.ToString(), item.Value.ToString());
                }
            }

            try
            {
                using (WebResponse response = await request.GetResponseAsync())
                using (Stream stream = response.GetResponseStream())
                using (StreamReader reader = new StreamReader(stream))
                {
                    StringBuilder partialMessage = new StringBuilder();
                    char[] buffer = new char[4096];
                    int bytesRead;

                    while ((bytesRead = await reader.ReadAsync(buffer, 0, buffer.Length)) > 0)
                    {
                        // 检查取消令牌
                        cancellationToken.ThrowIfCancellationRequested();

                        string chunk = new string(buffer, 0, bytesRead);
                        partialMessage.Append(chunk);

                        //if (partialMessage.ToString().Contains("[DONE]"))
                        //{
                        //    callback?.Invoke("[DONE]");
                        //    return;
                        //}
                        // 处理SSE格式数据 (data: {...}\n\n)
                        var events = partialMessage.ToString().Split(new[] { "data: ", "\n\n" }, StringSplitOptions.RemoveEmptyEntries);

                        foreach (var evt in events.Take(events.Length - 1))
                        {
                            // 检查取消令牌
                            cancellationToken.ThrowIfCancellationRequested();

                            if (string.IsNullOrWhiteSpace(evt)) continue;

                            if (evt == "[DONE]")
                            {
                                callback?.Invoke("[DONE]");
                                Logger.Info($"LLM_REQUEST: Stream Completed Successfully - User: {user}", user);
                                return;
                            }
                            if (evt.StartsWith("{\"id\":"))
                            {
                                var eventData = evt;
                                var dataObject = JsonConvert.DeserializeObject<ChatCompletion>(eventData);
                                if (dataObject.Choices.Count > 0)
                                {
                                    callback?.Invoke(dataObject.Choices[0].Delta?.Content ?? "");
                                }
                            }
                        }

                        // 保留未完成的事件
                        partialMessage.Clear();
                        if (events.Length > 0)
                        {
                            partialMessage.Append(events.Last());
                        }
                    }
                    callback?.Invoke("[DONE]");
                    Logger.Info($"LLM_REQUEST: Stream Completed Successfully - User: {user}", user);
                }
            }
            catch (OperationCanceledException)
            {
                // 请求被取消，不调用callback，直接返回
                Logger.Info($"LLM_REQUEST: Stream Cancelled - User: {user}", user);
                return;
            }
            catch (Exception e)
            {
                Logger.Error($"LLM_REQUEST: Stream Failed - User: {user}, Error: {e.Message}", e, user);
                callback?.Invoke($"错误: {e.Message}");
            }
        }


        public static string HttpLLMPost(object jsonData = null, Hashtable headht = null, string user = "")
        {
            HttpWebRequest request;
            var url = "https://llm.gaorongvc.cn/v1/chat/completions";
            
            // Log the start of LLM request with user and content information
            try
            {
                string requestContent = jsonData != null ? JsonConvert.SerializeObject(jsonData) : "null";
                Logger.Info($"LLM_REQUEST: Non-Stream Started - User: {user}, Content Length: {requestContent.Length}, URL: {url}", user);
                
                // Log a truncated version of the content for debugging (first 500 chars)
                string truncatedContent = requestContent.Length > 500 ? requestContent.Substring(0, 500) + "..." : requestContent;
                Logger.Info($"LLM_REQUEST: Non-Stream Content: {truncatedContent}", user);
            }
            catch (Exception logEx)
            {
                Logger.Error($"LLM_REQUEST: Error logging non-stream request: {logEx.Message}", logEx, user);
            }

            //如果是发送HTTPS请求  
            if (url.StartsWith("https", StringComparison.OrdinalIgnoreCase))
            {
                ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);
                request = WebRequest.Create(url) as HttpWebRequest;
                request.ProtocolVersion = HttpVersion.Version10;
            }
            else
            {
                request = WebRequest.Create(url) as HttpWebRequest;
            }
            request.Headers.Add("Authorization", "Bearer sk-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ1");
            request.Method = "Post";
            //request.ContentType = "application/x-www-form-urlencoded";
            request.Accept = "*/*";
            request.Timeout = 240000;
            request.AllowAutoRedirect = true;


            if (jsonData != null)
            {
                string json = JsonConvert.SerializeObject(jsonData);
                request.ContentType = "application/json"; // 设置请求头以发送JSON格式的请求体
                using (Stream requestStream = request.GetRequestStream())
                {
                    StreamWriter writer = new StreamWriter(requestStream, new UTF8Encoding(false));
                    writer.Write(json);
                    writer.Flush();
                }
            }

            if (headht != null)
            {
                foreach (DictionaryEntry item in headht)
                {
                    request.Headers.Add(item.Key.ToString(), item.Value.ToString());
                }
            }
            //WebResponse response = null;
            //string responseStr = null;
            try
            {
                using (WebResponse response = request.GetResponse())
                {
                    using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
                    {
                        var responseStr = reader.ReadToEnd();
                        ChatCompletion dataObject = JsonConvert.DeserializeObject<ChatCompletion>(responseStr);
                        
                        Logger.Info($"LLM_REQUEST: Non-Stream Completed Successfully - User: {user}, Response Length: {responseStr.Length}", user);
                        return dataObject.Choices[0].Message.Content;
                    }
                }
                //response = request.GetResponse();

                //if (response != null)
                //{
                //    StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8);
                //    responseStr = reader.ReadToEnd();
                //    reader.Close();
                //}
            }
            catch (Exception e)
            {
                Logger.Error($"LLM_REQUEST: Non-Stream Failed - User: {user}, Error: {e.Message}", e, user);
                throw e;
            }
        }

        public static bool HttpLLMPing()
        {
            HttpWebRequest request;
            var url = "https://llm.gaorongvc.cn/v1/models";
            //如果是发送HTTPS请求  
            if (url.StartsWith("https", StringComparison.OrdinalIgnoreCase))
            {
                ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);
                request = WebRequest.Create(url) as HttpWebRequest;
                request.ProtocolVersion = HttpVersion.Version10;
            }
            else
            {
                request = WebRequest.Create(url) as HttpWebRequest;
            }
            request.Headers.Add("Authorization", "Bearer sk-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ1");
            request.Method = "Get";
            request.Accept = "*/*";
            request.Timeout = 600;
            request.AllowAutoRedirect = true;

            try
            {
                using (WebResponse response = request.GetResponse())
                {
                    using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
                    {
                        var responseStr = reader.ReadToEnd();
                        return responseStr.Contains("deepseek-r1-0528");
                    }
                }
            }
            catch (Exception e)
            {
                return false;
            }
        }

        /// <summary>
        /// 调用Embedding服务
        /// </summary>
        /// <param name="jsonData">请求数据</param>
        /// <param name="headht">请求头</param>
        /// <param name="user">用户标识</param>
        /// <returns>Embedding响应</returns>
        public static string HttpEmbeddingPost(object jsonData = null, Hashtable headht = null, string user = "")
        {
            HttpWebRequest request;
            var url = "https://llm.gaorongvc.cn/v1/embeddings";
            
            // Log the start of Embedding request with user and content information
            try
            {
                string requestContent = jsonData != null ? JsonConvert.SerializeObject(jsonData) : "null";
                Logger.Info($"EMBEDDING_REQUEST: Started - User: {user}, Content Length: {requestContent.Length}, URL: {url}", user);
                
                // Log a truncated version of the content for debugging (first 500 chars)
                string truncatedContent = requestContent.Length > 500 ? requestContent.Substring(0, 500) + "..." : requestContent;
                Logger.Info($"EMBEDDING_REQUEST: Content: {truncatedContent}", user);
            }
            catch (Exception logEx)
            {
                Logger.Error($"EMBEDDING_REQUEST: Error logging request: {logEx.Message}", logEx, user);
            }

            //如果是发送HTTPS请求  
            if (url.StartsWith("https", StringComparison.OrdinalIgnoreCase))
            {
                ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);
                request = WebRequest.Create(url) as HttpWebRequest;
                request.ProtocolVersion = HttpVersion.Version10;
            }
            else
            {
                request = WebRequest.Create(url) as HttpWebRequest;
            }
            request.Headers.Add("Authorization", "Bearer sk-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ1");
            request.Method = "Post";
            request.Accept = "*/*";
            request.Timeout = 60000; // Embedding可能需要更长时间
            request.AllowAutoRedirect = true;

            if (jsonData != null)
            {
                string json = JsonConvert.SerializeObject(jsonData);
                request.ContentType = "application/json";
                using (Stream requestStream = request.GetRequestStream())
                {
                    StreamWriter writer = new StreamWriter(requestStream, new UTF8Encoding(false));
                    writer.Write(json);
                    writer.Flush();
                }
            }

            if (headht != null)
            {
                foreach (DictionaryEntry item in headht)
                {
                    request.Headers.Add(item.Key.ToString(), item.Value.ToString());
                }
            }

            try
            {
                using (WebResponse response = request.GetResponse())
                {
                    using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
                    {
                        var responseStr = reader.ReadToEnd();
                        
                        Logger.Info($"EMBEDDING_REQUEST: Completed Successfully - User: {user}, Response Length: {responseStr.Length}", user);
                        return responseStr; // 直接返回原始JSON响应，让调用方解析
                    }
                }
            }
            catch (Exception e)
            {
                Logger.Error($"EMBEDDING_REQUEST: Failed - User: {user}, Error: {e.Message}", e, user);
                throw e;
            }
        }
        #region POST
        /// <summary>
        /// HTTP POST方式请求数据
        /// </summary>
        /// <param name="url">URL.</param>
        /// <param name="param">POST的数据</param>
        /// <returns></returns>
        public static string HttpPost(string url, string param = null)
        {
            HttpWebRequest request;

            //如果是发送HTTPS请求  
            if (url.StartsWith("https", StringComparison.OrdinalIgnoreCase))
            {
                ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);
                request = WebRequest.Create(url) as HttpWebRequest;
                request.ProtocolVersion = HttpVersion.Version10;
            }
            else
            {
                request = WebRequest.Create(url) as HttpWebRequest;
            }

            request.Method = "POST";
            request.ContentType = "application/x-www-form-urlencoded";
            request.Accept = "*/*";
            request.Timeout = 15000;
            request.AllowAutoRedirect = false;



            StreamWriter requestStream = null;
            WebResponse response = null;
            string responseStr = null;

            try
            {
                requestStream = new StreamWriter(request.GetRequestStream());
                requestStream.Write(param);
                requestStream.Close();

                response = request.GetResponse();
                if (response != null)
                {
                    StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8);
                    responseStr = reader.ReadToEnd();
                    reader.Close();
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                request = null;
                requestStream = null;
                response = null;
            }

            return responseStr;
        }


        private static bool CheckValidationResult(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors)
        {
            return true; //总是接受  
        }
        public static string BuildRequest(string strUrl, Dictionary<string, string> dicPara, string fileName)
        {
            string contentType = "image/jpeg";
            //待请求参数数组
            FileStream Pic = new FileStream(fileName, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
            byte[] PicByte = new byte[Pic.Length];
            Pic.Read(PicByte, 0, PicByte.Length);
            int lengthFile = PicByte.Length;

            //构造请求地址

            //设置HttpWebRequest基本信息
            HttpWebRequest request = (HttpWebRequest)HttpWebRequest.Create(strUrl);
            //设置请求方式：get、post
            request.Method = "POST";
            //设置boundaryValue
            string boundaryValue = DateTime.Now.Ticks.ToString("x");
            string boundary = "--" + boundaryValue;
            request.ContentType = "\r\nmultipart/form-data; boundary=" + boundaryValue;
            //设置KeepAlive
            request.KeepAlive = true;
            //设置请求数据，拼接成字符串
            StringBuilder sbHtml = new StringBuilder();
            foreach (KeyValuePair<string, string> key in dicPara)
            {
                sbHtml.Append(boundary + "\r\nContent-Disposition: form-data; name=\"" + key.Key + "\"\r\n\r\n" + key.Value + "\r\n");
            }
            sbHtml.Append(boundary + "\r\nContent-Disposition: form-data; name=\"pic\"; filename=\"");
            sbHtml.Append(fileName);
            sbHtml.Append("\"\r\nContent-Type: " + contentType + "\r\n\r\n");
            string postHeader = sbHtml.ToString();
            //将请求数据字符串类型根据编码格式转换成字节流
            Encoding code = Encoding.GetEncoding("UTF-8");
            byte[] postHeaderBytes = code.GetBytes(postHeader);
            byte[] boundayBytes = Encoding.ASCII.GetBytes("\r\n" + boundary + "--\r\n");
            //设置长度
            long length = postHeaderBytes.Length + lengthFile + boundayBytes.Length;
            request.ContentLength = length;

            //请求远程HTTP
            Stream requestStream = request.GetRequestStream();
            Stream myStream = null;
            try
            {
                //发送数据请求服务器
                requestStream.Write(postHeaderBytes, 0, postHeaderBytes.Length);
                requestStream.Write(PicByte, 0, lengthFile);
                requestStream.Write(boundayBytes, 0, boundayBytes.Length);
                HttpWebResponse HttpWResp = (HttpWebResponse)request.GetResponse();
                myStream = HttpWResp.GetResponseStream();
            }
            catch
            {
                return string.Empty;
            }
            finally
            {
                if (requestStream != null)
                {
                    requestStream.Close();
                }
            }

            //读取处理结果
            StreamReader reader = new StreamReader(myStream, code);
            StringBuilder responseData = new StringBuilder();

            String line;
            while ((line = reader.ReadLine()) != null)
            {
                responseData.Append(line);
            }
            myStream.Close();
            Pic.Close();

            return responseData.ToString();
        }
        #endregion

        #region Put
        /// <summary>
        /// HTTP Put方式请求数据.
        /// </summary>
        /// <param name="url">URL.</param>
        /// <returns></returns>
        public static string HttpPut(string url, string param = null)
        {
            HttpWebRequest request;

            //如果是发送HTTPS请求  
            if (url.StartsWith("https", StringComparison.OrdinalIgnoreCase))
            {
                ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);
                request = WebRequest.Create(url) as HttpWebRequest;
                request.ProtocolVersion = HttpVersion.Version10;
            }
            else
            {
                request = WebRequest.Create(url) as HttpWebRequest;
            }
            request.Method = "PUT";
            request.ContentType = "application/x-www-form-urlencoded";
            request.Accept = "*/*";
            request.Timeout = 15000;
            request.AllowAutoRedirect = false;

            StreamWriter requestStream = null;
            WebResponse response = null;
            string responseStr = null;

            try
            {
                requestStream = new StreamWriter(request.GetRequestStream());
                requestStream.Write(param);
                requestStream.Close();

                response = request.GetResponse();
                if (response != null)
                {
                    StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8);
                    responseStr = reader.ReadToEnd();
                    reader.Close();
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                request = null;
                requestStream = null;
                response = null;
            }

            return responseStr;
        }
        #endregion

        #region Delete
        /// <summary>
        /// HTTP Delete方式请求数据.
        /// </summary>
        /// <param name="url">URL.</param>
        /// <returns></returns>
        public static string HttpDelete(string url, string param = null)
        {
            HttpWebRequest request;

            //如果是发送HTTPS请求  
            if (url.StartsWith("https", StringComparison.OrdinalIgnoreCase))
            {
                ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);
                request = WebRequest.Create(url) as HttpWebRequest;
                request.ProtocolVersion = HttpVersion.Version10;
            }
            else
            {
                request = WebRequest.Create(url) as HttpWebRequest;
            }
            request.Method = "Delete";
            request.ContentType = "application/x-www-form-urlencoded";
            request.Accept = "*/*";
            request.Timeout = 15000;
            request.AllowAutoRedirect = false;

            StreamWriter requestStream = null;
            WebResponse response = null;
            string responseStr = null;

            try
            {
                requestStream = new StreamWriter(request.GetRequestStream());
                requestStream.Write(param);
                requestStream.Close();

                response = request.GetResponse();
                if (response != null)
                {
                    StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8);
                    responseStr = reader.ReadToEnd();
                    reader.Close();
                }
            }
            catch (Exception)
            {
                throw;
            }
            return responseStr;
        }
        #endregion

        #region Get
        /// <summary>
        /// HTTP GET方式请求数据.
        /// </summary>
        /// <param name="url">URL.</param>
        /// <returns></returns>
        public static string HttpGet(string url, Hashtable headht = null)
        {
            HttpWebRequest request;

            //如果是发送HTTPS请求  
            if (url.StartsWith("https", StringComparison.OrdinalIgnoreCase))
            {
                ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);
                request = WebRequest.Create(url) as HttpWebRequest;
                request.ProtocolVersion = HttpVersion.Version10;
            }
            else
            {
                request = WebRequest.Create(url) as HttpWebRequest;
            }
            request.Method = "GET";
            //request.ContentType = "application/x-www-form-urlencoded";
            request.Accept = "*/*";
            request.Timeout = 15000;
            request.AllowAutoRedirect = false;
            WebResponse response = null;
            string responseStr = null;
            if (headht != null)
            {
                foreach (DictionaryEntry item in headht)
                {
                    request.Headers.Add(item.Key.ToString(), item.Value.ToString());
                }
            }

            try
            {
                response = request.GetResponse();

                if (response != null)
                {
                    StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8);
                    responseStr = reader.ReadToEnd();
                    reader.Close();
                }
            }
            catch (Exception)
            {
                throw;
            }
            return responseStr;
        }
        public static string HttpGet(string url, Encoding encodeing, Hashtable headht = null)
        {
            HttpWebRequest request;

            //如果是发送HTTPS请求  
            if (url.StartsWith("https", StringComparison.OrdinalIgnoreCase))
            {
                ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);
                request = WebRequest.Create(url) as HttpWebRequest;
                request.ProtocolVersion = HttpVersion.Version10;
            }
            else
            {
                request = WebRequest.Create(url) as HttpWebRequest;
            }
            request.Method = "GET";
            //request.ContentType = "application/x-www-form-urlencoded";
            request.Accept = "*/*";
            request.Timeout = 15000;
            request.AllowAutoRedirect = false;
            WebResponse response = null;
            string responseStr = null;
            if (headht != null)
            {
                foreach (DictionaryEntry item in headht)
                {
                    request.Headers.Add(item.Key.ToString(), item.Value.ToString());
                }
            }

            try
            {
                response = request.GetResponse();

                if (response != null)
                {
                    StreamReader reader = new StreamReader(response.GetResponseStream(), encodeing);
                    responseStr = reader.ReadToEnd();
                    reader.Close();
                }
            }
            catch (Exception)
            {
                throw;
            }
            return responseStr;
        }
        #endregion

        #region Post With Pic
        private string HttpPost(string url, IDictionary<object, object> param, string filePath)
        {
            string boundary = "---------------------------" + DateTime.Now.Ticks.ToString("x");
            byte[] boundarybytes = System.Text.Encoding.ASCII.GetBytes("\r\n--" + boundary + "\r\n");

            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(url);
            wr.ContentType = "multipart/form-data; boundary=" + boundary;
            wr.Method = "POST";
            wr.KeepAlive = true;
            wr.Credentials = System.Net.CredentialCache.DefaultCredentials;

            Stream rs = wr.GetRequestStream();
            string responseStr = null;

            string formdataTemplate = "Content-Disposition: form-data; name=\"{0}\"\r\n\r\n{1}";
            foreach (string key in param.Keys)
            {
                rs.Write(boundarybytes, 0, boundarybytes.Length);
                string formitem = string.Format(formdataTemplate, key, param[key]);
                byte[] formitembytes = System.Text.Encoding.UTF8.GetBytes(formitem);
                rs.Write(formitembytes, 0, formitembytes.Length);
            }
            rs.Write(boundarybytes, 0, boundarybytes.Length);

            string headerTemplate = "Content-Disposition: form-data; name=\"{0}\"; filename=\"{1}\"\r\nContent-Type: {2}\r\n\r\n";
            string header = string.Format(headerTemplate, "pic", filePath, "text/plain");
            byte[] headerbytes = System.Text.Encoding.UTF8.GetBytes(header);
            rs.Write(headerbytes, 0, headerbytes.Length);

            FileStream fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            byte[] buffer = new byte[4096];
            int bytesRead = 0;
            while ((bytesRead = fileStream.Read(buffer, 0, buffer.Length)) != 0)
            {
                rs.Write(buffer, 0, bytesRead);
            }
            fileStream.Close();

            byte[] trailer = System.Text.Encoding.ASCII.GetBytes("\r\n--" + boundary + "--\r\n");
            rs.Write(trailer, 0, trailer.Length);
            rs.Close();

            WebResponse wresp = null;
            try
            {
                wresp = wr.GetResponse();
                Stream stream2 = wresp.GetResponseStream();
                StreamReader reader2 = new StreamReader(stream2);
                responseStr = reader2.ReadToEnd();
            }
            catch (Exception ex)
            {
                if (wresp != null)
                {
                    wresp.Close();
                    wresp = null;
                }
                throw;
            }
            return responseStr;
        }
        #endregion

        #region Post With Pic
        /// <summary>
        /// HTTP POST方式请求数据(带图片)
        /// </summary>
        /// <param name="url">URL</param>        
        /// <param name="param">POST的数据</param>
        /// <param name="fileByte">图片</param>
        /// <returns></returns>
        public static string HttpPost(string url, IDictionary<object, object> param, byte[] fileByte)
        {
            string boundary = "---------------------------" + DateTime.Now.Ticks.ToString("x");
            byte[] boundarybytes = System.Text.Encoding.ASCII.GetBytes("\r\n--" + boundary + "\r\n");

            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(url);
            wr.ContentType = "multipart/form-data; boundary=" + boundary;
            wr.Method = "POST";
            wr.KeepAlive = true;
            wr.Credentials = System.Net.CredentialCache.DefaultCredentials;

            Stream rs = wr.GetRequestStream();
            string responseStr = null;

            string formdataTemplate = "Content-Disposition: form-data; name=\"{0}\"\r\n\r\n{1}";
            foreach (string key in param.Keys)
            {
                rs.Write(boundarybytes, 0, boundarybytes.Length);
                string formitem = string.Format(formdataTemplate, key, param[key]);
                byte[] formitembytes = System.Text.Encoding.UTF8.GetBytes(formitem);
                rs.Write(formitembytes, 0, formitembytes.Length);
            }
            rs.Write(boundarybytes, 0, boundarybytes.Length);

            string headerTemplate = "Content-Disposition: form-data; name=\"{0}\"; filename=\"{1}\"\r\nContent-Type: {2}\r\n\r\n";
            string header = string.Format(headerTemplate, "pic", fileByte, "text/plain");//image/jpeg
            byte[] headerbytes = System.Text.Encoding.UTF8.GetBytes(header);
            rs.Write(headerbytes, 0, headerbytes.Length);

            rs.Write(fileByte, 0, fileByte.Length);

            byte[] trailer = System.Text.Encoding.ASCII.GetBytes("\r\n--" + boundary + "--\r\n");
            rs.Write(trailer, 0, trailer.Length);
            rs.Close();

            WebResponse wresp = null;
            try
            {
                wresp = wr.GetResponse();
                Stream stream2 = wresp.GetResponseStream();
                StreamReader reader2 = new StreamReader(stream2);
                responseStr = reader2.ReadToEnd();
            }
            catch (Exception ex)
            {
                if (wresp != null)
                {
                    wresp.Close();
                    wresp = null;
                }
                throw;
            }
            return responseStr;
        }
        #endregion

        //#region HttpsClient
        ///// <summary>
        ///// 创建HttpClient
        ///// </summary>
        ///// <returns></returns>
        //public static HttpClient CreateHttpClient(string url)
        //{
        //    HttpClient httpclient;
        //    //如果是发送HTTPS请求  
        //    if (url.StartsWith("https", StringComparison.OrdinalIgnoreCase))
        //    {
        //        ServicePointManager.ServerCertificateValidationCallback += (sender, cert, chain, sslPolicyErrors) => true;
        //        httpclient = new HttpClient();
        //    }
        //    else
        //    {
        //        httpclient = new HttpClient();
        //    }
        //    return httpclient;
        //}
        //#endregion
    }
}
