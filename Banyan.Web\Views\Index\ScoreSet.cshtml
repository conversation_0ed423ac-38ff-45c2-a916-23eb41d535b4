﻿@model Banyan.Domain.Member
@{
    Layout = null;
    var roleList = (List<Banyan.Domain.Role>)ViewData["rolelist"];
}

<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>高榕资本-GAORONG VENTURES</title>

    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" href="~/content/img/favicons/favicon.ico">
    <link rel="icon" type="image/png" href="~/content/img/favicons/favicon.ico" sizes="16x16">
    <title>添加评分活动</title>
    <link type="text/css" href="/content/js/plugins/layui/css/layui.css" rel="stylesheet" />
    <style>
        .layui-form-label {
            padding: 9px 0;
        }

        .layui-input-block {
            margin-left: 90px;
        }
    </style>
</head>
<body>
    <div style="padding:50px 20px 20px">
        <div class="layui-form" id="user-form" style="display:none;">
            <div class="layui-form-item">
                <label class="layui-form-label">轮次名称：</label>
                <div class="layui-input-block">
                    <input type="text" name="actname" id="actname" v-model="actname" placeholder="请输入轮次名称" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <a href="javascript:;" class="layui-btn" @@click="create">创建</a>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="~/Content/js/core/jquery.min.js"></script>
    <script type="text/javascript" src="~/content/js/plugins/layui/layui.js"></script>
    <script type="text/javascript" src="/content/js/vue/vue.min.js"></script>

    <script type="text/javascript">
        var layer, form, id=@(Convert.ToInt32(ViewData["id"]));
        layui.use(['layer','form'], function () {
            layer = layui.layer;
            form = layui.form;
        });

        var app = new Vue({
            el: '#user-form',
            data: {
                id: id,
                actname: '',
            },
            methods: {
                create: function () {
                    var that = this;
                    var pdata = {
                        ActName: that.actname,
                        ProjectId: id,
                    }
                    if (that.actname.trim() == '') {
                        layer.msg('请输入轮次名称');
                        return;
                    }
                    $.ajax({
                        type: 'post',
                        url: '@(Url.Action("scoreset", "adminapi"))',
                        data: pdata,
                        success: function (data) {
                            if (data.code == 0) {
                                parent.layer.closeAll();
                                parent.location.reload();
                            } else {
                                layer.msg(data.msg);
                            }
                        },
                        error: function () {
                            layer.msg("很抱歉，请求异常！");
                        }
                    });
                }
            },
            created: function () {
                $("#user-form").show();
            }
        });
    </script>
</body>
</html>
