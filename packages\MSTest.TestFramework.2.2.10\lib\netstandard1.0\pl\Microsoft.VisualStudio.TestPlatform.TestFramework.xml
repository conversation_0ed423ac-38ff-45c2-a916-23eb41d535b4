<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod">
            <summary>
            Metoda TestMethod do wykonania.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestMethodName">
            <summary>
            Pobiera nazwę metody testowej.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestClassName">
            <summary>
            Pobiera nazwę klasy testowej.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ReturnType">
            <summary>
            Pobiera zwracany typ metody testowej.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ParameterTypes">
            <summary>
            Pobiera parametry metody testowej.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.MethodInfo">
            <summary>
            Pobiera element methodInfo dla metody testowej.
            </summary>
            <remarks>
            This is just to retrieve additional information about the method.
            Do not directly invoke the method using MethodInfo. Use ITestMethod.Invoke instead.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.Invoke(System.Object[])">
            <summary>
            Wywołuje metodę testową.
            </summary>
            <param name="arguments">
            Argumenty przekazywane do metody testowej (np. w przypadku opartej na danych)
            </param>
            <returns>
            Wynik wywołania metody testowej.
            </returns>
            <remarks>
            This call handles asynchronous test methods as well.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAllAttributes(System.Boolean)">
            <summary>
            Pobierz wszystkie atrybuty metody testowej.
            </summary>
            <param name="inherit">
            Informacja o tym, czy atrybut zdefiniowany w klasie nadrzędnej jest prawidłowy.
            </param>
            <returns>
            Wszystkie atrybuty.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAttributes``1(System.Boolean)">
            <summary>
            Pobierz atrybut określonego typu.
            </summary>
            <typeparam name="AttributeType"> System.Attribute type. </typeparam>
            <param name="inherit">
            Informacja o tym, czy atrybut zdefiniowany w klasie nadrzędnej jest prawidłowy.
            </param>
            <returns>
            Atrybuty określonego typu.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Helper">
            <summary>
            Element pomocniczy.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNull(System.Object,System.String,System.String)">
            <summary>
            Sprawdzany parametr nie ma wartości null.
            </summary>
            <param name="param">
            Parametr.
            </param>
            <param name="parameterName">
            Nazwa parametru.
            </param>
            <param name="message">
            Komunikat.
            </param>
            <exception cref="T:System.ArgumentNullException"> Throws argument null exception when parameter is null. </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNullOrEmpty(System.String,System.String,System.String)">
            <summary>
            Sprawdzany parametr nie ma wartości null i nie jest pusty.
            </summary>
            <param name="param">
            Parametr.
            </param>
            <param name="parameterName">
            Nazwa parametru.
            </param>
            <param name="message">
            Komunikat.
            </param>
            <exception cref="T:System.ArgumentException"> Throws ArgumentException when parameter is null. </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod">
            <summary>
            Wyliczenie dotyczące sposobu dostępu do wierszy danych w teście opartym na danych.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Sequential">
            <summary>
            Wiersze są zwracane po kolei.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random">
            <summary>
            Wiersze są zwracane w kolejności losowej.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute">
            <summary>
            Atrybut do definiowania danych wbudowanych dla metody testowej.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/>.
            </summary>
            <param name="data1"> Obiekt danych. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object,System.Object[])">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/>, które pobiera tablicę argumentów.
            </summary>
            <param name="data1"> Obiekt danych. </param>
            <param name="moreData"> Więcej danych. </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.Data">
            <summary>
            Pobiera dane do wywoływania metody testowej.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.DisplayName">
            <summary>
            Pobiera lub ustawia nazwę wyświetlaną w wynikach testu do dostosowania.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            <summary>
            Wyjątek niejednoznacznej asercji.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String,System.Exception)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
            <param name="msg"> Komunikat. </param>
            <param name="ex"> Wyjątek. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
            <param name="msg"> Komunikat. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException">
            <summary>
            Klasa InternalTestFailureException. Używana do określenia wewnętrznego błędu przypadku testowego
            </summary>
            <remarks>
            This class is only added to preserve source compatibility with the V1 framework.
            For all practical purposes either use AssertFailedException/AssertInconclusiveException.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String,System.Exception)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
            <param name="msg"> Komunikat wyjątku. </param>
            <param name="ex"> Wyjątek. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
            <param name="msg"> Komunikat wyjątku. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute">
            <summary>
            Atrybut określający, że jest oczekiwany wyjątek określonego typu
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> z oczekiwanym typem
            </summary>
            <param name="exceptionType">Typ oczekiwanego wyjątku</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type,System.String)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> z
            oczekiwanym typem i komunikatem do uwzględnienia, gdy test nie zgłasza żadnego wyjątku.
            </summary>
            <param name="exceptionType">Typ oczekiwanego wyjątku</param>
            <param name="noExceptionMessage">
            Komunikat do dołączenia do wyniku testu, jeśli test nie powiedzie się, ponieważ nie zostanie zgłoszony wyjątek
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.ExceptionType">
            <summary>
            Pobiera wartość wskazującą typ oczekiwanego wyjątku
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.AllowDerivedTypes">
            <summary>
            Pobiera lub ustawia wartość wskazującą, czy typy pochodne typu oczekiwanego wyjątku
            są traktowane jako oczekiwane
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.NoExceptionMessage">
            <summary>
            Pobiera komunikat do uwzględnienia w wyniku testu, jeśli test nie powiedzie się z powodu niezgłoszenia wyjątku
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.Verify(System.Exception)">
            <summary>
            Weryfikuje, czy typ wyjątku zgłoszonego przez test jednostkowy jest oczekiwany
            </summary>
            <param name="exception">Wyjątek zgłoszony przez test jednostkowy</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute">
            <summary>
            Klasa podstawowa dla atrybutów, które określają, że jest oczekiwany wyjątek z testu jednostkowego
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> z domyślnym komunikatem o braku wyjątku
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor(System.String)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> z komunikatem o braku wyjątku
            </summary>
            <param name="noExceptionMessage">
            Komunikat do dołączenia do wyniku testu, jeśli test nie powiedzie się, ponieważ
            nie zostanie zgłoszony wyjątek
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.NoExceptionMessage">
            <summary>
            Pobiera komunikat do uwzględnienia w wyniku testu, jeśli test nie powiedzie się z powodu niezgłoszenia wyjątku
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.SpecifiedNoExceptionMessage">
            <summary>
            Pobiera komunikat do uwzględnienia w wyniku testu, jeśli test nie powiedzie się z powodu niezgłoszenia wyjątku
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.GetDefaultNoExceptionMessage(System.String)">
            <summary>
            Pobiera domyślny komunikat bez wyjątku
            </summary>
            <param name="expectedExceptionAttributeTypeName">Nazwa typu atrybutu ExpectedException</param>
            <returns>Domyślny komunikat bez wyjątku</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.Verify(System.Exception)">
            <summary>
            Określa, czy wyjątek jest oczekiwany. Jeśli wykonanie metody zakończy się normalnie, oznacza to,
            że wyjątek był oczekiwany. Jeśli metoda zgłosi wyjątek, oznacza to,
            że wyjątek nie był oczekiwany, a komunikat zgłoszonego wyjątku
            jest dołączony do wyniku testu. Klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert"/> można użyć dla
            wygody. Jeśli zostanie użyta klasa <see cref="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive"/> i asercja nie powiedzie się,
            wynik testu zostanie ustawiony jako Niejednoznaczny.
            </summary>
            <param name="exception">Wyjątek zgłoszony przez test jednostkowy</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.RethrowIfAssertException(System.Exception)">
            <summary>
            Zgłoś ponownie wyjątek, jeśli jest to wyjątek AssertFailedException lub AssertInconclusiveException
            </summary>
            <param name="exception">Wyjątek do ponownego zgłoszenia, jeśli jest to wyjątek asercji</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper">
            <summary>
            Ta klasa jest zaprojektowana w taki sposób, aby pomóc użytkownikowi wykonującemu testy jednostkowe dla typów używających typów ogólnych.
            Element GenericParameterHelper zachowuje niektóre typowe ograniczenia typów ogólnych,
            takie jak:
            1. publiczny konstruktor domyślny
            2. implementuje wspólny interfejs: IComparable, IEnumerable
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>, które
            spełnia ograniczenie „newable” w typach ogólnych języka C#.
            </summary>
            <remarks>
            This constructor initializes the Data property to a random value.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor(System.Int32)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>, które 
            inicjuje właściwość Data wartością dostarczoną przez użytkownika.
            </summary>
            <param name="data">Dowolna liczba całkowita</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Data">
            <summary>
            Pobiera lub ustawia element Data
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Equals(System.Object)">
            <summary>
            Wykonuje porównanie wartości dwóch obiektów GenericParameterHelper
            </summary>
            <param name="obj">obiekt, z którym ma zostać wykonane porównanie</param>
            <returns>Wartość true, jeśli obiekt ma tę samą wartość co obiekt „this” typu GenericParameterHelper.
            W przeciwnym razie wartość false.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetHashCode">
            <summary>
            Zwraca wartość skrótu tego obiektu.
            </summary>
            <returns>Kod skrótu.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.CompareTo(System.Object)">
            <summary>
            Porównuje dane dwóch obiektów <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </summary>
            <param name="obj">Obiekt do porównania.</param>
            <returns>
            Liczba ze znakiem, która wskazuje wartości względne tego wystąpienia i wartości.
            </returns>
            <exception cref="T:System.NotSupportedException">
            Thrown when the object passed in is not an instance of <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetEnumerator">
            <summary>
            Zwraca obiekt IEnumerator, którego długość jest określona na podstawie
            właściwości Data.
            </summary>
            <returns>Obiekt IEnumerator</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Clone">
            <summary>
            Zwraca obiekt GenericParameterHelper równy
            bieżącemu obiektowi.
            </summary>
            <returns>Sklonowany obiekt.</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger">
            <summary>
            Umożliwia użytkownikom rejestrowanie/zapisywanie śladów z testów jednostek w celach diagnostycznych.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessageHandler">
            <summary>
            Procedura obsługi elementu LogMessage.
            </summary>
            <param name="message">Komunikat do zarejestrowania.</param>
        </member>
        <member name="E:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.OnLogMessage">
            <summary>
            Zdarzenie, które ma być nasłuchiwane. Zgłaszane, gdy składnik zapisywania testu jednostkowego zapisze jakiś komunikat.
            Zwykle zużywane przez adapter.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessage(System.String,System.Object[])">
            <summary>
            Interfejs API składnika zapisywania testu do wywołania na potrzeby rejestrowania komunikatów.
            </summary>
            <param name="format">Format ciągu z symbolami zastępczymi.</param>
            <param name="args">Parametry dla symboli zastępczych.</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute">
            <summary>
            Atrybut TestCategory używany do określenia kategorii testu jednostkowego.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.#ctor(System.String)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute"/> i stosuje kategorię do testu.
            </summary>
            <param name="testCategory">
            Kategoria testu.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.TestCategories">
            <summary>
            Pobiera kategorie testu, które zostały zastosowane do testu.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute">
            <summary>
            Klasa podstawowa atrybutu „Category”
            </summary>
            <remarks>
            The reason for this attribute is to let the users create their own implementation of test categories.
            - test framework (discovery, etc) deals with TestCategoryBaseAttribute.
            - The reason that TestCategories property is a collection rather than a string,
              is to give more flexibility to the user. For instance the implementation may be based on enums for which the values can be OR'ed
              in which case it makes sense to have single attribute rather than multiple ones on the same test.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.#ctor">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute"/>.
            Stosuje kategorię do testu. Ciągi zwrócone przez element TestCategories
            są używane w poleceniu /category do filtrowania testów
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.TestCategories">
            <summary>
            Pobiera kategorię testu, która została zastosowana do testu.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            <summary>
            Klasa AssertFailedException. Używana do wskazania niepowodzenia przypadku testowego
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
            <param name="msg"> Komunikat. </param>
            <param name="ex"> Wyjątek. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
            <param name="msg"> Komunikat. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert">
            <summary>
            Kolekcja klas pomocniczych na potrzeby testowania różnych warunków w ramach
            testów jednostkowych. Jeśli testowany warunek nie zostanie spełniony, zostanie zgłoszony
            wyjątek.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.That">
            <summary>
            Pobiera pojedyncze wystąpienie funkcji Assert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void IsOfType&lt;T&gt;(this Assert assert, object obj)"
            Users could then use a syntax similar to the default assertions which in this case is "Assert.That.IsOfType&lt;Dog&gt;(animal);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean)">
            <summary>
            Testuje, czy określony warunek ma wartość true, i zgłasza wyjątek,
            jeśli warunek ma wartość false.
            </summary>
            <param name="condition">
            Warunek, którego wartość oczekiwana przez test to true.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String)">
            <summary>
            Testuje, czy określony warunek ma wartość true, i zgłasza wyjątek,
            jeśli warunek ma wartość false.
            </summary>
            <param name="condition">
            Warunek, którego wartość oczekiwana przez test to true.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="condition"/>
            ma wartość false. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String,System.Object[])">
            <summary>
            Testuje, czy określony warunek ma wartość true, i zgłasza wyjątek,
            jeśli warunek ma wartość false.
            </summary>
            <param name="condition">
            Warunek, którego wartość oczekiwana przez test to true.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="condition"/>
            ma wartość false. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean)">
            <summary>
            Testuje, czy określony warunek ma wartość false, i zgłasza wyjątek,
            jeśli warunek ma wartość true.
            </summary>
            <param name="condition">
            Warunek, którego wartość oczekiwana przez test to false.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String)">
            <summary>
            Testuje, czy określony warunek ma wartość false, i zgłasza wyjątek,
            jeśli warunek ma wartość true.
            </summary>
            <param name="condition">
            Warunek, którego wartość oczekiwana przez test to false.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="condition"/>
            ma wartość true. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String,System.Object[])">
            <summary>
            Testuje, czy określony warunek ma wartość false, i zgłasza wyjątek,
            jeśli warunek ma wartość true.
            </summary>
            <param name="condition">
            Warunek, którego wartość oczekiwana przez test to false.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="condition"/>
            ma wartość true. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object)">
            <summary>
            Testuje, czy określony obiekt ma wartość null, i zgłasza wyjątek,
            jeśli ma inną wartość.
            </summary>
            <param name="value">
            Obiekt, którego wartość oczekiwana przez test to null.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String)">
            <summary>
            Testuje, czy określony obiekt ma wartość null, i zgłasza wyjątek,
            jeśli ma inną wartość.
            </summary>
            <param name="value">
            Obiekt, którego wartość oczekiwana przez test to null.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="value"/>
            nie ma wartości null. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String,System.Object[])">
            <summary>
            Testuje, czy określony obiekt ma wartość null, i zgłasza wyjątek,
            jeśli ma inną wartość.
            </summary>
            <param name="value">
            Obiekt, którego wartość oczekiwana przez test to null.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="value"/>
            nie ma wartości null. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object)">
            <summary>
            Testuje, czy określony obiekt ma wartość inną niż null, i zgłasza wyjątek,
            jeśli ma wartość null.
            </summary>
            <param name="value">
            Obiekt, którego wartość oczekiwana przez test jest inna niż null.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String)">
            <summary>
            Testuje, czy określony obiekt ma wartość inną niż null, i zgłasza wyjątek,
            jeśli ma wartość null.
            </summary>
            <param name="value">
            Obiekt, którego wartość oczekiwana przez test jest inna niż null.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="value"/>
            ma wartość null. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String,System.Object[])">
            <summary>
            Testuje, czy określony obiekt ma wartość inną niż null, i zgłasza wyjątek,
            jeśli ma wartość null.
            </summary>
            <param name="value">
            Obiekt, którego wartość oczekiwana przez test jest inna niż null.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="value"/>
            ma wartość null. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object)">
            <summary>
            Testuje, czy oba określone obiekty przywołują ten sam obiekt,
            i zgłasza wyjątek, jeśli dwa obiekty wejściowe nie przywołują tego samego obiektu.
            </summary>
            <param name="expected">
            Pierwszy obiekt do porównania. To jest wartość, której oczekuje test.
            </param>
            <param name="actual">
            Drugi obiekt do porównania. To jest wartość utworzona przez testowany kod.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String)">
            <summary>
            Testuje, czy oba określone obiekty przywołują ten sam obiekt,
            i zgłasza wyjątek, jeśli dwa obiekty wejściowe nie przywołują tego samego obiektu.
            </summary>
            <param name="expected">
            Pierwszy obiekt do porównania. To jest wartość, której oczekuje test.
            </param>
            <param name="actual">
            Drugi obiekt do porównania. To jest wartość utworzona przez testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            nie jest tym samym elementem co <paramref name="expected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Testuje, czy oba określone obiekty przywołują ten sam obiekt,
            i zgłasza wyjątek, jeśli dwa obiekty wejściowe nie przywołują tego samego obiektu.
            </summary>
            <param name="expected">
            Pierwszy obiekt do porównania. To jest wartość, której oczekuje test.
            </param>
            <param name="actual">
            Drugi obiekt do porównania. To jest wartość utworzona przez testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            nie jest tym samym elementem co <paramref name="expected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object)">
            <summary>
            Testuje, czy określone obiekty przywołują inne obiekty,
            i zgłasza wyjątek, jeśli dwa obiekty wejściowe przywołują ten sam obiekt.
            </summary>
            <param name="notExpected">
            Pierwszy obiekt do porównania. To jest wartość, która zgodnie z testem powinna
            nie pasować do elementu <paramref name="actual"/>.
            </param>
            <param name="actual">
            Drugi obiekt do porównania. To jest wartość utworzona przez testowany kod.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String)">
            <summary>
            Testuje, czy określone obiekty przywołują inne obiekty,
            i zgłasza wyjątek, jeśli dwa obiekty wejściowe przywołują ten sam obiekt.
            </summary>
            <param name="notExpected">
            Pierwszy obiekt do porównania. To jest wartość, która zgodnie z testem powinna
            nie pasować do elementu <paramref name="actual"/>.
            </param>
            <param name="actual">
            Drugi obiekt do porównania. To jest wartość utworzona przez testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest taki sam jak element <paramref name="notExpected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Testuje, czy określone obiekty przywołują inne obiekty,
            i zgłasza wyjątek, jeśli dwa obiekty wejściowe przywołują ten sam obiekt.
            </summary>
            <param name="notExpected">
            Pierwszy obiekt do porównania. To jest wartość, która zgodnie z testem powinna
            nie pasować do elementu <paramref name="actual"/>.
            </param>
            <param name="actual">
            Drugi obiekt do porównania. To jest wartość utworzona przez testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest taki sam jak element <paramref name="notExpected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0)">
            <summary>
            Testuje, czy określone wartości są równe, i zgłasza wyjątek,
            jeśli dwie wartości są różne. Różne typy liczbowe są traktowane
            jako różne, nawet jeśli wartości logiczne są równe. Wartość 42L jest różna od wartości 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Pierwsza wartość do porównania. To jest wartość, której oczekuje test.
            </param>
            <param name="actual">
            Druga wartość do porównania. To jest wartość utworzona przez testowany kod.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String)">
            <summary>
            Testuje, czy określone wartości są równe, i zgłasza wyjątek,
            jeśli dwie wartości są różne. Różne typy liczbowe są traktowane
            jako różne, nawet jeśli wartości logiczne są równe. Wartość 42L jest różna od wartości 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Pierwsza wartość do porównania. To jest wartość, której oczekuje test.
            </param>
            <param name="actual">
            Druga wartość do porównania. To jest wartość utworzona przez testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            nie jest równy elementowi <paramref name="expected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Testuje, czy określone wartości są równe, i zgłasza wyjątek,
            jeśli dwie wartości są różne. Różne typy liczbowe są traktowane
            jako różne, nawet jeśli wartości logiczne są równe. Wartość 42L jest różna od wartości 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Pierwsza wartość do porównania. To jest wartość, której oczekuje test.
            </param>
            <param name="actual">
            Druga wartość do porównania. To jest wartość utworzona przez testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            nie jest równy elementowi <paramref name="expected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0)">
            <summary>
            Testuje, czy określone wartości są różne, i zgłasza wyjątek,
            jeśli dwie wartości są równe. Różne typy liczbowe są traktowane
            jako różne, nawet jeśli wartości logiczne są równe. Wartość 42L jest różna od wartości 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Pierwsza wartość do porównania. To jest wartość, która według testu
            nie powinna pasować <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druga wartość do porównania. To jest wartość utworzona przez testowany kod.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String)">
            <summary>
            Testuje, czy określone wartości są różne, i zgłasza wyjątek,
            jeśli dwie wartości są równe. Różne typy liczbowe są traktowane
            jako różne, nawet jeśli wartości logiczne są równe. Wartość 42L jest różna od wartości 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Pierwsza wartość do porównania. To jest wartość, która według testu
            nie powinna pasować <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druga wartość do porównania. To jest wartość utworzona przez testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest równy elementowi <paramref name="notExpected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Testuje, czy określone wartości są różne, i zgłasza wyjątek,
            jeśli dwie wartości są równe. Różne typy liczbowe są traktowane
            jako różne, nawet jeśli wartości logiczne są równe. Wartość 42L jest różna od wartości 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Pierwsza wartość do porównania. To jest wartość, która według testu
            nie powinna pasować <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druga wartość do porównania. To jest wartość utworzona przez testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest równy elementowi <paramref name="notExpected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object)">
            <summary>
            Testuje, czy określone obiekty są równe, i zgłasza wyjątek,
            jeśli dwa obiekty są różne. Różne typy liczbowe są traktowane
            jako różne, nawet jeśli wartości logiczne są równe. Wartość 42L jest różna od wartości 42.
            </summary>
            <param name="expected">
            Pierwszy obiekt do porównania. To jest obiekt, którego oczekuje test.
            </param>
            <param name="actual">
            Drugi obiekt do porównania. To jest obiekt utworzony przez testowany kod.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String)">
            <summary>
            Testuje, czy określone obiekty są równe, i zgłasza wyjątek,
            jeśli dwa obiekty są różne. Różne typy liczbowe są traktowane
            jako różne, nawet jeśli wartości logiczne są równe. Wartość 42L jest różna od wartości 42.
            </summary>
            <param name="expected">
            Pierwszy obiekt do porównania. To jest obiekt, którego oczekuje test.
            </param>
            <param name="actual">
            Drugi obiekt do porównania. To jest obiekt utworzony przez testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            nie jest równy elementowi <paramref name="expected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Testuje, czy określone obiekty są równe, i zgłasza wyjątek,
            jeśli dwa obiekty są różne. Różne typy liczbowe są traktowane
            jako różne, nawet jeśli wartości logiczne są równe. Wartość 42L jest różna od wartości 42.
            </summary>
            <param name="expected">
            Pierwszy obiekt do porównania. To jest obiekt, którego oczekuje test.
            </param>
            <param name="actual">
            Drugi obiekt do porównania. To jest obiekt utworzony przez testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            nie jest równy elementowi <paramref name="expected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object)">
            <summary>
            Testuje, czy określone obiekty są różne, i zgłasza wyjątek,
            jeśli dwa obiekty są równe. Różne typy liczbowe są traktowane
            jako różne, nawet jeśli wartości logiczne są równe. Wartość 42L jest różna od wartości 42.
            </summary>
            <param name="notExpected">
            Pierwszy obiekt do porównania. To jest wartość, która zgodnie z testem powinna
            nie pasować do elementu <paramref name="actual"/>.
            </param>
            <param name="actual">
            Drugi obiekt do porównania. To jest obiekt utworzony przez testowany kod.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String)">
            <summary>
            Testuje, czy określone obiekty są różne, i zgłasza wyjątek,
            jeśli dwa obiekty są równe. Różne typy liczbowe są traktowane
            jako różne, nawet jeśli wartości logiczne są równe. Wartość 42L jest różna od wartości 42.
            </summary>
            <param name="notExpected">
            Pierwszy obiekt do porównania. To jest wartość, która zgodnie z testem powinna
            nie pasować do elementu <paramref name="actual"/>.
            </param>
            <param name="actual">
            Drugi obiekt do porównania. To jest obiekt utworzony przez testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest równy elementowi <paramref name="notExpected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Testuje, czy określone obiekty są różne, i zgłasza wyjątek,
            jeśli dwa obiekty są równe. Różne typy liczbowe są traktowane
            jako różne, nawet jeśli wartości logiczne są równe. Wartość 42L jest różna od wartości 42.
            </summary>
            <param name="notExpected">
            Pierwszy obiekt do porównania. To jest wartość, która zgodnie z testem powinna
            nie pasować do elementu <paramref name="actual"/>.
            </param>
            <param name="actual">
            Drugi obiekt do porównania. To jest obiekt utworzony przez testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest równy elementowi <paramref name="notExpected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single)">
            <summary>
            Testuje, czy określone wartości zmiennoprzecinkowe są równe, i zgłasza wyjątek,
            jeśli są różne.
            </summary>
            <param name="expected">
            Pierwsza wartość zmiennoprzecinkowa do porównania. To jest wartość zmiennoprzecinkowa, której oczekuje test.
            </param>
            <param name="actual">
            Druga wartość zmiennoprzecinkowa do porównania. To jest wartość zmiennoprzecinkowa utworzona przez testowany kod.
            </param>
            <param name="delta">
            Wymagana dokładność. Wyjątek zostanie zgłoszony, tylko jeśli
            <paramref name="actual"/> jest różny od elementu <paramref name="expected"/>
            o więcej niż <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Testuje, czy określone wartości zmiennoprzecinkowe są równe, i zgłasza wyjątek,
            jeśli są różne.
            </summary>
            <param name="expected">
            Pierwsza wartość zmiennoprzecinkowa do porównania. To jest wartość zmiennoprzecinkowa, której oczekuje test.
            </param>
            <param name="actual">
            Druga wartość zmiennoprzecinkowa do porównania. To jest wartość zmiennoprzecinkowa utworzona przez testowany kod.
            </param>
            <param name="delta">
            Wymagana dokładność. Wyjątek zostanie zgłoszony, tylko jeśli
            <paramref name="actual"/> jest różny od elementu <paramref name="expected"/>
            o więcej niż <paramref name="delta"/>.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest różny od elementu <paramref name="expected"/> o więcej niż
            <paramref name="delta"/>. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Testuje, czy określone wartości zmiennoprzecinkowe są równe, i zgłasza wyjątek,
            jeśli są różne.
            </summary>
            <param name="expected">
            Pierwsza wartość zmiennoprzecinkowa do porównania. To jest wartość zmiennoprzecinkowa, której oczekuje test.
            </param>
            <param name="actual">
            Druga wartość zmiennoprzecinkowa do porównania. To jest wartość zmiennoprzecinkowa utworzona przez testowany kod.
            </param>
            <param name="delta">
            Wymagana dokładność. Wyjątek zostanie zgłoszony, tylko jeśli
            <paramref name="actual"/> jest różny od elementu <paramref name="expected"/>
            o więcej niż <paramref name="delta"/>.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest różny od elementu <paramref name="expected"/> o więcej niż
            <paramref name="delta"/>. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single)">
            <summary>
            Testuje, czy określone wartości zmiennoprzecinkowe są różne, i zgłasza wyjątek,
            jeśli są równe.
            </summary>
            <param name="notExpected">
            Pierwsza wartość zmiennoprzecinkowa do porównania. Test oczekuje, że ta wartość zmiennoprzecinkowa nie będzie
            zgodna z elementem <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druga wartość zmiennoprzecinkowa do porównania. To jest wartość zmiennoprzecinkowa utworzona przez testowany kod.
            </param>
            <param name="delta">
            Wymagana dokładność. Wyjątek zostanie zgłoszony, tylko jeśli
            <paramref name="actual"/> jest różny od elementu <paramref name="notExpected"/>
            o co najwyżej <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Testuje, czy określone wartości zmiennoprzecinkowe są różne, i zgłasza wyjątek,
            jeśli są równe.
            </summary>
            <param name="notExpected">
            Pierwsza wartość zmiennoprzecinkowa do porównania. Test oczekuje, że ta wartość zmiennoprzecinkowa nie będzie
            zgodna z elementem <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druga wartość zmiennoprzecinkowa do porównania. To jest wartość zmiennoprzecinkowa utworzona przez testowany kod.
            </param>
            <param name="delta">
            Wymagana dokładność. Wyjątek zostanie zgłoszony, tylko jeśli
            <paramref name="actual"/> jest różny od elementu <paramref name="notExpected"/>
            o co najwyżej <paramref name="delta"/>.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest równy elementowi <paramref name="notExpected"/> lub różny o mniej niż
            <paramref name="delta"/>. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Testuje, czy określone wartości zmiennoprzecinkowe są różne, i zgłasza wyjątek,
            jeśli są równe.
            </summary>
            <param name="notExpected">
            Pierwsza wartość zmiennoprzecinkowa do porównania. Test oczekuje, że ta wartość zmiennoprzecinkowa nie będzie
            zgodna z elementem <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druga wartość zmiennoprzecinkowa do porównania. To jest wartość zmiennoprzecinkowa utworzona przez testowany kod.
            </param>
            <param name="delta">
            Wymagana dokładność. Wyjątek zostanie zgłoszony, tylko jeśli
            <paramref name="actual"/> jest różny od elementu <paramref name="notExpected"/>
            o co najwyżej <paramref name="delta"/>.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest równy elementowi <paramref name="notExpected"/> lub różny o mniej niż
            <paramref name="delta"/>. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double)">
            <summary>
            Testuje, czy określone wartości podwójnej precyzji są równe, i zgłasza wyjątek,
            jeśli są różne.
            </summary>
            <param name="expected">
            Pierwsza wartość podwójnej precyzji do porównania. To jest wartość podwójnej precyzji, której oczekuje test.
            </param>
            <param name="actual">
            Druga wartość podwójnej precyzji do porównania. To jest wartość podwójnej precyzji utworzona przez testowany kod.
            </param>
            <param name="delta">
            Wymagana dokładność. Wyjątek zostanie zgłoszony, tylko jeśli
            <paramref name="actual"/> jest różny od elementu <paramref name="expected"/>
            o więcej niż <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Testuje, czy określone wartości podwójnej precyzji są równe, i zgłasza wyjątek,
            jeśli są różne.
            </summary>
            <param name="expected">
            Pierwsza wartość podwójnej precyzji do porównania. To jest wartość podwójnej precyzji, której oczekuje test.
            </param>
            <param name="actual">
            Druga wartość podwójnej precyzji do porównania. To jest wartość podwójnej precyzji utworzona przez testowany kod.
            </param>
            <param name="delta">
            Wymagana dokładność. Wyjątek zostanie zgłoszony, tylko jeśli
            <paramref name="actual"/> jest różny od elementu <paramref name="expected"/>
            o więcej niż <paramref name="delta"/>.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest różny od elementu <paramref name="expected"/> o więcej niż
            <paramref name="delta"/>. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Testuje, czy określone wartości podwójnej precyzji są równe, i zgłasza wyjątek,
            jeśli są różne.
            </summary>
            <param name="expected">
            Pierwsza wartość podwójnej precyzji do porównania. To jest wartość podwójnej precyzji, której oczekuje test.
            </param>
            <param name="actual">
            Druga wartość podwójnej precyzji do porównania. To jest wartość podwójnej precyzji utworzona przez testowany kod.
            </param>
            <param name="delta">
            Wymagana dokładność. Wyjątek zostanie zgłoszony, tylko jeśli
            <paramref name="actual"/> jest różny od elementu <paramref name="expected"/>
            o więcej niż <paramref name="delta"/>.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest różny od elementu <paramref name="expected"/> o więcej niż
            <paramref name="delta"/>. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double)">
            <summary>
            Testuje, czy określone wartości podwójnej precyzji są różne, i zgłasza wyjątek,
            jeśli są równe.
            </summary>
            <param name="notExpected">
            Pierwsza wartość podwójnej precyzji do porównania. Test oczekuje, że ta wartość podwójnej precyzji
            nie będzie pasować do elementu <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druga wartość podwójnej precyzji do porównania. To jest wartość podwójnej precyzji utworzona przez testowany kod.
            </param>
            <param name="delta">
            Wymagana dokładność. Wyjątek zostanie zgłoszony, tylko jeśli
            <paramref name="actual"/> jest różny od elementu <paramref name="notExpected"/>
            o co najwyżej <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Testuje, czy określone wartości podwójnej precyzji są różne, i zgłasza wyjątek,
            jeśli są równe.
            </summary>
            <param name="notExpected">
            Pierwsza wartość podwójnej precyzji do porównania. Test oczekuje, że ta wartość podwójnej precyzji
            nie będzie pasować do elementu <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druga wartość podwójnej precyzji do porównania. To jest wartość podwójnej precyzji utworzona przez testowany kod.
            </param>
            <param name="delta">
            Wymagana dokładność. Wyjątek zostanie zgłoszony, tylko jeśli
            <paramref name="actual"/> jest różny od elementu <paramref name="notExpected"/>
            o co najwyżej <paramref name="delta"/>.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest równy elementowi <paramref name="notExpected"/> lub różny o mniej niż
            <paramref name="delta"/>. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Testuje, czy określone wartości podwójnej precyzji są różne, i zgłasza wyjątek,
            jeśli są równe.
            </summary>
            <param name="notExpected">
            Pierwsza wartość podwójnej precyzji do porównania. Test oczekuje, że ta wartość podwójnej precyzji
            nie będzie pasować do elementu <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druga wartość podwójnej precyzji do porównania. To jest wartość podwójnej precyzji utworzona przez testowany kod.
            </param>
            <param name="delta">
            Wymagana dokładność. Wyjątek zostanie zgłoszony, tylko jeśli
            <paramref name="actual"/> jest różny od elementu <paramref name="notExpected"/>
            o co najwyżej <paramref name="delta"/>.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest równy elementowi <paramref name="notExpected"/> lub różny o mniej niż
            <paramref name="delta"/>. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean)">
            <summary>
            Testuje, czy określone ciągi są równe, i zgłasza wyjątek,
            jeśli są różne. Na potrzeby tego porównania jest używana niezmienna kultura.
            </summary>
            <param name="expected">
            Pierwszy ciąg do porównania. To jest ciąg, którego oczekuje test.
            </param>
            <param name="actual">
            Drugi ciąg do porównania. To jest ciąg utworzony przez testowany kod.
            </param>
            <param name="ignoreCase">
            Wartość logiczna wskazująca, czy porównanie uwzględnia wielkość liter. (Wartość true
            wskazuje porównanie bez uwzględniania wielkości liter).
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Testuje, czy określone ciągi są równe, i zgłasza wyjątek,
            jeśli są różne. Na potrzeby tego porównania jest używana niezmienna kultura.
            </summary>
            <param name="expected">
            Pierwszy ciąg do porównania. To jest ciąg, którego oczekuje test.
            </param>
            <param name="actual">
            Drugi ciąg do porównania. To jest ciąg utworzony przez testowany kod.
            </param>
            <param name="ignoreCase">
            Wartość logiczna wskazująca, czy porównanie uwzględnia wielkość liter. (Wartość true
            wskazuje porównanie bez uwzględniania wielkości liter).
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            nie jest równy elementowi <paramref name="expected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Testuje, czy określone ciągi są równe, i zgłasza wyjątek,
            jeśli są różne. Na potrzeby tego porównania jest używana niezmienna kultura.
            </summary>
            <param name="expected">
            Pierwszy ciąg do porównania. To jest ciąg, którego oczekuje test.
            </param>
            <param name="actual">
            Drugi ciąg do porównania. To jest ciąg utworzony przez testowany kod.
            </param>
            <param name="ignoreCase">
            Wartość logiczna wskazująca, czy porównanie uwzględnia wielkość liter. (Wartość true
            wskazuje porównanie bez uwzględniania wielkości liter).
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            nie jest równy elementowi <paramref name="expected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Testuje, czy określone ciągi są równe, i zgłasza wyjątek,
            jeśli są różne.
            </summary>
            <param name="expected">
            Pierwszy ciąg do porównania. To jest ciąg, którego oczekuje test.
            </param>
            <param name="actual">
            Drugi ciąg do porównania. To jest ciąg utworzony przez testowany kod.
            </param>
            <param name="ignoreCase">
            Wartość logiczna wskazująca, czy porównanie uwzględnia wielkość liter. (Wartość true
            wskazuje porównanie bez uwzględniania wielkości liter).
            </param>
            <param name="culture">
            Obiekt CultureInfo, który określa informacje dotyczące porównania specyficznego dla kultury.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Testuje, czy określone ciągi są równe, i zgłasza wyjątek,
            jeśli są różne.
            </summary>
            <param name="expected">
            Pierwszy ciąg do porównania. To jest ciąg, którego oczekuje test.
            </param>
            <param name="actual">
            Drugi ciąg do porównania. To jest ciąg utworzony przez testowany kod.
            </param>
            <param name="ignoreCase">
            Wartość logiczna wskazująca, czy porównanie uwzględnia wielkość liter. (Wartość true
            wskazuje porównanie bez uwzględniania wielkości liter).
            </param>
            <param name="culture">
            Obiekt CultureInfo, który określa informacje dotyczące porównania specyficznego dla kultury.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            nie jest równy elementowi <paramref name="expected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Testuje, czy określone ciągi są równe, i zgłasza wyjątek,
            jeśli są różne.
            </summary>
            <param name="expected">
            Pierwszy ciąg do porównania. To jest ciąg, którego oczekuje test.
            </param>
            <param name="actual">
            Drugi ciąg do porównania. To jest ciąg utworzony przez testowany kod.
            </param>
            <param name="ignoreCase">
            Wartość logiczna wskazująca, czy porównanie uwzględnia wielkość liter. (Wartość true
            wskazuje porównanie bez uwzględniania wielkości liter).
            </param>
            <param name="culture">
            Obiekt CultureInfo, który określa informacje dotyczące porównania specyficznego dla kultury.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            nie jest równy elementowi <paramref name="expected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean)">
            <summary>
            Testuje, czy określone ciągi są różne, i zgłasza wyjątek,
            jeśli są równe. Na potrzeby tego porównania jest używana niezmienna kultura.
            </summary>
            <param name="notExpected">
            Pierwszy ciąg do porównania. To jest ciąg, który według testu
            nie powinien pasować do elementu <paramref name="actual"/>.
            </param>
            <param name="actual">
            Drugi ciąg do porównania. To jest ciąg utworzony przez testowany kod.
            </param>
            <param name="ignoreCase">
            Wartość logiczna wskazująca, czy porównanie uwzględnia wielkość liter. (Wartość true
            wskazuje porównanie bez uwzględniania wielkości liter).
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Testuje, czy określone ciągi są różne, i zgłasza wyjątek,
            jeśli są równe. Na potrzeby tego porównania jest używana niezmienna kultura.
            </summary>
            <param name="notExpected">
            Pierwszy ciąg do porównania. To jest ciąg, który według testu
            nie powinien pasować do elementu <paramref name="actual"/>.
            </param>
            <param name="actual">
            Drugi ciąg do porównania. To jest ciąg utworzony przez testowany kod.
            </param>
            <param name="ignoreCase">
            Wartość logiczna wskazująca, czy porównanie uwzględnia wielkość liter. (Wartość true
            wskazuje porównanie bez uwzględniania wielkości liter).
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest równy elementowi <paramref name="notExpected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Testuje, czy określone ciągi są różne, i zgłasza wyjątek,
            jeśli są równe. Na potrzeby tego porównania jest używana niezmienna kultura.
            </summary>
            <param name="notExpected">
            Pierwszy ciąg do porównania. To jest ciąg, który według testu
            nie powinien pasować do elementu <paramref name="actual"/>.
            </param>
            <param name="actual">
            Drugi ciąg do porównania. To jest ciąg utworzony przez testowany kod.
            </param>
            <param name="ignoreCase">
            Wartość logiczna wskazująca, czy porównanie uwzględnia wielkość liter. (Wartość true
            wskazuje porównanie bez uwzględniania wielkości liter).
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest równy elementowi <paramref name="notExpected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Testuje, czy określone ciągi są różne, i zgłasza wyjątek,
            jeśli są równe.
            </summary>
            <param name="notExpected">
            Pierwszy ciąg do porównania. To jest ciąg, który według testu
            nie powinien pasować do elementu <paramref name="actual"/>.
            </param>
            <param name="actual">
            Drugi ciąg do porównania. To jest ciąg utworzony przez testowany kod.
            </param>
            <param name="ignoreCase">
            Wartość logiczna wskazująca, czy porównanie uwzględnia wielkość liter. (Wartość true
            wskazuje porównanie bez uwzględniania wielkości liter).
            </param>
            <param name="culture">
            Obiekt CultureInfo, który określa informacje dotyczące porównania specyficznego dla kultury.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Testuje, czy określone ciągi są różne, i zgłasza wyjątek,
            jeśli są równe.
            </summary>
            <param name="notExpected">
            Pierwszy ciąg do porównania. To jest ciąg, który według testu
            nie powinien pasować do elementu <paramref name="actual"/>.
            </param>
            <param name="actual">
            Drugi ciąg do porównania. To jest ciąg utworzony przez testowany kod.
            </param>
            <param name="ignoreCase">
            Wartość logiczna wskazująca, czy porównanie uwzględnia wielkość liter. (Wartość true
            wskazuje porównanie bez uwzględniania wielkości liter).
            </param>
            <param name="culture">
            Obiekt CultureInfo, który określa informacje dotyczące porównania specyficznego dla kultury.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest równy elementowi <paramref name="notExpected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Testuje, czy określone ciągi są różne, i zgłasza wyjątek,
            jeśli są równe.
            </summary>
            <param name="notExpected">
            Pierwszy ciąg do porównania. To jest ciąg, który według testu
            nie powinien pasować do elementu <paramref name="actual"/>.
            </param>
            <param name="actual">
            Drugi ciąg do porównania. To jest ciąg utworzony przez testowany kod.
            </param>
            <param name="ignoreCase">
            Wartość logiczna wskazująca, czy porównanie uwzględnia wielkość liter. (Wartość true
            wskazuje porównanie bez uwzględniania wielkości liter).
            </param>
            <param name="culture">
            Obiekt CultureInfo, który określa informacje dotyczące porównania specyficznego dla kultury.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest równy elementowi <paramref name="notExpected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type)">
            <summary>
            Testuje, czy określony obiekt jest wystąpieniem oczekiwanego
            typu, i zgłasza wyjątek, jeśli oczekiwany typ nie należy
            do hierarchii dziedziczenia obiektu.
            </summary>
            <param name="value">
            Obiekt, który według testu powinien być określonego typu.
            </param>
            <param name="expectedType">
            Oczekiwany typ elementu <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Testuje, czy określony obiekt jest wystąpieniem oczekiwanego
            typu, i zgłasza wyjątek, jeśli oczekiwany typ nie należy
            do hierarchii dziedziczenia obiektu.
            </summary>
            <param name="value">
            Obiekt, który według testu powinien być określonego typu.
            </param>
            <param name="expectedType">
            Oczekiwany typ elementu <paramref name="value"/>.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="value"/>
            nie jest wystąpieniem typu <paramref name="expectedType"/>. Komunikat
            jest wyświetlony w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Testuje, czy określony obiekt jest wystąpieniem oczekiwanego
            typu, i zgłasza wyjątek, jeśli oczekiwany typ nie należy
            do hierarchii dziedziczenia obiektu.
            </summary>
            <param name="value">
            Obiekt, który według testu powinien być określonego typu.
            </param>
            <param name="expectedType">
            Oczekiwany typ elementu <paramref name="value"/>.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="value"/>
            nie jest wystąpieniem typu <paramref name="expectedType"/>. Komunikat
            jest wyświetlony w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type)">
            <summary>
            Testuje, czy określony obiekt nie jest wystąpieniem nieprawidłowego
            typu, i zgłasza wyjątek, jeśli podany typ należy
            do hierarchii dziedziczenia obiektu.
            </summary>
            <param name="value">
            Obiekt, który według testu nie powinien być określonego typu.
            </param>
            <param name="wrongType">
            Element <paramref name="value"/> nie powinien być tego typu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Testuje, czy określony obiekt nie jest wystąpieniem nieprawidłowego
            typu, i zgłasza wyjątek, jeśli podany typ należy
            do hierarchii dziedziczenia obiektu.
            </summary>
            <param name="value">
            Obiekt, który według testu nie powinien być określonego typu.
            </param>
            <param name="wrongType">
            Element <paramref name="value"/> nie powinien być tego typu.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="value"/>
            jest wystąpieniem typu <paramref name="wrongType"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Testuje, czy określony obiekt nie jest wystąpieniem nieprawidłowego
            typu, i zgłasza wyjątek, jeśli podany typ należy
            do hierarchii dziedziczenia obiektu.
            </summary>
            <param name="value">
            Obiekt, który według testu nie powinien być określonego typu.
            </param>
            <param name="wrongType">
            Element <paramref name="value"/> nie powinien być tego typu.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="value"/>
            jest wystąpieniem typu <paramref name="wrongType"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail">
            <summary>
            Zgłasza wyjątek AssertFailedException.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String)">
            <summary>
            Zgłasza wyjątek AssertFailedException.
            </summary>
            <param name="message">
            Komunikat do dołączenia do wyjątku. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String,System.Object[])">
            <summary>
            Zgłasza wyjątek AssertFailedException.
            </summary>
            <param name="message">
            Komunikat do dołączenia do wyjątku. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive">
            <summary>
            Zgłasza wyjątek AssertInconclusiveException.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String)">
            <summary>
            Zgłasza wyjątek AssertInconclusiveException.
            </summary>
            <param name="message">
            Komunikat do dołączenia do wyjątku. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String,System.Object[])">
            <summary>
            Zgłasza wyjątek AssertInconclusiveException.
            </summary>
            <param name="message">
            Komunikat do dołączenia do wyjątku. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Equals(System.Object,System.Object)">
            <summary>
            Statyczne przeciążenia metody equals są używane do porównywania wystąpień dwóch typów pod kątem
            równości odwołań. Ta metoda <b>nie</b> powinna być używana do porównywania dwóch wystąpień pod kątem
            równości. Ten obiekt <b>zawsze</b> będzie zgłaszał wyjątek za pomocą metody Assert.Fail. Użyj metody
            Assert.AreEqual i skojarzonych przeciążeń w testach jednostkowych.
            </summary>
            <param name="objA"> Obiekt A </param>
            <param name="objB"> Obiekt B </param>
            <returns> Zawsze wartość false. </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action)">
            <summary>
            Testuje, czy kod określony przez delegata <paramref name="action"/> zgłasza wyjątek dokładnie typu <typeparamref name="T"/> (a nie jego typu pochodnego)
            i zgłasza wyjątek
            <code>
            AssertFailedException
            </code>,
            jeśli kod nie zgłasza wyjątku lub zgłasza wyjątek typu innego niż <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegat dla kodu do przetestowania, który powinien zgłosić wyjątek.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Typ wyjątku, którego zgłoszenie jest oczekiwane.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String)">
            <summary>
            Testuje, czy kod określony przez delegata <paramref name="action"/> zgłasza wyjątek dokładnie typu <typeparamref name="T"/> (a nie jego typu pochodnego)
            i zgłasza wyjątek
            <code>
            AssertFailedException
            </code>,
            jeśli kod nie zgłasza wyjątku lub zgłasza wyjątek typu innego niż <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegat dla kodu do przetestowania, który powinien zgłosić wyjątek.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="action"/>
            nie zgłasza wyjątku typu <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Typ wyjątku, którego zgłoszenie jest oczekiwane.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object})">
            <summary>
            Testuje, czy kod określony przez delegata <paramref name="action"/> zgłasza wyjątek dokładnie typu <typeparamref name="T"/> (a nie jego typu pochodnego)
            i zgłasza wyjątek
            <code>
            AssertFailedException
            </code>,
            jeśli kod nie zgłasza wyjątku lub zgłasza wyjątek typu innego niż <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegat dla kodu do przetestowania, który powinien zgłosić wyjątek.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Typ wyjątku, którego zgłoszenie jest oczekiwane.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String)">
            <summary>
            Testuje, czy kod określony przez delegata <paramref name="action"/> zgłasza wyjątek dokładnie typu <typeparamref name="T"/> (a nie jego typu pochodnego)
            i zgłasza wyjątek
            <code>
            AssertFailedException
            </code>,
            jeśli kod nie zgłasza wyjątku lub zgłasza wyjątek typu innego niż <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegat dla kodu do przetestowania, który powinien zgłosić wyjątek.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="action"/>
            nie zgłasza wyjątku typu <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Typ wyjątku, którego zgłoszenie jest oczekiwane.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String,System.Object[])">
            <summary>
            Testuje, czy kod określony przez delegata <paramref name="action"/> zgłasza wyjątek dokładnie typu <typeparamref name="T"/> (a nie jego typu pochodnego)
            i zgłasza wyjątek
            <code>
            AssertFailedException
            </code>,
            jeśli kod nie zgłasza wyjątku lub zgłasza wyjątek typu innego niż <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegat dla kodu do przetestowania, który powinien zgłosić wyjątek.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="action"/>
            nie zgłasza wyjątku typu <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throw exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Typ wyjątku, którego zgłoszenie jest oczekiwane.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String,System.Object[])">
            <summary>
            Testuje, czy kod określony przez delegata <paramref name="action"/> zgłasza wyjątek dokładnie typu <typeparamref name="T"/> (a nie jego typu pochodnego)
            i zgłasza wyjątek
            <code>
            AssertFailedException
            </code>,
            jeśli kod nie zgłasza wyjątku lub zgłasza wyjątek typu innego niż <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegat dla kodu do przetestowania, który powinien zgłosić wyjątek.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="action"/>
            nie zgłasza wyjątku typu <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Typ wyjątku, którego zgłoszenie jest oczekiwane.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Testuje, czy kod określony przez delegata <paramref name="action"/> zgłasza wyjątek dokładnie typu <typeparamref name="T"/> (a nie jego typu pochodnego)
            i zgłasza wyjątek
            <code>
            AssertFailedException
            </code>,
            jeśli kod nie zgłasza wyjątku lub zgłasza wyjątek typu innego niż <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegat dla kodu do przetestowania, który powinien zgłosić wyjątek.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Element <see cref="T:System.Threading.Tasks.Task"/> wykonywanie delegata.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            Testuje, czy kod określony przez delegata <paramref name="action"/> zgłasza wyjątek dokładnie typu <typeparamref name="T"/> (a nie jego typu pochodnego)
            i zgłasza wyjątek <code>AssertFailedException</code>, jeśli kod nie zgłasza wyjątku lub zgłasza wyjątek typu innego niż <typeparamref name="T"/>.
            </summary>
            <param name="action">Delegat dla kodu do przetestowania, który powinien zgłosić wyjątek.</param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="action"/>
            nie zgłasza wyjątku typu <typeparamref name="T"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Element <see cref="T:System.Threading.Tasks.Task"/> wykonywanie delegata.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String,System.Object[])">
            <summary>
            Testuje, czy kod określony przez delegata <paramref name="action"/> zgłasza wyjątek dokładnie typu <typeparamref name="T"/> (a nie jego typu pochodnego)
            i zgłasza wyjątek <code>AssertFailedException</code>, jeśli kod nie zgłasza wyjątku lub zgłasza wyjątek typu innego niż <typeparamref name="T"/>.
            </summary>
            <param name="action">Delegat dla kodu do przetestowania, który powinien zgłosić wyjątek.</param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="action"/>
            nie zgłasza wyjątku typu <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Element <see cref="T:System.Threading.Tasks.Task"/> wykonywanie delegata.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNullChars(System.String)">
            <summary>
            Zastępuje znaki null („\0”) ciągiem „\\0”.
            </summary>
            <param name="input">
            Ciąg do wyszukania.
            </param>
            <returns>
            Przekonwertowany ciąg ze znakami null zastąpionymi ciągiem „\\0”.
            </returns>
            <remarks>
            This is only public and still present to preserve compatibility with the V1 framework.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.HandleFail(System.String,System.String,System.Object[])">
            <summary>
            Funkcja pomocnicza, która tworzy i zgłasza wyjątek AssertionFailedException
            </summary>
            <param name="assertionName">
            nazwa asercji zgłaszającej wyjątek
            </param>
            <param name="message">
            komunikat opisujący warunki dla błędu asercji
            </param>
            <param name="parameters">
            Parametry.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.CheckParameterNotNull(System.Object,System.String,System.String,System.String,System.Object[])">
            <summary>
            Sprawdza parametry pod kątem prawidłowych warunków
            </summary>
            <param name="param">
            Parametr.
            </param>
            <param name="assertionName">
            Nazwa asercji.
            </param>
            <param name="parameterName">
            nazwa parametru
            </param>
            <param name="message">
            komunikat dla wyjątku nieprawidłowego parametru
            </param>
            <param name="parameters">
            Parametry.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNulls(System.Object)">
            <summary>
            Bezpiecznie konwertuje obiekt na ciąg, obsługując wartości null i znaki null.
            Wartości null są konwertowane na ciąg „(null)”. Znaki null są konwertowane na ciąg „\\0”.
            </summary>
            <param name="input">
            Obiekt do przekonwertowania na ciąg.
            </param>
            <returns>
            Przekonwertowany ciąg.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert">
            <summary>
            Asercja ciągu.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.That">
            <summary>
            Pobiera pojedyncze wystąpienie funkcji CollectionAssert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void ContainsWords(this StringAssert cusomtAssert, string value, ICollection substrings)"
            Users could then use a syntax similar to the default assertions which in this case is "StringAssert.That.ContainsWords(value, substrings);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String)">
            <summary>
            Testuje, czy określony ciąg zawiera podany podciąg,
            i zgłasza wyjątek, jeśli podciąg nie występuje
            w testowanym ciągu.
            </summary>
            <param name="value">
            Ciąg, który powinien zawierać ciąg <paramref name="substring"/>.
            </param>
            <param name="substring">
            Ciąg, którego wystąpienie jest oczekiwane w ciągu <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String)">
            <summary>
            Testuje, czy określony ciąg zawiera podany podciąg,
            i zgłasza wyjątek, jeśli podciąg nie występuje
            w testowanym ciągu.
            </summary>
            <param name="value">
            Ciąg, który powinien zawierać ciąg <paramref name="substring"/>.
            </param>
            <param name="substring">
            Ciąg, którego wystąpienie jest oczekiwane w ciągu <paramref name="value"/>.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="substring"/>
            nie znajduje się w ciągu <paramref name="value"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String,System.Object[])">
            <summary>
            Testuje, czy określony ciąg zawiera podany podciąg,
            i zgłasza wyjątek, jeśli podciąg nie występuje
            w testowanym ciągu.
            </summary>
            <param name="value">
            Ciąg, który powinien zawierać ciąg <paramref name="substring"/>.
            </param>
            <param name="substring">
            Ciąg, którego wystąpienie jest oczekiwane w ciągu <paramref name="value"/>.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="substring"/>
            nie znajduje się w ciągu <paramref name="value"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String)">
            <summary>
            Testuje, czy określony ciąg rozpoczyna się podanym podciągiem,
            i zgłasza wyjątek, jeśli testowany ciąg nie rozpoczyna się
            podciągiem.
            </summary>
            <param name="value">
            Ciąg, którego oczekiwany początek to <paramref name="substring"/>.
            </param>
            <param name="substring">
            Ciąg, który powinien być prefiksem ciągu <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String)">
            <summary>
            Testuje, czy określony ciąg rozpoczyna się podanym podciągiem,
            i zgłasza wyjątek, jeśli testowany ciąg nie rozpoczyna się
            podciągiem.
            </summary>
            <param name="value">
            Ciąg, którego oczekiwany początek to <paramref name="substring"/>.
            </param>
            <param name="substring">
            Ciąg, który powinien być prefiksem ciągu <paramref name="value"/>.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="value"/>
            nie zaczyna się ciągiem <paramref name="substring"/>. Komunikat
            jest wyświetlony w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Testuje, czy określony ciąg rozpoczyna się podanym podciągiem,
            i zgłasza wyjątek, jeśli testowany ciąg nie rozpoczyna się
            podciągiem.
            </summary>
            <param name="value">
            Ciąg, którego oczekiwany początek to <paramref name="substring"/>.
            </param>
            <param name="substring">
            Ciąg, który powinien być prefiksem ciągu <paramref name="value"/>.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="value"/>
            nie zaczyna się ciągiem <paramref name="substring"/>. Komunikat
            jest wyświetlony w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String)">
            <summary>
            Testuje, czy określony ciąg kończy się podanym podciągiem,
            i zgłasza wyjątek, jeśli testowany ciąg nie kończy się
            podciągiem.
            </summary>
            <param name="value">
            Ciąg, którego oczekiwane zakończenie to <paramref name="substring"/>.
            </param>
            <param name="substring">
            Ciąg, który powinien być sufiksem ciągu <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String)">
            <summary>
            Testuje, czy określony ciąg kończy się podanym podciągiem,
            i zgłasza wyjątek, jeśli testowany ciąg nie kończy się
            podciągiem.
            </summary>
            <param name="value">
            Ciąg, którego oczekiwane zakończenie to <paramref name="substring"/>.
            </param>
            <param name="substring">
            Ciąg, który powinien być sufiksem ciągu <paramref name="value"/>.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="value"/>
            nie kończy się ciągiem <paramref name="substring"/>. Komunikat
            jest wyświetlony w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Testuje, czy określony ciąg kończy się podanym podciągiem,
            i zgłasza wyjątek, jeśli testowany ciąg nie kończy się
            podciągiem.
            </summary>
            <param name="value">
            Ciąg, którego oczekiwane zakończenie to <paramref name="substring"/>.
            </param>
            <param name="substring">
            Ciąg, który powinien być sufiksem ciągu <paramref name="value"/>.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="value"/>
            nie kończy się ciągiem <paramref name="substring"/>. Komunikat
            jest wyświetlony w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Testuje, czy określony ciąg pasuje do wyrażenia regularnego,
            i zgłasza wyjątek, jeśli ciąg nie pasuje do wyrażenia.
            </summary>
            <param name="value">
            Ciąg, który powinien pasować do wzorca <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Wyrażenie regularne, do którego ciąg <paramref name="value"/> ma
            pasować.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Testuje, czy określony ciąg pasuje do wyrażenia regularnego,
            i zgłasza wyjątek, jeśli ciąg nie pasuje do wyrażenia.
            </summary>
            <param name="value">
            Ciąg, który powinien pasować do wzorca <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Wyrażenie regularne, do którego ciąg <paramref name="value"/> ma
            pasować.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="value"/>
            nie pasuje do wzorca <paramref name="pattern"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Testuje, czy określony ciąg pasuje do wyrażenia regularnego,
            i zgłasza wyjątek, jeśli ciąg nie pasuje do wyrażenia.
            </summary>
            <param name="value">
            Ciąg, który powinien pasować do wzorca <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Wyrażenie regularne, do którego ciąg <paramref name="value"/> ma
            pasować.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="value"/>
            nie pasuje do wzorca <paramref name="pattern"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Testuje, czy określony ciąg nie pasuje do wyrażenia regularnego,
            i zgłasza wyjątek, jeśli ciąg pasuje do wyrażenia.
            </summary>
            <param name="value">
            Ciąg, który nie powinien pasować do wzorca <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Wyrażenie regularne, do którego ciąg <paramref name="value"/> nie
            powinien pasować.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Testuje, czy określony ciąg nie pasuje do wyrażenia regularnego,
            i zgłasza wyjątek, jeśli ciąg pasuje do wyrażenia.
            </summary>
            <param name="value">
            Ciąg, który nie powinien pasować do wzorca <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Wyrażenie regularne, do którego ciąg <paramref name="value"/> nie
            powinien pasować.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="value"/>
            dopasowania <paramref name="pattern"/>. Komunikat jest wyświetlony w wynikach
            testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Testuje, czy określony ciąg nie pasuje do wyrażenia regularnego,
            i zgłasza wyjątek, jeśli ciąg pasuje do wyrażenia.
            </summary>
            <param name="value">
            Ciąg, który nie powinien pasować do wzorca <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Wyrażenie regularne, do którego ciąg <paramref name="value"/> nie
            powinien pasować.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="value"/>
            dopasowania <paramref name="pattern"/>. Komunikat jest wyświetlony w wynikach
            testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert">
            <summary>
            Kolekcja klas pomocniczych na potrzeby testowania różnych warunków skojarzonych
            z kolekcjami w ramach testów jednostkowych. Jeśli testowany warunek
            nie jest spełniony, zostanie zgłoszony wyjątek.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.That">
            <summary>
            Pobiera pojedyncze wystąpienie funkcji CollectionAssert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void AreEqualUnordered(this CollectionAssert cusomtAssert, ICollection expected, ICollection actual)"
            Users could then use a syntax similar to the default assertions which in this case is "CollectionAssert.That.AreEqualUnordered(list1, list2);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object)">
            <summary>
            Testuje, czy określona kolekcja zawiera podany element,
            i zgłasza wyjątek, jeśli element nie znajduje się w kolekcji.
            </summary>
            <param name="collection">
            Kolekcja, w której ma znajdować się wyszukiwany element.
            </param>
            <param name="element">
            Element, który powinien należeć do kolekcji.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Testuje, czy określona kolekcja zawiera podany element,
            i zgłasza wyjątek, jeśli element nie znajduje się w kolekcji.
            </summary>
            <param name="collection">
            Kolekcja, w której ma znajdować się wyszukiwany element.
            </param>
            <param name="element">
            Element, który powinien należeć do kolekcji.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="element"/>
            nie znajduje się w ciągu <paramref name="collection"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Testuje, czy określona kolekcja zawiera podany element,
            i zgłasza wyjątek, jeśli element nie znajduje się w kolekcji.
            </summary>
            <param name="collection">
            Kolekcja, w której ma znajdować się wyszukiwany element.
            </param>
            <param name="element">
            Element, który powinien należeć do kolekcji.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="element"/>
            nie znajduje się w ciągu <paramref name="collection"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object)">
            <summary>
            Testuje, czy określona kolekcja nie zawiera podanego elementu,
            i zgłasza wyjątek, jeśli element znajduje się w kolekcji.
            </summary>
            <param name="collection">
            Kolekcja, w której ma znajdować się wyszukiwany element.
            </param>
            <param name="element">
            Element, który nie powinien należeć do kolekcji.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Testuje, czy określona kolekcja nie zawiera podanego elementu,
            i zgłasza wyjątek, jeśli element znajduje się w kolekcji.
            </summary>
            <param name="collection">
            Kolekcja, w której ma znajdować się wyszukiwany element.
            </param>
            <param name="element">
            Element, który nie powinien należeć do kolekcji.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="element"/>
            znajduje się w kolekcji <paramref name="collection"/>. Komunikat jest wyświetlony w wynikach
            testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Testuje, czy określona kolekcja nie zawiera podanego elementu,
            i zgłasza wyjątek, jeśli element znajduje się w kolekcji.
            </summary>
            <param name="collection">
            Kolekcja, w której ma znajdować się wyszukiwany element.
            </param>
            <param name="element">
            Element, który nie powinien należeć do kolekcji.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="element"/>
            znajduje się w kolekcji <paramref name="collection"/>. Komunikat jest wyświetlony w wynikach
            testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection)">
            <summary>
            Testuje, czy wszystkie elementy w określonej kolekcji mają wartości inne niż null, i zgłasza
            wyjątek, jeśli którykolwiek element ma wartość null.
            </summary>
            <param name="collection">
            Kolekcja, w której mają być wyszukiwane elementy o wartości null.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String)">
            <summary>
            Testuje, czy wszystkie elementy w określonej kolekcji mają wartości inne niż null, i zgłasza
            wyjątek, jeśli którykolwiek element ma wartość null.
            </summary>
            <param name="collection">
            Kolekcja, w której mają być wyszukiwane elementy o wartości null.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="collection"/>
            zawiera element o wartości null. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testuje, czy wszystkie elementy w określonej kolekcji mają wartości inne niż null, i zgłasza
            wyjątek, jeśli którykolwiek element ma wartość null.
            </summary>
            <param name="collection">
            Kolekcja, w której mają być wyszukiwane elementy o wartości null.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="collection"/>
            zawiera element o wartości null. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection)">
            <summary>
            Testuje, czy wszystkie elementy w określonej kolekcji są unikatowe,
            i zgłasza wyjątek, jeśli dowolne dwa elementy w kolekcji są równe.
            </summary>
            <param name="collection">
            Kolekcja, w której mają być wyszukiwane zduplikowane elementy.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String)">
            <summary>
            Testuje, czy wszystkie elementy w określonej kolekcji są unikatowe,
            i zgłasza wyjątek, jeśli dowolne dwa elementy w kolekcji są równe.
            </summary>
            <param name="collection">
            Kolekcja, w której mają być wyszukiwane zduplikowane elementy.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="collection"/>
            zawiera co najmniej jeden zduplikowany element. Komunikat jest wyświetlony w
            wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testuje, czy wszystkie elementy w określonej kolekcji są unikatowe,
            i zgłasza wyjątek, jeśli dowolne dwa elementy w kolekcji są równe.
            </summary>
            <param name="collection">
            Kolekcja, w której mają być wyszukiwane zduplikowane elementy.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="collection"/>
            zawiera co najmniej jeden zduplikowany element. Komunikat jest wyświetlony w
            wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testuje, czy dana kolekcja stanowi podzbiór innej kolekcji,
            i zgłasza wyjątek, jeśli dowolny element podzbioru znajduje się także
            w nadzbiorze.
            </summary>
            <param name="subset">
            Kolekcja powinna być podzbiorem <paramref name="superset"/>.
            </param>
            <param name="superset">
            Kolekcja powinna być nadzbiorem <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testuje, czy dana kolekcja stanowi podzbiór innej kolekcji,
            i zgłasza wyjątek, jeśli dowolny element podzbioru znajduje się także
            w nadzbiorze.
            </summary>
            <param name="subset">
            Kolekcja powinna być podzbiorem <paramref name="superset"/>.
            </param>
            <param name="superset">
            Kolekcja powinna być nadzbiorem <paramref name="subset"/>
            </param>
            <param name="message">
            Komunikat do uwzględnienia w wyjątku, gdy elementu w
            <paramref name="subset"/> nie można odnaleźć w <paramref name="superset"/>.
            Komunikat jest wyświetlany w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testuje, czy dana kolekcja stanowi podzbiór innej kolekcji,
            i zgłasza wyjątek, jeśli dowolny element podzbioru znajduje się także
            w nadzbiorze.
            </summary>
            <param name="subset">
            Kolekcja powinna być podzbiorem <paramref name="superset"/>.
            </param>
            <param name="superset">
            Kolekcja powinna być nadzbiorem <paramref name="subset"/>
            </param>
            <param name="message">
            Komunikat do uwzględnienia w wyjątku, gdy elementu w
            <paramref name="subset"/> nie można odnaleźć w <paramref name="superset"/>.
            Komunikat jest wyświetlany w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testuje, czy jedna kolekcja nie jest podzbiorem innej kolekcji,
            i zgłasza wyjątek, jeśli wszystkie elementy w podzbiorze znajdują się również
            w nadzbiorze.
            </summary>
            <param name="subset">
            Kolekcja nie powinna być podzbiorem <paramref name="superset"/>.
            </param>
            <param name="superset">
            Kolekcja nie powinna być nadzbiorem <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testuje, czy jedna kolekcja nie jest podzbiorem innej kolekcji,
            i zgłasza wyjątek, jeśli wszystkie elementy w podzbiorze znajdują się również
            w nadzbiorze.
            </summary>
            <param name="subset">
            Kolekcja nie powinna być podzbiorem <paramref name="superset"/>.
            </param>
            <param name="superset">
            Kolekcja nie powinna być nadzbiorem <paramref name="subset"/>
            </param>
            <param name="message">
            Komunikat do uwzględnienia w wyjątku, gdy każdy element w kolekcji
            <paramref name="subset"/> znajduje się również w kolekcji <paramref name="superset"/>.
            Komunikat jest wyświetlany w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testuje, czy jedna kolekcja nie jest podzbiorem innej kolekcji,
            i zgłasza wyjątek, jeśli wszystkie elementy w podzbiorze znajdują się również
            w nadzbiorze.
            </summary>
            <param name="subset">
            Kolekcja nie powinna być podzbiorem <paramref name="superset"/>.
            </param>
            <param name="superset">
            Kolekcja nie powinna być nadzbiorem <paramref name="subset"/>
            </param>
            <param name="message">
            Komunikat do uwzględnienia w wyjątku, gdy każdy element w kolekcji
            <paramref name="subset"/> znajduje się również w kolekcji <paramref name="superset"/>.
            Komunikat jest wyświetlany w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testuje, czy dwie kolekcje zawierają te same elementy, i zgłasza
            wyjątek, jeśli któraś z kolekcji zawiera element niezawarty w drugiej
            kolekcji.
            </summary>
            <param name="expected">
            Pierwsza kolekcja do porównania. Zawiera elementy oczekiwane przez
            test.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testuje, czy dwie kolekcje zawierają te same elementy, i zgłasza
            wyjątek, jeśli któraś z kolekcji zawiera element niezawarty w drugiej
            kolekcji.
            </summary>
            <param name="expected">
            Pierwsza kolekcja do porównania. Zawiera elementy oczekiwane przez
            test.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <param name="message">
            Komunikat do uwzględnienia w wyjątku, gdy element został odnaleziony
            w jednej z kolekcji, ale nie ma go w drugiej. Komunikat jest wyświetlany
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testuje, czy dwie kolekcje zawierają te same elementy, i zgłasza
            wyjątek, jeśli któraś z kolekcji zawiera element niezawarty w drugiej
            kolekcji.
            </summary>
            <param name="expected">
            Pierwsza kolekcja do porównania. Zawiera elementy oczekiwane przez
            test.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <param name="message">
            Komunikat do uwzględnienia w wyjątku, gdy element został odnaleziony
            w jednej z kolekcji, ale nie ma go w drugiej. Komunikat jest wyświetlany
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testuje, czy dwie kolekcje zawierają różne elementy, i zgłasza
            wyjątek, jeśli dwie kolekcje zawierają identyczne elementy bez względu
            na porządek.
            </summary>
            <param name="expected">
            Pierwsza kolekcja do porównania. Zawiera elementy, co do których test oczekuje,
            że będą inne niż rzeczywista kolekcja.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testuje, czy dwie kolekcje zawierają różne elementy, i zgłasza
            wyjątek, jeśli dwie kolekcje zawierają identyczne elementy bez względu
            na porządek.
            </summary>
            <param name="expected">
            Pierwsza kolekcja do porównania. Zawiera elementy, co do których test oczekuje,
            że będą inne niż rzeczywista kolekcja.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            zawiera te same elementy co <paramref name="expected"/>. Komunikat
            jest wyświetlany w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testuje, czy dwie kolekcje zawierają różne elementy, i zgłasza
            wyjątek, jeśli dwie kolekcje zawierają identyczne elementy bez względu
            na porządek.
            </summary>
            <param name="expected">
            Pierwsza kolekcja do porównania. Zawiera elementy, co do których test oczekuje,
            że będą inne niż rzeczywista kolekcja.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            zawiera te same elementy co <paramref name="expected"/>. Komunikat
            jest wyświetlany w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type)">
            <summary>
            Sprawdza, czy wszystkie elementy w określonej kolekcji są wystąpieniami
            oczekiwanego typu i zgłasza wyjątek, jeśli oczekiwanego typu nie ma
            w hierarchii dziedziczenia jednego lub większej liczby elementów.
            </summary>
            <param name="collection">
            Kolekcja zawierająca elementy, co do których test oczekuje, że będą
            elementami określonego typu.
            </param>
            <param name="expectedType">
            Oczekiwany typ każdego elementu kolekcji <paramref name="collection"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String)">
            <summary>
            Sprawdza, czy wszystkie elementy w określonej kolekcji są wystąpieniami
            oczekiwanego typu i zgłasza wyjątek, jeśli oczekiwanego typu nie ma
            w hierarchii dziedziczenia jednego lub większej liczby elementów.
            </summary>
            <param name="collection">
            Kolekcja zawierająca elementy, co do których test oczekuje, że będą
            elementami określonego typu.
            </param>
            <param name="expectedType">
            Oczekiwany typ każdego elementu kolekcji <paramref name="collection"/>.
            </param>
            <param name="message">
            Komunikat do uwzględnienia w wyjątku, gdy elementu w
            <paramref name="collection"/> nie jest wystąpieniem
            <paramref name="expectedType"/>. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String,System.Object[])">
            <summary>
            Sprawdza, czy wszystkie elementy w określonej kolekcji są wystąpieniami
            oczekiwanego typu i zgłasza wyjątek, jeśli oczekiwanego typu nie ma
            w hierarchii dziedziczenia jednego lub większej liczby elementów.
            </summary>
            <param name="collection">
            Kolekcja zawierająca elementy, co do których test oczekuje, że będą
            elementami określonego typu.
            </param>
            <param name="expectedType">
            Oczekiwany typ każdego elementu kolekcji <paramref name="collection"/>.
            </param>
            <param name="message">
            Komunikat do uwzględnienia w wyjątku, gdy elementu w
            <paramref name="collection"/> nie jest wystąpieniem
            <paramref name="expectedType"/>. Komunikat jest wyświetlony w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testuje, czy określone kolekcje są równe, i zgłasza wyjątek,
            jeśli dwie kolekcje nie są równe. Równość jest definiowana jako zawieranie tych samych
            elementów w takim samym porządku i ilości. Różne odwołania do tej samej
            wartości są uznawane za równe.
            </summary>
            <param name="expected">
            Pierwsza kolekcja do porównania. To jest kolekcja oczekiwana przez test.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testuje, czy określone kolekcje są równe, i zgłasza wyjątek,
            jeśli dwie kolekcje nie są równe. Równość jest definiowana jako zawieranie tych samych
            elementów w takim samym porządku i ilości. Różne odwołania do tej samej
            wartości są uznawane za równe.
            </summary>
            <param name="expected">
            Pierwsza kolekcja do porównania. To jest kolekcja oczekiwana przez test.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            nie jest równy elementowi <paramref name="expected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testuje, czy określone kolekcje są równe, i zgłasza wyjątek,
            jeśli dwie kolekcje nie są równe. Równość jest definiowana jako zawieranie tych samych
            elementów w takim samym porządku i ilości. Różne odwołania do tej samej
            wartości są uznawane za równe.
            </summary>
            <param name="expected">
            Pierwsza kolekcja do porównania. To jest kolekcja oczekiwana przez test.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            nie jest równy elementowi <paramref name="expected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testuje, czy określone kolekcje są nierówne, i zgłasza wyjątek,
            jeśli dwie kolekcje są równe. Równość jest definiowana jako zawieranie tych samych
            elementów w takim samym porządku i ilości. Różne odwołania do tej samej
            wartości są uznawane za równe.
            </summary>
            <param name="notExpected">
            Pierwsza kolekcja do porównania. To jest kolekcja, co do której test oczekuje
,            że nie będzie zgodna <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testuje, czy określone kolekcje są nierówne, i zgłasza wyjątek,
            jeśli dwie kolekcje są równe. Równość jest definiowana jako zawieranie tych samych
            elementów w takim samym porządku i ilości. Różne odwołania do tej samej
            wartości są uznawane za równe.
            </summary>
            <param name="notExpected">
            Pierwsza kolekcja do porównania. To jest kolekcja, co do której test oczekuje
,            że nie będzie zgodna <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest równy elementowi <paramref name="notExpected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testuje, czy określone kolekcje są nierówne, i zgłasza wyjątek,
            jeśli dwie kolekcje są równe. Równość jest definiowana jako zawieranie tych samych
            elementów w takim samym porządku i ilości. Różne odwołania do tej samej
            wartości są uznawane za równe.
            </summary>
            <param name="notExpected">
            Pierwsza kolekcja do porównania. To jest kolekcja, co do której test oczekuje
,            że nie będzie zgodna <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest równy elementowi <paramref name="notExpected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Testuje, czy określone kolekcje są równe, i zgłasza wyjątek,
            jeśli dwie kolekcje nie są równe. Równość jest definiowana jako zawieranie tych samych
            elementów w takim samym porządku i ilości. Różne odwołania do tej samej
            wartości są uznawane za równe.
            </summary>
            <param name="expected">
            Pierwsza kolekcja do porównania. To jest kolekcja oczekiwana przez test.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <param name="comparer">
            Implementacja porównania do użycia podczas porównywania elementów kolekcji.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Testuje, czy określone kolekcje są równe, i zgłasza wyjątek,
            jeśli dwie kolekcje nie są równe. Równość jest definiowana jako zawieranie tych samych
            elementów w takim samym porządku i ilości. Różne odwołania do tej samej
            wartości są uznawane za równe.
            </summary>
            <param name="expected">
            Pierwsza kolekcja do porównania. To jest kolekcja oczekiwana przez test.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <param name="comparer">
            Implementacja porównania do użycia podczas porównywania elementów kolekcji.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            nie jest równy elementowi <paramref name="expected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Testuje, czy określone kolekcje są równe, i zgłasza wyjątek,
            jeśli dwie kolekcje nie są równe. Równość jest definiowana jako zawieranie tych samych
            elementów w takim samym porządku i ilości. Różne odwołania do tej samej
            wartości są uznawane za równe.
            </summary>
            <param name="expected">
            Pierwsza kolekcja do porównania. To jest kolekcja oczekiwana przez test.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <param name="comparer">
            Implementacja porównania do użycia podczas porównywania elementów kolekcji.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            nie jest równy elementowi <paramref name="expected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Testuje, czy określone kolekcje są nierówne, i zgłasza wyjątek,
            jeśli dwie kolekcje są równe. Równość jest definiowana jako zawieranie tych samych
            elementów w takim samym porządku i ilości. Różne odwołania do tej samej
            wartości są uznawane za równe.
            </summary>
            <param name="notExpected">
            Pierwsza kolekcja do porównania. To jest kolekcja, co do której test oczekuje
,            że nie będzie zgodna <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <param name="comparer">
            Implementacja porównania do użycia podczas porównywania elementów kolekcji.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Testuje, czy określone kolekcje są nierówne, i zgłasza wyjątek,
            jeśli dwie kolekcje są równe. Równość jest definiowana jako zawieranie tych samych
            elementów w takim samym porządku i ilości. Różne odwołania do tej samej
            wartości są uznawane za równe.
            </summary>
            <param name="notExpected">
            Pierwsza kolekcja do porównania. To jest kolekcja, co do której test oczekuje
,            że nie będzie zgodna <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <param name="comparer">
            Implementacja porównania do użycia podczas porównywania elementów kolekcji.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest równy elementowi <paramref name="notExpected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Testuje, czy określone kolekcje są nierówne, i zgłasza wyjątek,
            jeśli dwie kolekcje są równe. Równość jest definiowana jako zawieranie tych samych
            elementów w takim samym porządku i ilości. Różne odwołania do tej samej
            wartości są uznawane za równe.
            </summary>
            <param name="notExpected">
            Pierwsza kolekcja do porównania. To jest kolekcja, co do której test oczekuje
,            że nie będzie zgodna <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druga kolekcja do porównania. To jest kolekcja utworzona przez
            testowany kod.
            </param>
            <param name="comparer">
            Implementacja porównania do użycia podczas porównywania elementów kolekcji.
            </param>
            <param name="message">
            Komunikat do dołączenia do wyjątku, gdy element <paramref name="actual"/>
            jest równy elementowi <paramref name="notExpected"/>. Komunikat jest wyświetlony
            w wynikach testu.
            </param>
            <param name="parameters">
            Tablica parametrów do użycia podczas formatowania elementu <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOfHelper(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Określa, czy pierwsza kolekcja jest podzbiorem drugiej kolekcji.
            Jeśli któryś zbiór zawiera zduplikowane elementy, liczba wystąpień
            elementu w podzbiorze musi być mniejsza lub równa liczbie
            wystąpień w nadzbiorze.
            </summary>
            <param name="subset">
            Kolekcja, co do której test oczekuje, że powinna być zawarta w <paramref name="superset"/>.
            </param>
            <param name="superset">
            Kolekcja, co do której test oczekuje, że powinna zawierać <paramref name="subset"/>.
            </param>
            <returns>
            Wartość true, jeśli <paramref name="subset"/> jest podzbiorem kolekcji
            <paramref name="superset"/>, w przeciwnym razie wartość false.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.GetElementCounts(System.Collections.ICollection,System.Int32@)">
            <summary>
            Tworzy słownik zawierający liczbę wystąpień każdego elementu
            w określonej kolekcji.
            </summary>
            <param name="collection">
            Kolekcja do przetworzenia.
            </param>
            <param name="nullCount">
            Liczba elementów o wartości null w kolekcji.
            </param>
            <returns>
            Słownik zawierający liczbę wystąpień każdego elementu
            w określonej kolekcji.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.FindMismatchedElement(System.Collections.ICollection,System.Collections.ICollection,System.Int32@,System.Int32@,System.Object@)">
            <summary>
            Znajduje niezgodny element w dwóch kolekcjach. Niezgodny
            element to ten, którego liczba wystąpień w oczekiwanej kolekcji
            jest inna niż w rzeczywistej kolekcji. Kolekcje
            są uznawane za różne odwołania o wartości innej niż null z tą samą
            liczbą elementów. Obiekt wywołujący jest odpowiedzialny za ten poziom weryfikacji.
            Jeśli nie ma żadnego niezgodnego elementu, funkcja zwraca wynik
            false i parametry wyjściowe nie powinny być używane.
            </summary>
            <param name="expected">
            Pierwsza kolekcja do porównania.
            </param>
            <param name="actual">
            Druga kolekcja do porównania.
            </param>
            <param name="expectedCount">
            Oczekiwana liczba wystąpień elementu
            <paramref name="mismatchedElement"/> lub 0, jeśli nie ma żadnego niezgodnego
            elementu.
            </param>
            <param name="actualCount">
            Rzeczywista liczba wystąpień elementu
            <paramref name="mismatchedElement"/> lub 0, jeśli nie ma żadnego niezgodnego
            elementu.
            </param>
            <param name="mismatchedElement">
            Niezgodny element (może mieć wartość null) lub wartość null, jeśli
            nie ma żadnego niezgodnego elementu.
            </param>
            <returns>
            wartość true, jeśli znaleziono niezgodny element; w przeciwnym razie wartość false.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.ObjectComparer">
            <summary>
            porównuje obiekty przy użyciu funkcji object.Equals
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException">
            <summary>
            Klasa podstawowa dla wyjątków struktury.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String,System.Exception)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
            <param name="msg"> Komunikat. </param>
            <param name="ex"> Wyjątek. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
            <param name="msg"> Komunikat. </param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages">
            <summary>
              Silnie typizowana klasa zasobów do wyszukiwania zlokalizowanych ciągów itp.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ResourceManager">
            <summary>
              Zwraca buforowane wystąpienie ResourceManager używane przez tę klasę.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Culture">
            <summary>
              Przesłania właściwość CurrentUICulture bieżącego wątku dla wszystkich
              przypadków przeszukiwania zasobów za pomocą tej silnie typizowanej klasy zasobów.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AccessStringInvalidSyntax">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Ciąg dostępu ma nieprawidłową składnię.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ActualHasMismatchedElements">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Oczekiwana kolekcja zawiera następującą liczbę wystąpień elementu &lt;{2}&gt;: {1}. Rzeczywista kolekcja zawiera następującą liczbę wystąpień: {3}. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AllItemsAreUniqueFailMsg">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Znaleziono zduplikowany element: &lt;{1}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualCaseFailMsg">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Oczekiwano: &lt;{1}&gt;. Przypadek jest inny w wartości rzeczywistej: &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDeltaFailMsg">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Oczekiwano różnicy nie większej niż &lt;{3}&gt; między oczekiwaną wartością &lt;{1}&gt; i wartością rzeczywistą &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDifferentTypesFailMsg">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Oczekiwana wartość: &lt;{1} ({2})&gt;. Rzeczywista wartość: &lt;{3} ({4})&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualFailMsg">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Oczekiwana wartość: &lt;{1}&gt;. Rzeczywista wartość: &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualDeltaFailMsg">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Oczekiwano różnicy większej niż &lt;{3}&gt; między oczekiwaną wartością &lt;{1}&gt; a wartością rzeczywistą &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualFailMsg">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Oczekiwano dowolnej wartości z wyjątkiem: &lt;{1}&gt;. Wartość rzeczywista: &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreSameGivenValues">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Nie przekazuj typów wartości do metody AreSame(). Wartości przekonwertowane na typ Object nigdy nie będą takie same. Rozważ użycie metody AreEqual(). {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AssertionFailed">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: {0} — niepowodzenie. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AsyncUITestMethodNotSupported">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do asynchronicznej metody TestMethod z elementem UITestMethodAttribute, które nie są obsługiwane. Usuń element asynchroniczny lub użyj elementu TestMethodAttribute.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsEmpty">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Obie kolekcje są puste. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameElements">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Obie kolekcje zawierają te same elementy.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameReference">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Odwołania do obu kolekcji wskazują ten sam obiekt kolekcji. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothSameElements">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Obie kolekcje zawierają te same elementy. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.CollectionEqualReason">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: {0}({1}).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_NullInMessages">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: (null).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_ObjectString">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: (object).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ContainsFail">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Ciąg „{0}” nie zawiera ciągu „{1}”. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DataDrivenResultDisplayName">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: {0} ({1}).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DoNotUseAssertEquals">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Nie można użyć metody Assert.Equals dla asercji. Zamiast tego użyj metody Assert.AreEqual i przeciążeń.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementNumbersDontMatch">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Liczba elementów w kolekcjach nie jest zgodna. Oczekiwana wartość: &lt;{1}&gt;. Wartość rzeczywista: &lt;{2}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementsAtIndexDontMatch">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Element w indeksie {0} nie jest zgodny.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Element w indeksie {1} nie ma oczekiwanego typu. Oczekiwany typ: &lt;{2}&gt;. Rzeczywisty typ: &lt;{3}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch2">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Element w indeksie {1} ma wartość (null). Oczekiwany typ: &lt;{2}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EndsWithFail">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Ciąg „{0}” nie kończy się ciągiem „{1}”. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EqualsTesterInvalidArgs">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Nieprawidłowy argument. Element EqualsTester nie może używać wartości null.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ErrorInvalidCast">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Nie można przekonwertować obiektu typu {0} na typ {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InternalObjectNotValid">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Przywoływany obiekt wewnętrzny nie jest już prawidłowy.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidParameterToAssert">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Parametr „{0}” jest nieprawidłowy. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidPropertyType">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Właściwość {0} ma typ {1}. Oczekiwano typu {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsInstanceOfFailMsg">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: {0} Oczekiwany typ: &lt;{1}&gt;. Rzeczywisty typ: &lt;{2}&gt;.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsMatchFail">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Ciąg „{0}” nie jest zgodny ze wzorcem „{1}”. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotInstanceOfFailMsg">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Niepoprawny typ: &lt;{1}&gt;. Rzeczywisty typ: &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotMatchFail">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Ciąg „{0}” jest zgodny ze wzorcem „{1}”. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoDataRow">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Nie określono atrybutu DataRowAttribute. Atrybut DataTestMethodAttribute wymaga co najmniej jednego atrybutu DataRowAttribute.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoExceptionThrown">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Nie zgłoszono wyjątku. Oczekiwany wyjątek: {1}. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NullParameterToAssert">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Parametr „{0}” jest nieprawidłowy. Wartość nie może być równa null. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NumberOfElementsDiff">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Inna liczba elementów.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorConstructorNotFound">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: 
                 Nie można odnaleźć konstruktora z określoną sygnaturą. Może być konieczne ponowne wygenerowanie prywatnej metody dostępu
                 lub element członkowski może być zdefiniowany jako prywatny w klasie podstawowej. W drugim przypadku należy przekazać typ,
                 który definiuje element członkowski w konstruktorze obiektu PrivateObject.
               .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorMemberNotFound">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: 
                 Nie można odnaleźć określonego elementu członkowskiego ({0}). Może być konieczne ponowne wygenerowanie prywatnej metody dostępu
                 lub element członkowski może być zdefiniowany jako prywatny w klasie podstawowej. W drugim przypadku należy przekazać typ,
                 który definiuje element członkowski w konstruktorze obiektu PrivateObject.
               .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.StartsWithFail">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Ciąg „{0}” nie rozpoczyna się od ciągu „{1}”. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_ExpectedExceptionTypeMustDeriveFromException">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Oczekiwanym typem wyjątku musi być typ System.Exception lub typ pochodzący od typu System.Exception.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_FailedToGetExceptionMessage">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: (Nie można pobrać komunikatu dotyczącego wyjątku typu {0} z powodu wyjątku).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoException">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Metoda testowa nie zgłosiła oczekiwanego wyjątku {0}. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoExceptionDefault">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Metoda testowa nie zgłosiła wyjątku. Wyjątek był oczekiwany przez atrybut {0} zdefiniowany w metodzie testowej.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongException">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Metoda testowa zgłosiła wyjątek {0}, ale oczekiwano wyjątku {1}. Komunikat o wyjątku: {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongExceptionDerivedAllowed">
            <summary>
              Wyszukuje zlokalizowany ciąg podobny do następującego: Metoda testowa zgłosiła wyjątek {0}, ale oczekiwano wyjątku {1} lub typu, który od niego pochodzi. Komunikat o wyjątku: {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.WrongExceptionThrown">
             <summary>
               Wyszukuje zlokalizowany ciąg podobny do następującego: Zgłoszono wyjątek {2}, ale oczekiwano wyjątku {1}. {0}
            Komunikat o wyjątku: {3}
            Ślad stosu: {4}.
             </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome">
            <summary>
            wyniki testu jednostkowego
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Failed">
            <summary>
            Test został wykonany, ale wystąpiły problemy.
            Problemy mogą obejmować wyjątki lub asercje zakończone niepowodzeniem.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Inconclusive">
            <summary>
            Test został ukończony, ale nie można stwierdzić, czy zakończył się powodzeniem, czy niepowodzeniem.
            Może być używany dla przerwanych testów.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Passed">
            <summary>
            Test został wykonany bez żadnych problemów.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.InProgress">
            <summary>
            Test jest obecnie wykonywany.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Error">
            <summary>
            Wystąpił błąd systemu podczas próby wykonania testu.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Timeout">
            <summary>
            Upłynął limit czasu testu.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Aborted">
            <summary>
            Test został przerwany przez użytkownika.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Unknown">
            <summary>
            Stan testu jest nieznany
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper">
            <summary>
            Udostępnia funkcjonalność pomocnika dla platformy testów jednostkowych
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper.GetExceptionMsg(System.Exception)">
            <summary>
            Pobiera komunikaty wyjątku, w tym rekursywnie komunikaty wszystkich wewnętrznych
            wyjątków
            </summary>
            <param name="ex">Wyjątek, dla którego mają zostać pobrane komunikaty</param>
            <returns>ciąg z informacjami o komunikacie o błędzie</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout">
            <summary>
            Wyliczenie dla limitów czasu, które może być używane z klasą <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/>.
            Typ wyliczenia musi być zgodny
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout.Infinite">
            <summary>
            Nieskończone.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute">
            <summary>
            Atrybut klasy testowej.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute.GetTestMethodAttribute(Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute)">
            <summary>
            Pobiera atrybut metody testowej, który umożliwia uruchomienie tego testu.
            </summary>
            <param name="testMethodAttribute">Wystąpienie atrybutu metody testowej zdefiniowane w tej metodzie.</param>
            <returns><see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/> do użycia do uruchamiania tego testu.</returns>
            <remarks>Extensions can override this method to customize how all methods in a class are run.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute">
            <summary>
            Atrybut metody testowej.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Wykonuje metodę testową.
            </summary>
            <param name="testMethod">Metoda testowa do wykonania.</param>
            <returns>Tablica obiektów TestResult reprezentujących wyniki testu.</returns>
            <remarks>Extensions can override this method to customize running a TestMethod.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestInitializeAttribute">
            <summary>
            Atrybut inicjowania testu.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCleanupAttribute">
            <summary>
            Atrybut oczyszczania testu.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute">
            <summary>
            Atrybut ignorowania.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute">
            <summary>
            Atrybut właściwości testu.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.#ctor(System.String,System.String)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute"/>.
            </summary>
            <param name="name">
            Nazwa.
            </param>
            <param name="value">
            Wartość.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Name">
            <summary>
            Pobiera nazwę.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Value">
            <summary>
            Pobiera wartość.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute">
            <summary>
            Atrybut inicjowania klasy.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute">
            <summary>
            Atrybut oczyszczania klasy.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyInitializeAttribute">
            <summary>
            Atrybut inicjowania zestawu.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyCleanupAttribute">
            <summary>
            Atrybut oczyszczania zestawu.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute">
            <summary>
            Właściciel testu
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.#ctor(System.String)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute"/>.
            </summary>
            <param name="owner">
            Właściciel.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.Owner">
            <summary>
            Pobiera właściciela.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute">
            <summary>
            Atrybut priorytetu służący do określania priorytetu testu jednostkowego.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.#ctor(System.Int32)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute"/>.
            </summary>
            <param name="priority">
            Priorytet.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.Priority">
            <summary>
            Pobiera priorytet.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute">
            <summary>
            Opis testu
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.#ctor(System.String)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute"/> do opisu testu.
            </summary>
            <param name="description">Opis.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.Description">
            <summary>
            Pobiera opis testu.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute">
            <summary>
            Identyfikator URI struktury projektu CSS
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.#ctor(System.String)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute"/> dla identyfikatora URI struktury projektu CSS.
            </summary>
            <param name="cssProjectStructure">Identyfikator URI struktury projektu CSS.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.CssProjectStructure">
            <summary>
            Pobiera identyfikator URI struktury projektu CSS.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute">
            <summary>
            Identyfikator URI iteracji CSS
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.#ctor(System.String)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute"/> dla identyfikatora URI iteracji CSS.
            </summary>
            <param name="cssIteration">Identyfikator URI iteracji CSS.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.CssIteration">
            <summary>
            Pobiera identyfikator URI iteracji CSS.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute">
            <summary>
            Atrybut elementu roboczego służący do określania elementu roboczego skojarzonego z tym testem.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.#ctor(System.Int32)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute"/> dla atrybutu WorkItem.
            </summary>
            <param name="id">Identyfikator dla elementu roboczego.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.Id">
            <summary>
            Pobiera identyfikator dla skojarzonego elementu roboczego.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute">
            <summary>
            Atrybut limitu czasu służący do określania limitu czasu testu jednostkowego.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(System.Int32)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/>.
            </summary>
            <param name="timeout">
            Limit czasu.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> ze wstępnie ustawionym limitem czasu
            </summary>
            <param name="timeout">
            Limit czasu
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.Timeout">
            <summary>
            Pobiera limit czasu.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult">
            <summary>
            Obiekt TestResult zwracany do adaptera.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.#ctor">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DisplayName">
            <summary>
            Pobiera lub ustawia nazwę wyświetlaną wyniku. Przydatny w przypadku zwracania wielu wyników.
            Jeśli ma wartość null, nazwa metody jest używana jako nazwa wyświetlana.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Outcome">
            <summary>
            Pobiera lub ustawia wynik wykonania testu.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestFailureException">
            <summary>
            Pobiera lub ustawia wyjątek zgłoszony, gdy test kończy się niepowodzeniem.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogOutput">
            <summary>
            Pobiera lub ustawia dane wyjściowe komunikatu rejestrowanego przez kod testu.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogError">
            <summary>
            Pobiera lub ustawia dane wyjściowe komunikatu rejestrowanego przez kod testu.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DebugTrace">
            <summary>
            Pobiera lub ustawia ślady debugowania przez kod testu.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestContextMessages">
            <summary>
            Gets or sets the debug traces by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Duration">
            <summary>
            Pobiera lub ustawia czas trwania wykonania testu.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DatarowIndex">
            <summary>
            Pobiera lub ustawia indeks wiersza danych w źródle danych. Ustawia tylko dla wyników oddzielnych
            uruchomień wiersza danych w teście opartym na danych.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ReturnValue">
            <summary>
            Pobiera lub ustawia wartość zwracaną metody testowej. (Obecnie zawsze wartość null).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ResultFiles">
            <summary>
            Pobiera lub ustawia pliki wyników dołączone przez test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute">
            <summary>
            Określa parametry połączenia, nazwę tabeli i metodę dostępu do wiersza w przypadku testowania opartego na danych.
            </summary>
            <example>
            [DataSource("Provider=SQLOLEDB.1;Data Source=source;Integrated Security=SSPI;Initial Catalog=EqtCoverage;Persist Security Info=False", "MyTable")]
            [DataSource("dataSourceNameFromConfigFile")]
            </example>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultProviderName">
            <summary>
            Nazwa domyślnego dostawcy dla źródła danych.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultDataAccessMethod">
            <summary>
            Domyślna metoda uzyskiwania dostępu do danych.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String,System.String,Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>. To wystąpienie zostanie zainicjowane z dostawcą danych, parametrami połączenia, tabelą danych i metodą dostępu do danych w celu uzyskania dostępu do źródła danych.
            </summary>
            <param name="providerInvariantName">Niezmienna nazwa dostawcy danych, taka jak System.Data.SqlClient</param>
            <param name="connectionString">
            Parametry połączenia specyficzne dla dostawcy danych. 
            OSTRZEŻENIE: parametry połączenia mogą zawierać poufne dane (na przykład hasło).
            Parametry połączenia są przechowywane w postaci zwykłego tekstu w kodzie źródłowym i w skompilowanym zestawie.
            Należy ograniczyć dostęp do kodu źródłowego i zestawu, aby chronić te poufne informacje.
            </param>
            <param name="tableName">Nazwa tabeli danych.</param>
            <param name="dataAccessMethod">Określa kolejność dostępu do danych.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>. To wystąpienie zostanie zainicjowane z parametrami połączenia i nazwą tabeli.
            Określ parametry połączenia i tabelę danych w celu uzyskania dostępu do źródła danych OLEDB.
            </summary>
            <param name="connectionString">
            Parametry połączenia specyficzne dla dostawcy danych. 
            OSTRZEŻENIE: parametry połączenia mogą zawierać poufne dane (na przykład hasło).
            Parametry połączenia są przechowywane w postaci zwykłego tekstu w kodzie źródłowym i w skompilowanym zestawie.
            Należy ograniczyć dostęp do kodu źródłowego i zestawu, aby chronić te poufne informacje.
            </param>
            <param name="tableName">Nazwa tabeli danych.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String)">
            <summary>
            Inicjuje nowe wystąpienie klasy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>.  To wystąpienie zostanie zainicjowane z dostawcą danych i parametrami połączenia skojarzonymi z nazwą ustawienia.
            </summary>
            <param name="dataSourceSettingName">Nazwa źródła danych znaleziona w sekcji &lt;microsoft.visualstudio.qualitytools&gt; pliku app.config.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ProviderInvariantName">
            <summary>
            Pobiera wartość reprezentującą dostawcę danych źródła danych.
            </summary>
            <returns>
            Nazwa dostawcy danych. Jeśli dostawca danych nie został wyznaczony w czasie inicjowania obiektu, zostanie zwrócony domyślny dostawca obiektu System.Data.OleDb.
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ConnectionString">
            <summary>
            Pobiera wartość reprezentującą parametry połączenia dla źródła danych.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.TableName">
            <summary>
            Pobiera wartość wskazującą nazwę tabeli udostępniającej dane.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataAccessMethod">
             <summary>
            Pobiera metodę używaną do uzyskiwania dostępu do źródła danych.
             </summary>
            
             <returns>
            Jedna z <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod"/>. Jeśli nie zainicjowano <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>, zwróci wartość domyślną <see cref="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random"/>.
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataSourceSettingName">
            <summary>
            Pobiera nazwę źródła danych znajdującego się w sekcji &lt;microsoft.visualstudio.qualitytools&gt; w pliku app.config.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute">
            <summary>
            Atrybut dla testu opartego na danych, w którym dane można określić bezpośrednio.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Znajdź wszystkie wiersze danych i wykonaj.
            </summary>
            <param name="testMethod">
            Metoda testowa.
            </param>
            <returns>
            Tablica elementów <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.RunDataDrivenTest(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod,Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute[])">
            <summary>
            Uruchamianie metody testowej dla testu opartego na danych.
            </summary>
            <param name="testMethod"> Metoda testowa do wykonania. </param>
            <param name="dataRows"> Wiersz danych. </param>
            <returns> Wyniki wykonania. </returns>
        </member>
    </members>
</doc>
