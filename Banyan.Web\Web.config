﻿<?xml version="1.0"?>
<!--
  有关如何配置 ASP.NET 应用程序的详细信息，请访问
  https://go.microsoft.com/fwlink/?LinkId=169433
-->
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
  </configSections>
  <appSettings>
    <add key="webpages:Version" value="*******"/>
    <add key="webpages:Enabled" value="false"/>
    <add key="PreserveLoginUrl" value="true"/>
    <add key="ClientValidationEnabled" value="true"/>
    <add key="UnobtrusiveJavaScriptEnabled" value="true"/>
    <add key="appKey" value="wx13a4685c552fa65f"/>
    <add key="appSecret" value="5585b96303b020fa1a3b73b6785a866d"/>
    <add key="appKeyWeb" value="wxe2c6ee8162e702fb"/>
    <add key="appSecretWeb" value="7f6e78a4ec892990b9a8de573cdca956"/>
    <!--Redis服务地址-->
    <!--<add key="RedisPath" value="*************:6379" />-->
    <!--<add key="RedisPath" value="************:6380" />-->
    <add key="RedisPath" value="127.0.0.1:6379"/>
    <!--文件服务器地址-->
    <add key="FileDomain" value="http://localhost:59348"/>
    <add key="ldappw" value="!Gaorong1990"/>
    <!--浏览量更新间隔秒数-->
    <add key="ViewCountUpdateInterval" value="90"/>
    <!--文章更新推送模板Id-->
    <!--<add key="ArticlePushTelpletId" value="0G8Pdmi75EzV9Z9GJWsgd4V4pMwwzbDoH91SoQ5-uZc" />-->
    <add key="Microsoft.VisualStudio.Enterprise.AspNetHelper.VsInstrLocation" value="C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Team Tools\Performance Tools\vsinstr.exe"/>
  </appSettings>
  <connectionStrings>
    <add name="ldappw" connectionString="!Gaorong1990"/>
    <!-- <add name="QLWL" connectionString="Data Source=grp.database.windows.net,1433;Initial Catalog=IMS;Persist Security Info=False;User ID=grvc;Password=************" /> -->
    <add name="QLWL" connectionString="Data Source=failproof.database.chinacloudapi.cn,1433;Initial Catalog=IMS;Persist Security Info=False;User ID=grvc;Password=************"/>
    <add name="Survey" connectionString="Server=gaorong.database.chinacloudapi.cn,1433;Initial Catalog=Research;Persist Security Info=False;User ID=grvc;Password=************;MultipleActiveResultSets=False;Connection Timeout=30;"/>
    <add name="BusinessCenterPortal" connectionString="Data Source=savefms.database.chinacloudapi.cn,1433;Initial Catalog=FMS;Persist Security Info=False;User ID=banyanvc;Password=************;Connection Timeout=30;MultipleActiveResultSets=False;"/>
    <!-- <add name="TestDB" connectionString="Data Source=127.0.0.1;Initial Catalog=DB;Persist Security Info=True;User ID=banyanvc;Password=************" providerName="System.Data.SqlClient" /> -->
  </connectionStrings>
  <!--
    有关 web.config 更改的说明，请参见 http://go.microsoft.com/fwlink/?LinkId=235367。

    可在 <httpRuntime> 标记上设置以下特性。
      <system.Web>
        <httpRuntime targetFramework="4.7.2" />
      </system.Web>
  -->
  <system.web>
    <customErrors mode="Off"/>
    <compilation debug="true" targetFramework="4.6.2"/>
    <httpRuntime targetFramework="4.6.1" requestValidationMode="2.0" maxRequestLength="2097151"/>
    <pages>
      <namespaces>
        <add namespace="System.Web.Helpers"/>
        <add namespace="System.Web.Mvc"/>
        <add namespace="System.Web.Mvc.Ajax"/>
        <add namespace="System.Web.Mvc.Html"/>
        <add namespace="System.Web.Routing"/>
        <add namespace="System.Web.WebPages"/>
      </namespaces>
    </pages>
    <httpModules>
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web"/>
      <add name="TelemetryCorrelationHttpModule" type="Microsoft.AspNet.TelemetryCorrelation.TelemetryCorrelationHttpModule, Microsoft.AspNet.TelemetryCorrelation"/>
    </httpModules>
  </system.web>
  <system.web.extensions>
    <scripting>
      <webServices>
        <!--单位为字节-->
        <jsonSerialization maxJsonLength="1024000"/>
      </webServices>
    </scripting>
  </system.web.extensions>
  <system.webServer>
    <security>
      <requestFiltering>
        <!-- 1 GB -->
        <requestLimits maxAllowedContentLength="**********"/>
      </requestFiltering>
    </security>
    <staticContent>
      <!--<mimeMap fileExtension=".woff" mimeType="application/x-font-woff" />
      <mimeMap fileExtension=".woff2" mimeType="application/x-font-woff" />-->
    </staticContent>
    <validation validateIntegratedModeConfiguration="false"/>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0"/>
      <remove name="OPTIONSVerbHandler"/>
      <remove name="TRACEVerbHandler"/>
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0"/>
    </handlers>
    <modules>
      <remove name="TelemetryCorrelationHttpModule"/>
      <remove name="ApplicationInsightsWebTracking"/>
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" preCondition="managedHandler"/>
      <add name="TelemetryCorrelationHttpModule" type="Microsoft.AspNet.TelemetryCorrelation.TelemetryCorrelationHttpModule, Microsoft.AspNet.TelemetryCorrelation" preCondition="managedHandler"/>
    </modules>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed"/>
        <bindingRedirect oldVersion="0.0.0.0-11.0.0.0" newVersion="11.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-5.2.0.0" newVersion="5.2.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.1" newVersion="4.0.1.1"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-2.0.8.0" newVersion="2.0.8.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.ApplicationInsights" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-2.13.1.12554" newVersion="2.13.1.12554"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.VisualStudio.Enterprise.AspNetHelper" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <codeBase version="16.0.0.0" href="file:///C:/Program%20Files%20(x86)/Microsoft%20Visual%20Studio/Shared/Common/VSPerfCollectionTools/vs2019/Microsoft.VisualStudio.Enterprise.AspNetHelper.DLL"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="VsWebSite.Interop" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <codeBase version="8.0.0.0" href="file:///C:/Program%20Files%20(x86)/Microsoft%20Visual%20Studio/Shared/Common/VSPerfCollectionTools/vs2019/VsWebSite.Interop.DLL"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <log4net>
    <root>
      <level value="ALL"/>
      <appender-ref ref="aiAppender"/>
    </root>
    <appender name="aiAppender" type="Microsoft.ApplicationInsights.Log4NetAppender.ApplicationInsightsAppender, Microsoft.ApplicationInsights.Log4NetAppender">
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%message%newline"/>
      </layout>
    </appender>
  </log4net>
</configuration>