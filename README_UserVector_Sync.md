# 用户向量同步到UserProfile数据库

## 概述
本次更新实现了用户兴趣向量从缓存到UserProfile数据库的同步功能，确保向量数据的持久化存储和高可用性。

## 主要变更

### 1. UserProfile实体模型更新
**文件**: `Banyan.Domain/SqlModels/UserProfile.cs`

添加了新的字段：
```csharp
/// <summary>
/// 用户兴趣向量JSON（1024维向量）
/// </summary>
[SqlField]
public string InterestVector { get; set; }

/// <summary>
/// 向量更新时间
/// </summary>
[SqlField]
public DateTime? VectorUpdateTime { get; set; }
```

### 2. UserProfileBLL业务逻辑更新
**文件**: `Banyan.Apps/UserProfileBLL.cs`

#### 新增方法
- `SyncVectorToUserProfileAsync()`: 同步向量到UserProfile数据库
- `GetVectorUpdateStatsAsync()`: 获取向量更新统计信息

#### 更新的方法
- `GenerateUserInterestVectorAsync()`: 生成向量后同步到数据库，更新VectorUpdateTime
- `UpdateUserVectorWithNewsAsync()`: 更新向量后同步到数据库，更新VectorUpdateTime
- `GetUserVectorFromDatabaseAsync()`: 优先从UserProfile获取向量，支持VectorUpdateTime
- `IsVectorUpdateNeeded()`: 基于VectorUpdateTime判断向量是否需要更新

### 3. 数据库迁移脚本
**文件**: `Database/Migration_AddInterestVectorToUserProfile.sql`

- 添加InterestVector列（NVARCHAR(MAX)）
- 添加VectorUpdateTime列（DATETIME）
- 添加列注释
- 创建性能优化索引（包含VectorUpdateTime索引）

## 功能特性

### 1. 双重存储策略
- **缓存优先**: Redis缓存提供高性能访问
- **数据库备份**: UserProfile表提供持久化存储

### 2. 自动同步机制
- 向量生成时自动同步到数据库
- 向量更新时自动同步到数据库
- 缓存失效时优先从数据库恢复

### 3. 数据一致性保证
- JSON序列化存储1024维向量
- 维度验证确保数据完整性
- VectorUpdateTime跟踪向量更新时间
- 异常处理保证系统稳定性

## 数据流程

### 向量生成流程
```
1. 分析用户标签关联
2. 计算用户兴趣向量
3. 缓存向量到Redis
4. 同步向量到UserProfile数据库 ✨ 新增
5. 记录操作日志
```

### 向量获取流程
```
1. 优先从Redis缓存获取
2. 缓存未命中时从UserProfile数据库获取 ✨ 新增
3. 数据库也没有时重新生成向量
4. 更新缓存和数据库
```

### 向量更新流程
```
1. 基于新闻或用户行为更新向量
2. 更新Redis缓存
3. 同步更新到UserProfile数据库 ✨ 新增
4. 记录更新日志
```

## 性能优化

### 1. 索引优化
- 创建UserId相关的复合索引
- 包含InterestVector和VectorUpdateTime字段提高查询性能
- 创建VectorUpdateTime专用索引支持向量过期检查

### 2. 异步处理
- 数据库同步操作异步执行
- 不影响主要业务流程性能

### 3. 错误处理
- JSON序列化异常处理
- 数据库操作异常处理
- 向量维度验证

## 部署步骤

### 1. 数据库迁移
```sql
-- 执行迁移脚本
EXEC Database/Migration_AddInterestVectorToUserProfile.sql
```

### 2. 代码部署
- 部署更新后的Banyan.Domain.dll
- 部署更新后的Banyan.Apps.dll

### 3. 验证部署
- 检查UserProfile表结构
- 验证向量生成和同步功能
- 监控系统日志

## 监控指标

### 1. 数据一致性
- 缓存命中率
- 数据库同步成功率
- 向量维度验证通过率

### 2. 性能指标
- 向量生成耗时
- 数据库同步耗时
- 向量获取响应时间
- 向量更新频率统计

### 3. 错误监控
- JSON序列化错误
- 数据库操作错误
- 向量维度不匹配错误

## 注意事项

1. **数据迁移**: 现有用户的向量数据将在下次更新时自动同步到数据库
2. **存储空间**: InterestVector字段存储JSON格式，约占用8KB空间
3. **兼容性**: 保持向后兼容，缓存优先策略不变
4. **时间跟踪**: VectorUpdateTime字段帮助识别过期向量和优化缓存策略
5. **监控**: 建议监控数据库同步操作的成功率和性能
6. **统计功能**: 可使用GetVectorUpdateStatsAsync()方法获取向量更新统计信息

## 相关文件

- `Banyan.Domain/SqlModels/UserProfile.cs` - 实体模型
- `Banyan.Apps/UserProfileBLL.cs` - 业务逻辑
- `Database/Migration_AddInterestVectorToUserProfile.sql` - 数据库迁移
- `README_UserVector_Sync.md` - 本文档
