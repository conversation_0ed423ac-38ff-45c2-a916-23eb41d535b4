using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Banyan.Code;
using Banyan.Domain;
using Banyan.Apps;
using Banyan.Apps.Configs;
using Newtonsoft.Json;

namespace Banyan.Apps
{
    /// <summary>
    /// 新闻推荐引擎模块
    /// 负责基于用户兴趣标签和新闻内容，实现精准的个性化新闻推荐
    /// </summary>
    public class NewsRecommendationEngine
    {
        private readonly ICache _cache;
        private readonly UserProfileBLL _userProfileBLL;
        private readonly VectorService _vectorService;
        private readonly NewsVectorizationService _vectorizationService;
        private readonly UserInterestVectorRetrieval _userInterestVectorRetrieval;
        private readonly NewsVectorSearch _newsVectorSearch;
        private readonly RecommendationCacheManager _recommendationCacheManager;

        // 默认相似度阈值
        private const double DEFAULT_SIMILARITY_THRESHOLD = 0.4;
        // 默认推荐结果数量
        private const int DEFAULT_RECOMMENDATION_LIMIT = 10;
        // 推荐结果缓存时间（小时）
        private const int RECOMMENDATION_CACHE_HOURS = 4;

        // 默认构造函数，用于简单场景
        public NewsRecommendationEngine()
        {
            _cache = CacheFactory.Cache();
            _userProfileBLL = new UserProfileBLL();
            _vectorService = new VectorService();
            _vectorizationService = new NewsVectorizationService();
            _userInterestVectorRetrieval = new UserInterestVectorRetrieval();
            _newsVectorSearch = new NewsVectorSearch();
            _recommendationCacheManager = RecommendationCacheManager.Instance;
        }

        // 完整构造函数，用于依赖注入
        public NewsRecommendationEngine(ICache cache, UserProfileBLL userProfileBLL, VectorService vectorService)
        {
            _cache = cache;
            _userProfileBLL = userProfileBLL;
            _vectorService = vectorService;
            _vectorizationService = new NewsVectorizationService();
            _userInterestVectorRetrieval = new UserInterestVectorRetrieval();
            _newsVectorSearch = new NewsVectorSearch();
            _recommendationCacheManager = RecommendationCacheManager.Instance;
        }

        #region 个性化推荐方法

        /// <summary>
        /// 获取用户个性化推荐新闻
        /// 基于用户兴趣向量和新闻内容相似度进行推荐
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="limit">返回结果数量</param>
        /// <param name="threshold">相似度阈值</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>推荐新闻列表</returns>
        public async Task<List<News>> GetPersonalizedRecommendationsAsync(
            int userId,
            int limit = DEFAULT_RECOMMENDATION_LIMIT,
            double threshold = DEFAULT_SIMILARITY_THRESHOLD,
            NewsSearchFilters filters = null)
        {
            try
            {
                Logger.Info($"开始为用户 {userId} 生成个性化推荐，限制数量: {limit}，相似度阈值: {threshold}");

                // 首先尝试获取预计算的推荐结果
                var precomputeService = NewsPrecomputeService.Instance;
                var precomputedRecommendations = precomputeService.GetPrecomputedUserRecommendations(userId);

                if (precomputedRecommendations != null && precomputedRecommendations.Count > 0)
                {
                    Logger.Info($"从预计算缓存获取用户 {userId} 的推荐结果，数量: {precomputedRecommendations.Count}");

                    // 转换为News对象并应用过滤条件
                    var precomputedNews = await ConvertSimilarityResultsToNews(precomputedRecommendations, threshold, filters);
                    if (precomputedNews.Count > 0)
                    {
                        var finalPrecomputedResults = precomputedNews.Take(limit).ToList();
                        Logger.Info($"预计算结果过滤后返回 {finalPrecomputedResults.Count} 条推荐");
                        return finalPrecomputedResults;
                    }
                }

                // 尝试从Redis缓存获取结果
                var cachedResult = await _recommendationCacheManager.GetCachedPersonalizedRecommendationsAsync(
                    userId, limit, threshold, filters);

                if (cachedResult != null && cachedResult.Count > 0)
                {
                    Logger.Info($"从Redis缓存获取用户 {userId} 的推荐结果，数量: {cachedResult.Count}");
                    return cachedResult;
                }
                
                // 获取用户兴趣向量
                var userVector = await _userInterestVectorRetrieval.GetUserInterestVectorAsync(userId);
                if (userVector == null)
                {
                    Logger.Warn($"用户 {userId} 没有兴趣向量，使用默认推荐策略");
                    return await GetDefaultRecommendationsAsync(limit, filters);
                }
                
                // 使用用户兴趣向量搜索相似新闻
                var searchResults = await _vectorService.SearchSimilarNewsAsync(userVector, limit * 2, threshold);
                if (searchResults == null || !searchResults.Any())
                {
                    Logger.Warn($"未找到与用户 {userId} 兴趣相似的新闻，使用默认推荐策略");
                    return await GetDefaultRecommendationsAsync(limit, filters);
                }
                
                // 获取新闻详情
                var newsBLL = new NewsBLL();
                var recommendedNews = new List<News>();
                
                foreach (var result in searchResults)
                {
                    if (int.TryParse(result.Id, out int newsId))
                    {
                        var news = newsBLL.GetModel(newsId);
                        if (news != null)
                        {
                            // 添加相似度分数
                            news.MatchScore = result.Score;
                            recommendedNews.Add(news);
                        }
                    }
                }
                
                // 应用过滤和排序
                var filteredNews = ApplyFiltersAndSort(recommendedNews, filters);
                var finalResults = filteredNews.Take(limit).ToList();
                
                // 缓存结果到Redis
                if (finalResults.Count > 0)
                {
                    // 设置缓存过期时间为配置的小时数
                    TimeSpan cacheExpiration = TimeSpan.FromHours(RECOMMENDATION_CACHE_HOURS);
                    
                    await _recommendationCacheManager.SetCachedPersonalizedRecommendationsAsync(
                        userId, finalResults, limit, threshold, filters, cacheExpiration);
                }
                
                Logger.Info($"为用户 {userId} 生成个性化推荐完成，结果数量: {finalResults.Count}");
                return finalResults;
            }
            catch (Exception ex)
            {
                Logger.Error($"为用户 {userId} 生成个性化推荐失败: {ex.Message}", ex);
                return await GetDefaultRecommendationsAsync(limit, filters);
            }
        }
        
        /// <summary>
        /// 获取用户混合推荐新闻
        /// 结合用户兴趣和热门新闻进行推荐
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="limit">返回结果数量</param>
        /// <param name="interestRatio">兴趣推荐比例（0-1之间）</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>混合推荐新闻列表</returns>
        public async Task<List<News>> GetHybridRecommendationsAsync(
            int userId, 
            int limit = DEFAULT_RECOMMENDATION_LIMIT, 
            double interestRatio = 0.7,
            NewsSearchFilters filters = null)
        {
            try
            {
                Logger.Info($"开始为用户 {userId} 生成混合推荐，限制数量: {limit}，兴趣比例: {interestRatio}");
                
                // 尝试从Redis缓存获取结果
                var cachedResult = await _recommendationCacheManager.GetCachedHybridRecommendationsAsync(
                    userId, limit, interestRatio, filters);
                    
                if (cachedResult != null && cachedResult.Count > 0)
                {
                    Logger.Info($"从Redis缓存获取用户 {userId} 的混合推荐结果，数量: {cachedResult.Count}");
                    return cachedResult;
                }
                
                // 参数验证
                interestRatio = Math.Max(0, Math.Min(1, interestRatio));
                
                // 计算兴趣推荐和热门推荐的数量
                int interestCount = (int)Math.Ceiling(limit * interestRatio);
                int popularCount = limit - interestCount;
                
                // 获取兴趣推荐
                var interestRecommendations = await GetPersonalizedRecommendationsAsync(
                    userId, 
                    interestCount, 
                    DEFAULT_SIMILARITY_THRESHOLD, 
                    filters);
                
                // 获取热门推荐（排除已经推荐的新闻）
                var excludeIds = interestRecommendations.Select(n => n.Id).ToList();
                var popularFilters = filters?.Clone() ?? new NewsSearchFilters();
                popularFilters.ExcludeNewsIds = popularFilters.ExcludeNewsIds ?? new List<int>();
                popularFilters.ExcludeNewsIds.AddRange(excludeIds);
                
                var popularRecommendations = await GetPopularNewsAsync(popularCount, popularFilters);
                
                // 合并结果
                var result = new List<News>();
                result.AddRange(interestRecommendations);
                result.AddRange(popularRecommendations);
                
                // 缓存结果到Redis
                if (result.Count > 0)
                {
                    // 设置缓存过期时间为配置的小时数
                    TimeSpan cacheExpiration = TimeSpan.FromHours(RECOMMENDATION_CACHE_HOURS);
                    
                    await _recommendationCacheManager.SetCachedHybridRecommendationsAsync(
                        userId, result, limit, interestRatio, filters, cacheExpiration);
                }
                
                Logger.Info($"为用户 {userId} 生成混合推荐完成，兴趣推荐: {interestRecommendations.Count}，热门推荐: {popularRecommendations.Count}");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"为用户 {userId} 生成混合推荐失败: {ex.Message}", ex);
                return await GetDefaultRecommendationsAsync(limit, filters);
            }
        }
        
        /// <summary>
        /// 获取热门新闻
        /// </summary>
        /// <param name="limit">返回结果数量</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>热门新闻列表</returns>
        public async Task<List<News>> GetPopularNewsAsync(int limit = DEFAULT_RECOMMENDATION_LIMIT, NewsSearchFilters filters = null)
        {
            try
            {
                Logger.Info($"开始获取热门新闻，限制数量: {limit}");
                
                // 尝试从Redis缓存获取结果
                var cachedResult = await _recommendationCacheManager.GetCachedPopularNewsAsync(limit, filters);
                    
                if (cachedResult != null && cachedResult.Count > 0)
                {
                    Logger.Info($"从Redis缓存获取热门新闻，数量: {cachedResult.Count}");
                    return cachedResult;
                }
                
                // 获取热门新闻
                var newsBLL = new NewsBLL();
                var where = "VectorStatus = 1"; // 只获取已向量化的新闻
                
                // 应用过滤条件
                if (filters != null)
                {
                    where = ApplyWhereFilters(where, filters);
                }
                
                // 按发布时间和优先级排序
                var orderBy = "PubTime DESC, priority DESC";
                var fields = "Id, Title, Content, Source, Classify, PubTime, Tag, Url";
                
                var newsList = newsBLL.GetList(where, limit, 1, fields, orderBy);
                
                // 缓存结果到Redis
                if (newsList.Count > 0)
                {
                    // 设置缓存过期时间为配置的小时数
                    TimeSpan cacheExpiration = TimeSpan.FromHours(RECOMMENDATION_CACHE_HOURS);
                    
                    await _recommendationCacheManager.SetCachedPopularNewsAsync(
                        newsList, limit, filters, cacheExpiration);
                }
                
                Logger.Info($"获取热门新闻完成，结果数量: {newsList.Count}");
                return newsList;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取热门新闻失败: {ex.Message}", ex);
                return new List<News>();
            }
        }
        
        /// <summary>
        /// 获取默认推荐新闻
        /// 当用户没有兴趣向量或推荐失败时使用
        /// </summary>
        /// <param name="limit">返回结果数量</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>默认推荐新闻列表</returns>
        public async Task<List<News>> GetDefaultRecommendationsAsync(int limit = DEFAULT_RECOMMENDATION_LIMIT, NewsSearchFilters filters = null)
        {
            // 默认推荐使用热门新闻
            return await GetPopularNewsAsync(limit, filters);
        }
        
        /// <summary>
        /// 记录用户对推荐新闻的反馈
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="newsId">新闻ID</param>
        /// <param name="feedbackType">反馈类型（点击、收藏、分享等）</param>
        /// <param name="score">反馈分数</param>
        /// <returns>操作结果</returns>
        public async Task<bool> RecordUserFeedbackAsync(int userId, int newsId, string feedbackType, double score = 1.0)
        {
            try
            {
                Logger.Info($"记录用户 {userId} 对新闻 {newsId} 的反馈，类型: {feedbackType}，分数: {score}");
                
                // 这里应该实现反馈记录逻辑，可以存储到数据库或缓存中
                // 为了简化实现，这里只记录日志
                
                // 清除用户推荐缓存，以便下次获取最新推荐
                await ClearUserRecommendationCacheAsync(userId);
                
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"记录用户 {userId} 反馈失败: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 清除用户推荐缓存
        /// </summary>
        /// <param name="userId">用户ID</param>
        private async Task ClearUserRecommendationCacheAsync(int userId)
        {
            try
            {
                // 使用Redis缓存管理器清除用户的所有推荐缓存
                await _recommendationCacheManager.InvalidateUserCacheAsync(userId);
                Logger.Info($"清除用户 {userId} 的推荐缓存");
            }
            catch (Exception ex)
            {
                Logger.Error($"清除用户 {userId} 推荐缓存失败: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 应用过滤条件并排序
        /// </summary>
        /// <param name="newsList">新闻列表</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>过滤和排序后的新闻列表</returns>
        private List<News> ApplyFiltersAndSort(List<News> newsList, NewsSearchFilters filters)
        {
            if (newsList == null || newsList.Count == 0)
            {
                return new List<News>();
            }
            
            var result = newsList;
            
            // 应用过滤条件
            if (filters != null)
            {
                // 分类过滤
                if (!string.IsNullOrEmpty(filters.Category))
                {
                    result = result.Where(n => n.Classify == filters.Category).ToList();
                }
                
                // 来源过滤
                if (!string.IsNullOrEmpty(filters.Source))
                {
                    result = result.Where(n => n.Source == filters.Source).ToList();
                }
                
                // 日期范围过滤
                if (filters.StartDate.HasValue)
                {
                    result = result.Where(n => n.PubTime >= filters.StartDate.Value).ToList();
                }
                
                if (filters.EndDate.HasValue)
                {
                    result = result.Where(n => n.PubTime < filters.EndDate.Value.AddDays(1)).ToList();
                }
                
                // 标签过滤
                if (!string.IsNullOrEmpty(filters.Tag))
                {
                    result = result.Where(n => n.Tag != null && n.Tag.Contains(filters.Tag)).ToList();
                }
                
                // 排除特定新闻ID
                if (filters.ExcludeNewsIds != null && filters.ExcludeNewsIds.Count > 0)
                {
                    result = result.Where(n => !filters.ExcludeNewsIds.Contains(n.Id)).ToList();
                }
            }
            
            // 按相似度降序排序
            result = result.OrderByDescending(n => n.MatchScore).ToList();
            
            return result;
        }
        
        /// <summary>
        /// 应用SQL WHERE过滤条件
        /// </summary>
        /// <param name="where">原始WHERE条件</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>更新后的WHERE条件</returns>
        private string ApplyWhereFilters(string where, NewsSearchFilters filters)
        {
            if (filters == null)
            {
                return where;
            }
            
            // 分类过滤
            if (!string.IsNullOrEmpty(filters.Category))
            {
                where += $" AND Classify = '{filters.Category}'";
            }
            
            // 来源过滤
            if (!string.IsNullOrEmpty(filters.Source))
            {
                where += $" AND Source = '{filters.Source}'";
            }
            
            // 日期范围过滤
            if (filters.StartDate.HasValue)
            {
                where += $" AND PubTime >= '{filters.StartDate.Value:yyyy-MM-dd}'";
            }
            
            if (filters.EndDate.HasValue)
            {
                where += $" AND PubTime < '{filters.EndDate.Value.AddDays(1):yyyy-MM-dd}'";
            }
            
            // 标签过滤
            if (!string.IsNullOrEmpty(filters.Tag))
            {
                where += $" AND Tag LIKE '%{filters.Tag}%'";
            }
            
            // 排除特定新闻ID
            if (filters.ExcludeNewsIds != null && filters.ExcludeNewsIds.Count > 0)
            {
                var excludeList = string.Join(",", filters.ExcludeNewsIds);
                where += $" AND Id NOT IN ({excludeList})";
            }
            
            return where;
        }

        #region 向量搜索方法

        /// <summary>
        /// 根据文本搜索相似新闻
        /// </summary>
        /// <param name="queryText">查询文本</param>
        /// <param name="limit">返回结果数量</param>
        /// <returns>相似新闻列表</returns>
        public List<News> SearchSimilarNewsByText(string queryText, int limit = 10)
        {
            try
            {
                Logger.Info($"开始根据文本搜索相似新闻: {queryText}");

                // 1. 生成查询文本的向量表示
                var queryVector = GenerateTextVectorAsync(queryText).GetAwaiter().GetResult();
                if (queryVector == null)
                {
                    Logger.Warn("生成查询向量失败");
                    return new List<News>();
                }

                // 2. 使用向量搜索相似新闻
                var searchResults = _vectorService.SearchSimilarNewsAsync(queryVector, limit, 0.3).GetAwaiter().GetResult();
                if (searchResults == null || searchResults.Count() == 0)
                {
                    Logger.Info("未找到相似新闻");
                    return new List<News>();
                }

                // 3. 获取新闻详情
                var newsBLL = new NewsBLL();
                var newsList = new List<News>();

                foreach (var result in searchResults)
                {
                    if (int.TryParse(result.Id, out int newsId))
                    {
                        var news = newsBLL.GetModel(newsId);
                        if (news != null)
                        {
                            // 添加相似度分数
                            news.MatchScore = result.Score;
                            newsList.Add(news);
                        }
                    }
                }

                Logger.Info($"向量搜索成功，找到 {newsList.Count} 条相似新闻");
                return newsList;
            }
            catch (Exception ex)
            {
                Logger.Error($"向量搜索失败: {ex.Message}", ex);
                return new List<News>();
            }
        }

        /// <summary>
        /// 获取与指定新闻相似的新闻
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <param name="limit">返回结果数量</param>
        /// <returns>相似新闻列表</returns>
        public List<News> GetSimilarNews(int newsId, int limit = 5)
        {
            try
            {
                Logger.Info($"开始获取相似新闻，新闻ID: {newsId}");

                // 1. 获取新闻向量
                var newsVector = _vectorizationService.GetNewsVectorAsync(newsId, new NewsBLL()).GetAwaiter().GetResult();
                if (newsVector == null)
                {
                    Logger.Warn($"新闻向量不存在，新闻ID: {newsId}");
                    return new List<News>();
                }

                // 2. 使用向量搜索相似新闻
                var searchResults = _vectorService.SearchSimilarNewsAsync(newsVector, limit + 1, 0.5).GetAwaiter().GetResult();
                if (searchResults == null || searchResults.Count() == 0)
                {
                    Logger.Info("未找到相似新闻");
                    return new List<News>();
                }

                // 3. 获取新闻详情并排除当前新闻
                var newsBLL = new NewsBLL();
                var newsList = new List<News>();

                foreach (var result in searchResults)
                {
                    if (int.TryParse(result.Id, out int resultNewsId) && resultNewsId != newsId)
                    {
                        var news = newsBLL.GetModel(resultNewsId);
                        if (news != null)
                        {
                            // 添加相似度分数
                            news.MatchScore = result.Score;
                            newsList.Add(news);
                        }
                    }
                }

                Logger.Info($"向量搜索成功，找到 {newsList.Count} 条相似新闻");
                return newsList.Take(limit).ToList();
            }
            catch (Exception ex)
            {
                Logger.Error($"获取相似新闻失败: {ex.Message}", ex);
                return new List<News>();
            }
        }

        /// <summary>
        /// 生成文本的向量表示
        /// </summary>
        /// <param name="text">输入文本</param>
        /// <returns>向量表示</returns>
        private async Task<double[]> GenerateTextVectorAsync(string text)
        {
            try
            {
                // 使用缓存避免重复计算
                var cacheKey = $"text_vector:{text.GetHashCode()}";
                var cachedVector = _cache.GetCache<double[]>(cacheKey);
                if (cachedVector != null)
                {
                    return cachedVector;
                }

                // 调用Embedding服务生成向量
                var vector = await CallEmbeddingServiceAsync(text);
                if (vector != null)
                {
                    // 缓存结果
                    _cache.WriteCache(vector, cacheKey, DateTime.Now.AddDays(7));
                }

                return vector;
            }
            catch (Exception ex)
            {
                Logger.Error($"生成文本向量失败: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 调用Embedding服务
        /// </summary>
        /// <param name="text">输入文本</param>
        /// <returns>向量数组</returns>
        private async Task<double[]> CallEmbeddingServiceAsync(string text)
        {
            try
            {
                if (string.IsNullOrEmpty(text))
                {
                    Logger.Error("调用Embedding服务失败：输入文本为空");
                    return null;
                }

                Logger.Info($"开始调用Embedding服务，文本长度: {text.Length}");

                // 使用VectorService获取文本向量
                var vector = await _vectorService.GetTextEmbeddingAsync(text);

                if (vector == null || vector.Length == 0)
                {
                    throw new Exception("Embedding服务返回空向量");
                }

                // 验证向量维度
                if (vector.Length != VectorServiceConfig.VECTOR_DIMENSION)
                {
                    throw new Exception($"向量维度不匹配，期望: {VectorServiceConfig.VECTOR_DIMENSION}，实际: {vector.Length}");
                }

                Logger.Info($"Embedding服务调用成功，向量维度: {vector.Length}");
                return vector;
            }
            catch (Exception ex)
            {
                Logger.Error($"调用Embedding服务失败: {ex.Message}", ex);
                return null;
            }
        }

        #endregion

        #region Web API 专用方法

        /// <summary>
        /// 获取基于用户兴趣的推荐新闻（Web API专用）
        /// 包含分页、评分计算、格式化等功能
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="page">页码（从1开始）</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="threshold">相似度阈值（0-1之间）</param>
        /// <param name="category">新闻分类过滤</param>
        /// <param name="source">新闻来源过滤</param>
        /// <param name="startDate">开始日期过滤</param>
        /// <param name="endDate">结束日期过滤</param>
        /// <param name="tagFilter">标签过滤</param>
        /// <returns>推荐新闻结果</returns>
        public async Task<object> GetRecommendationsForApiAsync(
            int userId,
            int page = 1,
            int pageSize = 20,
            double threshold = 0.4,
            string category = null,
            string source = null,
            string startDate = null,
            string endDate = null,
            string tagFilter = null)
        {
            try
            {
                // 构建过滤条件
                var filters = new NewsSearchFilters
                {
                    Category = category,
                    Source = source,
                    Tag = tagFilter
                };

                // 解析日期
                if (!string.IsNullOrEmpty(startDate) && DateTime.TryParse(startDate, out DateTime parsedStartDate))
                {
                    filters.StartDate = parsedStartDate;
                }

                if (!string.IsNullOrEmpty(endDate) && DateTime.TryParse(endDate, out DateTime parsedEndDate))
                {
                    filters.EndDate = parsedEndDate;
                }

                // 构建缓存键
                var cacheKey = $"api_recommendations:{userId}:{threshold}:{filters.GetHashCode()}";

                // 尝试从缓存获取推荐结果
                var cachedRecommendations = _cache.GetCache<List<NewsVectorSimilarity>>(cacheKey);

                List<NewsVectorSimilarity> allRecommendations;
                if (cachedRecommendations != null && cachedRecommendations.Count > 0)
                {
                    Logger.Info($"从缓存获取用户 {userId} 的API推荐结果，数量: {cachedRecommendations.Count}");
                    allRecommendations = cachedRecommendations;
                }
                else
                {
                    // 执行推荐 - 获取足够的数据用于分页
                    allRecommendations = await _newsVectorSearch.GetRecommendedNewsByInterest(userId, Math.Max(pageSize * 5, 50), threshold, filters);

                    // 缓存结果（5分钟）
                    if (allRecommendations.Count > 0)
                    {
                        _cache.WriteCache(allRecommendations, cacheKey, DateTime.Now.AddMinutes(5));
                    }
                }

                // 手动实现分页
                var totalCount = allRecommendations.Count;
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
                var skipCount = (page - 1) * pageSize;
                var recommendations = allRecommendations.Skip(skipCount).Take(pageSize).ToList();

                // 转换为前端友好的格式
                var result = new List<object>();
                foreach (var item in recommendations)
                {
                    // 获取新闻的标签信息
                    var tags = ExtractNewsTags(item);

                    // 计算更真实的评分
                    var scores = CalculateRecommendationScores(item, tags, userId);

                    // 生成摘要
                    //var subject = GenerateNewsSubject(item.News);

                    result.Add(new
                    {
                        id = item.NewsId,
                        title = item.News?.Title ?? "无标题",
                        subject = item.News.Subject,
                        category = item.News?.Classify ?? "未分类",
                        source = item.News?.Source ?? "未知来源",
                        publishTime = item.News?.PubTime.ToString("yyyy-MM-dd HH:mm") ?? DateTime.Now.ToString("yyyy-MM-dd HH:mm"),
                        similarity = scores.VectorSimilarity,
                        tagMatchScore = scores.TagMatchScore,
                        matchedTagCount = scores.MatchedTagCount,
                        finalScore = scores.FinalScore,
                        tags = tags, // 保留所有标签用于详情查看
                        matchedTags = scores.MatchedTags.Select(mt => new
                        {
                            userTagName = mt.UserTagName,
                            newsTagName = mt.NewsTagName,
                            userTagWeight = mt.UserTagWeight,
                            newsTagWeight = mt.NewsTagWeight,
                            matchScore = mt.MatchScore,
                            newsTagType = mt.NewsTagType,
                            newsTagCategory = mt.NewsTagCategory
                        }).ToList() // 匹配的标签信息，用于推荐原因展示
                    });
                }

                // 按综合评分降序排序
                result = result.OrderByDescending(r => ((dynamic)r).finalScore)
                              .ThenByDescending(r => ((dynamic)r).similarity)
                              .ThenByDescending(r => ((dynamic)r).tagMatchScore)
                              .ToList();

                // 记录日志
                Logger.Info($"用户 {userId} 获取了基于兴趣的API推荐新闻，页码: {page}，结果数量: {result.Count}");

                return new {
                    code = 0,
                    data = new {
                        data = result,
                        totalCount = totalCount,
                        currentPage = page,
                        pageSize = pageSize,
                        hasNextPage = page < totalPages,
                        totalPages = totalPages
                    }
                };
            }
            catch (Exception ex)
            {
                Logger.Error($"获取API推荐新闻失败: {ex.Message}", ex);
                return new { code = 1, msg = "获取推荐新闻失败: " + ex.Message };
            }
        }

        /// <summary>
        /// 生成新闻摘要
        /// 如果Subject为空，从Content中提取摘要
        /// </summary>
        /// <param name="news">新闻对象</param>
        /// <param name="maxLength">最大长度，默认150字符</param>
        /// <returns>新闻摘要</returns>
        private string GenerateNewsSubject(News news, int maxLength = 150)
        {
            if (news == null) return "";

            // 如果已有Subject，直接返回
            var subject = news.Subject ?? "";
            if (!string.IsNullOrEmpty(subject))
            {
                return subject;
            }

            // 如果Content为空，返回空字符串
            if (string.IsNullOrEmpty(news.Content))
            {
                return "";
            }

            // 清理HTML标签和特殊字符，提取纯文本作为摘要
            var content = news.Content;
            content = System.Text.RegularExpressions.Regex.Replace(content, @"<[^>]+>", ""); // 移除HTML标签
            content = System.Text.RegularExpressions.Regex.Replace(content, @"\s+", " "); // 合并多个空白字符
            content = content.Trim();

            if (content.Length <= maxLength)
            {
                return content;
            }

            // 尝试在句号处截断，如果没有句号则直接截断
            var truncated = content.Substring(0, maxLength);
            var lastPeriod = truncated.LastIndexOf('。');
            if (lastPeriod > maxLength / 3) // 确保摘要不会太短
            {
                return truncated.Substring(0, lastPeriod + 1);
            }
            else
            {
                return truncated + "...";
            }
        }

        /// <summary>
        /// 提取新闻标签信息
        /// </summary>
        /// <param name="item">新闻向量相似度项</param>
        /// <returns>标签列表</returns>
        private List<object> ExtractNewsTags(NewsVectorSimilarity item)
        {
            var tags = new List<object>();
            if (item.News != null && !string.IsNullOrEmpty(item.News.TagAnalysis))
            {
                try
                {
                    var tagAnalysis = JsonConvert.DeserializeObject<NewsTagAnalysis>(item.News.TagAnalysis);
                    if (tagAnalysis != null)
                    {
                        // 添加主要标签
                        if (tagAnalysis.MainTags != null)
                        {
                            foreach (var tag in tagAnalysis.MainTags)
                            {
                                tags.Add(new
                                {
                                    name = CleanTagName(tag.Name),
                                    weight = tag.Weight,
                                    category = tag.Category,
                                    type = "main"
                                });
                            }
                        }

                        // 添加次要标签
                        if (tagAnalysis.SecondaryTags != null)
                        {
                            foreach (var tag in tagAnalysis.SecondaryTags)
                            {
                                tags.Add(new
                                {
                                    name = CleanTagName(tag.Name),
                                    weight = tag.Weight,
                                    category = tag.Category,
                                    type = "secondary"
                                });
                            }
                        }

                        // 添加语义关键词
                        if (tagAnalysis.SemanticKeywords != null)
                        {
                            foreach (var tag in tagAnalysis.SemanticKeywords)
                            {
                                tags.Add(new
                                {
                                    name = CleanTagName(tag.Name),
                                    weight = tag.Weight,
                                    category = tag.Category,
                                    type = "semantic"
                                });
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error($"解析新闻标签分析失败，新闻ID: {item.NewsId}", ex);
                }
            }
            return tags;
        }

        /// <summary>
        /// 计算推荐评分
        /// </summary>
        /// <param name="item">推荐项</param>
        /// <param name="tags">标签列表</param>
        /// <param name="userId">用户ID</param>
        /// <returns>评分结果</returns>
        private RecommendationScores CalculateRecommendationScores(NewsVectorSimilarity item, List<object> tags, int userId)
        {
            try
            {
                // 1. 向量相似度评分 (0-100)
                // 使用非线性变换增加区分度
                var rawSimilarity = Math.Max(0, Math.Min(1, item.Similarity));
                var vectorSimilarity = TransformSimilarityScore(rawSimilarity);

                // 2. 标签匹配评分 (0-100) - 同时获取匹配的标签信息
                var tagMatchResult = CalculateTagMatchScoreWithDetails(tags, userId);
                var tagMatchScore = tagMatchResult.Score;

                // 3. 时效性评分 (0-100) - 暂时注释掉，专注于内容相关性
                // var timelinessScore = CalculateTimelinessScore(item.News?.PubTime);
                var timelinessScore = 0; // 不考虑时效性影响

                // 4. 内容质量评分 (0-100)
                var qualityScore = CalculateContentQualityScore(item.News);

                // 5. 综合评分计算 (使用现有权重配置)
                var finalScore =
                    vectorSimilarity * RecommendationScoringConfig.VECTOR_SIMILARITY_WEIGHT +
                    tagMatchScore * RecommendationScoringConfig.TAG_MATCH_WEIGHT +
                    timelinessScore * RecommendationScoringConfig.TIMELINESS_WEIGHT +
                    qualityScore * RecommendationScoringConfig.QUALITY_WEIGHT;

                return new RecommendationScores
                {
                    VectorSimilarity = Math.Round((double)vectorSimilarity, 2),
                    TagMatchScore = Math.Round((double)tagMatchScore, 2),
                    TimelinessScore = Math.Round((double)timelinessScore, 2),
                    QualityScore = Math.Round((double)qualityScore, 2),
                    FinalScore = Math.Round((double)finalScore, 2),
                    MatchedTagCount = tagMatchResult.MatchedTags.Count,
                    MatchedTags = tagMatchResult.MatchedTags
                };
            }
            catch (Exception ex)
            {
                Logger.Error($"计算推荐评分失败: {ex.Message}", ex);

                // 返回基础评分
                var basicScore = Math.Round(Math.Max(0, Math.Min(1, item.Similarity)) * 60, 2);
                return new RecommendationScores
                {
                    VectorSimilarity = basicScore,
                    TagMatchScore = basicScore * 0.8,
                    TimelinessScore = 50,
                    QualityScore = 50,
                    FinalScore = basicScore,
                    MatchedTagCount = 0,
                    MatchedTags = new List<MatchedTagInfo>()
                };
            }
        }

        /// <summary>
        /// 变换相似度分数以增加区分度
        /// </summary>
        /// <param name="rawSimilarity">原始相似度 (0-1)</param>
        /// <returns>变换后的分数 (0-100)</returns>
        private double TransformSimilarityScore(double rawSimilarity)
        {
            // 使用S型曲线变换，增加中等相似度的区分度
            // 公式: score = 100 * (1 / (1 + exp(-k * (x - 0.5))))
            // 其中k控制曲线陡峭度，x是原始相似度

            var k = 8.0; // 调整这个值可以改变区分度
            var transformed = 1.0 / (1.0 + Math.Exp(-k * (rawSimilarity - 0.5)));

            // 映射到0-100范围，并增加一些随机性以避免完全相同的分数
            var score = transformed * 80 + 10; // 基础分数范围10-90

            // 添加基于内容的微调
            var contentAdjustment = (rawSimilarity * 1000) % 10 - 5; // -5到+5的微调
            score += contentAdjustment;

            return Math.Max(10, Math.Min(95, score));
        }

        /// <summary>
        /// 用户标签缓存
        /// </summary>
        private static readonly Dictionary<int, List<UserTagRelation>> _userTagsCache = new Dictionary<int, List<UserTagRelation>>();
        private static readonly Dictionary<int, Dictionary<int, UserInterestTag>> _userInterestTagsCache = new Dictionary<int, Dictionary<int, UserInterestTag>>();
        private static DateTime _lastCacheUpdate = DateTime.MinValue;
        private static readonly object _cacheLock = new object();

        /// <summary>
        /// 计算标签匹配评分并返回详细匹配信息
        /// </summary>
        /// <param name="tags">新闻标签</param>
        /// <param name="userId">用户ID</param>
        /// <returns>标签匹配结果</returns>
        private TagMatchResult CalculateTagMatchScoreWithDetails(List<object> tags, int userId)
        {
            var result = new TagMatchResult();

            try
            {
                if (tags == null || tags.Count == 0)
                {
                    result.Score = 5; // 无标签时给予很低的基础分数
                    return result;
                }

                // 获取用户标签（使用缓存）
                var userTags = GetUserTagsFromCache(userId);
                var userInterestTags = GetUserInterestTagsFromCache(userId);

                if (userTags == null || userTags.Count == 0)
                {
                    result.Score = 10; // 用户无兴趣标签时给予较低的基础分数
                    return result;
                }

                double totalScore = 0;
                double maxPossibleScore = 0;

                // 计算标签匹配度
                foreach (dynamic newsTag in tags)
                {
                    var newsTagName = newsTag.name?.ToString()?.ToLower();
                    var newsTagWeight = Convert.ToDouble(newsTag.weight ?? 0);
                    var newsTagType = newsTag.type?.ToString() ?? "unknown";
                    var newsTagCategory = newsTag.category?.ToString() ?? "未分类";

                    if (string.IsNullOrEmpty(newsTagName)) continue;

                    // 查找匹配的用户标签（使用更严格的匹配逻辑）
                    var matchingUserTag = userTags.FirstOrDefault(ut =>
                    {
                        if (userInterestTags.TryGetValue(ut.TagId, out var tag))
                        {
                            var userTagName = tag.Name.ToLower().Trim();
                            var newsTagNameLower = newsTagName.Trim();

                            // 更严格的匹配条件：
                            // 1. 完全匹配
                            if (string.Equals(userTagName, newsTagNameLower, StringComparison.OrdinalIgnoreCase)) return true;

                            // 2. 包含匹配（但要求长度相近，避免过度匹配）
                            var lengthDiff = Math.Abs(userTagName.Length - newsTagNameLower.Length);
                            if (lengthDiff <= 2) // 长度差不超过2个字符
                            {
                                return userTagName.Contains(newsTagNameLower) || newsTagNameLower.Contains(userTagName);
                            }

                            // 3. 对于较长的标签，允许部分匹配
                            if (userTagName.Length >= 4 && newsTagNameLower.Length >= 4)
                            {
                                return userTagName.Contains(newsTagNameLower) || newsTagNameLower.Contains(userTagName);
                            }
                        }
                        return false;
                    });

                    if (matchingUserTag != null && userInterestTags.TryGetValue(matchingUserTag.TagId, out var userTag))
                    {
                        // 计算匹配分数：用户权重 * 新闻权重，并根据匹配类型调整
                        var userTagName = userTag.Name.ToLower().Trim();
                        var newsTagNameLower = newsTagName.Trim();

                        // 根据匹配精确度调整分数
                        double matchQuality = 1.0;
                        if (string.Equals(userTagName, newsTagNameLower, StringComparison.OrdinalIgnoreCase))
                        {
                            matchQuality = 1.0; // 完全匹配，满分
                        }
                        else if (userTagName.Contains(newsTagNameLower) || newsTagNameLower.Contains(userTagName))
                        {
                            // 包含匹配，根据长度比例调整
                            var shorterLength = Math.Min(userTagName.Length, newsTagNameLower.Length);
                            var longerLength = Math.Max(userTagName.Length, newsTagNameLower.Length);
                            matchQuality = (double)shorterLength / longerLength; // 0.5-1.0之间
                        }

                        var baseMatchScore = matchingUserTag.Weight * newsTagWeight * 100;
                        var adjustedMatchScore = baseMatchScore * matchQuality;
                        totalScore += adjustedMatchScore;

                        // 添加匹配的标签信息，清理特殊字符
                        result.MatchedTags.Add(new MatchedTagInfo
                        {
                            UserTagName = CleanTagName(userTag.Name),
                            NewsTagName = CleanTagName(newsTag.name?.ToString()),
                            UserTagWeight = Math.Round(matchingUserTag.Weight, 3),
                            NewsTagWeight = Math.Round(newsTagWeight, 3),
                            MatchScore = Math.Round(adjustedMatchScore, 2),
                            NewsTagType = newsTagType,
                            NewsTagCategory = newsTagCategory
                        });
                    }

                    maxPossibleScore += newsTagWeight * 100;
                }

                // 计算最终分数
                if (maxPossibleScore > 0)
                {
                    var baseScore = (totalScore / maxPossibleScore) * 100;

                    // 根据匹配标签数量给予奖励
                    var matchBonus = Math.Min(result.MatchedTags.Count * 5, 20);

                    result.Score = Math.Max(15, Math.Min(90, baseScore + matchBonus));
                }
                else
                {
                    // 如果没有任何标签匹配，给予更低的基础分数
                    result.Score = result.MatchedTags.Count > 0 ? 25 : 5;
                }

                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"计算标签匹配评分失败: {ex.Message}", ex);
                result.Score = 30;
                return result;
            }
        }

        /// <summary>
        /// 从缓存获取用户标签
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户标签列表</returns>
        private List<UserTagRelation> GetUserTagsFromCache(int userId)
        {
            lock (_cacheLock)
            {
                // 缓存5分钟
                if (DateTime.Now - _lastCacheUpdate > TimeSpan.FromMinutes(5))
                {
                    _userTagsCache.Clear();
                    _userInterestTagsCache.Clear();
                    _lastCacheUpdate = DateTime.Now;
                }

                if (!_userTagsCache.TryGetValue(userId, out var userTags))
                {
                    var userTagRelationBLL = new UserTagRelationBLL();
                    userTags = userTagRelationBLL.GetList($"UserId = {userId}", 50, 1, "*", "Weight DESC");
                    _userTagsCache[userId] = userTags ?? new List<UserTagRelation>();
                }

                return userTags;
            }
        }

        /// <summary>
        /// 从缓存获取用户兴趣标签
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户兴趣标签字典</returns>
        private Dictionary<int, UserInterestTag> GetUserInterestTagsFromCache(int userId)
        {
            lock (_cacheLock)
            {
                if (!_userInterestTagsCache.TryGetValue(userId, out var userInterestTags))
                {
                    userInterestTags = new Dictionary<int, UserInterestTag>();
                    var userTags = GetUserTagsFromCache(userId);

                    if (userTags != null && userTags.Count > 0)
                    {
                        var userInterestTagBLL = new UserInterestTagBLL();
                        var tagIds = userTags.Select(ut => ut.TagId).Distinct().ToList();

                        // 批量获取标签信息
                        foreach (var tagId in tagIds)
                        {
                            var tag = userInterestTagBLL.GetModel(tagId);
                            if (tag != null)
                            {
                                userInterestTags[tagId] = tag;
                            }
                        }
                    }

                    _userInterestTagsCache[userId] = userInterestTags;
                }

                return userInterestTags;
            }
        }

        /// <summary>
        /// 将相似度结果转换为News对象列表
        /// </summary>
        /// <param name="similarityResults">相似度结果列表</param>
        /// <param name="threshold">相似度阈值</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>News对象列表</returns>
        private async Task<List<News>> ConvertSimilarityResultsToNews(
            List<NewsVectorSimilarity> similarityResults,
            double threshold,
            NewsSearchFilters filters)
        {
            var newsList = new List<News>();

            if (similarityResults == null || similarityResults.Count == 0)
            {
                return newsList;
            }

            try
            {
                var newsBLL = new NewsBLL();

                foreach (var similarity in similarityResults)
                {
                    // 应用相似度阈值过滤
                    if (similarity.Similarity < threshold)
                    {
                        continue;
                    }

                    // 如果已经有新闻详情，直接使用
                    if (similarity.News != null)
                    {
                        similarity.News.MatchScore = similarity.Similarity;
                        newsList.Add(similarity.News);
                    }
                    else
                    {
                        // 获取新闻详情
                        var news = newsBLL.GetModel(similarity.NewsId);
                        if (news != null)
                        {
                            news.MatchScore = similarity.Similarity;
                            newsList.Add(news);
                        }
                    }
                }

                // 应用过滤条件
                if (filters != null)
                {
                    newsList = ApplyFiltersAndSort(newsList, filters);
                }

                Logger.Debug($"转换相似度结果：输入 {similarityResults.Count} 条，输出 {newsList.Count} 条");
                return newsList;
            }
            catch (Exception ex)
            {
                Logger.Error($"转换相似度结果为News对象失败: {ex.Message}", ex);
                return newsList;
            }
        }

        /// <summary>
        /// 计算内容质量评分
        /// </summary>
        /// <param name="news">新闻对象</param>
        /// <returns>内容质量分数</returns>
        private double CalculateContentQualityScore(News news)
        {
            if (news == null) return 40;

            double score = 50; // 基础分数

            // 标题质量评分
            if (!string.IsNullOrEmpty(news.Title))
            {
                var titleLength = news.Title.Length;
                if (titleLength >= 10 && titleLength <= 100) score += 10;
                if (titleLength >= 15 && titleLength <= 60) score += 5; // 理想长度额外加分
            }

            // 内容长度评分
            if (!string.IsNullOrEmpty(news.Content))
            {
                var contentLength = news.Content.Length;
                if (contentLength >= 200) score += 10;
                if (contentLength >= 500) score += 5;
                if (contentLength >= 1000) score += 5;
            }

            // 分类完整性评分
            if (!string.IsNullOrEmpty(news.Classify) && news.Classify != "未分类")
            {
                score += 5;
            }

            return Math.Max(20, Math.Min(90, score));
        }

        /// <summary>
        /// 清理标签名称中的特殊字符
        /// </summary>
        /// <param name="tagName">原始标签名称</param>
        /// <returns>清理后的标签名称</returns>
        private string CleanTagName(string tagName)
        {
            if (string.IsNullOrEmpty(tagName))
            {
                return string.Empty;
            }

            // 移除可能导致显示问题的特殊字符，保留中文、英文、数字和常用符号
            var cleaned = System.Text.RegularExpressions.Regex.Replace(tagName, @"[^\u4e00-\u9fa5a-zA-Z0-9\s\-_()（）]", "");

            // 清理多余的空格
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"\s+", " ").Trim();

            return cleaned;
        }

        #endregion

    }

    /// <summary>
    /// 推荐评分结果
    /// </summary>
    public class RecommendationScores
    {
        public double VectorSimilarity { get; set; }
        public double TagMatchScore { get; set; }
        public double TimelinessScore { get; set; }
        public double QualityScore { get; set; }
        public double FinalScore { get; set; }
        public int MatchedTagCount { get; set; }
        public List<MatchedTagInfo> MatchedTags { get; set; } = new List<MatchedTagInfo>();
    }

    /// <summary>
    /// 匹配的标签信息
    /// </summary>
    public class MatchedTagInfo
    {
        /// <summary>
        /// 用户兴趣标签名称
        /// </summary>
        public string UserTagName { get; set; }

        /// <summary>
        /// 新闻标签名称
        /// </summary>
        public string NewsTagName { get; set; }

        /// <summary>
        /// 用户标签权重
        /// </summary>
        public double UserTagWeight { get; set; }

        /// <summary>
        /// 新闻标签权重
        /// </summary>
        public double NewsTagWeight { get; set; }

        /// <summary>
        /// 匹配分数
        /// </summary>
        public double MatchScore { get; set; }

        /// <summary>
        /// 新闻标签类型 (main, secondary, semantic)
        /// </summary>
        public string NewsTagType { get; set; }

        /// <summary>
        /// 新闻标签分类
        /// </summary>
        public string NewsTagCategory { get; set; }
    }

    /// <summary>
    /// 标签匹配结果
    /// </summary>
    public class TagMatchResult
    {
        public double Score { get; set; }
        public List<MatchedTagInfo> MatchedTags { get; set; } = new List<MatchedTagInfo>();
    }
}
#endregion