using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public partial class CollectDetail
    {
        public CollectDetail() { }

        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        [SqlField]
        public int UserId { get; set; }

        /// <summary>
        /// 文章Id
        /// </summary>
        [SqlField]
        public int ArticleId { get; set; }

        [SqlField]
        public byte Mode { get; set; } = (int)CollectModeEnum.Interview;

        /// <summary>
        /// 添加时间
        /// </summary>
        [SqlField]
        public DateTime AddTime { get; set; } = DateTime.Now;
    }

    public enum CollectModeEnum
    {
        Project = 1,
        Interview = 0,
    }
}
