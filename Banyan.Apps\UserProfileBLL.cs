using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Specialized;
using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using Banyan.Apps.Configs;

namespace Banyan.Apps
{
    /// <summary>
    /// 用户画像管理业务逻辑类
    /// 完全基于AI分析用户项目数据动态生成标签
    /// </summary>
    public class UserProfileBLL : BaseDAL<UserProfile>
    {
        private MemberBLL memberBLL = new MemberBLL();
        private readonly ICache _cache;
        private readonly Log _logger;

        // 配置常量已迁移到 VectorServiceConfig 类中

        public UserProfileBLL()
        {
            _cache = CacheFactory.Cache();
            _logger = LogFactory.GetLogger("UserProfileBLL");
        }

        #region 核心功能

        /// <summary>
        /// 分析用户项目数据并生成兴趣画像
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户画像信息</returns>
        public async Task<UserProfile> AnalyzeUserProfileAsync(int userId)
        {
            try
            {
                // 参数验证
                if (userId <= 0)
                {
                    _logger.Error($"无效的用户ID: {userId}");
                    return null;
                }

                _logger.Info($"开始分析用户 {userId} 的兴趣画像");

                // 1. 获取用户最近的项目数据
                var userProjects = await GetUserRecentProjectsAsync(userId);
                if (!userProjects.Any())
                {
                    _logger.Warn($"用户 {userId} 没有项目数据，无法生成画像");
                    return null;
                }

                // 2. 构建AI分析提示词
                var prompt = BuildAnalysisPrompt(userProjects);

                // 3. 调用AI分析
                var aiResult = await CallAIAnalysisAsync(prompt);
                if (string.IsNullOrEmpty(aiResult))
                {
                    _logger.Error($"AI分析用户 {userId} 失败");
                    return null;
                }

                // 4. 解析AI结果并创建/更新标签
                var profile = await ParseAIResultAndCreateTagsAsync(userId, aiResult, userProjects.Count);

                // 5. 生成用户兴趣向量
                if (profile != null)
                {
                    await GenerateUserInterestVectorAsync(userId, profile);
                }

                // 6. 缓存用户画像和向量
                if (profile != null)
                {
                    await CacheUserProfileAsync(userId, profile);
                    _logger.Info($"用户 {userId} 兴趣画像和向量分析完成");
                }
                else
                {
                    _logger.Warn($"用户 {userId} 兴趣画像分析失败");
                }

                return profile;
            }
            catch (Exception ex)
            {
                _logger.Error($"分析用户 {userId} 兴趣画像时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 冷启动：为所有用户生成初始画像
        /// </summary>
        /// <returns>处理结果</returns>
        public async Task<UserProfileBatchProcessResult> ColdStartAsync()
        {
            try
            {
                _logger.Info("开始冷启动：为所有用户生成初始画像");

                var result = new UserProfileBatchProcessResult();
                var allUsers = await GetAllActiveUsersAsync();
                
                // 分批处理用户
                for (int i = 0; i < allUsers.Count; i += VectorServiceConfig.AI_ANALYSIS_BATCH_SIZE)
                {
                    var batch = allUsers.Skip(i).Take(VectorServiceConfig.AI_ANALYSIS_BATCH_SIZE);
                    var batchResult = await ProcessUserBatchAsync(batch);
                    result.TotalProcessed += batchResult.TotalProcessed;
                    result.SuccessCount += batchResult.SuccessCount;
                    result.FailedCount += batchResult.FailedCount;
                    
                    // 批次间延迟
                    await Task.Delay(VectorServiceConfig.BATCH_UPDATE_INTERVAL);
                }

                _logger.Info($"冷启动完成，共处理 {result.TotalProcessed} 个用户，成功 {result.SuccessCount} 个，失败 {result.FailedCount} 个");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"冷启动过程中发生错误: {ex.Message}");
                return new UserProfileBatchProcessResult { FailedCount = 1 };
            }
        }

        /// <summary>
        /// 用户保存新项目时动态更新画像
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="newProject">新项目对象</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateProfileOnNewProjectAsync(int userId, Project newProject)
        {
            try
            {
                // 1. 验证项目数据
                if (newProject == null)
                {
                    _logger.Warn($"项目对象为空");
                    return false;
                }

                // 2. 获取用户现有画像
                var existingProfile = await GetUserProfileAsync(userId);
                
                // 3. 检查是否需要更新画像（超过一周才更新）
                if (existingProfile != null)
                {
                    var timeSinceLastUpdate = DateTime.Now - existingProfile.UpdateTime;
                    if (timeSinceLastUpdate.TotalDays < VectorServiceConfig.PROFILE_EXPIRE_DAYS)
                    {
                       return true; // 返回true表示处理完成，只是不需要更新
                    }
                    else
                    {
                        _logger.Info($"用户 {userId} 画像更新时间 {existingProfile.UpdateTime:yyyy-MM-dd HH:mm:ss}，距离现在 {timeSinceLastUpdate.TotalDays:F1} 天，超过 {VectorServiceConfig.PROFILE_EXPIRE_DAYS} 天，开始更新");
                    }
                }
                else
                {
                    _logger.Info($"用户 {userId} 画像不存在，创建新画像");
                }

                // 4. 分析新项目对画像的影响
                var updatedProfile = await AnalyzeNewProjectImpactAsync(userId, newProject, existingProfile);

                // 5. 重新生成向量（如果画像发生变化）
                if (updatedProfile != null && (existingProfile == null || 
                    existingProfile.InterestDescription != updatedProfile.InterestDescription))
                {
                    await GenerateUserInterestVectorAsync(userId, updatedProfile);
                }

                // 6. 更新数据库和缓存
                if (updatedProfile != null)
                {
                    await SaveUserProfileAsync(updatedProfile);
                    await CacheUserProfileAsync(userId, updatedProfile);
                    
                    _logger.Info($"用户 {userId} 画像和向量更新完成");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error($"更新用户 {userId} 画像时发生错误: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 向量生成相关

        /// <summary>
        /// 生成用户兴趣向量
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="profile">用户画像</param>
        /// <returns>生成结果</returns>
        public async Task<bool> GenerateUserInterestVectorAsync(int userId, UserProfile profile)
        {
            try
            {
                _logger.Info($"开始生成用户 {userId} 的兴趣向量");

                // 1. 获取用户标签关联
                var userTagRelations = await GetUserTagRelationsAsync(userId);
                if (!userTagRelations.Any())
                {
                    _logger.Warn($"用户 {userId} 没有标签关联，无法生成向量");
                    return false;
                }

                // 2. 获取标签向量并计算用户兴趣向量
                var userVector = await CalculateUserInterestVectorAsync(userTagRelations);

                if (userVector != null && userVector.Length == VectorServiceConfig.VECTOR_DIMENSION)
                {
                    // 3. 缓存用户向量
                    var cacheKey = $"user_vector:{userId}";
                    _cache.WriteCache(userVector, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.VECTOR_CACHE_DAYS));

                    // 4. 同步向量到UserProfile数据库
                    await SyncVectorToUserProfileAsync(userId, userVector, profile);

                    _logger.Info($"用户 {userId} 兴趣向量生成成功，维度: {userVector.Length}");
                    return true;
                }
                else
                {
                    _logger.Error($"用户 {userId} 兴趣向量生成失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"生成用户 {userId} 兴趣向量时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 基于标签向量计算用户兴趣向量
        /// </summary>
        /// <param name="userTagRelations">用户标签关联列表</param>
        /// <returns>用户兴趣向量</returns>
        private async Task<double[]> CalculateUserInterestVectorAsync(List<UserTagRelation> userTagRelations)
        {
            try
            {
                _logger.Info($"开始计算用户兴趣向量，标签数量: {userTagRelations.Count}");

                var interestTagBLL = new UserInterestTagBLL();
                var userVector = new double[VectorServiceConfig.VECTOR_DIMENSION];
                var totalWeight = 0.0;
                var validTagCount = 0;

                // 按权重排序，确保高权重标签优先处理
                var sortedRelations = userTagRelations.OrderByDescending(r => r.Weight).ToList();

                foreach (var relation in sortedRelations)
                {
                    try
                    {
                        // 获取标签向量
                        var tagVector = await interestTagBLL.GetTagVectorAsync(relation.TagId);
                        if (tagVector != null && tagVector.Length == VectorServiceConfig.VECTOR_DIMENSION)
                        {
                            // 使用标签权重进行加权累加
                            var weight = Math.Min(relation.Weight, 1.0); // 确保权重不超过1
                            
                            for (int i = 0; i < VectorServiceConfig.VECTOR_DIMENSION; i++)
                            {
                                userVector[i] += tagVector[i] * weight;
                            }
                            
                            totalWeight += weight;
                            validTagCount++;
                            
                            _logger.Info($"标签 {relation.TagId} 向量加权累加完成，权重: {weight}");
                        }
                        else
                        {
                            _logger.Warn($"标签 {relation.TagId} 向量无效或维度不匹配");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"处理标签 {relation.TagId} 向量时发生错误: {ex.Message}");
                    }
                }

                // 只有在有有效标签向量的情况下才进行归一化处理
                if (validTagCount > 0 && totalWeight > 0)
                {
                    for (int i = 0; i < VectorServiceConfig.VECTOR_DIMENSION; i++)
                    {
                        userVector[i] /= totalWeight;
                    }
                    
                    _logger.Info($"用户兴趣向量计算完成，有效标签数: {validTagCount}，总权重: {totalWeight}");
                    return userVector;
                }
                else
                {
                    _logger.Error("没有有效的标签向量，无法生成用户兴趣向量");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"计算用户兴趣向量时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 将向量转换为字符串
        /// </summary>
        /// <param name="vector">向量数组</param>
        /// <returns>向量字符串</returns>
        private string VectorToString(double[] vector)
        {
            if (vector == null || vector.Length == 0)
                return string.Empty;
                
            return string.Join(",", vector.Select(v => v.ToString("F6")));
        }

        /// <summary>
        /// 从字符串解析向量
        /// </summary>
        /// <param name="vectorString">向量字符串</param>
        /// <returns>向量数组</returns>
        private double[] StringToVector(string vectorString)
        {
            if (string.IsNullOrEmpty(vectorString))
                return null;
                
            try
            {
                var vectorStrings = vectorString.Split(',');
                var vector = new double[vectorStrings.Length];
                
                for (int i = 0; i < vectorStrings.Length; i++)
                {
                    if (double.TryParse(vectorStrings[i], out double value))
                    {
                        vector[i] = value;
                    }
                }
                
                return vector;
            }
            catch (Exception ex)
            {
                _logger.Error($"解析向量失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 同步向量到UserProfile数据库
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="userVector">用户向量</param>
        /// <param name="profile">用户画像对象</param>
        /// <returns>同步结果</returns>
        private async Task<bool> SyncVectorToUserProfileAsync(int userId, double[] userVector, UserProfile profile)
        {
            try
            {
                if (userVector == null || userVector.Length != VectorServiceConfig.VECTOR_DIMENSION)
                {
                    _logger.Warn($"用户 {userId} 向量无效，无法同步到数据库");
                    return false;
                }

                // 将向量转换为JSON字符串存储
                var vectorJson = Newtonsoft.Json.JsonConvert.SerializeObject(userVector);

                // 更新UserProfile中的向量字段
                if (profile != null)
                {
                    profile.InterestVector = vectorJson;
                    profile.VectorUpdateTime = DateTime.Now;
                    profile.UpdateTime = DateTime.Now;

                    // 保存到数据库
                    await SaveUserProfileAsync(profile);
                    _logger.Info($"用户 {userId} 向量已同步到UserProfile数据库，维度: {userVector.Length}，更新时间: {profile.VectorUpdateTime}");
                    return true;
                }
                else
                {
                    _logger.Warn($"用户 {userId} 画像对象为空，无法同步向量");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"同步用户 {userId} 向量到数据库时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查向量是否需要更新
        /// </summary>
        /// <param name="profile">用户画像</param>
        /// <returns>是否需要更新</returns>
        private bool IsVectorUpdateNeeded(UserProfile profile)
        {
            if (profile == null)
                return true;

            // 检查缓存中是否有用户向量
            var cacheKey = $"user_vector:{profile.UserId}";
            var cachedVector = _cache.GetCache<double[]>(cacheKey);
            if (cachedVector == null)
            {
                // 缓存中没有向量，检查数据库中是否有
                if (string.IsNullOrEmpty(profile.InterestVector) || !profile.VectorUpdateTime.HasValue)
                    return true;

                // 检查向量是否过期（超过缓存有效期）
                var vectorAge = DateTime.Now - profile.VectorUpdateTime.Value;
                if (vectorAge.TotalDays > VectorServiceConfig.VECTOR_CACHE_DAYS)
                    return true;
            }

            return false;
        }

        /// <summary>
        /// 批量生成用户向量
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <returns>批量处理结果</returns>
        public async Task<UserProfileBatchProcessResult> BatchGenerateVectorsAsync(List<int> userIds)
        {
            var result = new UserProfileBatchProcessResult();
            
            // 分批处理，避免API限流
            for (int i = 0; i < userIds.Count; i += VectorServiceConfig.AI_ANALYSIS_BATCH_SIZE)
            {
                var batch = userIds.Skip(i).Take(VectorServiceConfig.AI_ANALYSIS_BATCH_SIZE);
                
                foreach (var userId in batch)
                {
                    try
                    {
                        var profile = await GetUserProfileAsync(userId);
                        if (profile != null)
                        {
                            // 检查缓存中是否已有向量
                            var cacheKey = $"user_vector:{userId}";
                            var cachedVector = _cache.GetCache<double[]>(cacheKey);
                            if (cachedVector == null) // 缓存中没有向量
                            {
                                var success = await GenerateUserInterestVectorAsync(userId, profile);
                                if (success)
                                    result.SuccessCount++;
                                else
                                    result.FailedCount++;
                            }
                        }
                        result.TotalProcessed++;
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"批量生成用户 {userId} 向量失败: {ex.Message}");
                        result.FailedCount++;
                    }
                }
                
                // 批次间延迟
                await Task.Delay(VectorServiceConfig.BATCH_UPDATE_INTERVAL);
            }
            
            return result;
        }

        #endregion

        #region 公共接口方法

        /// <summary>
        /// 获取或创建用户画像
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户画像</returns>
        public async Task<UserProfile> GetOrCreateUserProfile(int userId)
        {
            return await GetOrCreateUserProfileAsync(userId, null, 0);
        }

        /// <summary>
        /// 更新用户画像
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateUserProfile(int userId)
        {
            try
            {
                var profile = await AnalyzeUserProfileAsync(userId);
                return profile != null;
            }
            catch (Exception ex)
            {
                _logger.Error($"更新用户 {userId} 画像失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 基于用户点击的新闻更新用户兴趣画像
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="newsId">新闻ID</param>
        /// <param name="newsVector">新闻向量</param>
        /// <param name="tags">新闻标签</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateUserInterestWithNewsAsync(int userId, int newsId, double[] newsVector, List<string> tags)
        {
            try
            {
                _logger.Info($"开始基于新闻 {newsId} 更新用户 {userId} 的兴趣画像");

                // 1. 获取用户当前画像
                var userProfile = await GetUserProfileAsync(userId);
                if (userProfile == null)
                {
                    _logger.Warn($"用户 {userId} 画像不存在，无法更新");
                    return false;
                }

                // 2. 更新用户兴趣向量（基于新闻向量）
                await UpdateUserVectorWithNewsAsync(userId, newsVector);

                // 3. 更新用户标签权重（基于新闻标签）
                if (tags != null && tags.Count > 0)
                {
                    await UpdateUserTagWeightsAsync(userId, tags);
                }

                _logger.Info($"成功基于新闻 {newsId} 更新用户 {userId} 的兴趣画像");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"基于新闻 {newsId} 更新用户 {userId} 兴趣画像时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 批量更新用户画像
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="userNames">用户名称列表</param>
        /// <returns>批量更新结果</returns>
        public async Task<AjaxResult> BatchUpdateProfiles(List<int> userIds, List<string> userNames = null)
        {
            try
            {
                if (userIds == null || userIds.Count == 0)
                {
                    // 如果没有指定用户，则处理所有用户
                    var coldStartResult = await ColdStartAsync();
                    
                    // 根据处理结果决定返回状态
                    var coldStartResultCode = coldStartResult.FailedCount == 0 ? ResultCode.success : 
                                           (coldStartResult.SuccessCount == 0 ? ResultCode.exception : ResultCode.success);
                    
                    var coldStartResultMessage = coldStartResult.FailedCount == 0 ? 
                        $"批量更新完成，共处理 {coldStartResult.TotalProcessed} 个用户，成功 {coldStartResult.SuccessCount} 个。" :
                        $"批量更新部分完成，共处理 {coldStartResult.TotalProcessed} 个用户，成功 {coldStartResult.SuccessCount} 个，失败 {coldStartResult.FailedCount} 个。";
                    
                    return new AjaxResult
                    {
                        code = (int)coldStartResultCode,
                        msg = coldStartResultMessage,
                        data = coldStartResult
                    };
                }
                
                // 处理指定的用户列表
                var batchResult = await ProcessUserBatchAsync(userIds);
                
                string userListStr = "";
                if (userNames != null && userNames.Count > 0)
                {
                    userListStr = string.Join(", ", userNames);
                }
                
                // 根据处理结果决定返回状态
                var resultCode = batchResult.FailedCount == 0 ? ResultCode.success : 
                               (batchResult.SuccessCount == 0 ? ResultCode.exception : ResultCode.success);
                
                var resultMessage = batchResult.FailedCount == 0 ? 
                    $"批量更新完成，共处理 {batchResult.TotalProcessed} 个用户，成功 {batchResult.SuccessCount} 个。" :
                    $"批量更新部分完成，共处理 {batchResult.TotalProcessed} 个用户，成功 {batchResult.SuccessCount} 个，失败 {batchResult.FailedCount} 个。";
                
                if (!string.IsNullOrEmpty(userListStr))
                {
                    resultMessage += $"用户：{userListStr}";
                }
                
                return new AjaxResult
                {
                    code = (int)resultCode,
                    msg = resultMessage,
                    data = batchResult
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"批量更新用户画像失败: {ex.Message}");
                return new AjaxResult
                {
                    code = (int)ResultCode.exception,
                    msg = "批量更新失败"
                };
            }
        }
        
        /// <summary>
        /// 批量更新用户画像（兼容旧版本）
        /// </summary>
        /// <param name="paramValues">请求参数</param>
        /// <returns>批量更新结果</returns>
        public async Task<AjaxResult> BatchUpdateProfiles(NameValueCollection paramValues)
        {
            return await BatchUpdateProfiles(new List<int>(), null);
        }

        /// <summary>
        /// 为所有用户生成初始向量
        /// </summary>
        /// <returns>处理结果</returns>
        public async Task<AjaxResult> GenerateAllUserVectorsAsync()
        {
            try
            {
                _logger.Info("开始为所有用户生成初始向量");
                
                var vectorResult = await GenerateAllUserVectorsInternalAsync();
                
                // 根据处理结果决定返回状态
                var vectorResultCode = vectorResult.FailedCount == 0 ? ResultCode.success : 
                                     (vectorResult.SuccessCount == 0 ? ResultCode.exception : ResultCode.success);
                
                var vectorResultMessage = vectorResult.FailedCount == 0 ? 
                    $"批量向量生成完成，共处理 {vectorResult.TotalProcessed} 个用户，成功 {vectorResult.SuccessCount} 个。" :
                    $"批量向量生成部分完成，共处理 {vectorResult.TotalProcessed} 个用户，成功 {vectorResult.SuccessCount} 个，失败 {vectorResult.FailedCount} 个。";
                
                return new AjaxResult
                {
                    code = (int)vectorResultCode,
                    msg = vectorResultMessage,
                    data = vectorResult
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"批量向量生成过程中发生错误: {ex.Message}");
                return new AjaxResult
                {
                    code = (int)ResultCode.exception,
                    msg = "批量向量生成失败"
                };
            }
        }

        /// <summary>
        /// 为所有用户生成初始向量（内部方法）
        /// </summary>
        /// <returns>处理结果</returns>
        private async Task<UserProfileBatchProcessResult> GenerateAllUserVectorsInternalAsync()
        {
            try
            {
                _logger.Info("开始为所有用户生成初始向量");
                
                var result = new UserProfileBatchProcessResult();
                var allUsers = await GetAllActiveUsersAsync();
                
                // 分批处理用户
                for (int i = 0; i < allUsers.Count; i += VectorServiceConfig.AI_ANALYSIS_BATCH_SIZE)
                {
                    var batch = allUsers.Skip(i).Take(VectorServiceConfig.AI_ANALYSIS_BATCH_SIZE);
                    var batchResult = await BatchGenerateVectorsAsync(batch.ToList());
                    
                    result.TotalProcessed += batchResult.TotalProcessed;
                    result.SuccessCount += batchResult.SuccessCount;
                    result.FailedCount += batchResult.FailedCount;
                    
                    // 批次间延迟
                    await Task.Delay(VectorServiceConfig.BATCH_UPDATE_INTERVAL);
                }
                
                _logger.Info($"批量向量生成完成，共处理 {result.TotalProcessed} 个用户，成功 {result.SuccessCount} 个，失败 {result.FailedCount} 个");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"批量向量生成过程中发生错误: {ex.Message}");
                return new UserProfileBatchProcessResult { FailedCount = 1 };
            }
        }

        /// <summary>
        /// 获取用户推荐标签
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>推荐标签结果</returns>
        public async Task<AjaxResult> GetUserRecommendationTags(int userId)
        {
            try
            {
                var profile = await GetUserProfileAsync(userId);
                if (profile == null)
                {
                    return new AjaxResult
                    {
                        code = (int)ResultCode.notdata,
                        msg = "用户画像不存在"
                    };
                }

                // 这里应该从数据库获取用户的标签关联
                var userTags = await GetUserTagRelationsAsync(userId);
                var recommendedTags = userTags
                    .OrderByDescending(ut => ut.Weight)
                    .Take(VectorServiceConfig.MAX_RECOMMENDATION_TAGS)
                    .Select(ut => new
                    {
                        TagId = ut.TagId,
                        Weight = ut.Weight,
                        ClickCount = ut.ClickCount
                    })
                    .ToList();

                return new AjaxResult
                {
                    code = (int)ResultCode.success,
                    data = recommendedTags
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"获取用户 {userId} 推荐标签失败: {ex.Message}");
                return new AjaxResult
                {
                    code = (int)ResultCode.exception,
                    msg = "获取推荐标签失败"
                };
            }
        }

        /// <summary>
        /// 记录标签点击
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="tagName">标签名称</param>
        public void RecordTagClick(int userId, string tagName)
        {
            try
            {
                _logger.Info($"记录用户 {userId} 点击标签 {tagName}");
                
                // 这里应该更新数据库中的点击统计
                // 暂时只记录日志
                _logger.Info($"用户 {userId} 点击标签 {tagName} 已记录");
            }
            catch (Exception ex)
            {
                _logger.Error($"记录用户 {userId} 标签点击失败: {ex.Message}");
            }
        }

        #endregion

        #region AI分析相关

        /// <summary>
        /// 构建AI分析提示词
        /// </summary>
        /// <param name="projects">用户项目列表</param>
        /// <returns>AI提示词</returns>
        private string BuildAnalysisPrompt(List<Project> projects)
        {
            var projectData = projects.Select(p => new
            {
                Name = p.Name,
                Description = p.Summary + " " +p.HighLight,
                //InvestmentAmount = p.InvestHistory,
                Industry = p.RoleName,
                //Technology = p.HighLight,
                //Stage = p.Status,
                //CreateTime = p.PubTime
            }).ToList();

            // 手动构建JSON字符串，避免JsonConvert依赖
            var jsonBuilder = new System.Text.StringBuilder();
            jsonBuilder.AppendLine("[");
            for (int i = 0; i < projectData.Count(); i++)
            {
                var project = projectData.ElementAt(i);
                jsonBuilder.AppendLine("  {");
                jsonBuilder.AppendLine($"    \"Name\": \"{project.Name?.Replace("\"", "\\\"")}\",");
                jsonBuilder.AppendLine($"    \"Description\": \"{project.Description?.Replace("\"", "\\\"")}\",");
                jsonBuilder.AppendLine($"    \"Industry\": \"{project.Industry?.Replace("\"", "\\\"")}\",");
                jsonBuilder.Append(i < projectData.Count() - 1 ? "  }," : "  }");
                jsonBuilder.AppendLine();
            }
            jsonBuilder.AppendLine("]");

            var prompt = $@"
请分析以下用户的项目数据，生成该用户的兴趣画像。

用户项目数据：
{jsonBuilder.ToString()}

请基于项目数据，分析用户的：
1. 技术领域偏好（如AI、大数据、云计算、区块链、物联网、Web3、芯片、新能源等）
2. 行业领域偏好（如消费、医疗、技术、产业、Web3、教育、金融、汽车等）
3. 热点话题偏好（如数字化转型、碳中和、元宇宙、ChatGPT、新能源汽车等）
4. 其他兴趣标签

请以JSON格式返回结果，包含以下字段：
{{
  ""InterestDescription"": ""用户兴趣描述"",
  ""Tags"": [
    {{
      ""Name"": ""标签名称"",
      ""Category"": ""技术|行业|热点话题|其他"",
      ""Weight"": 0.0-1.0,
      ""Keywords"": ""相关关键词""
    }}
  ]
}}

注意：
- 标签名称要简洁明确
- 权重基于项目数据的重要性计算
- 避免主观臆测，严格基于项目数据
- 确保JSON格式正确
";

            return prompt;
        }

        /// <summary>
        /// 调用AI分析
        /// </summary>
        /// <param name="prompt">分析提示词</param>
        /// <returns>AI分析结果</returns>
        private async Task<string> CallAIAnalysisAsync(string prompt)
        {
            try
            {
                _logger.Info($"开始调用AI分析，提示词长度: {prompt.Length}");
                
                // 构建请求数据
                var requestData = new
                {
                    model = VectorServiceConfig.AI_MODEL_NAME,
                    messages = new[]
                    {
                        new
                        {
                            role = "user",
                            content = prompt
                        }
                    },
                    temperature = 0.7,
                    max_tokens = 2000,
                    timeout = VectorServiceConfig.AI_ANALYSIS_TIMEOUT
                };

                // 调用HttpLLMPost进行AI分析
                var response = HttpMethods.HttpLLMPost(requestData, null);
                
                if (!string.IsNullOrEmpty(response))
                {
                    _logger.Info($"AI分析完成，响应长度: {response.Length}");
                    return response;
                }
                else
                {
                    _logger.Error("AI分析返回空响应");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"AI分析调用失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 解析AI结果并创建标签
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="aiResult">AI分析结果</param>
        /// <param name="projectCount">项目数量</param>
        /// <returns>用户画像</returns>
        private async Task<UserProfile> ParseAIResultAndCreateTagsAsync(int userId, string aiResult, int projectCount)
        {
            try
            {
                _logger.Info($"开始解析AI分析结果，长度: {aiResult?.Length ?? 0}");
                
                // 解析AI返回的JSON结果
                var analysisResult = ParseAIJsonResult(aiResult);
                
                if (analysisResult?.Tags == null || !analysisResult.Tags.Any())
                {
                    _logger.Warn($"AI分析结果为空或格式错误: {aiResult}");
                    return null;
                }

                _logger.Info($"解析到 {analysisResult.Tags.Count} 个标签");

                // 获取或创建用户画像，直接传入AI分析结果
                var profile = await GetOrCreateUserProfileAsync(userId, analysisResult, projectCount);

                // 处理每个标签
                foreach (var tagInfo in analysisResult.Tags)
                {
                    try
                    {
                        // 创建或获取标签
                        var tag = await CreateOrGetTagAsync(tagInfo);
                        
                        // 创建用户标签关联
                        await CreateUserTagRelationAsync(userId, tag.Id, tagInfo.Weight);
                        
                        _logger.Info($"成功处理标签: {tagInfo.Name} (权重: {tagInfo.Weight})");
                    }
                    catch (Exception tagEx)
                    {
                        _logger.Error($"处理标签 {tagInfo.Name} 时发生错误: {tagEx.Message}");
                    }
                }

                _logger.Info($"用户 {userId} 画像解析完成，共处理 {analysisResult.Tags.Count} 个标签");
                return profile;
            }
            catch (Exception ex)
            {
                _logger.Error($"解析AI结果失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 解析AI返回的JSON结果
        /// </summary>
        /// <param name="aiResult">AI返回的JSON字符串</param>
        /// <returns>解析后的分析结果</returns>
        private AIAnalysisResult ParseAIJsonResult(string aiResult)
        {
            try
            {
                if (string.IsNullOrEmpty(aiResult))
                {
                    _logger.Warn("AI返回结果为空");
                    return null;
                }

                // 处理包含<think>标签的响应
                string jsonContent = WebHelper.ExtractJsonFromResponse(aiResult);
                
                if (string.IsNullOrEmpty(jsonContent))
                {
                    _logger.Error("无法从AI响应中提取有效的JSON内容");
                    return null;
                }

                _logger.Info($"提取的JSON内容长度: {jsonContent.Length}");
                _logger.Info($"JSON内容预览: {jsonContent.Substring(0, Math.Min(200, jsonContent.Length))}...");

                // 优先使用Banyan.Code.Json解析JSON
                var result = ParseJsonWithBanyanCode(jsonContent);
                if (result != null && result.Tags != null && result.Tags.Any())
                {
                    _logger.Info($"使用Banyan.Code.Json成功解析JSON，标签数量: {result.Tags.Count}");
                    return result;
                }

                // 如果Banyan.Code.Json解析失败，使用手动解析作为备选
                _logger.Warn("Banyan.Code.Json解析失败，尝试使用手动解析");
                result = ParseJsonManually(jsonContent);
                if (result != null && result.Tags != null && result.Tags.Any())
                {
                    _logger.Info($"使用手动解析成功解析JSON，标签数量: {result.Tags.Count}");
                    return result;
                }

                _logger.Error("所有JSON解析方法都失败了");
                return null;
            }
            catch (Exception ex)
            {
                _logger.Error($"解析AI JSON结果失败: {ex.Message}");
                return null;
            }
        }

       

        /// <summary>
        /// 使用Banyan.Code.Json解析JSON
        /// </summary>
        /// <param name="jsonString">JSON字符串</param>
        /// <returns>解析结果</returns>
        private AIAnalysisResult ParseJsonWithBanyanCode(string jsonString)
        {
            try
            {
                // 使用Banyan.Code.Json进行JSON解析
                var jsonObject = jsonString.ToObject<dynamic>();
                
                if (jsonObject == null)
                {
                    _logger.Warn("JSON反序列化失败");
                    return null;
                }

                var result = new AIAnalysisResult();
                var tags = new List<TagInfo>();

                // 解析InterestDescription
                if (jsonObject.InterestDescription != null)
                {
                    result.InterestDescription = jsonObject.InterestDescription.ToString();
                    _logger.Info($"解析到兴趣描述: {result.InterestDescription}");
                }

                // 解析Tags数组
                if (jsonObject.Tags != null)
                {
                    foreach (var tagObj in jsonObject.Tags)
                    {
                        var tag = new TagInfo
                        {
                            Name = tagObj.Name?.ToString() ?? "",
                            Category = tagObj.Category?.ToString() ?? "",
                            Weight = tagObj.Weight != null ? Convert.ToDouble(tagObj.Weight) : 0.0,
                            Keywords = tagObj.Keywords?.ToString() ?? ""
                        };

                        if (!string.IsNullOrEmpty(tag.Name))
                        {
                            tags.Add(tag);
                            _logger.Info($"解析标签: {tag.Name} ({tag.Category}) 权重: {tag.Weight}");
                        }
                    }
                }

                result.Tags = tags;
                _logger.Info($"总共解析到 {tags.Count} 个标签");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"使用Banyan.Code.Json解析JSON失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 手动解析JSON（避免依赖JsonConvert）
        /// </summary>
        /// <param name="jsonContent">JSON内容</param>
        /// <returns>解析结果</returns>
        private AIAnalysisResult ParseJsonManually(string jsonContent)
        {
            try
            {
                var result = new AIAnalysisResult();
                var tags = new List<TagInfo>();

                // 解析interestDescription
                var descMatch = System.Text.RegularExpressions.Regex.Match(jsonContent, @"""InterestDescription""\s*:\s*""([^""]+)""");
                if (descMatch.Success)
                {
                    result.InterestDescription = descMatch.Groups[1].Value;
                    _logger.Info($"解析到兴趣描述: {result.InterestDescription}");
                }

                // 解析tags数组 - 使用更精确的正则表达式
                var tagsMatch = System.Text.RegularExpressions.Regex.Match(jsonContent, @"""Tags""\s*:\s*\[(.*?)\]", System.Text.RegularExpressions.RegexOptions.Singleline);
                if (tagsMatch.Success)
                {
                    var tagsContent = tagsMatch.Groups[1].Value;
                    _logger.Info($"找到Tags数组，内容长度: {tagsContent.Length}");
                    
                    // 使用更精确的方法分割标签对象
                    var tagObjects = SplitTagObjects(tagsContent);
                    
                    foreach (var tagJson in tagObjects)
                    {
                        var tag = ParseTagObject(tagJson);
                        if (tag != null)
                        {
                            tags.Add(tag);
                            _logger.Info($"解析标签: {tag.Name} ({tag.Category}) 权重: {tag.Weight}");
                        }
                    }
                }
                else
                {
                    _logger.Warn("未找到Tags数组");
                }

                result.Tags = tags;
                _logger.Info($"总共解析到 {tags.Count} 个标签");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"手动解析JSON失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 分割标签对象
        /// </summary>
        /// <param name="tagsContent">标签数组内容</param>
        /// <returns>标签对象列表</returns>
        private List<string> SplitTagObjects(string tagsContent)
        {
            var tagObjects = new List<string>();
            var braceCount = 0;
            var startIndex = -1;
            var inString = false;
            var escapeNext = false;
            
            for (int i = 0; i < tagsContent.Length; i++)
            {
                var ch = tagsContent[i];
                
                // 处理转义字符
                if (escapeNext)
                {
                    escapeNext = false;
                    continue;
                }
                
                if (ch == '\\')
                {
                    escapeNext = true;
                    continue;
                }
                
                // 处理字符串
                if (ch == '"' && !escapeNext)
                {
                    inString = !inString;
                    continue;
                }
                
                // 只有在不在字符串内时才处理括号
                if (!inString)
                {
                    if (ch == '{')
                    {
                        if (braceCount == 0)
                        {
                            startIndex = i;
                        }
                        braceCount++;
                    }
                    else if (ch == '}')
                    {
                        braceCount--;
                        if (braceCount == 0 && startIndex != -1)
                        {
                            var tagObject = tagsContent.Substring(startIndex, i - startIndex + 1);
                            tagObjects.Add(tagObject);
                            _logger.Info($"分割到标签对象: {tagObject.Substring(0, Math.Min(50, tagObject.Length))}...");
                            startIndex = -1;
                        }
                    }
                }
            }
            
            _logger.Info($"总共分割出 {tagObjects.Count} 个标签对象");
            return tagObjects;
        }

        /// <summary>
        /// 解析单个标签对象
        /// </summary>
        /// <param name="tagJson">标签JSON字符串</param>
        /// <returns>标签信息</returns>
        private TagInfo ParseTagObject(string tagJson)
        {
            try
            {
                _logger.Info($"开始解析标签对象: {tagJson.Substring(0, Math.Min(100, tagJson.Length))}...");
                
                // 解析标签属性 - 使用更宽松的正则表达式
                var nameMatch = System.Text.RegularExpressions.Regex.Match(tagJson, @"""name""\s*:\s*""([^""\\]*(?:\\.[^""\\]*)*)""");
                var categoryMatch = System.Text.RegularExpressions.Regex.Match(tagJson, @"""category""\s*:\s*""([^""\\]*(?:\\.[^""\\]*)*)""");
                var weightMatch = System.Text.RegularExpressions.Regex.Match(tagJson, @"""weight""\s*:\s*([0-9.]+)");
                var keywordsMatch = System.Text.RegularExpressions.Regex.Match(tagJson, @"""keywords""\s*:\s*\[(.*?)\]", System.Text.RegularExpressions.RegexOptions.Singleline);

                if (nameMatch.Success && categoryMatch.Success && weightMatch.Success)
                {
                    var tag = new TagInfo
                    {
                        Name = UnescapeJsonString(nameMatch.Groups[1].Value),
                        Category = UnescapeJsonString(categoryMatch.Groups[1].Value),
                        Weight = double.Parse(weightMatch.Groups[1].Value),
                        Keywords = keywordsMatch.Success ? UnescapeJsonString(keywordsMatch.Groups[1].Value) : ""
                    };
                    
                    _logger.Info($"成功解析标签: {tag.Name} ({tag.Category}) 权重: {tag.Weight}");
                    return tag;
                }
                else
                {
                    _logger.Warn($"解析标签对象失败，缺少必要字段。name: {nameMatch.Success}, category: {categoryMatch.Success}, weight: {weightMatch.Success}");
                    _logger.Warn($"标签JSON: {tagJson}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"解析标签对象时发生错误: {ex.Message}, 内容: {tagJson}");
                return null;
            }
        }

        /// <summary>
        /// 反转义JSON字符串
        /// </summary>
        /// <param name="jsonString">JSON字符串</param>
        /// <returns>反转义后的字符串</returns>
        private string UnescapeJsonString(string jsonString)
        {
            if (string.IsNullOrEmpty(jsonString))
                return jsonString;
                
            return jsonString
                .Replace("\\\"", "\"")
                .Replace("\\\\", "\\")
                .Replace("\\n", "\n")
                .Replace("\\r", "\r")
                .Replace("\\t", "\t");
        }

        #endregion

        #region 标签管理

        /// <summary>
        /// 创建或获取标签
        /// </summary>
        /// <param name="tagInfo">标签信息</param>
        /// <returns>标签对象</returns>
        private async Task<UserInterestTag> CreateOrGetTagAsync(TagInfo tagInfo)
        {
            // 检查标签是否已存在
            var existingTag = await GetTagByNameAsync(tagInfo.Name);
            if (existingTag != null)
            {
                return existingTag;
            }

            // 创建新标签
            var newTag = new UserInterestTag
            {
                Name = tagInfo.Name,
                Category = tagInfo.Category,
                Keywords = tagInfo.Keywords,
                UserCount = 0,
                ClickCount = 0,
                ClickRate = 0,
                LastUsedTime = DateTime.Now,
                IsActive = 1,
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now
            };

            await SaveTagAsync(newTag);
            _logger.Info($"创建新标签: {tagInfo.Name} ({tagInfo.Category})");
            
            return newTag;
        }

        /// <summary>
        /// 创建用户标签关联
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="tagId">标签ID</param>
        /// <param name="weight">权重</param>
        private async Task CreateUserTagRelationAsync(int userId, int tagId, double weight)
        {
            var relation = new UserTagRelation
            {
                UserId = userId,
                TagId = tagId,
                Weight = Math.Min(weight, 1.0), // 确保权重不超过1
                ClickCount = 0,
                LastClickTime = DateTime.Now, // 设置初始点击时间，避免SqlDateTime溢出
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now
            };

            await SaveUserTagRelationAsync(relation);
        }

        #endregion

        #region 数据访问

        /// <summary>
        /// 获取用户最近的项目数据
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>项目列表</returns>
        private async Task<List<Project>> GetUserRecentProjectsAsync(int userId)
        {
            try
            {
                _logger.Info($"开始获取用户 {userId} 最近的项目数据");

                // 获取用户信息
                var memberBLL = new MemberBLL();
                var user = memberBLL.GetModelByCache(userId);
                if (user == null)
                {
                    _logger.Warn($"用户 {userId} 不存在");
                    return new List<Project>();
                }

                // 构建查询条件 - 直接使用字符串拼接，因为ProjectBLL可能不支持参数化查询
                var userName = user.RealName?.Replace("'", "''") ?? ""; // 转义单引号避免SQL注入
                string strWhere = $"Status<>{(int)ProjectStatus.delete} AND (EditorName='{userName}' OR ProjectManager LIKE '%{userName}%' OR groupMember LIKE '%{userName}%' OR Introducer LIKE '%{userName}%' OR finder LIKE '%{userName}%' OR DDManager LIKE '%{userName}%' OR InteralPTCP LIKE '%{userName}%')";

                // 使用ProjectBLL获取项目列表，限制50个，按发布时间倒序
                var projectBLL = new ProjectBLL();
                var projectList = projectBLL.GetList(strWhere, 50, 1, "*", "PubTime DESC");

                if (projectList != null && projectList.Count > 0)
                {
                    _logger.Info($"用户 {userId} ({user.RealName}) 获取到 {projectList.Count} 个项目");
                    
                    // 为每个项目添加角色名称
                    var roleList = new RoleBLL().GetList(false);
                    foreach (var project in projectList)
                    {
                        project.RoleName = roleList.Where(x => x.Id == project.ToRoleId).Select(x => x.RoleName).FirstOrDefault()?.ToString();
                    }
                }
                else
                {
                    _logger.Info($"用户 {userId} ({user.RealName}) 没有找到相关项目");
                }

                return projectList ?? new List<Project>();
            }
            catch (Exception ex)
            {
                _logger.Error($"获取用户 {userId} 最近项目时发生错误: {ex.Message}");
                return new List<Project>();
            }
        }

        /// <summary>
        /// 获取所有活跃用户
        /// </summary>
        /// <returns>用户ID列表</returns>
        private async Task<List<int>> GetAllActiveUsersAsync()
        {
            try
            {
                _logger.Info("开始获取所有活跃用户ID");
                
                // 使用带缓存的后台任务专用方法
                var activeUsers = memberBLL.GetAllListForBackgroundTask(false); // 不包括审核中的用户，带缓存
                var userIds = activeUsers.Select(u => u.Id).ToList();
                
                _logger.Info($"获取到 {userIds.Count} 个活跃用户");
                return userIds;
            }
            catch (Exception ex)
            {
                _logger.Error($"获取活跃用户列表时发生错误: {ex.Message}");
                return new List<int>();
            }
        }

        /// <summary>
        /// 根据ID获取标签
        /// </summary>
        /// <param name="tagId">标签ID</param>
        /// <returns>标签对象</returns>
        public async Task<UserInterestTag> GetTagByIdAsync(int tagId)
        {
            try
            {
                _logger.Info($"根据ID获取标签: {tagId}");
                
                        // 使用UserInterestTagBLL的GetTagById方法
        var interestTagBLL = new UserInterestTagBLL();
        var tag = interestTagBLL.GetTagById(tagId);
                
                if (tag != null)
                {
                    _logger.Info($"成功获取标签: {tag.Name}");
                }
                else
                {
                    _logger.Info($"标签不存在: {tagId}");
                }
                
                return tag;
            }
            catch (Exception ex)
            {
                _logger.Error($"根据ID获取标签时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取项目详情
        /// </summary>
        /// <param name="projectId">项目ID</param>
        /// <returns>项目对象</returns>
        private async Task<Project> GetProjectByIdAsync(int projectId)
        {
            try
            {
                if (projectId <= 0)
                {
                    _logger.Warn($"无效的项目ID: {projectId}");
                    return null;
                }

                _logger.Info($"获取项目详情，项目ID: {projectId}");
                
                // 使用ProjectBLL获取项目详情
                var projectBLL = new ProjectBLL();
                var project = projectBLL.GetModel($"Id={projectId}");
                
                if (project != null)
                {
                    _logger.Info($"成功获取项目详情，项目ID: {projectId}，名称: {project.Name}");
                }
                else
                {
                    _logger.Warn($"项目不存在，项目ID: {projectId}");
                }
                
                return project;
            }
            catch (Exception ex)
            {
                _logger.Error($"获取项目详情时发生错误: {ex.Message}，项目ID: {projectId}");
                return null;
            }
        }

        /// <summary>
        /// 获取用户画像
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户画像</returns>
        private async Task<UserProfile> GetUserProfileAsync(int userId)
        {
            // 先从缓存获取
            var cacheKey = $"user_profile:{userId}";
            var cached = _cache.GetCache<UserProfile>(cacheKey);
            if (cached != null)
                return cached;

            // 从数据库获取
            var profile = await GetUserProfileFromDbAsync(userId);
            if (profile != null)
            {
                await CacheUserProfileAsync(userId, profile);
            }

            return profile;
        }

        /// <summary>
        /// 获取或创建用户画像
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="aiAnalysisResult">AI分析结果（可选）</param>
        /// <param name="projectCount">项目数量</param>
        /// <returns>用户画像</returns>
        private async Task<UserProfile> GetOrCreateUserProfileAsync(int userId, AIAnalysisResult aiAnalysisResult = null, int projectCount = 0)
        {
            try
            {
                _logger.Info($"获取或创建用户画像，用户ID: {userId}");
                
                // 先从数据库获取现有画像
                var profile = await GetUserProfileAsync(userId);
                
                if (profile == null)
                {
                    _logger.Info($"用户画像不存在，创建新的用户画像，用户ID: {userId}");
                    
                    // 获取用户名
                    var userName = await GetUserNameAsync(userId);
                    
                    // 创建新的用户画像，使用AI分析结果或默认值
                    profile = new UserProfile
                    {
                        UserId = userId,
                        UserName = userName,
                        InterestDescription = aiAnalysisResult?.InterestDescription ?? "",
                        ProjectCount = projectCount,
                        TagWeights = "{}",
                        ClickStats = "{}",
                        Status = 1
                    };
                    
                    // 保存到数据库
                    await SaveUserProfileAsync(profile);
                    
                    _logger.Info($"新用户画像创建并保存成功，用户ID: {userId}，用户名: {userName}");
                }
                else
                {
                    _logger.Info($"找到现有用户画像，用户ID: {userId}，用户名: {profile.UserName}");
                    
                    // 如果有AI分析结果，更新现有画像
                    if (aiAnalysisResult != null)
                    {
                        profile.InterestDescription = aiAnalysisResult.InterestDescription;
                        profile.ProjectCount = projectCount;
                        profile.UpdateTime = DateTime.Now;
                        
                        // 保存更新后的画像
                        await SaveUserProfileAsync(profile);
                        
                        _logger.Info($"现有用户画像已更新，用户ID: {userId}");
                    }
                }

                return profile;
            }
            catch (Exception ex)
            {
                _logger.Error($"获取或创建用户画像时发生错误: {ex.Message}，用户ID: {userId}");
                return null;
            }
        }

       
        #endregion

        #region 缓存管理

        /// <summary>
        /// 缓存用户画像
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="profile">用户画像</param>
        private async Task CacheUserProfileAsync(int userId, UserProfile profile)
        {
            var cacheKey = $"user_profile:{userId}";
            _cache.WriteCache(profile, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.USER_PROFILE_CACHE_DAYS));
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取用户名
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户名</returns>
        private async Task<string> GetUserNameAsync(int userId)
        {
            try
            {
                _logger.Info($"从数据库获取用户名，用户ID: {userId}");
                
                // 使用MemberBLL从数据库获取用户信息
                var user = memberBLL.GetModelByCache(userId);
                
                if (user != null)
                {
                    var userName = user.RealName ?? $"User_{userId}";
                    _logger.Info($"成功获取用户名: {userName}，用户ID: {userId}");
                    return userName;
                }
                else
                {
                    _logger.Warn($"用户不存在，用户ID: {userId}");
                    return $"User_{userId}";
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"获取用户名时发生错误: {ex.Message}，用户ID: {userId}");
                return $"User_{userId}";
            }
        }

        /// <summary>
        /// 处理用户批次
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <returns>批次处理结果</returns>
        private async Task<UserProfileBatchProcessResult> ProcessUserBatchAsync(IEnumerable<int> userIds)
        {
            var result = new UserProfileBatchProcessResult();
            
            foreach (var userId in userIds)
            {
                try
                {
                    var profile = await AnalyzeUserProfileAsync(userId);
                    if (profile != null)
                        result.SuccessCount++;
                    else
                        result.FailedCount++;
                }
                catch (Exception ex)
                {
                    _logger.Error($"处理用户 {userId} 时发生错误: {ex.Message}");
                    result.FailedCount++;
                }
                
                result.TotalProcessed++;
            }

            return result;
        }

        /// <summary>
        /// 分析新项目对画像的影响
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="newProject">新项目</param>
        /// <param name="existingProfile">现有画像</param>
        /// <returns>更新后的画像</returns>
        private async Task<UserProfile> AnalyzeNewProjectImpactAsync(int userId, Project newProject, UserProfile existingProfile)
        {
            // 获取用户所有项目（包括新项目）
            var allProjects = await GetUserRecentProjectsAsync(userId);
            allProjects.Add(newProject);

            // 重新分析用户画像
            var prompt = BuildAnalysisPrompt(allProjects);
            var aiResult = await CallAIAnalysisAsync(prompt);
            
            if (!string.IsNullOrEmpty(aiResult))
            {
                return await ParseAIResultAndCreateTagsAsync(userId, aiResult, allProjects.Count);
            }

            return existingProfile;
        }

        /// <summary>
        /// 基于新闻向量更新用户兴趣向量
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="newsVector">新闻向量</param>
        private async Task UpdateUserVectorWithNewsAsync(int userId, double[] newsVector)
        {
            try
            {
                // 获取用户当前向量
                var cacheKey = $"user_vector:{userId}";
                var currentVector = await Task.Run(() => _cache.GetCache<double[]>(cacheKey));

                if (currentVector != null && currentVector.Length == VectorServiceConfig.VECTOR_DIMENSION)
                {
                    // 使用加权平均更新向量（新闻权重较小，避免过度影响）
                    const double newsWeight = 0.1;
                    const double currentWeight = 0.9;

                    for (int i = 0; i < currentVector.Length; i++)
                    {
                        currentVector[i] = currentWeight * currentVector[i] + newsWeight * newsVector[i];
                    }

                    // 更新缓存
                    await Task.Run(() => _cache.WriteCache(currentVector, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.VECTOR_CACHE_DAYS)));

                    // 同步向量到UserProfile数据库
                    var profile = await GetUserProfileAsync(userId);
                    if (profile != null)
                    {
                        await SyncVectorToUserProfileAsync(userId, currentVector, profile);
                    }

                    _logger.Info($"用户 {userId} 兴趣向量已基于新闻更新");
                }
                else
                {
                    _logger.Warn($"用户 {userId} 当前向量不存在或维度不匹配，无法更新");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"更新用户 {userId} 兴趣向量时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 基于新闻标签更新用户标签权重
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="tags">新闻标签</param>
        private async Task UpdateUserTagWeightsAsync(int userId, List<string> tags)
        {
            try
            {
                var userTagRelationBLL = new UserTagRelationBLL();

                foreach (var tagName in tags)
                {
                    // 查找或创建标签
                    var tag = await CreateOrGetTagAsync(new TagInfo { Name = tagName.Trim(), Weight = 0.1f });

                    // 获取用户与标签的关系
                    var relation = userTagRelationBLL.GetModel($"UserId = {userId} AND TagId = {tag.Id}");

                    if (relation != null)
                    {
                        // 增加权重（但不超过1.0）
                        relation.Weight = Math.Min(1.0f, relation.Weight + 0.05f);
                        relation.UpdateTime = DateTime.Now;
                        userTagRelationBLL.Update(relation);
                    }
                    else
                    {
                        // 创建新的关系
                        await CreateUserTagRelationAsync(userId, tag.Id, 0.1f);
                    }
                }

                _logger.Info($"用户 {userId} 标签权重已基于新闻标签更新");
            }
            catch (Exception ex)
            {
                _logger.Error($"更新用户 {userId} 标签权重时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取用户向量更新统计信息
        /// </summary>
        /// <returns>向量更新统计</returns>
        public async Task<object> GetVectorUpdateStatsAsync()
        {
            try
            {
                _logger.Info("开始获取用户向量更新统计信息");

                // 获取所有用户画像
                var allProfiles = await Task.Run(() => GetList());

                var stats = new
                {
                    TotalUsers = allProfiles.Count,
                    UsersWithVector = allProfiles.Count(p => !string.IsNullOrEmpty(p.InterestVector)),
                    UsersWithUpdateTime = allProfiles.Count(p => p.VectorUpdateTime.HasValue),
                    RecentUpdates = allProfiles
                        .Where(p => p.VectorUpdateTime.HasValue && p.VectorUpdateTime.Value > DateTime.Now.AddDays(-7))
                        .Count(),
                    OldVectors = allProfiles
                        .Where(p => p.VectorUpdateTime.HasValue && p.VectorUpdateTime.Value < DateTime.Now.AddDays(-30))
                        .Count(),
                    LastUpdateTime = allProfiles
                        .Where(p => p.VectorUpdateTime.HasValue)
                        .Max(p => p.VectorUpdateTime),
                    UpdateTimeDistribution = allProfiles
                        .Where(p => p.VectorUpdateTime.HasValue)
                        .GroupBy(p => p.VectorUpdateTime.Value.Date)
                        .OrderByDescending(g => g.Key)
                        .Take(7)
                        .Select(g => new { Date = g.Key.ToString("yyyy-MM-dd"), Count = g.Count() })
                        .ToList()
                };

                _logger.Info($"向量更新统计: 总用户{stats.TotalUsers}，有向量{stats.UsersWithVector}，最近更新{stats.RecentUpdates}");
                return stats;
            }
            catch (Exception ex)
            {
                _logger.Error($"获取向量更新统计信息失败: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region 数据保存方法（需要根据实际数据访问层实现）

        /// <summary>
        /// 保存用户画像到数据库
        /// </summary>
        /// <param name="profile">用户画像对象</param>
        private async Task SaveUserProfileAsync(UserProfile profile)
        {
            try
            {
                _logger.Info($"开始保存用户画像，用户ID: {profile.UserId}");
                
                // 检查是否已存在
                var existingProfile = await GetUserProfileFromDbAsync(profile.UserId);
                
                if (existingProfile != null)
                {
                    // 更新现有记录
                    profile.Id = existingProfile.Id;
                    profile.UpdateTime = DateTime.Now;
                    
                    // 使用BaseDAL的Update方法
                    bool success = this.Update(profile);
                    
                    if (success)
                    {
                        _logger.Info($"用户画像更新成功，用户ID: {profile.UserId}");
                    }
                    else
                    {
                        _logger.Error($"用户画像更新失败，用户ID: {profile.UserId}");
                    }
                }
                else
                {
                    // 创建新记录
                    profile.CreateTime = DateTime.Now;
                    profile.UpdateTime = DateTime.Now;
                    
                    // 使用BaseDAL的Add方法
                    object result = this.Add(profile);
                    
                    if (result != null)
                    {
                        profile.Id = Convert.ToInt32(result);
                        _logger.Info($"用户画像创建成功，用户ID: {profile.UserId}，ID: {profile.Id}");
                    }
                    else
                    {
                        _logger.Error($"用户画像创建失败，用户ID: {profile.UserId}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"保存用户画像时发生错误: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 保存标签到数据库
        /// </summary>
        /// <param name="tag">标签对象</param>
        private async Task SaveTagAsync(UserInterestTag tag)
        {
            try
            {
                _logger.Info($"开始保存标签: {tag.Name}");
                
                // 检查标签是否已存在
                var existingTag = await GetTagByNameAsync(tag.Name);
                
                if (existingTag != null)
                {
                    // 更新现有标签
                    tag.Id = existingTag.Id;
                    tag.UpdateTime = DateTime.Now;
                    tag.LastUsedTime = DateTime.Now;
                    
                    // 使用BaseDAL的Update方法
                            var interestTagBLL = new UserInterestTagBLL();
        bool success = interestTagBLL.Update(tag);
                    
                    if (success)
                    {
                        _logger.Info($"标签更新成功: {tag.Name}");
                    }
                    else
                    {
                        _logger.Error($"标签更新失败: {tag.Name}");
                    }
                }
                else
                {
                    // 创建新标签
                    tag.CreateTime = DateTime.Now;
                    tag.UpdateTime = DateTime.Now;
                    tag.LastUsedTime = DateTime.Now;
                    
                    // 使用BaseDAL的Add方法
                            var interestTagBLL = new UserInterestTagBLL();
        object result = interestTagBLL.Add(tag);
                    
                    if (result != null)
                    {
                        tag.Id = Convert.ToInt32(result);
                        _logger.Info($"标签创建成功: {tag.Name}，ID: {tag.Id}");
                    }
                    else
                    {
                        _logger.Error($"标签创建失败: {tag.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"保存标签时发生错误: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 保存用户标签关联到数据库
        /// </summary>
        /// <param name="relation">用户标签关联对象</param>
        private async Task SaveUserTagRelationAsync(UserTagRelation relation)
        {
            try
            {
                _logger.Info($"开始保存用户标签关联，用户ID: {relation.UserId}，标签ID: {relation.TagId}");
                
                // 检查关联是否已存在
                var existingRelation = await GetUserTagRelationAsync(relation.UserId, relation.TagId);
                
                if (existingRelation != null)
                {
                    // 更新现有关联
                    relation.Id = existingRelation.Id;
                    relation.UpdateTime = DateTime.Now;
                    
                    // 使用BaseDAL的Update方法
                    var userTagRelationBLL = new UserTagRelationBLL();
                    bool success = userTagRelationBLL.Update(relation);
                    
                    if (success)
                    {
                        _logger.Info($"用户标签关联更新成功，用户ID: {relation.UserId}，标签ID: {relation.TagId}");
                    }
                    else
                    {
                        _logger.Error($"用户标签关联更新失败，用户ID: {relation.UserId}，标签ID: {relation.TagId}");
                    }
                }
                else
                {
                    // 创建新关联
                    relation.CreateTime = DateTime.Now;
                    relation.UpdateTime = DateTime.Now;
                    // 确保LastClickTime不为默认值，避免SqlDateTime溢出
                    if (relation.LastClickTime == DateTime.MinValue)
                    {
                        relation.LastClickTime = DateTime.Now;
                    }
                    
                    // 使用BaseDAL的Add方法
                    var userTagRelationBLL = new UserTagRelationBLL();
                    object result = userTagRelationBLL.Add(relation);
                    
                    if (result != null)
                    {
                        relation.Id = Convert.ToInt32(result);
                        _logger.Info($"用户标签关联创建成功，用户ID: {relation.UserId}，标签ID: {relation.TagId}，ID: {relation.Id}");
                    }
                    else
                    {
                        _logger.Error($"用户标签关联创建失败，用户ID: {relation.UserId}，标签ID: {relation.TagId}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"保存用户标签关联时发生错误: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 从数据库获取用户画像
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户画像对象</returns>
        private async Task<UserProfile> GetUserProfileFromDbAsync(int userId)
        {
            try
            {
                _logger.Info($"从数据库获取用户画像，用户ID: {userId}");
                
                // 使用BaseDAL的GetModel方法
                var profile = this.GetModel($"UserId={userId}");
                
                if (profile != null)
                {
                    _logger.Info($"成功获取用户画像，用户ID: {userId}");
                }
                else
                {
                    _logger.Info($"用户画像不存在，用户ID: {userId}");
                }
                
                return profile;
            }
            catch (Exception ex)
            {
                _logger.Error($"从数据库获取用户画像时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 根据名称获取标签
        /// </summary>
        /// <param name="tagName">标签名称</param>
        /// <returns>标签对象</returns>
        private async Task<UserInterestTag> GetTagByNameAsync(string tagName)
        {
            try
            {
                _logger.Info($"根据名称获取标签: {tagName}");
                
                // 使用BaseDAL的GetModel方法
                        var interestTagBLL = new UserInterestTagBLL();
        var tag = interestTagBLL.GetModel($"Name='{tagName}' AND IsActive=1");
                
                if (tag != null)
                {
                    _logger.Info($"成功获取标签: {tagName}");
                }
                else
                {
                    _logger.Info($"标签不存在: {tagName}");
                }
                
                return tag;
            }
            catch (Exception ex)
            {
                _logger.Error($"根据名称获取标签时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取用户标签关联
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="tagId">标签ID</param>
        /// <returns>用户标签关联对象</returns>
        private async Task<UserTagRelation> GetUserTagRelationAsync(int userId, int tagId)
        {
            try
            {
                _logger.Info($"获取用户标签关联，用户ID: {userId}，标签ID: {tagId}");
                
                // 使用BaseDAL的GetModel方法
                var userTagRelationBLL = new UserTagRelationBLL();
                var relation = userTagRelationBLL.GetModel($"UserId={userId} AND TagId={tagId}");
                
                if (relation != null)
                {
                    _logger.Info($"成功获取用户标签关联，用户ID: {userId}，标签ID: {tagId}");
                }
                else
                {
                    _logger.Info($"用户标签关联不存在，用户ID: {userId}，标签ID: {tagId}");
                }
                
                return relation;
            }
            catch (Exception ex)
            {
                _logger.Error($"获取用户标签关联时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取用户所有标签关联
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户标签关联列表</returns>
        public async Task<List<UserTagRelation>> GetUserTagRelationsAsync(int userId)
        {
            try
            {
                _logger.Info($"获取用户所有标签关联，用户ID: {userId}");
                
                // 使用BaseDAL的GetList方法
                var userTagRelationBLL = new UserTagRelationBLL();
                var relations = userTagRelationBLL.GetList($"UserId={userId}", int.MaxValue, 1, "*", "Weight DESC");
                
                if (relations != null && relations.Count > 0)
                {
                    _logger.Info($"成功获取用户标签关联，用户ID: {userId}，数量: {relations.Count}");
                }
                else
                {
                    _logger.Info($"用户没有标签关联，用户ID: {userId}");
                }
                
                return relations ?? new List<UserTagRelation>();
            }
            catch (Exception ex)
            {
                _logger.Error($"获取用户标签关联时发生错误: {ex.Message}");
                return new List<UserTagRelation>();
            }
        }

        #endregion

        /// <summary>
        /// 测试Embedding服务
        /// </summary>
        /// <param name="testText">测试文本</param>
        /// <returns>测试结果</returns>
        public async Task<AjaxResult> TestEmbeddingServiceAsync(string testText = "人工智能技术")
        {
            try
            {
                _logger.Info($"开始测试Embedding服务，测试文本: {testText}");
                
                        // 使用UserInterestTagBLL中的Embedding服务
        var interestTagBLL = new UserInterestTagBLL();
                
                // 创建一个临时标签来测试Embedding服务
                var tempTag = new UserInterestTag
                {
                    Name = testText,
                    Keywords = testText,
                    Category = "测试"
                };
                
                // 直接调用UserInterestTagBLL的Embedding服务
                var vector = await interestTagBLL.GenerateTagVectorAsync(0); // 这里需要先创建标签
                
                if (vector)
                {
                    _logger.Info($"Embedding服务测试成功");
                    
                    return new AjaxResult
                    {
                        code = (int)ResultCode.success,
                        msg = $"Embedding服务测试成功",
                        data = new
                        {
                            TestText = testText,
                            Status = "Success"
                        }
                    };
                }
                else
                {
                    _logger.Error("Embedding服务测试失败");
                    
                    return new AjaxResult
                    {
                        code = (int)ResultCode.exception,
                        msg = "Embedding服务测试失败"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Embedding服务测试异常: {ex.Message}");
                
                return new AjaxResult
                {
                    code = (int)ResultCode.exception,
                    msg = $"Embedding服务测试异常: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 从数据库获取用户向量（优先从UserProfile.InterestVector字段获取，作为缓存的后备方案）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户向量</returns>
        public async Task<double[]> GetUserVectorFromDatabaseAsync(int userId)
        {
            try
            {
                _logger.Info($"从数据库获取用户向量，用户ID: {userId}");
                
                // 1. 获取用户画像
                var profile = await GetUserProfileAsync(userId);
                if (profile == null)
                {
                    _logger.Warn($"用户画像不存在，用户ID: {userId}");
                    return null;
                }

                // 2. 尝试从UserProfile的InterestVector字段获取向量
                if (!string.IsNullOrEmpty(profile.InterestVector))
                {
                    try
                    {
                        var vector = Newtonsoft.Json.JsonConvert.DeserializeObject<double[]>(profile.InterestVector);
                        if (vector != null && vector.Length == VectorServiceConfig.VECTOR_DIMENSION)
                        {
                            var updateTimeStr = profile.VectorUpdateTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "未知";
                            _logger.Info($"从UserProfile数据库获取用户向量成功，用户ID: {userId}，维度: {vector.Length}，更新时间: {updateTimeStr}");

                            // 同时更新缓存
                            var cacheKey = $"user_vector:{userId}";
                            _cache.WriteCache(vector, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.VECTOR_CACHE_DAYS));

                            return vector;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warn($"解析用户 {userId} 的InterestVector字段失败: {ex.Message}");
                    }
                }

                // 3. 如果UserProfile中没有向量，则重新生成
                _logger.Info($"UserProfile中没有向量数据，重新生成用户 {userId} 的向量");
                var success = await GenerateUserInterestVectorAsync(userId, profile);
                if (success)
                {
                    // 4. 从缓存获取生成的向量
                    var cacheKey = $"user_vector:{userId}";
                    var vector = _cache.GetCache<double[]>(cacheKey);
                    if (vector != null)
                    {
                        _logger.Info($"成功重新生成用户向量，用户ID: {userId}，维度: {vector.Length}");
                        return vector;
                    }
                }
                
                _logger.Error($"重新生成用户向量失败，用户ID: {userId}");
                return null;
            }
            catch (Exception ex)
            {
                _logger.Error($"从数据库获取用户向量失败: {ex.Message}，用户ID: {userId}");
                return null;
            }
        }

        /// <summary>
        /// 获取用户向量（优先缓存，后备数据库）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户向量</returns>
        public async Task<double[]> GetUserVectorAsync(int userId)
        {
            try
            {
                _logger.Info($"获取用户向量，用户ID: {userId}");
                
                // 1. 优先从缓存获取
                var cacheKey = $"user_vector:{userId}";
                var cachedVector = _cache.GetCache<double[]>(cacheKey);
                if (cachedVector != null)
                {
                    _logger.Info($"✓ 从缓存获取用户向量成功，用户ID: {userId}，维度: {cachedVector.Length}");
                    return cachedVector;
                }
                
                // 2. 缓存未命中，从数据库获取
                _logger.Info($"🔄 缓存未命中，从数据库获取用户向量，用户ID: {userId}");
                var dbVector = await GetUserVectorFromDatabaseAsync(userId);
                
                if (dbVector != null)
                {
                    _logger.Info($"✓ 从数据库获取用户向量成功，用户ID: {userId}，维度: {dbVector.Length}");
                    return dbVector;
                }
                
                // 3. 数据库也没有，需要重新生成
                _logger.Info($"🔄 数据库也没有向量，需要重新生成，用户ID: {userId}");
                var profile = await GetUserProfileAsync(userId);
                if (profile != null)
                {
                    var success = await GenerateUserInterestVectorAsync(userId, profile);
                    if (success)
                    {
                        // 重新获取生成的向量
                        return await GetUserVectorFromDatabaseAsync(userId);
                    }
                }
                
                _logger.Warn($"无法获取用户向量，用户ID: {userId}");
                return null;
            }
            catch (Exception ex)
            {
                _logger.Error($"获取用户向量失败: {ex.Message}，用户ID: {userId}");
                return null;
            }
        }

        /// <summary>
        /// 批量获取用户向量（用于推荐系统）
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <returns>用户向量字典</returns>
        public async Task<Dictionary<int, double[]>> GetUserVectorsBatchAsync(List<int> userIds)
        {
            var result = new Dictionary<int, double[]>();
            
            try
            {
                _logger.Info($"批量获取用户向量，用户数量: {userIds.Count}");
                
                // 分批处理，避免内存压力
                const int batchSize = 50;
                for (int i = 0; i < userIds.Count; i += batchSize)
                {
                    var batch = userIds.Skip(i).Take(batchSize);
                    
                    foreach (var userId in batch)
                    {
                        try
                        {
                            var vector = await GetUserVectorAsync(userId);
                            if (vector != null)
                            {
                                result[userId] = vector;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.Error($"获取用户 {userId} 向量失败: {ex.Message}");
                        }
                    }
                    
                    // 批次间延迟，避免过载
                    if (i + batchSize < userIds.Count)
                    {
                        await Task.Delay(100);
                    }
                }
                
                _logger.Info($"批量获取用户向量完成，成功获取: {result.Count}/{userIds.Count}");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"批量获取用户向量失败: {ex.Message}");
                return result;
            }
        }
    }

    #region 辅助类

    /// <summary>
    /// AI分析结果
    /// </summary>
    public class AIAnalysisResult
    {
        public string InterestDescription { get; set; }
        public List<TagInfo> Tags { get; set; }
    }

    /// <summary>
    /// 标签信息
    /// </summary>
    public class TagInfo
    {
        public string Name { get; set; }
        public string Category { get; set; }
        public double Weight { get; set; }
        public string Keywords { get; set; }
    }

    /// <summary>
    /// 批次处理结果
    /// </summary>
    public class UserProfileBatchProcessResult
    {
        public int TotalProcessed { get; set; }
        public int SuccessCount { get; set; }
        public int FailedCount { get; set; }
    }

    #endregion
} 