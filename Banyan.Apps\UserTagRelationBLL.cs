using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Banyan.Apps
{
    /// <summary>
    /// User tag relation business logic class
    /// </summary>
    public class UserTagRelationBLL : BaseDAL<UserTagRelation>
    {
        private readonly Log _logger;

        public UserTagRelationBLL()
        {
            _logger = LogFactory.GetLogger("UserTagRelationBLL");
        }

        public override object Add(UserTagRelation model)
        {
            try
            {
                _logger.Info($"Adding user tag relation, User ID: {model.UserId}, Tag ID: {model.TagId}");
                return base.Add(model);
            }
            catch (Exception ex)
            {
                _logger.Error($"Failed to add user tag relation: {ex.Message}");
                throw;
            }
        }

        public override bool Update(UserTagRelation model)
        {
            try
            {
                _logger.Info($"Updating user tag relation, User ID: {model.UserId}, Tag ID: {model.TagId}");
                return base.Update(model);
            }
            catch (Exception ex)
            {
                _logger.Error($"Failed to update user tag relation: {ex.Message}");
                return false;
            }
        }

        public override bool Update(UserTagRelation model, string fldList)
        {
            try
            {
                _logger.Info($"Updating user tag relation fields, User ID: {model.UserId}, Tag ID: {model.TagId}, Fields: {fldList}");
                return base.Update(model, fldList);
            }
            catch (Exception ex)
            {
                _logger.Error($"Failed to update user tag relation fields: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Adds a user tag relation asynchronously
        /// </summary>
        /// <param name="model">User tag relation model</param>
        /// <returns>ID of the newly created relation</returns>
        public async Task<int> AddUserTagRelationAsync(UserTagRelation model)
        {
            try
            {
                _logger.Info($"Adding user tag relation asynchronously, User ID: {model.UserId}, Tag ID: {model.TagId}");
                return await Task.Run(() => (int)Add(model));
            }
            catch (Exception ex)
            {
                _logger.Error($"Failed to add user tag relation asynchronously: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Updates a user tag relation asynchronously
        /// </summary>
        /// <param name="model">User tag relation model</param>
        /// <returns>True if update was successful, false otherwise</returns>
        public async Task<bool> UpdateUserTagRelationAsync(UserTagRelation model)
        {
            try
            {
                _logger.Info($"Updating user tag relation asynchronously, User ID: {model.UserId}, Tag ID: {model.TagId}");
                return await Task.Run(() => Update(model));
            }
            catch (Exception ex)
            {
                _logger.Error($"Failed to update user tag relation asynchronously: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets user tag relations for a specific user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of user tag relations</returns>
        public async Task<List<UserTagRelation>> GetUserTagRelationsAsync(int userId)
        {
            try
            {
                _logger.Info($"Getting user tag relations for user {userId}");
                return await Task.Run(() => GetList($"UserId = {userId}"));
            }
            catch (Exception ex)
            {
                _logger.Error($"Failed to get user tag relations for user {userId}: {ex.Message}");
                return new List<UserTagRelation>();
            }
        }

        /// <summary>
        /// Gets a user tag relation by user ID and tag ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="tagId">Tag ID</param>
        /// <returns>User tag relation or null if not found</returns>
        public async Task<UserTagRelation> GetUserTagRelationAsync(int userId, int tagId)
        {
            try
            {
                _logger.Info($"Getting user tag relation for user {userId} and tag {tagId}");
                return await Task.Run(() => GetModel($"UserId = {userId} AND TagId = {tagId}"));
            }
            catch (Exception ex)
            {
                _logger.Error($"Failed to get user tag relation for user {userId} and tag {tagId}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Deletes a user tag relation
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="tagId">Tag ID</param>
        /// <returns>True if deletion was successful, false otherwise</returns>
        public async Task<bool> DeleteUserTagRelationAsync(int userId, int tagId)
        {
            try
            {
                _logger.Info($"Deleting user tag relation for user {userId} and tag {tagId}");
                return await Task.Run(() => DeleteByWhere($"UserId = {userId} AND TagId = {tagId}") > 0);
            }
            catch (Exception ex)
            {
                _logger.Error($"Failed to delete user tag relation for user {userId} and tag {tagId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets the top tags for a user by weight
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="limit">Maximum number of tags to return</param>
        /// <returns>List of user tag relations sorted by weight</returns>
        public async Task<List<UserTagRelation>> GetTopUserTagsAsync(int userId, int limit = 10)
        {
            try
            {
                _logger.Info($"Getting top {limit} tags for user {userId}");
                return await Task.Run(() => GetList($"UserId = {userId}", limit, 1, "*", "Weight DESC"));
            }
            catch (Exception ex)
            {
                _logger.Error($"Failed to get top tags for user {userId}: {ex.Message}");
                return new List<UserTagRelation>();
            }
        }
    }
} 