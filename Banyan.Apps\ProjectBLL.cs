﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.Linq;
using System.IO;
using System.Globalization;
using System.Web;
using System.Collections;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Threading;
using Banyan.Apps.llm;


namespace Banyan.Apps
{
    public class ProjectBLL : BaseDAL<Project>
    {
        private readonly AjaxResult ajaxResult = null;
        private ScoreService scoreService = new ScoreService();
        private SysLogBLL logBLL = new SysLogBLL();
        private MemberBLL _memberBLL;
        private readonly object _memberBLLLock = new object();
        //private RpcClient rpcClient = new RpcClient();
        public ProjectBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        /// <summary>
        /// 获取MemberBLL实例（延迟初始化）
        /// </summary>
        private MemberBLL MemberBLL
        {
            get
            {
                if (_memberBLL == null)
                {
                    lock (_memberBLLLock)
                    {
                        if (_memberBLL == null)
                        {
                            _memberBLL = new MemberBLL();
                        }
                    }
                }
                return _memberBLL;
            }
        }

        public override object Add(Project model)
        {
            try
            {
                ClearCache(model);

                int id = Convert.ToInt32(base.Add(model));
                model.Id = id;
                //Task.Run(() =>
                //{
                //    AddMilvus(model);
                //});
                return id;
            }
            catch (Exception e)
            {
                Logger.Error($"项目{model.Name}添加失败", e);
                throw (e);
            }
        }
        //public async Task<Milvus.AjaxReply> AddMilvus(Project model)
        //{
        //    var p = rpcClient.convertProject(model);
        //    return await Task.FromResult(rpcClient.Add(p));
        //}
        //public async Task<Milvus.AjaxReply> UpdateMilvus(Project model)
        //{
        //    var p = rpcClient.convertProject(model);
        //    return await Task.FromResult(rpcClient.Update(p));
        //}

        public override bool Update(Project model, string fldList)
        {
            try
            {
                ClearCache(model);
                return base.Update(model, fldList);
            }
            catch (Exception e)
            {
                Logger.Error($"项目{model.Name}更新失败", e);
                return false;
            }
        }
        public AjaxResult contributionManagerConfirm(Project model)
        {
            Member user = MemberBLL.GetLogOnUser();
            updateLog("Web, New Project Contribution", "manager confirm", "project ID:" + model.Id + " " + model.contributionLog, user, model.Name);
            return contributionConfirmHelper(model, "contributionManagerConfirm");
        }
        public AjaxResult contributionPartnerConfirm(Project model)
        {
            Member user = MemberBLL.GetLogOnUser();
            updateLog("Web, New Project Contribution", "partner confirm", "project ID:" + model.Id + " " + model.contributionLog, user, model.Name);
            return contributionConfirmHelper(model, "contributionPartnerConfirm");
        }
        public AjaxResult contributionConfirmHelper(Project model, string fields)
        {
            var original = GetModel(model.Id);
            var bothConfirm = (fields == "contributionManagerConfirm" && original.contributionPartnerConfirm) ||
                (fields == "contributionPartnerConfirm" && original.contributionManagerConfirm);

            if (!String.IsNullOrEmpty(original.contributionLog))
            {
                original.contributionLog = '\n' + original.contributionLog;
            }
            model.contributionLog = (bothConfirm ? "<b>" : "") + DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss") + " " + model.contributionLog + (bothConfirm ? "</b>" : "") + original.contributionLog;
            if (Update(model, fields + ", contributionLog"))
            {
                ajaxResult.code = (int)ResultCode.success;
                ajaxResult.data = model.contributionLog;
                return ajaxResult;
            }
            return null;
        }

        public bool ClearCache(Project model)
        {
            try
            {
                RedisUtil.Remove(string.Format(RedisKey.Project_model, model.Id));
                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool UpdateFunds(int id, string fundList)
        {
            try
            {
                var p = new Project();
                p.Id = id;
                p.FundFamilyName = fundList;
                Update(p, "FundFamilyName");
                return true;
            }
            catch (Exception e)
            {
                Logger.Error($"项目{id}更新基金失败", e);
                return false;
            }
        }

        /// <summary>
        /// 获取项目缓存
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Project GetModelByCache(int id)
        {
            string cacheKey = string.Format(RedisKey.Project_model, id);
            Project model = RedisUtil.Get<Project>(cacheKey);
            if (model == null)
            {
                model = base.GetModel(id);
                RedisUtil.Set<Project>(cacheKey, model, TimeSpan.FromMinutes(10));
            }
            return model;
        }

        public string constuctSingle(params string[] sArray)
        {
            return sArray.Aggregate(" ", (res, val) =>
            {
                return res + $@"AND Name like '%{val}%' ";
            });
        }
        public string constructMulti(params string[] sArray)
        {
            return sArray.Aggregate(" ", (res, Name) =>
            {
                return res + $@" AND (Name like '%{Name}%' OR EditorName like '%{Name}%'  OR Participant like '%{Name}%' OR Introducer like '%{Name}%' OR InteralPTCP like '%{Name}%' 
                OR Summary like '%{Name}%' OR Founder like '%{Name}%' OR Background like '%{Name}%' OR CompareProduct like '%{Name}%' OR ProjectManager like '%{Name}%') ";
            });

        }

        public string constructMultiSearch(bool searchNameOnly, params string[] sArray)
        {
            var res = constuctSingle(sArray).Substring(5);
            if (searchNameOnly)
            {
                return res;
            }
            else
            {
                res += $" OR founder like '%{sArray[0]}%' OR contains((EditorName, Participant, Introducer, InteralPTCP, Summary,  Background, CompareProduct, ProjectManager, BusinessData), '\"{sArray[0]}*\"";
                if (sArray.Length == 1)
                {
                    for (int i = 1; i < sArray.Length; i++)
                    {
                        res += $" AND \"{sArray[i]}*\"";
                    }
                    return res + "')";
                }
                else
                {
                    res = "";
                    for (var i = 0; i < sArray.Length; i++)
                    {
                        var tmp = constructMultiSearch(searchNameOnly, new string[] { sArray[i] });
                        res += " AND (" + tmp + ")";
                    }
                    return res.Substring(5);
                }
            }
            //if (searchNameOnly)
            //{
            //    return constuctSingle(sArray);
            //}
            //else
            //{
            //    return constructMulti(sArray);
            //}
        }

        /// <summary>
        /// 构建用户权限控制的WHERE子句
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="myproject">是否只查看我的项目</param>
        /// <param name="forMCP">是否为MCP调用</param>
        /// <returns>权限控制的WHERE子句</returns>
        private string BuildUserPermissionWhere(Member user, string myproject = "", bool forMCP = false)
        {
            string strWhere = $"Status<>{(int)ProjectStatus.delete} ";
            if (forMCP)
            {
                strWhere = $" (Status<>{(int)ProjectStatus.delete} AND nextstepstatusid<>70) ";
            }

            if (!(user.Levels == (int)MemberLevels.Administrator || user.Levels == (int)MemberLevels.SuperUser))
            {
                strWhere += $"AND (EditorName='{user.RealName}' "; // OR DDManager like '%{user.RealName}%' OR ProjectManager like '%{user.RealName}%' OR groupMember like '%{user.RealName}%' OR InteralPTCP like '%{user.RealName}%' ";
                strWhere += $"OR contains((DDManager, ProjectManager, groupMember, InteralPTCP, privateReader), '{user.RealName}') ";
                if (string.IsNullOrEmpty(myproject) || myproject != "true")
                {
                    if (user.Levels != (int)MemberLevels.LimitedUser && user.Levels != (int)MemberLevels.DealRMB && user.Levels != (int)MemberLevels.DealUSD && user.Levels != (int)MemberLevels.DealALL)
                    {
                        string roleIds = string.IsNullOrEmpty(user.Groups) ? "0" : $"{user.Groups}";
                        if (user.Levels != (int)MemberLevels.OnlyRMB && user.Levels != (int)MemberLevels.OnlyUSD)
                        {
                            roleIds += ",6";//其他组均可见,除仅人民币和仅美元的外部受限情况
                        }
                        if (!string.IsNullOrEmpty(roleIds)) // 消费产业组可互看新项目
                        {
                            //if (roleIds.Contains("7") && !roleIds.Contains("1"))
                            //{
                            //    strWhere += $" OR ( (ToRoleId in ({roleIds}) OR (ToRoleId='1' AND  PubTime>='2022-12-5') )  AND IsPrivate = 0) ";
                            //}
                            //else if (roleIds.Contains("1") && !roleIds.Contains("7"))
                            //{
                            //    strWhere += $" OR ( (ToRoleId in ({roleIds}) OR (ToRoleId='7' AND  PubTime>='2022-12-5') ) AND IsPrivate = 0) ";
                            //}
                            //else
                            //{
                                strWhere += $" OR (ToRoleId in ({roleIds}) AND IsPrivate = 0) ";
                            //}
                        }
                        if (user.Levels == (int)MemberLevels.OnlyRMB)
                        {
                            strWhere += $" AND currency='人民币'";
                        }
                        if (user.Levels == (int)MemberLevels.OnlyUSD)
                        {
                            strWhere += $" AND currency='美元'";
                        }
                    }
                }
                else
                {
                    string Collects = new CollectDetailBLL().GetCollects(user.Id, 1);

                    if (!string.IsNullOrEmpty(Collects))
                        strWhere += $" OR Id in({Collects}) ";
                }
                strWhere += ") ";
            }
            else if (myproject == "true")
            {
                strWhere += $"AND (EditorName='{user.RealName}' OR contains((DDManager, ProjectManager, groupMember, InteralPTCP,privateReader), '{user.RealName}') )";
            }

            // 用户入职时间限制
            if (user.limitedJoinTime)
            {
                strWhere += $"AND (PubTime>='{user.AddTime.ToString("yyyy-MM-dd")}' OR (EditorName='{user.RealName}' OR contains((DDManager, ProjectManager, groupMember, InteralPTCP, privateReader), '{user.RealName}') ) ) ";
            }

            return strWhere;
        }

        private string getStrWhere(NameValueCollection paramValues, Member user, out string sort, bool searchNameOnly = false, bool mobile = false, bool forMCP = false)
        {
            string myproject = WebHelper.GetValue("myproject", "", paramValues);
            
            // 使用公共权限控制方法
            string strWhere = BuildUserPermissionWhere(user, myproject, forMCP);

            int isPrivate = WebHelper.GetValueInt("isPrivate", 0, paramValues);
            if (isPrivate != 0)
            {
                strWhere += $"AND IsPrivate={1} ";
            }
            int isSilver = WebHelper.GetValueInt("isSilver", 0, paramValues);
            if (isSilver != 0)
            {
                strWhere += $"AND IsSilver={1} ";
            }

            int ToRoleId;
            if (mobile)
            {
                ToRoleId = WebHelper.GetValueInt("roleid", 0, paramValues);
            }
            else
            {
                ToRoleId = WebHelper.GetValueInt("ToRoleId", 0, paramValues);
            }
            if (ToRoleId > 0)
            {
                strWhere += $"AND ToRoleId={ToRoleId} ";
            }

            string startDate = WebHelper.GetValue("startdate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(startDate))
            {
                strWhere += $"AND PubTime>='{startDate}' ";
            }

            string endDate = WebHelper.GetValue("enddate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(endDate))
            {
                strWhere += $"AND PubTime<'{Convert.ToDateTime(endDate).AddDays(1).ToString("yyyy-MM-dd")}' ";
            }

            string Creator = WebHelper.GetValue("Creator", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(Creator))
            {
                strWhere += $"AND EditorName = '{Creator}' ";
            }

            string projectStatus = WebHelper.GetValue("projectStatus", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(projectStatus))
                strWhere += $"AND nextStepStatus='{projectStatus}' ";
            sort = " PubTime DESC, nextStepStatusID DESC, EditorName DESC, ViewCount DESC ";
            // if (!string.IsNullOrEmpty(startDate) || !string.IsNullOrEmpty(endDate))
            //     sort = " nextStepStatusID DESC, PubTime DESC, EditorName DESC, ViewCount DESC ";

            string Name, fullTextWhere = "";

            if (mobile)
            {
                Name = WebHelper.GetValue("keywords", string.Empty, paramValues);
            }
            else
            {
                Name = WebHelper.GetValue("Name", string.Empty, paramValues);
            }
            Name = Name.Replace("'", "''");

            if (!string.IsNullOrWhiteSpace(Name))
            {
                //string[] sArray = Regex.Split(Name, " 与 ");
                string[] sArray = Name.Split(new[] { " 与 ", ",", "，" }, StringSplitOptions.RemoveEmptyEntries);
                fullTextWhere += "AND (" + constructMultiSearch(searchNameOnly, sArray) + ")";

            }
            if (fullTextWhere != "")
            {
                strWhere += fullTextWhere;
            }
            return strWhere;
        }
        public List<Project> searchCommon(NameValueCollection paramValues, Member user,
            int pageIndex, int pageSize, out int count, bool searchNameOnly = false, bool searchMonthOnly = false, bool forMCP = false)
        {
            string sort;
            string strWhere = getStrWhere(paramValues, user, out sort, searchNameOnly, false, forMCP);

            Logger.Info("search str: " + strWhere, user.RealName);

            List<Project> ProjectList;
            int isScore = WebHelper.GetValueInt("isScore", 0, paramValues);
            int isMemo = WebHelper.GetValueInt("isMemo", 0, paramValues);
            if (isScore != 0)
            {
                ProjectList = GetListBySql($"select * from project join ( select distinct projectId from  ProjectScoreStage) as ps on project.Id = ps.ProjectId where {strWhere} order by PubTime DESC ");
            } else if (isMemo != 0)
            {
                ProjectList = GetListBySql($"select * from project join ( select distinct projectId from  ProjectMemo) as ps on project.Id = ps.ProjectId where {strWhere} order by PubTime DESC ");
            }
            else
            {
                ProjectList = GetList(strWhere, pageSize, pageIndex, "*", sort);
            }

            if (ProjectList != null && ProjectList.Count() > 0 && !searchNameOnly)
            {
                var roleList = new RoleBLL().GetList(false);
                if (roleList != null && roleList.Count() > 0)
                {
                    foreach (var item in ProjectList)
                    {
                        if (searchMonthOnly)
                        {
                            item.PubTimeStr = item.PubTime.ToString("yyyy-MM");
                        }
                        else
                        {
                            item.PubTimeStr = item.PubTime.ToString("yyyy-MM-dd");
                        }
                        item.RoleName = roleList.Where(x => x.Id == item.ToRoleId).Select(x => x.RoleName).FirstOrDefault()?.ToString();
                        item.IsOperate = user.Levels == (byte)MemberLevels.Administrator || item.EditorName == user.RealName || user.Levels == (byte)MemberLevels.SuperUser
                            || item.InteralPTCP.Contains(user.RealName) || item.ProjectManager.Contains(user.RealName)
                            || item.DDManager.Contains(user.RealName) || item.groupMember.Contains(user.RealName);
                        item.Source = GetSource(item.Source);
                    }
                }
            }
            if (isScore != 0 || isMemo != 0)
            { // TODO fix: paga size
                count = ProjectList.Count;
                return ProjectList;
            }
            count = GetCount(strWhere);
            return ProjectList;
        }

        /// <summary>
        /// 分页查询记录
        /// </summary>
        /// <param name="paramValues">页面请求参数</param>
        /// <returns></returns>
        public AjaxResult searchByName(NameValueCollection paramValues)
        {
            Member user = MemberBLL.GetLogOnUser();
            return searchByNameHelper(paramValues, user);
        }
        public class MeetSearchProject
        {
            public int Id;
            public string Name;
        }
        public AjaxResult searchByNameHelper(NameValueCollection paramValues, Member user)
        {
            int count;
            var ProjectList = searchCommon(paramValues, user, 1, int.MaxValue, out count, true);
            var result = ProjectList.Select(val =>
            {
                var tmp = new MeetSearchProject();
                tmp.Id = val.Id;
                tmp.Name = val.Name;
                return tmp;
            }).ToList();
            //Logger.Info(JsonConvert.SerializeObject(ProjectList));
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = result;
            ajaxResult.count = count;
            return ajaxResult;
        }
        /// <summary>
        /// 分页查询记录
        /// </summary>
        /// <param name="paramValues">页面请求参数</param>
        /// <returns></returns>
        public AjaxResult searchByNameMobile(NameValueCollection paramValues)
        {
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var userModel = MemberBLL.GetModelByCache(uid);
            if (userModel == null || userModel.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = userModel == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = userModel == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }

            return searchByNameHelper(paramValues, userModel);
        }

        /// <summary>
        /// 分页查询记录
        /// </summary>
        /// <param name="paramValues">页面请求参数</param>
        /// <returns></returns>
        public AjaxResult GetPageList(NameValueCollection paramValues)
        {

            Member user = MemberBLL.GetLogOnUser();
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10000, paramValues);
            int count;
            var ProjectList = searchCommon(paramValues, user, pageIndex, pageSize, out count);
            updateLog("Web, Get Project List", "view", "getpagelist", user);
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = ProjectList;
            ajaxResult.count = count;
            return ajaxResult;
        }
        public string GetNameByID(int projectID)
        {
            var model = GetModel(projectID);
            return model.Name;
        }
        public AjaxResult GetPageListByNameOnly(NameValueCollection paramValues)
        {
            Member user = MemberBLL.GetLogOnUser();
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10000, paramValues);
            int count;
            var ProjectList = searchCommon(paramValues, user, pageIndex, pageSize, out count, true);
            if (ProjectList != null && ProjectList.Count() > 0)
            {
                var roleList = new RoleBLL().GetList(false);
                if (roleList != null && roleList.Count() > 0)
                {
                    foreach (var item in ProjectList)
                    {
                        item.PubTimeStr = item.PubTime.ToString("yyyy-MM-dd");
                        item.RoleName = roleList.Where(x => x.Id == item.ToRoleId).Select(x => x.RoleName).FirstOrDefault()?.ToString();
                        item.IsOperate = user.Levels == (byte)MemberLevels.Administrator || item.EditorName == user.RealName || user.Levels == (byte)MemberLevels.SuperUser
                            || item.InteralPTCP.Contains(user.RealName) || item.ProjectManager.Contains(user.RealName)
                            || item.DDManager.Contains(user.RealName) || item.groupMember.Contains(user.RealName) || item.privateReader.Contains(user.RealName);
                        item.Source = GetSource(item.Source);
                    }
                }
            }


            updateLog("Web, Get Project List", "view", "getpagelistbyname", user);
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = ProjectList;
            ajaxResult.count = count;
            return ajaxResult;
        }

        /// <summary>
        /// 搜索项目用于聊天选择 - 支持全文搜索
        /// </summary>
        /// <param name="keywords"></param>
        /// <returns></returns>
        public AjaxResult SearchProjectsForChat(string keywords)
        {
            Member user = MemberBLL.GetLogOnUser();
            
            if (string.IsNullOrWhiteSpace(keywords))
            {
                // 如果没有关键词，返回最近的项目
                var recentProjects = GetRecentProjectsForChat(user);
                ajaxResult.code = (int)ResultCode.success;
                ajaxResult.data = recentProjects;
                ajaxResult.count = recentProjects.Count;
                return ajaxResult;
            }

            keywords = keywords.Trim();
            string[] keywordArray = keywords.Split(' ');
            
            // 使用公共权限控制方法
            string strWhere = BuildUserPermissionWhere(user);
            
            // 添加搜索条件 - 搜索项目的多个字段
            strWhere += constructMulti(keywordArray); // 保持完整的 " AND " 连接符
            
            try
            {
                var ProjectList = GetList(strWhere, 20, 1, "*", "PubTime DESC"); // 限制返回20个结果
                
                var roleList = new RoleBLL().GetList(false);
                var result = ProjectList.Select(item => new
                {
                    item.Id,
                    item.Name,
                    item.Summary,
                    item.EditorName,
                    PubTimeStr = item.PubTime.ToString("yyyy-MM-dd"),
                    RoleName = roleList.Where(x => x.Id == item.ToRoleId).Select(x => x.RoleName).FirstOrDefault()?.ToString(),
                    item.ProjectManager,
                    item.Founder
                }).ToList();

                updateLog("Web, Search Projects For Chat", "search", $"keywords: {keywords}", user);
                ajaxResult.code = (int)ResultCode.success;
                ajaxResult.data = result;
                ajaxResult.count = result.Count;
            }
            catch (Exception ex)
            {
                Logger.Error($"SearchProjectsForChat error: {ex.Message}", ex);
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = "搜索失败";
                ajaxResult.data = new List<object>();
                ajaxResult.count = 0;
            }
            
            return ajaxResult;
        }

        /// <summary>
        /// 获取最近的项目用于聊天
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        private List<object> GetRecentProjectsForChat(Member user)
        {
            // 使用公共权限控制方法
            string strWhere = BuildUserPermissionWhere(user);
            
            try
            {
                var ProjectList = GetList(strWhere, 10, 1, "*", "PubTime DESC"); // 返回最近10个项目
                
                var roleList = new RoleBLL().GetList(false);
                return ProjectList.Select(item => new
                {
                    item.Id,
                    item.Name,
                    item.Summary,
                    item.EditorName,
                    PubTimeStr = item.PubTime.ToString("yyyy-MM-dd"),
                    RoleName = roleList.Where(x => x.Id == item.ToRoleId).Select(x => x.RoleName).FirstOrDefault()?.ToString(),
                    item.ProjectManager,
                    item.Founder
                }).ToList().Cast<object>().ToList();
            }
            catch (Exception ex)
            {
                Logger.Error($"GetRecentProjectsForChat error: {ex.Message}", ex);
                return new List<object>();
            }
        }
        public string GetSource(string source)
        {
            string sourceStr = string.Empty;
            switch (source)
            {
                case "0":
                    sourceStr = "项目负责人人脉";
                    break;
                case "1":
                    sourceStr = "同事提及";
                    break;
                case "2":
                    sourceStr = "同事介绍";
                    break;
                case "3":
                    sourceStr = "FA介绍";
                    break;
                case "4":
                    sourceStr = "项目负责人研究";
                    break;
                case "5":
                    sourceStr = "其他形式的同事贡献";
                    break;
                    //case "4":
                    //    sourceStr = "主动上门";
                    //    break;
                    //case "5":
                    //    sourceStr = "老项目";
                    //    break;
            }
            return string.IsNullOrEmpty(sourceStr) ? source : sourceStr;
        }

        /// <summary>
        /// Excel数据bytes
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public byte[] GetListBytes(NameValueCollection paramValues)
        {
            Member user = MemberBLL.GetLogOnUser();

            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int count;
            var ProjectList = searchCommon(paramValues, user, pageIndex, int.MaxValue, out count);
            Logger.Info("Web, Export Project List: userID," + user.Id);

            string[] columnsName = { "项目组", "项目名", "项目负责人", "项目组成员", "编辑人", "日期", "高榕参会人", "公司参会人", "项目来源", "项目介绍人", "项目联系人", "DD负责人", "城市", "公司成立年份", "简介", "创始人姓名", "团队背景", "业务数据", "财务数据", "股权结构", "历史及当前融资方案", "提到的同行/供应商", "项目亮点", "项目风险", "What's New", "是否拿到数据包及下一步安排", "员工人数", "总资产规模", "项目状态", "币种" };
            string[] columns = { "RoleName", "Name", "ProjectManager", "groupMember", "EditorName", "PubTimeStr", "InteralPTCP", "Participant", "Source", "Introducer", "finder", "DDManager", "city", "foundedYear", "Summary", "Founder", "Background", "BusinessData", "FinancialData", "ShareStructure", "InvestHistory", "CompareProduct", "HighLight", "Risk", "UpdatedNews", "NextStep", "HeadCount", "TotalAsset", "nextStepStatus", "Currency" };
            return ExcelHelper.ExportExcel<Project>(ProjectList, columnsName.ToList(), "", false, columns);
        }

        /// <summary>
        /// 更新字段
        /// </summary>
        /// <param name="field"></param>
        /// <returns></returns>
        public AjaxResult FieldSet(NameValueCollection paramValues, Member user)
        {
            var id = WebHelper.GetValueInt("id", 0, paramValues);
            var field = WebHelper.GetValue("field", string.Empty, paramValues);
            var state = WebHelper.GetValueInt("state", 0, paramValues);
            var values = WebHelper.GetValue("values", string.Empty, paramValues);

            if (user == null)
            {
                ajaxResult.code = (int)ResultCode.noright;
                ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.noright);
                return ajaxResult;
            }

            if (id <= 0 || string.IsNullOrWhiteSpace(field))
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                return ajaxResult;
            }

            string oper = field;
            Project model = new Project() { Id = id };
            switch (field)
            {
                case "issue": //超级管理员才有权限上下架项目
                    field = "Status";
                    model.Status = (byte)(state == (int)ProjectStatus.wait ? (int)ProjectStatus.normal : (int)ProjectStatus.wait);
                    break;
                case "delete":
                    field = "Status";
                    model.Status = (byte)ProjectStatus.delete;
                    updateLog("Web, Delete Project", "delete", "ID:" + id, user, model.Id.ToString());
                    //rpcClient.Delete(model.Id.ToString());
                    break;
                //case "recomend":
                //    field = "IsRecommend";
                //    model.IsRecommend = state > 0 ? false : true;
                //    break;
                //case "top": //超级管理员才允许置顶
                //    field = "IsStick";
                //    model.IsStick = state > 0 ? false : true;
                //    break;
                //case "sort":
                //    field = "Sort";
                //    model.Sort = (byte)state;
                //    break;
                //case "role":
                //    field = "ToRoleIds";
                //    model.ToRoleIds = values;
                //    break;
                case "prise":
                    field = "PriseCount";
                    model = GetModelByCache(id);
                    if (model != null)
                        model.PriseCount += 1;
                    break;
                default:
                    break;
            }

            var result = false;
            result = string.IsNullOrEmpty(field) ? false : Update(model, field);
            ajaxResult.code = result ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }
        
        public static string formatProject(Project project)
        {
            Member user = new MemberBLL().GetLogOnUser();
            var prompt = llm.prompts.questionDic["消费"];

            if(project.ToRoleId == 5)
            {
                prompt = llm.prompts.questionDic["医疗"];
            }

            var res = "项目名：" + project.Name.Replace("(revisit)", "");
            if (!string.IsNullOrEmpty(project.Summary))
            {
                res += " 简介：" + project.Summary;
            }
            if(!string.IsNullOrEmpty(project.BusinessData))
            {
                res += " 业务数据：" + project.BusinessData;
            }
            if (!string.IsNullOrEmpty(project.FinancialData))
            {
                res += " 财务数据：" + project.FinancialData;
            }
            if (!string.IsNullOrEmpty(project.ShareStructure))
            {
                res += " 股权结构：" + project.ShareStructure;
            }
            if (!string.IsNullOrEmpty(project.InvestHistory))
            {
                res += " 融资方案：" + project.InvestHistory;
            }
            if (!string.IsNullOrEmpty(project.HighLight))
            {
                res += " 亮点：" + project.HighLight;
            }
            if (!string.IsNullOrEmpty(project.Risk))
            {
                res += " 风险：" + project.Risk;
            }
            if (!string.IsNullOrEmpty(project.UpdatedNews))
            {
                res += " 新进展：" + project.UpdatedNews;
            }
            if (!string.IsNullOrEmpty(project.NextStep))
            {
                res += " 下一步：" + project.NextStep;
            }
            if(res.Length < 30)
            {
                return "";
            }
            Logger.Info("ai提问，项目内容为：" + res, user.RealName);
            res = "您是高榕创投的资深投资人，拥有丰富的行业知识和敏锐的市场洞察力，以严谨的投资视角审阅以下创业项目，从多个角度提出有针对性的疑问，为投资决策提供最有价值的信息。【输出格式参考(仅参考格式，提问内容需结合项目本身，提问角度可以更多)】 " + prompt + @"
【格式参考结束】
项目内容为：
" + res + @"
您的问题如下：";

            return res;
        }
        /// <summary>
        /// 添加或保存分类数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public AjaxResult Save(Project model, string attach = "", string contributions = "", string mobile = "")
        {
            AjaxResult ajaxResult = new AjaxResult();
            var user = MemberBLL.GetLogOnUser(model.Creator);
            if (!string.IsNullOrEmpty(model.nextStepStatus?.Trim()))
            {
                switch (model.nextStepStatus)
                {
                    case "安排合伙人见面":
                        model.nextStepStatusID = 100;
                        break;
                    case "小组讨论":
                        model.nextStepStatusID = 90;
                        break;
                    case "不推进但持续关注":
                        model.nextStepStatusID = 80;
                        break;
                    case "Pass":
                        model.nextStepStatusID = 70;
                        break;
                    default: model.nextStepStatusID = 0; break;
                }
            }
            else
                model.nextStepStatusID = 0;

            if (user != null)
            {
                model.Creator = user.Id;
                model.EditorName = user.RealName;
            }
            if (model.Id > 0)
            {
                try
                {
                    var original = GetModel(model.Id);
                    if (model.Name != original.Name)
                    {
                        Logger.Info($"projectName {original.Name} changed to {model.Name}", user.RealName);
                        new ContributionBLL().SyncProjectName(model.Id, model.Name, original.Name, user);
                    }
                    ClearCache(model);
                    model.CommonName = model.Name.Replace("(revisit)", "");
                    model.LastTime = DateTime.Now;
                    ajaxResult.data = base.Update(model, @"Name,CommonName,Participant,Introducer,Summary,Founder, Background,BusinessData,FinancialData,ShareStructure,InvestHistory,CompareProduct,HighLight,Risk,NextStep,ProjectManager,ToRoleId,IsPrivate,IsSilver,PubTime,Status,foundedYear,nextStepStatus,nextStepStatusID,InteralPTCP,privateReader,Source,city,DDManager,finder,city,UpdatedNews,HeadCount,TotalAsset,groupMember, RevisitId, ContributionDetail, Currency,LastTime");//MeetingDate，LastTime
                }
                catch (Exception e)
                {
                    ajaxResult.code = (int)ResultCode.exception;
                    ajaxResult.msg = e.Message;
                    return ajaxResult;
                }
                //Task.Run(() =>
                //{
                //    UpdateMilvus(model);
                //});
                ajaxResult.code = (bool)ajaxResult.data ? (int)ResultCode.success : (int)ResultCode.exception;
                updateLog("Update Project", "update", mobile + model.ToJson(), user, model.Name);
            }
            else
            {
                var tmpName = model.Name.Replace("'", "''");
                string strWhere = $"Status<>{(int)ProjectStatus.delete} AND Name='{tmpName}' AND PubTime='{model.PubTime}' "; //获取用户可以观看的项目
                if (user != null)
                    strWhere += $"AND EditorName='{user.RealName}' ";
                if (GetCount(strWhere) == 0)
                {
                    try
                    {
                        model.CommonName = model.Name.Replace("(revisit)", "");
                        model.AddTime = DateTime.Now;
                        model.LastTime = DateTime.Now;
                        model.Id = Convert.ToInt32(Add(model));
                    }
                    catch (Exception e)
                    {
                        Logger.Error(e.Message + " " + e.StackTrace, user.RealName);
                        ajaxResult.code = (int)ResultCode.exception;
                        ajaxResult.msg = e.Message;
                        return ajaxResult;
                    }
                    if (model.Id > 0)
                    {
                        AddAttach(attach, model.Id); // 附件添加
                        if (!string.IsNullOrEmpty(contributions) && contributions != "[]")
                        {
                            AddContribution(contributions, model.Id, model.Name); // contribution添加
                            updateLog("Web, New Contribution of Project", "add", "project ID:" + model.Id + " " + contributions, user, model.Name);
                        }
                        
                        // 异步更新用户画像
                        Task.Run(async () =>
                        {
                            try
                            {
                                var userProfileBLL = new UserProfileBLL();
                                await userProfileBLL.UpdateProfileOnNewProjectAsync(user.Id, model);
                            }
                            catch (Exception ex)
                            {
                                Logger.Error($"更新用户画像失败: {ex.Message}", ex);
                            }
                        });
                    }
                    ajaxResult.code = model.Id > 0 ? (int)ResultCode.success : (int)ResultCode.exception;
                    updateLog("New Project", "Save", mobile + model.ToJson(), user, model.Name);
                }
                else
                {
                    ajaxResult.code = (int)ResultCode.exception;
                    ajaxResult.msg = "该项目已添加，勿重复添加！";
                }
            }
            ajaxResult.data = model.Id;
            return ajaxResult;
        }
        public bool AddContribution(string attach, int projectId, string projectName)
        {
            if (string.IsNullOrEmpty(attach))
                return true;

            List<Contribution> list = Utils.DeserializeObjectByJson<List<Contribution>>(attach);
            if (list == null || list.Count <= 0)
            {
                return true;
            }
            var contributionBll = new ContributionBLL();
            foreach (var item in list)
            {
                item.projectID = projectId;
                item.projectName = projectName;
                contributionBll.Add(item);
            }
            return true;
        }

        public bool AddAttach(string attach, int sourceId)
        {
            Member user = MemberBLL.GetLogOnUser();
            if (string.IsNullOrEmpty(attach))
                return true;

            List<Attachment> list = Utils.DeserializeObjectByJson<List<Attachment>>(attach);
            if (list == null || list.Count <= 0)
            {
                return true;
            }
            AttachmentBLL attachBll = new AttachmentBLL();
            //TransactionModel tranModel = new TransactionModel();
            //foreach(var item in list)
            //{
            //    tranModel.Add(attachBll.BuildAddSql(item));
            //}
            foreach (var item in list)
            {
                try
                {
                    item.SourceId = sourceId;
                    item.AtName = item.AtName.Replace("+", "加").Replace(" ", "_");
                    var tmpPathArr = item.AtUrl.Split('/');
                    item.Path = $"/imsfiles/{tmpPathArr[2]}/{tmpPathArr[3]}/" + item.AtName;
                    item.Creator = user.RealName;
                    try
                    {
                        if (!item.Content.IsEmpty() && !item.Content.Equals("-1"))
                        {
                            item.Content = logBLL.GetModel(Convert.ToInt32(item.Content)).Description;
                        }
                    }
                    catch (Exception e)
                    {
                        Logger.Error(e.Message, e);
                    }
                    attachBll.Add(item);
                }
                catch (Exception e)
                {
                    Logger.Error(e.Message, user.RealName);
                }
            }
            return true;
        }
        public AjaxResult GetTypeProjects(NameValueCollection paramValues)
        {
            #region 用户信息
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var user = MemberBLL.GetModelByCache(uid); //GetModelByCache(uid);
            if (user == null || user.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = user == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = user == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }
            #endregion
            string type = WebHelper.GetValue("type", string.Empty, paramValues);
            string keyWords = WebHelper.GetValue("keywords", string.Empty, paramValues);
            string searchName = WebHelper.GetValue("searchName", string.Empty, paramValues);

            var ProjectList = new List<Project>();
            string sort;
            string strWhere = getStrWhere(paramValues, user, out sort, !searchName.IsEmpty(), true);
            if (type == "introduce")
            {
                strWhere += $" AND (introducer like '%{user.RealName}%' OR finder like '%{user.RealName}%') AND projectManager NOT like  '%{user.RealName}%' ";
            }
            else if (type == "fugai")
            {
                strWhere += $" AND projectManager like '%{user.RealName}%' AND source = '0'";
            }
            else if (type == "yanjiu")
            {
                strWhere += $" AND projectManager like '%{user.RealName}%' AND source = '4'";
            }
            else if (type == "tongshitiji")
            {
                strWhere += $" AND projectManager like '%{user.RealName}%' AND source = '1'";
            }
            else if (type == "tongshijieshao")
            {
                strWhere += $" AND projectManager like '%{user.RealName}%' AND source = '2'";
            }
            else if (type == "Fa")
            {
                strWhere += $" AND projectManager like '%{user.RealName}%' AND source = '3'";
            }
            else if (type == "ts")
            {
                strWhere += $" AND projectManager like '%{user.RealName}%' AND nextStepStatus = 'TS已签署'";
            }
            else if (type == "DD")
            {
                strWhere += $" AND projectManager like '%{user.RealName}%' AND nextStepStatus = 'Pre-DD'";
            }
            else if (type == "partner")
            {
                strWhere += $" AND projectManager like '%{user.RealName}%' AND nextStepStatus = '安排合伙人见面'";
            }
            else if (type == "teamdiscuss")
            {
                strWhere += $" AND projectManager like '%{user.RealName}%' AND nextStepStatus = '小组讨论'";
            }
            else if (type != "")
            {
                strWhere += $" AND {type} like '%{user.RealName}%'";
            }
            else
            {
                strWhere += $" AND projectManager like '%{user.RealName}%' ";
            }

            //默认按排序值、发布时间、点赞数、评论数、浏览数排序
            ProjectList = GetList(strWhere, int.MaxValue, 0, "*", sort);
            ajaxResult.count = GetCount(strWhere);


            //项目内容格式处理
            if (ProjectList != null && ProjectList.Count() > 0)
            {
                var roleList = new RoleBLL().GetList(false);
                var priseList = new PraiseDetailBLL().GetList($"UserId={user.Id} AND Mode = {(int)PraiseModeEnum.Project} AND ArticleId IN({string.Join(",", ProjectList.Select(x => x.Id).ToArray())})");
                ajaxResult.data = ProjectList.Select(item =>
                {
                    return new { item.Id, item.Name, item.Summary, item.PriseCount, item.EditorName, PubTime = item.PubTime.ToString("yyyy-MM-dd"), IsPrise = priseList.Where(x => x.ArticleId == item.Id).Count() > 0, RoleName = roleList.Where(x => x.Id == item.ToRoleId).Select(x => x.RoleName).FirstOrDefault()?.ToString(), item.nextStepStatus, item.HeadCount, item.TotalAsset, item.groupMember, item.aisummary, item.ai_reasoning, ai_question=MarkdownToHtmlLight(item.ai_question), HasRight = true };
                });
            }
            else
            {
                ajaxResult.data = ProjectList;
            }
            updateLog("MiniApp, Get Project List", "view", strWhere, user);
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;
        }
        public string MarkdownToHtmlLight(string markdown)
        {
            markdown = Regex.Replace(markdown, @"\*\*(.*?)\*\*", "<strong>$1</strong>");

            // 3. 处理水平线（以---开头的行）
            markdown = Regex.Replace(markdown, @"^\s*---\s*$", "", RegexOptions.Multiline);

            //// 替换换行符，以便在HTML中显示多行文本
            //markdown = markdown.Replace(Environment.NewLine, "<br />" + Environment.NewLine);

            //markdown = markdown.Replace("#", "✦");
            markdown = Regex.Replace(markdown, @"#+", "•");

            return markdown;
        }
        /// <summary>
        /// 项目列表
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public AjaxResult GetProjects(NameValueCollection paramValues)
        {
            #region 用户信息
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var user = MemberBLL.GetModelByCache(uid); //GetModelByCache(uid);
            if (user == null || user.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = user == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = user == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }
            #endregion

            #region 交互基本参数
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            if (pageIndex == 2)
            {
                scoreService.checkStageClose();
            }
            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            string getType = WebHelper.GetValue("getType", string.Empty, paramValues);
            string keyWords = WebHelper.GetValue("keywords", string.Empty, paramValues);
            string searchName = WebHelper.GetValue("searchName", string.Empty, paramValues);

            var ProjectList = new List<Project>();
            string sort;
            string strWhere = getStrWhere(paramValues, user, out sort, !searchName.IsEmpty(), true);

            //Logger.Info("MiniApp, Get Project List: userID," + user.Id);
            if (getType.Trim().Equals("search")) //搜索
            {
                if (!string.IsNullOrEmpty(keyWords))
                {
                    ProjectList = GetList(strWhere, 1000, 1, "Id,Name");
                    if (ProjectList != null && ProjectList.Count() > 0)
                    {
                        ajaxResult.data = ProjectList.Select(item =>
                        {
                            return new { item.Id, item.Name };
                        });
                    }
                    ajaxResult.code = (int)ResultCode.success;
                    return ajaxResult;
                }
                else
                {
                    ajaxResult.code = (int)ResultCode.success;
                    ajaxResult.data = null;
                    return ajaxResult;
                }
            }
            int isScore = WebHelper.GetValueInt("isScore", 0, paramValues);
            int isMemo = WebHelper.GetValueInt("isMemo", 0, paramValues);
            if (isScore != 0)
            {
                ProjectList = GetListBySql($"select * from project join ( select distinct projectId from  ProjectScoreStage) as ps on project.Id = ps.ProjectId where {strWhere} order by PubTime DESC ");
                ajaxResult.count = ProjectList.Count();
            } else if (isMemo != 0)
            {
                ProjectList = GetListBySql($"select * from project join ( select distinct projectId from  ProjectMemo) as ps on project.Id = ps.ProjectId where {strWhere} order by PubTime DESC ");
                ajaxResult.count = ProjectList.Count();
            }
            else
            {   //默认按排序值、发布时间、点赞数、评论数、浏览数排序

                var str = $"SELECT * FROM Project WHERE Id in(SELECT ArticleId FROM (SELECT ROW_NUMBER() OVER(ORDER BY T.Id DESC)AS Row, T.* FROM CollectDetail T ) TT WHERE TT.Row BETWEEN {(pageIndex - 1) * pageSize + 1} AND {pageIndex * pageSize}) {strWhere}";
                ProjectList = GetList(strWhere, pageSize, pageIndex, "*", sort);
                ajaxResult.count = GetCount(strWhere);
            }
            #endregion
            //项目内容格式处理
            if (ProjectList != null && ProjectList.Count() > 0)
            {
                var roleList = new RoleBLL().GetList(false);
                var priseList = new PraiseDetailBLL().GetList($"UserId={user.Id} AND Mode = {(int)PraiseModeEnum.Project} AND ArticleId IN({string.Join(",", ProjectList.Select(x => x.Id).ToArray())})");
                ajaxResult.data = ProjectList.Select(item =>
                {
                    return new { item.Id, item.Name, item.Summary, item.PriseCount, item.EditorName, PubTime = item.PubTime.ToString("yyyy-MM-dd"), IsPrise = priseList.Where(x => x.ArticleId == item.Id).Count() > 0, RoleName = roleList.Where(x => x.Id == item.ToRoleId).Select(x => x.RoleName).FirstOrDefault()?.ToString(), item.nextStepStatus, item.HeadCount, item.TotalAsset, item.groupMember, item.aisummary, item.ai_question, item.ai_reasoning, HasRight = true };
                });
            } // 考虑已经是最后一页没有更多项目的情况
            else if (ajaxResult.count == 0)
            {
                var tmpres = new NewsBLL().GetNews(paramValues, false);
                var list = (List<News>)tmpres.data;
                if (list != null && list.Count > 0)
                {
                    ajaxResult.data = list.Select(item =>
                    {
                        return new
                        {
                            isNews = true,
                            item.Id,
                            Name = item.Title,
                            Summary = item.Content,
                            EditorName = item.Classify,
                            PubTime = item.PubTime.ToString("yyyy-MM-dd"),
                            nextStepStatus = item.Tag,
                            HasRight = true
                        };
                    });
                    ajaxResult.count = tmpres.count;
                }
                else
                {
                    ajaxResult.data = null;
                }
            }
            else
            {
                ajaxResult.data = null;
            }

            updateLog("MiniApp, Get Project List", "view", strWhere, user);
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;
        }

        //object stage = null;
        //ScoreService scoreService = new ScoreService();
        //if (tag == "score") {
        //    stage = scoreService.GetInScoreStage(ProjectId);
        //}
        public AjaxResult UpdateSummaryViews(NameValueCollection paramValues)
        {
            int ProjectId = WebHelper.GetValueInt("aid", 0, paramValues);
            int userId = WebHelper.GetValueInt("uid", 0, paramValues);

            if (ProjectId <= 0 || userId <= 0)
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                ajaxResult.msg = "参数不合法！";
                return ajaxResult;
            }

            var user = MemberBLL.GetModelByCache(userId);
            if (user == null || user.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = user == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = user == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }

            var model = new ProjectBLL().GetModelByCache(ProjectId);
            if (model == null || model.Status == (int)ProjectStatus.delete)
            {
                ajaxResult.code = (int)ResultCode.notdata;
                return ajaxResult;
            }
            updateLog("MiniApp, Get Project Summary", "view", "projectID:" + model.Id + ",Name:" + model.Name, user, model.Name);
            var cacheKey = string.Format(RedisKey.Project_views, model.Id);
            var viewsCache = RedisUtil.Get<string>(cacheKey);
            var canView = true;
            if (!string.IsNullOrEmpty(viewsCache)) // 已经看过
            {
            }
            else if (DateTime.Now - model.PubTime > TimeSpan.FromDays(7)) // 一周前的项目
            {
                var key = string.Format(RedisKey.daily_summary_view_count, user.RealName, $"{DateTime.Now.ToString("MM-dd")}");

                var value = RedisUtil.Get<string>(key);
                var numLimit = 50;
                if (user.RealName.Equals("孙杭萍"))
                {
                    numLimit = 60;
                }
                if (!string.IsNullOrEmpty(value) && user.Levels != (int)MemberLevels.SuperUser && !user.RealName.Equals("吕祺")) // 除去开发测试的情况
                {
                    var count = Convert.ToInt32(value) + 1;
                    RedisUtil.Set<string>(key, count + "", TimeSpan.FromDays(2));
                    if (count >= numLimit)
                    {
                        canView = false;
                    }
                }
                else
                {
                    RedisUtil.Set<string>(key, "1", TimeSpan.FromDays(2));
                }

                RedisUtil.Set<string>(cacheKey, $"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")},1");
            }
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = canView;
            return ajaxResult;
        }
        /// <summary>
        /// 更新浏览量
        /// </summary>
        /// <param name="model"></param>
        public bool UpdateViews(Project model, Member user)
        {
            // 1. 更新项目浏览量，并判断是否近期查看
            bool isRecentlyViewed = UpdateProjectViewCount(model);
            
            // 2. 检查用户访问限制（仅对一周前的项目，且不是刚查看过）
            if (!isRecentlyViewed && user.Levels != (int)MemberLevels.SuperUser 
            && DateTime.Now - model.PubTime > TimeSpan.FromDays(7))
            {
                return CheckUserDailyViewLimit(user, model);
            }
            
            return true; // 一周内的新项目无访问限制，或近期已查看过
        }
        
        /// <summary>
        /// 更新项目浏览量
        /// </summary>
        /// <returns>true表示近期已查看过，false表示首次查看或距离上次查看时间较长</returns>
        private bool UpdateProjectViewCount(Project model)
        {
            var projectViewCacheKey = string.Format(RedisKey.Project_views, model.Id);
            var cachedViewData = RedisUtil.Get<string>(projectViewCacheKey);
            
            if (string.IsNullOrEmpty(cachedViewData)) // 第一次查看项目
            {  
                // 直接更新数据库中的浏览量
                model.ViewCount += 1;
                Update(model, "ViewCount");
                
                // 存入Redis缓存，格式为 "时间,浏览次数"
                RedisUtil.Set<string>(projectViewCacheKey, $"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")},1", TimeSpan.FromMinutes(5));
                return false; // 首次查看
            }
            else 
            {
                // 解析缓存数据：格式为 "时间,浏览次数"
                var cacheDataParts = cachedViewData.Split(',');
                if (cacheDataParts.Length >= 2)
                {
                    var lastUpdateTime = Convert.ToDateTime(cacheDataParts[0]);
                    var cachedViewCount = Convert.ToInt32(cacheDataParts[1]);
                    var timeSinceLastUpdate = DateTime.Now - lastUpdateTime;

                    // 获取配置的浏览量更新间隔时间（默认90秒）
                    var updateIntervalSeconds = Convert.ToInt32(ConfigurationManager.AppSettings["ViewCountUpdateInterval"] ?? "90");
                    
                    // 如果距离上次更新时间超过配置间隔，则更新数据库中的浏览量
                    if (timeSinceLastUpdate.TotalSeconds > updateIntervalSeconds)
                    {
                        // 累加缓存中的浏览次数和当前这次访问
                        model.ViewCount += cachedViewCount + 1;
                        Update(model, "ViewCount");
                        //清除缓存的原因：1. 重置统计周期 2. 防止重复累加  3. 保持数据一致性 
                        RedisUtil.Remove(projectViewCacheKey);
                        return false; // 距离上次查看时间较长，不算近期查看
                    }
                    else
                    {
                        // 未达到更新间隔，只更新缓存中的浏览次数
                        RedisUtil.Set<string>(projectViewCacheKey, $"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")},{cachedViewCount + 1}", TimeSpan.FromMinutes(5));
                        return true; // 近期已查看过
                    }
                }
            }
            return false; // 默认情况
        }
        
        /// <summary>
        /// 检查用户每日查看限制
        /// </summary>
        private bool CheckUserDailyViewLimit(Member user, Project model)
        {
            // 构建用户每日项目查看详情的Redis键
            var dailyDetailKey = string.Format(RedisKey.daily_project_view_detail, user.RealName, $"{DateTime.Now.ToString("MM-dd")}");
            var existingViewDetail = RedisUtil.Get<string>(dailyDetailKey);
            var updatedViewContent = (existingViewDetail ?? "") + "\n" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + " " 
            + model.Name + " (" + model.ProjectManager + " " + model.PubTime.ToString("yyyy-MM-dd") + ")";
            RedisUtil.Set<string>(dailyDetailKey, updatedViewContent, TimeSpan.FromDays(2));

            // 构建用户每日项目查看计数的Redis键
            var dailyCountKey = string.Format(RedisKey.daily_project_view_count, user.RealName, $"{DateTime.Now.ToString("MM-dd")}");
            var currentDailyCount = RedisUtil.Get<string>(dailyCountKey);
            var numLimit = GetUserViewLimit(user.RealName);
            
            // 检查用户是否已超过每日查看限制
            if (string.IsNullOrEmpty(currentDailyCount))  // 首次查看设置初始计数
            {
                RedisUtil.Set<string>(dailyCountKey, "1", TimeSpan.FromDays(2));
            }
            else
            {
                 var newDailyCount = Convert.ToInt32(currentDailyCount) + 1;
                // 更新每日查看计数
                RedisUtil.Set<string>(dailyCountKey, newDailyCount + "", TimeSpan.FromDays(2));
                
                // 达到限制时发送报警邮件并禁用账户
                if (newDailyCount == numLimit)
                {
                    Mail.botSendMailToAdmin($"【报警】IMS日阅读数达{numLimit}!", $"WARNING! \n {user.RealName} visited {numLimit} projects today! Account is Banned now!\n" + updatedViewContent, "<EMAIL>");
                    Ldap.addToGroup(user.RealName, "cn=leave,ou=groups,dc=gaorongvc,dc=cn");
                    RedisUtil.Remove(string.Format(RedisKey.member_model, user.Id)); // 立刻禁用
                }
                
                if (newDailyCount >= numLimit)
                {
                    return false; // 超过限制时禁止查看
                }
            }

            return true;
        }
        
        /// <summary>
        /// 获取用户查看限制数量
        /// </summary>
        private int GetUserViewLimit(string userName)
        {
            // 可以从配置文件或数据库读取，避免硬编码
            switch (userName)
            {
                case "孙杭萍":
                    return 50;
                case "吕祺":
                    return int.MaxValue;
                default:
                    return 30;
            }
        }

        public AjaxResult GetRevisitRightMobile(NameValueCollection paramValues)
        {
            int id = WebHelper.GetValueInt("id", 0, paramValues);
            Project projectmodel = GetModelByCache(id);
            int userId = WebHelper.GetValueInt("uid", 0, paramValues);
            var user = MemberBLL.GetModelByCache(userId);
            if (user == null || user.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = user == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = user == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }
            return getRevisitRightHelper(projectmodel, user);
        }

        public AjaxResult GetRevisitRight(int id)
        {

            Project projectmodel = GetModelByCache(id);
            Member user = MemberBLL.GetLogOnUser();
            return getRevisitRightHelper(projectmodel, user);

        }
        private AjaxResult getRevisitRightHelper(Project projectmodel, Member user)
        {
            ajaxResult.code = (int)ResultCode.success;
            if (user.Levels == (int)MemberLevels.Administrator || user.Levels == (int)MemberLevels.SuperUser || projectmodel.EditorName == user.RealName ||
                projectmodel.DDManager.Contains(user.RealName) ||
                projectmodel.ProjectManager.Contains(user.RealName) ||
                projectmodel.groupMember.Contains(user.RealName) ||
                $",{user.Groups},".Contains($",{projectmodel.ToRoleId},") ||
                (user.Levels != (int)MemberLevels.LimitedUser && projectmodel.ToRoleId == 6))
            {

                ajaxResult.data = new
                {
                    projectmodel.Id,
                    projectmodel.Name,
                    projectmodel.ProjectManager
                };

            }
            else
            {
                ajaxResult.data = new
                {
                    projectmodel.Name,
                    projectmodel.ProjectManager
                };
            }
            return ajaxResult;
        }

        public AjaxResult GetProjectCompareList(NameValueCollection paramValues)
        {
            string ids = WebHelper.GetValue("ids", string.Empty, paramValues);
            int userId = WebHelper.GetValueInt("uid", 0, paramValues);

            if (ids.IsEmpty() || userId <= 0)
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                ajaxResult.msg = "参数不合法！";
                return ajaxResult;
            }

            var user = MemberBLL.GetModelByCache(userId);
            if (user == null || user.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = user == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = user == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }

            //列表页设置权限，详情页无需再加权限
            //if (ProjectModel.Creator != user.Id && !(user.Levels == (int)MemberLevels.Administrator || user.Levels == (int)MemberLevels.SuperUser) && !$",{user.Groups},".Contains($",{ProjectModel.ToRoleId},") && !ProjectModel.DDManager.ToString().Contains(user.RealName) && !ProjectModel.ProjectManager.ToString().Contains(user.RealName))
            //{
            //    ajaxResult.code = (int)ResultCode.noright;
            //    return ajaxResult;
            //}

            // 添加记录
            Logger.Info("MiniApp, Compare Project projectIDs:" + ids, user.RealName);

            var ProjectList = GetListBySql($"select * from project where id in ({ids}) order by charindex(','+ cast(id as varchar(10)) +',', ',{ids},')");

            ajaxResult.code = (int)ResultCode.success;

            ajaxResult.data = ProjectList.Select(ProjectModel => {
                var role = new RoleBLL().GetModelByCache(ProjectModel.ToRoleId);
                return new
                {
                    ProjectModel.Id,
                    ProjectModel.Creator,
                    ProjectModel.Name,
                    ProjectModel.Participant,
                    ProjectModel.Introducer,
                    ProjectModel.Summary,
                    ProjectModel.BusinessData,
                    ProjectModel.FinancialData,
                    ProjectModel.ShareStructure,
                    ProjectModel.InvestHistory,
                    ProjectModel.CompareProduct,
                    ProjectModel.HighLight,
                    ProjectModel.Risk,
                    ProjectModel.NextStep,
                    ProjectModel.ProjectManager,
                    ProjectModel.Founder,
                    ProjectModel.Background,
                    ProjectModel.EditorName,
                    ProjectModel.CollectCount,
                    ProjectModel.CommentCount,
                    ProjectModel.PriseCount,
                    RoleName = (role == null ? string.Empty : role.RoleName),
                    PubTime = ProjectModel.PubTime.ToString("yyyy-MM-dd"),
                    //UserScore = scoreService.GetUserToScore(user, ProjectId),
                    ProjectModel.foundedYear,
                    ProjectModel.nextStepStatus,
                    ProjectModel.ToRoleId,
                    ProjectModel.InteralPTCP,
                    Source = GetSource(ProjectModel.Source),
                    SourceIdx = ProjectModel.Source,
                    ProjectModel.city,
                    ProjectModel.DDManager,
                    ProjectModel.finder,
                    ProjectModel.UpdatedNews,
                    ProjectModel.HeadCount,
                    ProjectModel.TotalAsset,
                    ProjectModel.groupMember,
                    ProjectModel.CompleteScore,
                    ProjectModel.RevisitId,
                    ProjectModel.ContributionDetail,
                    ProjectModel.IsPrivate,
                    ProjectModel.IsSilver
                    //PartnerScoreAvg=CalPartnerScoreAvg(ProjectId),
                    //ProjectModel.AvgScore,
                    //ProjectModel.CountScore,
                    //stage
                };
            });

            return ajaxResult;
        }
        /// <summary>
        /// 获取项目数据
        /// </summary>
        /// <returns></returns>
        public AjaxResult GetProject(NameValueCollection paramValues)
        {
            int ProjectId = WebHelper.GetValueInt("aid", 0, paramValues);
            int userId = WebHelper.GetValueInt("uid", 0, paramValues);

            if (ProjectId <= 0 || userId <= 0)
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                ajaxResult.msg = "参数不合法！";
                return ajaxResult;
            }

            var user = MemberBLL.GetModelByCache(userId);
            if (user == null || user.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = user == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = user == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }

            var ProjectModel = new ProjectBLL().GetModelByCache(ProjectId);
            if (ProjectModel == null || ProjectModel.Status == (int)ProjectStatus.delete)
            {
                ajaxResult.code = (int)ResultCode.notdata;
                return ajaxResult;
            }
            //列表页设置权限，详情页无需再加权限
            //if (ProjectModel.Creator != user.Id && !(user.Levels == (int)MemberLevels.Administrator || user.Levels == (int)MemberLevels.SuperUser) && !$",{user.Groups},".Contains($",{ProjectModel.ToRoleId},") && !ProjectModel.DDManager.ToString().Contains(user.RealName) && !ProjectModel.ProjectManager.ToString().Contains(user.RealName))
            //{
            //    ajaxResult.code = (int)ResultCode.noright;
            //    return ajaxResult;
            //}

            // 添加记录
            updateLog("MiniApp, Get Project", "view", "projectID:" + ProjectId + ",Name:" + ProjectModel.Name, user, ProjectModel.Name);

            //浏览量更新
            if (!UpdateViews(ProjectModel, user))
            {
                ajaxResult.code = (int)ResultCode.notdata;
                return ajaxResult;
            }
            //object stage = null;
            //ScoreService scoreService = new ScoreService();
            //if (tag == "score") {
            //    stage = scoreService.GetInScoreStage(ProjectId);
            //}

            //返回数据
            var role = new RoleBLL().GetModelByCache(ProjectModel.ToRoleId);
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = new
            {
                ProjectModel.Id,
                ProjectModel.Creator,
                ProjectModel.Name,
                ProjectModel.Participant,
                ProjectModel.Introducer,
                ProjectModel.Summary,
                ProjectModel.BusinessData,
                ProjectModel.FinancialData,
                ProjectModel.ShareStructure,
                ProjectModel.InvestHistory,
                ProjectModel.CompareProduct,
                ProjectModel.HighLight,
                ProjectModel.Risk,
                ProjectModel.NextStep,
                ProjectModel.ProjectManager,
                ProjectModel.Founder,
                ProjectModel.Background,
                ProjectModel.EditorName,
                ProjectModel.CollectCount,
                ProjectModel.CommentCount,
                ProjectModel.PriseCount,
                RoleName = (role == null ? string.Empty : role.RoleName),
                PubTime = ProjectModel.PubTime.ToString("yyyy-MM-dd"),
                IsCollect = new CollectDetailBLL().IsCollect(userId, ProjectId, 1),
                //UserScore = scoreService.GetUserToScore(user, ProjectId),
                ProjectModel.foundedYear,
                ProjectModel.nextStepStatus,
                ProjectModel.ToRoleId,
                ProjectModel.InteralPTCP,
                ProjectModel.privateReader,
                Source = GetSource(ProjectModel.Source),
                SourceIdx = ProjectModel.Source,
                ProjectModel.city,
                ProjectModel.DDManager,
                ProjectModel.finder,
                ProjectModel.UpdatedNews,
                ProjectModel.HeadCount,
                ProjectModel.TotalAsset,
                ProjectModel.groupMember,
                ProjectModel.CompleteScore,
                ProjectModel.RevisitId,
                ProjectModel.ContributionDetail,
                ProjectModel.IsPrivate,
                ProjectModel.IsSilver,
                ProjectModel.Currency,
                ProjectModel.FundFamilyName,
                ProjectModel.ai_reasoning,
                ai_question = MarkdownToHtmlLight(ProjectModel.ai_question),
                ProjectModel.contributionLog,
                ProjectModel.contributionManagerConfirm,
                ProjectModel.contributionPartnerConfirm,
                AddTime = ProjectModel.AddTime.ToString("yyyy-MM-dd") == "0001-01-01" ? "" : ProjectModel.AddTime.ToString("yyyy-MM-dd"),
                LastTime = ProjectModel.LastTime.ToString("yyyy-MM-dd") == "0001-01-01" ? "" : ProjectModel.LastTime.ToString("yyyy-MM-dd"),
                //PartnerScoreAvg=CalPartnerScoreAvg(ProjectId),
                //ProjectModel.AvgScore,
                //ProjectModel.CountScore,
                //stage
            };

            return ajaxResult;
        }
        public bool isProjectManager(int id)
        {
            if (id == 0)
            {
                return false;
            }
            Project ProjectModel = GetModelByCache(id);
            Member user = MemberBLL.GetLogOnUser();
            return ProjectModel.ProjectManager == user.RealName;
        }
        public AjaxResult SetProjectCompleteScore(int id, string score)
        {
            Project ProjectModel = GetModelByCache(id);
            ProjectModel.CompleteScore = score;
            try
            {
                Update(ProjectModel, "CompleteScore");
                ajaxResult.code = (int)ResultCode.success;
            }
            catch
            {
                ajaxResult.code = (int)ResultCode.exception;
            }
            return ajaxResult;
        }

        public int GetProjectRoleId(int id)
        {
            Project ProjectModel = GetModelByCache(id);
            return ProjectModel.ToRoleId;
        }
        public AjaxResult GetProjectDetail(int id, bool backAll = false)
        {
            Project ProjectModel = null;
            if (id <= 0)
            {
                ProjectModel = new Project();
            }
            else
                ProjectModel = GetModelByCache(id);

            if (backAll)
            {
                ajaxResult.code = (int)ResultCode.success;
                ProjectModel = ProjectModel ?? new Project();
                ProjectModel.ai_question = MarkdownToHtmlLight(ProjectModel.ai_question);
                ProjectModel.PubTimeStr = ProjectModel.PubTime.ToString("yyyy-MM-dd");
                ajaxResult.data = ProjectModel;
                return ajaxResult;
            }
            if (ProjectModel == null || ProjectModel.Status == (int)ProjectStatus.delete)
            {
                ajaxResult.code = (int)ResultCode.notdata;
                return ajaxResult;
            }
            var user = MemberBLL.GetLogOnUser(0);

            updateLog("Web, Get Project", "view", "projectID:" + id + ",Name:" + ProjectModel.Name, user, ProjectModel.Name);

            //浏览量更新
            if (!UpdateViews(ProjectModel, user))
            {
                ajaxResult.code = (int)ResultCode.notdata;
                return ajaxResult;
            }

            Role role = new RoleBLL().GetModelByCache(ProjectModel.ToRoleId);
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = new
            {
                ProjectModel.Id,
                ProjectModel.Creator,
                ProjectModel.Name,
                ProjectModel.Participant,
                ProjectModel.Introducer,
                ProjectModel.Summary,
                ProjectModel.BusinessData,
                ProjectModel.FinancialData,
                ProjectModel.ShareStructure,
                ProjectModel.InvestHistory,
                ProjectModel.CompareProduct,
                ProjectModel.HighLight,
                ProjectModel.Risk,
                ProjectModel.NextStep,
                ProjectModel.ProjectManager,
                ProjectModel.Founder,
                ProjectModel.Background,
                ProjectModel.EditorName,
                ProjectModel.CollectCount,
                ProjectModel.CommentCount,
                ProjectModel.PriseCount,
                RoleName = (role == null ? string.Empty : role.RoleName),
                PubTime = ProjectModel.PubTime.ToString("yyyy-MM-dd"),
                ProjectModel.foundedYear,
                ProjectModel.nextStepStatus,
                ProjectModel.InteralPTCP,
                ProjectModel.privateReader,
                Source = GetSource(ProjectModel.Source),

                ProjectModel.city,
                ProjectModel.DDManager,
                ProjectModel.finder,
                ProjectModel.UpdatedNews,
                ProjectModel.HeadCount,
                ProjectModel.TotalAsset,
                ProjectModel.groupMember,
                ProjectModel.CompleteScore,
                ProjectModel.RevisitId,
                ProjectModel.Currency,
                ProjectModel.ContributionDetail,
                ProjectModel.contributionManagerConfirm,
                ProjectModel.contributionPartnerConfirm,
                ai_question = MarkdownToHtmlLight(ProjectModel.ai_question),
                ProjectModel.ai_reasoning,
                AddTime = ProjectModel.AddTime.ToString("yyyy-MM-dd") == "0001-01-01" ? "" : ProjectModel.AddTime.ToString("yyyy-MM-dd"),
                LastTime = ProjectModel.LastTime.ToString("yyyy-MM-dd") == "0001-01-01" ? "" : ProjectModel.LastTime.ToString("yyyy-MM-dd"),
            };

            return ajaxResult;
        }

        public AjaxResult GetProjectDocs(int id, bool fullPath = false)
        {
            ajaxResult.code = (int)ResultCode.success;
            List<Attachment> list = new AttachmentBLL().GetDocList(id, SourceTypeEnum.Project);
            if (fullPath && list.Count > 0)
            {
                String hostUrl = System.Configuration.ConfigurationManager.AppSettings["FileDomain"].ToString() ?? "";
                foreach (var item in list)
                {
                    if (item.AtSuffix == "BP" || item.AtSuffix == "DD")
                    {
                        var t = item.AtUrl.Split(',');
                        item.AtUrl = hostUrl + string.Join("," + hostUrl, t);
                    }
                    else
                    {
                        item.AtUrl = hostUrl + item.AtUrl;
                    }
                    item.Content = "";
                }
            }
            var newList = new List<Attachment>();
            var sortList = new List<Attachment>();
            foreach (var i in list)
            {
                if(i.AtSuffix == "Image")
                {
                    newList.Add(i);
                } else
                {
                    sortList.Add(i);
                }
            }
            sortList.AddRange(newList);
            ajaxResult.data = sortList;
            return ajaxResult;
        }

        /// <summary>
        /// 更新数量审计字段
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public bool UpCountField(int id, string type, int raiseCount = 1)
        {
            string field = string.Empty;
            var model = GetModel(id);
            if (model == null)
            {
                return false;
            }
            switch (type)
            {
                case "comment":
                    field = "CommentCount";
                    model.CommentCount += raiseCount;
                    break;
                default:
                    break;
            }

            if (string.IsNullOrEmpty(field))
            {
                return false;
            }
            return Update(model, field);
        }

        /// <summary>
        /// 操作日志更新
        /// </summary>
        /// <param name="page"></param>
        /// <param name="action"></param>
        /// <param name="description"></param>
        /// <param name="userName"></param>
        public void updateLog(string page, string action, string description, Member user, string project = "")
        {
            SysLog log = new SysLog
            {
                Page = page,
                Action = action,
                Name = project,
                Project = project,
                Description = description,
                CreatedBy = user == null ? "" : user.RealName,
                Ip = Utility.WebHelper.GetIP(),
                CreatorId = user == null ? 0 : user.Id,
            };
            logBLL.Add(log);
        }

        /// <summary>
        /// 计算平均分
        /// </summary>
        /// <returns></returns>
        public bool UpdateScoreAvg(int projectId)
        {
            Project project = GetModelByCache(projectId);
            if (project == null)
                return false;

            ProjectScore model = new ScoreBLL().GetModelBySql($"SELECT COUNT(*) AS UserId, round(AVG(score), 1) AS Score FROM ProjectScore WHERE projectId = {projectId}");
            if (model == null)
                return false;

            project.AvgScore = model.Score;
            project.CountScore = model.UserId;
            return Update(project, "AvgScore,CountScore");
        }

        public bool UpdateQuestion(int id, string ai_reasoning, string ai_question)
        {
            Project project = GetModelByCache(id);
            if (project == null)
                return false;

            project.ai_reasoning = ai_reasoning;
            project.ai_question = ai_question;
            project.ai_question_date = DateTime.Now;
            return Update(project, "ai_reasoning, ai_question, ai_question_date");
        }
        private string get(Dictionary<string, string> dic, string key, string defaultVal = "")
        {
            string data;
            if (dic.TryGetValue(key, out data))
            {
                return data;
            }
            return defaultVal;
        }
        public AjaxResult showError(string msg)
        {
            ajaxResult.msg = msg;
            ajaxResult.code = (int)ResultCode.exception;
            return ajaxResult;
        }

        public AjaxResult Import()
        {
            Member user = MemberBLL.GetLogOnUser();
            try
            {
                var request = System.Web.HttpContext.Current.Request;
                var server = System.Web.HttpContext.Current.Server;

                //文件保存目录路径
                String savePath = "/content/attached/";

                //文件保存目录URL
                // String hostUrl = System.Configuration.ConfigurationManager.AppSettings["FileDomain"].ToString() ?? "";
                String saveUrl = "/content/attached/";

                //定义允许上传的文件扩展名
                Hashtable extTable = new Hashtable();
                extTable.Add("projectimport", "doc,docx");// "doc,docx,xls,xlsx,ppt,pptx,pdf");

                //最大文件大小
                int maxSize = 102400000;

                HttpPostedFile imgFile = request.Files["imgFile"];
                if (imgFile == null)
                {
                    imgFile = request.Files["file"];
                }
                if (imgFile == null)
                {
                    return showError("请选择文件。");
                }

                String dirPath = server.MapPath(savePath);
                if (!Directory.Exists(dirPath))
                {
                    return showError("上传目录不存在。");
                }

                String dirName = request.QueryString["dir"];

                String fileName = imgFile.FileName;

                String fileExt = Path.GetExtension(fileName).ToLower();

                if (imgFile.InputStream == null || imgFile.InputStream.Length > maxSize)
                {
                    return showError("上传文件大小超过限制。");
                }

                if (String.IsNullOrEmpty(fileExt) || Array.IndexOf(((String)extTable[dirName]).Split(','), fileExt.Substring(1).ToLower()) == -1)
                {
                    return showError("上传文件扩展名是不允许的扩展名，\n只允许" + ((String)extTable[dirName]) + "格式。");
                }
                //创建文件夹
                dirPath += dirName + "/";
                saveUrl += dirName + "/";
                if (!Directory.Exists(dirPath))
                {
                    Directory.CreateDirectory(dirPath);
                }
                String ymd = DateTime.Now.ToString("yyyyMMdd", DateTimeFormatInfo.InvariantInfo);
                dirPath += ymd + "/";
                saveUrl += ymd + "/";
                if (!Directory.Exists(dirPath))
                {
                    Directory.CreateDirectory(dirPath);
                }

                String newFileName = user.RealName + "_" + fileName + "_" + DateTime.Now.ToString("yyyyMMddHHmmss_ffff", DateTimeFormatInfo.InvariantInfo) + fileExt;
                String filePath = dirPath + newFileName;

                imgFile.SaveAs(filePath);
                Logger.Info("convert project " + filePath, user.RealName);

                WordDocumentTable wordDoc = new WordDocumentTable();
                var dic = wordDoc.ExtractTable(filePath);

                var p = new Project();
                p.Name = get(dic, "项目名称");
                var group = get(dic, "项目组");
                switch (group)
                {
                    case "消费组": p.ToRoleId = 1; break;
                    case "技术组": p.ToRoleId = 2; break;
                    case "互联网组": p.ToRoleId = 3; break;
                    case "医疗组": p.ToRoleId = 5; break;
                    case "参谋部": p.ToRoleId = 6; break;
                    case "产业组": p.ToRoleId = 7; break;
                    default: break;
                }
                p.ProjectManager = get(dic, "项目负责人");
                p.groupMember = get(dic, "其他项目组员");
                p.nextStepStatus = get(dic, "项目状态");
                p.InteralPTCP = get(dic, "高榕参会人");
                p.Participant = get(dic, "外部参会人");
                p.DDManager = get(dic, "DD负责人");
                p.foundedYear = get(dic, "公司成立年份");
                p.city = get(dic, "项目所在地");
                var source = get(dic, "项目来源");
                switch (source)
                {
                    case "项目负责人人脉": p.Source = "0"; break;
                    case "项目负责人研究": p.Source = "4"; break;
                    case "同事提及": p.Source = "1"; break;
                    case "同事介绍": p.Source = "2"; break;
                    case "FA介绍": p.Source = "3"; break;
                    default: break;
                }
                p.HeadCount = get(dic, "员工人数");
                p.TotalAsset = get(dic, "总资产规模");
                p.Summary = get(dic, "项目简介");
                p.Founder = get(dic, "创始人姓名");
                p.Background = get(dic, "团队背景");
                p.BusinessData = get(dic, "业务数据");
                p.FinancialData = get(dic, "财务数据");
                p.ShareStructure = get(dic, "股权结构");
                p.InvestHistory = get(dic, "历史及当前融资方案");
                p.CompareProduct = get(dic, "提到的同行/供应商");
                p.HighLight = get(dic, "项目亮点");
                p.Risk = get(dic, "项目风险");
                p.Currency = get(dic, "融资币种(人民币/美元)");
                p.NextStep = get(dic, "数据包/后续工作");
                p.UpdatedNews = get(dic, "新发现");
                p.PubTimeStr = get(dic, "日期", DateTime.Now.ToString("yyyy-MM-dd"));
                ajaxResult.data = p;
                return ajaxResult;
            }
            catch (Exception e)
            {
                ajaxResult.code = -1;
                ajaxResult.msg = "导入失败 " + e.Message;
                Logger.Error(e.Message, user.RealName);
                return ajaxResult;
            }
        }
    }
}
