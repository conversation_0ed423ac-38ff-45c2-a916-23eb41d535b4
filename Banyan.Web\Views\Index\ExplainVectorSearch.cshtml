﻿
@{
    Layout = null;
    Banyan.Domain.Member member = ViewData["manager"] as Banyan.Domain.Member;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="divport" content="width=device-width" />
    <title>向量语义搜索</title>
    <link type="text/css" href="/content/js/plugins/layui/css/layui.css" rel="stylesheet" />
    <link href="~/content/css/preview.css?v=@(DateTime.Now.Ticks)" rel="stylesheet" />
</head>
<body>
    <div id="app">
        <pre style="font-size: 15px;overflow-y: scroll; height: 800px;padding: 10px">
    词向量：Word Embedding是一种将单词转换为向量表示的技术。它使用了一个称为Transformer的深度学习模型来学习单词之间的关系，从而将每个单词映射到一个高维向量空间中的位置。这个向量表示可以用来计算单词之间的相似度，以及在其他自然语言处理任务中使用。 例如，我们可以将单词“dog”、“cat”等转换为向量表示如下：

    dog: [0.12, 0.34, -0.56, ...]
    cat: [-0.23, 0.67, 0.45, ...]

    这样，我们就可以计算它们之间的相似度。相似的单词会得到相近的向量表示，例如下图中，根据性别和地位的不同，单词的相似度也不同：
                                                           <img style="width: 400px" src="https://common.blob.core.chinacloudapi.cn/avatars/wordembedding-mini.png" />
    句子向量：接下来看一下Sentence Transformer向量语义搜索的原理。Sentence Transformer是一种将句子转换为向量表示的技术，它使用了一个深度学习模型来学习句子之间的关系，从而将每个句子映射到一个高维向量空间中的位置。这个向量表示可以用来计算句子之间的相似度，以及在其他自然语言处理任务中使用。例如，我们可以使用Sentence Transformer将句子“今天天气真好”、“明天会下雨”等转换为向量表示如下：

    今天天气真好: [0.56, -0.34, 0.23, ...]
    明天会下雨: [-0.23, 0.67, 0.45, ...]

    这样，我们就可以计算它们之间的相似度，例如计算“今天天气真好”和“明天会下雨”的余弦相似度。

    在向量搜索中，我们可以使用这些向量来搜索与给定查询语句向量最相似的向量。例如想查询“创始人来自北京”，会先转换为向量表示，然后在所有句子的向量表示中搜索与之最相似的向量，根据相似度返回结果。
         </pre>
    </div>
</body>
</html>
