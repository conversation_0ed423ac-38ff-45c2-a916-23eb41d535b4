﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using Org.BouncyCastle.Crypto.Parameters;
//using Org.BouncyCastle.Crypto.Generators;
using Org.BouncyCastle.Security;

namespace Banyan.Code
{
 
   public class RSAEncrypt
    {
        // test at https://www.bejson.com/enc/rsa/
        public static string RSAPublicKeyJava2DotNet(string publicKey)
        {
            RsaKeyParameters publicKeyParam = (RsaKeyParameters)PublicKeyFactory.CreateKey(Convert.FromBase64String(publicKey));
            return string.Format("<RSAKeyValue><Modulus>{0}</Modulus><Exponent>{1}</Exponent></RSAKeyValue>",
               Convert.ToBase64String(publicKeyParam.Modulus.ToByteArrayUnsigned()),
               Convert.ToBase64String(publicKeyParam.Exponent.ToByteArrayUnsigned()));
       }
        public static string RSAPrivateKeyJava2DotNet(string privateKey)
        {
            RsaPrivateCrtKeyParameters privateKeyParam = (RsaPrivateCrtKeyParameters)PrivateKeyFactory.CreateKey(Convert.FromBase64String(privateKey));

            return string.Format("<RSAKeyValue><Modulus>{0}</Modulus><Exponent>{1}</Exponent><P>{2}</P><Q>{3}</Q><DP>{4}</DP><DQ>{5}</DQ><InverseQ>{6}</InverseQ><D>{7}</D></RSAKeyValue>",
                Convert.ToBase64String(privateKeyParam.Modulus.ToByteArrayUnsigned()),
                Convert.ToBase64String(privateKeyParam.PublicExponent.ToByteArrayUnsigned()),
                Convert.ToBase64String(privateKeyParam.P.ToByteArrayUnsigned()),
                Convert.ToBase64String(privateKeyParam.Q.ToByteArrayUnsigned()),
                Convert.ToBase64String(privateKeyParam.DP.ToByteArrayUnsigned()),
                Convert.ToBase64String(privateKeyParam.DQ.ToByteArrayUnsigned()),
                Convert.ToBase64String(privateKeyParam.QInv.ToByteArrayUnsigned()),
                Convert.ToBase64String(privateKeyParam.Exponent.ToByteArrayUnsigned()));
        }
        public static string Encrypt(string content, string publickey = null)
        {
            if (publickey == null)
            {
                publickey = @"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDhiTd+gCZjqzcsbmCHdE6jzrr1
bt4msOl83TSvZsgJx7bobm/5dceejVFERNkN13Hdtv/fHfpcfaAxX+ACjv+O+pys
FB7HVbKSH6x0deWbQUb1PautWEGitZ5hdK5t26VCq3Td7BAEMhqkzgV8YvXUoMaO
HW3iGUKGaTYqCdNC0wIDAQAB";
            }
            RSACryptoServiceProvider rsa = new RSACryptoServiceProvider();
            byte[] cipherbytes;
            rsa.FromXmlString(RSAPublicKeyJava2DotNet(publickey));
            cipherbytes = rsa.Encrypt(Encoding.UTF8.GetBytes(content), false);

            return Convert.ToBase64String(cipherbytes);
        }

        /// <summary>
        /// RSA解密
        /// </summary>
        /// <param name="privatekey"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        public static string Decrypt(string content, string privatekey = null)
        {
            if (privatekey == null)
            {
                privatekey = @"MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAOGJN36AJmOrNyxu
YId0TqPOuvVu3iaw6XzdNK9myAnHtuhub/l1x56NUURE2Q3Xcd22/98d+lx9oDFf
4AKO/476nKwUHsdVspIfrHR15ZtBRvU9q61YQaK1nmF0rm3bpUKrdN3sEAQyGqTO
BXxi9dSgxo4dbeIZQoZpNioJ00LTAgMBAAECgYEA2iokvz5xZCpEFbuzrnPIelZI
rY+QXzb0tS85fo5a5HMHMLFbqaDP90RWH0bF+8izQbh6rLZP71YqNni6tV7kPvZ4
P36TSQxU2yV2K8b6+7OkjAIUgsZ+9OD3nM2JZklB98EK0Mwfcf/oGrtyA05ClhG2
rmZGjqCg2b16qBnEt1kCQQD4Ulobg2I+i6DHCOX5jRITOckwGPaNQ2eVPecdulLD
hosOsJBz14YAOiBQBIzZYlPSKHrW+Rx5cfrQNJnldAB9AkEA6IJ/2XpFcd/FpdwO
yCvtbM284jR+9xU8I+65NZn4caxsvZUui6fJ3mhVu2KKErqo9Rs3w6r/OFF/tDSk
w2iBjwJBAMBO9p2MGE+bzr2VdJTY3YCqJdbr3jT0WInJ0OzC2Um3LoYH0zcDWDFg
0plljLblYUjGNKu7bIPY84a+1dhPB/kCQEVgYwueW0hOL+h8kACUcuSrQvfwGA5b
zLUgDy16QtKQU2YCNIBBUY/GbAZcsbxC7BUuyOXeiQbh8ovvxVX1QesCQHa8+MSE
TZbWmTFz4+IfwMtGV+tfeFa0aOkemk88hApX401+PKKTjyFPpc+Xq1z708pcrkA8
F5djNu4aMvhVKzo=";
            }
            RSACryptoServiceProvider rsa = new RSACryptoServiceProvider();
            byte[] cipherbytes;
            rsa.FromXmlString(RSAPrivateKeyJava2DotNet(privatekey));
            cipherbytes = rsa.Decrypt(Convert.FromBase64String(content), false);

            return Encoding.UTF8.GetString(cipherbytes);
        }
    }
}
