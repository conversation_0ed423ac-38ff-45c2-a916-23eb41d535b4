﻿@using Banyan.Domain
@{
    ViewBag.Name = "投资团队报表";
    Layout = "/Views/Shared/_LayoutAdmin.cshtml";
    var memberlist = (List<Banyan.Domain.Member>)ViewData["memberlist"];
    var manager = ViewData["manager"] as Banyan.Domain.Member;
}

<style>
    td .layui-table-cell, .layui-table-cell img {
        height: 35px !important;
        line-height: 35px !important;
    }
</style>

<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li>员工报表</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <div class="tab-pane active">
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="pull-right search-bar"></div>
                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                                <div class="form-group">
                                    <select class="form-control" id="keyclass" name="keyclass" size="1">
                                        <option value="0">项目负责人</option>
                                        @if (memberlist != null && memberlist.Count() > 0)
                                        {
                                            foreach (var ci in memberlist)
                                            {
                                                <option value="@(ci.RealName)">@(ci.RealName)</option>
                                            }
                                        }
                                    </select>
                                </div>
                                @*<div class="form-group">
                                    <select class="form-control" id="creator" name="creator" size="1">
                                        <option value="0">创建人</option>
                                    </select>
                                </div>*@
                                @*<div class="form-group">
                                    <label class="sr-only" for="title-name">搜索</label>
                                    <input class="form-control" type="text" id="keyname" name="keyname" placeholder="搜索">
                                </div>
                                <div class="form-group">
                                    <div class="input-daterange input-group">
                                        <input class="form-control" type="text" id="startdate" name="startdate" placeholder="开始日期">
                                        <span class="input-group-addon" style="border-left:none;border-right:none;"><i class="fa fa-chevron-right"></i></span>
                                        <input class="form-control" type="text" id="enddate" name="enddate" placeholder="结束日期">
                                    </div>
                                </div>*@
                                <input type="hidden" id="OpenId" name="OpenId" value="@(manager.OpenId)">
                                <div class="form-group">
                                    <a class="btn btn-default" id="dosearch">搜索</a>
                                    @*@if (manager.Levels == (byte)Banyan.Domain.MemberLevels.Administrator || manager.Levels == (byte)Banyan.Domain.MemberLevels.SuperUser)
                                    {
                                        <a class="btn btn-minw btn-warning" id="doexport">导出Excel</a>
                                    }*@
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <iframe src="" name="main_self_frame" frameborder="0" class="layadmin-iframe"></iframe>
                <div  id="staffperformance" ></div>
                <table class="layui-hide" id="staffperformance-list" lay-filter="list-filter"></table>
                <table class="layui-hide" id="table-list" lay-filter="list-filter"></table>
                
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="bartpl">
    <div class="btn-group" role="group" data-toggle="buttons">
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="预览" lay-event="preview" data-original-title="预览"><i class="fa fa-eye"></i></button>
    </div>
</script>
<script type="text/html" id="titleTpl">
    <button type="button"  lay-event="preview" class="layui-btn layui-btn-primary" style="width:100%">{{d.RealName}}</button>
</script>
@section scripts{
    <script type="text/javascript">
        var queryParams = {}, date = new Date();
        layui.use(['laypage', 'layer', 'table', 'laydate'], function () {
            var laypage = layui.laypage,
                layer = layui.layer,
                laydate = layui.laydate,
                table = layui.table;

            table.render({
                elem: '#table-list'
                , height: 570
                , url: '@(Url.Action("GetCreatorsSummary", "adminapi"))'
                , page: true
                , method: 'post'
                , cols: [[
                    //{field:'RealName', title: '编辑人', width: 70, templet: '#titleTpl'} //这里的templet值是模板元素的选择器
                    { field: 'RealName', title: '编辑人', fixed: 'left', width: 70 }
                    , { field: 'EditorCount', title: '创建数', width: 70}
                    , { field: 'ProjectManagerCount', title: '负责项目数', width: 100 }
                    , { field: 'groupMemberCount', title: '项目成员数', width: 100 }
                    , { field: 'PTCPCount', title: '参会数', width: 70 }
                    , { field: 'IntroducerCount', title: '介绍数', width: 70 }

                    , { field: 'DDManagerCount', title: 'DD负责数', width: 90 }
                    , { field: 'FugaiCount', title: '主动覆盖数', width: 100 }
                    , { field: 'FUGAIP', title: '主动覆盖%', width: 105 }
                    , { field: 'TongshitijiCount', title: '同事提及数', width: 100 }
                    , { field: 'TongshijieshaoCount', title: '同事介绍数', width: 100 }
                    , { field: 'FaCount', title: 'FA推荐数', width: 90 }
                    , { field: 'FAP', title: 'FA推荐%', width: 90 }
                    , { field: 'DDCount', title: 'DD阶段数', width: 90 }
                     , { field: 'DDP', title: 'DD阶段%', width: 90 }
                    , { field: 'TSCount', title: 'TS阶段数', width: 90 }
                    , { field: 'PartnerCount', title: '合伙人见面数', width: 110 }
                    , { field: 'TeamDiscussCount', title: '小组讨论数', width: 100 }

                , { fixed: 'right', title: '业绩', width: 60, align: 'center', toolbar: '#bartpl' }
                ]],
                done: function () { }
            });

            table.on('tool(list-filter)', function (obj) {
            var data = obj.data
                , layEvent = obj.event;

            if (layEvent === 'preview') {

                preview(data.RealName);
            }
            return;
        });

        laypage.render({
            elem: 'pageBar'
            , count: 100
            , jump: function (obj, first) {
                if (!first) {
                    layer.msg('第' + obj.curr + '页');
                }
            }
        });
        layui.use(['laypage', 'layer', 'table', 'laydate'], function () {
            var laypage = layui.laypage,
                layer = layui.layer,
                laydate = layui.laydate,
                table = layui.table;
            var querystr = 'http://fms.banyanvc.com/staffperformance.ashx?PMName=' + RealName + '&startDate=' + $('#startdate').val() + '&endDate=' + $('#enddate').val() + '&OpenId=' + $('#OpenId').val() + '&type=table';
            table.render({
                elem: '#staffperformance-list'
                , height: 570
                , url: querystr
                , page: true
                , method: 'get'
                , cols: [[
                    { field: 'projectManager', title: '项目负责人', fixed: 'left', width: 70 }
                    , { field: 'abbName', title: 'abbName', width: 70}
                    , { field: 'cost', title: 'cost', width: 100 }
                    , { field: 'loan', title: 'loan', width: 100 }
                    , { field: 'totalCost', title: 'totalCost', width: 70 }
                    , { field: 'Realized', title: 'Realized', width: 70 }

                    , { field: 'UnRealized', title: 'UnRealized', width: 90 }
                    , { field: 'totalValue', title: 'totalValue', width: 100 }
                    , { field: 'Multiples', title: 'Multiples', width: 105 }
                    , { field: 'Mark-up', title: 'Mark-up', width: 100 }
                    , { field: 'IRR', title: 'IRR', width: 100 }
                    , { field: 'Note', title: 'Note', width: 90 }
                , { fixed: 'right', title: '业绩', width: 60, align: 'center', toolbar: '#bartpl' }
                ]],
                done: function () { }
            });

            table.on('tool(list-filter)', function (obj) {
                var data = obj.data
                    , layEvent = obj.event;

                if (layEvent === 'preview') {

                    preview(data.RealName);
                }
                return;
            });

            laypage.render({
                elem: 'pageBar'
                , count: 100
                , jump: function (obj, first) {
                    if (!first) {
                        layer.msg('第' + obj.curr + '页');
                    }
                }
            });
        //laydate.render({
        //    elem: '#startdate'
        //});

        //laydate.render({
        //    elem: '#enddate'
        //});
        //$('#keyname').on('keypress', function(event) {
        //    if (event.keyCode === 13) {
        //        $('#dosearch').trigger('click');
        //    }
        //});
        $('#dosearch').on('click', function () {
            var PMName = $('#keyclass').val();
            queryParams = {
                //ToRoleId: $('#keyclass').val(),
                Name: PMName,
                //startdate: $('#startdate').val(),
                //enddate: $('#enddate').val(),
            }
            table.reload('table-list', {
                where: queryParams,
            });
            //Ajax获取
            $.post('http://fms.banyanvc.com/staffperformance.ashx?PMName=' + PMName, {}, function (str) {
                //alert(str);
                divshow.text("");// 清空数据
                $('#staffperformance').append(str);
            });
        });

            //$('#doexport').on('click', function () {
            //    var querystr = 'ToRoleId=' + $('#keyclass').val() + '&Name=' + $('#keyname').val() + '&startdate=' + $('#startdate').val() + '&enddate=' + $('#enddate').val();
            //    if ($('#downloadcsv').length <= 0)
            //        $('body').append("<iframe id=\"downloadcsv\" style=\"display:none\"></iframe>");
            //    $('#downloadcsv').attr('src', "/adminapi/exportprojects?" + encodeURI(querystr.trim('&')));
            //});
        });

        function preview(RealName) {
            console.log(RealName);
            //Ajax获取
            //$.post('http://fms.banyanvc.com/staffperformance.ashx?PMName=' + RealName, {}, function (str) {
            //    alert(str);
            //    layer.open({
            //        type: 1,
            //        content: str,
            //    });
            //});
            var querystr = 'http://fms.banyanvc.com/staffperformance.ashx?PMName=' + RealName + '&startDate=' + $('#startdate').val() + '&endDate=' + $('#enddate').val() + '&OpenId=' + $('#OpenId').val() + '&type=col';
            layer.open({
                type: 2,
                area: ['1140px', '667px'],
                fix: false,
                maxmin: false,
                anim: 5,
                shade: 0,
                title: "投资业绩-" + RealName,
                content: querystr//这里content是一个URL，如果你不想让iframe出现滚动条，你还可以content: ['http://sentsin.com', 'no']
            });
        }
    </script>
}
