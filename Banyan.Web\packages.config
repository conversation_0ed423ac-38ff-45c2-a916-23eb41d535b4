﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Gelf4Net.Core" version="1.0.0.17" targetFramework="net461" />
  <package id="Gelf4Net.UdpAppender" version="1.0.0.17" targetFramework="net461" />
  <package id="log4net" version="2.0.8" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights" version="2.13.1" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.Agent.Intercept" version="2.4.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.DependencyCollector" version="2.12.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.Log4NetAppender" version="2.13.1" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.PerfCounterCollector" version="2.12.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.Web" version="2.12.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.WindowsServer" version="2.12.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel" version="2.12.0" targetFramework="net461" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.Mvc.zh-Hans" version="5.2.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.Razor" version="3.2.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.Razor.zh-Hans" version="3.2.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.TelemetryCorrelation" version="1.0.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Client.zh-Hans" version="5.2.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Core.zh-Hans" version="5.2.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.WebHost.zh-Hans" version="5.2.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages.zh-Hans" version="3.2.3" targetFramework="net461" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net461" />
  <package id="Newtonsoft.Json" version="9.0.1" targetFramework="net461" />
  <package id="System.Buffers" version="4.4.0" targetFramework="net461" />
  <package id="System.Diagnostics.DiagnosticSource" version="4.6.0" targetFramework="net461" />
  <package id="System.Memory" version="4.5.3" targetFramework="net461" />
  <package id="System.Numerics.Vectors" version="4.4.0" targetFramework="net461" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net461" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net461" />
</packages>