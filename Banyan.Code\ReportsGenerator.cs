﻿using igos_data;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace Banyan.Code
{
    public class ReportsGenerator
    {
        protected static decimal ChinaRound(decimal value, int decimals)
        {
            decimal result = 0;
            if (value < 0)
            {
                decimal t = Math.Round(-value, decimals, MidpointRounding.AwayFromZero);
                result = -t;
            }
            else
            {
                result = Math.Round(value, decimals, MidpointRounding.AwayFromZero);
            }
            return result;
        }
        public static string getPerformanceTable(string username, string PMName, decimal USDtoRMBRate, decimal recommendWeight, decimal PManagerWeight, decimal PIManagerWeight, decimal PMemberWeight, decimal PIMemberWeight, bool combine)
        {
            if(PMName == "张震" || PMName == "高翔" || PMName == "岳斌")
            {
                if(username != "张震" && username != "高翔" && username != "岳斌")
                {
                    return "";
                }
            }
            //if(username == "吕祺")
            //{
            //    return "";
            //}
            Data_basic db = new Data_basic();
            DataTable costValuation = new DataTable();
            DataTable investHistory = new DataTable();
            RGHelper.getData(db, ref costValuation, ref investHistory);
            string html = @"";
            USDtoRMBRate = USDtoRMBRate == 0 ? (decimal)6.5 : USDtoRMBRate;
            if (!string.IsNullOrEmpty(PMName))
            {
                if (combine)
                {
                    html += string.Format(@"<div style=""width:1400px;""><hr><h4 style=""color:black;text-align:center;""><strong>投资业绩汇总-{0}</strong> </h4>", PMName);
                    html += @"<table class=""mini-table"" border=""0"" cellspacing=""0"" style=""width:100%;margin: 0px auto;"" >";
                    string filter = string.Format(@"portfolioManager like '%{0}%' OR portfolioIntroducer like '%{0}%' OR groupMember like '%{0}%' OR postInvestManager like '%{0}%' OR postInvestMember like '%{0}%'", PMName);
                    DataRow[] performanceRows = costValuation.Select(filter, "currency,fundFamillyName,abbName");
                    html += generateCombineReport(performanceRows.CopyToDataTable(), investHistory.Select(filter, "").CopyToDataTable(), PMName, USDtoRMBRate, PManagerWeight, PMemberWeight, recommendWeight, PIManagerWeight, PIMemberWeight);
                    html += @"</table>";
                    html += "</div>";
                }
                else
                {
                    String summaryHtml = "";
                    String tableTr = "";
                    String PM = "";
                    List<String> listHtml = new List<String>();
                    listHtml = generateReport(costValuation, investHistory, "负责项目", USDtoRMBRate, "portfolioManager like '%" + PMName + "%'", PManagerWeight);
                    if (listHtml != null && listHtml.Count() > 1 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                    {
                        PM = listHtml[0];
                        summaryHtml += listHtml[0];
                        tableTr += listHtml[1] + @"<tr><td></td><td colspan=""11"" style=""border-top-style:solid;border-top-width:medium;height:40px;""></td><td></td></tr>";
                    }
                    //decimal totalMarkup = 0;
                    string filter = "portfolioManager not like '%" + PMName + "%'";
                    listHtml = generateReport(costValuation, investHistory, "Sourcing项目", USDtoRMBRate, filter + " and portfolioIntroducer like '%" + PMName + "%'", recommendWeight);
                    if (listHtml != null && listHtml.Count() > 2 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                    {
                        summaryHtml += listHtml[0];
                        tableTr += listHtml[1] + @"<tr><td></td><td colspan=""11"" style=""border-top-style:solid;border-top-width:medium;height:40px;""></td><td></td></tr>";
                        //totalMarkup += string.IsNullOrEmpty(listHtml[0]) ? 0 : decimal.Parse(listHtml[2]);
                    }

                    listHtml = generateReport(costValuation, investHistory, "参与项目", USDtoRMBRate, filter + " and (groupMember like '%" + PMName + "%' OR postInvestManager like '%" + PMName + "%' OR postInvestMember like '%" + PMName + "%')", PMemberWeight);
                    if (listHtml != null && listHtml.Count() > 2 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                    {
                        summaryHtml += listHtml[0];
                        tableTr += listHtml[1] + @"<tr><td></td><td colspan=""11"" style=""border-top-style:solid;border-top-width:medium;height:40px;""></td><td></td></tr>";
                        //totalMarkup += string.IsNullOrEmpty(listHtml[0]) ? 0 : decimal.Parse(listHtml[2]);
                    }

                    html += @"<div style=""width:1140px;margin:auto;"">";
                    if (!string.IsNullOrEmpty(summaryHtml) && !string.IsNullOrEmpty(tableTr))
                    {
                        html += string.Format(@"<hr><h4 style=""color:black;text-align:center;font-size:20px;""><strong>投资业绩汇总-{0}</strong> </h4>", PMName);
                        html += @"<table class=""mini-table"" border=""0"" cellspacing=""0"" style=""width:100%;margin: 0px auto;"" >";
                        html += RGHelper.addHeader(true);
                        html += summaryHtml + @"<tr><td></td><td colspan=""11"" style=""border-top-style:solid;border-top-width:medium;height:40px;""></td><td></td></tr>" + tableTr;
                        html += @"</table>";
                    }
                    html += @"</div>";
                }
            }
            else
            {
                html += @"<div style=""width:1140px""><table class=""mini-table"" border=""0"" cellspacing=""0"" style=""width:100%;margin: 0px auto;"" id=""investmentSummary"">";
                if (db.ExecuteDataset("SELECT DISTINCT lastNameChi+firstNameChi as Name FROM Staff WHERE PM='1' order by Name") && db.AffectedRow > 0)
                {
                    var distinctNames = (from row in db.Dataset.Tables[0].AsEnumerable() select row.Field<string>("Name")).Distinct();
                    foreach (var projectManager in distinctNames)
                    {
                        if (!string.IsNullOrEmpty(projectManager))
                        {
                            List<String> listHtml = new List<String>();

                            listHtml = generateReport(costValuation, investHistory, projectManager + "负责项目", USDtoRMBRate, "portfolioManager like '%" + projectManager + "%'", 1);
                            if (listHtml != null && listHtml.Count() > 1 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                                html += listHtml[1];
                            else
                                html += RGHelper.addHeader(true) + string.Format(@"<tr><td></td><td colspan=""2"" style=""height:20px;font-size:18px;text-align:right;border-bottom-style:solid;border-bottom-width:thin;"">{0} 负责项目汇总（CNY）</td><td style=""font-size:18px;text-align:center;border-bottom-style:solid;border-bottom-width:thin;"">0</td><td colspan=""6""></td></tr>", projectManager);

                            string summaryHtml = "";
                            string filter = "portfolioManager not like '%" + projectManager + "%'";
                            listHtml = generateReport(costValuation, investHistory, projectManager + "Sourcing", USDtoRMBRate, filter + " and portfolioIntroducer like '%" + projectManager + "%'", recommendWeight);
                            if (listHtml != null && listHtml.Count() > 2 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                                summaryHtml += listHtml[0];

                            listHtml = generateReport(costValuation, investHistory, projectManager + "参与项目", USDtoRMBRate, filter + " and (groupMember like '%" + projectManager + "%' OR postInvestManager like '%" + projectManager + "%' OR postInvestMember like '%" + projectManager + "%')", PMemberWeight);
                            if (listHtml != null && listHtml.Count() > 2 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                                summaryHtml += listHtml[0];

                            html += summaryHtml + @"<tr><td colspan=""9"" style=""height:20px;""></td></tr>";

                        }
                    }
                }
                html += @"</table></div>";
            }
            return html;
        }
        protected static decimal generateReportHelper(bool isCombine, DataTable performanceTable, DataTable investHistory, string PMName, decimal USDtoRMBRate, ref string html, ref string summaryHtml,string summary = "", decimal PManagerWeight = 0, decimal PMemberWeight = 0, decimal recommendWeight = 0, decimal PIManagerWeight = 0, decimal PIMemberWeight = 0)
        {
            int totalNum = 0;
            decimal totalInvestmentCost = 0;
            decimal totalInvestmentLoan = 0;
            decimal totalInvestmentTotalCost = 0;
            decimal totalProceeds = 0;
            //decimal totalCostRealization = 0;
            decimal totalValuation = 0;
            decimal totalLoanRealization = 0;
            decimal YYUnRelized = 0;
            double IRR = 0;
            decimal totalWeightMarkUp = 0;
            var currencys = (from row in performanceTable.AsEnumerable()
                             select row.Field<string>("currency")).Distinct();
            string Names = "";
            foreach (var currency in currencys)
            {
                string filter = @" currency = '" + currency + "'";
                if (summary != "")
                {
                    filter += " and " + summary;
                }
                DataRow[] performanceRows = performanceTable.Select(filter, "currency,fundFamillyName,abbName");
                if (performanceRows != null && performanceRows.Count() > 0 && totalNum == 0) 
                    html += isCombine ? RGHelper.addCombineHeader(): RGHelper.addHeader(false);
                int i = 0;
                string rowNum = "";
                decimal totalInvestmentCostTemp = 0;
                decimal totalInvestmentLoanTemp = 0;
                decimal totalInvestmentTotalCostTemp = 0;
                decimal totalProceedsTemp = 0;
                decimal totalLoanRealizationTemp = 0;
                decimal totalValuationTemp = 0;
                decimal totalWeightMarkUpTemp = 0;
                foreach (DataRow dr in performanceRows)
                {
                    if (!Names.Contains(dr["abbName"].ToString()))
                    {
                        i++;
                        Names += dr["abbName"].ToString() + ",";
                        rowNum = i.ToString();
                    }
                    else
                        rowNum = "";

                    decimal spvOwnership = 1;
                    decimal cost = ChinaRound(decimal.Parse(dr["cost"].ToString()) * spvOwnership, 0);
                    decimal totalCost = ChinaRound(decimal.Parse(dr["totalCost"].ToString()) * spvOwnership, 0);
                    decimal proceeds = ChinaRound(decimal.Parse(dr["proceeds"].ToString()) * spvOwnership, 0);
                    decimal costRelization = ChinaRound(decimal.Parse(dr["costRelization"].ToString()) * spvOwnership, 0);
                    decimal carryingCost = ChinaRound(decimal.Parse(dr["carryingCost"].ToString()) * spvOwnership, 0);
                    decimal Valuation = 0;
                    decimal loan = ChinaRound(decimal.Parse(dr["loan"].ToString()) * spvOwnership, 0);
                    decimal loanProceeds = ChinaRound(decimal.Parse(dr["loanProceeds"].ToString()) * spvOwnership, 0);
                    decimal loanRelization = ChinaRound(decimal.Parse(dr["loanRelization"].ToString()) * spvOwnership, 0);
                    #region valuation compute
                    decimal LatestBanyanTotalValuation = 0;
                    decimal banyanSumSharesNo = Convert.ToDecimal(dr["shareOwnedNo"].ToString());
                    if (!(cost <= 0 && banyanSumSharesNo == 0) && dr["portfolioID"] != null && !string.IsNullOrEmpty(dr["portfolioID"].ToString()))
                    {
                        DataTable dt = new DataTable();
                        dt = RGHelper.getShareStructureTable(dr["portfolioID"].ToString());

                        if (dt != null && dt.Rows.Count > 0 && !(loan > 0 && banyanSumSharesNo == 0))//ignore no equity
                            LatestBanyanTotalValuation = Xirr.Program.ValuationCompute(dt, dr["fundFamillyName"].ToString(), USDtoRMBRate, cost, currency, dr) * spvOwnership;
                    }
                    //else
                    //    Valuation = ChinaRound(decimal.Parse(dr["valuation"].ToString()), 0);
                    if (Valuation <= 0 || (Valuation <= totalCost && LatestBanyanTotalValuation > 0 && Math.Abs(LatestBanyanTotalValuation - Valuation) > 10000) && LatestBanyanTotalValuation > 10000)
                    {
                        Valuation = LatestBanyanTotalValuation;
                    }
                    if (carryingCost <= 0 && LatestBanyanTotalValuation == 0)
                    {
                        Valuation = 0;
                        carryingCost = 0;
                    }
                    if (Math.Abs(carryingCost - Valuation) < 100)
                    {
                        Valuation = carryingCost;
                    }
                    #endregion
                    //if (!string.IsNullOrEmpty(dr["valuation"].ToString()))
                    //    decimal.TryParse(dr["valuation"].ToString(), out Valuation);
                    decimal loanValuation = loan;
                    if (!string.IsNullOrEmpty(dr["loanValuation"].ToString()))
                        decimal.TryParse(dr["loanValuation"].ToString(), out loanValuation);
                 
                    decimal Realized = proceeds + loanProceeds;
                    decimal UnRealized = Valuation + loanValuation;
                    if (!isCombine)
                    {
                        if (dr["abbName"].ToString() == "慧科" && currency == "CNY")
                        {
                            totalCost -= loanRelization;
                            Realized -= loanRelization;
                        }
                        if (dr["abbName"].ToString() == "YY" && currency == "USD")
                        {
                            YYUnRelized = UnRealized;
                            continue;
                        }
                        if (dr["abbName"].ToString() == "微会（BIGO）" && currency == "USD")
                        {
                            UnRealized += YYUnRelized;
                        }
                    }
                    //if (!string.IsNullOrEmpty(dr["IRR"].ToString()))
                    //    IRR = ChinaRound(decimal.Parse(dr["IRR"].ToString()), 0);
                    string portfolioFilter = filter;
                    if (!string.IsNullOrEmpty(dr["portfolioID"].ToString()))
                        portfolioFilter += " and portfolioID = '" + dr["portfolioID"].ToString() + "'";
                    if (!string.IsNullOrEmpty(dr["fundFamillyName"].ToString()))
                        portfolioFilter += " and fundFamillyName = '" + dr["fundFamillyName"].ToString() + "'";
                    if (investHistory.Select(portfolioFilter, "").Length > 0)
                        IRR = 100 * Xirr.Program.IRRCal(investHistory.Select(portfolioFilter, ""), DateTime.Now, double.Parse((UnRealized).ToString()));

                    decimal weight = 0;
                    if (isCombine)
                    {
                        weight = WeightCal(dr, PMName, PManagerWeight, PMemberWeight, recommendWeight, PIManagerWeight, PIMemberWeight);
                        html += RGHelper.fmtRowCombine(rowNum, dr, totalCost, proceeds, loanProceeds, totalProceeds, Valuation, loanValuation, IRR, weight);

                    } else
                    {
                        html += RGHelper.fmtRow(rowNum, currency, dr, totalCost, Realized, UnRealized, IRR);
                    }

                    totalInvestmentCost += cost + costRelization;
                    //totalInvestmentLoan += loan;
                    totalLoanRealization += loanRelization;
                    totalInvestmentTotalCost += totalCost;
                    totalProceeds += Realized;
                    totalValuation += UnRealized;
                    totalWeightMarkUp += (Valuation + Realized + loanValuation - totalCost) * weight;
                    if (currency == "USD")
                    {
                        totalInvestmentCost += (cost + costRelization) * (USDtoRMBRate - 1);
                        //totalInvestmentLoan += (loan) * (USDtoRMBRate - 1);
                        totalLoanRealization += loanRelization * (USDtoRMBRate - 1);
                        totalInvestmentTotalCost += totalCost * (USDtoRMBRate - 1);
                        totalProceeds += (Realized) * (USDtoRMBRate - 1);
                        totalValuation += (UnRealized) * (USDtoRMBRate - 1); totalWeightMarkUp += (Valuation + Realized + loanValuation - totalCost) * weight * (USDtoRMBRate - 1);

                    }
                    totalInvestmentCostTemp += cost + costRelization;
                    //totalInvestmentLoanTemp += loan;
                    totalLoanRealizationTemp += loanRelization;
                    totalInvestmentTotalCostTemp += totalCost;
                    totalProceedsTemp += Realized;
                    totalValuationTemp += UnRealized; // totalValuationForContribution += UnRealized / spvOwnership;
                    totalWeightMarkUpTemp += (Valuation + Realized + loanValuation - totalCost) * weight;
                }

                IRR = 100 * Xirr.Program.IRRCal(investHistory.Select(filter, ""), DateTime.Now, double.Parse(totalValuationTemp.ToString())); // totalValuationForContribution
                if (isCombine)
                {
                  html += i == 0 ? "" : RGHelper.fmtRowSumCombine(currency, totalInvestmentCostTemp, totalProceedsTemp, totalValuationTemp, IRR, totalInvestmentCostTemp, totalWeightMarkUpTemp);
                }else
                {
                     html += i == 0 ? "" : RGHelper.fmtRowSum(currency, totalInvestmentTotalCostTemp, totalProceedsTemp, totalValuationTemp, IRR);
                }
                totalNum += i;
            }
            double IRRTotal = 0;
            //RMB end valuation
            IRRTotal = 100 * Xirr.Program.IRRCal(investHistory.Select(summary, ""), DateTime.Now, double.Parse((totalValuation).ToString()), (double)USDtoRMBRate);
            if(isCombine)
            {
                html += totalNum == 0 ? "" : RGHelper.fmtRowSumTotalCombine(totalNum, PMName, totalInvestmentTotalCost, totalProceeds, totalValuation, IRRTotal, totalInvestmentCost, totalWeightMarkUp);
                return 0;
            }
            else
            {
                summaryHtml = totalNum == 0 ? "" : RGHelper.fmtRowSumTotal(totalNum, PMName, totalInvestmentTotalCost, totalProceeds, totalValuation, IRRTotal, totalInvestmentCost);
                return totalProceeds + totalValuation - totalInvestmentTotalCost;
            }
        }

        protected static List<String> generateReport(DataTable performanceTable, DataTable investHistory, string PMName, decimal USDtoRMBRate, string summary, decimal Weight, string username = "")
        {
            List<String> listHtml = new List<String>();
            if (performanceTable == null || performanceTable.Rows.Count == 0 || investHistory == null || investHistory.Rows.Count == 0)
                return listHtml;
            String html = "";
            String summaryHtml = "";
            try
            {
                if (!string.IsNullOrEmpty(summary))
                {

                    var tmp = generateReportHelper(false, performanceTable, investHistory, PMName, USDtoRMBRate, ref html, ref summaryHtml, summary);
                    html += summaryHtml;
                    listHtml.Add(summaryHtml);
                    listHtml.Add(html);
                    listHtml.Add(string.Format("{0:N0}", ChinaRound(tmp * Weight, 0)));
                }
            }
            catch (Exception e)
            {
               Logger.Error(e.Message,e);
            }
            return listHtml;
        }

        protected static string generateCombineReport(DataTable performanceTable, DataTable investHistory, string PMName, decimal USDtoRMBRate, decimal PManagerWeight, decimal PMemberWeight, decimal recommendWeight, decimal PIManagerWeight, decimal PIMemberWeight)
        {
            if (performanceTable == null || performanceTable.Rows.Count == 0 || investHistory == null || investHistory.Rows.Count == 0)
                return "";
            string html = "";
            string summaryHtml = "";
            try
            {
                generateReportHelper(true, performanceTable, investHistory, PMName, USDtoRMBRate, ref html, ref summaryHtml, "", PManagerWeight, PMemberWeight, recommendWeight, PIManagerWeight, PIMemberWeight);
                return html;
            }
            catch (Exception)
            {
                //html += @"</table></div>";
                return html;
            }
        }
        protected static decimal WeightCal(DataRow dr, string PMName, decimal PManagerWeight, decimal PMemberWeight, decimal recommendWeight, decimal PIManagerWeight, decimal PIMemberWeight)
        {
            decimal weight = 0;
            decimal myWeight = 0;
            decimal totalWeight = 0;

            if (!string.IsNullOrEmpty(dr["portfolioManager"].ToString()))
            {
                totalWeight += PManagerWeight;
                if (dr["portfolioManager"].ToString().Contains(PMName))
                {
                    if (dr["portfolioManager"].ToString().Split(',').Count() > 1)
                        myWeight += PManagerWeight / dr["portfolioManager"].ToString().Split(',').Count();
                    else
                        myWeight += PManagerWeight;
                }
            }
            if (!string.IsNullOrEmpty(dr["portfolioIntroducer"].ToString()))
            {
                totalWeight += recommendWeight;
                if (dr["portfolioIntroducer"].ToString().Contains(PMName))
                {
                    if (dr["portfolioIntroducer"].ToString().Split(',').Count() > 1)
                        myWeight += recommendWeight / dr["portfolioIntroducer"].ToString().Split(',').Count();
                    else
                        myWeight += recommendWeight;
                }
            }
            if (!string.IsNullOrEmpty(dr["groupMember"].ToString()))
            {
                totalWeight += PMemberWeight;
                if (dr["groupMember"].ToString().Contains(PMName))
                {
                    if (dr["groupMember"].ToString().Split(',').Count() > 1)
                        myWeight += PMemberWeight / dr["groupMember"].ToString().Split(',').Count();
                    else
                        myWeight += PMemberWeight;
                }
            }
            if (!string.IsNullOrEmpty(dr["postInvestManager"].ToString()))
            {
                totalWeight += PIManagerWeight;
                if (dr["postInvestManager"].ToString().Contains(PMName))
                {
                    if (dr["postInvestManager"].ToString().Split(',').Count() > 1)
                        myWeight += PIManagerWeight / dr["postInvestManager"].ToString().Split(',').Count();
                    else
                        myWeight += PIManagerWeight;
                }
            }
            if (!string.IsNullOrEmpty(dr["postInvestMember"].ToString()))
            {
                totalWeight += PIMemberWeight;
                if (dr["postInvestMember"].ToString().Contains(PMName))
                {
                    if (dr["postInvestMember"].ToString().Split(',').Count() > 1)
                        myWeight += PIMemberWeight / dr["postInvestMember"].ToString().Split(',').Count();
                    else
                        myWeight += PIMemberWeight;
                }
            }
            if (totalWeight != 0)
                weight = myWeight / totalWeight;
            return weight;
        }
    }
}
