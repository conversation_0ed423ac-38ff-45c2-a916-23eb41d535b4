﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!-- 
    'Grpc_SkipNativeLibsCopy' should not be enabled in normal use.

    It only exists to support special scenarios where user wants to copy the native libraries
    to output directory themselves, in a separate build step or script.

    Only use this flag if you really know what you're doing. It's your responsibility to ensure that matching versions of 
    the Grpc.Core.dll assembly and the native libraries are used at all times.
  -->
  <ItemGroup Condition="'$(Grpc_SkipNativeLibsCopy)' != 'true'">
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\win-x86\native\grpc_csharp_ext.x86.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Link>grpc_csharp_ext.x86.dll</Link>
      <Visible>false</Visible>
      <NuGetPackageId>Grpc.Core</NuGetPackageId>
    </Content>
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\win-x64\native\grpc_csharp_ext.x64.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Link>grpc_csharp_ext.x64.dll</Link>
      <Visible>false</Visible>
      <NuGetPackageId>Grpc.Core</NuGetPackageId>
    </Content>
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-x64\native\libgrpc_csharp_ext.x64.so">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Link>libgrpc_csharp_ext.x64.so</Link>
      <Visible>false</Visible>
      <NuGetPackageId>Grpc.Core</NuGetPackageId>
    </Content>
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\osx-x64\native\libgrpc_csharp_ext.x64.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Link>libgrpc_csharp_ext.x64.dylib</Link>
      <Visible>false</Visible>
      <NuGetPackageId>Grpc.Core</NuGetPackageId>
    </Content>
  </ItemGroup>
</Project>
