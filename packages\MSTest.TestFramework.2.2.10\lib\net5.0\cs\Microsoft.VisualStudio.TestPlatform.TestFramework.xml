<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod">
            <summary>
            Atribut TestMethod pro provádění
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestMethodName">
            <summary>
            Získá název testovací metody.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestClassName">
            <summary>
            Získá název třídy testu.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ReturnType">
            <summary>
            Získá návratový typ testovací metody.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ParameterTypes">
            <summary>
            <PERSON><PERSON><PERSON><PERSON> parametry testovací metody.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.MethodInfo">
            <summary>
            Získá methodInfo pro testovací metodu.
            </summary>
            <remarks>
            This is just to retrieve additional information about the method.
            Do not directly invoke the method using MethodInfo. Use ITestMethod.Invoke instead.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.Invoke(System.Object[])">
            <summary>
            Vyvolá testovací metodu.
            </summary>
            <param name="arguments">
            Argumenty pro testovací metodu (např. pro testování řízené daty)
            </param>
            <returns>
            Výsledek vyvolání testovací metody
            </returns>
            <remarks>
            This call handles asynchronous test methods as well.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAllAttributes(System.Boolean)">
            <summary>
            Získá všechny atributy testovací metody.
            </summary>
            <param name="inherit">
            Jestli je platný atribut definovaný v nadřazené třídě
            </param>
            <returns>
            Všechny atributy
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAttributes``1(System.Boolean)">
            <summary>
            Získá atribut konkrétního typu.
            </summary>
            <typeparam name="AttributeType"> System.Attribute type. </typeparam>
            <param name="inherit">
            Jestli je platný atribut definovaný v nadřazené třídě
            </param>
            <returns>
            Atributy zadaného typu
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Helper">
            <summary>
            Pomocná služba
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNull(System.Object,System.String,System.String)">
            <summary>
            Kontrolní parametr není null.
            </summary>
            <param name="param">
            Parametr
            </param>
            <param name="parameterName">
            Název parametru
            </param>
            <param name="message">
            Zpráva
            </param>
            <exception cref="T:System.ArgumentNullException"> Throws argument null exception when parameter is null. </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNullOrEmpty(System.String,System.String,System.String)">
            <summary>
            Ověřovací parametr není null nebo prázdný.
            </summary>
            <param name="param">
            Parametr
            </param>
            <param name="parameterName">
            Název parametru
            </param>
            <param name="message">
            Zpráva
            </param>
            <exception cref="T:System.ArgumentException"> Throws ArgumentException when parameter is null. </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod">
            <summary>
            Výčet způsobů přístupu k datovým řádkům při testování řízeném daty
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Sequential">
            <summary>
            Řádky se vrací v sekvenčním pořadí.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random">
            <summary>
            Řádky se vrátí v náhodném pořadí.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute">
            <summary>
            Atribut pro definování vložených dat pro testovací metodu
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/>.
            </summary>
            <param name="data1"> Datový objekt </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object,System.Object[])">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/>, která přijímá pole argumentů.
            </summary>
            <param name="data1"> Datový objekt </param>
            <param name="moreData"> Další data </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.Data">
            <summary>
            Získá data pro volání testovací metody.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.DisplayName">
            <summary>
            Získá nebo nastaví zobrazovaný název ve výsledcích testu pro přizpůsobení.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            <summary>
            Výjimka s neprůkazným kontrolním výrazem
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String,System.Exception)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
            <param name="msg"> Zpráva </param>
            <param name="ex"> Výjimka </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
            <param name="msg"> Zpráva </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException">
            <summary>
            Třída InternalTestFailureException. Používá se pro označení interní chyby testovacího případu.
            </summary>
            <remarks>
            This class is only added to preserve source compatibility with the V1 framework.
            For all practical purposes either use AssertFailedException/AssertInconclusiveException.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String,System.Exception)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
            <param name="msg"> Zpráva o výjimce </param>
            <param name="ex"> Výjimka </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
            <param name="msg"> Zpráva o výjimce </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute">
            <summary>
            Atribut, podle kterého se má očekávat výjimka zadaného typu
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> s očekávaným typem.
            </summary>
            <param name="exceptionType">Typ očekávané výjimky</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type,System.String)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/>
            s očekávaným typem a zprávou, která se zahrne v případě, že test nevyvolá žádnou výjimku.
            </summary>
            <param name="exceptionType">Typ očekávané výjimky</param>
            <param name="noExceptionMessage">
            Zpráva, která má být zahrnuta do výsledku testu, pokud se test nezdaří z důvodu nevyvolání výjimky
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.ExceptionType">
            <summary>
            Načte hodnotu, která označuje typ očekávané výjimky.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.AllowDerivedTypes">
            <summary>
            Získá nebo načte hodnotu, která označuje, jestli je možné typy odvozené od typu očekávané výjimky
            považovat za očekávané.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.NoExceptionMessage">
            <summary>
            Získá zprávu, které se má zahrnout do výsledku testu, pokud tento test selže v důsledku výjimky.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.Verify(System.Exception)">
            <summary>
            Ověří, jestli se očekává typ výjimky vyvolané testem jednotek.
            </summary>
            <param name="exception">Výjimka vyvolaná testem jednotek</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute">
            <summary>
            Základní třída pro atributy, které určují, že se má očekávat výjimka testu jednotek
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> s výchozí zprávou no-exception.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor(System.String)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> se zprávou no-exception.
            </summary>
            <param name="noExceptionMessage">
            Zprávy, které mají být zahrnuty ve výsledku testu, pokud se test nezdaří z důvodu nevyvolání
            výjimky
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.NoExceptionMessage">
            <summary>
            Získá zprávu, které se má zahrnout do výsledku testu, pokud tento test selže v důsledku výjimky.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.SpecifiedNoExceptionMessage">
            <summary>
            Získá zprávu, které se má zahrnout do výsledku testu, pokud tento test selže v důsledku výjimky.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.GetDefaultNoExceptionMessage(System.String)">
            <summary>
            Získá výchozí zprávu no-exception.
            </summary>
            <param name="expectedExceptionAttributeTypeName">Název typu atributu ExpectedException</param>
            <returns>Výchozí zpráva neobsahující výjimku</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.Verify(System.Exception)">
            <summary>
            Určuje, jestli se daná výjimka očekává. Pokud metoda skončí, rozumí se tomu tak,
            že se výjimka očekávala. Pokud metoda vyvolá výjimku, rozumí se tím,
            že se výjimka neočekávala a součástí výsledku testu
            je zpráva vyvolané výjimky. Pomocí třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert"/> je možné si usnadnit
            práci. Pokud se použije <see cref="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive"/> a kontrolní výraz selže,
            výsledek testu se nastaví na Neprůkazný.
            </summary>
            <param name="exception">Výjimka vyvolaná testem jednotek</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.RethrowIfAssertException(System.Exception)">
            <summary>
            Znovu vyvolá výjimku, pokud se jedná o atribut AssertFailedException nebo AssertInconclusiveException.
            </summary>
            <param name="exception">Výjimka, která se má znovu vyvolat, pokud se jedná výjimku kontrolního výrazu</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper">
            <summary>
            Tato třída je koncipovaná tak, aby uživatelům pomáhala při testování jednotek typů, které využívá obecné typy.
            Atribut GenericParameterHelper řeší některá běžná omezení obecných typů,
            jako jsou:
            1. veřejný výchozí konstruktor
            2. implementace společného rozhraní: IComparable, IEnumerable
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>, která
            splňuje omezení newable v obecných typech jazyka C#.
            </summary>
            <remarks>
            This constructor initializes the Data property to a random value.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor(System.Int32)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>, která
            inicializuje vlastnost Data na hodnotu zadanou uživatelem.
            </summary>
            <param name="data">Libovolné celé číslo</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Data">
            <summary>
            Získá nebo nastaví data.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Equals(System.Object)">
            <summary>
            Provede porovnání hodnot pro dva objekty GenericParameterHelper.
            </summary>
            <param name="obj">objekt, se kterým chcete porovnávat</param>
            <returns>pravda, pokud má objekt stejnou hodnotu jako „tento“ objekt GenericParameterHelper.
            V opačném případě nepravda.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetHashCode">
            <summary>
            Vrátí pro tento objekt hodnotu hash.
            </summary>
            <returns>Kód hash</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.CompareTo(System.Object)">
            <summary>
            Porovná data daných dvou objektů <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </summary>
            <param name="obj">Objekt pro porovnání</param>
            <returns>
            Číslo se znaménkem označující relativní hodnoty této instance a hodnoty
            </returns>
            <exception cref="T:System.NotSupportedException">
            Thrown when the object passed in is not an instance of <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetEnumerator">
            <summary>
            Vrátí objekt IEnumerator, jehož délka je odvozená od
            vlastnosti dat.
            </summary>
            <returns>Objekt IEnumerator</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Clone">
            <summary>
            Vrátí objekt GenericParameterHelper, který se rovná
            aktuálnímu objektu.
            </summary>
            <returns>Klonovaný objekt</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger">
            <summary>
            Umožňuje uživatelům protokolovat/zapisovat trasování z testů jednotek pro účely diagnostiky.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessageHandler">
            <summary>
            Obslužná rutina pro LogMessage
            </summary>
            <param name="message">Zpráva, kterou chcete zaprotokolovat</param>
        </member>
        <member name="E:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.OnLogMessage">
            <summary>
            Událost pro naslouchání. Dojde k ní, když autor testů jednotek napíše zprávu.
            Určeno především pro použití adaptérem.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessage(System.String,System.Object[])">
            <summary>
            Rozhraní API pro volání zpráv protokolu zapisovačem testu
            </summary>
            <param name="format">Formátovací řetězec se zástupnými symboly</param>
            <param name="args">Parametry pro zástupné symboly</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute">
            <summary>
            Atribut TestCategory, používá se pro zadání kategorie testu jednotek.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.#ctor(System.String)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute"/> a zavede pro daný test kategorii.
            </summary>
            <param name="testCategory">
            Kategorie testu
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.TestCategories">
            <summary>
            Získá kategorie testu, které se nastavily pro test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute">
            <summary>
            Základní třída atributu Category
            </summary>
            <remarks>
            The reason for this attribute is to let the users create their own implementation of test categories.
            - test framework (discovery, etc) deals with TestCategoryBaseAttribute.
            - The reason that TestCategories property is a collection rather than a string,
              is to give more flexibility to the user. For instance the implementation may be based on enums for which the values can be OR'ed
              in which case it makes sense to have single attribute rather than multiple ones on the same test.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.#ctor">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute"/>.
            Tuto kategorii zavede pro daný test. Řetězce vrácené z TestCategories
            se použijí spolu s příkazem /category k filtrování testů.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.TestCategories">
            <summary>
            Získá kategorii testu, která se nastavila pro test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            <summary>
            Třída AssertFailedException. Používá se pro značení chyby testovacího případu.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
            <param name="msg"> Zpráva </param>
            <param name="ex"> Výjimka </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
            <param name="msg"> Zpráva </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert">
            <summary>
            Kolekce pomocných tříd pro testování nejrůznějších podmínek v rámci
            testů jednotek. Pokud se testovaná podmínka nesplní, vyvolá se
            výjimka.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.That">
            <summary>
            Získá instanci typu singleton funkce Assert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void IsOfType&lt;T&gt;(this Assert assert, object obj)"
            Users could then use a syntax similar to the default assertions which in this case is "Assert.That.IsOfType&lt;Dog&gt;(animal);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean)">
            <summary>
            Testuje, jestli je zadaná podmínka pravdivá, a vyvolá výjimku,
            pokud nepravdivá není.
            </summary>
            <param name="condition">
            Podmínka, která má být podle testu pravdivá.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String)">
            <summary>
            Testuje, jestli je zadaná podmínka pravdivá, a vyvolá výjimku,
            pokud nepravdivá není.
            </summary>
            <param name="condition">
            Podmínka, která má být podle testu pravdivá.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="condition"/>
            je nepravda. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String,System.Object[])">
            <summary>
            Testuje, jestli je zadaná podmínka pravdivá, a vyvolá výjimku,
            pokud nepravdivá není.
            </summary>
            <param name="condition">
            Podmínka, která má být podle testu pravdivá.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="condition"/>
            je nepravda. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean)">
            <summary>
            Testuje, jestli zadaná podmínka není nepravdivá, a vyvolá výjimku,
            pokud pravdivá je.
            </summary>
            <param name="condition">
            Podmínka, která podle testu má být nepravdivá
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String)">
            <summary>
            Testuje, jestli zadaná podmínka není nepravdivá, a vyvolá výjimku,
            pokud pravdivá je.
            </summary>
            <param name="condition">
            Podmínka, která podle testu má být nepravdivá
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="condition"/>
            je pravda. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String,System.Object[])">
            <summary>
            Testuje, jestli zadaná podmínka není nepravdivá, a vyvolá výjimku,
            pokud pravdivá je.
            </summary>
            <param name="condition">
            Podmínka, která podle testu má být nepravdivá
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="condition"/>
            je pravda. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object)">
            <summary>
            Testuje, jestli je zadaný objekt null, a vyvolá výjimku,
            pokud tomu tak není.
            </summary>
            <param name="value">
            Objekt, který má podle testu být Null
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String)">
            <summary>
            Testuje, jestli je zadaný objekt null, a vyvolá výjimku,
            pokud tomu tak není.
            </summary>
            <param name="value">
            Objekt, který má podle testu být Null
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="value"/>
            není Null. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String,System.Object[])">
            <summary>
            Testuje, jestli je zadaný objekt null, a vyvolá výjimku,
            pokud tomu tak není.
            </summary>
            <param name="value">
            Objekt, který má podle testu být Null
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="value"/>
            není Null. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object)">
            <summary>
            Testuje, jestli je zadaný objekt null, a pokud je,
            vyvolá výjimku.
            </summary>
            <param name="value">
            Objekt, u kterého test očekává, že nebude Null.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String)">
            <summary>
            Testuje, jestli je zadaný objekt null, a pokud je,
            vyvolá výjimku.
            </summary>
            <param name="value">
            Objekt, u kterého test očekává, že nebude Null.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="value"/>
            je Null. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String,System.Object[])">
            <summary>
            Testuje, jestli je zadaný objekt null, a pokud je,
            vyvolá výjimku.
            </summary>
            <param name="value">
            Objekt, u kterého test očekává, že nebude Null.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="value"/>
            je Null. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object)">
            <summary>
            Testuje, jestli oba zadané objekty odkazují na stejný objekt,
            a vyvolá výjimku, pokud obě zadané hodnoty na stejný objekt neodkazují.
            </summary>
            <param name="expected">
            První objekt, který chcete porovnat. Jedná se o hodnotu, kterou test očekává.
            </param>
            <param name="actual">
            Druhý objekt, který chcete porovnat. Jedná se o hodnotu vytvořenou testovaným kódem.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String)">
            <summary>
            Testuje, jestli oba zadané objekty odkazují na stejný objekt,
            a vyvolá výjimku, pokud obě zadané hodnoty na stejný objekt neodkazují.
            </summary>
            <param name="expected">
            První objekt, který chcete porovnat. Jedná se o hodnotu, kterou test očekává.
            </param>
            <param name="actual">
            Druhý objekt, který chcete porovnat. Jedná se o hodnotu vytvořenou testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se nerovná <paramref name="expected"/>. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Testuje, jestli oba zadané objekty odkazují na stejný objekt,
            a vyvolá výjimku, pokud obě zadané hodnoty na stejný objekt neodkazují.
            </summary>
            <param name="expected">
            První objekt, který chcete porovnat. Jedná se o hodnotu, kterou test očekává.
            </param>
            <param name="actual">
            Druhý objekt, který chcete porovnat. Jedná se o hodnotu vytvořenou testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se nerovná <paramref name="expected"/>. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object)">
            <summary>
            Testuje, jestli zadané objekty odkazují na různé objekty,
            a vyvolá výjimku, pokud tyto dvě zadané hodnoty odkazují na stejný objekt.
            </summary>
            <param name="notExpected">
            První objekt, který chcete porovnat. Jedná se o hodnotu, která se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhý objekt, který chcete porovnat. Jedná se o hodnotu vytvořenou testovaným kódem.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String)">
            <summary>
            Testuje, jestli zadané objekty odkazují na různé objekty,
            a vyvolá výjimku, pokud tyto dvě zadané hodnoty odkazují na stejný objekt.
            </summary>
            <param name="notExpected">
            První objekt, který chcete porovnat. Jedná se o hodnotu, která se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhý objekt, který chcete porovnat. Jedná se o hodnotu vytvořenou testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se nerovná <paramref name="notExpected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Testuje, jestli zadané objekty odkazují na různé objekty,
            a vyvolá výjimku, pokud tyto dvě zadané hodnoty odkazují na stejný objekt.
            </summary>
            <param name="notExpected">
            První objekt, který chcete porovnat. Jedná se o hodnotu, která se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhý objekt, který chcete porovnat. Jedná se o hodnotu vytvořenou testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se nerovná <paramref name="notExpected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0)">
            <summary>
            Testuje, jestli jsou zadané hodnoty stejné, a vyvolá výjimku,
            pokud tyto dvě hodnoty stejné nejsou. Rozdílné číselné typy se považují
            za nestejné, i když jsou dvě logické hodnoty stejné. 42L se nerovná 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            První hodnota, kterou chcete porovnat. Jedná se o hodnotu, kterou test očekává.
            </param>
            <param name="actual">
            Druhá hodnota, kterou chcete porovnat. Jedná se o hodnotu vytvořenou testovaným kódem.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String)">
            <summary>
            Testuje, jestli jsou zadané hodnoty stejné, a vyvolá výjimku,
            pokud tyto dvě hodnoty stejné nejsou. Rozdílné číselné typy se považují
            za nestejné, i když jsou dvě logické hodnoty stejné. 42L se nerovná 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            První hodnota, kterou chcete porovnat. Jedná se o hodnotu, kterou test očekává.
            </param>
            <param name="actual">
            Druhá hodnota, kterou chcete porovnat. Jedná se o hodnotu vytvořenou testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se nerovná <paramref name="expected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Testuje, jestli jsou zadané hodnoty stejné, a vyvolá výjimku,
            pokud tyto dvě hodnoty stejné nejsou. Rozdílné číselné typy se považují
            za nestejné, i když jsou dvě logické hodnoty stejné. 42L se nerovná 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            První hodnota, kterou chcete porovnat. Jedná se o hodnotu, kterou test očekává.
            </param>
            <param name="actual">
            Druhá hodnota, kterou chcete porovnat. Jedná se o hodnotu vytvořenou testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se nerovná <paramref name="expected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0)">
            <summary>
            Testuje nerovnost zadaných hodnot a vyvolá výjimku,
            pokud si tyto dvě hodnoty jsou rovny. Rozdílné číselné typy se považují
            za nestejné, i když jsou logické hodnoty stejné. 42L se nerovná 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            První hodnota, kterou chcete porovnat. Jedná se o hodnotu, která se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhá hodnota, kterou chcete porovnat. Jedná se o hodnotu vytvořenou testovaným kódem.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String)">
            <summary>
            Testuje nerovnost zadaných hodnot a vyvolá výjimku,
            pokud si tyto dvě hodnoty jsou rovny. Rozdílné číselné typy se považují
            za nestejné, i když jsou logické hodnoty stejné. 42L se nerovná 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            První hodnota, kterou chcete porovnat. Jedná se o hodnotu, která se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhá hodnota, kterou chcete porovnat. Jedná se o hodnotu vytvořenou testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se rovná <paramref name="notExpected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Testuje nerovnost zadaných hodnot a vyvolá výjimku,
            pokud si tyto dvě hodnoty jsou rovny. Rozdílné číselné typy se považují
            za nestejné, i když jsou logické hodnoty stejné. 42L se nerovná 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            První hodnota, kterou chcete porovnat. Jedná se o hodnotu, která se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhá hodnota, kterou chcete porovnat. Jedná se o hodnotu vytvořenou testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se rovná <paramref name="notExpected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object)">
            <summary>
            Testuje, jestli jsou zadané objekty stejné, a vyvolá výjimku,
           pokud oba objekty stejné nejsou. Rozdílné číselné typy se považují
            za nestejné, i když jsou logické hodnoty stejné. 42L se nerovná 42.
            </summary>
            <param name="expected">
            První objekt, který chcete porovnat. Jedná se o objekt, který test očekává.
            </param>
            <param name="actual">
            Druhý objekt, který chcete porovnat. Jedná se o objekt vytvořený testovaným kódem.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String)">
            <summary>
            Testuje, jestli jsou zadané objekty stejné, a vyvolá výjimku,
           pokud oba objekty stejné nejsou. Rozdílné číselné typy se považují
            za nestejné, i když jsou logické hodnoty stejné. 42L se nerovná 42.
            </summary>
            <param name="expected">
            První objekt, který chcete porovnat. Jedná se o objekt, který test očekává.
            </param>
            <param name="actual">
            Druhý objekt, který chcete porovnat. Jedná se o objekt vytvořený testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se nerovná <paramref name="expected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Testuje, jestli jsou zadané objekty stejné, a vyvolá výjimku,
           pokud oba objekty stejné nejsou. Rozdílné číselné typy se považují
            za nestejné, i když jsou logické hodnoty stejné. 42L se nerovná 42.
            </summary>
            <param name="expected">
            První objekt, který chcete porovnat. Jedná se o objekt, který test očekává.
            </param>
            <param name="actual">
            Druhý objekt, který chcete porovnat. Jedná se o objekt vytvořený testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se nerovná <paramref name="expected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object)">
            <summary>
            Testuje nerovnost zadaných objektů a vyvolá výjimku,
            pokud jsou oba objekty stejné. Rozdílné číselné typy se považují
            za nestejné, i když jsou logické hodnoty stejné. 42L se nerovná 42.
            </summary>
            <param name="notExpected">
            První objekt, který chcete porovnat. Jedná se o hodnotu, která se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhý objekt, který chcete porovnat. Jedná se o objekt vytvořený testovaným kódem.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String)">
            <summary>
            Testuje nerovnost zadaných objektů a vyvolá výjimku,
            pokud jsou oba objekty stejné. Rozdílné číselné typy se považují
            za nestejné, i když jsou logické hodnoty stejné. 42L se nerovná 42.
            </summary>
            <param name="notExpected">
            První objekt, který chcete porovnat. Jedná se o hodnotu, která se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhý objekt, který chcete porovnat. Jedná se o objekt vytvořený testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se rovná <paramref name="notExpected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Testuje nerovnost zadaných objektů a vyvolá výjimku,
            pokud jsou oba objekty stejné. Rozdílné číselné typy se považují
            za nestejné, i když jsou logické hodnoty stejné. 42L se nerovná 42.
            </summary>
            <param name="notExpected">
            První objekt, který chcete porovnat. Jedná se o hodnotu, která se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhý objekt, který chcete porovnat. Jedná se o objekt vytvořený testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se rovná <paramref name="notExpected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single)">
            <summary>
            Testuje rovnost zadaných hodnot float a vyvolá výjimku,
            pokud nejsou stejné.
            </summary>
            <param name="expected">
            První plovoucí desetinná čárka, kterou chcete porovnat. Jedná se o plovoucí desetinnou čárku, kterou test očekává.
            </param>
            <param name="actual">
            Druhá plovoucí desetinná čárka, kterou chcete porovnat. Jedná se o plovoucí desetinnou čárku vytvořenou testovaným kódem.
            </param>
            <param name="delta">
            Požadovaná přesnost. Výjimka bude vyvolána pouze tehdy, pokud
            <paramref name="actual"/> se liší od <paramref name="expected"/>
            o více než <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Testuje rovnost zadaných hodnot float a vyvolá výjimku,
            pokud nejsou stejné.
            </summary>
            <param name="expected">
            První plovoucí desetinná čárka, kterou chcete porovnat. Jedná se o plovoucí desetinnou čárku, kterou test očekává.
            </param>
            <param name="actual">
            Druhá plovoucí desetinná čárka, kterou chcete porovnat. Jedná se o plovoucí desetinnou čárku vytvořenou testovaným kódem.
            </param>
            <param name="delta">
            Požadovaná přesnost. Výjimka bude vyvolána pouze tehdy, pokud
            <paramref name="actual"/> se liší od <paramref name="expected"/>
            o více než <paramref name="delta"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se liší od <paramref name="expected"/> o více než
            <paramref name="delta"/>. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Testuje rovnost zadaných hodnot float a vyvolá výjimku,
            pokud nejsou stejné.
            </summary>
            <param name="expected">
            První plovoucí desetinná čárka, kterou chcete porovnat. Jedná se o plovoucí desetinnou čárku, kterou test očekává.
            </param>
            <param name="actual">
            Druhá plovoucí desetinná čárka, kterou chcete porovnat. Jedná se o plovoucí desetinnou čárku vytvořenou testovaným kódem.
            </param>
            <param name="delta">
            Požadovaná přesnost. Výjimka bude vyvolána pouze tehdy, pokud
            <paramref name="actual"/> se liší od <paramref name="expected"/>
            o více než <paramref name="delta"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se liší od <paramref name="expected"/> o více než
            <paramref name="delta"/>. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single)">
            <summary>
            Testuje nerovnost zadaných hodnot float a vyvolá výjimku,
            pokud jsou stejné.
            </summary>
            <param name="notExpected">
            První desetinná čárka, kterou chcete porovnat. Toto je desetinná čárka, která se podle testu nemá
            shodovat s aktuální hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhá plovoucí desetinná čárka, kterou chcete porovnat. Jedná se o plovoucí desetinnou čárku vytvořenou testovaným kódem.
            </param>
            <param name="delta">
            Požadovaná přesnost. Výjimka bude vyvolána pouze tehdy, pokud
            <paramref name="actual"/> se liší od <paramref name="notExpected"/>
            o maximálně <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Testuje nerovnost zadaných hodnot float a vyvolá výjimku,
            pokud jsou stejné.
            </summary>
            <param name="notExpected">
            První desetinná čárka, kterou chcete porovnat. Toto je desetinná čárka, která se podle testu nemá
            shodovat s aktuální hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhá plovoucí desetinná čárka, kterou chcete porovnat. Jedná se o plovoucí desetinnou čárku vytvořenou testovaným kódem.
            </param>
            <param name="delta">
            Požadovaná přesnost. Výjimka bude vyvolána pouze tehdy, pokud
            <paramref name="actual"/> se liší od <paramref name="notExpected"/>
            o maximálně <paramref name="delta"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se rovná <paramref name="notExpected"/> nebo se liší o méně než
            <paramref name="delta"/>. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Testuje nerovnost zadaných hodnot float a vyvolá výjimku,
            pokud jsou stejné.
            </summary>
            <param name="notExpected">
            První desetinná čárka, kterou chcete porovnat. Toto je desetinná čárka, která se podle testu nemá
            shodovat s aktuální hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhá plovoucí desetinná čárka, kterou chcete porovnat. Jedná se o plovoucí desetinnou čárku vytvořenou testovaným kódem.
            </param>
            <param name="delta">
            Požadovaná přesnost. Výjimka bude vyvolána pouze tehdy, pokud
            <paramref name="actual"/> se liší od <paramref name="notExpected"/>
            o maximálně <paramref name="delta"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se rovná <paramref name="notExpected"/> nebo se liší o méně než
            <paramref name="delta"/>. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double)">
            <summary>
            Testuje rovnost zadaných hodnot double a vyvolá výjimku,
            pokud se neshodují.
            </summary>
            <param name="expected">
            První dvojitá přesnost, kterou chcete porovnat. Jedná se o dvojitou přesnost, kterou test očekává.
            </param>
            <param name="actual">
            Druhá dvojitá přesnost, kterou chcete porovnat. Jedná se o dvojitou přesnost vytvořenou testovaným kódem.
            </param>
            <param name="delta">
            Požadovaná přesnost. Výjimka bude vyvolána pouze tehdy, pokud
            <paramref name="actual"/> se liší od <paramref name="expected"/>
            o více než <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Testuje rovnost zadaných hodnot double a vyvolá výjimku,
            pokud se neshodují.
            </summary>
            <param name="expected">
            První dvojitá přesnost, kterou chcete porovnat. Jedná se o dvojitou přesnost, kterou test očekává.
            </param>
            <param name="actual">
            Druhá dvojitá přesnost, kterou chcete porovnat. Jedná se o dvojitou přesnost vytvořenou testovaným kódem.
            </param>
            <param name="delta">
            Požadovaná přesnost. Výjimka bude vyvolána pouze tehdy, pokud
            <paramref name="actual"/> se liší od <paramref name="expected"/>
            o více než <paramref name="delta"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se liší od <paramref name="expected"/> o více než
            <paramref name="delta"/>. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Testuje rovnost zadaných hodnot double a vyvolá výjimku,
            pokud se neshodují.
            </summary>
            <param name="expected">
            První dvojitá přesnost, kterou chcete porovnat. Jedná se o dvojitou přesnost, kterou test očekává.
            </param>
            <param name="actual">
            Druhá dvojitá přesnost, kterou chcete porovnat. Jedná se o dvojitou přesnost vytvořenou testovaným kódem.
            </param>
            <param name="delta">
            Požadovaná přesnost. Výjimka bude vyvolána pouze tehdy, pokud
            <paramref name="actual"/> se liší od <paramref name="expected"/>
            o více než <paramref name="delta"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se liší od <paramref name="expected"/> o více než
            <paramref name="delta"/>. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double)">
            <summary>
            Testuje nerovnost zadaných hodnot double a vyvolá výjimku,
            pokud jsou si rovny.
            </summary>
            <param name="notExpected">
            První dvojitá přesnost, kterou chcete porovnat. Jedná se o dvojitou přesnost, která se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhá dvojitá přesnost, kterou chcete porovnat. Jedná se o dvojitou přesnost vytvořenou testovaným kódem.
            </param>
            <param name="delta">
            Požadovaná přesnost. Výjimka bude vyvolána pouze tehdy, pokud
            <paramref name="actual"/> se liší od <paramref name="notExpected"/>
            o maximálně <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Testuje nerovnost zadaných hodnot double a vyvolá výjimku,
            pokud jsou si rovny.
            </summary>
            <param name="notExpected">
            První dvojitá přesnost, kterou chcete porovnat. Jedná se o dvojitou přesnost, která se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhá dvojitá přesnost, kterou chcete porovnat. Jedná se o dvojitou přesnost vytvořenou testovaným kódem.
            </param>
            <param name="delta">
            Požadovaná přesnost. Výjimka bude vyvolána pouze tehdy, pokud
            <paramref name="actual"/> se liší od <paramref name="notExpected"/>
            o maximálně <paramref name="delta"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se rovná <paramref name="notExpected"/> nebo se liší o méně než
            <paramref name="delta"/>. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Testuje nerovnost zadaných hodnot double a vyvolá výjimku,
            pokud jsou si rovny.
            </summary>
            <param name="notExpected">
            První dvojitá přesnost, kterou chcete porovnat. Jedná se o dvojitou přesnost, která se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhá dvojitá přesnost, kterou chcete porovnat. Jedná se o dvojitou přesnost vytvořenou testovaným kódem.
            </param>
            <param name="delta">
            Požadovaná přesnost. Výjimka bude vyvolána pouze tehdy, pokud
            <paramref name="actual"/> se liší od <paramref name="notExpected"/>
            o maximálně <paramref name="delta"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se rovná <paramref name="notExpected"/> nebo se liší o méně než
            <paramref name="delta"/>. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean)">
            <summary>
            Testuje, jestli jsou zadané řetězce stejné, a vyvolá výjimku,
            pokud stejné nejsou. Pro porovnání se používá neutrální jazyková verze.
            </summary>
            <param name="expected">
            První řetězec, který chcete porovnat. Jedná se o řetězec, který test očekává.
            </param>
            <param name="actual">
            Druhý řetězec, který se má porovnat. Jedná se o řetězec vytvořený testovaným kódem.
            </param>
            <param name="ignoreCase">
            Logická hodnota označující porovnání s rozlišováním velkých a malých písmen nebo bez jejich rozlišování. (Hodnota pravda
            označuje porovnání bez rozlišování velkých a malých písmen.)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Testuje, jestli jsou zadané řetězce stejné, a vyvolá výjimku,
            pokud stejné nejsou. Pro porovnání se používá neutrální jazyková verze.
            </summary>
            <param name="expected">
            První řetězec, který chcete porovnat. Jedná se o řetězec, který test očekává.
            </param>
            <param name="actual">
            Druhý řetězec, který se má porovnat. Jedná se o řetězec vytvořený testovaným kódem.
            </param>
            <param name="ignoreCase">
            Logická hodnota označující porovnání s rozlišováním velkých a malých písmen nebo bez jejich rozlišování. (Hodnota pravda
            označuje porovnání bez rozlišování velkých a malých písmen.)
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se nerovná <paramref name="expected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Testuje, jestli jsou zadané řetězce stejné, a vyvolá výjimku,
            pokud stejné nejsou. Pro porovnání se používá neutrální jazyková verze.
            </summary>
            <param name="expected">
            První řetězec, který chcete porovnat. Jedná se o řetězec, který test očekává.
            </param>
            <param name="actual">
            Druhý řetězec, který se má porovnat. Jedná se o řetězec vytvořený testovaným kódem.
            </param>
            <param name="ignoreCase">
            Logická hodnota označující porovnání s rozlišováním velkých a malých písmen nebo bez jejich rozlišování. (Hodnota pravda
            označuje porovnání bez rozlišování velkých a malých písmen.)
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se nerovná <paramref name="expected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Testuje, jestli jsou zadané řetězce stejné, a vyvolá výjimku,
            pokud stejné nejsou.
            </summary>
            <param name="expected">
            První řetězec, který chcete porovnat. Jedná se o řetězec, který test očekává.
            </param>
            <param name="actual">
            Druhý řetězec, který se má porovnat. Jedná se o řetězec vytvořený testovaným kódem.
            </param>
            <param name="ignoreCase">
            Logická hodnota označující porovnání s rozlišováním velkých a malých písmen nebo bez jejich rozlišování. (Hodnota pravda
            označuje porovnání bez rozlišování velkých a malých písmen.)
            </param>
            <param name="culture">
            Objekt CultureInfo, který poskytuje informace o porovnání jazykových verzí.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Testuje, jestli jsou zadané řetězce stejné, a vyvolá výjimku,
            pokud stejné nejsou.
            </summary>
            <param name="expected">
            První řetězec, který chcete porovnat. Jedná se o řetězec, který test očekává.
            </param>
            <param name="actual">
            Druhý řetězec, který se má porovnat. Jedná se o řetězec vytvořený testovaným kódem.
            </param>
            <param name="ignoreCase">
            Logická hodnota označující porovnání s rozlišováním velkých a malých písmen nebo bez jejich rozlišování. (Hodnota pravda
            označuje porovnání bez rozlišování velkých a malých písmen.)
            </param>
            <param name="culture">
            Objekt CultureInfo, který poskytuje informace o porovnání jazykových verzí.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se nerovná <paramref name="expected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Testuje, jestli jsou zadané řetězce stejné, a vyvolá výjimku,
            pokud stejné nejsou.
            </summary>
            <param name="expected">
            První řetězec, který chcete porovnat. Jedná se o řetězec, který test očekává.
            </param>
            <param name="actual">
            Druhý řetězec, který se má porovnat. Jedná se o řetězec vytvořený testovaným kódem.
            </param>
            <param name="ignoreCase">
            Logická hodnota označující porovnání s rozlišováním velkých a malých písmen nebo bez jejich rozlišování. (Hodnota pravda
            označuje porovnání bez rozlišování velkých a malých písmen.)
            </param>
            <param name="culture">
            Objekt CultureInfo, který poskytuje informace o porovnání jazykových verzí.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se nerovná <paramref name="expected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean)">
            <summary>
            Testuje nerovnost zadaných řetězců a vyvolá výjimku,
            pokud jsou stejné. Pro srovnání se používá neutrální jazyková verze.
            </summary>
            <param name="notExpected">
            První řetězec, který chcete porovnat. Jedná se o řetězec, který se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhý řetězec, který se má porovnat. Jedná se o řetězec vytvořený testovaným kódem.
            </param>
            <param name="ignoreCase">
            Logická hodnota označující porovnání s rozlišováním velkých a malých písmen nebo bez jejich rozlišování. (Hodnota pravda
            označuje porovnání bez rozlišování velkých a malých písmen.)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Testuje nerovnost zadaných řetězců a vyvolá výjimku,
            pokud jsou stejné. Pro srovnání se používá neutrální jazyková verze.
            </summary>
            <param name="notExpected">
            První řetězec, který chcete porovnat. Jedná se o řetězec, který se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhý řetězec, který se má porovnat. Jedná se o řetězec vytvořený testovaným kódem.
            </param>
            <param name="ignoreCase">
            Logická hodnota označující porovnání s rozlišováním velkých a malých písmen nebo bez jejich rozlišování. (Hodnota pravda
            označuje porovnání bez rozlišování velkých a malých písmen.)
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se rovná <paramref name="notExpected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Testuje nerovnost zadaných řetězců a vyvolá výjimku,
            pokud jsou stejné. Pro srovnání se používá neutrální jazyková verze.
            </summary>
            <param name="notExpected">
            První řetězec, který chcete porovnat. Jedná se o řetězec, který se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhý řetězec, který se má porovnat. Jedná se o řetězec vytvořený testovaným kódem.
            </param>
            <param name="ignoreCase">
            Logická hodnota označující porovnání s rozlišováním velkých a malých písmen nebo bez jejich rozlišování. (Hodnota pravda
            označuje porovnání bez rozlišování velkých a malých písmen.)
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se rovná <paramref name="notExpected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Testuje nerovnost zadaných řetězců a vyvolá výjimku,
            pokud jsou si rovny.
            </summary>
            <param name="notExpected">
            První řetězec, který chcete porovnat. Jedná se o řetězec, který se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhý řetězec, který se má porovnat. Jedná se o řetězec vytvořený testovaným kódem.
            </param>
            <param name="ignoreCase">
            Logická hodnota označující porovnání s rozlišováním velkých a malých písmen nebo bez jejich rozlišování. (Hodnota pravda
            označuje porovnání bez rozlišování velkých a malých písmen.)
            </param>
            <param name="culture">
            Objekt CultureInfo, který poskytuje informace o porovnání jazykových verzí.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Testuje nerovnost zadaných řetězců a vyvolá výjimku,
            pokud jsou si rovny.
            </summary>
            <param name="notExpected">
            První řetězec, který chcete porovnat. Jedná se o řetězec, který se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhý řetězec, který se má porovnat. Jedná se o řetězec vytvořený testovaným kódem.
            </param>
            <param name="ignoreCase">
            Logická hodnota označující porovnání s rozlišováním velkých a malých písmen nebo bez jejich rozlišování. (Hodnota pravda
            označuje porovnání bez rozlišování velkých a malých písmen.)
            </param>
            <param name="culture">
            Objekt CultureInfo, který poskytuje informace o porovnání jazykových verzí.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se rovná <paramref name="notExpected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Testuje nerovnost zadaných řetězců a vyvolá výjimku,
            pokud jsou si rovny.
            </summary>
            <param name="notExpected">
            První řetězec, který chcete porovnat. Jedná se o řetězec, který se podle testu nemá
            shodovat se skutečnou hodnotou <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhý řetězec, který se má porovnat. Jedná se o řetězec vytvořený testovaným kódem.
            </param>
            <param name="ignoreCase">
            Logická hodnota označující porovnání s rozlišováním velkých a malých písmen nebo bez jejich rozlišování. (Hodnota pravda
            označuje porovnání bez rozlišování velkých a malých písmen.)
            </param>
            <param name="culture">
            Objekt CultureInfo, který poskytuje informace o porovnání jazykových verzí.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se rovná <paramref name="notExpected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type)">
            <summary>
            Testuje, jestli zadaný objekt je instancí očekávaného
            typu, a vyvolá výjimku, pokud očekávaný typ není
            v hierarchii dědění objektu.
            </summary>
            <param name="value">
            Objekt, který podle testu má být zadaného typu
            </param>
            <param name="expectedType">
            Očekávaný typ <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Testuje, jestli zadaný objekt je instancí očekávaného
            typu, a vyvolá výjimku, pokud očekávaný typ není
            v hierarchii dědění objektu.
            </summary>
            <param name="value">
            Objekt, který podle testu má být zadaného typu
            </param>
            <param name="expectedType">
            Očekávaný typ <paramref name="value"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="value"/>
            není instancí <paramref name="expectedType"/>. Zpráva se
            zobrazuje ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Testuje, jestli zadaný objekt je instancí očekávaného
            typu, a vyvolá výjimku, pokud očekávaný typ není
            v hierarchii dědění objektu.
            </summary>
            <param name="value">
            Objekt, který podle testu má být zadaného typu
            </param>
            <param name="expectedType">
            Očekávaný typ <paramref name="value"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="value"/>
            není instancí <paramref name="expectedType"/>. Zpráva se
            zobrazuje ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type)">
            <summary>
            Testuje, jestli zadaný objekt není instancí nesprávného
            typu, a vyvolá výjimku, pokud zadaný typ je v
            hierarchii dědění objektu.
            </summary>
            <param name="value">
            Objekt, který podle testu nemá být zadaného typu.
            </param>
            <param name="wrongType">
            Typ, který by hodnotou <paramref name="value"/> neměl být.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Testuje, jestli zadaný objekt není instancí nesprávného
            typu, a vyvolá výjimku, pokud zadaný typ je v
            hierarchii dědění objektu.
            </summary>
            <param name="value">
            Objekt, který podle testu nemá být zadaného typu.
            </param>
            <param name="wrongType">
            Typ, který by hodnotou <paramref name="value"/> neměl být.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="value"/>
            je instancí <paramref name="wrongType"/>. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Testuje, jestli zadaný objekt není instancí nesprávného
            typu, a vyvolá výjimku, pokud zadaný typ je v
            hierarchii dědění objektu.
            </summary>
            <param name="value">
            Objekt, který podle testu nemá být zadaného typu.
            </param>
            <param name="wrongType">
            Typ, který by hodnotou <paramref name="value"/> neměl být.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="value"/>
            je instancí <paramref name="wrongType"/>. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail">
            <summary>
            Vyvolá výjimku AssertFailedException.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String)">
            <summary>
            Vyvolá výjimku AssertFailedException.
            </summary>
            <param name="message">
            Zpráva, která má být zahrnuta do výjimky. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String,System.Object[])">
            <summary>
            Vyvolá výjimku AssertFailedException.
            </summary>
            <param name="message">
            Zpráva, která má být zahrnuta do výjimky. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive">
            <summary>
            Vyvolá výjimku AssertInconclusiveException.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String)">
            <summary>
            Vyvolá výjimku AssertInconclusiveException.
            </summary>
            <param name="message">
            Zpráva, která má být zahrnuta do výjimky. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String,System.Object[])">
            <summary>
            Vyvolá výjimku AssertInconclusiveException.
            </summary>
            <param name="message">
            Zpráva, která má být zahrnuta do výjimky. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Equals(System.Object,System.Object)">
            <summary>
            Statická přetížení operátoru rovnosti se používají k porovnání rovnosti odkazů na instance
             dvou typů. Tato metoda by se <b>neměla</b> používat k porovnání rovnosti dvou
            instancí. Tento objekt <b>vždy</b> vyvolá Assert.Fail. Ve svých testech
            jednotek prosím použijte Assert.AreEqual a přidružená přetížení.
            </summary>
            <param name="objA"> Objekt A </param>
            <param name="objB"> Objekt B </param>
            <returns> Vždy nepravda. </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action)">
            <summary>
            Testujte, jestli kód určený delegátem <paramref name="action"/> vyvolá přesně danou výjimku typu <typeparamref name="T"/> (a ne odvozeného typu),
            a vyvolá
            <code>
            AssertFailedException
            </code>,
            pokud kód nevyvolává výjimky nebo vyvolává výjimky typu jiného než <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegát kódu, který chcete testovat a který má vyvolat výjimku
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Typ výjimky, ke které má podle očekávání dojít
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String)">
            <summary>
            Testujte, jestli kód určený delegátem <paramref name="action"/> vyvolá přesně danou výjimku typu <typeparamref name="T"/> (a ne odvozeného typu),
            a vyvolá
            <code>
            AssertFailedException
            </code>,
            pokud kód nevyvolává výjimky nebo vyvolává výjimky typu jiného než <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegujte kód, který chcete testovat a který má vyvolat výjimku.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="action"/>
            nevyvolá výjimku typu <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Typ výjimky, ke které má podle očekávání dojít
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object})">
            <summary>
            Testujte, jestli kód určený delegátem <paramref name="action"/> vyvolá přesně danou výjimku typu <typeparamref name="T"/> (a ne odvozeného typu),
            a vyvolá
            <code>
            AssertFailedException
            </code>,
            pokud kód nevyvolává výjimky nebo vyvolává výjimky typu jiného než <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegujte kód, který chcete testovat a který má vyvolat výjimku.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Typ výjimky, ke které má podle očekávání dojít
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String)">
            <summary>
            Testujte, jestli kód určený delegátem <paramref name="action"/> vyvolá přesně danou výjimku typu <typeparamref name="T"/> (a ne odvozeného typu),
            a vyvolá
            <code>
            AssertFailedException
            </code>,
            pokud kód nevyvolává výjimky nebo vyvolává výjimky typu jiného než <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegujte kód, který chcete testovat a který má vyvolat výjimku.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="action"/>
            nevyvolá výjimku typu <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Typ výjimky, ke které má podle očekávání dojít
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String,System.Object[])">
            <summary>
            Testujte, jestli kód určený delegátem <paramref name="action"/> vyvolá přesně danou výjimku typu <typeparamref name="T"/> (a ne odvozeného typu),
            a vyvolá
            <code>
            AssertFailedException
            </code>,
            pokud kód nevyvolává výjimky nebo vyvolává výjimky typu jiného než <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegujte kód, který chcete testovat a který má vyvolat výjimku.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="action"/>
            nevyvolá výjimku typu <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throw exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Typ výjimky, ke které má podle očekávání dojít
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String,System.Object[])">
            <summary>
            Testujte, jestli kód určený delegátem <paramref name="action"/> vyvolá přesně danou výjimku typu <typeparamref name="T"/> (a ne odvozeného typu),
            a vyvolá
            <code>
            AssertFailedException
            </code>,
            pokud kód nevyvolává výjimky nebo vyvolává výjimky typu jiného než <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegujte kód, který chcete testovat a který má vyvolat výjimku.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="action"/>
            nevyvolá výjimku typu <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Typ výjimky, ke které má podle očekávání dojít
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Testujte, jestli kód určený delegátem <paramref name="action"/> vyvolá přesně danou výjimku typu <typeparamref name="T"/> (a ne odvozeného typu),
            a vyvolá
            <code>
            AssertFailedException
            </code>,
            pokud kód nevyvolává výjimky nebo vyvolává výjimky typu jiného než <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegát kódu, který chcete testovat a který má vyvolat výjimku
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Třídu <see cref="T:System.Threading.Tasks.Task"/> spouští delegáta.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            Testujte, jestli kód určený delegátem <paramref name="action"/> vyvolá přesně danou výjimku typu <typeparamref name="T"/> (a ne odvozeného typu),
            a vyvolá <code>AssertFailedException</code>, pokud kód nevyvolává výjimky nebo vyvolává výjimky typu jiného než <typeparamref name="T"/>.
            </summary>
            <param name="action">Delegát kódu, který chcete testovat a který má vyvolat výjimku</param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="action"/>
            nevyvolá výjimku typu <typeparamref name="T"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Třídu <see cref="T:System.Threading.Tasks.Task"/> spouští delegáta.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String,System.Object[])">
            <summary>
            Testujte, jestli kód určený delegátem <paramref name="action"/> vyvolá přesně danou výjimku typu <typeparamref name="T"/> (a ne odvozeného typu),
            a vyvolá <code>AssertFailedException</code>, pokud kód nevyvolává výjimky nebo vyvolává výjimky typu jiného než <typeparamref name="T"/>.
            </summary>
            <param name="action">Delegát kódu, který chcete testovat a který má vyvolat výjimku</param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="action"/>
            nevyvolá výjimku typu <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Třídu <see cref="T:System.Threading.Tasks.Task"/> spouští delegáta.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNullChars(System.String)">
            <summary>
            Nahradí znaky null ('\0') řetězcem "\\0".
            </summary>
            <param name="input">
            Řetězec, který se má hledat
            </param>
            <returns>
            Převedený řetězec se znaky Null nahrazený řetězcem "\\0".
            </returns>
            <remarks>
            This is only public and still present to preserve compatibility with the V1 framework.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.HandleFail(System.String,System.String,System.Object[])">
            <summary>
            Pomocná funkce, která vytváří a vyvolává výjimku AssertionFailedException
            </summary>
            <param name="assertionName">
            název kontrolního výrazu, který vyvolává výjimku
            </param>
            <param name="message">
            zpráva popisující podmínky neplatnosti kontrolního výrazu
            </param>
            <param name="parameters">
            Parametry
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.CheckParameterNotNull(System.Object,System.String,System.String,System.String,System.Object[])">
            <summary>
            Ověří parametr pro platné podmínky.
            </summary>
            <param name="param">
            Parametr
            </param>
            <param name="assertionName">
            Název kontrolního výrazu
            </param>
            <param name="parameterName">
            název parametru
            </param>
            <param name="message">
            zpráva pro neplatnou výjimku parametru
            </param>
            <param name="parameters">
            Parametry
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNulls(System.Object)">
            <summary>
            Bezpečně převede objekt na řetězec, včetně zpracování hodnot null a znaků null.
            Hodnoty null se převádějí na formát (null). Znaky null se převádějí na \\0.
            </summary>
            <param name="input">
            Objekt, který chcete převést na řetězec
            </param>
            <returns>
            Převedený řetězec
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert">
            <summary>
            Kontrolní výraz řetězce
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.That">
            <summary>
            Získá instanci typu singleton funkce CollectionAssert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void ContainsWords(this StringAssert cusomtAssert, string value, ICollection substrings)"
            Users could then use a syntax similar to the default assertions which in this case is "StringAssert.That.ContainsWords(value, substrings);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String)">
            <summary>
            Testuje, jestli zadaný řetězec obsahuje zadaný podřetězec,
            a vyvolá výjimku, pokud se podřetězec v testovacím řetězci
            nevyskytuje.
            </summary>
            <param name="value">
            Řetězec, který má obsahovat <paramref name="substring"/>.
            </param>
            <param name="substring">
            Řetězec má být v rozmezí hodnot <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String)">
            <summary>
            Testuje, jestli zadaný řetězec obsahuje zadaný podřetězec,
            a vyvolá výjimku, pokud se podřetězec v testovacím řetězci
            nevyskytuje.
            </summary>
            <param name="value">
            Řetězec, který má obsahovat <paramref name="substring"/>.
            </param>
            <param name="substring">
            Řetězec má být v rozmezí hodnot <paramref name="value"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="substring"/>
            není v <paramref name="value"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String,System.Object[])">
            <summary>
            Testuje, jestli zadaný řetězec obsahuje zadaný podřetězec,
            a vyvolá výjimku, pokud se podřetězec v testovacím řetězci
            nevyskytuje.
            </summary>
            <param name="value">
            Řetězec, který má obsahovat <paramref name="substring"/>.
            </param>
            <param name="substring">
            Řetězec má být v rozmezí hodnot <paramref name="value"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="substring"/>
            není v <paramref name="value"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String)">
            <summary>
            Testuje, jestli zadaný řetězec začíná zadaným podřetězcem,
            a vyvolá výjimku, pokud testovací řetězec podřetězcem
            nezačíná.
            </summary>
            <param name="value">
            Řetězec, který má začínat na <paramref name="substring"/>.
            </param>
            <param name="substring">
            Řetězec, který má být prefixem hodnoty <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String)">
            <summary>
            Testuje, jestli zadaný řetězec začíná zadaným podřetězcem,
            a vyvolá výjimku, pokud testovací řetězec podřetězcem
            nezačíná.
            </summary>
            <param name="value">
            Řetězec, který má začínat na <paramref name="substring"/>.
            </param>
            <param name="substring">
            Řetězec, který má být prefixem hodnoty <paramref name="value"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="value"/>
            nezačíná na <paramref name="substring"/>. Zpráva se
            zobrazuje ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Testuje, jestli zadaný řetězec začíná zadaným podřetězcem,
            a vyvolá výjimku, pokud testovací řetězec podřetězcem
            nezačíná.
            </summary>
            <param name="value">
            Řetězec, který má začínat na <paramref name="substring"/>.
            </param>
            <param name="substring">
            Řetězec, který má být prefixem hodnoty <paramref name="value"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="value"/>
            nezačíná na <paramref name="substring"/>. Zpráva se
            zobrazuje ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String)">
            <summary>
            Testuje, jestli zadaný řetězec končí zadaným podřetězcem,
            a vyvolá výjimku, pokud jím testovací řetězec
            nekončí.
            </summary>
            <param name="value">
            Řetězec, který má končit na <paramref name="substring"/>.
            </param>
            <param name="substring">
            Řetězec, který má být příponou <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String)">
            <summary>
            Testuje, jestli zadaný řetězec končí zadaným podřetězcem,
            a vyvolá výjimku, pokud jím testovací řetězec
            nekončí.
            </summary>
            <param name="value">
            Řetězec, který má končit na <paramref name="substring"/>.
            </param>
            <param name="substring">
            Řetězec, který má být příponou <paramref name="value"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="value"/>
            nekončí na <paramref name="substring"/>. Zpráva se
            zobrazuje ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Testuje, jestli zadaný řetězec končí zadaným podřetězcem,
            a vyvolá výjimku, pokud jím testovací řetězec
            nekončí.
            </summary>
            <param name="value">
            Řetězec, který má končit na <paramref name="substring"/>.
            </param>
            <param name="substring">
            Řetězec, který má být příponou <paramref name="value"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="value"/>
            nekončí na <paramref name="substring"/>. Zpráva se
            zobrazuje ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Testuje, jestli se zadaný objekt shoduje s regulárním výrazem, a
            vyvolá výjimku, pokud se řetězec s výrazem neshoduje.
            </summary>
            <param name="value">
            Řetězec, který se má shodovat se vzorkem <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Regulární výraz, který <paramref name="value"/> se
            má shodovat.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Testuje, jestli se zadaný objekt shoduje s regulárním výrazem, a
            vyvolá výjimku, pokud se řetězec s výrazem neshoduje.
            </summary>
            <param name="value">
            Řetězec, který se má shodovat se vzorkem <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Regulární výraz, který <paramref name="value"/> se
            má shodovat.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="value"/>
            neodpovídá <paramref name="pattern"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Testuje, jestli se zadaný objekt shoduje s regulárním výrazem, a
            vyvolá výjimku, pokud se řetězec s výrazem neshoduje.
            </summary>
            <param name="value">
            Řetězec, který se má shodovat se vzorkem <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Regulární výraz, který <paramref name="value"/> se
            má shodovat.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="value"/>
            neodpovídá <paramref name="pattern"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Testuje, jestli se zadaný řetězec neshoduje s regulárním výrazem,
            a vyvolá výjimku, pokud se řetězec s výrazem shoduje.
            </summary>
            <param name="value">
            Řetězec, který se nemá shodovat se skutečnou hodnotou <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Regulární výraz, který <paramref name="value"/> se
            nemá shodovat.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Testuje, jestli se zadaný řetězec neshoduje s regulárním výrazem,
            a vyvolá výjimku, pokud se řetězec s výrazem shoduje.
            </summary>
            <param name="value">
            Řetězec, který se nemá shodovat se skutečnou hodnotou <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Regulární výraz, který <paramref name="value"/> se
            nemá shodovat.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="value"/>
            odpovídá <paramref name="pattern"/>. Zpráva je zobrazena ve výsledcích
            testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Testuje, jestli se zadaný řetězec neshoduje s regulárním výrazem,
            a vyvolá výjimku, pokud se řetězec s výrazem shoduje.
            </summary>
            <param name="value">
            Řetězec, který se nemá shodovat se skutečnou hodnotou <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Regulární výraz, který <paramref name="value"/> se
            nemá shodovat.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="value"/>
            odpovídá <paramref name="pattern"/>. Zpráva je zobrazena ve výsledcích
            testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert">
            <summary>
            Kolekce tříd pomocných služeb pro ověřování nejrůznějších podmínek vztahujících se
            na kolekce v rámci testů jednotek. Pokud se testovaná podmínka
            nesplní, vyvolá se výjimka.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.That">
            <summary>
            Získá instanci typu singleton funkce CollectionAssert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void AreEqualUnordered(this CollectionAssert cusomtAssert, ICollection expected, ICollection actual)"
            Users could then use a syntax similar to the default assertions which in this case is "CollectionAssert.That.AreEqualUnordered(list1, list2);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object)">
            <summary>
            Testuje, jestli zadaná kolekce obsahuje zadaný prvek,
            a vyvolá výjimku, pokud prvek v kolekci není.
            </summary>
            <param name="collection">
            Kolekce, ve které chcete prvek vyhledat
            </param>
            <param name="element">
            Prvek, který má být v kolekci
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Testuje, jestli zadaná kolekce obsahuje zadaný prvek,
            a vyvolá výjimku, pokud prvek v kolekci není.
            </summary>
            <param name="collection">
            Kolekce, ve které chcete prvek vyhledat
            </param>
            <param name="element">
            Prvek, který má být v kolekci
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="element"/>
            není v <paramref name="collection"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Testuje, jestli zadaná kolekce obsahuje zadaný prvek,
            a vyvolá výjimku, pokud prvek v kolekci není.
            </summary>
            <param name="collection">
            Kolekce, ve které chcete prvek vyhledat
            </param>
            <param name="element">
            Prvek, který má být v kolekci
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="element"/>
            není v <paramref name="collection"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object)">
            <summary>
            Testuje, jestli zadaná kolekce neobsahuje zadaný
            prvek, a vyvolá výjimku, pokud prvek je v kolekci.
            </summary>
            <param name="collection">
            Kolekce, ve které chcete prvek vyhledat
            </param>
            <param name="element">
            Prvek, který nemá být v kolekci
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Testuje, jestli zadaná kolekce neobsahuje zadaný
            prvek, a vyvolá výjimku, pokud prvek je v kolekci.
            </summary>
            <param name="collection">
            Kolekce, ve které chcete prvek vyhledat
            </param>
            <param name="element">
            Prvek, který nemá být v kolekci
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="element"/>
            je v kolekci <paramref name="collection"/>. Zpráva je zobrazena ve výsledcích
            testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Testuje, jestli zadaná kolekce neobsahuje zadaný
            prvek, a vyvolá výjimku, pokud prvek je v kolekci.
            </summary>
            <param name="collection">
            Kolekce, ve které chcete prvek vyhledat
            </param>
            <param name="element">
            Prvek, který nemá být v kolekci
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="element"/>
            je v kolekci <paramref name="collection"/>. Zpráva je zobrazena ve výsledcích
            testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection)">
            <summary>
            Testuje, jestli ani jedna položka v zadané kolekci není null, a vyvolá
            výjimku, pokud je jakýkoli prvek null.
            </summary>
            <param name="collection">
            Kolekce, ve které chcete hledat prvky Null.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String)">
            <summary>
            Testuje, jestli ani jedna položka v zadané kolekci není null, a vyvolá
            výjimku, pokud je jakýkoli prvek null.
            </summary>
            <param name="collection">
            Kolekce, ve které chcete hledat prvky Null.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="collection"/>
            obsahuje prvek Null. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testuje, jestli ani jedna položka v zadané kolekci není null, a vyvolá
            výjimku, pokud je jakýkoli prvek null.
            </summary>
            <param name="collection">
            Kolekce, ve které chcete hledat prvky Null.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="collection"/>
            obsahuje prvek Null. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection)">
            <summary>
            Testuje, jestli jsou všechny položky v zadané kolekci jedinečné, a
            vyvolá výjimku, pokud libovolné dva prvky v kolekci jsou stejné.
            </summary>
            <param name="collection">
            Kolekce, ve které chcete hledat duplicitní prvky
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String)">
            <summary>
            Testuje, jestli jsou všechny položky v zadané kolekci jedinečné, a
            vyvolá výjimku, pokud libovolné dva prvky v kolekci jsou stejné.
            </summary>
            <param name="collection">
            Kolekce, ve které chcete hledat duplicitní prvky
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="collection"/>
            obsahuje alespoň jeden duplicitní prvek. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testuje, jestli jsou všechny položky v zadané kolekci jedinečné, a
            vyvolá výjimku, pokud libovolné dva prvky v kolekci jsou stejné.
            </summary>
            <param name="collection">
            Kolekce, ve které chcete hledat duplicitní prvky
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="collection"/>
            obsahuje alespoň jeden duplicitní prvek. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testuje, jestli jedna kolekce je podmnožinou jiné kolekce,
            a vyvolá výjimku, pokud libovolný prvek podmnožiny není zároveň
            prvkem nadmnožiny.
            </summary>
            <param name="subset">
            Kolekce, která má být podmnožinou <paramref name="superset"/>.
            </param>
            <param name="superset">
            Kolekce má být nadmnožinou <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testuje, jestli jedna kolekce je podmnožinou jiné kolekce,
            a vyvolá výjimku, pokud libovolný prvek podmnožiny není zároveň
            prvkem nadmnožiny.
            </summary>
            <param name="subset">
            Kolekce, která má být podmnožinou <paramref name="superset"/>.
            </param>
            <param name="superset">
            Kolekce má být nadmnožinou <paramref name="subset"/>
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud prvek v
            <paramref name="subset"/> se nenachází v podmnožině <paramref name="superset"/>.
            Zpráva je zobrazena ve výsledku testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testuje, jestli jedna kolekce je podmnožinou jiné kolekce,
            a vyvolá výjimku, pokud libovolný prvek podmnožiny není zároveň
            prvkem nadmnožiny.
            </summary>
            <param name="subset">
            Kolekce, která má být podmnožinou <paramref name="superset"/>.
            </param>
            <param name="superset">
            Kolekce má být nadmnožinou <paramref name="subset"/>
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud prvek v
            <paramref name="subset"/> se nenachází v podmnožině <paramref name="superset"/>.
            Zpráva je zobrazena ve výsledku testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testuje, jestli jedna z kolekcí není podmnožinou jiné kolekce, a vyvolá
            výjimku, pokud všechny prvky podmnožiny jsou také prvky
            nadmnožiny.
            </summary>
            <param name="subset">
            Kolekce, která nemá být podmnožinou nadmnožiny <paramref name="superset"/>.
            </param>
            <param name="superset">
            Kolekce, která nemá být nadmnožinou podmnožiny <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testuje, jestli jedna z kolekcí není podmnožinou jiné kolekce, a vyvolá
            výjimku, pokud všechny prvky podmnožiny jsou také prvky
            nadmnožiny.
            </summary>
            <param name="subset">
            Kolekce, která nemá být podmnožinou nadmnožiny <paramref name="superset"/>.
            </param>
            <param name="superset">
            Kolekce, která nemá být nadmnožinou podmnožiny <paramref name="subset"/>
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud každý prvek v podmnožině
            <paramref name="subset"/> se nachází také v nadmnožině <paramref name="superset"/>.
            Zpráva je zobrazena ve výsledku testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testuje, jestli jedna z kolekcí není podmnožinou jiné kolekce, a vyvolá
            výjimku, pokud všechny prvky podmnožiny jsou také prvky
            nadmnožiny.
            </summary>
            <param name="subset">
            Kolekce, která nemá být podmnožinou nadmnožiny <paramref name="superset"/>.
            </param>
            <param name="superset">
            Kolekce, která nemá být nadmnožinou podmnožiny <paramref name="subset"/>
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud každý prvek v podmnožině
            <paramref name="subset"/> se nachází také v nadmnožině <paramref name="superset"/>.
            Zpráva je zobrazena ve výsledku testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testuje, jestli dvě kolekce obsahují stejný prvek, a vyvolá
            výjimku, pokud některá z kolekcí obsahuje prvek, který není součástí druhé
            kolekce.
            </summary>
            <param name="expected">
            První kolekce, kterou chcete porovnat. Jedná se o prvek, který test
            očekává.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testuje, jestli dvě kolekce obsahují stejný prvek, a vyvolá
            výjimku, pokud některá z kolekcí obsahuje prvek, který není součástí druhé
            kolekce.
            </summary>
            <param name="expected">
            První kolekce, kterou chcete porovnat. Jedná se o prvek, který test
            očekává.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud byl nalezen prvek
            v jedné z kolekcí, ale ne ve druhé. Zpráva je zobrazena
            ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testuje, jestli dvě kolekce obsahují stejný prvek, a vyvolá
            výjimku, pokud některá z kolekcí obsahuje prvek, který není součástí druhé
            kolekce.
            </summary>
            <param name="expected">
            První kolekce, kterou chcete porovnat. Jedná se o prvek, který test
            očekává.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud byl nalezen prvek
            v jedné z kolekcí, ale ne ve druhé. Zpráva je zobrazena
            ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testuje, jestli dvě kolekce obsahují rozdílné prvky, a vyvolá
            výjimku, pokud tyto dvě kolekce obsahují identické prvky bez ohledu
            na pořadí.
            </summary>
            <param name="expected">
            První kolekce, kterou chcete porovnat. Obsahuje prvek, který se podle testu
            má lišit od skutečné kolekce.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testuje, jestli dvě kolekce obsahují rozdílné prvky, a vyvolá
            výjimku, pokud tyto dvě kolekce obsahují identické prvky bez ohledu
            na pořadí.
            </summary>
            <param name="expected">
            První kolekce, kterou chcete porovnat. Obsahuje prvek, který se podle testu
            má lišit od skutečné kolekce.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            obsahuje stejný prvek jako <paramref name="expected"/>. Zpráva
            je zobrazena ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testuje, jestli dvě kolekce obsahují rozdílné prvky, a vyvolá
            výjimku, pokud tyto dvě kolekce obsahují identické prvky bez ohledu
            na pořadí.
            </summary>
            <param name="expected">
            První kolekce, kterou chcete porovnat. Obsahuje prvek, který se podle testu
            má lišit od skutečné kolekce.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            obsahuje stejný prvek jako <paramref name="expected"/>. Zpráva
            je zobrazena ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type)">
            <summary>
            Testuje, jestli všechny prvky v zadané kolekci jsou instancemi
            očekávaného typu, a vyvolá výjimku, pokud očekávaný typ není
            v hierarchii dědičnosti jednoho nebo více prvků.
            </summary>
            <param name="collection">
            Kolekce obsahující prvky, které podle testu mají být
            zadaného typu.
            </param>
            <param name="expectedType">
            Očekávaný typ jednotlivých prvků <paramref name="collection"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String)">
            <summary>
            Testuje, jestli všechny prvky v zadané kolekci jsou instancemi
            očekávaného typu, a vyvolá výjimku, pokud očekávaný typ není
            v hierarchii dědičnosti jednoho nebo více prvků.
            </summary>
            <param name="collection">
            Kolekce obsahující prvky, které podle testu mají být
            zadaného typu.
            </param>
            <param name="expectedType">
            Očekávaný typ jednotlivých prvků <paramref name="collection"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud prvek v
            <paramref name="collection"/> není instancí typu
            <paramref name="expectedType"/>. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String,System.Object[])">
            <summary>
            Testuje, jestli všechny prvky v zadané kolekci jsou instancemi
            očekávaného typu, a vyvolá výjimku, pokud očekávaný typ není
            v hierarchii dědičnosti jednoho nebo více prvků.
            </summary>
            <param name="collection">
            Kolekce obsahující prvky, které podle testu mají být
            zadaného typu.
            </param>
            <param name="expectedType">
            Očekávaný typ jednotlivých prvků <paramref name="collection"/>.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud prvek v
            <paramref name="collection"/> není instancí typu
            <paramref name="expectedType"/>. Zpráva je zobrazena ve výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testuje, jestli jsou zadané kolekce stejné, a vyvolá výjimku,
            pokud obě kolekce stejné nejsou. Rovnost je definovaná jako množina stejných
            prvků ve stejném pořadí a o stejném počtu. Rozdílné odkazy na stejnou hodnotu
            se považují za stejné.
            </summary>
            <param name="expected">
            První kolekce, kterou chcete porovnat. Jedná se o kolekci, kterou test očekává.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testuje, jestli jsou zadané kolekce stejné, a vyvolá výjimku,
            pokud obě kolekce stejné nejsou. Rovnost je definovaná jako množina stejných
            prvků ve stejném pořadí a o stejném počtu. Rozdílné odkazy na stejnou hodnotu
            se považují za stejné.
            </summary>
            <param name="expected">
            První kolekce, kterou chcete porovnat. Jedná se o kolekci, kterou test očekává.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se nerovná <paramref name="expected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testuje, jestli jsou zadané kolekce stejné, a vyvolá výjimku,
            pokud obě kolekce stejné nejsou. Rovnost je definovaná jako množina stejných
            prvků ve stejném pořadí a o stejném počtu. Rozdílné odkazy na stejnou hodnotu
            se považují za stejné.
            </summary>
            <param name="expected">
            První kolekce, kterou chcete porovnat. Jedná se o kolekci, kterou test očekává.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se nerovná <paramref name="expected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testuje nerovnost zadaných kolekcí a vyvolá výjimku,
            pokud jsou dvě kolekce stejné. Rovnost je definovaná jako množina stejných
            prvků ve stejném pořadí a o stejném počtu. Odlišné odkazy na stejnou
            hodnotu se považují za sobě rovné.
            </summary>
            <param name="notExpected">
            První kolekce, kterou chcete porovnat. Jedná se o kolekci, která podle testu
            nemá odpovídat <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testuje nerovnost zadaných kolekcí a vyvolá výjimku,
            pokud jsou dvě kolekce stejné. Rovnost je definovaná jako množina stejných
            prvků ve stejném pořadí a o stejném počtu. Odlišné odkazy na stejnou
            hodnotu se považují za sobě rovné.
            </summary>
            <param name="notExpected">
            První kolekce, kterou chcete porovnat. Jedná se o kolekci, která podle testu
            nemá odpovídat <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se rovná <paramref name="notExpected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testuje nerovnost zadaných kolekcí a vyvolá výjimku,
            pokud jsou dvě kolekce stejné. Rovnost je definovaná jako množina stejných
            prvků ve stejném pořadí a o stejném počtu. Odlišné odkazy na stejnou
            hodnotu se považují za sobě rovné.
            </summary>
            <param name="notExpected">
            První kolekce, kterou chcete porovnat. Jedná se o kolekci, která podle testu
            nemá odpovídat <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se rovná <paramref name="notExpected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Testuje, jestli jsou zadané kolekce stejné, a vyvolá výjimku,
            pokud obě kolekce stejné nejsou. Rovnost je definovaná jako množina stejných
            prvků ve stejném pořadí a o stejném počtu. Rozdílné odkazy na stejnou hodnotu
            se považují za stejné.
            </summary>
            <param name="expected">
            První kolekce, kterou chcete porovnat. Jedná se o kolekci, kterou test očekává.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <param name="comparer">
            Implementace porovnání, která se má použít pro porovnání prvků kolekce
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Testuje, jestli jsou zadané kolekce stejné, a vyvolá výjimku,
            pokud obě kolekce stejné nejsou. Rovnost je definovaná jako množina stejných
            prvků ve stejném pořadí a o stejném počtu. Rozdílné odkazy na stejnou hodnotu
            se považují za stejné.
            </summary>
            <param name="expected">
            První kolekce, kterou chcete porovnat. Jedná se o kolekci, kterou test očekává.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <param name="comparer">
            Implementace porovnání, která se má použít pro porovnání prvků kolekce
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se nerovná <paramref name="expected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Testuje, jestli jsou zadané kolekce stejné, a vyvolá výjimku,
            pokud obě kolekce stejné nejsou. Rovnost je definovaná jako množina stejných
            prvků ve stejném pořadí a o stejném počtu. Rozdílné odkazy na stejnou hodnotu
            se považují za stejné.
            </summary>
            <param name="expected">
            První kolekce, kterou chcete porovnat. Jedná se o kolekci, kterou test očekává.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <param name="comparer">
            Implementace porovnání, která se má použít pro porovnání prvků kolekce
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, pokud <paramref name="actual"/>
            se nerovná <paramref name="expected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Testuje nerovnost zadaných kolekcí a vyvolá výjimku,
            pokud jsou dvě kolekce stejné. Rovnost je definovaná jako množina stejných
            prvků ve stejném pořadí a o stejném počtu. Odlišné odkazy na stejnou
            hodnotu se považují za sobě rovné.
            </summary>
            <param name="notExpected">
            První kolekce, kterou chcete porovnat. Jedná se o kolekci, která podle testu
            nemá odpovídat <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <param name="comparer">
            Implementace porovnání, která se má použít pro porovnání prvků kolekce
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Testuje nerovnost zadaných kolekcí a vyvolá výjimku,
            pokud jsou dvě kolekce stejné. Rovnost je definovaná jako množina stejných
            prvků ve stejném pořadí a o stejném počtu. Odlišné odkazy na stejnou
            hodnotu se považují za sobě rovné.
            </summary>
            <param name="notExpected">
            První kolekce, kterou chcete porovnat. Jedná se o kolekci, která podle testu
            nemá odpovídat <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <param name="comparer">
            Implementace porovnání, která se má použít pro porovnání prvků kolekce
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, když <paramref name="actual"/>
            se rovná <paramref name="notExpected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Testuje nerovnost zadaných kolekcí a vyvolá výjimku,
            pokud jsou dvě kolekce stejné. Rovnost je definovaná jako množina stejných
            prvků ve stejném pořadí a o stejném počtu. Odlišné odkazy na stejnou
            hodnotu se považují za sobě rovné.
            </summary>
            <param name="notExpected">
            První kolekce, kterou chcete porovnat. Jedná se o kolekci, která podle testu
            nemá odpovídat <paramref name="actual"/>.
            </param>
            <param name="actual">
            Druhá kolekce, kterou chcete porovnat. Jedná se o kolekci vytvořenou
            testovaným kódem.
            </param>
            <param name="comparer">
            Implementace porovnání, která se má použít pro porovnání prvků kolekce
            </param>
            <param name="message">
            Zpráva, kterou chcete zahrnout do výjimky, když <paramref name="actual"/>
            se rovná <paramref name="notExpected"/>. Zpráva je zobrazena ve
            výsledcích testu.
            </param>
            <param name="parameters">
            Pole parametrů, které se má použít při formátování <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOfHelper(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Určuje, jestli první kolekce je podmnožinou druhé
            kolekce. Pokud některá z množin obsahuje duplicitní prvky, musí počet
            výskytů prvku v podmnožině být menší, nebo
            se musí rovnat počtu výskytů v nadmnožině.
            </summary>
            <param name="subset">
            Kolekce, která podle testu má být obsažena v nadmnožině <paramref name="superset"/>.
            </param>
            <param name="superset">
            Kolekce, která podle testu má obsahovat <paramref name="subset"/>.
            </param>
            <returns>
            Pravda, pokud <paramref name="subset"/> je podmnožinou
            <paramref name="superset"/>, jinak nepravda.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.GetElementCounts(System.Collections.ICollection,System.Int32@)">
            <summary>
            Vytvoří slovník obsahující počet výskytů jednotlivých
            prvků v zadané kolekci.
            </summary>
            <param name="collection">
            Kolekce, kterou chcete zpracovat
            </param>
            <param name="nullCount">
            Počet prvků Null v kolekci
            </param>
            <returns>
            Slovník obsahující počet výskytů jednotlivých prvků
            v zadané kolekci.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.FindMismatchedElement(System.Collections.ICollection,System.Collections.ICollection,System.Int32@,System.Int32@,System.Object@)">
            <summary>
            Najde mezi dvěma kolekcemi neshodný prvek. Neshodný
            prvek je takový, který má v očekávané kolekci
            odlišný počet výskytů ve srovnání se skutečnou kolekcí. Kolekce
            se považují za rozdílné reference bez hodnoty null se
            stejným počtem prvků. Za tuto úroveň ověření odpovídá
            volající. Pokud neexistuje žádný neshodný prvek, funkce vrátí
            false a neměli byste použít parametry Out.
            </summary>
            <param name="expected">
            První kolekce, která se má porovnat
            </param>
            <param name="actual">
            Druhá kolekce k porovnání
            </param>
            <param name="expectedCount">
            Očekávaný počet výskytů prvku
            <paramref name="mismatchedElement"/> nebo 0, pokud není žádný nevyhovující
            prvek.
            </param>
            <param name="actualCount">
            Skutečný počet výskytů prvku
            <paramref name="mismatchedElement"/> nebo 0, pokud není žádný nevyhovující
            prvek.
            </param>
            <param name="mismatchedElement">
            Neshodný prvek (může být Null) nebo Null, pokud neexistuje žádný
            neshodný prvek.
            </param>
            <returns>
            pravda, pokud je nalezen nevyhovující prvek; v opačném případě nepravda.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.ObjectComparer">
            <summary>
            Porovná objekt pomocí atributu object.Equals.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException">
            <summary>
            Základní třída pro výjimky architektury
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String,System.Exception)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
            <param name="msg"> Zpráva </param>
            <param name="ex"> Výjimka </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
            <param name="msg"> Zpráva </param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages">
            <summary>
              Třída prostředků se silnými typy pro vyhledávání lokalizovaných řetězců atd.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ResourceManager">
            <summary>
              Vrátí v mezipaměti uloženou instanci ResourceManager použitou touto třídou.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Culture">
            <summary>
              Přepíše vlastnost CurrentUICulture aktuálního vlákna pro všechna
              vyhledávání prostředků pomocí této třídy prostředků silného typu.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AccessStringInvalidSyntax">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Přístupový řetězec má neplatnou syntaxi.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ActualHasMismatchedElements">
            <summary>
              Vyhledá lokalizovaný řetězec podobný tomuto: Očekávaná kolekce obsahuje počet výskytů {1} &lt;{2}&gt;. Skutečná kolekce obsahuje tento počet výskytů: {3}. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AllItemsAreUniqueFailMsg">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Našla se duplicitní položka:&lt;{1}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualCaseFailMsg">
            <summary>
              Vyhledá lokalizovaný řetězec podobný tomuto: Očekáváno:&lt;{1}&gt;. Případ je rozdílný pro skutečnou hodnotu:&lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDeltaFailMsg">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Mezi očekávanou hodnotou &lt;{1}&gt; a skutečnou hodnotou &lt;{2}&gt; se očekává rozdíl maximálně &lt;{3}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDifferentTypesFailMsg">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Očekáváno:&lt;{1} ({2})&gt;. Skutečnost:&lt;{3} ({4})&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualFailMsg">
            <summary>
              Vyhledá řetězec podobný řetězci Očekáváno:&lt;{1}&gt;. Skutečnost:&lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualDeltaFailMsg">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Mezi očekávanou hodnotou &lt;{1}&gt; a skutečnou hodnotou &lt;{2}&gt; se očekával rozdíl větší než &lt;{3}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualFailMsg">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Očekávala se libovolná hodnota s výjimkou:&lt;{1}&gt;. Skutečnost:&lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreSameGivenValues">
            <summary>
              Vyhledá lokalizovaný řetězec podobný tomuto: Nevkládejte hodnotu typů do AreSame(). Hodnoty převedené na typ Object nebudou nikdy stejné. Zvažte možnost použít AreEqual(). {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AssertionFailed">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Chyba {0}. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AsyncUITestMethodNotSupported">
            <summary>
              Vyhledá lokalizovaný řetězec podobný tomuto: async TestMethod s atributem UITestMethodAttribute se nepodporují. Buď odeberte async, nebo použijte TestMethodAttribute.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsEmpty">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Obě kolekce jsou prázdné. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameElements">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Obě kolekce obsahují stejný prvek.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameReference">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Obě reference kolekce odkazují na stejný objekt kolekce. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothSameElements">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Obě kolekce obsahují stejné prvky. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.CollectionEqualReason">
            <summary>
              Vyhledá řetězec podobný řetězci {0}({1}).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_NullInMessages">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci (null).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_ObjectString">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci (objekt).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ContainsFail">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Řetězec {0} neobsahuje řetězec {1}. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DataDrivenResultDisplayName">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci {0} ({1}).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DoNotUseAssertEquals">
            <summary>
              Vyhledá lokalizovaný řetězec podobný tomuto: Atribut Assert.Equals by se neměl používat pro kontrolní výrazy. Použijte spíše Assert.AreEqual a přetížení.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementNumbersDontMatch">
            <summary>
              Vyhledá lokalizovaný řetězec podobný tomuto: Počet prvků v kolekci se neshoduje. Očekáváno:&lt;{1}&gt;. Skutečnost:&lt;{2}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementsAtIndexDontMatch">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Prvek indexu {0} se neshoduje.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch">
            <summary>
              Vyhledá lokalizovaný řetězec podobný tomuto: Prvek indexu {1} je neočekávaného typu. Očekávaný typ:&lt;{2}&gt;. Skutečný typ:&lt;{3}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch2">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Prvek indexu {1} je (null). Očekávaný typ:&lt;{2}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EndsWithFail">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Řetězec {0} nekončí řetězcem {1}. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EqualsTesterInvalidArgs">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Neplatný argument: EqualsTester nemůže použít hodnoty null.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ErrorInvalidCast">
            <summary>
              Vyhledá řetězec podobný řetězci Nejde převést objekt typu {0} na {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InternalObjectNotValid">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Interní odkazovaný objekt už není platný.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidParameterToAssert">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Parametr {0} je neplatný. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidPropertyType">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Vlastnost {0} má typ {1}; očekávaný typ {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsInstanceOfFailMsg">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci {0} Očekávaný typ:&lt;{1}&gt;. Skutečný typ:&lt;{2}&gt;.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsMatchFail">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Řetězec {0} se neshoduje se vzorkem {1}. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotInstanceOfFailMsg">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Nesprávný typ:&lt;{1}&gt;. Skutečný typ:&lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotMatchFail">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Řetězec {0} se shoduje se vzorkem {1}. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoDataRow">
            <summary>
              Vyhledá lokalizovaný řetězec podobný tomuto: Nezadal se žádný atribut DataRowAttribute. K atributu DataTestMethodAttribute se vyžaduje aspoň jeden atribut DataRowAttribute.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoExceptionThrown">
            <summary>
              Vyhledá lokalizovaný řetězec podobný tomuto: Nevyvolala se žádná výjimka. Očekávala se výjimka {1}. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NullParameterToAssert">
            <summary>
              Vyhledá lokalizované řetězce podobné tomuto: Parametr {0} je neplatný. Hodnota nemůže být null. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NumberOfElementsDiff">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Rozdílný počet prvků.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorConstructorNotFound">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci 
                 Konstruktor se zadaným podpisem se nenašel. Pravděpodobně budete muset obnovit privátní přístupový objekt,
                 nebo je člen pravděpodobně privátní a založený na základní třídě. Pokud je pravdivý druhý zmíněný případ, musíte vložit typ
                 definující člen do konstruktoru objektu PrivateObject.
               
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorMemberNotFound">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci 
                 Zadaný člen ({0}) se nenašel. Pravděpodobně budete muset obnovit privátní přístupový objekt,
                 nebo je člen pravděpodobně privátní a založený na základní třídě. Pokud je pravdivý druhý zmíněný případ, musíte vložit typ
                 definující člen do konstruktoru atributu PrivateObject.
               
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.StartsWithFail">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Řetězec {0} nezačíná řetězcem {1}. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_ExpectedExceptionTypeMustDeriveFromException">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Očekávaný typ výjimky musí být System.Exception nebo typ odvozený od System.Exception.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_FailedToGetExceptionMessage">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci (Z důvodu výjimky se nepodařilo získat zprávu pro výjimku typu {0}.).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoException">
            <summary>
              Vyhledá lokalizovaný řetězec podobný řetězci Testovací metoda nevyvolala očekávanou výjimku {0}. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoExceptionDefault">
            <summary>
              Vyhledá lokalizovaný řetězec podobný tomuto: Testovací metoda nevyvolala výjimku. Atribut {0} definovaný testovací metodou očekával výjimku.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongException">
            <summary>
              Vyhledá lokalizovaný řetězec podobný tomuto: Testovací metoda vyvolala výjimku {0}, ale očekávala se výjimka {1}. Zpráva o výjimce: {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongExceptionDerivedAllowed">
            <summary>
              Vyhledá lokalizovaný řetězec podobný tomuto: Testovací metoda vyvolala výjimku {0}, očekávala se ale odvozená výjimka {1} nebo typ. Zpráva o výjimce: {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.WrongExceptionThrown">
             <summary>
               Vyhledá lokalizovaný řetězec podobný tomuto: Vyvolala se výjimka {2}, ale očekávala se výjimka {1}. {0}
            Zpráva o výjimce: {3}
            Trasování zásobníku: {4}
             </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome">
            <summary>
            Výsledky testu jednotek
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Failed">
            <summary>
            Test se provedl, ale došlo k problémům.
            Problémy se můžou týkat výjimek nebo neúspěšných kontrolních výrazů.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Inconclusive">
            <summary>
            Test se dokončil, ale není možné zjistit, jestli byl úspěšný, nebo ne.
            Dá se použít pro zrušené testy.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Passed">
            <summary>
            Test se provedl zcela bez problémů.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.InProgress">
            <summary>
            V tuto chvíli probíhá test.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Error">
            <summary>
            Při provádění testu došlo k chybě systému.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Timeout">
            <summary>
            Časový limit testu vypršel.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Aborted">
            <summary>
            Test byl zrušen uživatelem.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Unknown">
            <summary>
            Test je v neznámém stavu.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper">
            <summary>
            Poskytuje pomocnou funkci pro systém pro testy jednotek.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper.GetExceptionMsg(System.Exception)">
            <summary>
            Rekurzivně získá zprávy o výjimce, včetně zpráv pro všechny vnitřní
            výjimky.
            </summary>
            <param name="ex">Výjimka pro načítání zpráv pro</param>
            <returns>řetězec s informacemi v chybové zprávě</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout">
            <summary>
            Výčet pro časové limity, který se dá použít spolu s třídou <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/>.
            Typ výčtu musí odpovídat
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout.Infinite">
            <summary>
            Nekonečno
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute">
            <summary>
            Atribut třídy testu
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute.GetTestMethodAttribute(Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute)">
            <summary>
            Získá atribut testovací metody, který umožní spustit tento test.
            </summary>
            <param name="testMethodAttribute">Instance atributu testovací metody definované v této metodě.</param>
            <returns>Typ <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/> Použije se ke spuštění tohoto testu.</returns>
            <remarks>Extensions can override this method to customize how all methods in a class are run.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute">
            <summary>
            Atribut testovací metody
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Spustí testovací metodu.
            </summary>
            <param name="testMethod">Testovací metoda, která se má spustit.</param>
            <returns>Pole objektů TestResult, které představuje výsledek (nebo výsledky) daného testu.</returns>
            <remarks>Extensions can override this method to customize running a TestMethod.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestInitializeAttribute">
            <summary>
            Atribut inicializace testu
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCleanupAttribute">
            <summary>
            Atribut vyčištění testu
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute">
            <summary>
            Atribut ignore
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute">
            <summary>
            Atribut vlastnosti testu
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.#ctor(System.String,System.String)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute"/>.
            </summary>
            <param name="name">
            Název
            </param>
            <param name="value">
            Hodnota
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Name">
            <summary>
            Získá název.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Value">
            <summary>
            Získá hodnotu.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute">
            <summary>
            Atribut inicializace třídy
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute">
            <summary>
            Atribut vyčištění třídy
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyInitializeAttribute">
            <summary>
            Atribut inicializace sestavení
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyCleanupAttribute">
            <summary>
            Atribut vyčištění sestavení
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute">
            <summary>
            Vlastník testu
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.#ctor(System.String)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute"/>.
            </summary>
            <param name="owner">
            Vlastník
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.Owner">
            <summary>
            Získá vlastníka.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute">
            <summary>
            Atribut priority, používá se pro určení priority testu jednotek.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.#ctor(System.Int32)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute"/>.
            </summary>
            <param name="priority">
            Priorita
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.Priority">
            <summary>
            Získá prioritu.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute">
            <summary>
            Popis testu
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.#ctor(System.String)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute"/>, která popíše test.
            </summary>
            <param name="description">Popis</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.Description">
            <summary>
            Získá popis testu.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute">
            <summary>
            Identifikátor URI struktury projektů CSS
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.#ctor(System.String)">
            <summary>
             Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute"/> pro identifikátor URI struktury projektů CSS.
            </summary>
            <param name="cssProjectStructure">Identifikátor URI struktury projektů CSS</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.CssProjectStructure">
            <summary>
            Získá identifikátor URI struktury projektů CSS.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute">
            <summary>
            Identifikátor URI iterace CSS
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.#ctor(System.String)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute"/> pro identifikátor URI iterace CSS.
            </summary>
            <param name="cssIteration">Identifikátor URI iterace CSS</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.CssIteration">
            <summary>
            Získá identifikátor URI iterace CSS.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute">
            <summary>
            Atribut WorkItem, používá se pro zadání pracovní položky přidružené k tomuto testu.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.#ctor(System.Int32)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute"/> pro atribut WorkItem.
            </summary>
            <param name="id">ID pro pracovní položku</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.Id">
            <summary>
            Získá ID k přidružené pracovní položce.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute">
            <summary>
            Atribut časového limitu, používá se pro zadání časového limitu testu jednotek.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(System.Int32)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/>.
            </summary>
            <param name="timeout">
            Časový limit
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> s předem nastaveným časovým limitem.
            </summary>
            <param name="timeout">
            Časový limit
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.Timeout">
            <summary>
            Získá časový limit.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult">
            <summary>
            Objekt TestResult, který se má vrátit adaptéru
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.#ctor">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DisplayName">
            <summary>
            Získá nebo nastaví zobrazovaný název výsledku. Vhodné pro vrácení většího počtu výsledků.
            Pokud je null, jako DisplayName se použije název metody.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Outcome">
            <summary>
            Získá nebo nastaví výsledek provedení testu.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestFailureException">
            <summary>
            Získá nebo nastaví výjimku vyvolanou při chybě testu.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogOutput">
            <summary>
            Získá nebo nastaví výstup zprávy zaprotokolované testovacím kódem.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogError">
            <summary>
            Získá nebo nastaví výstup zprávy zaprotokolované testovacím kódem.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DebugTrace">
            <summary>
            Získá nebo načte trasování ladění testovacího kódu.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestContextMessages">
            <summary>
            Gets or sets the debug traces by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Duration">
            <summary>
            Získá nebo nastaví délku trvání testu.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DatarowIndex">
            <summary>
            Získá nebo nastaví index řádku dat ve zdroji dat. Nastavte pouze pro výsledky jednoho
            spuštění řádku dat v testu řízeném daty.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ReturnValue">
            <summary>
            Získá nebo nastaví návratovou hodnotu testovací metody. (Aktuálně vždy null)
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ResultFiles">
            <summary>
            Získá nebo nastaví soubory s výsledky, které připojil test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute">
            <summary>
            Určuje připojovací řetězec, název tabulky a metodu přístupu řádku pro testování řízené daty.
            </summary>
            <example>
            [DataSource("Provider=SQLOLEDB.1;Data Source=source;Integrated Security=SSPI;Initial Catalog=EqtCoverage;Persist Security Info=False", "MyTable")]
            [DataSource("dataSourceNameFromConfigFile")]
            </example>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultProviderName">
            <summary>
            Název výchozího poskytovatele pro DataSource
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultDataAccessMethod">
            <summary>
            Výchozí metoda pro přístup k datům
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String,System.String,Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>. Tato instance se inicializuje s poskytovatelem dat, připojovacím řetězcem, tabulkou dat a přístupovou metodou k datům, pomocí kterých se získá přístup ke zdroji dat.
            </summary>
            <param name="providerInvariantName">Název poskytovatele neutrálních dat, jako je System.Data.SqlClient</param>
            <param name="connectionString">
            Připojovací řetězec specifický pro poskytovatele dat.
            UPOZORNĚNÍ: Připojovací řetězec může obsahovat citlivé údaje (třeba heslo).
            Připojovací řetězec se ukládá v podobě prostého textu ve zdrojovém kódu a v kompilovaném sestavení.
            Tyto citlivé údaje zabezpečíte omezením přístupu ke zdrojovému kódu a sestavení.
            </param>
            <param name="tableName">Název tabulky dat</param>
            <param name="dataAccessMethod">Určuje pořadí přístupu k datům.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>. Tato instance se inicializuje s připojovacím řetězcem a názvem tabulky.
            Zadejte připojovací řetězec a tabulku dat, pomocí kterých se získá přístup ke zdroji dat OLEDB.
            </summary>
            <param name="connectionString">
            Připojovací řetězec specifický pro poskytovatele dat.
            UPOZORNĚNÍ: Připojovací řetězec může obsahovat citlivé údaje (třeba heslo).
            Připojovací řetězec se ukládá v podobě prostého textu ve zdrojovém kódu a v kompilovaném sestavení.
            Tyto citlivé údaje zabezpečíte omezením přístupu ke zdrojovému kódu a sestavení.
            </param>
            <param name="tableName">Název tabulky dat</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>. Tato instance se inicializuje s poskytovatelem dat a připojovacím řetězcem přidruženým k názvu nastavení.
            </summary>
            <param name="dataSourceSettingName">Název zdroje dat nalezený v oddílu &lt;microsoft.visualstudio.qualitytools&gt; souboru app.config.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ProviderInvariantName">
            <summary>
            Získá hodnotu představující poskytovatele dat zdroje dat.
            </summary>
            <returns>
            Název poskytovatele dat. Pokud poskytovatel dat nebyl při inicializaci objektu zadán, bude vrácen výchozí poskytovatel System.Data.OleDb.
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ConnectionString">
            <summary>
            Získá hodnotu představující připojovací řetězec zdroje dat.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.TableName">
            <summary>
            Získá hodnotu označující název tabulky poskytující data.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataAccessMethod">
             <summary>
             Získá metodu používanou pro přístup ke zdroji dat.
             </summary>
            
             <returns>
             Jedna z těchto položek: <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod"/>. Pokud <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> není inicializován, vrátí výchozí hodnotu <see cref="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random"/>.
             </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataSourceSettingName">
            <summary>
            Získá název zdroje dat nalezeného v části &lt;microsoft.visualstudio.qualitytools&gt; v souboru app.config.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute">
            <summary>
            Atribut testu řízeného daty, kde se data dají zadat jako vložená.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Vyhledá všechny datové řádky a spustí je.
            </summary>
            <param name="testMethod">
            Testovací metoda
            </param>
            <returns>
            Pole <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.RunDataDrivenTest(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod,Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute[])">
            <summary>
            Spustí testovací metodu řízenou daty.
            </summary>
            <param name="testMethod"> Testovací metoda, kterou chcete provést. </param>
            <param name="dataRows"> Datový řádek </param>
            <returns> Výsledek provedení </returns>
        </member>
    </members>
</doc>
