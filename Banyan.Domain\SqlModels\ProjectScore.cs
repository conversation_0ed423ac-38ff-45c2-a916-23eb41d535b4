using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public partial class ProjectScore
    {
        public ProjectScore() { }

        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        [SqlField]
        public int UserId { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        [SqlField]
        public int ProjectId { get; set; }

        /// <summary>
        /// 场次ID
        /// </summary>
        [SqlField]
        public int StageId { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        [SqlField]
        public decimal Score { get; set; }

        /// <summary>
        /// 添加时间
        /// </summary>
        [SqlField]
        public DateTime AddTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 用户名
        /// </summary>
        [SqlField]
        public string UserName { get; set; }
    }
}
