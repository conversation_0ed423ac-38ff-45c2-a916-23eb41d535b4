﻿html {
    height: 100%;
    margin: 0 auto !important;
    position: relative;
    overflow-y: scroll;
    -webkit-tap-highlight-color: transparent;
    overflow: hidden;
}

#app {
    font-size: 13px;
}

.page-bd_scroll {
    position: absolute;
    overflow-x: hidden;
    width: 100%;
    top: 0;
    left: 0;
    bottom: 0;
    font-size: 15px;
}

.page-bd_fixedbt{
    padding-bottom: 50px;
}

.weui-flex {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
}

.weui-flex__item {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
}

.weui-table_tr {
    padding: 12px 10px;
    border-top: 1px solid #f3f3f3;
}

.weui-table_hd {
    font-size: 15px;
}

.weui-table_tr .weui-table_td {
    width: 115px;
    padding-right: 8px;
    font-weight: bold;
}

.weui-cells__title {
    margin-top: .77em;
    margin-bottom: .3em;
    padding-left: 15px;
    padding-right: 15px;
    color: #808080;
    font-size: 14px;
}

.weui-cell {
    padding: 10px 15px;
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
}

    .weui-cell:first-child:before {
        display: none;
    }

.weui-cell_active {
    background-color: #ececec;
}

.weui-cell_primary {
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    align-items: flex-start;
}

.weui-cell__bd {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
}

.weui-cell__ft {
    text-align: right;
    color: #999;
}

.weui-cell_access {
    color: inherit;
}

.weui-cell__ft_in-access {
    padding-right: 13px;
    position: relative;
}

    .weui-cell__ft_in-access:after {
        content: " ";
        display: inline-block;
        height: 8px;
        width: 8px;
        border-width: 1px 1px 0 0;
        border-color: #c8c8cd;
        border-style: solid;
        -webkit-transform: matrix(0.71, 0.71, -.71, 0.71, 0, 0);
        transform: matrix(0.71, 0.71, -.71, 0.71, 0, 0);
        top: -2px;
        position: absolute;
        top: 50%;
        margin-top: -4px;
        right: 2px;
    }

.weui-cell_link {
    color: #586c94;
    font-size: 14px;
}

    .weui-cell_link:active {
        background-color: #ececec;
    }

    .weui-cell_link:first-child:before {
        display: block;
    }

.air-name {
    font-size: 14px;
}

.air-time {
    font-size: 13px;
    color: #b2b2b2;
    padding-bottom: 5px;
}

.airbubble {
    background: #f3f3f3;
    color: #353535;
    font-size: 14px;
    padding: 10px;
    border-radius: 15px;
    border-top-left-radius: 0;
    -moz-box-shadow: 0px 0px 3px rgba(0, 255, 178, 0.1);
    -webkit-box-shadow: 0px 0px 3px rgba(0, 255, 178, 0.1);
    box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.1);
}

.box-reply_expert {
    -webkit-box-align: start;
    -webkit-align-items: start;
    align-items: start;
    padding: 12px 10px;
}

.page-ft .box-reply_expert {
    border-top: 1px solid #f3f3f3;
}

.box-reply_expert.right .air-name {
    text-align: right;
}

.box-reply_expert.right .airbubble {
    border-radius: 20px;
    border-top-right-radius: 0;
}

.box-avatar_mini {
    width: 36px;
    height: 36px;
    margin-right: 5px;
    border: 3px solid #f3f3f3;
    border-radius: 50%;
}

.section-comment {
  position: fixed;
  height: 50px;
  bottom: 0;
  width: 100%;
  background: #fff;
  box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.15);
}

.section-comment .section-comment-box {
  margin: 0 10px;
}

.section-comment .section-comment_opt {
  width: 100%;
}

.section-comment .weui-flex__item {
  margin-top: 14px;
}

.section-comment_input {
    font-size: 15px;
    color: #353535;
    opacity: 0.8;
    width: 100%;
    border: none;
    background: none;
    width: 100%;
    height: 26px;
    font-size: 14px;
}

.section-comment__send {
    height: 100%;
    padding: 0 7px;
    cursor:pointer;
    margin:auto;
}
