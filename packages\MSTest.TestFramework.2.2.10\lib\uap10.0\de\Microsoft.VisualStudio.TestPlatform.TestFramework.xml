<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod">
            <summary>
            TestMethod für die Ausführung.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestMethodName">
            <summary>
            Ruft den Namen der Testmethode ab.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestClassName">
            <summary>
            Ruft den Namen der Testklasse ab.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ReturnType">
            <summary>
            Ruft den Rückgabetyp der Testmethode ab.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ParameterTypes">
            <summary>
            Ruft die Parameter der Testmethode ab.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.MethodInfo">
            <summary>
            Ruft die methodInfo der Testmethode ab.
            </summary>
            <remarks>
            This is just to retrieve additional information about the method.
            Do not directly invoke the method using MethodInfo. Use ITestMethod.Invoke instead.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.Invoke(System.Object[])">
            <summary>
            Ruft die Testmethode auf.
            </summary>
            <param name="arguments">
            An die Testmethode zu übergebende Argumente (z. B. für datengesteuerte Tests).
            </param>
            <returns>
            Das Ergebnis des Testmethodenaufrufs.
            </returns>
            <remarks>
            This call handles asynchronous test methods as well.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAllAttributes(System.Boolean)">
            <summary>
            Ruft alle Attribute der Testmethode ab.
            </summary>
            <param name="inherit">
            Gibt an, ob das in der übergeordneten Klasse definierte Attribut gültig ist.
            </param>
            <returns>
            Alle Attribute.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAttributes``1(System.Boolean)">
            <summary>
            Ruft ein Attribut eines bestimmten Typs ab.
            </summary>
            <typeparam name="AttributeType"> System.Attribute type. </typeparam>
            <param name="inherit">
            Gibt an, ob das in der übergeordneten Klasse definierte Attribut gültig ist.
            </param>
            <returns>
            Die Attribute des angegebenen Typs.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Helper">
            <summary>
            Das Hilfsprogramm.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNull(System.Object,System.String,System.String)">
            <summary>
            Der check-Parameter ungleich null.
            </summary>
            <param name="param">
            Der Parameter.
            </param>
            <param name="parameterName">
            Der Parametername.
            </param>
            <param name="message">
            Die Meldung.
            </param>
            <exception cref="T:System.ArgumentNullException"> Throws argument null exception when parameter is null. </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNullOrEmpty(System.String,System.String,System.String)">
            <summary>
            Der check-Parameter ungleich null oder leer.
            </summary>
            <param name="param">
            Der Parameter.
            </param>
            <param name="parameterName">
            Der Parametername.
            </param>
            <param name="message">
            Die Meldung.
            </param>
            <exception cref="T:System.ArgumentException"> Throws ArgumentException when parameter is null. </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod">
            <summary>
            Enumeration für die Art des Zugriffs auf Datenzeilen in datengesteuerten Tests.
       </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Sequential">
            <summary>
            Zeilen werden in sequenzieller Reihenfolge zurückgegeben.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random">
            <summary>
            Zeilen werden in zufälliger Reihenfolge zurückgegeben.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute">
            <summary>
            Attribut zum Definieren von Inlinedaten für eine Testmethode.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/>-Klasse.
            </summary>
            <param name="data1"> Das Datenobjekt. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object,System.Object[])">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/>-Klasse, die ein Array aus Argumenten akzeptiert.
            </summary>
            <param name="data1"> Ein Datenobjekt. </param>
            <param name="moreData"> Weitere Daten. </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.Data">
            <summary>
            Ruft Daten für den Aufruf der Testmethode ab.
          </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.DisplayName">
            <summary>
            Ruft den Anzeigenamen in den Testergebnissen für die Anpassung ab.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            <summary>
            Die nicht eindeutige Assert-Ausnahme.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String,System.Exception)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>-Klasse.
            </summary>
            <param name="msg"> Die Meldung. </param>
            <param name="ex"> Die Ausnahme. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>-Klasse.
            </summary>
            <param name="msg"> Die Meldung. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>-Klasse.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException">
            <summary>
            Die InternalTestFailureException-Klasse. Wird zum Angeben eines internen Fehlers für einen Testfall verwendet.
            </summary>
            <remarks>
            This class is only added to preserve source compatibility with the V1 framework.
            For all practical purposes either use AssertFailedException/AssertInconclusiveException.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String,System.Exception)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>-Klasse.
            </summary>
            <param name="msg"> Die Ausnahmemeldung. </param>
            <param name="ex"> Die Ausnahme. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>-Klasse.
            </summary>
            <param name="msg"> Die Ausnahmemeldung. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>-Klasse.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute">
            <summary>
            Ein Attribut, das angibt, dass eine Ausnahme des angegebenen Typs erwartet wird
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/>-Klasse mit dem erwarteten Typ
            </summary>
            <param name="exceptionType">Der Typ der erwarteten Ausnahme.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type,System.String)">
            <summary>
            Initialisiert eine neue Instanz der<see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/>-Klasse mit
            dem erwarteten Typ und der einzuschließenden Meldung, wenn vom Test keine Ausnahme ausgelöst wurde.
            </summary>
            <param name="exceptionType">Der Typ der erwarteten Ausnahme.</param>
            <param name="noExceptionMessage">
            Die Meldung, die in das Testergebnis eingeschlossen werden soll, wenn beim Test ein Fehler auftritt, weil keine Ausnahme ausgelöst wird.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.ExceptionType">
            <summary>
            Ruft einen Wert ab, der den Typ der erwarteten Ausnahme angibt.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.AllowDerivedTypes">
            <summary>
            Ruft einen Wert ab, der angibt, ob es zulässig ist, dass vom Typ der erwarteten Ausnahme abgeleitete Typen
            als erwartet qualifiziert werden.
          </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.NoExceptionMessage">
            <summary>
            Ruft die Meldung ab, die dem Testergebnis hinzugefügt werden soll, falls beim Test ein Fehler auftritt, weil keine Ausnahme ausgelöst wird.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.Verify(System.Exception)">
            <summary>
            Überprüft, ob der Typ der vom Komponententest ausgelösten Ausnahme erwartet wird.
            </summary>
            <param name="exception">Die vom Komponententest ausgelöste Ausnahme.</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute">
            <summary>
            Basisklasse für Attribute, die angeben, dass eine Ausnahme aus einem Komponententest erwartet wird.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/>-Klasse mit einer standardmäßigen "no-exception"-Meldung.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor(System.String)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/>-Klasse mit einer 2no-exception"-Meldung
            </summary>
            <param name="noExceptionMessage">
            Die Meldung, die in das Testergebnis eingeschlossen werden soll, wenn beim Test ein Fehler auftritt,
            weil keine Ausnahme ausgelöst wird.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.NoExceptionMessage">
            <summary>
            Ruft die Meldung ab, die dem Testergebnis hinzugefügt werden soll, falls beim Test ein Fehler auftritt, weil keine Ausnahme ausgelöst wird.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.SpecifiedNoExceptionMessage">
            <summary>
            Ruft die Meldung ab, die dem Testergebnis hinzugefügt werden soll, falls beim Test ein Fehler auftritt, weil keine Ausnahme ausgelöst wird.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.GetDefaultNoExceptionMessage(System.String)">
            <summary>
            Ruft die standardmäßige Nichtausnahmemeldung ab.
            </summary>
            <param name="expectedExceptionAttributeTypeName">Der Typname des ExpectedException-Attributs.</param>
            <returns>Die standardmäßige Nichtausnahmemeldung.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.Verify(System.Exception)">
            <summary>
            Ermittelt, ob die Annahme erwartet ist. Wenn die Methode zurückkehrt, wird davon ausgegangen,
            dass die Annahme erwartet war. Wenn die Methode eine Ausnahme auslöst,
            wird davon ausgegangen, dass die Ausnahme nicht erwartet war, und die Meldung
            der ausgelösten Ausnahme wird in das Testergebnis eingeschlossen. Die <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert"/>-Klasse wird aus Gründen der
            Zweckmäßigkeit bereitgestellt. Wenn <see cref="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive"/> verwendet wird und ein Fehler der Assertion auftritt,
            wird das Testergebnis auf Inconclusive festgelegt.
            </summary>
            <param name="exception">Die vom Komponententest ausgelöste Ausnahme.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.RethrowIfAssertException(System.Exception)">
            <summary>
            Löst die Ausnahme erneut aus, wenn es sich um eine AssertFailedException oder eine AssertInconclusiveException handelt.
            </summary>
            <param name="exception">Die Ausnahme, die erneut ausgelöst werden soll, wenn es sich um eine Assertionausnahme handelt.</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper">
            <summary>
            Diese Klasse unterstützt Benutzer beim Ausführen von Komponententests für Typen, die generische Typen verwenden.
            GenericParameterHelper erfüllt einige allgemeine generische Typeinschränkungen,
            beispielsweise:
            1. öffentlicher Standardkonstruktor
            2. implementiert allgemeine Schnittstellen: IComparable, IEnumerable
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>-Klasse, die
            die Einschränkung "newable" in C#-Generika erfüllt.
            </summary>
            <remarks>
            This constructor initializes the Data property to a random value.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor(System.Int32)">
            <summary>
            Initialisiert eine neue Instanz der<see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>-Klasse, die
            die Data-Eigenschaft mit einem vom Benutzer bereitgestellten Wert initialisiert.
            </summary>
            <param name="data">Ein Integerwert</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Data">
            <summary>
            Ruft die Daten ab oder legt sie fest.
          </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Equals(System.Object)">
            <summary>
            Führt den Wertvergleich für zwei GenericParameterHelper-Objekte aus.
            </summary>
            <param name="obj">Das Objekt, mit dem der Vergleich ausgeführt werden soll.</param>
            <returns>TRUE, wenn das Objekt den gleichen Wert wie "dieses" GenericParameterHelper-Objekt aufweist.
            Andernfalls FALSE.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetHashCode">
            <summary>
            Gibt einen Hashcode für diese Objekt zurück.
            </summary>
            <returns>Der Hash.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.CompareTo(System.Object)">
            <summary>
            Vergleicht die Daten der beiden <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>-Objekte.
            </summary>
            <param name="obj">Das Objekt, mit dem verglichen werden soll.</param>
            <returns>
            Eine signierte Zahl, die die relativen Werte dieser Instanz und dieses Werts angibt.
            </returns>
            <exception cref="T:System.NotSupportedException">
            Thrown when the object passed in is not an instance of <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetEnumerator">
            <summary>
            Gibt ein IEnumerator-Objekt zurück, dessen Länge aus
            der Data-Eigenschaft abgeleitet ist.
            </summary>
            <returns>Das IEnumerator-Objekt</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Clone">
            <summary>
            Gibt ein GenericParameterHelper-Objekt zurück, das gleich
           dem aktuellen Objekt ist.
            </summary>
            <returns>Das geklonte Objekt.</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger">
            <summary>
            Ermöglicht Benutzern das Protokollieren/Schreiben von Ablaufverfolgungen aus Komponententests für die Diagnose.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessageHandler">
            <summary>
            Handler für LogMessage.
            </summary>
            <param name="message">Die zu protokollierende Meldung.</param>
        </member>
        <member name="E:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.OnLogMessage">
            <summary>
            Zu überwachendes Ereignis. Wird ausgelöst, wenn der Komponententestwriter eine Meldung schreibt.
            Wird hauptsächlich von Adaptern verwendet.
           </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessage(System.String,System.Object[])">
            <summary>
            Vom Testwriter aufzurufende API zum Protokollieren von Meldungen.
            </summary>
            <param name="format">Das Zeichenfolgenformat mit Platzhaltern.</param>
            <param name="args">Parameter für Platzhalter.</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute">
            <summary>
            Das TestCategory-Attribut. Wird zum Angeben der Kategorie eines Komponententests verwendet.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.#ctor(System.String)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute"/>-Klasse und wendet die Kategorie auf den Test an.
            </summary>
            <param name="testCategory">
            Die test-Kategorie.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.TestCategories">
            <summary>
            Ruft die Testkategorien ab, die auf den Test angewendet wurden.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute">
            <summary>
            Die Basisklasse für das Category-Attribut.
            </summary>
            <remarks>
            The reason for this attribute is to let the users create their own implementation of test categories.
            - test framework (discovery, etc) deals with TestCategoryBaseAttribute.
            - The reason that TestCategories property is a collection rather than a string,
              is to give more flexibility to the user. For instance the implementation may be based on enums for which the values can be OR'ed
              in which case it makes sense to have single attribute rather than multiple ones on the same test.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.#ctor">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute"/>-Klasse.
            Wendet die Kategorie auf den Test an. Die von TestCategories
            zurückgegebenen Zeichenfolgen werden mit dem Befehl "/category" zum Filtern von Tests verwendet.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.TestCategories">
            <summary>
            Ruft die Testkategorie ab, die auf den Test angewendet wurde.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            <summary>
            Die AssertFailedException-Klasse. Wird zum Angeben eines Fehlers für einen Testfall verwendet.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>-Klasse.
            </summary>
            <param name="msg"> Die Meldung. </param>
            <param name="ex"> Die Ausnahme. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>-Klasse.
            </summary>
            <param name="msg"> Die Meldung. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>-Klasse.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert">
            <summary>
            Eine Sammlung von Hilfsklassen zum Testen verschiedener Bedingungen in
            Komponententests. Wenn die getestete Bedingung nicht erfüllt wird, wird eine Ausnahme
            ausgelöst.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.That">
            <summary>
            Ruft die Singleton-Instanz der Assert-Funktionalität ab.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void IsOfType&lt;T&gt;(this Assert assert, object obj)"
            Users could then use a syntax similar to the default assertions which in this case is "Assert.That.IsOfType&lt;Dog&gt;(animal);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean)">
            <summary>
            Testet, ob die angegebene Bedingung TRUE ist, und löst eine Ausnahme aus,
            wenn die Bedingung FALSE ist.
            </summary>
            <param name="condition">
            Die Bedingung, von der der Test erwartet, dass sie TRUE ist.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String)">
            <summary>
            Testet, ob die angegebene Bedingung TRUE ist, und löst eine Ausnahme aus,
            wenn die Bedingung FALSE ist.
            </summary>
            <param name="condition">
            Die Bedingung, von der der Test erwartet, dass sie TRUE ist.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="condition"/>
            FALSE ist. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebene Bedingung TRUE ist, und löst eine Ausnahme aus,
            wenn die Bedingung FALSE ist.
            </summary>
            <param name="condition">
            Die Bedingung, von der der Test erwartet, dass sie TRUE ist.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="condition"/>
            FALSE ist. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean)">
            <summary>
            Testet, ob die angegebene Bedingung FALSE ist, und löst eine Ausnahme aus,
            wenn die Bedingung TRUE ist.
            </summary>
            <param name="condition">
            Die Bedingung, von der der Test erwartet, dass sie FALSE ist.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String)">
            <summary>
            Testet, ob die angegebene Bedingung FALSE ist, und löst eine Ausnahme aus,
            wenn die Bedingung TRUE ist.
            </summary>
            <param name="condition">
            Die Bedingung, von der der Test erwartet, dass sie FALSE ist.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="condition"/>
            ist TRUE. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebene Bedingung FALSE ist, und löst eine Ausnahme aus,
            wenn die Bedingung TRUE ist.
            </summary>
            <param name="condition">
            Die Bedingung, von der der Test erwartet, dass sie FALSE ist.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="condition"/>
            ist TRUE. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object)">
            <summary>
            Testet, ob das angegebene Objekt NULL ist, und löst eine Ausnahme aus,
            wenn dies nicht der Fall ist.
          </summary>
            <param name="value">
            Das Objekt, von dem der Test erwartet, dass es NULL ist.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String)">
            <summary>
            Testet, ob das angegebene Objekt NULL ist, und löst eine Ausnahme aus,
            wenn dies nicht der Fall ist.
            </summary>
            <param name="value">
            Das Objekt, von dem der Test erwartet, dass es NULL ist.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="value"/>
            ist nicht NULL. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String,System.Object[])">
            <summary>
            Testet, ob das angegebene Objekt NULL ist, und löst eine Ausnahme aus,
            wenn dies nicht der Fall ist.
            </summary>
            <param name="value">
            Das Objekt, von dem der Test erwartet, dass es NULL ist.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="value"/>
            ist nicht NULL. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object)">
            <summary>
            Testet, ob das angegebene Objekt ungleich NULL ist, und löst eine Ausnahme aus,
            wenn es NULL ist.
            </summary>
            <param name="value">
            Das Objekt, von dem der Test erwartet, dass es ungleich NULL ist.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String)">
            <summary>
            Testet, ob das angegebene Objekt ungleich NULL ist, und löst eine Ausnahme aus,
            wenn es NULL ist.
            </summary>
            <param name="value">
            Das Objekt, von dem der Test erwartet, dass es ungleich NULL ist.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="value"/>
            ist NULL. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String,System.Object[])">
            <summary>
            Testet, ob das angegebene Objekt ungleich NULL ist, und löst eine Ausnahme aus,
            wenn es NULL ist.
            </summary>
            <param name="value">
            Das Objekt, von dem der Test erwartet, dass es ungleich NULL ist.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="value"/>
            ist NULL. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object)">
            <summary>
            Testet, ob die angegebenen Objekte beide auf das gleiche Objekt verweisen, und
            löst eine Ausnahme aus, wenn die beiden Eingaben nicht auf das gleiche Objekt verweisen.
            </summary>
            <param name="expected">
            Das erste zu vergleichende Objekt. Dies ist der Wert, den der Test erwartet.
            </param>
            <param name="actual">
            Das zweite zu vergleichende Objekt. Dies ist der Wert, der vom getesteten Code generiert wird.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String)">
            <summary>
            Testet, ob die angegebenen Objekte beide auf das gleiche Objekt verweisen, und
            löst eine Ausnahme aus, wenn die beiden Eingaben nicht auf das gleiche Objekt verweisen.
            </summary>
            <param name="expected">
            Das erste zu vergleichende Objekt. Dies ist der Wert, den der Test erwartet.
            </param>
            <param name="actual">
            Das zweite zu vergleichende Objekt. Dies ist der Wert, der vom getesteten Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist nicht identisch mit <paramref name="expected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Objekte beide auf das gleiche Objekt verweisen, und
            löst eine Ausnahme aus, wenn die beiden Eingaben nicht auf das gleiche Objekt verweisen.
            </summary>
            <param name="expected">
            Das erste zu vergleichende Objekt. Dies ist der Wert, den der Test erwartet.
            </param>
            <param name="actual">
            Das zweite zu vergleichende Objekt. Dies ist der Wert, der vom getesteten Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist nicht identisch mit <paramref name="expected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object)">
            <summary>
            Testet, ob die angegebenen Objekte beide auf das gleiche Objekt verweisen, und
            löst eine Ausnahme aus, wenn die beiden Eingaben nicht auf das gleiche Objekt verweisen.
            </summary>
            <param name="notExpected">
            Das erste zu vergleichende Objekt. Dies ist der Wert, von dem der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Das zweite zu vergleichende Objekt. Dies ist der Wert, der vom getesteten Code generiert wird.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String)">
            <summary>
            Testet, ob die angegebenen Objekte beide auf das gleiche Objekt verweisen, und
            löst eine Ausnahme aus, wenn die beiden Eingaben nicht auf das gleiche Objekt verweisen.
            </summary>
            <param name="notExpected">
            Das erste zu vergleichende Objekt. Dies ist der Wert, von dem der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Das zweite zu vergleichende Objekt. Dies ist der Wert, der vom getesteten Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist identisch mit <paramref name="notExpected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Objekte beide auf das gleiche Objekt verweisen, und
            löst eine Ausnahme aus, wenn die beiden Eingaben nicht auf das gleiche Objekt verweisen.
            </summary>
            <param name="notExpected">
            Das erste zu vergleichende Objekt. Dies ist der Wert, von dem der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Das zweite zu vergleichende Objekt. Dies ist der Wert, der vom getesteten Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist identisch mit <paramref name="notExpected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0)">
            <summary>
            Testet, ob die angegebenen Werte gleich sind, und löst eine Ausnahme aus,
            wenn die beiden Werte nicht gleich sind. Verschiedene numerische Typen werden selbst dann als ungleich
            behandelt, wenn die logischen Werte gleich sind. 42L ist nicht gleich 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Der erste zu vergleichende Wert. Dies ist der Wert, den der Test erwartet.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Wert. Dies ist der Wert, der vom zu testenden Code generiert wird.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String)">
            <summary>
            Testet, ob die angegebenen Werte gleich sind, und löst eine Ausnahme aus,
            wenn die beiden Werte nicht gleich sind. Verschiedene numerische Typen werden selbst dann als ungleich
            behandelt, wenn die logischen Werte gleich sind. 42L ist nicht gleich 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Der erste zu vergleichende Wert. Dies ist der Wert, den der Test erwartet.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Wert. Dies ist der Wert, der vom zu testenden Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist nicht gleich <paramref name="expected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Werte gleich sind, und löst eine Ausnahme aus,
            wenn die beiden Werte nicht gleich sind. Verschiedene numerische Typen werden selbst dann als ungleich
            behandelt, wenn die logischen Werte gleich sind. 42L ist nicht gleich 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Der erste zu vergleichende Wert. Dies ist der Wert, den der Test erwartet.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Wert. Dies ist der Wert, der vom zu testenden Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist nicht gleich <paramref name="expected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0)">
            <summary>
            Testet, ob die angegebenen Werte ungleich sind, und löst eine Ausnahme aus,
            wenn die beiden Werte gleich sind. Verschiedene numerische Typen werden selbst dann als ungleich
            behandelt, wenn die logischen Werte gleich sind. 42L ist nicht gleich 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Das erste zu vergleichende Objekt. Dies ist der Wert, von dem der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Wert. Dies ist der Wert, der vom zu testenden Code generiert wird.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String)">
            <summary>
            Testet, ob die angegebenen Werte ungleich sind, und löst eine Ausnahme aus,
            wenn die beiden Werte gleich sind. Verschiedene numerische Typen werden selbst dann als ungleich
            behandelt, wenn die logischen Werte gleich sind. 42L ist nicht gleich 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Das erste zu vergleichende Objekt. Dies ist der Wert, von dem der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Wert. Dies ist der Wert, der vom zu testenden Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist gleich <paramref name="notExpected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Werte ungleich sind, und löst eine Ausnahme aus,
            wenn die beiden Werte gleich sind. Verschiedene numerische Typen werden selbst dann als ungleich
            behandelt, wenn die logischen Werte gleich sind. 42L ist nicht gleich 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Das erste zu vergleichende Objekt. Dies ist der Wert, von dem der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Wert. Dies ist der Wert, der vom zu testenden Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist gleich <paramref name="notExpected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object)">
            <summary>
            Testet, ob die angegebenen Objekte gleich sind, und löst eine Ausnahme aus,
            wenn die beiden Objekte nicht gleich sind. Verschiedene numerische Typen werden selbst dann als ungleich
            behandelt, wenn die logischen Werte gleich sind. 42L ist nicht gleich 42.
            </summary>
            <param name="expected">
            Das erste zu vergleichende Objekt. Dies ist das Objekt, das der Test erwartet.
            </param>
            <param name="actual">
            Das zweite zu vergleichende Objekt. Dies ist das Objekt, das vom getesteten Code generiert wird.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String)">
            <summary>
            Testet, ob die angegebenen Objekte gleich sind, und löst eine Ausnahme aus,
            wenn die beiden Objekte nicht gleich sind. Verschiedene numerische Typen werden selbst dann als ungleich
            behandelt, wenn die logischen Werte gleich sind. 42L ist nicht gleich 42.
            </summary>
            <param name="expected">
            Das erste zu vergleichende Objekt. Dies ist das Objekt, das der Test erwartet.
            </param>
            <param name="actual">
            Das zweite zu vergleichende Objekt. Dies ist das Objekt, das vom getesteten Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist nicht gleich <paramref name="expected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Objekte gleich sind, und löst eine Ausnahme aus,
            wenn die beiden Objekte nicht gleich sind. Verschiedene numerische Typen werden selbst dann als ungleich
            behandelt, wenn die logischen Werte gleich sind. 42L ist nicht gleich 42.
            </summary>
            <param name="expected">
            Das erste zu vergleichende Objekt. Dies ist das Objekt, das der Test erwartet.
            </param>
            <param name="actual">
            Das zweite zu vergleichende Objekt. Dies ist das Objekt, das vom getesteten Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist nicht gleich <paramref name="expected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object)">
            <summary>
            Testet, ob die angegebenen Objekte ungleich sind, und löst eine Ausnahme aus,
            wenn die beiden Objekte gleich sind. Verschiedene numerische Typen werden selbst dann als ungleich
            behandelt, wenn die logischen Werte gleich sind. 42L ist nicht gleich 42.
            </summary>
            <param name="notExpected">
            Das erste zu vergleichende Objekt. Dies ist der Wert, von dem der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Das zweite zu vergleichende Objekt. Dies ist das Objekt, das vom getesteten Code generiert wird.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String)">
            <summary>
            Testet, ob die angegebenen Objekte ungleich sind, und löst eine Ausnahme aus,
            wenn die beiden Objekte gleich sind. Verschiedene numerische Typen werden selbst dann als ungleich
            behandelt, wenn die logischen Werte gleich sind. 42L ist nicht gleich 42.
            </summary>
            <param name="notExpected">
            Das erste zu vergleichende Objekt. Dies ist der Wert, von dem der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Das zweite zu vergleichende Objekt. Dies ist das Objekt, das vom getesteten Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist gleich <paramref name="notExpected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Objekte ungleich sind, und löst eine Ausnahme aus,
            wenn die beiden Objekte gleich sind. Verschiedene numerische Typen werden selbst dann als ungleich
            behandelt, wenn die logischen Werte gleich sind. 42L ist nicht gleich 42.
            </summary>
            <param name="notExpected">
            Das erste zu vergleichende Objekt. Dies ist der Wert, von dem der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Das zweite zu vergleichende Objekt. Dies ist das Objekt, das vom getesteten Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist gleich <paramref name="notExpected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single)">
            <summary>
            Testet, ob die angegebenen Gleitkommawerte gleich sind, und löst eine Ausnahme aus,
            wenn sie ungleich sind.
            </summary>
            <param name="expected">
            Der erste zu vergleichende Gleitkommawert. Dies ist der Gleitkommawert, den der Test erwartet.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Gleitkommawert. Dies ist der Gleitkommawert, der vom getesteten Code generiert wird.
            </param>
            <param name="delta">
            Die erforderliche Genauigkeit. Eine Ausnahme wird nur ausgelöst, wenn
            <paramref name="actual"/> sich unterscheidet von <paramref name="expected"/>
            um mehr als <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Testet, ob die angegebenen Gleitkommawerte gleich sind, und löst eine Ausnahme aus,
            wenn sie ungleich sind.
            </summary>
            <param name="expected">
            Der erste zu vergleichende Gleitkommawert. Dies ist der Gleitkommawert, den der Test erwartet.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Gleitkommawert. Dies ist der Gleitkommawert, der vom getesteten Code generiert wird.
            </param>
            <param name="delta">
            Die erforderliche Genauigkeit. Eine Ausnahme wird nur ausgelöst, wenn
            <paramref name="actual"/> sich unterscheidet von <paramref name="expected"/>
            um mehr als <paramref name="delta"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            sich unterscheidet von <paramref name="expected"/> um mehr als
            <paramref name="delta"/>. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Gleitkommawerte gleich sind, und löst eine Ausnahme aus,
            wenn sie ungleich sind.
            </summary>
            <param name="expected">
            Der erste zu vergleichende Gleitkommawert. Dies ist der Gleitkommawert, den der Test erwartet.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Gleitkommawert. Dies ist der Gleitkommawert, der vom getesteten Code generiert wird.
            </param>
            <param name="delta">
            Die erforderliche Genauigkeit. Eine Ausnahme wird nur ausgelöst, wenn
            <paramref name="actual"/> sich unterscheidet von <paramref name="expected"/>
            um mehr als <paramref name="delta"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            sich unterscheidet von <paramref name="expected"/> um mehr als
            <paramref name="delta"/>. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single)">
            <summary>
            Testet, ob die angegebenen Gleitkommawerte ungleich sind, und löst eine Ausnahme aus,
            wenn sie gleich sind.
            </summary>
            <param name="notExpected">
            Der erste zu vergleichende Gleitkommawert. Dies ist der Gleitkommawert, für den der Test keine Übereinstimmung
            erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Gleitkommawert. Dies ist der Gleitkommawert, der vom getesteten Code generiert wird.
            </param>
            <param name="delta">
            Die erforderliche Genauigkeit. Eine Ausnahme wird nur ausgelöst, wenn
            <paramref name="actual"/> sich unterscheidet von <paramref name="notExpected"/>
            um höchstens <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Testet, ob die angegebenen Gleitkommawerte ungleich sind, und löst eine Ausnahme aus,
            wenn sie gleich sind.
            </summary>
            <param name="notExpected">
            Der erste zu vergleichende Gleitkommawert. Dies ist der Gleitkommawert, für den der Test keine Übereinstimmung
            erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Gleitkommawert. Dies ist der Gleitkommawert, der vom getesteten Code generiert wird.
            </param>
            <param name="delta">
            Die erforderliche Genauigkeit. Eine Ausnahme wird nur ausgelöst, wenn
            <paramref name="actual"/> sich unterscheidet von <paramref name="notExpected"/>
            um höchstens <paramref name="delta"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist gleich <paramref name="notExpected"/> oder sich unterscheidet um weniger als
            <paramref name="delta"/>. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Gleitkommawerte ungleich sind, und löst eine Ausnahme aus,
            wenn sie gleich sind.
            </summary>
            <param name="notExpected">
            Der erste zu vergleichende Gleitkommawert. Dies ist der Gleitkommawert, für den der Test keine Übereinstimmung
            erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Gleitkommawert. Dies ist der Gleitkommawert, der vom getesteten Code generiert wird.
            </param>
            <param name="delta">
            Die erforderliche Genauigkeit. Eine Ausnahme wird nur ausgelöst, wenn
            <paramref name="actual"/> sich unterscheidet von <paramref name="notExpected"/>
            um höchstens <paramref name="delta"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist gleich <paramref name="notExpected"/> oder sich unterscheidet um weniger als
            <paramref name="delta"/>. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double)">
            <summary>
            Testet, ob die angegebenen Double-Werte gleich sind, und löst eine Ausnahme aus,
            wenn sie ungleich sind.
            </summary>
            <param name="expected">
            Der erste zu vergleichende Double-Wert. Dies ist der Double-Wert, den der Test erwartet.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Double-Wert. Dies ist der Double-Wert, der vom getesteten Code generiert wird.
            </param>
            <param name="delta">
            Die erforderliche Genauigkeit. Eine Ausnahme wird nur ausgelöst, wenn
            <paramref name="actual"/> sich unterscheidet von <paramref name="expected"/>
            um mehr als <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Testet, ob die angegebenen Double-Werte gleich sind, und löst eine Ausnahme aus,
            wenn sie ungleich sind.
            </summary>
            <param name="expected">
            Der erste zu vergleichende Double-Wert. Dies ist der Double-Wert, den der Test erwartet.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Double-Wert. Dies ist der Double-Wert, der vom getesteten Code generiert wird.
            </param>
            <param name="delta">
            Die erforderliche Genauigkeit. Eine Ausnahme wird nur ausgelöst, wenn
            <paramref name="actual"/> sich unterscheidet von <paramref name="expected"/>
            um mehr als <paramref name="delta"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            sich unterscheidet von <paramref name="expected"/> um mehr als
            <paramref name="delta"/>. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Double-Werte gleich sind, und löst eine Ausnahme aus,
            wenn sie ungleich sind.
            </summary>
            <param name="expected">
            Der erste zu vergleichende Double-Wert. Dies ist der Double-Wert, den der Test erwartet.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Double-Wert. Dies ist der Double-Wert, der vom getesteten Code generiert wird.
            </param>
            <param name="delta">
            Die erforderliche Genauigkeit. Eine Ausnahme wird nur ausgelöst, wenn
            <paramref name="actual"/> sich unterscheidet von <paramref name="expected"/>
            um mehr als <paramref name="delta"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            sich unterscheidet von <paramref name="expected"/> um mehr als
            <paramref name="delta"/>. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double)">
            <summary>
            Testet, ob die angegebenen Double-Werte ungleich sind, und löst eine Ausnahme aus,
            wenn sie gleich sind.
            </summary>
            <param name="notExpected">
            Der erste zu vergleichende Double-Wert. Dies ist der Double-Wert, für den der Test keine Übereinstimmung
            erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Double-Wert. Dies ist der Double-Wert, der vom getesteten Code generiert wird.
            </param>
            <param name="delta">
            Die erforderliche Genauigkeit. Eine Ausnahme wird nur ausgelöst, wenn
            <paramref name="actual"/> sich unterscheidet von <paramref name="notExpected"/>
            um höchstens <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Testet, ob die angegebenen Double-Werte ungleich sind, und löst eine Ausnahme aus,
            wenn sie gleich sind.
            </summary>
            <param name="notExpected">
            Der erste zu vergleichende Double-Wert. Dies ist der Double-Wert, für den der Test keine Übereinstimmung
            erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Double-Wert. Dies ist der Double-Wert, der vom getesteten Code generiert wird.
            </param>
            <param name="delta">
            Die erforderliche Genauigkeit. Eine Ausnahme wird nur ausgelöst, wenn
            <paramref name="actual"/> sich unterscheidet von <paramref name="notExpected"/>
            um höchstens <paramref name="delta"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist gleich <paramref name="notExpected"/> oder sich unterscheidet um weniger als
            <paramref name="delta"/>. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Double-Werte ungleich sind, und löst eine Ausnahme aus,
            wenn sie gleich sind.
            </summary>
            <param name="notExpected">
            Der erste zu vergleichende Double-Wert. Dies ist der Double-Wert, für den der Test keine Übereinstimmung
            erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Der zweite zu vergleichende Double-Wert. Dies ist der Double-Wert, der vom getesteten Code generiert wird.
            </param>
            <param name="delta">
            Die erforderliche Genauigkeit. Eine Ausnahme wird nur ausgelöst, wenn
            <paramref name="actual"/> sich unterscheidet von <paramref name="notExpected"/>
            um höchstens <paramref name="delta"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist gleich <paramref name="notExpected"/> oder sich unterscheidet um weniger als
            <paramref name="delta"/>. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean)">
            <summary>
            Testet, ob die angegebenen Zeichenfolgen gleich sind, und löst eine Ausnahme aus,
            wenn sie ungleich sind. Die invariante Kultur wird für den Vergleich verwendet.
         </summary>
            <param name="expected">
            Die erste zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die der Test erwartet.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die vom getesteten Code generiert wird.
            </param>
            <param name="ignoreCase">
            Ein boolescher Wert, der einen Vergleich mit oder ohne Beachtung von Groß-/Kleinschreibung angibt. (TRUE
            gibt einen Vergleich ohne Beachtung von Groß-/Kleinschreibung an.)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Testet, ob die angegebenen Zeichenfolgen gleich sind, und löst eine Ausnahme aus,
            wenn sie ungleich sind. Die invariante Kultur wird für den Vergleich verwendet.
         </summary>
            <param name="expected">
            Die erste zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die der Test erwartet.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die vom getesteten Code generiert wird.
            </param>
            <param name="ignoreCase">
            Ein boolescher Wert, der einen Vergleich mit oder ohne Beachtung von Groß-/Kleinschreibung angibt. (TRUE
            gibt einen Vergleich ohne Beachtung von Groß-/Kleinschreibung an.)
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist nicht gleich <paramref name="expected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Zeichenfolgen gleich sind, und löst eine Ausnahme aus,
            wenn sie ungleich sind. Die invariante Kultur wird für den Vergleich verwendet.
         </summary>
            <param name="expected">
            Die erste zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die der Test erwartet.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die vom getesteten Code generiert wird.
            </param>
            <param name="ignoreCase">
            Ein boolescher Wert, der einen Vergleich mit oder ohne Beachtung von Groß-/Kleinschreibung angibt. (TRUE
            gibt einen Vergleich ohne Beachtung von Groß-/Kleinschreibung an.)
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist nicht gleich <paramref name="expected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Testet, ob die angegebenen Zeichenfolgen gleich sind, und löst eine Ausnahme aus,
            wenn sie ungleich sind.
            </summary>
            <param name="expected">
            Die erste zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die der Test erwartet.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die vom getesteten Code generiert wird.
            </param>
            <param name="ignoreCase">
            Ein boolescher Wert, der einen Vergleich mit oder ohne Beachtung von Groß-/Kleinschreibung angibt. (TRUE
            gibt einen Vergleich ohne Beachtung von Groß-/Kleinschreibung an.)
            </param>
            <param name="culture">
            Ein CultureInfo-Objekt, das kulturspezifische Vergleichsinformationen bereitstellt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Testet, ob die angegebenen Zeichenfolgen gleich sind, und löst eine Ausnahme aus,
            wenn sie ungleich sind.
            </summary>
            <param name="expected">
            Die erste zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die der Test erwartet.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die vom getesteten Code generiert wird.
            </param>
            <param name="ignoreCase">
            Ein boolescher Wert, der einen Vergleich mit oder ohne Beachtung von Groß-/Kleinschreibung angibt. (TRUE
            gibt einen Vergleich ohne Beachtung von Groß-/Kleinschreibung an.)
            </param>
            <param name="culture">
            Ein CultureInfo-Objekt, das kulturspezifische Vergleichsinformationen bereitstellt.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist nicht gleich <paramref name="expected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Zeichenfolgen gleich sind, und löst eine Ausnahme aus,
            wenn sie ungleich sind.
            </summary>
            <param name="expected">
            Die erste zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die der Test erwartet.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die vom getesteten Code generiert wird.
            </param>
            <param name="ignoreCase">
            Ein boolescher Wert, der einen Vergleich mit oder ohne Beachtung von Groß-/Kleinschreibung angibt. (TRUE
            gibt einen Vergleich ohne Beachtung von Groß-/Kleinschreibung an.)
            </param>
            <param name="culture">
            Ein CultureInfo-Objekt, das kulturspezifische Vergleichsinformationen bereitstellt.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist nicht gleich <paramref name="expected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean)">
            <summary>
            Testet, ob die angegebenen Zeichenfolgen ungleich sind, und löst eine Ausnahme aus,
            wenn sie gleich sind. Die invariante Kultur wird für den Vergleich verwendet.
           </summary>
            <param name="notExpected">
            Die erste zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, von der der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die vom getesteten Code generiert wird.
            </param>
            <param name="ignoreCase">
            Ein boolescher Wert, der einen Vergleich mit oder ohne Beachtung von Groß-/Kleinschreibung angibt. (TRUE
            gibt einen Vergleich ohne Beachtung von Groß-/Kleinschreibung an.)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Testet, ob die angegebenen Zeichenfolgen ungleich sind, und löst eine Ausnahme aus,
            wenn sie gleich sind. Die invariante Kultur wird für den Vergleich verwendet.
            </summary>
            <param name="notExpected">
            Die erste zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, von der der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die vom getesteten Code generiert wird.
            </param>
            <param name="ignoreCase">
            Ein boolescher Wert, der einen Vergleich mit oder ohne Beachtung von Groß-/Kleinschreibung angibt. (TRUE
            gibt einen Vergleich ohne Beachtung von Groß-/Kleinschreibung an.)
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist gleich <paramref name="notExpected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Zeichenfolgen ungleich sind, und löst eine Ausnahme aus,
            wenn sie gleich sind. Die invariante Kultur wird für den Vergleich verwendet.
            </summary>
            <param name="notExpected">
            Die erste zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, von der der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die vom getesteten Code generiert wird.
            </param>
            <param name="ignoreCase">
            Ein boolescher Wert, der einen Vergleich mit oder ohne Beachtung von Groß-/Kleinschreibung angibt. (TRUE
            gibt einen Vergleich ohne Beachtung von Groß-/Kleinschreibung an.)
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist gleich <paramref name="notExpected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Testet, ob die angegebenen Zeichenfolgen ungleich sind, und löst eine Ausnahme aus,
            wenn sie gleich sind.
            </summary>
            <param name="notExpected">
            Die erste zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, von der der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die vom getesteten Code generiert wird.
            </param>
            <param name="ignoreCase">
            Ein boolescher Wert, der einen Vergleich mit oder ohne Beachtung von Groß-/Kleinschreibung angibt. (TRUE
            gibt einen Vergleich ohne Beachtung von Groß-/Kleinschreibung an.)
            </param>
            <param name="culture">
            Ein CultureInfo-Objekt, das kulturspezifische Vergleichsinformationen bereitstellt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Testet, ob die angegebenen Zeichenfolgen ungleich sind, und löst eine Ausnahme aus,
            wenn sie gleich sind.
            </summary>
            <param name="notExpected">
            Die erste zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, von der der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die vom getesteten Code generiert wird.
            </param>
            <param name="ignoreCase">
            Ein boolescher Wert, der einen Vergleich mit oder ohne Beachtung von Groß-/Kleinschreibung angibt. (TRUE
            gibt einen Vergleich ohne Beachtung von Groß-/Kleinschreibung an.)
            </param>
            <param name="culture">
            Ein CultureInfo-Objekt, das kulturspezifische Vergleichsinformationen bereitstellt.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist gleich <paramref name="notExpected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Zeichenfolgen ungleich sind, und löst eine Ausnahme aus,
            wenn sie gleich sind.
            </summary>
            <param name="notExpected">
            Die erste zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, von der der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Zeichenfolge. Dies ist die Zeichenfolge, die vom getesteten Code generiert wird.
            </param>
            <param name="ignoreCase">
            Ein boolescher Wert, der einen Vergleich mit oder ohne Beachtung von Groß-/Kleinschreibung angibt. (TRUE
            gibt einen Vergleich ohne Beachtung von Groß-/Kleinschreibung an.)
            </param>
            <param name="culture">
            Ein CultureInfo-Objekt, das kulturspezifische Vergleichsinformationen bereitstellt.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist gleich <paramref name="notExpected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type)">
            <summary>
            Testet, ob das angegebene Objekt eine Instanz des erwarteten
            Typs ist, und löst eine Ausnahme aus, wenn sich der erwartete Typ nicht in der
            Vererbungshierarchie des Objekts befindet.
            </summary>
            <param name="value">
            Das Objekt, von dem der Test erwartet, dass es vom angegebenen Typ ist.
            </param>
            <param name="expectedType">
            Der erwartete Typ von <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Testet, ob das angegebene Objekt eine Instanz des erwarteten
            Typs ist, und löst eine Ausnahme aus, wenn sich der erwartete Typ nicht in der
            Vererbungshierarchie des Objekts befindet.
            </summary>
            <param name="value">
            Das Objekt, von dem der Test erwartet, dass es vom angegebenen Typ ist.
            </param>
            <param name="expectedType">
            Der erwartete Typ von <paramref name="value"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="value"/>
            ist keine Instanz von <paramref name="expectedType"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Testet, ob das angegebene Objekt eine Instanz des erwarteten
            Typs ist, und löst eine Ausnahme aus, wenn sich der erwartete Typ nicht in der
            Vererbungshierarchie des Objekts befindet.
            </summary>
            <param name="value">
            Das Objekt, von dem der Test erwartet, dass es vom angegebenen Typ ist.
            </param>
            <param name="expectedType">
            Der erwartete Typ von <paramref name="value"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="value"/>
            ist keine Instanz von <paramref name="expectedType"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type)">
            <summary>
            Testet, ob das angegebene Objekt keine Instanz des falschen
            Typs ist, und löst eine Ausnahme aus, wenn sich der angegebene Typ in der
            Vererbungshierarchie des Objekts befindet.
            </summary>
            <param name="value">
            Das Objekt, von dem der Test erwartet, dass es nicht vom angegebenen Typ ist.
            </param>
            <param name="wrongType">
            Der Typ, der <paramref name="value"/> unzulässig ist.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Testet, ob das angegebene Objekt keine Instanz des falschen
            Typs ist, und löst eine Ausnahme aus, wenn sich der angegebene Typ in der
            Vererbungshierarchie des Objekts befindet.
            </summary>
            <param name="value">
            Das Objekt, von dem der Test erwartet, dass es nicht vom angegebenen Typ ist.
            </param>
            <param name="wrongType">
            Der Typ, der <paramref name="value"/> unzulässig ist.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="value"/>
            ist keine Instanz von <paramref name="wrongType"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Testet, ob das angegebene Objekt keine Instanz des falschen
            Typs ist, und löst eine Ausnahme aus, wenn sich der angegebene Typ in der
            Vererbungshierarchie des Objekts befindet.
            </summary>
            <param name="value">
            Das Objekt, von dem der Test erwartet, dass es nicht vom angegebenen Typ ist.
            </param>
            <param name="wrongType">
            Der Typ, der <paramref name="value"/> unzulässig ist.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="value"/>
            ist keine Instanz von <paramref name="wrongType"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail">
            <summary>
            Löst eine AssertFailedException aus.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String)">
            <summary>
            Löst eine AssertFailedException aus.
            </summary>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung. Die Meldung wird in
            den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String,System.Object[])">
            <summary>
            Löst eine AssertFailedException aus.
            </summary>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung. Die Meldung wird in
            den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive">
            <summary>
            Löst eine AssertInconclusiveException aus.
        </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String)">
            <summary>
            Löst eine AssertInconclusiveException aus.
        </summary>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung. Die Meldung wird in
            den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String,System.Object[])">
            <summary>
            Löst eine AssertInconclusiveException aus.
        </summary>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung. Die Meldung wird in
            den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Equals(System.Object,System.Object)">
            <summary>
            Statische equals-Überladungen werden zum Vergleichen von Instanzen zweier Typen für
            Verweisgleichheit verwendet. Diese Methode sollte <b>nicht</b> zum Vergleichen von zwei Instanzen auf
            Gleichheit verwendet werden. Dieses Objekt löst <b>immer</b> einen Assert.Fail aus. Verwenden Sie
            Assert.AreEqual und zugehörige Überladungen in Ihren Komponententests.
            </summary>
            <param name="objA"> Objekt A </param>
            <param name="objB"> Objekt B </param>
            <returns> Immer FALSE. </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action)">
            <summary>
            Testet, ob der von Delegat <paramref name="action"/> ausgegebene Code genau die angegebene Ausnahme vom Typ <typeparamref name="T"/> (und nicht vom abgeleiteten Typ) auslöst
            und
            <code>
            AssertFailedException
            </code>
            auslöst, wenn der Code keine Ausnahme oder einen anderen Typ als <typeparamref name="T"/> auslöst.
            </summary>
            <param name="action">
            Zu testender Delegatcode, von dem erwartet wird, dass er eine Ausnahme auslöst.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Der Typ der Ausnahme, die ausgelöst werden soll.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String)">
            <summary>
            Testet, ob der von Delegat <paramref name="action"/> ausgegebene Code genau die angegebene Ausnahme vom Typ <typeparamref name="T"/> (und nicht vom abgeleiteten Typ) auslöst
            und
            <code>
            AssertFailedException
            </code>
            auslöst, wenn der Code keine Ausnahme oder einen anderen Typ als <typeparamref name="T"/> auslöst.
            </summary>
            <param name="action">
            Zu testender Delegatcode, von dem erwartet wird, dass er eine Ausnahme auslöst.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="action"/>
            löst keine Ausnahme aus vom Typ <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Der Typ der Ausnahme, die ausgelöst werden soll.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object})">
            <summary>
            Testet, ob der von Delegat <paramref name="action"/> ausgegebene Code genau die angegebene Ausnahme vom Typ <typeparamref name="T"/> (und nicht vom abgeleiteten Typ) auslöst
            und
            <code>
            AssertFailedException
            </code>
            auslöst, wenn der Code keine Ausnahme oder einen anderen Typ als <typeparamref name="T"/> auslöst.
            </summary>
            <param name="action">
            Zu testender Delegatcode, von dem erwartet wird, dass er eine Ausnahme auslöst.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Der Typ der Ausnahme, die ausgelöst werden soll.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String)">
            <summary>
            Testet, ob der von Delegat <paramref name="action"/> ausgegebene Code genau die angegebene Ausnahme vom Typ <typeparamref name="T"/> (und nicht vom abgeleiteten Typ) auslöst
            und
            <code>
            AssertFailedException
            </code>
            auslöst, wenn der Code keine Ausnahme oder einen anderen Typ als <typeparamref name="T"/> auslöst.
            </summary>
            <param name="action">
            Zu testender Delegatcode, von dem erwartet wird, dass er eine Ausnahme auslöst.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="action"/>
            löst keine Ausnahme aus vom Typ <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Der Typ der Ausnahme, die ausgelöst werden soll.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String,System.Object[])">
            <summary>
            Testet, ob der von Delegat <paramref name="action"/> ausgegebene Code genau die angegebene Ausnahme vom Typ <typeparamref name="T"/> (und nicht vom abgeleiteten Typ) auslöst
            und
            <code>
            AssertFailedException
            </code>
            auslöst, wenn der Code keine Ausnahme oder einen anderen Typ als <typeparamref name="T"/> auslöst.
            </summary>
            <param name="action">
            Zu testender Delegatcode, von dem erwartet wird, dass er eine Ausnahme auslöst.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="action"/>
            löst keine Ausnahme aus vom Typ <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throw exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Der Typ der Ausnahme, die ausgelöst werden soll.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String,System.Object[])">
            <summary>
            Testet, ob der von Delegat <paramref name="action"/> ausgegebene Code genau die angegebene Ausnahme vom Typ <typeparamref name="T"/> (und nicht vom abgeleiteten Typ) auslöst
            und
            <code>
            AssertFailedException
            </code>
            auslöst, wenn der Code keine Ausnahme oder einen anderen Typ als <typeparamref name="T"/> auslöst.
            </summary>
            <param name="action">
            Zu testender Delegatcode, von dem erwartet wird, dass er eine Ausnahme auslöst.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="action"/>
            löst keine Ausnahme aus vom Typ <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Der Typ der Ausnahme, die ausgelöst werden soll.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Testet, ob der von Delegat <paramref name="action"/> ausgegebene Code genau die angegebene Ausnahme vom Typ <typeparamref name="T"/> (und nicht vom abgeleiteten Typ) auslöst
            und
            <code>
            AssertFailedException
            </code>
            auslöst, wenn der Code keine Ausnahme oder einen anderen Typ als <typeparamref name="T"/> auslöst.
            </summary>
            <param name="action">
            Zu testender Delegatcode, von dem erwartet wird, dass er eine Ausnahme auslöst.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Der <see cref="T:System.Threading.Tasks.Task"/> der Delegat ausgeführt wird.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            Testet, ob der von Delegat <paramref name="action"/> angegebene Code genau die angegebene Ausnahme vom Typ <typeparamref name="T"/> (und nicht vom abgeleiteten Typ) auslöst
            und <code>AssertFailedException</code> auslöst, wenn der Code keine Ausnahme auslöst oder einen anderen Typ als <typeparamref name="T"/> auslöst.
            </summary>
            <param name="action">Zu testender Delegatcode, von dem erwartet wird, dass er eine Ausnahme auslöst.</param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="action"/>
            löst keine Ausnahme aus vom Typ <typeparamref name="T"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Der <see cref="T:System.Threading.Tasks.Task"/> der Delegat ausgeführt wird.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String,System.Object[])">
            <summary>
            Testet, ob der von Delegat <paramref name="action"/> angegebene Code genau die angegebene Ausnahme vom Typ <typeparamref name="T"/> (und nicht vom abgeleiteten Typ) auslöst
            und <code>AssertFailedException</code> auslöst, wenn der Code keine Ausnahme auslöst oder einen anderen Typ als <typeparamref name="T"/> auslöst.
            </summary>
            <param name="action">Zu testender Delegatcode, von dem erwartet wird, dass er eine Ausnahme auslöst.</param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="action"/>
            löst keine Ausnahme aus vom Typ <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Der <see cref="T:System.Threading.Tasks.Task"/> der Delegat ausgeführt wird.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNullChars(System.String)">
            <summary>
            Ersetzt Nullzeichen ("\0") durch "\\0".
            </summary>
            <param name="input">
            Die Zeichenfolge, nach der gesucht werden soll.
            </param>
            <returns>
            Die konvertierte Zeichenfolge, in der Nullzeichen durch "\\0" ersetzt wurden.
            </returns>
            <remarks>
            This is only public and still present to preserve compatibility with the V1 framework.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.HandleFail(System.String,System.String,System.Object[])">
            <summary>
            Eine Hilfsfunktion, die eine AssertionFailedException erstellt und auslöst.
            </summary>
            <param name="assertionName">
            Der Name der Assertion, die eine Ausnahme auslöst.
            </param>
            <param name="message">
            Eine Meldung, die Bedingungen für den Assertionfehler beschreibt.
            </param>
            <param name="parameters">
            Die Parameter.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.CheckParameterNotNull(System.Object,System.String,System.String,System.String,System.Object[])">
            <summary>
            Überprüft den Parameter auf gültige Bedingungen.
            </summary>
            <param name="param">
            Der Parameter.
            </param>
            <param name="assertionName">
            Der Name der Assertion.
            </param>
            <param name="parameterName">
            Parametername
            </param>
            <param name="message">
            Meldung für die ungültige Parameterausnahme.
            </param>
            <param name="parameters">
            Die Parameter.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNulls(System.Object)">
            <summary>
            Konvertiert ein Objekt sicher in eine Zeichenfolge und verarbeitet dabei NULL-Werte und Nullzeichen.
            NULL-Werte werden in "(null)" konvertiert. Nullzeichen werden in "\\0" konvertiert".
            </summary>
            <param name="input">
            Das Objekt, das in eine Zeichenfolge konvertiert werden soll.
            </param>
            <returns>
            Die konvertierte Zeichenfolge.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert">
            <summary>
            Die Zeichenfolgenassertion.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.That">
            <summary>
            Ruft die Singleton-Instanz der CollectionAssert-Funktionalität ab.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void ContainsWords(this StringAssert cusomtAssert, string value, ICollection substrings)"
            Users could then use a syntax similar to the default assertions which in this case is "StringAssert.That.ContainsWords(value, substrings);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String)">
            <summary>
            Testet, ob die angegebene Zeichenfolge die angegebene Teilzeichenfolge
            enthält, und löst eine Ausnahme aus, wenn die Teilzeichenfolge nicht in der
            Testzeichenfolge vorkommt.
            </summary>
            <param name="value">
            Die Zeichenfolge, von der erwartet wird, dass sie Folgendes enthält: <paramref name="substring"/>.
            </param>
            <param name="substring">
            Die Zeichenfolge, die erwartet wird in <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String)">
            <summary>
            Testet, ob die angegebene Zeichenfolge die angegebene Teilzeichenfolge
            enthält, und löst eine Ausnahme aus, wenn die Teilzeichenfolge nicht in der
            Testzeichenfolge vorkommt.
            </summary>
            <param name="value">
            Die Zeichenfolge, von der erwartet wird, dass sie Folgendes enthält: <paramref name="substring"/>.
            </param>
            <param name="substring">
            Die Zeichenfolge, die erwartet wird in <paramref name="value"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="substring"/>
            ist nicht in <paramref name="value"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebene Zeichenfolge die angegebene Teilzeichenfolge
            enthält, und löst eine Ausnahme aus, wenn die Teilzeichenfolge nicht in der
            Testzeichenfolge vorkommt.
            </summary>
            <param name="value">
            Die Zeichenfolge, von der erwartet wird, dass sie Folgendes enthält: <paramref name="substring"/>.
            </param>
            <param name="substring">
            Die Zeichenfolge, die erwartet wird in <paramref name="value"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="substring"/>
            ist nicht in <paramref name="value"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String)">
            <summary>
            Testet, ob die angegebene Zeichenfolge mit der angegebenen Teilzeichenfolge
            beginnt, und löst eine Ausnahme aus, wenn die Testzeichenfolge nicht mit der
            Teilzeichenfolge beginnt.
            </summary>
            <param name="value">
            Die Zeichenfolge, von der erwartet wird, dass sie beginnt mit <paramref name="substring"/>.
            </param>
            <param name="substring">
            Die Zeichenfolge, von der erwartet wird, dass sie ein Präfix ist von <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String)">
            <summary>
            Testet, ob die angegebene Zeichenfolge mit der angegebenen Teilzeichenfolge
            beginnt, und löst eine Ausnahme aus, wenn die Testzeichenfolge nicht mit der
            Teilzeichenfolge beginnt.
            </summary>
            <param name="value">
            Die Zeichenfolge, von der erwartet wird, dass sie beginnt mit <paramref name="substring"/>.
            </param>
            <param name="substring">
            Die Zeichenfolge, von der erwartet wird, dass sie ein Präfix ist von <paramref name="value"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="value"/>
            beginnt nicht mit <paramref name="substring"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebene Zeichenfolge mit der angegebenen Teilzeichenfolge
            beginnt, und löst eine Ausnahme aus, wenn die Testzeichenfolge nicht mit der
            Teilzeichenfolge beginnt.
            </summary>
            <param name="value">
            Die Zeichenfolge, von der erwartet wird, dass sie beginnt mit <paramref name="substring"/>.
            </param>
            <param name="substring">
            Die Zeichenfolge, von der erwartet wird, dass sie ein Präfix ist von <paramref name="value"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="value"/>
            beginnt nicht mit <paramref name="substring"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String)">
            <summary>
            Testet, ob die angegebene Zeichenfolge mit der angegebenen Teilzeichenfolge
            endet, und löst eine Ausnahme aus, wenn die Testzeichenfolge nicht mit der
            Teilzeichenfolge endet.
            </summary>
            <param name="value">
            Die Zeichenfolge, von der erwartet wird, dass sie endet mit <paramref name="substring"/>.
            </param>
            <param name="substring">
            Die Zeichenfolge, von der erwartet wird, dass sie ein Suffix ist von <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String)">
            <summary>
            Testet, ob die angegebene Zeichenfolge mit der angegebenen Teilzeichenfolge
            endet, und löst eine Ausnahme aus, wenn die Testzeichenfolge nicht mit der
            Teilzeichenfolge endet.
            </summary>
            <param name="value">
            Die Zeichenfolge, von der erwartet wird, dass sie endet mit <paramref name="substring"/>.
            </param>
            <param name="substring">
            Die Zeichenfolge, von der erwartet wird, dass sie ein Suffix ist von <paramref name="value"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="value"/>
            endet nicht mit <paramref name="substring"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebene Zeichenfolge mit der angegebenen Teilzeichenfolge
            endet, und löst eine Ausnahme aus, wenn die Testzeichenfolge nicht mit der
            Teilzeichenfolge endet.
            </summary>
            <param name="value">
            Die Zeichenfolge, von der erwartet wird, dass sie endet mit <paramref name="substring"/>.
            </param>
            <param name="substring">
            Die Zeichenfolge, von der erwartet wird, dass sie ein Suffix ist von <paramref name="value"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="value"/>
            endet nicht mit <paramref name="substring"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Testet, ob die angegebene Zeichenfolge mit einem regulären Ausdruck übereinstimmt, und
            löst eine Ausnahme aus, wenn die Zeichenfolge nicht mit dem Ausdruck übereinstimmt.
            </summary>
            <param name="value">
            Die Zeichenfolge, von der erwartet wird, dass sie übereinstimmt mit <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Der reguläre Ausdruck, mit dem <paramref name="value"/> eine
            Übereinstimmung erwartet wird.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Testet, ob die angegebene Zeichenfolge mit einem regulären Ausdruck übereinstimmt, und
            löst eine Ausnahme aus, wenn die Zeichenfolge nicht mit dem Ausdruck übereinstimmt.
            </summary>
            <param name="value">
            Die Zeichenfolge, von der erwartet wird, dass sie übereinstimmt mit <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Der reguläre Ausdruck, mit dem <paramref name="value"/> eine
            Übereinstimmung erwartet wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="value"/>
            keine Übereinstimmung vorliegt. <paramref name="pattern"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebene Zeichenfolge mit einem regulären Ausdruck übereinstimmt, und
            löst eine Ausnahme aus, wenn die Zeichenfolge nicht mit dem Ausdruck übereinstimmt.
            </summary>
            <param name="value">
            Die Zeichenfolge, von der erwartet wird, dass sie übereinstimmt mit <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Der reguläre Ausdruck, mit dem <paramref name="value"/> eine
            Übereinstimmung erwartet wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="value"/>
            keine Übereinstimmung vorliegt. <paramref name="pattern"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Testet, ob die angegebene Zeichenfolge nicht mit einem regulären Ausdruck übereinstimmt, und
            löst eine Ausnahme aus, wenn die Zeichenfolge mit dem Ausdruck übereinstimmt.
            </summary>
            <param name="value">
            Die Zeichenfolge, von der erwartet wird, dass sie nicht übereinstimmt mit <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Der reguläre Ausdruck, mit dem <paramref name="value"/> keine
            Übereinstimmung erwartet wird.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Testet, ob die angegebene Zeichenfolge nicht mit einem regulären Ausdruck übereinstimmt, und
            löst eine Ausnahme aus, wenn die Zeichenfolge mit dem Ausdruck übereinstimmt.
            </summary>
            <param name="value">
            Die Zeichenfolge, von der erwartet wird, dass sie nicht übereinstimmt mit <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Der reguläre Ausdruck, mit dem <paramref name="value"/> keine
            Übereinstimmung erwartet wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="value"/>
            Übereinstimmungen <paramref name="pattern"/>. Die Meldung wird in den Testergebnissen
            angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebene Zeichenfolge nicht mit einem regulären Ausdruck übereinstimmt, und
            löst eine Ausnahme aus, wenn die Zeichenfolge mit dem Ausdruck übereinstimmt.
            </summary>
            <param name="value">
            Die Zeichenfolge, von der erwartet wird, dass sie nicht übereinstimmt mit <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Der reguläre Ausdruck, mit dem <paramref name="value"/> keine
            Übereinstimmung erwartet wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="value"/>
            Übereinstimmungen <paramref name="pattern"/>. Die Meldung wird in den Testergebnissen
            angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert">
            <summary>
            Eine Sammlung von Hilfsklassen zum Testen verschiedener Bedingungen, die
            Sammlungen in Komponententests zugeordnet sind. Wenn die getestete Bedingung nicht
            erfüllt wird, wird eine Ausnahme ausgelöst.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.That">
            <summary>
            Ruft die Singleton-Instanz der CollectionAssert-Funktionalität ab.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void AreEqualUnordered(this CollectionAssert cusomtAssert, ICollection expected, ICollection actual)"
            Users could then use a syntax similar to the default assertions which in this case is "CollectionAssert.That.AreEqualUnordered(list1, list2);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object)">
            <summary>
            Testet, ob die angegebene Sammlung das angegebene Element enthält,
            und löst eine Ausnahme aus, wenn das Element nicht in der Sammlung enthalten ist.
            </summary>
            <param name="collection">
            Die Sammlung, in der nach dem Element gesucht werden soll.
            </param>
            <param name="element">
            Das Element, dessen Vorhandensein in der Sammlung erwartet wird.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Testet, ob die angegebene Sammlung das angegebene Element enthält,
            und löst eine Ausnahme aus, wenn das Element nicht in der Sammlung enthalten ist.
            </summary>
            <param name="collection">
            Die Sammlung, in der nach dem Element gesucht werden soll.
            </param>
            <param name="element">
            Das Element, dessen Vorhandensein in der Sammlung erwartet wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="element"/>
            ist nicht in <paramref name="collection"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebene Sammlung das angegebene Element enthält,
            und löst eine Ausnahme aus, wenn das Element nicht in der Sammlung enthalten ist.
            </summary>
            <param name="collection">
            Die Sammlung, in der nach dem Element gesucht werden soll.
            </param>
            <param name="element">
            Das Element, dessen Vorhandensein in der Sammlung erwartet wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="element"/>
            ist nicht in <paramref name="collection"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object)">
            <summary>
            Testet, ob die angegebene Sammlung das angegebene Element nicht enthält,
            und löst eine Ausnahme aus, wenn das Element in der Sammlung enthalten ist.
            </summary>
            <param name="collection">
            Die Sammlung, in der nach dem Element gesucht werden soll.
            </param>
            <param name="element">
            Das Element, dessen Vorhandensein nicht in der Sammlung erwartet wird.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Testet, ob die angegebene Sammlung das angegebene Element nicht enthält,
            und löst eine Ausnahme aus, wenn das Element in der Sammlung enthalten ist.
            </summary>
            <param name="collection">
            Die Sammlung, in der nach dem Element gesucht werden soll.
            </param>
            <param name="element">
            Das Element, dessen Vorhandensein nicht in der Sammlung erwartet wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="element"/>
            ist in <paramref name="collection"/>. Die Meldung wird in den Testergebnissen
            angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebene Sammlung das angegebene Element nicht enthält,
            und löst eine Ausnahme aus, wenn das Element in der Sammlung enthalten ist.
            </summary>
            <param name="collection">
            Die Sammlung, in der nach dem Element gesucht werden soll.
            </param>
            <param name="element">
            Das Element, dessen Vorhandensein nicht in der Sammlung erwartet wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="element"/>
            ist in <paramref name="collection"/>. Die Meldung wird in den Testergebnissen
            angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection)">
            <summary>
            Testet, ob alle Elemente in der angegebenen Sammlung ungleich null sind, und löst
            eine Ausnahme aus, wenn eines der Elemente NULL ist.
            </summary>
            <param name="collection">
            Die Sammlung, in der nach den Nullelementen gesucht werden soll.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String)">
            <summary>
            Testet, ob alle Elemente in der angegebenen Sammlung ungleich null sind, und löst
            eine Ausnahme aus, wenn eines der Elemente NULL ist.
            </summary>
            <param name="collection">
            Die Sammlung, in der nach den Nullelementen gesucht werden soll.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="collection"/>
            enthält ein Nullelement. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testet, ob alle Elemente in der angegebenen Sammlung ungleich null sind, und löst
            eine Ausnahme aus, wenn eines der Elemente NULL ist.
            </summary>
            <param name="collection">
            Die Sammlung, in der nach den Nullelementen gesucht werden soll.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="collection"/>
            enthält ein Nullelement. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection)">
            <summary>
            Testet, ob alle Elemente in der angegebenen Sammlung eindeutig sind, und
            löst eine Ausnahme aus, wenn zwei Elemente in der Sammlung gleich sind.
            </summary>
            <param name="collection">
            Die Sammlung, in der nach Elementduplikaten gesucht werden soll.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String)">
            <summary>
            Testet, ob alle Elemente in der angegebenen Sammlung eindeutig sind, und
            löst eine Ausnahme aus, wenn zwei Elemente in der Sammlung gleich sind.
            </summary>
            <param name="collection">
            Die Sammlung, in der nach Elementduplikaten gesucht werden soll.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="collection"/>
            enthält mindestens ein Elementduplikat. Die Meldung wird in
            den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testet, ob alle Elemente in der angegebenen Sammlung eindeutig sind, und
            löst eine Ausnahme aus, wenn zwei Elemente in der Sammlung gleich sind.
            </summary>
            <param name="collection">
            Die Sammlung, in der nach Elementduplikaten gesucht werden soll.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="collection"/>
            enthält mindestens ein Elementduplikat. Die Meldung wird in
            den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testet, ob eine Sammlung eine Untermenge einer anderen Sammlung ist, und
            löst eine Ausnahme aus, wenn ein beliebiges Element in der Untermenge nicht auch in der
            Obermenge enthalten ist.
            </summary>
            <param name="subset">
            Die Sammlung, von der erwartet wird, dass sie eine Untermenge ist von <paramref name="superset"/>.
            </param>
            <param name="superset">
            Die Sammlung, von der erwartet wird, dass sie eine Obermenge ist von <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testet, ob eine Sammlung eine Untermenge einer anderen Sammlung ist, und
            löst eine Ausnahme aus, wenn ein beliebiges Element in der Untermenge nicht auch in der
            Obermenge enthalten ist.
            </summary>
            <param name="subset">
            Die Sammlung, von der erwartet wird, dass sie eine Untermenge ist von <paramref name="superset"/>.
            </param>
            <param name="superset">
            Die Sammlung, von der erwartet wird, dass sie eine Obermenge ist von <paramref name="subset"/>
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn ein Element in
            <paramref name="subset"/> wurde nicht gefunden in <paramref name="superset"/>.
            Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testet, ob eine Sammlung eine Untermenge einer anderen Sammlung ist, und
            löst eine Ausnahme aus, wenn ein beliebiges Element in der Untermenge nicht auch in der
            Obermenge enthalten ist.
            </summary>
            <param name="subset">
            Die Sammlung, von der erwartet wird, dass sie eine Untermenge ist von <paramref name="superset"/>.
            </param>
            <param name="superset">
            Die Sammlung, von der erwartet wird, dass sie eine Obermenge ist von <paramref name="subset"/>
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn ein Element in
            <paramref name="subset"/> wurde nicht gefunden in <paramref name="superset"/>.
            Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testet, ob eine Sammlung eine Untermenge einer anderen Sammlung ist, und
            löst eine Ausnahme aus, wenn alle Elemente in der Untermenge auch in der
            Obermenge enthalten sind.
            </summary>
            <param name="subset">
            Die Sammlung, von der erwartet wird, dass sie keine Untermenge ist von <paramref name="superset"/>.
            </param>
            <param name="superset">
            Die Sammlung, von der erwartet wird, dass sie keine Obermenge ist von <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testet, ob eine Sammlung eine Untermenge einer anderen Sammlung ist, und
            löst eine Ausnahme aus, wenn alle Elemente in der Untermenge auch in der
            Obermenge enthalten sind.
            </summary>
            <param name="subset">
            Die Sammlung, von der erwartet wird, dass sie keine Untermenge ist von <paramref name="superset"/>.
            </param>
            <param name="superset">
            Die Sammlung, von der erwartet wird, dass sie keine Obermenge ist von <paramref name="subset"/>
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn jedes Element in
            <paramref name="subset"/> auch gefunden wird in <paramref name="superset"/>.
            Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testet, ob eine Sammlung eine Untermenge einer anderen Sammlung ist, und
            löst eine Ausnahme aus, wenn alle Elemente in der Untermenge auch in der
            Obermenge enthalten sind.
            </summary>
            <param name="subset">
            Die Sammlung, von der erwartet wird, dass sie keine Untermenge ist von <paramref name="superset"/>.
            </param>
            <param name="superset">
            Die Sammlung, von der erwartet wird, dass sie keine Obermenge ist von <paramref name="subset"/>
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn jedes Element in
            <paramref name="subset"/> auch gefunden wird in <paramref name="superset"/>.
            Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testet, ob zwei Sammlungen die gleichen Elemente enthalten, und löst eine
            Ausnahme aus, wenn eine der Sammlungen ein Element enthält, das in der anderen
            Sammlung nicht enthalten ist.
            </summary>
            <param name="expected">
            Die erste zu vergleichende Sammlung. Enthält die Elemente, die der Test
            erwartet.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testet, ob zwei Sammlungen die gleichen Elemente enthalten, und löst eine
            Ausnahme aus, wenn eine der Sammlungen ein Element enthält, das in der anderen
            Sammlung nicht enthalten ist.
            </summary>
            <param name="expected">
            Die erste zu vergleichende Sammlung. Enthält die Elemente, die der Test
            erwartet.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn ein Element in einer
            der Sammlungen gefunden wurde, aber nicht in der anderen. Die Meldung wird in
            den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testet, ob zwei Sammlungen die gleichen Elemente enthalten, und löst eine
            Ausnahme aus, wenn eine der Sammlungen ein Element enthält, das in der anderen
            Sammlung nicht enthalten ist.
            </summary>
            <param name="expected">
            Die erste zu vergleichende Sammlung. Enthält die Elemente, die der Test
            erwartet.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn ein Element in einer
            der Sammlungen gefunden wurde, aber nicht in der anderen. Die Meldung wird in
            den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testet, ob zwei Sammlungen verschiedene Elemente enthalten, und löst eine
            Ausnahme aus, wenn die beiden Sammlungen identische Elemente enthalten (ohne Berücksichtigung
            der Reihenfolge).
            </summary>
            <param name="expected">
            Die erste zu vergleichende Sammlung. Enthält die Elemente, von denen der Test erwartet,
            dass sie sich von der tatsächlichen Sammlung unterscheiden.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testet, ob zwei Sammlungen verschiedene Elemente enthalten, und löst eine
            Ausnahme aus, wenn die beiden Sammlungen identische Elemente enthalten (ohne Berücksichtigung
            der Reihenfolge).
            </summary>
            <param name="expected">
            Die erste zu vergleichende Sammlung. Enthält die Elemente, von denen der Test erwartet,
            dass sie sich von der tatsächlichen Sammlung unterscheiden.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            enthält die gleichen Elemente wie <paramref name="expected"/>. Die Meldung
            wird in den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testet, ob zwei Sammlungen verschiedene Elemente enthalten, und löst eine
            Ausnahme aus, wenn die beiden Sammlungen identische Elemente enthalten (ohne Berücksichtigung
            der Reihenfolge).
            </summary>
            <param name="expected">
            Die erste zu vergleichende Sammlung. Enthält die Elemente, von denen der Test erwartet,
            dass sie sich von der tatsächlichen Sammlung unterscheiden.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            enthält die gleichen Elemente wie <paramref name="expected"/>. Die Meldung
            wird in den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type)">
            <summary>
            Testet, ob alle Elemente in der angegebenen Sammlung Instanzen
            des erwarteten Typs sind, und löst eine Ausnahme aus, wenn der erwartete Typ sich
            nicht in der Vererbungshierarchie mindestens eines Elements befindet.
            </summary>
            <param name="collection">
            Die Sammlung, die Elemente enthält, von denen der Test erwartet, dass sie
            vom angegebenen Typ sind.
            </param>
            <param name="expectedType">
            Der erwartete Typ jedes Elements von <paramref name="collection"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String)">
            <summary>
            Testet, ob alle Elemente in der angegebenen Sammlung Instanzen
            des erwarteten Typs sind, und löst eine Ausnahme aus, wenn der erwartete Typ sich
            nicht in der Vererbungshierarchie mindestens eines Elements befindet.
            </summary>
            <param name="collection">
            Die Sammlung, die Elemente enthält, von denen der Test erwartet, dass sie
            vom angegebenen Typ sind.
            </param>
            <param name="expectedType">
            Der erwartete Typ jedes Elements von <paramref name="collection"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn ein Element in
            <paramref name="collection"/> ist keine Instanz von
            <paramref name="expectedType"/>. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String,System.Object[])">
            <summary>
            Testet, ob alle Elemente in der angegebenen Sammlung Instanzen
            des erwarteten Typs sind, und löst eine Ausnahme aus, wenn der erwartete Typ sich
            nicht in der Vererbungshierarchie mindestens eines Elements befindet.
            </summary>
            <param name="collection">
            Die Sammlung, die Elemente enthält, von denen der Test erwartet, dass sie
            vom angegebenen Typ sind.
            </param>
            <param name="expectedType">
            Der erwartete Typ jedes Elements von <paramref name="collection"/>.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn ein Element in
            <paramref name="collection"/> ist keine Instanz von
            <paramref name="expectedType"/>. Die Meldung wird in den Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testet, ob die angegebenen Sammlungen gleich sind, und löst eine Ausnahme aus,
            wenn die beiden Sammlungen ungleich sind. "Gleichheit" wird definiert durch die gleichen
            Elemente in der gleichen Reihenfolge und Anzahl. Unterschiedliche Verweise auf den gleichen
            Wert werden als gleich betrachtet.
            </summary>
            <param name="expected">
            Die erste zu vergleichende Sammlung. Dies ist die Sammlung, die der Test erwartet.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testet, ob die angegebenen Sammlungen gleich sind, und löst eine Ausnahme aus,
            wenn die beiden Sammlungen ungleich sind. "Gleichheit" wird definiert durch die gleichen
            Elemente in der gleichen Reihenfolge und Anzahl. Unterschiedliche Verweise auf den gleichen
            Wert werden als gleich betrachtet.
            </summary>
            <param name="expected">
            Die erste zu vergleichende Sammlung. Dies ist die Sammlung, die der Test erwartet.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist nicht gleich <paramref name="expected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Sammlungen gleich sind, und löst eine Ausnahme aus,
            wenn die beiden Sammlungen ungleich sind. "Gleichheit" wird definiert durch die gleichen
            Elemente in der gleichen Reihenfolge und Anzahl. Unterschiedliche Verweise auf den gleichen
            Wert werden als gleich betrachtet.
            </summary>
            <param name="expected">
            Die erste zu vergleichende Sammlung. Dies ist die Sammlung, die der Test erwartet.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist nicht gleich <paramref name="expected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Testet, ob die angegebenen Sammlungen ungleich sind, und löst eine Ausnahme aus,
            wenn die beiden Sammlungen gleich sind. "Gleichheit" wird definiert durch die gleichen
            Elemente in der gleichen Reihenfolge und Anzahl. Unterschiedliche Verweise auf den gleichen
            Wert werden als gleich betrachtet.
            </summary>
            <param name="notExpected">
            Die erste zu vergleichende Sammlung. Dies ist die Sammlung, mit der der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Testet, ob die angegebenen Sammlungen ungleich sind, und löst eine Ausnahme aus,
            wenn die beiden Sammlungen gleich sind. "Gleichheit" wird definiert durch die gleichen
            Elemente in der gleichen Reihenfolge und Anzahl. Unterschiedliche Verweise auf den gleichen
            Wert werden als gleich betrachtet.
            </summary>
            <param name="notExpected">
            Die erste zu vergleichende Sammlung. Dies ist die Sammlung, mit der der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist gleich <paramref name="notExpected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Sammlungen ungleich sind, und löst eine Ausnahme aus,
            wenn die beiden Sammlungen gleich sind. "Gleichheit" wird definiert durch die gleichen
            Elemente in der gleichen Reihenfolge und Anzahl. Unterschiedliche Verweise auf den gleichen
            Wert werden als gleich betrachtet.
            </summary>
            <param name="notExpected">
            Die erste zu vergleichende Sammlung. Dies ist die Sammlung, mit der der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist gleich <paramref name="notExpected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Testet, ob die angegebenen Sammlungen gleich sind, und löst eine Ausnahme aus,
            wenn die beiden Sammlungen ungleich sind. "Gleichheit" wird definiert durch die gleichen
            Elemente in der gleichen Reihenfolge und Anzahl. Unterschiedliche Verweise auf den gleichen
            Wert werden als gleich betrachtet.
            </summary>
            <param name="expected">
            Die erste zu vergleichende Sammlung. Dies ist die Sammlung, die der Test erwartet.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <param name="comparer">
            Die zu verwendende Vergleichsimplementierung beim Vergleichen von Elementen der Sammlung.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Testet, ob die angegebenen Sammlungen gleich sind, und löst eine Ausnahme aus,
            wenn die beiden Sammlungen ungleich sind. "Gleichheit" wird definiert durch die gleichen
            Elemente in der gleichen Reihenfolge und Anzahl. Unterschiedliche Verweise auf den gleichen
            Wert werden als gleich betrachtet.
            </summary>
            <param name="expected">
            Die erste zu vergleichende Sammlung. Dies ist die Sammlung, die der Test erwartet.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <param name="comparer">
            Die zu verwendende Vergleichsimplementierung beim Vergleichen von Elementen der Sammlung.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist nicht gleich <paramref name="expected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Sammlungen gleich sind, und löst eine Ausnahme aus,
            wenn die beiden Sammlungen ungleich sind. "Gleichheit" wird definiert durch die gleichen
            Elemente in der gleichen Reihenfolge und Anzahl. Unterschiedliche Verweise auf den gleichen
            Wert werden als gleich betrachtet.
            </summary>
            <param name="expected">
            Die erste zu vergleichende Sammlung. Dies ist die Sammlung, die der Test erwartet.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <param name="comparer">
            Die zu verwendende Vergleichsimplementierung beim Vergleichen von Elementen der Sammlung.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist nicht gleich <paramref name="expected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Testet, ob die angegebenen Sammlungen ungleich sind, und löst eine Ausnahme aus,
            wenn die beiden Sammlungen gleich sind. "Gleichheit" wird definiert durch die gleichen
            Elemente in der gleichen Reihenfolge und Anzahl. Unterschiedliche Verweise auf den gleichen
            Wert werden als gleich betrachtet.
            </summary>
            <param name="notExpected">
            Die erste zu vergleichende Sammlung. Dies ist die Sammlung, mit der der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <param name="comparer">
            Die zu verwendende Vergleichsimplementierung beim Vergleichen von Elementen der Sammlung.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Testet, ob die angegebenen Sammlungen ungleich sind, und löst eine Ausnahme aus,
            wenn die beiden Sammlungen gleich sind. "Gleichheit" wird definiert durch die gleichen
            Elemente in der gleichen Reihenfolge und Anzahl. Unterschiedliche Verweise auf den gleichen
            Wert werden als gleich betrachtet.
            </summary>
            <param name="notExpected">
            Die erste zu vergleichende Sammlung. Dies ist die Sammlung, mit der der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <param name="comparer">
            Die zu verwendende Vergleichsimplementierung beim Vergleichen von Elementen der Sammlung.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist gleich <paramref name="notExpected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Testet, ob die angegebenen Sammlungen ungleich sind, und löst eine Ausnahme aus,
            wenn die beiden Sammlungen gleich sind. "Gleichheit" wird definiert durch die gleichen
            Elemente in der gleichen Reihenfolge und Anzahl. Unterschiedliche Verweise auf den gleichen
            Wert werden als gleich betrachtet.
            </summary>
            <param name="notExpected">
            Die erste zu vergleichende Sammlung. Dies ist die Sammlung, mit der der Test keine
            Übereinstimmung erwartet. <paramref name="actual"/>.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung. Dies ist die Sammlung, die vom
            zu testenden Code generiert wird.
            </param>
            <param name="comparer">
            Die zu verwendende Vergleichsimplementierung beim Vergleichen von Elementen der Sammlung.
            </param>
            <param name="message">
            Die in die Ausnahme einzuschließende Meldung, wenn <paramref name="actual"/>
            ist gleich <paramref name="notExpected"/>. Die Meldung wird in den
            Testergebnissen angezeigt.
            </param>
            <param name="parameters">
            Ein zu verwendendes Array von Parametern beim Formatieren von: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOfHelper(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Ermittelt, ob die erste Sammlung eine Teilmenge der zweiten
            Sammlung ist. Wenn eine der Mengen Elementduplikate enthält, muss die Anzahl
            der Vorkommen des Elements in der Teilmenge kleiner oder
            gleich der Anzahl der Vorkommen in der Obermenge sein.
            </summary>
            <param name="subset">
            Die Sammlung, von der der Test erwartet, dass sie enthalten ist in <paramref name="superset"/>.
            </param>
            <param name="superset">
            Die Sammlung, von der der Test erwartet, dass sie Folgendes enthält: <paramref name="subset"/>.
            </param>
            <returns>
            TRUE, wenn: <paramref name="subset"/> eine Teilmenge ist von
            <paramref name="superset"/>, andernfalls FALSE.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.GetElementCounts(System.Collections.ICollection,System.Int32@)">
            <summary>
            Generiert ein Wörterbuch, das Anzahl der Vorkommen jedes
            Elements in der angegebenen Sammlung enthält.
            </summary>
            <param name="collection">
            Die zu verarbeitende Sammlung.
            </param>
            <param name="nullCount">
            Die Anzahl der Nullelemente in der Sammlung.
            </param>
            <returns>
            Ein Wörterbuch, das Anzahl der Vorkommen jedes
            Elements in der angegebenen Sammlung enthält.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.FindMismatchedElement(System.Collections.ICollection,System.Collections.ICollection,System.Int32@,System.Int32@,System.Object@)">
            <summary>
            Findet ein nicht übereinstimmendes Element in den beiden Sammlungen. Ein nicht übereinstimmendes
            Element ist ein Element, für das sich die Anzahl der Vorkommen in der
            erwarteten Sammlung von der Anzahl der Vorkommen in der tatsächlichen Sammlung unterscheidet. Von den
            Sammlungen wird angenommen, dass unterschiedliche Verweise ungleich null mit der
            gleichen Anzahl von Elementen vorhanden sind. Der Aufrufer ist für diese Ebene
            der Überprüfung verantwortlich. Wenn kein nicht übereinstimmendes Element vorhanden ist, gibt die Funktion FALSE
            zurück, und die out-Parameter sollten nicht verwendet werden.
            </summary>
            <param name="expected">
            Die erste zu vergleichende Sammlung.
            </param>
            <param name="actual">
            Die zweite zu vergleichende Sammlung.
            </param>
            <param name="expectedCount">
            Die erwartete Anzahl von Vorkommen von
            <paramref name="mismatchedElement"/> oder 0, wenn kein nicht übereinstimmendes
            Element vorhanden ist.
            </param>
            <param name="actualCount">
            Die tatsächliche Anzahl von Vorkommen von
            <paramref name="mismatchedElement"/> oder 0, wenn kein nicht übereinstimmendes
            Element vorhanden ist.
            </param>
            <param name="mismatchedElement">
            Das nicht übereinstimmende Element (kann NULL sein) oder NULL, wenn kein nicht
            übereinstimmendes Element vorhanden ist.
            </param>
            <returns>
            TRUE, wenn ein nicht übereinstimmendes Element gefunden wurde, andernfalls FALSE.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.ObjectComparer">
            <summary>
            vergleicht die Objekte mithilfe von object.Equals
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException">
            <summary>
            Basisklasse für Frameworkausnahmen.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>-Klasse.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String,System.Exception)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>-Klasse.
            </summary>
            <param name="msg"> Die Meldung. </param>
            <param name="ex"> Die Ausnahme. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>-Klasse.
            </summary>
            <param name="msg"> Die Meldung. </param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages">
            <summary>
              Eine stark typisierte Ressourcenklasse zum Suchen nach lokalisierten Zeichenfolgen usw.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ResourceManager">
            <summary>
              Gibt die zwischengespeicherte ResourceManager-Instanz zurück, die von dieser Klasse verwendet wird.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Culture">
            <summary>
              Überschreibt die CurrentUICulture-Eigenschaft des aktuellen Threads für alle
            Ressourcensuchen mithilfe dieser stark typisierten Ressourcenklasse.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AccessStringInvalidSyntax">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Zugriffszeichenfolge weist ungültige Syntax auf." nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ActualHasMismatchedElements">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Erwartete Sammlung enthält {1} Vorkommen von &lt;{2}&gt;. Die tatsächliche Sammlung enthält {3} Vorkommen. {0}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AllItemsAreUniqueFailMsg">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Elementduplikat gefunden: &lt;{1}&gt;. {0}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualCaseFailMsg">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Erwartet: &lt;{1}&gt;. Groß-/Kleinschreibung unterscheidet sich für den tatsächlichen Wert: &lt;{2}&gt;. {0}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDeltaFailMsg">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Differenz nicht größer als &lt;{3}&gt; zwischen erwartetem Wert &lt;{1}&gt; und tatsächlichem Wert &lt;{2}&gt; erwartet. {0}" nach.
        </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDifferentTypesFailMsg">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Erwartet: &lt;{1} ({2})&gt;. Tatsächlich: &lt;{3} ({4})&gt;. {0}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualFailMsg">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Erwartet: &lt;{1}&gt;. Tatsächlich: &lt;{2}&gt;. {0}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualDeltaFailMsg">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Differenz größer als &lt;{3}&gt; zwischen erwartetem Wert &lt;{1}&gt; und tatsächlichem Wert &lt;{2}&gt; erwartet. {0}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualFailMsg">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Beliebiger Wert erwartet, ausgenommen: &lt;{1}&gt;. Tatsächlich: &lt;{2}&gt;. {0}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreSameGivenValues">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Übergeben Sie keine Werttypen an AreSame(). In Object konvertierte Werte sind nie gleich. Verwenden Sie ggf. AreEqual(). {0}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AssertionFailed">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Fehler von {0}. {1}" nach.
           </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AsyncUITestMethodNotSupported">
            <summary>
              Sucht nach einer lokalisierten Zeichenfolge ähnlich der folgenden: "async TestMethod" wird mit UITestMethodAttribute nicht unterstützt. Entfernen Sie "async", oder verwenden Sie TestMethodAttribute.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsEmpty">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Beide Sammlungen sind leer. {0}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameElements">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Beide Sammlungen enthalten die gleichen Elemente." nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameReference">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Beide Sammlungsverweise zeigen auf das gleiche Sammlungsobjekt. {0}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothSameElements">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Beide Sammlungen enthalten die gleichen Elemente. {0}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.CollectionEqualReason">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "{0}({1})." nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_NullInMessages">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "(null)" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_ObjectString">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "(object)" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ContainsFail">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Zeichenfolge '{0}' enthält nicht Zeichenfolge '{1}'. {2}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DataDrivenResultDisplayName">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "{0} ({1})." nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DoNotUseAssertEquals">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Assert.Equals sollte für Assertionen nicht verwendet werden. Verwenden Sie stattdessen Assert.AreEqual &amp; Überladungen." nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementNumbersDontMatch">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Die Anzahl der Elemente in den Sammlungen stimmt nicht überein. Erwartet: &lt;{1}&gt;. Tatsächlich: &lt;{2}&gt;. {0}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementsAtIndexDontMatch">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Element am Index {0} stimmt nicht überein." nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Element am Index {1} weist nicht den erwarteten Typ auf. Erwarteter Typ: &lt;{2}&gt;. Tatsächlicher Typ: &lt;{3}&gt;. {0}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch2">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Element am Index {1} ist (null). Erwarteter Typ: &lt;{2}&gt;. {0}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EndsWithFail">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Zeichenfolge '{0}' endet nicht mit Zeichenfolge '{1}'. {2}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EqualsTesterInvalidArgs">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Ungültiges Argument: EqualsTester darf keine NULL-Werte verwenden." nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ErrorInvalidCast">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Objekt vom Typ {0} kann nicht in {1} konvertiert werden." nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InternalObjectNotValid">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Das referenzierte interne Objekt ist nicht mehr gültig." nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidParameterToAssert">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Der Parameter '{0}' ist ungültig. {1}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidPropertyType">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Die Eigenschaft {0} weist den Typ {1} auf. Erwartet wurde der Typ {2}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsInstanceOfFailMsg">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "{0} Erwarteter Typ: &lt;{1}&gt;. Tatsächlicher Typ: &lt;{2}&gt;." nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsMatchFail">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Zeichenfolge '{0}' stimmt nicht mit dem Muster '{1}' überein. {2}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotInstanceOfFailMsg">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Falscher Typ: &lt;{1}&gt;. Tatsächlicher Typ: &lt;{2}&gt;. {0}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotMatchFail">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Zeichenfolge '{0}' stimmt mit dem Muster '{1}' überein. {2}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoDataRow">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Kein DataRowAttribute angegeben. Mindestens ein DataRowAttribute ist mit DataTestMethodAttribute erforderlich." nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoExceptionThrown">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Keine Ausnahme ausgelöst. {1}-Ausnahme wurde erwartet. {0}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NullParameterToAssert">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Der Parameter '{0}' ist ungültig. Der Wert darf nicht NULL sein. {1}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NumberOfElementsDiff">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Unterschiedliche Anzahl von Elementen." nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorConstructorNotFound">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich 
                 "Der Konstruktor mit der angegebenen Signatur wurde nicht gefunden. Möglicherweise müssen Sie Ihren privaten Accessor erneut generieren,
                 oder der Member ist ggf. privat und für eine Basisklasse definiert. Wenn Letzteres zutrifft, müssen Sie den Typ an den
                 Konstruktor von PrivateObject übergeben, der den Member definiert." nach.
               .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorMemberNotFound">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich 
                 "Der angegebene Member ({0}) wurde nicht gefunden. Möglicherweise müssen Sie Ihren privaten Accessor erneut generieren,
                 oder der Member ist ggf. privat und für eine Basisklasse definiert. Wenn Letzteres zutrifft, müssen Sie den Typ an den
                 Konstruktor von PrivateObject übergeben, der den Member definiert." nach.
               .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.StartsWithFail">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Die Zeichenfolge '{0}' beginnt nicht mit der Zeichenfolge '{1}'. {2}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_ExpectedExceptionTypeMustDeriveFromException">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Der erwartete Ausnahmetyp muss System.Exception oder ein von System.Exception abgeleiteter Typ sein." nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_FailedToGetExceptionMessage">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "(Fehler beim Abrufen der Meldung vom Typ {0} aufgrund einer Ausnahme.)" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoException">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Testmethode hat erwartete Ausnahme {0} nicht ausgelöst. {1}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoExceptionDefault">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Die Testmethode hat keine Ausnahme ausgelöst. Vom Attribut {0}, das für die Testmethode definiert ist, wurde eine Ausnahme erwartet." nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongException">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Testmethode hat Ausnahme {0} ausgelöst, aber Ausnahme {1} wurde erwartet. Ausnahmemeldung: {2}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongExceptionDerivedAllowed">
            <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Testmethode hat Ausnahme {0} ausgelöst, aber Ausnahme {1} oder ein davon abgeleiteter Typ wurde erwartet. Ausnahmemeldung: {2}" nach.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.WrongExceptionThrown">
             <summary>
              Schlägt eine lokalisierte Zeichenfolge ähnlich "Ausnahme {2} wurde ausgelöst, aber Ausnahme {1} wurde erwartet. {0}
            Ausnahmemeldung: {3}
            Stapelüberwachung: {4}" nach.
          </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome">
            <summary>
            Ergebnisse des Komponententests
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Failed">
            <summary>
            Der Test wurde ausgeführt, aber es gab Probleme.
            Möglicherweise liegen Ausnahmen oder Assertionsfehler vor.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Inconclusive">
            <summary>
            Der Test wurde abgeschlossen, es lässt sich aber nicht sagen, ob er bestanden wurde oder fehlerhaft war.
            Kann für abgebrochene Tests verwendet werden.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Passed">
            <summary>
            Der Test wurde ohne Probleme ausgeführt.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.InProgress">
            <summary>
            Der Test wird zurzeit ausgeführt.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Error">
            <summary>
            Systemfehler beim Versuch, einen Test auszuführen.
         </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Timeout">
            <summary>
            Timeout des Tests.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Aborted">
            <summary>
            Der Test wurde vom Benutzer abgebrochen.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Unknown">
            <summary>
            Der Test weist einen unbekannten Zustand auf.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper">
            <summary>
            Stellt Hilfsfunktionen für das Komponententestframework bereit.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper.GetExceptionMsg(System.Exception)">
            <summary>
            Ruft die Ausnahmemeldungen (einschließlich der Meldungen für alle inneren Ausnahmen)
            rekursiv ab.
            </summary>
            <param name="ex">Ausnahme, für die Meldungen abgerufen werden sollen</param>
            <returns>Zeichenfolge mit Fehlermeldungsinformationen</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout">
            <summary>
            Enumeration für Timeouts, die mit der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/>-Klasse verwendet werden kann.
            Der Typ der Enumeration muss entsprechen:
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout.Infinite">
            <summary>
            Unendlich.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute">
            <summary>
            Das Testklassenattribut.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute.GetTestMethodAttribute(Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute)">
            <summary>
            Erhält ein Testmethodenattribut, das die Ausführung des Tests ermöglicht.
            </summary>
            <param name="testMethodAttribute">Die für diese Methode definierte Attributinstanz der Testmethode.</param>
            <returns>Die<see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/>zum Ausführen dieses Tests</returns>
            <remarks>Extensions can override this method to customize how all methods in a class are run.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute">
            <summary>
            Das Testmethodenattribut.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Führt eine Testmethode aus.
            </summary>
            <param name="testMethod">Die auszuführende Textmethode.</param>
            <returns>Ein Array aus TestResult-Objekten, die für die Ergebnisses des Tests stehen.</returns>
            <remarks>Extensions can override this method to customize running a TestMethod.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestInitializeAttribute">
            <summary>
            Das Testinitialisierungsattribut.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCleanupAttribute">
            <summary>
            Das Testbereinigungsattribut.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute">
            <summary>
            Das Ignorierattribut.
          </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute">
            <summary>
            Das Testeigenschaftattribut.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.#ctor(System.String,System.String)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute"/>-Klasse.
            </summary>
            <param name="name">
            Der Name.
            </param>
            <param name="value">
            Der Wert.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Name">
            <summary>
            Ruft den Namen ab.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Value">
            <summary>
            Ruft den Wert ab.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute">
            <summary>
            Das Klasseninitialisierungsattribut.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute">
            <summary>
            Das Klassenbereinigungsattribut.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyInitializeAttribute">
            <summary>
            Das Assemblyinitialisierungsattribut.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyCleanupAttribute">
            <summary>
            Das Assemblybereinigungsattribut.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute">
            <summary>
            Der Testbesitzer.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.#ctor(System.String)">
            <summary>
            Initialisiert eine neue Instanz der<see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute"/>-Klasse.
            </summary>
            <param name="owner">
            Der Besitzer.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.Owner">
            <summary>
            Ruft den Besitzer ab.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute">
            <summary>
            Prioritätsattribut. Wird zum Angeben der Priorität eines Komponententests verwendet.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.#ctor(System.Int32)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute"/>-Klasse.
            </summary>
            <param name="priority">
            Die Priorität.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.Priority">
            <summary>
            Ruft die Priorität ab.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute">
            <summary>
            Die Beschreibung des Tests.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.#ctor(System.String)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute"/>-Klasse zum Beschreiben eines Tests.
            </summary>
            <param name="description">Die Beschreibung.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.Description">
            <summary>
            Ruft die Beschreibung eines Tests ab.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute">
            <summary>
            Der URI der CSS-Projektstruktur.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.#ctor(System.String)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute"/>-Klasse der CSS Projektstruktur-URI.
            </summary>
            <param name="cssProjectStructure">Der CSS-Projektstruktur-URI.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.CssProjectStructure">
            <summary>
            Ruft den CSS-Projektstruktur-URI ab.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute">
            <summary>
            Der URI der CSS-Iteration.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.#ctor(System.String)">
            <summary>
            Initialisiert eine neue Instanz der<see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute"/>-Klasse für den CSS Iterations-URI.
            </summary>
            <param name="cssIteration">Der CSS-Iterations-URI.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.CssIteration">
            <summary>
            Ruft den CSS-Iterations-URI ab.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute">
            <summary>
            WorkItem-Attribut. Wird zum Angeben eines Arbeitselements verwendet, das diesem Test zugeordnet ist.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.#ctor(System.Int32)">
            <summary>
            Initialisiert eine neue Instanz der<see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute"/>-Klasse für das WorkItem-Attribut.
            </summary>
            <param name="id">Die ID eines Arbeitselements.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.Id">
            <summary>
            Ruft die ID für ein zugeordnetes Arbeitselement ab.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute">
            <summary>
            Timeoutattribut. Wird zum Angeben des Timeouts eines Komponententests verwendet.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(System.Int32)">
            <summary>
             Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/>-Klasse.
            </summary>
            <param name="timeout">
            Das Timeout.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/>-Klasse mit einem voreingestellten Timeout.
            </summary>
            <param name="timeout">
            Das Timeout.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.Timeout">
            <summary>
            Ruft das Timeout ab.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult">
            <summary>
            Das TestResult-Objekt, das an den Adapter zurückgegeben werden soll.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.#ctor">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>-Klasse.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DisplayName">
            <summary>
            Ruft den Anzeigenamen des Ergebnisses ab oder legt ihn fest. Hilfreich, wenn mehrere Ergebnisse zurückgegeben werden.
            Wenn NULL, wird der Methodenname als DisplayName verwendet.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Outcome">
            <summary>
            Ruft das Ergebnis der Testausführung ab oder legt es fest.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestFailureException">
            <summary>
            Ruft die Ausnahme ab, die bei einem Testfehler ausgelöst wird, oder legt sie fest.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogOutput">
            <summary>
            Ruft die Ausgabe der Meldung ab, die vom Testcode protokolliert wird, oder legt sie fest.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogError">
            <summary>
            Ruft die Ausgabe der Meldung ab, die vom Testcode protokolliert wird, oder legt sie fest.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DebugTrace">
            <summary>
            Ruft die Debugablaufverfolgungen nach Testcode fest oder legt sie fest.
           </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestContextMessages">
            <summary>
            Gets or sets the debug traces by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Duration">
            <summary>
            Ruft die Dauer der Testausführung ab oder legt sie fest.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DatarowIndex">
            <summary>
            Ruft den Datenzeilenindex in der Datenquelle ab, oder legt ihn fest. Nur festgelegt für Ergebnisse einer individuellen
            Ausführung einer Datenzeile eines datengesteuerten Tests.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ReturnValue">
            <summary>
            Ruft den Rückgabewert der Testmethode ab (zurzeit immer NULL).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ResultFiles">
            <summary>
            Ruft die vom Test angehängten Ergebnisdateien ab, oder legt sie fest.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute">
            <summary>
            Gibt die Verbindungszeichenfolge, den Tabellennamen und die Zeilenzugriffsmethode für datengesteuerte Tests an.
            </summary>
            <example>
            [DataSource("Provider=SQLOLEDB.1;Data Source=source;Integrated Security=SSPI;Initial Catalog=EqtCoverage;Persist Security Info=False", "MyTable")]
            [DataSource("dataSourceNameFromConfigFile")]
            </example>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultProviderName">
            <summary>
            Der Standardanbietername für DataSource.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultDataAccessMethod">
            <summary>
            Die standardmäßige Datenzugriffsmethode.
           </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String,System.String,Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>-Klasse. Diese Instanz wird mit einem Datenanbieter, einer Verbindungszeichenfolge, einer Datentabelle und einer Datenzugriffsmethode für den Zugriff auf die Daten initialisiert.
            </summary>
            <param name="providerInvariantName">Invarianter Datenanbietername, z. B. "System.Data.SqlClient"</param>
            <param name="connectionString">
            Die für den Datenanbieter spezifische Verbindungszeichenfolge.
            WARNUNG: Die Verbindungszeichenfolge kann sensible Daten (z. B. ein Kennwort) enthalten.
            Die Verbindungszeichenfolge wird als Nur-Text im Quellcode und in der kompilierten Assembly gespeichert.
            Schränken Sie den Zugriff auf den Quellcode und die Assembly ein, um diese vertraulichen Informationen zu schützen.
            </param>
            <param name="tableName">Der Name der Datentabelle.</param>
            <param name="dataAccessMethod">Gibt die Reihenfolge für den Datenzugriff an.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>-Klasse. Diese Instanz wird mit einer Verbindungszeichenfolge und einem Tabellennamen initialisiert.
            Geben Sie eine Verbindungszeichenfolge und Datentabelle an, um auf die OLEDB-Datenquelle zuzugreifen.
            </summary>
            <param name="connectionString">
            Die für den Datenanbieter spezifische Verbindungszeichenfolge.
            WARNUNG: Die Verbindungszeichenfolge kann sensible Daten (z. B. ein Kennwort) enthalten.
            Die Verbindungszeichenfolge wird als Nur-Text im Quellcode und in der kompilierten Assembly gespeichert.
            Schränken Sie den Zugriff auf den Quellcode und die Assembly ein, um diese vertraulichen Informationen zu schützen.
    </param>
            <param name="tableName">Der Name der Datentabelle.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String)">
            <summary>
            Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>-Klasse.  Diese Instanz wird mit einem Datenanbieter und einer Verbindungszeichenfolge mit dem Namen der Einstellung initialisiert.
            </summary>
            <param name="dataSourceSettingName">Der Name einer Datenquelle, die im Abschnitt &lt;microsoft.visualstudio.qualitytools&gt; in der Datei "app.config" gefunden wurde.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ProviderInvariantName">
            <summary>
            Ruft einen Wert ab, der den Datenanbieter der Datenquelle darstellt.
            </summary>
            <returns>
            Der Name des Datenanbieters. Wenn kein Datenanbieter während der Objektinitialisierung festgelegt wurde, wird der Standardanbieter "System.Data.OleDb" zurückgegeben.
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ConnectionString">
            <summary>
            Ruft einen Wert ab, der die Verbindungszeichenfolge für die Datenquelle darstellt.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.TableName">
            <summary>
            Ruft einen Wert ab, der den Tabellennamen angibt, der Daten bereitstellt.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataAccessMethod">
             <summary>
            Ruft die Methode ab, die für den Zugriff auf die Datenquelle verwendet wird.
            </summary>
            
             <returns>
             Einer der<see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod"/>-Werte. Wenn das <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> nicht initialisiert wurde, wird der Standardwert zurückgegeben. <see cref="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random"/>.
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataSourceSettingName">
            <summary>
            Ruft den Namen einer Datenquelle ab, die im Abschnitt &lt;microsoft.visualstudio.qualitytools&gt; in der Datei "app.config" gefunden wurde.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute">
            <summary>
            Ein Attribut für datengesteuerte Tests, in denen Daten inline angegeben werden können.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Ermittelt alle Datenzeilen und beginnt mit der Ausführung.
            </summary>
            <param name="testMethod">
            Die test-Methode.
            </param>
            <returns>
            Ein Array aus <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.RunDataDrivenTest(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod,Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute[])">
            <summary>
            Führt die datengesteuerte Testmethode aus.
            </summary>
            <param name="testMethod"> Die auszuführende Testmethode. </param>
            <param name="dataRows"> Die Datenzeile. </param>
            <returns> Ergebnisse der Ausführung. </returns>
        </member>
    </members>
</doc>
