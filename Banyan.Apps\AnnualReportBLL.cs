﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;

namespace Banyan.Apps
{
    public class AnnualReportBLL : BaseDAL<AnnualReport>
    {
        private readonly AjaxResult ajaxResult = null;
        private SysLogBLL logBLL = new SysLogBLL();
        public AnnualReportBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }
        public void updateLog(string page, string action, string description, Member user, string project = "")
        {
            SysLog log = new SysLog
            {
                Page = page,
                Action = action,
                Project = project,
                Description = description,
                CreatedBy = user == null ? "" : user.RealName,
                Ip = Utility.WebHelper.GetIP(),
                CreatorId = user == null ? 0 : user.Id,
            };
            logBLL.Add(log);
        }
        public override object Add(AnnualReport model)
        {
            Member user = new MemberBLL().GetLogOnUser();
            model.Creator = user.RealName;
            model.AddTime = DateTime.Now;
            model.year = DateTime.Now.Year;
            if (model.Id == 0)
            {
                updateLog("New annualreport", "Save", model.ToJson(), user, model.type);
                return base.Add(model);
            } else {
                updateLog("Update annualreport", "Save", model.ToJson(), user, model.type);
                return Update(model); 
            }
        }
        public AjaxResult DeleteStatus(int id)
        {
            Member user = new MemberBLL().GetLogOnUser();
            updateLog("Delete annualreport", "Save", id +"", user, id +"");
            var model = new AnnualReport();
            model.Id = id;
            model.status = (int)AnnualReportStatus.delete;
            var result = base.Update(model, "status");
            ajaxResult.code = result ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }
        public override bool Update(AnnualReport model)
        {
            Member user = new MemberBLL().GetLogOnUser();
            model.Modifier = user.RealName;
            model.UpdateTime = DateTime.Now;
            return base.Update(model);
        }

        public AjaxResult GetPageList(NameValueCollection paramValues)
        {
            Member user = new MemberBLL().GetLogOnUser();
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            int year = WebHelper.GetValueInt("year", 0, paramValues);
            int id = WebHelper.GetValueInt("id", 0, paramValues);
            string name = WebHelper.GetValue("Name", string.Empty, paramValues);

            string creator = WebHelper.GetValue("creator", "", paramValues);

            string strWhere = $" id<>16 AND Status !={(int)AnnualReportStatus.delete} ";
            if (creator == "")
            {
                if (!(user.Levels == (int)MemberLevels.Administrator || user.Levels == (int)MemberLevels.SuperUser))
                {
                    creator = user.RealName;
                    strWhere += $" AND creator='{creator}' ";
                }
            } else
            {
                strWhere += $" AND creator='{creator}' ";
            }

            if(year != 0)
            {
                strWhere += $" AND year={year} ";
            }

            
            if (id > 0)
            {
                strWhere += $"AND id={id} ";
            }

          
            if (!string.IsNullOrWhiteSpace(name))
            {
                strWhere += $"AND investProject like '%{name}%' ";
            }

            var list = GetList(strWhere, pageSize, pageIndex, "*", "Id DESC");

            foreach (var item in list)
            {
                item.isOperate = item.Creator == user.RealName;
            }
      
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = list;
            ajaxResult.count = GetCount(strWhere);
            return ajaxResult;
        }
    }
}
