﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;
using DBConnection;
using Banyan.Code;

/// <summary>
/// Summary description for data
/// </summary>
/// 
namespace igos_data
{
    public class Data_basic
    {
        #region "Common Attributes"

        protected string _ErrMsg;
        private DataSet _Dataset;
        protected int _intReturn;
        protected object _objReturn;
        private SqlDataReader _dr;
        private string _returnVal;
        public string returnVal
        {
            get { return _returnVal; }
        }


        public string ErrMsg
        {
            get { return _ErrMsg; }
        }

        public DataSet Dataset
        {
            get { return _Dataset; }
        }

        public int AffectedRow
        {
            get { return _intReturn; }
        }

        public object AffectedRowReturn
        {
            get { return _objReturn; }
        }

        public SqlDataReader DataReader
        {
            get { return _dr; }
        }

        protected DBConnection_Basic _dbConnection;

        public Data_basic()
        {
            _dbConnection = new DBConnection_Basic();
        }

        #endregion

        #region "MsSQl Execution"
        public virtual bool ExecuteDataset(string astrQuery)
        {
            SqlDataAdapter DA = new SqlDataAdapter();
            SqlCommand Comm = new SqlCommand();
            DataSet ds = new DataSet();

            try
            {
                if (_dbConnection is DBConnection_Basic)
                {
                    Comm = _dbConnection.Connection.CreateCommand();
                }

                Comm.CommandText = astrQuery;
                Comm.CommandType = CommandType.Text;
                Comm.CommandTimeout = 0;
                DA.SelectCommand = Comm;

                _dbConnection.Connection.Open();
                DA.Fill(ds);

                _Dataset = ds;
                _intReturn = ds.Tables[0].Rows.Count;
                _dbConnection.Connection.Close();
                Comm.Dispose();
                return true;
            }
            catch (Exception ex)
            {
                _ErrMsg = ex.Message;
                return false;
            }
            finally
            {
                if (_dbConnection != null)
                {
                    _dbConnection.Connection.Close();
                }
                Comm.Dispose();
            }
        }

        public virtual bool ExecuteSPDataset(string astrQuery, List<string> lstName, List<string> lstValue)
        {
            SqlDataAdapter DA = new SqlDataAdapter();
            SqlCommand Comm = new SqlCommand();
            DataSet ds = new DataSet();

            try
            {
                if (_dbConnection is DBConnection_Basic)
                {
                    Comm = _dbConnection.Connection.CreateCommand();
                }

                Comm.CommandText = astrQuery;
                Comm.CommandType = CommandType.StoredProcedure;
                Comm.CommandTimeout = 0;
                DA.SelectCommand = Comm;
                for (int i = 0; i < lstName.Count; i++)
                {
                    Comm.Parameters.Add(lstName[i], lstValue[i]);
                }

                _dbConnection.Connection.Open();
                DA.Fill(ds);

                _Dataset = ds;
                _intReturn = ds.Tables[0].Rows.Count;
                return true;
            }
            catch (Exception ex)
            {
                _ErrMsg = ex.Message;
                return false;
            }
            finally
            {
                if (_dbConnection != null)
                {
                    _dbConnection.Connection.Close();
                }
                Comm.Dispose();
            }
        }

        public virtual bool ExecuteSPData(string astrQuery, List<string> lstName, List<string> lstValue)
        {
            SqlDataAdapter DA = new SqlDataAdapter();
            SqlCommand Comm = new SqlCommand();
            DataSet ds = new DataSet();

            try
            {
                if (_dbConnection is DBConnection_Basic)
                {
                    Comm = _dbConnection.Connection.CreateCommand();
                }

                Comm.CommandText = astrQuery;
                Comm.CommandType = CommandType.StoredProcedure;
                Comm.CommandTimeout = 0;
                DA.SelectCommand = Comm;
                for (int i = 0; i < lstName.Count; i++)
                {
                    Comm.Parameters.Add(lstName[i], lstValue[i]);
                }

                var returnParameter = Comm.Parameters.Add("@ReturnVal", SqlDbType.Int);
                returnParameter.Direction = ParameterDirection.ReturnValue;

                _dbConnection.Connection.Open();
                Comm.ExecuteNonQuery();
                _returnVal = returnParameter.Value.ToString();
                //DA.Fill(ds);

                return true;
            }
            catch (Exception ex)
            {
                _ErrMsg = ex.Message;
                return false;
            }
            finally
            {
                if (_dbConnection != null)
                {
                    _dbConnection.Connection.Close();
                }
                Comm.Dispose();
            }
        }

        public virtual bool ExecuteSPDataDt(DataTable dt, string spName, string spTable)
        {
            SqlDataAdapter DA = new SqlDataAdapter();
            SqlCommand Comm = new SqlCommand();
            DataSet ds = new DataSet();

            try
            {
                if (_dbConnection is DBConnection_Basic)
                {
                    Comm = _dbConnection.Connection.CreateCommand();
                }

                Comm.CommandText = spName;
                Comm.CommandType = CommandType.StoredProcedure;
                Comm.CommandTimeout = 0;
                DA.SelectCommand = Comm;

                var returnParameter = Comm.Parameters.Add("@ReturnVal", SqlDbType.Int);
                returnParameter.Direction = ParameterDirection.ReturnValue;

                _dbConnection.Connection.Open();
                Comm.Parameters.AddWithValue(spTable, dt);
                Comm.ExecuteNonQuery();
                _returnVal = returnParameter.Value.ToString();
                //DA.Fill(ds);

                return true;
            }
            catch (Exception ex)
            {
                _ErrMsg = ex.Message;
                return false;
            }
            finally
            {
                if (_dbConnection != null)
                {
                    _dbConnection.Connection.Close();
                }
                Comm.Dispose();
            }
        }
        //end

        public virtual bool ExecuteSPDataset(string astrQuery)
        {
            SqlDataAdapter DA = new SqlDataAdapter();
            SqlCommand Comm = new SqlCommand();
            DataSet ds = new DataSet();

            try
            {
                if (_dbConnection is DBConnection_Basic)
                {
                    Comm = _dbConnection.Connection.CreateCommand();
                }

                Comm.CommandText = astrQuery;
                Comm.CommandType = CommandType.StoredProcedure;
                Comm.CommandTimeout = 0;
                DA.SelectCommand = Comm;
                _dbConnection.Connection.Open();
                DA.Fill(ds);

                _Dataset = ds;
                _intReturn = ds.Tables[0].Rows.Count;
                return true;
            }
            catch (Exception ex)
            {
                _ErrMsg = ex.Message;
                return false;
            }
            finally
            {
                if (_dbConnection != null)
                {
                    _dbConnection.Connection.Close();
                }
                Comm.Dispose();
            }
        }

        public virtual bool ExecuteSPDatasetIU(string astrQuery, List<string> lstName, List<string> lstValue)
        {
            SqlDataAdapter DA = new SqlDataAdapter();
            SqlCommand Comm = new SqlCommand();
            DataSet ds = new DataSet();

            try
            {
                if (_dbConnection is DBConnection_Basic)
                {
                    Comm = _dbConnection.Connection.CreateCommand();
                }

                Comm.CommandText = astrQuery;
                Comm.CommandType = CommandType.StoredProcedure;
                Comm.CommandTimeout = 0;
                DA.SelectCommand = Comm;
                for (int i = 0; i < lstName.Count; i++)
                {
                    Comm.Parameters.Add(lstName[i], lstValue[i]);
                }

                _dbConnection.Connection.Open();
                Comm.ExecuteNonQuery();
                return true;
            }
            catch (Exception ex)
            {
                _ErrMsg = ex.Message;
                return false;
            }
            finally
            {
                if (_dbConnection != null)
                {
                    _dbConnection.Connection.Close();
                }
                Comm.Dispose();
            }
        }

        public bool ExecuteSPTable(DataTable dt, string spName, string spTable)
        {
            try
            {
                //SqlDataReader _dr;
                //Comm = _dbConnection.Connection.CreateCommand();
                //// 1.  create a command object identifying
                ////     the stored procedure
                //// 2. set the command object so it knows
                ////    to execute a stored procedure
                //Comm.CommandType = CommandType.StoredProcedure;
                //Comm.CommandText = "processStfType";

                //_dbConnection.Connection.Open();

                //// 3. add parameter to command, which
                ////    will be passed to the stored procedure
                //Comm.Parameters.Add(new SqlParameter("@staffTMWIndex", _indexID));
                //Comm.Parameters.Add(new SqlParameter("@staffID", _staffID));
                //Comm.Parameters.Add(new SqlParameter("@userID", _userID));

                DBConnection_Basic _dbConnection = new DBConnection_Basic();
                SqlCommand Comm = new SqlCommand();
                Comm = _dbConnection.Connection.CreateCommand();
                Comm.CommandType = CommandType.StoredProcedure;
                Comm.CommandTimeout = 0;
                Comm.CommandText = spName;
                _dbConnection.Connection.Open();
                Comm.Parameters.AddWithValue(spTable, dt);

                Comm.ExecuteNonQuery();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public bool ExecuteSPTable2Tbl(DataTable dt, DataTable dt2, string spName, string spTable, string spTable2)
        {
            try
            {
                //SqlDataReader _dr;
                //Comm = _dbConnection.Connection.CreateCommand();
                //// 1.  create a command object identifying
                ////     the stored procedure
                //// 2. set the command object so it knows
                ////    to execute a stored procedure
                //Comm.CommandType = CommandType.StoredProcedure;
                //Comm.CommandText = "processStfType";

                //_dbConnection.Connection.Open();

                //// 3. add parameter to command, which
                ////    will be passed to the stored procedure
                //Comm.Parameters.Add(new SqlParameter("@staffTMWIndex", _indexID));
                //Comm.Parameters.Add(new SqlParameter("@staffID", _staffID));
                //Comm.Parameters.Add(new SqlParameter("@userID", _userID));

                DBConnection_Basic _dbConnection = new DBConnection_Basic();
                SqlCommand Comm = new SqlCommand();
                Comm = _dbConnection.Connection.CreateCommand();
                Comm.CommandType = CommandType.StoredProcedure;
                Comm.CommandTimeout = 0;
                Comm.CommandText = spName;
                _dbConnection.Connection.Open();
                Comm.Parameters.AddWithValue(spTable, dt);
                Comm.Parameters.AddWithValue(spTable2, dt2);

                Comm.ExecuteNonQuery();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }


        public virtual bool ExecuteSPNonQuery(string astrQuery, List<string> lstName, List<string> lstValue)
        {
            SqlDataAdapter DA = new SqlDataAdapter();
            SqlCommand Comm = new SqlCommand();
            DataSet ds = new DataSet();

            try
            {
                if (_dbConnection is DBConnection_Basic)
                {
                    Comm = _dbConnection.Connection.CreateCommand();
                }

                Comm.CommandText = astrQuery;
                Comm.CommandType = CommandType.StoredProcedure;
                Comm.CommandTimeout = 0;
                DA.SelectCommand = Comm;
                for (int i = 0; i < lstName.Count; i++)
                {
                    Comm.Parameters.Add(lstName[i], lstValue[i]);
                }

                _dbConnection.Connection.Open();
                _intReturn = Comm.ExecuteNonQuery(); ;
                return true;
            }
            catch (Exception ex)
            {
                _ErrMsg = ex.Message;
                return false;
            }
            finally
            {
                if (_dbConnection != null)
                {
                    _dbConnection.Connection.Close();
                }
                Comm.Dispose();
            }
        }

        public bool ExcuteNonQuery(string astrQuery)
        {
            SqlCommand Comm = new SqlCommand();
            try
            {
                Comm = _dbConnection.Connection.CreateCommand();
                Comm.CommandType = CommandType.Text;
                Comm.CommandTimeout = 0;
                Comm.CommandText = astrQuery;

                _dbConnection.Connection.Open();
                _intReturn = Comm.ExecuteNonQuery();
                return true;
            }
            catch (Exception ex)
            {
                _ErrMsg = ex.Message;
                Logger.Error(astrQuery, ex);
                return false;
            }
            finally
            {
                if (_dbConnection != null)
                {
                    _dbConnection.Connection.Close();
                }
                Comm.Dispose();
            }
        }

        public bool ExcuteNonQuery(SqlConnection aConn, SqlTransaction aTrans, string astrQuery)
        {
            SqlCommand Comm = new SqlCommand();

            try
            {
                Comm = aConn.CreateCommand();
                Comm.Connection = aConn;
                Comm.Transaction = aTrans;

                Comm.CommandType = CommandType.Text;
                Comm.CommandText = astrQuery;
                Comm.CommandTimeout = 0;
                _intReturn = Comm.ExecuteNonQuery();
                return true;
            }
            catch (Exception ex)
            {
                _ErrMsg = ex.Message;
                Logger.Error(astrQuery, ex);
                return false;
            }
        }

        public bool ExecuteScalar(string astrquery)
        {
            SqlCommand Comm = new SqlCommand();

            try
            {
                Comm = _dbConnection.Connection.CreateCommand();
                Comm.CommandType = CommandType.Text;
                Comm.CommandText = astrquery;
                Comm.CommandTimeout = 0;
                _dbConnection.Connection.Open();
                _objReturn = Comm.ExecuteScalar();
                return true;
            }
            catch (Exception ex)
            {
                _ErrMsg = ex.Message;
                return false;
            }
            finally
            {
                if (_dbConnection != null)
                {
                    _dbConnection.Connection.Close();
                }
                Comm.Dispose();
            }
        }

        public bool ExecuteReader(string astrquery)
        {
            SqlCommand Comm = new SqlCommand();

            try
            {
                Comm = _dbConnection.Connection.CreateCommand();
                Comm.CommandType = CommandType.Text;
                Comm.CommandText = astrquery;
                Comm.CommandTimeout = 0;
                _dbConnection.Connection.Open();
                _dr = Comm.ExecuteReader(CommandBehavior.CloseConnection);
                return true;
            }
            catch (Exception ex)
            {
                _ErrMsg = ex.Message;
                return false;
            }
            finally
            {
                if (_dbConnection != null)
                {
                    Comm.Connection.Close();
                }
                Comm.Dispose();
            }
        }

        public bool ExecuteReaderKeepOpen(string astrquery)
        {
            SqlCommand Comm = new SqlCommand();

            try
            {
                Comm = _dbConnection.Connection.CreateCommand();
                Comm.CommandType = CommandType.Text;
                Comm.CommandText = astrquery;
                Comm.CommandTimeout = 0;
                _dbConnection.Connection.Open();
                _dr = Comm.ExecuteReader(CommandBehavior.CloseConnection);
                return true;
            }
            catch (Exception ex)
            {
                _ErrMsg = ex.Message;
                return false;
            }
        }

        public void closeConnection()
        {
            _dbConnection.Connection.Close();
        }
        #endregion        
    }
    //public class Data_igos : Data_basic
    //{
    //    public Data_igos() : base()
    //    {
    //        base._dbConnection = new DBConnection_Basic();
    //    }

    //}
}
