﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;

namespace Banyan.Apps
{
    public class MeetBLL : BaseDAL<Meet>
    {
        private readonly AjaxResult ajaxResult = null;
        private readonly Random rand = new Random();

        public MeetBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        public override object Add(Meet model)
        {
            ClearCache(model);
            return base.Add(model);
        }

        public override bool Update(Meet model)
        {
            ClearCache(model);
            return base.Update(model);
        }

        public override bool Update(Meet model, string fldList)
        {
            ClearCache(model);
            return base.Update(model, fldList);
        }

        public bool ClearCache(Meet model)
        {
            try
            {
                RedisUtil.Remove(string.Format(RedisKey.Meet_model, model.Id));
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取项目缓存
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Meet GetModelByCache(int id)
        {
            string cacheKey = string.Format(RedisKey.Meet_model, id);
            Meet model = RedisUtil.Get<Meet>(cacheKey);
            if (model == null)
            {
                model = base.GetModel(id);
                RedisUtil.Set<Meet>(cacheKey, model, TimeSpan.FromMinutes(10));
            }
            return model;
        }

        public AjaxResult GetMeetListHelper(NameValueCollection paramValues, Member user, bool ignoreStatus = false)
        {
            CheckRepeat();

            // 十分之一概率清理操作
            if(rand.Next(101) < 10)
            {
                CheckPublish();
            }
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            int status = WebHelper.GetValueInt("status", (int)MeetStatus.normal, paramValues);
            string strWhere = $"status={status}";
            if (ignoreStatus)
            {
                strWhere = $"status<>{(int)MeetStatus.delete}";
            }

            #region search
            string Name = WebHelper.GetValue("Name", string.Empty, paramValues);
            if (!(user.Levels == (int)MemberLevels.Administrator || user.Levels == (int)MemberLevels.SuperUser))
            {
                strWhere += $"AND ( CreatorName = '{user.RealName}' OR InternalPTCP like '%{user.RealName}%') ";
            }
            if (!string.IsNullOrWhiteSpace(Name))
            {
                strWhere += $@"AND (Title like '%{Name}%' OR CreatorName like '%{Name}%'  OR Participant like '%{Name}%' OR InternalPTCP like '%{Name}%' OR Summary like '%{Name}%'  OR Manager like '%{Name}%' OR [tt].name like  '%{Name}%' OR Comment like '%{Name}%') ";
            }
            int isRepeat = WebHelper.GetValueInt("IsRepeat", 0, paramValues);
            if (isRepeat == 1)
            {
                strWhere += $"AND IsRepeat=1 ";
            }

            string startDate = WebHelper.GetValue("startdate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(startDate))
            {
                strWhere += $"AND StartTime>='{startDate}' ";
            }

            string endDate = WebHelper.GetValue("enddate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(endDate))
            {
                strWhere += $"AND   dateadd(mi, Duration, StartTime ) <='{endDate}' ";
            }

            string Creator = WebHelper.GetValue("Creator", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(Creator))
            {
                strWhere += $"AND CreatorName like '%{Creator}%' ";
            }


            #endregion
            //$"select * from(select *, row_number() over(order by  StartTime DESC) as row from [Meet] WITH(NOLOCK)  where status<>{(int)ProjectStatus.delete} ";

            // 需要order by  StartTime DESC否则小程序分页拿不到最新会议
            strWhere = $" select [Meet].[id],[title], tt.name, [creator],[creatorname],[manager],[starttime],[duration],[summary],[comment],[isrepeat],[internalptcp],[participant],[isprivate],[pubtime],[lasttime],[repeatlasttime],[status], row_number() over(order by  StartTime DESC) as row from [Meet]  WITH(NOLOCK) left join (SELECT DISTINCT meetId, STUFF((SELECT '；' + (case when sourceType = 1 then '项目：' when sourceType = 5 then '其他项目报告：' when sourceType = 2 then 'DD：' when sourceType=3 then '研究：' else '' end) + name + (case when speakers = '' then '' else '（'+ speakers + '）' end) FROM MeetAttach WHERE meetId = T.meetId order by sourceType,ranking FOR XML PATH('')), 1, 1, '') AS name FROM MeetAttach AS T) as tt on Meet.Id = tt.MeetId where {strWhere}";
            var strAll = $" select * from({strWhere})  a where row between {(pageIndex - 1) * pageSize + 1} and {pageIndex * pageSize}";
            Logger.Info($"Get meets status {status}", user.RealName);
            var ProjectList = GetListBySql(strAll);

            if (ProjectList != null && ProjectList.Count() > 0)
            {
                var roleList = new RoleBLL().GetList(false);
                if (roleList != null && roleList.Count() > 0)
                {
                    foreach (var item in ProjectList)
                    {
                        item.IsOperate = isOperateHelper(user, item);
                        item.StartTimeStr = item.StartTime.ToString("MM-dd HH:mm");
                    }
                }
                ajaxResult.count = GetListBySql(strWhere).Count;
            }
            else
            {
                ajaxResult.count = 0;
            }
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = ProjectList;

            return ajaxResult;
        }
        /// <summary>
        /// 分页查询记录
        /// </summary>
        /// <param name="paramValues">页面请求参数</param>
        /// <returns></returns>
        public AjaxResult GetPageList(NameValueCollection paramValues)
        {
            Member user = new MemberBLL().GetLogOnUser();
            return GetMeetListHelper(paramValues, user);

        }


        /// <summary>
        /// 项目列表
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public AjaxResult GetMeets(NameValueCollection paramValues)
        {
            #region 用户信息
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var userModel = new MemberBLL().GetModelByCache(uid);
            if (userModel == null || userModel.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = userModel == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = userModel == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }
            #endregion
            return GetMeetListHelper(paramValues, userModel, true);

        }


        /// <summary>
        /// 添加或保存分类数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public AjaxResult Save(Meet model, string attach = "", string meetAttach = "")
        {
            AjaxResult ajaxResult = new AjaxResult();
            var user = new MemberBLL().GetLogOnUser(model.Creator);

            if (user != null)
            {
                model.Creator = user.Id;
                model.CreatorName = user.RealName;
                model.RepeatLastTime = model.StartTime;
            }
            if (model.Id > 0)
            {
                Logger.Info($"{user.RealName} update meet {model.Title} {model.StartTime.ToString("yyyy-MM-dd")} {model.Id}");
                ajaxResult.data = Update(model, @"Title, Name, Creator, CreatorName, Manager, StartTime, Duration, Summary, Comment, IsRepeat, InternalPTCP, Participant, RepeatLastTime,IsPrivate, Status");//MeetingDate，LastTime
                ajaxResult.code = (bool)ajaxResult.data ? (int)ResultCode.success : (int)ResultCode.exception;
            }
            else
            {
                string sqlStr = $"Status<>{(int)MeetStatus.delete} AND Title='{model.Title}' AND PubTime='{model.PubTime}' "; //获取用户可以观看的项目
                if (user != null)
                    sqlStr += $"AND Creator={user.Id} ";
                if (GetCount(sqlStr) == 0)
                {
                    Logger.Info($"{user.RealName} save meet {model.Title} {model.StartTime.ToString("yyyy-MM-dd")} {model.Id}");
                    model.Id = Convert.ToInt32(Add(model));
                    if (model.Id > 0)
                    {
                        AddAttach(attach, model.Id); // 附件添加
                        AddMeetAttach(meetAttach, model.Id); // 会议添加
                    }
                    ajaxResult.code = model.Id > 0 ? (int)ResultCode.success : (int)ResultCode.exception;
                }
                else
                {
                    ajaxResult.code = (int)ResultCode.exception;
                    ajaxResult.msg = "该项目已添加，勿重复添加！";
                }
            }
            ajaxResult.data = model.Id;
            return ajaxResult;
        }

        public bool AddAttach(string attach, int sourceId)
        {
            if (string.IsNullOrEmpty(attach))
                return true;

            List<Attachment> list = Utils.DeserializeObjectByJson<List<Attachment>>(attach);
            if (list == null || list.Count <= 0)
            {
                return true;
            }
            AttachmentBLL attachBll = new AttachmentBLL();
            foreach (var item in list)
            {
                item.SourceId = sourceId;
                item.SourceType = (byte)SourceTypeEnum.Meet;
                attachBll.Add(item);
            }
            return true;
        }
        public void CheckPublish()
        {
            DateTime checkPoint = DateTime.Now.AddDays(-6);
            string sqlStr = $"SELECT * FROM [Meet]  where Status={(int)MeetStatus.editing} AND StartTime<'{checkPoint.ToString("yyyy-MM-dd HH:mm:ss")}' ";
            var meets = GetListBySql(sqlStr);
            foreach (Meet m in meets)
            {
                Logger.Info($"publish meet {m.Title} {m.StartTime.ToString("yyyy-MM-dd")} {m.Id}");
                m.Status = (int)MeetStatus.normal;
                Update(m);
            }
        }
        public void CheckRepeat()
        {
            DateTime checkPoint = DateTime.Now.AddDays(-4);
            string sqlStr = $"SELECT * FROM [Meet]  where Status<>{(int)MeetStatus.delete} AND IsRepeat <> 0 AND RepeatLastTime<'{checkPoint.ToString("yyyy-MM-dd HH:mm:ss")}' ";
            var meets = GetListBySql(sqlStr);
            foreach (Meet m in meets)
            {
                try {
                    TimeSpan span = DateTime.Now - m.RepeatLastTime;
                    var diff = 7 - (span.Days % 7) + span.Days;
                    var newDate = m.RepeatLastTime.AddDays(diff);
                    m.RepeatLastTime = newDate;
                    Update(m);

                    Logger.Info($"重复会议模板{m.Title} 原开始时间{m.StartTime} 生成新开始时间{newDate}");
                    m.StartTime = newDate;
                    m.IsRepeat = 0;
                    m.Comment = string.Empty;
                    m.Status = (int)MeetStatus.editing;
                    Add(m);
                } catch(Exception e) {
                    Logger.Error(e.Message, e);
                }
             }
        }
        public bool AddMeetAttach(string attach, int meetId)
        {
            if (string.IsNullOrEmpty(attach))
                return true;

            List<MeetAttach> list = Utils.DeserializeObjectByJson<List<MeetAttach>>(attach);
            if (list == null || list.Count <= 0)
            {
                return true;
            }
            MeetAttachBLL attachBll = new MeetAttachBLL();
            foreach (var item in list)
            {
                item.MeetId = meetId;
                attachBll.Add(item);
            }
            return true;
        }

        /// <summary>
        /// 更新字段
        /// </summary>
        /// <param name="field"></param>
        /// <returns></returns>
        public AjaxResult FieldSet(NameValueCollection paramValues, Member user)
        {
            var id = WebHelper.GetValueInt("id", 0, paramValues);
            var field = WebHelper.GetValue("field", string.Empty, paramValues);

            if (user == null)
            {
                ajaxResult.code = (int)ResultCode.noright;
                ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.noright);
                return ajaxResult;
            }

            if (id <= 0 || string.IsNullOrWhiteSpace(field))
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                return ajaxResult;
            }

            Meet model = new Meet() { Id = id };
            switch (field)
            {
                case "delete":
                    field = "Status";
                    model.Status = (byte)ProjectStatus.delete;
                    Logger.Info("Web, Delete Meet ID:" + id + " user:" + user.Id);
                    break;
                default:
                    Logger.Warn("Meet field set no action for " + field);
                    break;
            }

            var result = false;
            result = !string.IsNullOrEmpty(field) && Update(model, field);
            ajaxResult.code = result ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }


        /// <summary>
        /// 获取项目数据
        /// </summary>
        /// <returns></returns>
        public AjaxResult GetMeet(NameValueCollection paramValues)
        {
            int id = WebHelper.GetValueInt("aid", 0, paramValues);
            int userId = WebHelper.GetValueInt("uid", 0, paramValues);

            if (id <= 0 || userId <= 0)
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                ajaxResult.msg = "参数不合法！";
                return ajaxResult;
            }

            var userModel = new MemberBLL().GetModelByCache(userId);
            if (userModel == null || userModel.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = userModel == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = userModel == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }
            return GetMeetHelper(id, userModel, true);
        }
        private  bool isOperateHelper(Member user, Meet item)
        {
            return user.Levels == (byte)MemberLevels.Administrator || item.CreatorName == user.RealName || user.Levels == (byte)MemberLevels.SuperUser || user.RealName == "韩锐"
                            || item.Manager.Contains(user.RealName);
        }
        public AjaxResult GetMeetHelper(int id, Member user, bool backAll = false)
        {
            Meet MeetModel = null;
            if (id <= 0)
            {
                MeetModel = new Meet();
            }
            else
                MeetModel = GetModelByCache(id);

            if (backAll)
            {
                ajaxResult.code = (int)ResultCode.success;
                MeetModel = MeetModel ?? new Meet();
                MeetModel.StartTimeStr = MeetModel.StartTime.ToString("yyyy-MM-dd HH:mm:ss");
                MeetModel.IsOperate = isOperateHelper(user, MeetModel);
                ajaxResult.data = MeetModel;
                return ajaxResult;
            }
            if (MeetModel == null || MeetModel.Status == (int)MeetStatus.delete)
            {
                ajaxResult.code = (int)ResultCode.notdata;
                return ajaxResult;
            }

            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = new
            {
                MeetModel.Id,
                MeetModel.Creator,
                MeetModel.Manager,
                IsOperate = isOperateHelper(user, MeetModel),
                StartTimeStr = MeetModel.StartTime.ToString("yyyy-MM-dd HH:mm:ss"),
                MeetModel.Summary,
            };

            return ajaxResult;
        }
        public AjaxResult GetMeetDetail(int id, bool backAll = false)
        {
            Member user = new MemberBLL().GetLogOnUser();
            return GetMeetHelper(id, user, backAll);
        }

        public AjaxResult GetMeetDocs(int id, bool fullPath = false)
        {
            ajaxResult.code = (int)ResultCode.success;
            List<Attachment> list = new AttachmentBLL().GetDocList(id, SourceTypeEnum.Meet);
            if (fullPath && list.Count > 0)
            {
                String hostUrl = System.Configuration.ConfigurationManager.AppSettings["FileDomain"].ToString() ?? "";
                foreach (var item in list)
                {
                    if (item.AtSuffix == "BP" || item.AtSuffix == "DD" || item.AtSuffix == "RECORD")
                    {
                        var t = item.AtUrl.Split(',');
                        item.AtUrl = hostUrl + string.Join("," + hostUrl, t);
                    }
                    else
                    {
                        item.AtUrl = hostUrl + item.AtUrl;
                    }
                }
            }
            ajaxResult.data = list;
            return ajaxResult;
        }

        public AjaxResult GetMeetAttaches(int id, bool fullPath = false)
        {
            ajaxResult.code = (int)ResultCode.success;
            List<MeetAttach> list = new MeetAttachBLL().GetDocList(id, SourceTypeEnum.Meet);
            ajaxResult.data = list;
            return ajaxResult;
        }

    }
}
