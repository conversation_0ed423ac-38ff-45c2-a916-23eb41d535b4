﻿using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.BusinessCenterPortal)]
    public class RestrictedTradingList
    {
        [SqlField(IsPrimaryKey = true)]
        public int ID { get; set; }

        [SqlField]
        public string companyName { get; set; } = string.Empty;
        [SqlField]
        public string code { get; set; } = string.Empty;
        [SqlField]
        public string ipoAddress { get; set; } = string.Empty;

    }

}
