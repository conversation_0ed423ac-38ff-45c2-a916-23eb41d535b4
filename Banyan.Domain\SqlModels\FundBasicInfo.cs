﻿using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.BusinessCenterPortal)]
    public class FundBasicInfo
    {
        [SqlField(IsPrimaryKey = true)]
        public string fundID { get; set; } = string.Empty;
        [SqlField]
        public string fundName { get; set; } = string.Empty;
        [SqlField]
        public string fundFamillyName { get; set; } = string.Empty;
        [SqlField]
        public string fundFullNameEng { get; set; } = string.Empty;
        [SqlField]
        public string fundTypeID { get; set; } = string.Empty;

    }
}

