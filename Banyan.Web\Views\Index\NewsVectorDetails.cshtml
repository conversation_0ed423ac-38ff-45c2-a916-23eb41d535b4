﻿@using Banyan.Domain
@using Banyan.Apps
@using Newtonsoft.Json
@using System.Linq
@{
    ViewBag.Name = "新闻向量化详情";
    Layout = "/Views/Shared/_Layout.cshtml";
    var news = ViewData["news"] as News;
    var tagAnalysis = ViewData["tagAnalysis"] as NewsTagAnalysis;
}

<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li><a href="/Index/NewsVector">新闻向量化浏览</a></li>
                <li>新闻向量化详情</li>
            </ol>
        </div>
        <div class="block-content">
            @if (news != null)
            {
                <div class="vector-details-container">
                    <!-- Header Section -->
                    <div class="vector-header">
                        <div class="vector-title">@news.Title</div>
                        <div class="vector-meta">
                            <span class="vector-date"><i class="fa fa-calendar"></i> @news.PubTime.ToString("yyyy-MM-dd HH:mm")</span> |
                            <span class="vector-source"><i class="fa fa-globe"></i> @news.Source</span> |
                            <span class="vector-category"><i class="fa fa-tag"></i> @news.Classify</span>

                            @{
                                string statusClass = "";
                                string statusText = "";

                                switch (news.VectorStatus)
                                {
                                    case 0:
                                        statusClass = "status-pending";
                                        statusText = "未向量化";
                                        break;
                                    case 1:
                                        statusClass = "status-success";
                                        statusText = "已向量化";
                                        break;
                                    case 2:
                                        statusClass = "status-failed";
                                        statusText = "向量化失败";
                                        break;
                                }
                            }

                            <span class="vector-status @statusClass"><i class="fa fa-vector-square"></i> @statusText</span>

                            @if (!string.IsNullOrEmpty(news.VectorError))
                            {
                                <span class="vector-error text-danger"><i class="fa fa-exclamation-circle"></i> @news.VectorError</span>
                            }
                        </div>
                    </div>

                    <!-- Compact Tag Display Section -->
                    @if (tagAnalysis != null)
                    {
                        <div class="compact-tags-section">
                            <div class="compact-tags-header">
                                <h4><i class="fa fa-tags"></i> 新闻标签概览</h4>
                                <div class="tags-summary">
                                    <span class="tag-count-badge main-count">
                                        <i class="fa fa-star"></i> 主要: @(tagAnalysis.MainTags != null ? tagAnalysis.MainTags.Count : 0)
                                    </span>
                                    <span class="tag-count-badge secondary-count">
                                        <i class="fa fa-bookmark"></i> 次要: @(tagAnalysis.SecondaryTags != null ? tagAnalysis.SecondaryTags.Count : 0)
                                    </span>
                                    <span class="tag-count-badge semantic-count">
                                        <i class="fa fa-lightbulb"></i> 语义: @(tagAnalysis.SemanticKeywords != null ? tagAnalysis.SemanticKeywords.Count : 0)
                                    </span>
                                </div>
                            </div>

                            <div class="compact-tags-container">
                                <div class="compact-tags-list">
                                    @{
                                        var allTags = new List<dynamic>();

                                        if (tagAnalysis.MainTags != null)
                                        {
                                            foreach (var tag in tagAnalysis.MainTags)
                                            {
                                                allTags.Add(new { Name = tag.Name, Weight = tag.Weight, Category = tag.Category, Type = "main" });
                                            }
                                        }

                                        if (tagAnalysis.SecondaryTags != null)
                                        {
                                            foreach (var tag in tagAnalysis.SecondaryTags)
                                            {
                                                allTags.Add(new { Name = tag.Name, Weight = tag.Weight, Category = tag.Category, Type = "secondary" });
                                            }
                                        }

                                        if (tagAnalysis.SemanticKeywords != null)
                                        {
                                            foreach (var keyword in tagAnalysis.SemanticKeywords)
                                            {
                                                allTags.Add(new { Name = keyword.Name, Weight = keyword.Weight, Category = keyword.Category, Type = "semantic" });
                                            }
                                        }

                                        // Sort by weight descending and take top 12
                                        var topTags = allTags.OrderByDescending(t => t.Weight).Take(12).ToList();
                                    }

                                    @foreach (var tag in topTags)
                                    {
                                        var tagClass = "compact-tag-badge";
                                        var tagIcon = "";

                                        string tagType = tag.Type.ToString();
                                        if (tagType == "main")
                                        {
                                            tagClass += " main-tag";
                                            tagIcon = "<i class=\"fa fa-star\"></i> ";
                                        }
                                        else if (tagType == "secondary")
                                        {
                                            tagClass += " secondary-tag";
                                            tagIcon = "<i class=\"fa fa-bookmark\"></i> ";
                                        }
                                        else if (tagType == "semantic")
                                        {
                                            tagClass += " semantic-tag";
                                            tagIcon = "<i class=\"fa fa-lightbulb\"></i> ";
                                        }

                                        <div class="compact-tag-item" title="@tag.Category - 权重: @((tag.Weight * 100).ToString("F0"))%">
                                            <span class="@tagClass">
                                                @Html.Raw(tagIcon)@tag.Name
                                                <span class="compact-tag-weight">@((tag.Weight * 100).ToString("F0"))%</span>
                                            </span>
                                        </div>
                                    }

                                    @if (allTags.Count > 12)
                                    {
                                        <div class="compact-tag-item">
                                            <span class="compact-tag-badge more-tags-badge" onclick="scrollToDetailedTags()">
                                                <i class="fa fa-ellipsis-h"></i> 更多(@(allTags.Count - 12))
                                            </span>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Tag Analysis Section -->
                    @if (tagAnalysis != null)
                    {
                        <div class="row" id="detailed-tags-section">
                            <!-- Main Tags -->
                            <div class="col-md-4">
                                <h4><i class="fa fa-tags"></i> 主要标签</h4>
                                <div class="tag-list">
                                    @if (tagAnalysis.MainTags != null && tagAnalysis.MainTags.Count > 0)
                                    {
                                        foreach (var tag in tagAnalysis.MainTags)
                                        {
                                            <div class="tag-item">
                                                <div class="tag-name">
                                                    @tag.Name
                                                    <span class="tag-category <EMAIL>">@tag.Category</span>
                                                </div>
                                                <div class="tag-weight">@((tag.Weight * 100).ToString("F0"))%</div>
                                                <div class="progress">
                                                    <div class="progress-bar progress-bar-primary" role="progressbar"
                                                         style="width: @((tag.Weight * 100).ToString("F0"))%;">
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <div class="alert alert-info">没有主要标签</div>
                                    }
                                </div>
                            </div>

                            <!-- Secondary Tags -->
                            <div class="col-md-4">
                                <h4><i class="fa fa-tags"></i> 次要标签</h4>
                                <div class="tag-list">
                                    @if (tagAnalysis.SecondaryTags != null && tagAnalysis.SecondaryTags.Count > 0)
                                    {
                                        foreach (var tag in tagAnalysis.SecondaryTags)
                                        {
                                            <div class="tag-item">
                                                <div class="tag-name">
                                                    @tag.Name
                                                    <span class="tag-category <EMAIL>">@tag.Category</span>
                                                </div>
                                                <div class="tag-weight">@((tag.Weight * 100).ToString("F0"))%</div>
                                                <div class="progress">
                                                    <div class="progress-bar progress-bar-info" role="progressbar"
                                                         style="width: @((tag.Weight * 100).ToString("F0"))%;">
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <div class="alert alert-info">没有次要标签</div>
                                    }
                                </div>
                            </div>

                            <!-- Semantic Keywords -->
                            <div class="col-md-4">
                                <h4><i class="fa fa-key"></i> 语义关键词</h4>
                                <div class="tag-list">
                                    @if (tagAnalysis.SemanticKeywords != null && tagAnalysis.SemanticKeywords.Count > 0)
                                    {
                                        foreach (var keyword in tagAnalysis.SemanticKeywords)
                                        {
                                            <div class="tag-item">
                                                <div class="tag-name">
                                                    @keyword.Name
                                                    <span class="tag-category <EMAIL>">@keyword.Category</span>
                                                </div>
                                                <div class="tag-weight">@((keyword.Weight * 100).ToString("F0"))%</div>
                                                <div class="progress">
                                                    <div class="progress-bar progress-bar-warning" role="progressbar"
                                                         style="width: @((keyword.Weight * 100).ToString("F0"))%;">
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <div class="alert alert-info">没有语义关键词</div>
                                    }
                                </div>
                            </div>
                        </div>

                        <!-- Vector Visualization -->
                        <div class="vector-visualization">
                            <h4><i class="fa fa-chart-bar"></i> 标签关系可视化</h4>
                            <div id="tag-relationship-chart" class="chart-container"></div>
                        </div>

                        <!-- Vector Metrics -->
                        <div class="vector-metrics">
                            <h4><i class="fa fa-chart-line"></i> 向量质量指标</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="metric-item">
                                        <span class="metric-label">向量维度：</span>
                                        <span class="metric-value">1024</span>
                                    </div>
                                    <div class="metric-item">
                                        <span class="metric-label">向量更新时间：</span>
                                        <span class="metric-value">@(news.VectorUpdateTime.ToString("yyyy-MM-dd HH:mm:ss"))</span>
                                    </div>
                                    <div class="metric-item">
                                        <span class="metric-label">标签总数：</span>
                                        <span class="metric-value">
                                            @((tagAnalysis.MainTags != null ? tagAnalysis.MainTags.Count : 0) + (tagAnalysis.SecondaryTags != null ? tagAnalysis.SecondaryTags.Count : 0) + (tagAnalysis.SemanticKeywords != null ? tagAnalysis.SemanticKeywords.Count : 0))
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="metric-item">
                                        <span class="metric-label">主要标签权重总和：</span>
                                        <span class="metric-value">
                                            @(tagAnalysis.MainTags != null ? (tagAnalysis.MainTags.Sum(t => t.Weight) * 100).ToString("F0") + "%" : "0%")
                                        </span>
                                    </div>
                                    <div class="metric-item">
                                        <span class="metric-label">次要标签权重总和：</span>
                                        <span class="metric-value">
                                            @(tagAnalysis.SecondaryTags != null ? (tagAnalysis.SecondaryTags.Sum(t => t.Weight) * 100).ToString("F0") + "%" : "0%")
                                        </span>
                                    </div>
                                    <div class="metric-item">
                                        <span class="metric-label">语义关键词权重总和：</span>
                                        <span class="metric-value">
                                            @(tagAnalysis.SemanticKeywords != null ? (tagAnalysis.SemanticKeywords.Sum(t => t.Weight) * 100).ToString("F0") + "%" : "0%")
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-warning">
                            <i class="fa fa-exclamation-triangle"></i> 该新闻尚未完成向量化处理或向量化失败，无法显示标签分析结果。
                        </div>
                    }

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <button class="btn btn-primary" id="vectorize-btn" onclick="vectorizeNews(@news.Id)">
                            <i class="fa fa-magic"></i>
                            @(news.VectorStatus == 1 ? "重新向量化" : (news.VectorStatus == 2 ? "重试向量化" : "向量化"))
                        </button>
                        <a href="/Index/News?id=@news.Id" class="btn btn-info">
                            <i class="fa fa-eye"></i> 查看新闻内容
                        </a>
                        <a href="/Index/NewsVector" class="btn btn-default">
                            <i class="fa fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div> <!-- End of vector-details-container -->
            }
            else
            {
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-circle"></i> 未找到指定的新闻
                </div>
            }
        </div> <!-- End of block-content -->
    </div> <!-- End of block -->
</div> <!-- End of col-md-12 -->

@section styles {
    <style>
        /* Compact Tags Section Styles */
        .compact-tags-section {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 30px;
            overflow: hidden;
            border: 1px solid #f0f0f0;
        }

        .compact-tags-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

            .compact-tags-header h4 {
                margin: 0;
                font-weight: 600;
                font-size: 1.3rem;
            }

        .tags-summary {
            display: flex;
            gap: 15px;
        }

        .tag-count-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .compact-tags-container {
            background-color: #f8f9fa;
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }

        .compact-tags-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .compact-tag-item {
            margin-bottom: 5px;
        }

        .compact-tag-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 14px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid transparent;
        }

            .compact-tag-badge:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            .compact-tag-badge.main-tag {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: #fff;
                border-color: #667eea;
            }

            .compact-tag-badge.secondary-tag {
                background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                color: #fff;
                border-color: #6c757d;
            }

            .compact-tag-badge.semantic-tag {
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                color: #fff;
                border-color: #28a745;
            }

        .more-tags-badge {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #fff;
            border-color: #ffc107;
        }

            .more-tags-badge:hover {
                background: linear-gradient(135deg, #e0a800 0%, #e8690b 100%);
            }

        .compact-tag-weight {
            font-size: 0.8rem;
            background-color: rgba(0, 0, 0, 0.2);
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: 6px;
        }

        .compact-tag-badge i {
            font-size: 0.8rem;
        }

        /* Vector Details Container Styles */
        .vector-details-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .vector-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .vector-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 15px;
            line-height: 1.3;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .vector-meta {
            font-size: 1rem;
            opacity: 0.9;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
        }

            .vector-meta span {
                display: flex;
                align-items: center;
                gap: 5px;
            }

        .status-pending {
            background-color: #ffc107;
            color: #212529;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.85rem;
        }

        .status-success {
            background-color: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.85rem;
        }

        .status-failed {
            background-color: #dc3545;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.85rem;
        }

        /* Tag Analysis Section Styles */
        .tag-list {
            max-height: 400px;
            overflow-y: auto;
            padding-right: 10px;
        }

        .tag-item {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

            .tag-item:hover {
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                border-color: #667eea;
            }

        .tag-name {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .tag-category {
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .category-技术 {
            background-color: #28a745;
            color: #fff;
        }

        .category-投资 {
            background-color: #007bff;
            color: #fff;
        }

        .category-行业 {
            background-color: #6f42c1;
            color: #fff;
        }

        .category-其他 {
            background-color: #6c757d;
            color: #fff;
        }

        .tag-weight {
            text-align: right;
            font-size: 1rem;
            color: #667eea;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .progress {
            height: 8px;
            border-radius: 10px;
            background-color: #f8f9fa;
            overflow: hidden;
        }

        .progress-bar {
            border-radius: 10px;
            transition: width 0.6s ease;
        }

        .progress-bar-primary {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        .progress-bar-info {
            background: linear-gradient(90deg, #17a2b8 0%, #138496 100%);
        }

        .progress-bar-warning {
            background: linear-gradient(90deg, #ffc107 0%, #e0a800 100%);
        }

        /* Vector Visualization Styles */
        .vector-visualization {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 25px;
            margin-bottom: 30px;
        }

            .vector-visualization h4 {
                color: #495057;
                font-weight: 600;
                margin-bottom: 20px;
            }

        .chart-container {
            width: 100%;
            height: 400px;
            min-height: 400px;
            border-radius: 8px;
            overflow: hidden;
            background-color: #fff;
            border: 1px solid #e9ecef;
        }

        /* Vector Metrics Styles */
        .vector-metrics {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 25px;
            margin-bottom: 30px;
        }

            .vector-metrics h4 {
                color: #495057;
                font-weight: 600;
                margin-bottom: 20px;
            }

        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

            .metric-item:last-child {
                border-bottom: none;
            }

        .metric-label {
            font-weight: 500;
            color: #6c757d;
        }

        .metric-value {
            font-weight: 600;
            color: #495057;
        }

        /* Action Buttons Styles */
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }

            .action-buttons .btn {
                border-radius: 25px;
                padding: 12px 25px;
                font-weight: 500;
                transition: all 0.3s ease;
            }

                .action-buttons .btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }

        /* Responsive Design */
        @@media (max-width: 768px) {
            .compact-tags-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .tags-summary {
                justify-content: center;
                flex-wrap: wrap;
            }

            .compact-tags-list {
                justify-content: center;
            }

            .vector-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

                .action-buttons .btn {
                    width: 100%;
                    max-width: 300px;
                }
        }
    </style>
}

@section scripts {
    <script src="~/Content/js/plugins/echarts.min.js"></script>
    <script type="text/javascript">
        $(function () {
            // Check if ECharts is loaded
            if (typeof echarts === 'undefined') {
                console.error('ECharts library is not loaded');
                return;
            }

            // Initialize tag relationship chart with delay to ensure DOM is ready
            setTimeout(function() {
                var chartElement = document.getElementById('tag-relationship-chart');
                if (chartElement) {
                    console.log('Chart element found, initializing chart...');
                    console.log('Chart element dimensions:', chartElement.offsetWidth, 'x', chartElement.offsetHeight);
                    initTagRelationshipChart();
                } else {
                    console.error('Chart element not found');
                }
            }, 100);
        });

        // Prepare chart data
        var chartData = null;
        @if (news != null && tagAnalysis != null)
        {
            <text>
            chartData = {
                newsTitle: @Html.Raw(JsonConvert.SerializeObject(news.Title)),
                mainTags: [
                    @if (tagAnalysis.MainTags != null && tagAnalysis.MainTags.Count > 0)
                    {
                        for (int i = 0; i < tagAnalysis.MainTags.Count; i++)
                        {
                            var tag = tagAnalysis.MainTags[i];
                            @:{ name: @Html.Raw(JsonConvert.SerializeObject(tag.Name)), weight: @tag.Weight, category: @Html.Raw(JsonConvert.SerializeObject(tag.Category)) }@Html.Raw(i < tagAnalysis.MainTags.Count - 1 ? "," : "")
                        }
                    }
                ],
                secondaryTags: [
                    @if (tagAnalysis.SecondaryTags != null && tagAnalysis.SecondaryTags.Count > 0)
                    {
                        for (int i = 0; i < tagAnalysis.SecondaryTags.Count; i++)
                        {
                            var tag = tagAnalysis.SecondaryTags[i];
                            @:{ name: @Html.Raw(JsonConvert.SerializeObject(tag.Name)), weight: @tag.Weight, category: @Html.Raw(JsonConvert.SerializeObject(tag.Category)) }@Html.Raw(i < tagAnalysis.SecondaryTags.Count - 1 ? "," : "")
                        }
                    }
                ],
                semanticKeywords: [
                    @if (tagAnalysis.SemanticKeywords != null && tagAnalysis.SemanticKeywords.Count > 0)
                    {
                        for (int i = 0; i < tagAnalysis.SemanticKeywords.Count; i++)
                        {
                            var keyword = tagAnalysis.SemanticKeywords[i];
                            @:{ name: @Html.Raw(JsonConvert.SerializeObject(keyword.Name)), weight: @keyword.Weight, category: @Html.Raw(JsonConvert.SerializeObject(keyword.Category)) }@Html.Raw(i < tagAnalysis.SemanticKeywords.Count - 1 ? "," : "")
                        }
                    }
                ]
            };
            </text>
        }

        // Function to initialize tag relationship chart
        function initTagRelationshipChart() {
            console.log('Initializing tag relationship chart...');
            console.log('Chart data:', chartData);

            var chartElement = document.getElementById('tag-relationship-chart');
            if (!chartElement) {
                console.error('Chart element not found');
                return;
            }

            console.log('Chart element dimensions before init:', chartElement.offsetWidth, 'x', chartElement.offsetHeight);

            // Ensure the container has proper dimensions
            if (chartElement.offsetWidth === 0 || chartElement.offsetHeight === 0) {
                console.warn('Chart container has zero dimensions, setting explicit size');
                chartElement.style.width = '100%';
                chartElement.style.height = '400px';
            }

            var chart = echarts.init(chartElement);
            console.log('ECharts instance created:', chart);

            // Check if chart data is available
            if (!chartData) {
                console.log('No chart data available, showing placeholder');
                var placeholderOption = {
                    title: {
                        text: '没有可用的标签数据',
                        left: 'center',
                        top: 'middle',
                        textStyle: {
                            fontSize: 16,
                            color: '#666'
                        }
                    },
                    backgroundColor: '#f8f9fa'
                };
                chart.setOption(placeholderOption);
                chart.resize();
                return;
            }

            // Prepare data for chart
            var categories = [
                { name: '主要标签' },
                { name: '次要标签' },
                { name: '语义关键词' },
                { name: '新闻' }
            ];

            var nodes = [];
            var links = [];
            var nodeIndex = 0;

            // Add center node (news title)
            nodes.push({
                id: nodeIndex,
                name: chartData.newsTitle,
                symbolSize: 50,
                category: 3,
                value: 1,
                itemStyle: {
                    color: '#1976d2'
                }
            });
            var centerNodeIndex = nodeIndex;
            nodeIndex++;

            // Add main tags
            if (chartData.mainTags && chartData.mainTags.length > 0) {
                chartData.mainTags.forEach(function(tag) {
                    nodes.push({
                        id: nodeIndex,
                        name: tag.name,
                        symbolSize: tag.weight * 40 + 10,
                        category: 0,
                        value: tag.weight
                    });
                    links.push({
                        source: nodeIndex,
                        target: centerNodeIndex,
                        value: tag.weight
                    });
                    nodeIndex++;
                });
            }

            // Add secondary tags
            if (chartData.secondaryTags && chartData.secondaryTags.length > 0) {
                chartData.secondaryTags.forEach(function(tag) {
                    nodes.push({
                        id: nodeIndex,
                        name: tag.name,
                        symbolSize: tag.weight * 30 + 8,
                        category: 1,
                        value: tag.weight
                    });
                    links.push({
                        source: nodeIndex,
                        target: centerNodeIndex,
                        value: tag.weight
                    });
                    nodeIndex++;
                });
            }

            // Add semantic keywords
            if (chartData.semanticKeywords && chartData.semanticKeywords.length > 0) {
                chartData.semanticKeywords.forEach(function(keyword) {
                    nodes.push({
                        id: nodeIndex,
                        name: keyword.name,
                        symbolSize: keyword.weight * 20 + 6,
                        category: 2,
                        value: keyword.weight
                    });
                    links.push({
                        source: nodeIndex,
                        target: centerNodeIndex,
                        value: keyword.weight
                    });
                    nodeIndex++;
                });
            }

            console.log('Nodes:', nodes);
            console.log('Links:', links);

            // Chart options
            var option = {
                backgroundColor: '#fff',
                title: {
                    text: '新闻标签关系图',
                    subtext: '标签大小表示权重',
                    top: 20,
                    left: 'center',
                    textStyle: {
                        fontSize: 18,
                        color: '#333'
                    },
                    subtextStyle: {
                        fontSize: 12,
                        color: '#666'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        if (params.dataType === 'node') {
                            return params.data.name + '<br>权重: ' + (params.data.value * 100).toFixed(0) + '%';
                        }
                        return '';
                    }
                },
                legend: {
                    data: categories.map(function(a) { return a.name; }),
                    bottom: 20,
                    left: 'center'
                },
                series: [{
                    type: 'graph',
                    layout: 'force',
                    data: nodes,
                    links: links,
                    categories: categories,
                    roam: true,
                    focusNodeAdjacency: true,
                    itemStyle: {
                        borderColor: '#fff',
                        borderWidth: 1,
                        shadowBlur: 10,
                        shadowColor: 'rgba(0, 0, 0, 0.3)'
                    },
                    label: {
                        show: true,
                        position: 'right',
                        formatter: '{b}'
                    },
                    edgeSymbol: ['circle', 'arrow'],
                    edgeSymbolSize: [4, 10],
                    force: {
                        repulsion: 1000,
                        gravity: 0.2,
                        edgeLength: [50, 200],
                        layoutAnimation: true
                    },
                    lineStyle: {
                        color: 'source',
                        curveness: 0.3,
                        width: function(params) {
                            return Math.max(1, params.data.value * 5);
                        }
                    }
                }]
            };

            // Set chart options
            try {
                chart.setOption(option);
                console.log('Chart options set successfully');

                // Force resize after setting options
                setTimeout(function() {
                    chart.resize();
                    console.log('Chart resized');

                    //// Test if chart is actually rendered
                    //setTimeout(function() {
                    //    var chartDom = chart.getDom();
                    //    var svgElements = chartDom.querySelectorAll('svg');
                    //    console.log('SVG elements found:', svgElements.length);
                    //    if (svgElements.length === 0) {
                    //        console.warn('No SVG elements found, trying simple test chart');
                    //        // Try a simple pie chart as fallback
                    //        var testOption = {
                    //            title: {
                    //                text: '测试图表',
                    //                left: 'center'
                    //            },
                    //            series: [{
                    //                type: 'pie',
                    //                data: [
                    //                    {value: 1048, name: '搜索引擎'},
                    //                    {value: 735, name: '直接访问'},
                    //                    {value: 580, name: '邮件营销'}
                    //                ],
                    //                radius: '50%'
                    //            }]
                    //        };
                    //        chart.setOption(testOption);
                    //    }
                    //}, 500);
                }, 100);
            } catch (error) {
                console.error('Error setting chart options:', error);
            }

            // Handle window resize
            window.addEventListener('resize', function() {
                chart.resize();
            });
        }

        // Function to scroll to detailed tags section
        function scrollToDetailedTags() {
            $('html, body').animate({
                scrollTop: $('#detailed-tags-section').offset().top - 20
            }, 800);
        }

        // Function to vectorize news
        function vectorizeNews(newsId) {
            // Show loading indicator on the button
            var button = $('#vectorize-btn');
            var originalText = button.html();
            button.html('<i class="fa fa-spinner fa-spin"></i> 处理中...');
            button.prop('disabled', true);

            // Make AJAX request to trigger vectorization
            $.ajax({
                url: '/Index/VectorizeNews',
                type: 'POST',
                data: { newsId: newsId },
                success: function(response) {
                    if (response.code === 0) {
                        // Show success message
                        layer.msg('向量化处理已成功触发', {icon: 1});

                        // Reload the page after a delay
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        // Show error message
                        layer.msg('向量化处理失败: ' + response.msg, {icon: 2});

                        // Restore button
                        button.html(originalText);
                        button.prop('disabled', false);
                    }
                },
                error: function() {
                    // Show error message
                    layer.msg('向量化处理请求失败，请稍后再试', {icon: 2});

                    // Restore button
                    button.html(originalText);
                    button.prop('disabled', false);
                }
            });
        }
    </script>
}