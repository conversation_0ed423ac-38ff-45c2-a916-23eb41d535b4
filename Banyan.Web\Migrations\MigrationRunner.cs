using System;
using System.IO;
using System.Data.SqlClient;
using System.Configuration;
using System.Reflection;
using System.Web;
using System.Web.Hosting;
using Banyan.Code;
using System.Text;

namespace Banyan.Web.Migrations
{
    /// <summary>
    /// Handles database migrations for the application
    /// </summary>
    public class MigrationRunner
    {
        private static readonly log4net.ILog log = log4net.LogManager.GetLogger(typeof(MigrationRunner));

        /// <summary>
        /// Runs all migration scripts in the Migrations folder
        /// </summary>
        public static void RunMigrations()
        {
            try
            {
                log.Info("Starting database migrations...");
                
                // Get the path to the migrations folder
                string migrationsPath = HostingEnvironment.MapPath("~/Migrations");
                if (migrationsPath == null)
                {
                    log.Error("Could not find Migrations folder");
                    return;
                }

                // Get all SQL files in the migrations folder
                string[] sqlFiles = Directory.GetFiles(migrationsPath, "*.sql");
                
                // Run each migration script
                foreach (string sqlFile in sqlFiles)
                {
                    RunMigrationScript(sqlFile);
                }
                
                log.Info("Database migrations completed successfully");
            }
            catch (Exception ex)
            {
                log.Error("Error running migrations", ex);
                throw;
            }
        }

        /// <summary>
        /// Runs a single migration script
        /// </summary>
        /// <param name="scriptPath">Path to the SQL script file</param>
        private static void RunMigrationScript(string scriptPath)
        {
            try
            {
                string scriptName = Path.GetFileName(scriptPath);
                log.Info($"Running migration script: {scriptName}");
                
                // Read the script content
                string script = File.ReadAllText(scriptPath);
                
                // Execute the script
                using (SqlConnection connection = new SqlConnection(DBConnection.GetConnectionString(DBConnection.dbEnum.QLWL)))
                {
                    connection.Open();
                    
                    // Split the script by GO statements if present
                    string[] batches = script.Split(new[] { "GO", "go" }, StringSplitOptions.RemoveEmptyEntries);
                    
                    foreach (string batch in batches)
                    {
                        if (!string.IsNullOrWhiteSpace(batch))
                        {
                            using (SqlCommand command = new SqlCommand(batch, connection))
                            {
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                }
                
                log.Info($"Migration script {scriptName} executed successfully");
            }
            catch (Exception ex)
            {
                log.Error($"Error running migration script: {scriptPath}", ex);
                throw;
            }
        }
    }
}