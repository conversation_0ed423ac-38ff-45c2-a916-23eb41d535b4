﻿
var notifyidx = null;
// info warning success danger
function notify(message, ntype, nurl) {
    ntype = ntype || 'warning';

    var nicon = 'fa fa-warning';
    if (ntype == 'info')
        nicon = 'fa fa-info-circle';
    else if (ntype == 'success')
        nicon = 'fa fa-check';
    else if (ntype == 'danger')
        nicon = 'fa fa-times';

    if (notifyidx) {
        notifyidx.close();
    }
    notifyidx = $.notify({
        icon: nicon,
        message: message,
        url: nurl || ''
    },
        {
            element: 'body',
            type: ntype,
            allow_dismiss: true,
            newest_on_top: true,
            showProgressbar: false,
            placement: {
                from: 'top',
                align: 'center'
            },
            offset: 20,
            spacing: 10,
            z_index: 1033,
            delay: 1000,
            timer: 1000,
            animate: {
                enter: 'animated fadeIn',
                exit: 'animated fadeOutDown'
            }
        });
}

function isphone(val) {
    if (val && /^1[3-9]\d{9}$/.test(val))
        return true;
    return false;
}

function ismail(val) {
    return val && /^[\w\+\-]+(\.[\w\+\-]+)*@[a-z\d\-]+(\.[a-z\d\-]+)*\.([a-z]{2,4})$/i.test(val);
}
function iscode(val, len) {
    if (val && /^\d{4}$/.test(val))
        return true;
    return false;
}

function iscodelong(val, len) {
    if (val && /^[0-9a-zA-Z]{6,9}$/.test(val))
        return true;
    return false;
}

function ischars(val) {
    if (val && /^\w{6,20}$/.test(val))
        return true;
    return false;
}