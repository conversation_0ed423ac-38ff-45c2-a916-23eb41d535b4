﻿using log4net;
using System;


namespace Banyan.Code
{
    public class Logger
    {
        private static readonly ILog log = LogManager.GetLogger("logger");
        
        public static void Debug(string info, string user = "")
        {
            if (!user.IsEmpty())
            {
                log4net.LogicalThreadContext.Properties["username"] = user;
            }
            try
            {
                log4net.LogicalThreadContext.Properties["ip"] = Utility.WebHelper.GetIP();
            }
            catch (Exception e)
            {
            }
            log.Debug(info);
        }
        
        public static void Debug(string info, Exception ex, string user = "")
        {
            if (!user.IsEmpty())
            {
                log4net.LogicalThreadContext.Properties["username"] = user;
            }
            try
            {
                log4net.LogicalThreadContext.Properties["ip"] = Utility.WebHelper.GetIP();
            }
            catch (Exception e)
            {
            }
            log.Debug(info, ex);
        }
        
        public static void Fatal(string info, string user = "")
        {
            if (!user.IsEmpty())
            {
                log4net.LogicalThreadContext.Properties["username"] = user;
            }
            try
            {
                log4net.LogicalThreadContext.Properties["ip"] = Utility.WebHelper.GetIP();
            }
            catch (Exception e)
            {
            }
            log.Fatal(info);
        }
        
        public static void Fatal(string info, Exception ex, string user = "")
        {
            if (!user.IsEmpty())
            {
                log4net.LogicalThreadContext.Properties["username"] = user;
            }
            try
            {
                log4net.LogicalThreadContext.Properties["ip"] = Utility.WebHelper.GetIP();
            }
            catch (Exception e)
            {
            }
            log.Fatal(info, ex);
        }

        public static void Info(string info, string user = "")
        {
            if (!user.IsEmpty())
            {
                log4net.LogicalThreadContext.Properties["username"] = user;
            }
            try
            {
                log4net.LogicalThreadContext.Properties["ip"] = Utility.WebHelper.GetIP();
            }
            catch (Exception e)
            {
            }
            log.Info(info);
        }
        public static void Info(string info, Exception ex, string user="")
        {
            if (!user.IsEmpty())
            {
                log4net.LogicalThreadContext.Properties["username"] = user;
            }
            try
            {
                log4net.LogicalThreadContext.Properties["ip"] = Utility.WebHelper.GetIP();
            }
            catch (Exception e)
            {
            }
            log.Info(info, ex);
        }
        public static void syslog(string info, string user, string project)
        {
            log4net.LogicalThreadContext.Properties["username"] = user;
            log4net.LogicalThreadContext.Properties["project"] = project;
            try
            {
                log4net.LogicalThreadContext.Properties["ip"] = Utility.WebHelper.GetIP();
            }
            catch (Exception e)
            {
            }
            log.Info(info);
        }
        public static void Warn(string info)
        {
            try
            {
                log4net.LogicalThreadContext.Properties["ip"] = Utility.WebHelper.GetIP();
            }
            catch (Exception e)
            {
            }
            log.Warn(info);
        }
        public static void Warn(string info, Exception ex)
        {
            try
            {
                log4net.LogicalThreadContext.Properties["ip"] = Utility.WebHelper.GetIP();
            }
            catch (Exception e)
            {
            }
            log.Warn(info, ex);
        }
        public static void Warn(string info, Exception ex, string user = "")
        {
            if (!user.IsEmpty())
            {
                log4net.LogicalThreadContext.Properties["username"] = user;
            }
            try
            {
                log4net.LogicalThreadContext.Properties["ip"] = Utility.WebHelper.GetIP();
            }
            catch (Exception e)
            {
            }
            log.Warn(info, ex);
        }
        public static void Error(Type type, Exception ex)
        {
            try
            {
                log4net.LogicalThreadContext.Properties["ip"] = Utility.WebHelper.GetIP();
            }
            catch (Exception e)
            {
            }
            log.Error(type, ex);
        }
        public static void Error(String message, string user = "")
        {
            if (!user.IsEmpty())
            {
                try
                {
                    log4net.LogicalThreadContext.Properties["ip"] = Utility.WebHelper.GetIP();
                }
                catch (Exception e)
                {
                }
                log4net.LogicalThreadContext.Properties["username"] = user;
            }
            log.Error(message);
        }
        public static void Error(String message, Exception ex, string user = "")
        {
            if (!user.IsEmpty())
            {
                log4net.LogicalThreadContext.Properties["ip"] = Utility.WebHelper.GetIP();
                log4net.LogicalThreadContext.Properties["username"] = user;
            }
            try
            {
                log4net.LogicalThreadContext.Properties["ip"] = Utility.WebHelper.GetIP();
            }
            catch (Exception e)
            {

            }
            log.Error(message, ex);
        }
    }
}