using System;

namespace Banyan.Apps.Configs
{
    /// <summary>
    /// 推荐评分配置
    /// 用于调整推荐系统的评分算法参数
    /// </summary>
    public static class RecommendationScoringConfig
    {
        #region 评分权重配置

        /// <summary>
        /// 向量相似度权重
        /// </summary>
        public const double VECTOR_SIMILARITY_WEIGHT = 0.4;

        /// <summary>
        /// 标签匹配权重
        /// </summary>
        public const double TAG_MATCH_WEIGHT = 0.3;

        /// <summary>
        /// 时效性权重
        /// </summary>
        public const double TIMELINESS_WEIGHT = 0.2;

        /// <summary>
        /// 内容质量权重
        /// </summary>
        public const double QUALITY_WEIGHT = 0.1;

        #endregion

        #region 相似度变换参数

        /// <summary>
        /// S型曲线陡峭度参数
        /// 值越大，中等相似度的区分度越高
        /// </summary>
        public const double SIMILARITY_CURVE_STEEPNESS = 8.0;

        /// <summary>
        /// 相似度分数基础范围最小值
        /// </summary>
        public const double SIMILARITY_SCORE_MIN = 10.0;

        /// <summary>
        /// 相似度分数基础范围最大值
        /// </summary>
        public const double SIMILARITY_SCORE_MAX = 95.0;

        /// <summary>
        /// 相似度分数基础范围大小
        /// </summary>
        public const double SIMILARITY_SCORE_RANGE = 80.0;

        #endregion

        #region 标签匹配评分参数

        /// <summary>
        /// 无标签时的基础分数
        /// </summary>
        public const double NO_TAGS_BASE_SCORE = 20.0;

        /// <summary>
        /// 用户无兴趣标签时的基础分数
        /// </summary>
        public const double NO_USER_TAGS_BASE_SCORE = 30.0;

        /// <summary>
        /// 标签匹配奖励分数（每个匹配标签）
        /// </summary>
        public const double TAG_MATCH_BONUS_PER_TAG = 5.0;

        /// <summary>
        /// 标签匹配奖励分数上限
        /// </summary>
        public const double TAG_MATCH_BONUS_MAX = 20.0;

        /// <summary>
        /// 标签匹配分数最小值
        /// </summary>
        public const double TAG_MATCH_SCORE_MIN = 15.0;

        /// <summary>
        /// 标签匹配分数最大值
        /// </summary>
        public const double TAG_MATCH_SCORE_MAX = 90.0;

        #endregion

        #region 时效性评分参数

        /// <summary>
        /// 1天内新闻的时效性分数
        /// </summary>
        public const double TIMELINESS_1_DAY = 95.0;

        /// <summary>
        /// 3天内新闻的时效性分数
        /// </summary>
        public const double TIMELINESS_3_DAYS = 85.0;

        /// <summary>
        /// 1周内新闻的时效性分数
        /// </summary>
        public const double TIMELINESS_1_WEEK = 75.0;

        /// <summary>
        /// 2周内新闻的时效性分数
        /// </summary>
        public const double TIMELINESS_2_WEEKS = 60.0;

        /// <summary>
        /// 1月内新闻的时效性分数
        /// </summary>
        public const double TIMELINESS_1_MONTH = 45.0;

        /// <summary>
        /// 3月内新闻的时效性分数
        /// </summary>
        public const double TIMELINESS_3_MONTHS = 30.0;

        /// <summary>
        /// 超过3个月新闻的时效性分数
        /// </summary>
        public const double TIMELINESS_OLD = 20.0;

        /// <summary>
        /// 无发布时间时的默认时效性分数
        /// </summary>
        public const double TIMELINESS_NO_DATE = 40.0;

        #endregion

        #region 内容质量评分参数

        /// <summary>
        /// 内容质量基础分数
        /// </summary>
        public const double QUALITY_BASE_SCORE = 50.0;

        /// <summary>
        /// 标题长度合适时的加分
        /// </summary>
        public const double QUALITY_TITLE_LENGTH_BONUS = 10.0;

        /// <summary>
        /// 标题长度理想时的额外加分
        /// </summary>
        public const double QUALITY_TITLE_IDEAL_BONUS = 5.0;

        /// <summary>
        /// 内容长度达标时的加分（200字符）
        /// </summary>
        public const double QUALITY_CONTENT_LENGTH_BONUS_1 = 10.0;

        /// <summary>
        /// 内容长度达标时的加分（500字符）
        /// </summary>
        public const double QUALITY_CONTENT_LENGTH_BONUS_2 = 5.0;

        /// <summary>
        /// 内容长度达标时的加分（1000字符）
        /// </summary>
        public const double QUALITY_CONTENT_LENGTH_BONUS_3 = 5.0;

        /// <summary>
        /// 可靠来源的加分
        /// </summary>
        public const double QUALITY_RELIABLE_SOURCE_BONUS = 15.0;

        /// <summary>
        /// 有分类信息的加分
        /// </summary>
        public const double QUALITY_CATEGORY_BONUS = 5.0;

        /// <summary>
        /// 内容质量分数最小值
        /// </summary>
        public const double QUALITY_SCORE_MIN = 20.0;

        /// <summary>
        /// 内容质量分数最大值
        /// </summary>
        public const double QUALITY_SCORE_MAX = 90.0;

        #endregion

        #region 可靠新闻来源列表

        /// <summary>
        /// 可靠新闻来源列表
        /// </summary>
        public static readonly string[] RELIABLE_NEWS_SOURCES = new[]
        {
            "新华社", "人民日报", "央视新闻", "财新", "第一财经", "界面新闻",
            "澎湃新闻", "经济观察报", "21世纪经济报道", "证券时报", "上海证券报",
            "中国证券报", "金融时报", "经济日报", "中国经济网", "东方财富网"
        };

        #endregion

        #region 评分调试配置

        /// <summary>
        /// 是否启用评分调试日志
        /// </summary>
        public const bool ENABLE_SCORING_DEBUG = true;

        /// <summary>
        /// 是否启用详细的标签匹配日志
        /// </summary>
        public const bool ENABLE_TAG_MATCH_DEBUG = true;

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取评分权重配置摘要
        /// </summary>
        /// <returns>权重配置字符串</returns>
        public static string GetWeightSummary()
        {
            return $"向量相似度:{VECTOR_SIMILARITY_WEIGHT:P0}, " +
                   $"标签匹配:{TAG_MATCH_WEIGHT:P0}, " +
                   $"时效性:{TIMELINESS_WEIGHT:P0}, " +
                   $"内容质量:{QUALITY_WEIGHT:P0}";
        }

        /// <summary>
        /// 验证权重配置是否合理
        /// </summary>
        /// <returns>是否合理</returns>
        public static bool ValidateWeights()
        {
            var totalWeight = VECTOR_SIMILARITY_WEIGHT + TAG_MATCH_WEIGHT + 
                             TIMELINESS_WEIGHT + QUALITY_WEIGHT;
            
            return Math.Abs(totalWeight - 1.0) < 0.001; // 允许小的浮点误差
        }

        #endregion
    }
}
