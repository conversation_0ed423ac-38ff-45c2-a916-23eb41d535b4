using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Banyan.Code;
using Banyan.Domain;
using BanyanDBConnection = Banyan.Code.DBConnection;

namespace Banyan.Apps
{
    /// <summary>
    /// Provides analytics and reporting functionality for user engagement with recommendations
    /// </summary>
    public class EngagementAnalytics
    {
        private readonly NewsRecommendationsBLL _recommendationBLL;
        private readonly EngagementRecordsBLL _engagementRecordsBLL;
        private readonly NewsBLL _newsBLL;
        private readonly UserProfileBLL _userProfileBLL;

        /// <summary>
        /// Constructor
        /// </summary>
        public EngagementAnalytics()
        {
            _recommendationBLL = new NewsRecommendationsBLL();
            _engagementRecordsBLL = new EngagementRecordsBLL();
            _newsBLL = new NewsBLL();
            _userProfileBLL = new UserProfileBLL();
        }

        /// <summary>
        /// Gets a summary of engagement statistics for a specific period
        /// </summary>
        /// <param name="startDate">Start date for statistics</param>
        /// <param name="endDate">End date for statistics</param>
        /// <returns>Engagement statistics summary</returns>
        public async Task<EngagementStatistics> GetEngagementSummaryAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                Logger.Info($"Getting engagement summary from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
                return await _recommendationBLL.GetEngagementStatisticsAsync(startDate, endDate);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting engagement summary: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets detailed engagement metrics for a specific period
        /// </summary>
        /// <param name="startDate">Start date for metrics</param>
        /// <param name="endDate">End date for metrics</param>
        /// <returns>Detailed engagement metrics</returns>
        public async Task<EngagementDetailedMetrics> GetDetailedEngagementMetricsAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                Logger.Info($"Getting detailed engagement metrics from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");

                var metrics = new EngagementDetailedMetrics
                {
                    StartDate = startDate,
                    EndDate = endDate
                };

                // Get daily engagement counts
                metrics.DailyEngagementCounts = await GetDailyEngagementCountsAsync(startDate, endDate);

                // Get source distribution
                metrics.SourceDistribution = await GetEngagementSourceDistributionAsync(startDate, endDate);

                // Get user engagement distribution
                metrics.UserEngagementDistribution = await GetUserEngagementDistributionAsync(startDate, endDate);

                // Get content type distribution
                metrics.ContentTypeDistribution = await GetContentTypeDistributionAsync(startDate, endDate);

                // Get engagement by hour of day
                metrics.HourlyDistribution = await GetHourlyEngagementDistributionAsync(startDate, endDate);

                // Get engagement by day of week
                metrics.DayOfWeekDistribution = await GetDayOfWeekEngagementDistributionAsync(startDate, endDate);

                Logger.Info($"Successfully retrieved detailed engagement metrics");
                return metrics;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting detailed engagement metrics: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets engagement effectiveness metrics
        /// </summary>
        /// <param name="startDate">Start date for metrics</param>
        /// <param name="endDate">End date for metrics</param>
        /// <returns>Engagement effectiveness metrics</returns>
        public async Task<EngagementEffectivenessMetrics> GetEngagementEffectivenessAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                Logger.Info($"Getting engagement effectiveness metrics from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");

                var metrics = new EngagementEffectivenessMetrics
                {
                    StartDate = startDate,
                    EndDate = endDate
                };

                using (SqlConnection connection = new SqlConnection(BanyanDBConnection.connectionString))
                {
                    await connection.OpenAsync();

                    // Get recommendation click-through rate
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT 
                            COUNT(CASE WHEN IsClicked = 1 THEN 1 ELSE NULL END) AS ClickedCount,
                            COUNT(*) AS TotalCount
                        FROM NewsRecommendations 
                        WHERE GeneratedTime BETWEEN @StartDate AND @EndDate", connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                int clickedCount = reader.GetInt32(0);
                                int totalCount = reader.GetInt32(1);
                                metrics.ClickThroughRate = totalCount > 0 ? (double)clickedCount / totalCount : 0;
                                metrics.TotalRecommendations = totalCount;
                                metrics.ClickedRecommendations = clickedCount;
                            }
                        }
                    }

                    // Get email digest effectiveness
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT 
                            COUNT(*) AS TotalEmailEngagements
                        FROM EngagementRecords 
                        WHERE Source = 'email' AND Timestamp BETWEEN @StartDate AND @EndDate", connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                metrics.EmailEngagements = reader.GetInt32(0);
                            }
                        }
                    }

                    // Get total email digests sent
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT 
                            COUNT(*) AS TotalDigestsSent,
                            SUM(NewsCount) AS TotalNewsIncluded
                        FROM EmailDigestRecords 
                        WHERE SentTime BETWEEN @StartDate AND @EndDate", connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                metrics.EmailDigestsSent = reader.GetInt32(0);
                                metrics.TotalNewsInDigests = reader.GetInt32(1);
                                
                                // Calculate email effectiveness
                                metrics.EmailEffectivenessRate = metrics.TotalNewsInDigests > 0 ? 
                                    (double)metrics.EmailEngagements / metrics.TotalNewsInDigests : 0;
                            }
                        }
                    }

                    // Get user retention metrics
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT 
                            COUNT(DISTINCT UserName) AS UniqueUsers,
                            COUNT(DISTINCT CASE WHEN DATEDIFF(day, MIN(Timestamp), MAX(Timestamp)) >= 7 THEN UserName ELSE NULL END) AS RetainedUsers
                        FROM EngagementRecords 
                        WHERE Timestamp BETWEEN @StartDate AND @EndDate", connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                metrics.UniqueUsers = reader.GetInt32(0);
                                metrics.RetainedUsers = reader.GetInt32(1);
                                metrics.RetentionRate = metrics.UniqueUsers > 0 ? 
                                    (double)metrics.RetainedUsers / metrics.UniqueUsers : 0;
                            }
                        }
                    }
                }

                Logger.Info($"Successfully retrieved engagement effectiveness metrics");
                return metrics;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting engagement effectiveness metrics: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets a list of all engagement records for a specific period
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="limit">Maximum number of records to return</param>
        /// <returns>List of engagement records</returns>
        public async Task<List<EngagementRecords>> GetAllEngagementRecordsAsync(DateTime startDate, DateTime endDate, int limit = 1000)
        {
            try
            {
                Logger.Info($"Getting all engagement records from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}, limit {limit}");

                string where = $"Timestamp BETWEEN '{startDate:yyyy-MM-dd}' AND '{endDate:yyyy-MM-dd 23:59:59}'";
                string orderBy = "Timestamp DESC";

                var records = await Task.Run(() => _engagementRecordsBLL.GetList(where, limit, 1, "*", orderBy));

                Logger.Info($"Retrieved {records.Count} engagement records");
                return records;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting all engagement records: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets engagement records for a specific user
        /// </summary>
        /// <param name="userName">User name</param>
        /// <param name="limit">Maximum number of records to return</param>
        /// <param name="startDate">Optional start date filter</param>
        /// <param name="endDate">Optional end date filter</param>
        /// <returns>List of engagement records</returns>
        public async Task<List<EngagementRecords>> GetUserEngagementHistoryAsync(string userName, int limit = 100, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                Logger.Info($"Getting engagement history for user {userName}, limit {limit}");
                return await _engagementRecordsBLL.GetUserEngagementHistoryAsync(userName, limit, startDate, endDate);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting engagement history for user {userName}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets engagement records for a specific news article
        /// </summary>
        /// <param name="newsId">News ID</param>
        /// <param name="limit">Maximum number of records to return</param>
        /// <returns>List of engagement records</returns>
        public async Task<List<EngagementRecords>> GetNewsEngagementHistoryAsync(int newsId, int limit = 100)
        {
            try
            {
                Logger.Info($"Getting engagement history for news {newsId}, limit {limit}");

                string where = $"NewsId = {newsId}";
                string orderBy = "Timestamp DESC";

                var records = await Task.Run(() => _engagementRecordsBLL.GetList(where, limit, 1, "*", orderBy));

                Logger.Info($"Retrieved {records.Count} engagement records for news {newsId}");
                return records;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting engagement history for news {newsId}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Exports engagement data to CSV format
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>CSV string containing engagement data</returns>
        public async Task<string> ExportEngagementDataToCsvAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                Logger.Info($"Exporting engagement data from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");

                var records = await GetAllEngagementRecordsAsync(startDate, endDate, 10000);
                if (records == null || records.Count == 0)
                {
                    return "No data to export";
                }

                var csv = new System.Text.StringBuilder();
                
                // Add header
                csv.AppendLine("Id,UserName,NewsId,NewsTitle,Timestamp,Source");
                
                // Add data rows
                foreach (var record in records)
                {
                    csv.AppendLine($"{record.Id},{EscapeCsvField(record.UserName)},{record.NewsId},{EscapeCsvField(record.NewsTitle)},{record.Timestamp:yyyy-MM-dd HH:mm:ss},{record.Source}");
                }

                Logger.Info($"Successfully exported {records.Count} engagement records to CSV");
                return csv.ToString();
            }
            catch (Exception ex)
            {
                Logger.Error($"Error exporting engagement data to CSV: {ex.Message}", ex);
                throw;
            }
        }

        #region Helper Methods

        /// <summary>
        /// Gets daily engagement counts for a specific period
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Dictionary mapping dates to engagement counts</returns>
        private async Task<Dictionary<DateTime, int>> GetDailyEngagementCountsAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                Logger.Info($"Getting daily engagement counts from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
                
                var result = new Dictionary<DateTime, int>();
                
                using (SqlConnection connection = new SqlConnection(BanyanDBConnection.connectionString))
                {
                    await connection.OpenAsync();
                    
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT CAST(Timestamp AS DATE) AS EngagementDate, COUNT(*) AS Count
                        FROM EngagementRecords 
                        WHERE Timestamp BETWEEN @StartDate AND @EndDate
                        GROUP BY CAST(Timestamp AS DATE)
                        ORDER BY CAST(Timestamp AS DATE)", connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);
                        
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                DateTime date = reader.GetDateTime(0);
                                int count = reader.GetInt32(1);
                                result[date] = count;
                            }
                        }
                    }
                }
                
                // Fill in missing dates with zero counts
                for (DateTime date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
                {
                    if (!result.ContainsKey(date))
                    {
                        result[date] = 0;
                    }
                }
                
                Logger.Info($"Retrieved daily engagement counts for {result.Count} days");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting daily engagement counts: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets source distribution of engagements for a specific period
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Dictionary mapping sources to engagement counts</returns>
        private async Task<Dictionary<string, int>> GetEngagementSourceDistributionAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                Logger.Info($"Getting engagement source distribution from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
                
                var result = new Dictionary<string, int>();
                
                using (SqlConnection connection = new SqlConnection(BanyanDBConnection.connectionString))
                {
                    await connection.OpenAsync();
                    
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT Source, COUNT(*) AS Count
                        FROM EngagementRecords 
                        WHERE Timestamp BETWEEN @StartDate AND @EndDate
                        GROUP BY Source", connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);
                        
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                string source = reader.GetString(0);
                                int count = reader.GetInt32(1);
                                result[source] = count;
                            }
                        }
                    }
                }
                
                Logger.Info($"Retrieved engagement source distribution: {string.Join(", ", result.Select(kv => $"{kv.Key}={kv.Value}"))}");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting engagement source distribution: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets user engagement distribution for a specific period
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Dictionary mapping user names to engagement counts</returns>
        private async Task<Dictionary<string, int>> GetUserEngagementDistributionAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                Logger.Info($"Getting user engagement distribution from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
                
                var result = new Dictionary<string, int>();
                
                using (SqlConnection connection = new SqlConnection(BanyanDBConnection.connectionString))
                {
                    await connection.OpenAsync();
                    
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT TOP 20 UserName, COUNT(*) AS Count
                        FROM EngagementRecords 
                        WHERE Timestamp BETWEEN @StartDate AND @EndDate
                        GROUP BY UserName
                        ORDER BY COUNT(*) DESC", connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);
                        
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                string userName = reader.GetString(0);
                                int count = reader.GetInt32(1);
                                result[userName] = count;
                            }
                        }
                    }
                }
                
                Logger.Info($"Retrieved user engagement distribution for {result.Count} users");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting user engagement distribution: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets content type distribution of engagements for a specific period
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Dictionary mapping content types to engagement counts</returns>
        private async Task<Dictionary<string, int>> GetContentTypeDistributionAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                Logger.Info($"Getting content type distribution from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
                
                var result = new Dictionary<string, int>();
                
                using (SqlConnection connection = new SqlConnection(BanyanDBConnection.connectionString))
                {
                    await connection.OpenAsync();
                    
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT n.Type, COUNT(*) AS Count
                        FROM EngagementRecords er
                        JOIN News n ON er.NewsId = n.Id
                        WHERE er.Timestamp BETWEEN @StartDate AND @EndDate
                        GROUP BY n.Type", connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);
                        
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                string contentType = reader.IsDBNull(0) ? "Unknown" : reader.GetString(0);
                                int count = reader.GetInt32(1);
                                result[contentType] = count;
                            }
                        }
                    }
                }
                
                Logger.Info($"Retrieved content type distribution: {string.Join(", ", result.Select(kv => $"{kv.Key}={kv.Value}"))}");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting content type distribution: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets hourly distribution of engagements for a specific period
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Dictionary mapping hours to engagement counts</returns>
        private async Task<Dictionary<int, int>> GetHourlyEngagementDistributionAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                Logger.Info($"Getting hourly engagement distribution from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
                
                var result = new Dictionary<int, int>();
                
                using (SqlConnection connection = new SqlConnection(BanyanDBConnection.connectionString))
                {
                    await connection.OpenAsync();
                    
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT DATEPART(HOUR, Timestamp) AS Hour, COUNT(*) AS Count
                        FROM EngagementRecords 
                        WHERE Timestamp BETWEEN @StartDate AND @EndDate
                        GROUP BY DATEPART(HOUR, Timestamp)
                        ORDER BY DATEPART(HOUR, Timestamp)", connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);
                        
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                int hour = reader.GetInt32(0);
                                int count = reader.GetInt32(1);
                                result[hour] = count;
                            }
                        }
                    }
                }
                
                // Fill in missing hours with zero counts
                for (int hour = 0; hour < 24; hour++)
                {
                    if (!result.ContainsKey(hour))
                    {
                        result[hour] = 0;
                    }
                }
                
                Logger.Info($"Retrieved hourly engagement distribution");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting hourly engagement distribution: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets day of week distribution of engagements for a specific period
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Dictionary mapping days of week to engagement counts</returns>
        private async Task<Dictionary<DayOfWeek, int>> GetDayOfWeekEngagementDistributionAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                Logger.Info($"Getting day of week engagement distribution from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
                
                var result = new Dictionary<DayOfWeek, int>();
                
                using (SqlConnection connection = new SqlConnection(BanyanDBConnection.connectionString))
                {
                    await connection.OpenAsync();
                    
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT DATEPART(WEEKDAY, Timestamp) AS WeekDay, COUNT(*) AS Count
                        FROM EngagementRecords 
                        WHERE Timestamp BETWEEN @StartDate AND @EndDate
                        GROUP BY DATEPART(WEEKDAY, Timestamp)
                        ORDER BY DATEPART(WEEKDAY, Timestamp)", connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);
                        
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                // SQL Server DATEPART(WEEKDAY) returns 1 for Sunday, 2 for Monday, etc.
                                // Convert to .NET DayOfWeek enum (0 for Sunday, 1 for Monday, etc.)
                                int sqlWeekDay = reader.GetInt32(0);
                                DayOfWeek dayOfWeek = (DayOfWeek)((sqlWeekDay - 1) % 7);
                                int count = reader.GetInt32(1);
                                result[dayOfWeek] = count;
                            }
                        }
                    }
                }
                
                // Fill in missing days with zero counts
                foreach (DayOfWeek day in Enum.GetValues(typeof(DayOfWeek)))
                {
                    if (!result.ContainsKey(day))
                    {
                        result[day] = 0;
                    }
                }
                
                Logger.Info($"Retrieved day of week engagement distribution");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting day of week engagement distribution: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Escapes a field for CSV output
        /// </summary>
        /// <param name="field">Field to escape</param>
        /// <returns>Escaped field</returns>
        private string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
            {
                return string.Empty;
            }

            // If the field contains a comma, a double quote, or a newline, wrap it in double quotes
            if (field.Contains(",") || field.Contains("\"") || field.Contains("\n") || field.Contains("\r"))
            {
                // Replace any double quotes with two double quotes
                field = field.Replace("\"", "\"\"");
                return $"\"{field}\"";
            }

            return field;
        }

        #endregion
    }

    /// <summary>
    /// Detailed engagement metrics
    /// </summary>
    public class EngagementDetailedMetrics
    {
        /// <summary>
        /// Start date for the metrics period
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// End date for the metrics period
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// Daily engagement counts
        /// </summary>
        public Dictionary<DateTime, int> DailyEngagementCounts { get; set; } = new Dictionary<DateTime, int>();

        /// <summary>
        /// Source distribution of engagements
        /// </summary>
        public Dictionary<string, int> SourceDistribution { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// User engagement distribution
        /// </summary>
        public Dictionary<string, int> UserEngagementDistribution { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// Content type distribution of engagements
        /// </summary>
        public Dictionary<string, int> ContentTypeDistribution { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// Hourly distribution of engagements
        /// </summary>
        public Dictionary<int, int> HourlyDistribution { get; set; } = new Dictionary<int, int>();

        /// <summary>
        /// Day of week distribution of engagements
        /// </summary>
        public Dictionary<DayOfWeek, int> DayOfWeekDistribution { get; set; } = new Dictionary<DayOfWeek, int>();
    }

    /// <summary>
    /// Engagement effectiveness metrics
    /// </summary>
    public class EngagementEffectivenessMetrics
    {
        /// <summary>
        /// Start date for the metrics period
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// End date for the metrics period
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// Click-through rate for recommendations
        /// </summary>
        public double ClickThroughRate { get; set; }

        /// <summary>
        /// Total number of recommendations
        /// </summary>
        public int TotalRecommendations { get; set; }

        /// <summary>
        /// Number of clicked recommendations
        /// </summary>
        public int ClickedRecommendations { get; set; }

        /// <summary>
        /// Number of email engagements
        /// </summary>
        public int EmailEngagements { get; set; }

        /// <summary>
        /// Number of email digests sent
        /// </summary>
        public int EmailDigestsSent { get; set; }

        /// <summary>
        /// Total number of news items included in email digests
        /// </summary>
        public int TotalNewsInDigests { get; set; }

        /// <summary>
        /// Email effectiveness rate
        /// </summary>
        public double EmailEffectivenessRate { get; set; }

        /// <summary>
        /// Number of unique users
        /// </summary>
        public int UniqueUsers { get; set; }

        /// <summary>
        /// Number of retained users
        /// </summary>
        public int RetainedUsers { get; set; }

        /// <summary>
        /// User retention rate
        /// </summary>
        public double RetentionRate { get; set; }
    }
}
