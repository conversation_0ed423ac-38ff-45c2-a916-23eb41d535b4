﻿@using Banyan.Domain
@{
    ViewBag.Name = "投资团队报表";
    Layout = "/Views/Shared/_LayoutSuper.cshtml";
    var creatorlist = (List<Banyan.Domain.Member>)ViewData["creatorlist"];
    var manager = ViewData["manager"] as Banyan.Domain.Member;
}

<style>
    td .layui-table-cell, .layui-table-cell img {
        height: 35px !important;
        line-height: 35px !important;
        padding: 0 15px;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
        box-sizing: border-box;
    }

</style>

<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li>选择编辑人查看</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <div class="tab-pane active">
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="pull-right search-bar"></div>
                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                                <div class="form-group">
                                    <select class="form-control" id="creator" name="creator" size="1">
                                        <option value="0">编辑人</option>
                                        @if (creatorlist != null && creatorlist.Count() > 0)
                                        {
                                            foreach (var ci in creatorlist)
                                            {
                                                <option value="@(ci.RealName)">@(ci.RealName)</option>
                                            }
                                        }
                                    </select>
                                </div>
                                @*<div class="form-group">
                <select class="form-control" id="creator" name="creator" size="1">
                    <option value="0">创建人</option>
                </select>
            </div>*@
                                <div class="form-group">
                                    <label class="sr-only" for="title-name">搜索</label>
                                    <input class="form-control" type="text" id="keyname" name="keyname" placeholder="搜索">
                                </div>
                                <div class="form-group">
                                    <div class="input-daterange input-group">
                                        <input class="form-control" type="text" id="startdate" name="startdate" placeholder="开始日期">
                                        <span class="input-group-addon" style="border-left:none;border-right:none;"><i class="fa fa-chevron-right"></i></span>
                                        <input class="form-control" type="text" id="enddate" name="enddate" placeholder="结束日期">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <a class="btn btn-default" id="dosearch">搜索</a>
                                </div>
                            </form>
                            @*<div class="form-group">
            <a class="btn btn-default" id="rotatel">左旋30度</a>
            <a class="btn btn-default" id="rotater">右旋30度</a>
        </div>*@
                            <div id="bymonth" style="width: 100%;height:600px;"></div>
                            <div id="main" style="width: 100%;height:1150px;"></div>



                        </div>

                    </div>
                </div>
                <table class="layui-hide" id="table-list" lay-filter="list-filter"></table>
                <table class="layui-hide" id="staffperformance" lay-filter="list-filter"></table>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="bartpl">
    <div class="btn-group" role="group" data-toggle="buttons">
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="预览" lay-event="preview" data-original-title="预览"><i class="fa fa-eye"></i></button>
    </div>
</script>

<script type="text/html" id="titleTpl">
    <button type="button" lay-event="preview" class="layui-btn layui-btn-primary" style="width:100%">{{d.RealName}}</button>
</script>
@section scripts{
    <script src="~/Content/js/plugins/echarts.min.js"></script>
    <script type="text/javascript">
        var myChart = echarts.init(document.getElementById('main'));

        var byMonth = echarts.init(document.getElementById('bymonth'));

        // 使用刚指定的配置项和数据显示图表。


        var queryParams = {}
        var deg = -15
        layui.use(['laypage', 'layer', 'table', 'laydate'], function () {
            var laydate = layui.laydate;

            var tmpDate = new Date()
            laydate.render({
                elem: '#startdate',
                value: new Date(new Date(tmpDate.setFullYear(tmpDate.getFullYear() - 1)).setDate(tmpDate.getDate() + 1)),
                isInitValue: true
            });

            laydate.render({
                elem: '#enddate',
                value: new Date(),
                isInitValue: true
            });
            return;
            });

            //$('#rotatel').on('click', function () {
            //    deg -= 30;
            //    $("canvas").css("transform", `rotate(${deg}deg)`);
            //})
            //$('#rotater').on('click', function () {
            //    deg += 30;
            //    $("canvas").css("transform", `rotate(${deg}deg)`);
            //})
        function genmonth(data) {
            var xAxisData = [];
            var data1 = [];
            var data2 = [];
            var data3 = [];
            var data4 = [];
            var data5 = [];
            var data6 = [];

            data = data.sort((a, b) => {
                return new Date(a.name) - new Date(b.name)
            })
            for (var i = 1; i < data.length; i++) {
                var tmp = data[i]
                xAxisData.push(tmp.name);
                tmp.children.map(val => {
                    switch (val.name) {
                        case "项目负责人人脉": data1.push(val.value); break;
                        case "项目负责人研究": data2.push(val.value); break;
                        case "同事提及": data3.push(val.value); break;
                        case "同事介绍": data4.push(val.value); break;
                        case "其他形式的同事贡献": data5.push(val.value); break;
                        case "FA介绍": data6.push(val.value); break;
                        default: break;
                    }
                })
                if (data1.length < i) data1.push(0);
                if (data2.length < i) data2.push(0);
                if (data3.length < i) data3.push(0);
                if (data4.length < i) data4.push(0);
                if (data5.length < i) data5.push(0);
                if (data6.length < i) data6.push(0);
                
            }

            var emphasisStyle = {
                itemStyle: {
                    barBorderWidth: 1,
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowOffsetY: 0,
                    shadowColor: 'rgba(0,0,0,0.5)'
                }
            };

            var option = {
                color: [
                    "#2ec7c9",
                    "#5ab1ef",
                    "#ffb980",
                    "#d87a80",
                    "#e5cf0d", 
                    "#dc69aa",
                    "#07a2a4",
                    "#9a7fd1",
                    "#588dd5",
                    "#f5994e",
                    "#c05050",
                    "#59678c",
                    "#c9ab00",
                    "#7eb00a",
                    "#6f5553",
                    "#c14089"
                ],
                legend: {
                    data: ["项目负责人人脉", "项目负责人研究", "同事提及", "同事介绍", "FA介绍","其他形式的同事贡献"],
                    left: 250
                },
              
                toolbox: {
                    feature: {
                        magicType: {
                            type: ['line', 'bar', 'tiled', 'stack']
                        },
                        dataView: {}
                    }
                },
                tooltip: {},
                xAxis: {
                    data: xAxisData,
                    name: '时间',
                    axisLine: { onZero: true },
                    splitLine: { show: false },
                    splitArea: { show: false }
                },
                yAxis: {
                    name: "项目数量(个)",
                    splitArea: { show: false }
                },
                grid: {
                    left: 100
                },

               series: [
                   {
                       name: '项目负责人人脉',
                       type: 'bar',
                       stack: 'one',
                       emphasis: emphasisStyle,
                       data: data1
                   },
                   {
                       name: '项目负责人研究',
                       type: 'bar',
                       stack: 'one',
                       emphasis: emphasisStyle,
                       data: data2
                   },
                   {
                       name: '同事提及',
                       type: 'bar',
                       stack: 'one',
                       emphasis: emphasisStyle,
                       data: data3
                   },
                   {
                       name: '同事介绍',
                       type: 'bar',
                       stack: 'one',
                       emphasis: emphasisStyle,
                       data: data4
                   },
                    {
                        name: 'FA介绍',
                        type: 'bar',
                        stack: 'one',
                        emphasis: emphasisStyle,
                        data: data5
                   },
                   {
                       name: '其他形式的同事贡献',
                       type: 'bar',
                       stack: 'one',
                       emphasis: emphasisStyle,
                       data: data6
                   }

                ]
            };

            byMonth.setOption(option);

         
        }
        function gensunburst(data) {
            var option = {
                title: {
                    text: '项目按状态分类旭日图',
                    subtext: '点击扇形区域方块可进入该区域，再点击中心可返回；点击项目名对应的方块可打开项目详情页',
                    textStyle: {
                        fontSize: 14,
                        align: 'center'
                    },
                    subtextStyle: {
                        align: 'center'
                    },
                },
                series: {
                    type: 'sunburst',
                    data: data,
                    radius: [0, '75%'],
                    sort: null,
                    levels: [{}, {
                        r0: '5%',
                        r: '32%',
                        itemStyle: {
                            borderWidth: 1
                        },
                        label: {
                            align: 'right'
                        }
                    }, {
                        r0: '32%',
                        r: '70%',
                        label: {
                            align: 'right'
                        },
                        itemStyle: {
                            borderWidth: 2
                        }
                    }, {
                        r0: '70%',
                        r: '74%',
                        nodeClick: 'link',
                        label: {
                            position: 'outside',
                            silent: false,
                            fontWeight: "normal",
                            textBorderWidth: 4,
                            fontFamily: 'Microsoft YaHei',
                        },
                        itemStyle: {
                            borderWidth: 2
                        },
                        downplay: {
                            label: {
                                opacity: 0.8
                            }
                        }
                    }]
                }
            };
            myChart.setOption(option);
        }
        function dosearch() {
              queryParams = {
                    Creator: $('#creator').val(),
                    Name: $('#keyname').val(),
                    startdate: $('#startdate').val(),
                    enddate: $('#enddate').val(),
            }
              $.ajax({
                    type: 'POST',
                    url: '@(Url.Action("summarynodeslink", "adminapi"))',
                    data: queryParams,
                    success: function (data) {
                        if (data.code == 0) {
                            if (data.data.node.length == 0) {
                                layer.msg('所选范围无数据！');
                                return
                            } else {
                                layer.msg('操作成功！');
                            }
                            
                            gensunburst(data.data.node);
                            genmonth(data.data.month);
                            
                        } else {
                            layer.msg(data.msg);
                        }
                    },
                    error: function () {
                        layui.layer.msg("很抱歉，请求异常！");
                    }
            });
          
        }

         $('#dosearch').on('click',dosearch);
        $('#creator').on('change', dosearch);



    </script>
}
