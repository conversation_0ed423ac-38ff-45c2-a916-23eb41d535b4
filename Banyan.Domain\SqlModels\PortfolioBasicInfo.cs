﻿using Entity.Base;
using System;
using System.Collections.Generic;
using Utility;

namespace Banyan.Domain
{
    public class PortfolioBaicInfoComparer : IEqualityComparer<PortfolioBasicInfo>
    {
        public bool Equals(PortfolioBasicInfo x, PortfolioBasicInfo y)
        {
            if (x == null)
                return y == null;
            return x.portfolioID.Equals(y.portfolioID);
        }

        public int GetHashCode(PortfolioBasicInfo obj)
        {
            return 1;
        }
    }
    [Serializable]
    [SqlTable(dbEnum.BusinessCenterPortal)]
    public class PortfolioBasicInfo
    {
        public string id { get; set; }
        [SqlField(IsPrimaryKey = true)]
        public string portfolioID { get; set; }

        [SqlField]
        public String Name { get; set; } = String.Empty;
        [SqlField]
        public String OfficialName { get; set; } = String.Empty;
        [SqlField]
        public String abbName { get; set; } = String.Empty;

        [SqlField]
        public String abbNameChi { get; set; } = String.Empty;

        [SqlField]
        public String fullName { get; set; } = String.Empty;

        [SqlField]
        public String fullNameChi { get; set; } = String.Empty;


        [SqlField]
        public int SectorId { get; set; }

        [SqlField]
        public string City { get; set; }

        [SqlField]
        public int CityID { get; set; }

        [SqlField]
        public string District { get; set; }

        [SqlField]
        public String CEO { get; set; } = String.Empty;

        [SqlField]
        public String GroupMember { get; set; } = String.Empty;

        [SqlField]
        public String PostInvestMember { get; set; } = String.Empty;

        [SqlField]
        public String NewManager { get; set; } = String.Empty;
        [SqlField]
        public String PostInvestManager { get; set; } = String.Empty;
        [SqlField]
        public String ExitManager { get; set; } = String.Empty;

        [SqlField]
        public String PortfolioIntroducer { get; set; } = String.Empty;

        [SqlField]
        public String PrivateOrPublic { get; set; } = String.Empty;
        [SqlField]
        public String BoardSeatType { get; set; } = String.Empty;
        [SqlField]
        public int TotalBoardSeatNo { get; set; }

        [SqlField]
        public String BoardManager { get; set; } = String.Empty;

        [SqlField]
        public int RoleID { get; set; }
        [SqlField]
        public String BoardSeatComment { get; set; } = String.Empty;

        [SqlField]
        public int BoardSeatNo { get; set; }

        [SqlField]
        public string Headcount { get; set; } = String.Empty;

        [SqlField]
        public String oneLineDesc { get; set; } = String.Empty;

        [SqlField]
        public String oneLineDescChi { get; set; } = String.Empty;

        [SqlField]
        public String website { get; set; } = String.Empty;

        [SqlField]
        public String foundedDate { get; set; } = String.Empty;

        [SqlField]
        public String portfolioManager { get; set; } = String.Empty;

        public String fundName { get; set; } = String.Empty;

        [SqlField]
        public String createdBy { get; set; } = String.Empty;
        [SqlField]
        public DateTime createdDate { get; set; }
        [SqlField]
        public String modifiedBy { get; set; } = String.Empty;

        [SqlField]
        public DateTime modifiedDate { get; set; }
        [SqlField]
        public int contributionProjectID { get; set; }
    }

}
