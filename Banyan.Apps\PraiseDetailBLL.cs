﻿using Banyan.Code;
using Banyan.Domain;
using System;
using System.Collections.Specialized;

namespace Banyan.Apps
{
    public class PraiseDetailBLL : DAL.Base.BaseDAL<PraiseDetail>
    {
        private readonly AjaxResult ajaxResult = null;

        public PraiseDetailBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        /// <summary>
        /// 添加或保存分类数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public AjaxResult PraiseSet(NameValueCollection paramValues)
        {
            AjaxResult ajaxResult = new AjaxResult();

            var aid = WebHelper.GetValueInt("articleid", 0, paramValues);
            var uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var mode = WebHelper.GetValueInt("mode", 0, paramValues);
            var setType = WebHelper.GetValue("setType", string.Empty, paramValues);

            var collect = new PraiseDetail
            {
                UserId = uid,
                ArticleId = aid,
                Mode = (byte)mode,
            };
            if (mode == 0)
            {
                ArticleBLL articleBll = new ArticleBLL();
                var articleModel = articleBll.GetModel($"Id={aid}", null, "Id,PriseCount", "Id");
                if (articleModel == null)
                {
                    ajaxResult.code = (int)ResultCode.paramerror;
                    ajaxResult.msg = "记录不存在！";
                    return ajaxResult;
                }
                ajaxResult.code = Convert.ToInt32(Add(collect)) > 0 ? (int)ResultCode.success : (int)ResultCode.failed;
                articleModel.PriseCount += 1;
                articleBll.Update(articleModel, "PriseCount");
            }
            else
            {
                ProjectBLL projectBll = new ProjectBLL();
                var projModel = projectBll.GetModel($"Id={aid}", null, "Id,PriseCount", "Id");
                if (projModel == null)
                {
                    ajaxResult.code = (int)ResultCode.paramerror;
                    ajaxResult.msg = "记录不存在！";
                    return ajaxResult;
                }
                ajaxResult.code = Convert.ToInt32(Add(collect)) > 0 ? (int)ResultCode.success : (int)ResultCode.failed;
                projModel.PriseCount += 1;
                projectBll.Update(projModel, "PriseCount");
            }

            return ajaxResult;
        }

        /// <summary>
        /// 判断用户是否点赞
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="articleId"></param>
        /// <returns></returns>
        public bool IsCollect(int userId, int articleId, int mode = (int)CollectModeEnum.Interview)
        {
            return Exists($"UserId={userId} AND ArticleId={articleId} AND Mode={mode}");
        }
    }
}
