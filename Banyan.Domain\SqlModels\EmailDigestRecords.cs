using Entity.Base;
using System;
using System.Collections.Generic;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public class EmailDigestRecords
    {
        /// <summary>
        /// Unique identifier for the email digest record
        /// </summary>
        public int Id { get; set; }

        [SqlField]
        public int UserId { get; set; }

        [SqlField]
        public DateTime SentTime { get; set; }

        [SqlField]
        public int NewsCount { get; set; }

        [SqlField]
        public string NewsIds { get; set; }

        /// <summary>
        /// Associated user profile (populated when needed)
        /// </summary>
        public UserProfile UserProfile { get; set; }
    }

   
}