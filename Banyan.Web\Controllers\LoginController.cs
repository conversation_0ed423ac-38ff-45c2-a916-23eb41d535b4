﻿using Banyan.Apps;
using Banyan.Code;
//using System;
using System.Web.Mvc;

namespace Banyan.Web.Controllers
{
    public class LoginController : Controller
    {
        private MemberBLL memberBll = new MemberBLL();
        private NewsBLL newsBll = new NewsBLL();
        public ActionResult Index()
        {
            return View();
        }


        /// <summary>
        /// 用户登录
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult Login()
        {
            var ajaxResult = new MemberBLL().LogonValid(Request.Params);
            return Json(ajaxResult);
        }

        [HttpPost]
        public ActionResult bindwechat()
        {
            var ajaxResult = new MemberBLL().LogonValid(Request.Params, true);
            return Json(ajaxResult);
        }

        public ActionResult Wechat()
        {
            var res = new MemberBLL().LogonWechat(Request.Params);
            if (res.Item1)
            {
                return Redirect("/");
            } else
            {
                ViewData["unionid"] = res.Item2;
                return View("");
            }
        }

        public ActionResult GetValidCode()
        {
            byte[] bytes = new VerifyCode().GetVerifyCode("banyan_verifycode");
            return File(bytes, @"image/jpeg");
        }

        public ActionResult News(string user, int id = 0, string highlight = "", string recommend = "")
        {
            if(string.IsNullOrEmpty(user))
            {
                return null;
            } 
            if(id == 0)
            {
                return null;
            }
            ViewBag.id = id;
            Domain.Member model = memberBll.getUserByCookie(user);
            ViewBag.model = newsBll.GetModel(model, id, recommend == "1");
            ViewBag.imgWidth = "100%";
            ViewBag.name = model.RealName;
            ViewBag.highlight = highlight.Replace("(revisit)", "").Trim(); ;
            return View();
        }

    }
}