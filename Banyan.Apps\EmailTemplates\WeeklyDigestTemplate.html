<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每周新闻推荐</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        h2 {
            color: #3498db;
            margin-top: 20px;
        }
        .news-item {
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .news-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .news-meta {
            font-size: 12px;
            color: #7f8c8d;
            margin-bottom: 10px;
        }
        .news-summary {
            font-size: 14px;
            margin-bottom: 10px;
        }
        .news-link {
            display: inline-block;
            color: #3498db;
            text-decoration: none;
            font-weight: bold;
        }
        .news-link:hover {
            text-decoration: underline;
        }
        .news-tag {
            display: inline-block;
            background-color: #f1f1f1;
            padding: 2px 6px;
            margin-right: 5px;
            border-radius: 3px;
            font-size: 12px;
            color: #555;
        }
        .footer {
            margin-top: 30px;
            font-size: 12px;
            color: #7f8c8d;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
        .header-logo {
            max-height: 50px;
            margin-bottom: 20px;
        }
        .container {
            background-color: #ffffff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #eee;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .button {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 10px 15px;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 10px;
        }
        .similarity-score {
            display: inline-block;
            background-color: #2ecc71;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            margin-left: 5px;
        }
        @media only screen and (max-width: 600px) {
            body {
                padding: 10px;
            }
            .news-title {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="https://ims.gaorongvc.com/Content/images/logo.png" alt="高榕创投" class="header-logo">
            <h1>每周新闻推荐</h1>
        </div>
        
        <div class="content">
            <p>您好，{UserName}！</p>
            <p>以下是本周为您精选的新闻内容，希望对您有所帮助。</p>
            
            <div class="recommendations">
                <!-- NEWS_ITEMS_PLACEHOLDER -->
            </div>
            
            <p>
                <a href="https://ims.gaorongvc.com/News/List" class="button">查看更多新闻</a>
            </p>
        </div>
        
        <div class="footer">
            <p>此邮件由系统自动发送，请勿直接回复。</p>
            <p>如需取消订阅或调整邮件接收频率，请联系管理员。</p>
            <p>&copy; {CurrentYear} 高榕创投. 保留所有权利.</p>
        </div>
    </div>
</body>
</html>