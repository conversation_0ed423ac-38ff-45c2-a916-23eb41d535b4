using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Banyan.Code;
using Banyan.Domain;
using Banyan.Apps.Configs;

namespace Banyan.Apps
{
    /// <summary>
    /// Handles updates to user interest profiles based on engagement with news articles
    /// </summary>
    public class UserInterestProfileUpdater
    {
        private readonly UserProfileBLL _userProfileBLL;
        private readonly NewsBLL _newsBLL;
        private readonly NewsVectorizationService _vectorizationService;
        private readonly UserInterestVectorRetrieval _vectorRetrieval;
        private readonly ICache _cache;
        private readonly Dictionary<string, double> _engagementWeights;
        private readonly Dictionary<int, DateTime> _lastUpdateTimes;
        private readonly Dictionary<int, int> _updateCounts;
        private readonly object _lockObject = new object();

        // Constants for profile update limits
        private const int MAX_UPDATES_PER_DAY = 50;
        private const int MIN_UPDATE_INTERVAL_SECONDS = 10;
        private const double MAX_WEIGHT_CHANGE_PER_UPDATE = 0.2;

        /// <summary>
        /// Constructor
        /// </summary>
        public UserInterestProfileUpdater()
        {
            _userProfileBLL = new UserProfileBLL();
            _newsBLL = new NewsBLL();
            _vectorizationService = new NewsVectorizationService();
            _vectorRetrieval = new UserInterestVectorRetrieval();
            _cache = CacheFactory.Cache();
            
            // Initialize engagement weights for different sources
            _engagementWeights = new Dictionary<string, double>
            {
                { "web", 1.0 },       // Full weight for web clicks (direct user action)
                { "email", 0.8 },      // Slightly lower weight for email clicks
                { "recommendation", 0.6 } // Lower weight for recommendation-driven clicks
            };
            
            // Initialize tracking dictionaries for anti-manipulation
            _lastUpdateTimes = new Dictionary<int, DateTime>();
            _updateCounts = new Dictionary<int, int>();
        }

        /// <summary>
        /// Updates a user's interest profile based on engagement with a news article
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="newsId">News ID</param>
        /// <param name="source">Source of the engagement (web, email, etc.)</param>
        /// <returns>True if update was successful, false otherwise</returns>
        public async Task<bool> UpdateProfileBasedOnEngagementAsync(int userId, int newsId, string source)
        {
            try
            {
                Logger.Info($"Updating interest profile for user {userId} based on news {newsId} from {source}");

                // Check for profile manipulation attempts
                if (!ValidateUpdateRequest(userId))
                {
                    Logger.Warn($"Update request for user {userId} rejected due to rate limiting");
                    return false;
                }

                // Get the news vector
                var newsVector = await _vectorizationService.GetNewsVectorAsync(newsId, _newsBLL);
                if (newsVector == null)
                {
                    Logger.Warn($"Cannot update interest profile: News vector for {newsId} not found");
                    return false;
                }

                // Get the news tags for interest updating
                var news = _newsBLL.GetModel(newsId);
                if (news == null)
                {
                    Logger.Warn($"Cannot update interest profile: News {newsId} not found");
                    return false;
                }

                // Extract tags from the news
                var tags = new List<string>();
                if (!string.IsNullOrEmpty(news.Tag))
                {
                    tags.AddRange(news.Tag.Split(new[] { ',', '，', ';', '；', ' ' }, StringSplitOptions.RemoveEmptyEntries));
                }

                // Apply engagement weight based on source
                double weight = GetEngagementWeight(source);
                
                // Apply weighted update to the user's interest profile
                bool success = await UpdateUserInterestWithWeightedNewsAsync(userId, newsId, newsVector, tags, weight);

                if (success)
                {
                    Logger.Info($"Successfully updated interest profile for user {userId} with weight {weight}");
                    
                    // Update tracking for anti-manipulation
                    RecordProfileUpdate(userId);
                }
                else
                {
                    Logger.Warn($"Failed to update interest profile for user {userId}");
                }

                return success;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error updating interest profile for user {userId}: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// Updates a user's interest profile with weighted news vector and tags
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="newsId">News ID</param>
        /// <param name="newsVector">News vector</param>
        /// <param name="tags">News tags</param>
        /// <param name="weight">Engagement weight</param>
        /// <returns>True if update was successful, false otherwise</returns>
        private async Task<bool> UpdateUserInterestWithWeightedNewsAsync(int userId, int newsId, double[] newsVector, List<string> tags, double weight)
        {
            try
            {
                // Get the user's current interest vector
                var userVector = await _vectorRetrieval.GetUserInterestVectorAsync(userId);
                if (userVector == null)
                {
                    Logger.Warn($"Cannot update interest profile: User vector for {userId} not found");
                    return false;
                }

                // Apply weighted update to the user vector
                var updatedVector = ApplyWeightedVectorUpdate(userVector, newsVector, weight);
                
                // Update the user's interest vector in cache
                _vectorRetrieval.UpdateUserInterestVectorCache(userId, updatedVector);
                
                // Update user tags with weighted importance
                await UpdateUserTagsWithWeightAsync(userId, tags, weight);
                
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error applying weighted update for user {userId}: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// Applies a weighted update to a user vector based on a news vector
        /// </summary>
        /// <param name="userVector">User interest vector</param>
        /// <param name="newsVector">News vector</param>
        /// <param name="weight">Update weight</param>
        /// <returns>Updated user vector</returns>
        private double[] ApplyWeightedVectorUpdate(double[] userVector, double[] newsVector, double weight)
        {
            if (userVector == null || newsVector == null || userVector.Length != newsVector.Length)
            {
                return userVector;
            }

            // Apply weighted update with limited influence
            double effectiveWeight = Math.Min(weight * MAX_WEIGHT_CHANGE_PER_UPDATE, MAX_WEIGHT_CHANGE_PER_UPDATE);
            double[] updatedVector = new double[userVector.Length];
            
            for (int i = 0; i < userVector.Length; i++)
            {
                // Weighted average: (1-w)*user + w*news
                updatedVector[i] = (1 - effectiveWeight) * userVector[i] + effectiveWeight * newsVector[i];
            }
            
            // Normalize the vector
            double magnitude = Math.Sqrt(updatedVector.Sum(v => v * v));
            if (magnitude > 0)
            {
                for (int i = 0; i < updatedVector.Length; i++)
                {
                    updatedVector[i] /= magnitude;
                }
            }
            
            return updatedVector;
        }

        /// <summary>
        /// Updates user tags with weighted importance
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="tags">News tags</param>
        /// <param name="weight">Update weight</param>
        /// <returns>True if update was successful, false otherwise</returns>
        private async Task<bool> UpdateUserTagsWithWeightAsync(int userId, List<string> tags, double weight)
        {
            try
            {
                if (tags == null || tags.Count == 0)
                {
                    return true; // No tags to update
                }
                
                // Get user tag relations
                var userTagRelationBLL = new UserTagRelationBLL();
                var userTagRelations = await userTagRelationBLL.GetUserTagRelationsAsync(userId);
                
                // Get or create tag IDs
                var userInterestTagBLL = new UserInterestTagBLL();
                var tagIds = new Dictionary<string, int>();
                
                foreach (var tag in tags)
                {
                    int tagId = await userInterestTagBLL.GetOrCreateTagIdAsync(tag);
                    if (tagId > 0)
                    {
                        tagIds[tag] = tagId;
                    }
                }
                
                // Update tag weights
                foreach (var tag in tags)
                {
                    if (!tagIds.ContainsKey(tag))
                    {
                        continue;
                    }
                    
                    int tagId = tagIds[tag];
                    var relation = userTagRelations.FirstOrDefault(r => r.TagId == tagId);
                    
                    if (relation != null)
                    {
                        // Update existing relation
                        double newWeight = Math.Min(relation.Weight + (weight * 0.1), 1.0);
                        relation.Weight = newWeight;
                        relation.ClickCount++;
                        await userTagRelationBLL.UpdateUserTagRelationAsync(relation);
                    }
                    else
                    {
                        // Create new relation
                        var newRelation = new UserTagRelation
                        {
                            UserId = userId,
                            TagId = tagId,
                            Weight = Math.Min(weight * 0.5, 0.5), // Initial weight is half of the engagement weight
                            ClickCount = 1
                        };
                        await userTagRelationBLL.AddUserTagRelationAsync(newRelation);
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error updating user tags for user {userId}: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// Gets the weight for an engagement based on its source
        /// </summary>
        /// <param name="source">Engagement source</param>
        /// <returns>Engagement weight</returns>
        private double GetEngagementWeight(string source)
        {
            if (string.IsNullOrWhiteSpace(source))
            {
                return _engagementWeights["web"]; // Default to web weight
            }
            
            source = source.ToLower();
            
            if (_engagementWeights.ContainsKey(source))
            {
                return _engagementWeights[source];
            }
            
            return _engagementWeights["web"]; // Default to web weight
        }

        /// <summary>
        /// Validates an update request to prevent profile manipulation
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if the request is valid, false otherwise</returns>
        private bool ValidateUpdateRequest(int userId)
        {
            lock (_lockObject)
            {
                // Check if this is the first update for this user today
                DateTime now = DateTime.Now;
                DateTime today = now.Date;
                
                if (_lastUpdateTimes.ContainsKey(userId))
                {
                    // Check minimum interval between updates
                    TimeSpan timeSinceLastUpdate = now - _lastUpdateTimes[userId];
                    if (timeSinceLastUpdate.TotalSeconds < MIN_UPDATE_INTERVAL_SECONDS)
                    {
                        Logger.Warn($"Update request for user {userId} rejected: too frequent (last update {timeSinceLastUpdate.TotalSeconds:F1} seconds ago)");
                        return false;
                    }
                    
                    // Check if last update was on a different day
                    if (_lastUpdateTimes[userId].Date < today)
                    {
                        // Reset counter for new day
                        _updateCounts[userId] = 0;
                    }
                    else
                    {
                        // Check maximum updates per day
                        if (_updateCounts[userId] >= MAX_UPDATES_PER_DAY)
                        {
                            Logger.Warn($"Update request for user {userId} rejected: maximum updates per day ({MAX_UPDATES_PER_DAY}) reached");
                            return false;
                        }
                    }
                }
                else
                {
                    // Initialize tracking for new user
                    _updateCounts[userId] = 0;
                }
                
                return true;
            }
        }

        /// <summary>
        /// Records a profile update for anti-manipulation tracking
        /// </summary>
        /// <param name="userId">User ID</param>
        private void RecordProfileUpdate(int userId)
        {
            lock (_lockObject)
            {
                _lastUpdateTimes[userId] = DateTime.Now;
                
                if (_updateCounts.ContainsKey(userId))
                {
                    _updateCounts[userId]++;
                }
                else
                {
                    _updateCounts[userId] = 1;
                }
            }
        }
    }
}