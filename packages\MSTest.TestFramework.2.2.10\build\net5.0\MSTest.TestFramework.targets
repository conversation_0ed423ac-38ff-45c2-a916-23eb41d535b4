<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(UseWinUI)' == 'true' ">
      <_TestFrameworkExtensionsRoot>$(MSBuildThisFileDirectory)winui/</_TestFrameworkExtensionsRoot>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(UseWinUI)' != 'true' ">
      <_TestFrameworkExtensionsRoot>$(MSBuildThisFileDirectory)</_TestFrameworkExtensionsRoot>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions">
      <HintPath>$(_TestFrameworkExtensionsRoot)Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll</HintPath>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Visible>False</Visible>
    </Reference>
  </ItemGroup>
</Project>