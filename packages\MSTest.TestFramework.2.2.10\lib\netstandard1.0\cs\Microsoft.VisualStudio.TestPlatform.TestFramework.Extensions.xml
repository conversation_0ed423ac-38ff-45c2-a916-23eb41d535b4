<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute">
            <summary>
            Používá se pro určení položky nasazení (souboru nebo adresáře) za účelem nasazení podle testu.
            Lze zadat na testovací třídě nebo testovací metodě.
            Může mít více instancí atributu pro zadání více než jedné polož<PERSON>.
            Cesta k položce může být absolutní nebo relativní. Pokud je relativní, je relativní ve vztahu k RunConfig.RelativePathRoot.
            </summary>
            <example>
            [DeploymentItem("file1.xml")]
            [DeploymentItem("file2.xml", "DataFiles")]
            [DeploymentItem("bin\Debug")]
            </example>
            <remarks>
            DeploymentItemAttribute is currently not supported in .Net Core. This is just a placehodler for support in the future.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/>.
            </summary>
            <param name="path">Soubor nebo adresář, který se má nasadit. Cesta je relativní ve vztahu k adresáři výstupu sestavení. Položka bude zkopírována do adresáře, ve kterém jsou nasazená testovací sestavení.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String,System.String)">
            <summary>
            Inicializuje novou instanci třídy <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/>.
            </summary>
            <param name="path">Relativní nebo absolutní cesta k souboru nebo adresáři, který se má nasadit. Cesta je relativní ve vztahu k adresáři výstupu sestavení. Položka bude zkopírována do stejného adresáře jako nasazená testovací sestavení.</param>
            <param name="outputDirectory">Cesta k adresáři, do kterého se mají položky kopírovat. Může být absolutní nebo relativní ve vztahu k adresáři nasazení. Všechny soubory a adresáře určené cestou <paramref name="path"/> budou zkopírovány do tohoto adresáře.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.Path">
            <summary>
            Získá cestu ke zdrojovému souboru nebo složce, které se mají kopírovat.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.OutputDirectory">
            <summary>
            Získá cestu adresáře, do kterého se položka zkopíruje.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext">
            <summary>
            Třída TestContext. Tato třída by měla být zcela abstraktní a neměla by obsahovat žádné
            členy. Členy implementuje adaptér. Uživatelé rozhraní by měli
            k této funkci přistupovat jenom prostřednictvím dobře definovaného rozhraní.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.Properties">
            <summary>
            Získá vlastnosti testu.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.FullyQualifiedTestClassName">
            <summary>
            Získá plně kvalifikovaný název třídy obsahující aktuálně prováděnou testovací metodu.
            </summary>
            <remarks>
            This property can be useful in attributes derived from ExpectedExceptionBaseAttribute.
            Those attributes have access to the test context, and provide messages that are included
            in the test results. Users can benefit from messages that include the fully-qualified
            class name in addition to the name of the test method currently being executed.
            </remarks>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestName">
            <summary>
            Získá název aktuálně prováděné testovací metody.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.CurrentTestOutcome">
            <summary>
            Získá aktuální výsledek testu.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String)">
            <summary>
            Used to write trace messages while the test is running
            </summary>
            <param name="message">formatted message string</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String,System.Object[])">
            <summary>
            Used to write trace messages while the test is running
            </summary>
            <param name="format">format string</param>
            <param name="args">the arguments</param>
        </member>
    </members>
</doc>
