/**
 * Recommendation Widget Loader
 * 
 * This script allows for easy embedding of recommendation widgets in any page.
 * It automatically initializes recommendation widgets based on data attributes.
 */

$(document).ready(function() {
    // Find all recommendation widget containers
    $('[data-recommendation-widget]').each(function() {
        var $container = $(this);
        var widgetId = $container.attr('id') || 'recommendation-widget-' + Math.random().toString(36).substr(2, 9);
        
        // Ensure container has an ID
        if (!$container.attr('id')) {
            $container.attr('id', widgetId);
        }
        
        // Get widget configuration from data attributes
        var config = {
            type: $container.data('recommendation-type') || 'personalized',
            limit: parseInt($container.data('recommendation-limit') || 3),
            title: $container.data('recommendation-title') || '推荐新闻',
            description: $container.data('recommendation-description') || '根据您的兴趣为您推荐的新闻',
            category: $container.data('recommendation-category') || '',
            source: $container.data('recommendation-source') || '',
            autoRefresh: $container.data('recommendation-auto-refresh') === true,
            refreshInterval: parseInt($container.data('recommendation-refresh-interval') || 300000),
            layout: $container.data('recommendation-layout') || 'vertical',
            showScore: $container.data('recommendation-show-score') !== false
        };
        
        // Create widget structure
        var widgetHtml = `
            <div class="recommendation-widget" id="${widgetId}-inner">
                <div class="widget-header">
                    <h5>${config.title}</h5>
                    <p class="text-muted">${config.description}</p>
                </div>
                <div class="widget-body">
                    <div class="recommendation-list ${config.layout}">
                        <div class="widget-loading">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="sr-only">加载中...</span>
                            </div>
                            <span>加载推荐内容...</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Insert widget HTML
        $container.html(widgetHtml);
        
        // Initialize widget
        if (typeof RecommendationWidget !== 'undefined') {
            RecommendationWidget.init(widgetId + '-inner', config);
        } else {
            console.error('RecommendationWidget module not loaded');
            $container.find('.widget-loading').html('<div class="widget-error">推荐模块未加载</div>');
        }
    });
});

/**
 * Helper function to embed a recommendation widget in any page
 * @param {string} containerId - ID of the container element
 * @param {Object} options - Widget configuration options
 */
function embedRecommendationWidget(containerId, options) {
    var container = document.getElementById(containerId);
    if (!container) {
        console.error('Container element not found:', containerId);
        return;
    }
    
    // Set data attributes based on options
    if (options) {
        container.setAttribute('data-recommendation-widget', 'true');
        
        if (options.type) container.setAttribute('data-recommendation-type', options.type);
        if (options.limit) container.setAttribute('data-recommendation-limit', options.limit);
        if (options.title) container.setAttribute('data-recommendation-title', options.title);
        if (options.description) container.setAttribute('data-recommendation-description', options.description);
        if (options.category) container.setAttribute('data-recommendation-category', options.category);
        if (options.source) container.setAttribute('data-recommendation-source', options.source);
        if (options.autoRefresh) container.setAttribute('data-recommendation-auto-refresh', options.autoRefresh);
        if (options.refreshInterval) container.setAttribute('data-recommendation-refresh-interval', options.refreshInterval);
        if (options.layout) container.setAttribute('data-recommendation-layout', options.layout);
        if (options.showScore !== undefined) container.setAttribute('data-recommendation-show-score', options.showScore);
    }
    
    // Initialize if document is already ready
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        $(container).each(function() {
            var $container = $(this);
            var widgetId = $container.attr('id') || 'recommendation-widget-' + Math.random().toString(36).substr(2, 9);
            
            // Ensure container has an ID
            if (!$container.attr('id')) {
                $container.attr('id', widgetId);
            }
            
            // Get widget configuration from data attributes
            var config = {
                type: $container.data('recommendation-type') || 'personalized',
                limit: parseInt($container.data('recommendation-limit') || 3),
                title: $container.data('recommendation-title') || '推荐新闻',
                description: $container.data('recommendation-description') || '根据您的兴趣为您推荐的新闻',
                category: $container.data('recommendation-category') || '',
                source: $container.data('recommendation-source') || '',
                autoRefresh: $container.data('recommendation-auto-refresh') === true,
                refreshInterval: parseInt($container.data('recommendation-refresh-interval') || 300000),
                layout: $container.data('recommendation-layout') || 'vertical',
                showScore: $container.data('recommendation-show-score') !== false
            };
            
            // Create widget structure
            var widgetHtml = `
                <div class="recommendation-widget" id="${widgetId}-inner">
                    <div class="widget-header">
                        <h5>${config.title}</h5>
                        <p class="text-muted">${config.description}</p>
                    </div>
                    <div class="widget-body">
                        <div class="recommendation-list ${config.layout}">
                            <div class="widget-loading">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="sr-only">加载中...</span>
                                </div>
                                <span>加载推荐内容...</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Insert widget HTML
            $container.html(widgetHtml);
            
            // Initialize widget
            if (typeof RecommendationWidget !== 'undefined') {
                RecommendationWidget.init(widgetId + '-inner', config);
            } else {
                console.error('RecommendationWidget module not loaded');
                $container.find('.widget-loading').html('<div class="widget-error">推荐模块未加载</div>');
            }
        });
    }
}