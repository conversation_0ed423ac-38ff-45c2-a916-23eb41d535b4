﻿
using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace Banyan.Code
{
    /// <summary>
    /// DES加密、解密帮助类
    /// </summary>
    public class DESEncrypt
    {
        private static readonly string IKey = "PANYAN==";

        /// <summary>  
        /// DES加密  
        /// </summary>  
        /// <param name="encryptString">待加密的字符串</param>  
        /// <param name="encryptKey">加密密匙，要求为8位</param>  
        /// <returns></returns>  
        public static string Encrypt(string encryptString, string encryptKey = "")
        {
            if (string.IsNullOrEmpty(encryptKey))
            {
                encryptKey = IKey;
            }
            using (DESCryptoServiceProvider des = new DESCryptoServiceProvider())
            {
                byte[] inputByteArray = Encoding.UTF8.GetBytes(encryptString);
                des.Key = UTF8Encoding.UTF8.GetBytes(encryptKey);
                des.IV = UTF8Encoding.UTF8.GetBytes(encryptKey);
                System.IO.MemoryStream ms = new System.IO.MemoryStream();
                using (CryptoStream cs = new CryptoStream(ms, des.CreateEncryptor(), CryptoStreamMode.Write))
                {
                    cs.Write(inputByteArray, 0, inputByteArray.Length);
                    cs.FlushFinalBlock();
                    cs.Close();
                }

                StringBuilder strBuilder = new StringBuilder();
                foreach (byte b in ms.ToArray())
                {
                    strBuilder.AppendFormat("{0:x2}", b);
                }

                return strBuilder.ToString();
            }
        }

        /// <summary>  
        /// DES解密字符串  
        /// </summary>  
        /// <param name="decryptString">待解密的字符串</param>  
        /// <param name="decryptKey">解密密钥,要求为8位,和加密密钥相同</param>  
        /// <returns></returns>  
        public static string Decrypt(string decryptString, string decryptKey = "")
        {
            if (string.IsNullOrEmpty(decryptKey))
            {
                decryptKey = IKey;
            }
            DESCryptoServiceProvider des = new DESCryptoServiceProvider();

            byte[] inputbytearray = new byte[decryptString.Length / 2];
            try{
                for (int x = 0; x < decryptString.Length / 2; x++)
                    {
                        int i = (Convert.ToInt32(decryptString.Substring(x * 2, 2), 16));
                        inputbytearray[x] = (byte)i;
                    }
            } catch(Exception e) {
                Logger.Warn("decrypt failed");
                Logger.Warn(decryptString);
                return "";
            }

            //建立加密对象的密钥和偏移量，此值重要，不能修改    
            des.Key = UTF8Encoding.UTF8.GetBytes(decryptKey);
            des.IV = UTF8Encoding.UTF8.GetBytes(decryptKey);
            MemoryStream ms = new MemoryStream();
            CryptoStream cs = new CryptoStream(ms, des.CreateDecryptor(), CryptoStreamMode.Write);

            cs.Write(inputbytearray, 0, inputbytearray.Length);
            cs.FlushFinalBlock();

            return Encoding.UTF8.GetString(ms.ToArray(), 0, ms.ToArray().Length);
        }
    }
}
