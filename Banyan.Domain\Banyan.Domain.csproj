﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{FA32FEF4-2AB2-4031-B78E-1C9EBD219784}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Banyan.Domain</RootNamespace>
    <AssemblyName>Banyan.Domain</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Entity.Base">
      <HintPath>..\lib\Entity.Base.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="Utility">
      <HintPath>..\lib\Utility.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Mappers\RoleDto.cs" />
    <Compile Include="Meet.cs" />
    <Compile Include="MeetAttach.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SqlModels\EmailDigestRecords.cs" />
    <Compile Include="SqlModels\EngagementRecords.cs" />
    <Compile Include="SqlModels\NewsRecommendations.cs" />
    <Compile Include="ResearchAttach.cs" />
    <Compile Include="SqlModels\AnnualReport.cs" />
    <Compile Include="SqlModels\AttachmentFMS.cs" />
    <Compile Include="SqlModels\Buyback.cs" />
    <Compile Include="SqlModels\Reports.cs" />
    <Compile Include="SqlModels\News.cs" />
    <Compile Include="SqlModels\Contribution.cs" />
    <Compile Include="SqlModels\investForIRR.cs" />
    <Compile Include="SqlModels\PortfolioBasicInfo.cs" />
    <Compile Include="SqlModels\FundBasicInfo.cs" />
    <Compile Include="SqlModels\PortfolioEquityInvestment.cs" />
    <Compile Include="SqlModels\PortfolioExit.cs" />
    <Compile Include="SqlModels\ExitScore.cs" />
    <Compile Include="SqlModels\ExitScoreStage.cs" />
    <Compile Include="SqlModels\Project_Active_Closed.cs" />
    <Compile Include="SqlModels\Fund2PortfolioSummary.cs" />
    <Compile Include="SqlModels\Meet_project_attach.cs" />
    <Compile Include="SqlModels\Project_ActiveStatus.cs" />
    <Compile Include="SqlModels\RestrictedTradingList.cs" />
    <Compile Include="SqlModels\Research.cs" />
    <Compile Include="SqlModels\Attachment.cs" />
    <Compile Include="SqlModels\ProjectMemo.cs" />
    <Compile Include="SqlModels\ProjectScoreStage.cs" />
    <Compile Include="SqlModels\Project_Active.cs" />
    <Compile Include="SqlModels\SysLog.cs" />
    <Compile Include="SqlModels\ProjectScore.cs" />
    <Compile Include="SqlModels\PraiseDetail.cs" />
    <Compile Include="SqlModels\Project.cs" />
    <Compile Include="SqlModels\Article.cs" />
    <Compile Include="SqlModels\Comment.cs" />
    <Compile Include="SqlModels\Member.cs" />
    <Compile Include="SqlModels\CollectDetail.cs" />
    <Compile Include="SqlModels\Role.cs" />
    <Compile Include="SqlModels\UserInterestTag.cs" />
    <Compile Include="SqlModels\UserProfile.cs" />
    <Compile Include="SqlModels\UserTagRelation.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>