﻿@using Banyan.Domain
@{
    ViewBag.Name = "项目贡献列表";
    Layout = "/Views/Shared/_Layout.cshtml";
    var creatorlist = (List<Banyan.Domain.Member>)ViewData["creatorList"];
    var portfoliolist = (List<Banyan.Domain.PortfolioBasicInfo>)ViewData["portfolioList"];
    var manager = ViewData["manager"] as Banyan.Domain.Member;
    var isAdmin = manager.Levels == (byte)Banyan.Domain.MemberLevels.Administrator || manager.Levels == (byte)Banyan.Domain.MemberLevels.SuperUser;
}

<style>
    td .layui-table-cell, .layui-table-cell img {
        height: 35px !important;
        line-height: 35px !important;
        padding: 0 15px;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
        box-sizing: border-box;
    }
</style>

<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li>项目贡献列表</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <div class="tab-pane active">
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="pull-right search-bar"></div>
                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                                <div class="form-group">
                                    <select class="form-control" id="creator" name="creator" size="1">
                                        <option value="">贡献人</option>
                                        @if (creatorlist != null && creatorlist.Count() > 0)
                                        {
                                            foreach (var creator in creatorlist)
                                            {
                                                <option value="@(creator.RealName)">@(creator.RealName)</option>
                                            }
                                        }
                                    </select>
                                </div>
                                <input type="hidden" id="OpenId" name="OpenId" value="@(manager.OpenId)">
                                <div class="form-group">
                                    <label class="sr-only" for="title-name">搜索</label>
                                    <input class="form-control" type="text" id="keyname" name="keyname" placeholder="搜索">
                                </div>
                                <div class="form-group">
                                    <a class="btn btn-default" id="dosearch">搜索</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <table class="layui-hide" id="table-list" lay-filter="list-filter"></table>
                <table class="layui-hide" id="staffperformance" lay-filter="list-filter"></table>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="toolbar-btn">
</script>
<script type="text/html" id="bartpl">
    <div class="btn-group" role="group" data-toggle="buttons">
        @if (isAdmin)
        {
            <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="绑定投后" lay-event="preview" data-original-title="绑定"><i class="fa fa-pencil"></i></button>
        }
    </div>
</script>
@section modal{
    <div id="modal">
        <div class="edit-modal form-horizontal" style='margin: 20px'  id="project-form" name="project-form">
            <div> 若选项中投后项目已有绑定，点击绑定将取消原有绑定并绑定至新项目 </div>
            <div class="roles" style="margin-top: 10px">
                <vselect2single v-model="model.portfolioID" name="portfolioID" label="绑定贡献至投后项目" elm="selectPortfolio">
                    @foreach (var rli in portfoliolist)
                    {
                        <option value="@(rli.portfolioID)">@(rli.Name)  @(rli.abbName) 
                        @if (rli.contributionProjectID != 0)
                        {
                            <span> 已有绑定</span>
                        }
                    </option>
                    }
                </vselect2single>


            </div>
            <div class="form-group" style="margin-top: 230px">
                <div class="col-md-8 col-md-offset-4">
                    <button class="btn btn-warn" type="button" @@click="cancel()" style="margin-right: 25px"><i class="fa fa-remove push-5-r"></i>取消</button>
                    <button class="btn btn-info" @@click="portfolioBind()"><i class="fa fa-save push-5-r"></i>绑定</button>
                </div>
            </div>
        </div>
    </div>
}

@section scripts{
    <link href="/content/js/plugins/select2/select2.min.css" rel="stylesheet" />
    <link href="/content/js/plugins/select2/select2-bootstrap.min.css" rel="stylesheet" />
    <script src="~/Content/js/form-component.js"></script>
    <script type="text/javascript" src="/content/js/plugins/select2/select2.min.js"></script>
    <script type="text/javascript">
        function GetStringDate(dateStr) {
            if (!dateStr) return ''
            var date = eval('new ' + dateStr.substr(1, dateStr.length - 2));
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            var res = year + "-" + month + "-" + day;
            if (res == "1-1-1") return "";
            return res;
        }

      

        var queryParams = {}, date = new Date();
        layui.use(['laypage', 'layer', 'table'], function () {
            var laypage = layui.laypage,
                layer = layui.layer,
                table = layui.table;

            app = new Vue({
                el: '#modal',
                data: {
                    model: { portfolioID: "", contributionProjectID: "" },
                },
                methods: {
                    cancel: function () {
                        layer.closeAll();
                    },
                    portfolioBind: function (e) {
                        var that = this;
                        $.ajax({
                            type: 'POST',
                            url: "/adminapi/PortfolioBindProject",
                            data: that.model,
                            success: function (data) {
                                if (data.code == 0) {
                                    layer.msg('操作成功！', {
                                        time: 1000
                                    }, function () {
                                        history.go(0); // 已绑定需要重刷页面
                                        //table.reload("table-list");
                                        //layer.closeAll();
                                    });
                                } else {
                                    layer.msg('操作失败');
                                }
                            },
                            error: function () {
                                layui.layer.msg("很抱歉，请求异常！");
                            }
                        });
                    },
                    created: function () {
                    }

                }
            })

            table.render({
                elem: '#table-list'
                , height: 570
                , defaultToolbar: ['filter']
                , toolbar: '#toolbar-btn'
                , url: '@(Url.Action("ProjectContributionList", "adminapi"))'
                , page: {limit: 50, limits: [50]}
                , method: 'post'
                , cols: [[
                    { field: 'Name', title: '对应投后项目通称', fixed: 'left', width: 180 },
                    { field: 'projectName', title: '项目名', fixed: 'left', width: 180 },
                    { field: 'projectID', title: '项目ID',  width: 90 },
                    { field: 'percentage', title: '百分比',  width: 100 },
                    { field: 'username', title: '贡献人', width: 75 },
                    { field: 'description', title: '贡献描述', width: 200 },
                    { field: 'creator', title: '创建人', width: 75 },
                    {
                        field: 'createdDate', title: '添加时间', sort: true, width: 110, templet: function (d) {
                            return GetStringDate(d.createdDate)
                        }
                    },
                    { fixed: 'right', title: "绑定投后项目", width: 120, align: 'center', toolbar: '#bartpl' }
                ]],
                done: function () { }
            });
            laypage.render({
                elem: 'pageBar'
                , count: 100
                , jump: function (obj, first) {
                    if (!first) {
                        layer.msg('第' + obj.curr + '页');
                    }
                }
            });
            $('#dosearch').on('click', function () {
                queryParams = {
                    creator: $('#creator').val(),
                    keyname: $('#keyname').val(),
                }
                table.reload('table-list', {
                    where: queryParams,
                });
            });

            table.on('tool(list-filter)', function (obj) {
                var data = obj.data
                    , layEvent = obj.event;

                app.model.contributionProjectID = data.projectID
                app.model.portfolioID = ""

                if (layEvent === 'preview') {
                    var index = layer.open({
                        type: 1,
                        area: ['540px', '450px'],
                        maxmin: true,
                        title: "选择投后项目与 " + data.projectName + " 绑定",
                        content: $('#modal') //这里content是一个DOM，注意：最好该元素要存放在body最外层，否则可能被其它的相对元素所影响
                    });
                }
                return;
            });
        });







    </script>
}
