using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public partial class Reports
    {
        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        [SqlField]
        public string Title { get; set; }
        public string Subject { get; set; }
        [SqlField]
        public string Content { get; set; }

        [SqlField]
        public DateTime PubTime { get; set; } = DateTime.Now;

        [SqlField]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        [SqlField]
        public string Url { get; set; }

        [SqlField]
        public string Classify { get; set; }
        [SqlField]
        public string Attach_Url { get; set; }

        [SqlField]
        public string sRatingName { get; set; }

        [SqlField]
        public string OrgName { get; set; }

        [SqlField]
        public string Industry { get; set; }
        [SqlField]
        public string Researcher { get; set; }
        [SqlField]
        public string ShortName { get; set; }
    }

}
