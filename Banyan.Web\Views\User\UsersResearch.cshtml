﻿@{
    Layout = "/Views/Shared/_LayoutAdminWithResearch.cshtml";
    var roleList = (List<Banyan.Domain.Role>)ViewData["rolelist"];
    var manager = ViewData["manager"] as Banyan.Domain.Member;
}

<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li>
                    <i class="si si-pointer"></i>
                </li>
                <li>用户管理</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <input type="hidden" id="super" value="@(manager == null ? "false" : (manager.Levels == (byte)Banyan.Domain.MemberLevels.Administrator).ToString())" />
            <div class="tab-pane active">
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="pull-right search-bar"></div>
                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                                @*<div class="form-group">
                                    <select class="form-control" id="role" name="role" size="1">
                                        <option value="">全部</option>
                                        @if (roleList != null && roleList.Count() > 0)
                                        {
                                            foreach (var rli in roleList)
                                            {
                                                <option value="@(rli.Id)">@(rli.RoleName)</option>}
                                        }
                                    </select>
                                </div>*@
                                <div class="form-group">
                                    <label class="sr-only" for="title-name">用户名称</label>
                                    <input class="form-control" type="text" id="uname" name="uname" placeholder="请输入用户名称">
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-default" id="dosearch">搜索</button>
                                    @*<div class="btn" href="void(0)" target="_blank" title="用户注册后进入underReview组等待审核，审批后需从该组删除；根据权限将用户加入相应系统子分组；leave组为禁用">使用说明</div>
                                    <a class="btn btn-primary" href="https://ldap.gaorongvc.cn" target="_blank" title="cn=姓名,ou=users,dc=gaorongvc,dc=cn">编辑用户</a>*@
                                    @*<a class="btn btn-primary" href="javascript:;" onclick="parent.openmodal('新建用户','@(Url.Action("UserSet","User"))','400px','570px')"><i class="fa fa-plus"></i>&nbsp;&nbsp;新建用户</a>*@
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <table class="layui-hide" id="table-list" lay-filter="list-filter"></table>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="bartpl">
    {{#  if(d.Levels != 1){ }}
    @*<button class="btn btn-xs btn-default {{d.Status == 3 ? '':'hidden'}}" style="display:none;" type="button" title="审核" lay-event="verify"><i class="fa fa-sign-in"></i></button>*@
    <button class="btn btn-xs {{d.Status == 2 ? 'btn-warning' : 'btn-default'}}" style="display:none;" type="button" title="{{d.Status == 2 ? '启用' : '禁用'}}" lay-event="enable"><i class="fa fa-toggle-off"></i></button>
    <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="编辑" data-original-title="编辑" lay-event="modify"><i class="fa fa-pencil"></i></button>
    @*<button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="删除" data-original-title="删除" lay-event="delete"><i class="fa fa-times"></i></button>*@
    {{#  } }}
</script>

@section scripts{
    <script type="text/javascript">
        function getParameter(el) {
            var obj = {};
            $(el).each(function (index, item) {
                // 判断元素的类型
                if (item.type == "text" || item.type == "password" || item.type == "select-one" || item.type == "tel" ||
                    item.type == "search" || item.type == "range" || item.type == "number" || item.type == "month" ||
                    item.type == "email" || item.type == "datetime-local" || item.type == "datetime" || item.type == "date" ||
                    item.type == "color") {
                    //获取到name的值,name的值就是向后台传递的数据
                    obj[$(this).attr("name")] = $(this).val();
                } else if (item.type == "checkbox") {
                    var stamp = false;
                    if ($(this).attr("name") && !stamp) {
                        stamp = false;
                        // 获取到复选框选中的元素
                        var checkboxEl = $("input[name='" + $(item).attr('name') + "']:checked");
                        if (checkboxEl) {
                            var checkboxArr = [];
                            // 取出复选框选中的值
                            checkboxEl.each(function (idx, itm) {
                                checkboxArr.push($(itm).val());
                            });
                            obj[$(this).attr("name")] = checkboxArr.join(",");
                        }

                    }
                } else if (item.type == "radio") {
                    // 获取到单选框选中的值
                    var radio_val = $("input[name=" + $(item).attr('name') + "]:checked").val();
                    if (radio_val) {
                        obj[$(item).attr("name")] = radio_val;
                    }
                }
            });
            return obj;
        }
        saveUser = function (e) {
            // document.forms[1].elements
            // 通过description 去修改组员
            var parameter = getParameter(".query");
            console.log(parameter);
            if (parameter["cn=unserReview"] == "on" && (parameter["ims-user"] != "待审核" || parameter["research-user"] != "待审核")) {
                return alert("请取消待审核状态")
            }
            if (parameter["cn=unserReview"] == "" && (parameter["ims-user"] == "待审核" || parameter["research-user"] == "待审核")) {
                return alert("请审核所有权限")
            }
            if (!currentUser) {
                return alert("用户未选择错误")
            }
            res = {}
            for (key in parameter) {
                if (parameter[key] && parameter[key] != "待审核" && parameter[key] != "normal") {
                    res[key] = parameter[key]
                }
            }
            $.ajax({
                type: 'POST',
                url: '@(Url.Action("usersavegroup", "adminapi"))',
                data: { name: currentUser, groups: JSON.stringify(res), isResearch: 1 },
                success: function (data) {
                    if (data) {
                        layer.msg('操作成功！');
                        window.location.reload();
                    } else {
                        layer.msg('操作失败');
                    }
                },
                error: function () {
                    layui.layer.msg("很抱歉，请求异常！");
                }
            });

        }
        currentUser = ''
        var layer, date = new Date();
        layui.use(['laypage', 'layer', 'table'], function () {
            var laypage = layui.laypage,
                table = layui.table;
            layer = layui.layer

            table.render({
                id: 'table-list',
                elem: '#table-list'
                , height: 700
                , url: '@(Url.Action("userlist", "adminapi"))'
                , where: {
                    filterleave: "on"
                } 
                , page: {limit: 200, limits:[200]}
                , method: 'post'
                , cols: [[
                    //{ field: 'Id', title: 'ID', width: 80, sort: true, fixed: 'left' }
                     { field: 'RealName', title: '姓名', width: 100, fixed: 'left', sort: true }
                    , {
                        field: 'RoleName', title: 'Research权限', sort: true, templet: function (d) {
                            var label = '';
                            if (d.StatusResearch == 14) {
                                label += '<span class="label label-danger">审核不通过</span>';
                            }
 

                            if (d.Status == 1 && d.StatusResearch != 14 && d.LevelResearch == 10) {
                                label += '<span class="label label-info">普通用户</span>';
                            } else if (d.LevelResearch == 11) {
                                label += '<span class="label label-success">普通编辑</span>';
                            } else if (d.LevelResearch == 12) {
                                label += '<span class="label label-success">编辑管理员</span>';
                            } else if (d.LevelResearch == 13) {
                                label += '<span class="label label-info">投资团队成员</span>';
                            } else if (d.LevelResearch == 14) {
                                label += '<span class="label label-danger">合伙人</span>';
                            } else if (d.LevelResearch == 15) {
                                label += '<span class="label label-danger">超级管理员</span>';
                            } else if (d.LevelResearch == 16) {
                                label += '<span class="label label-warning">受限实习生</span>';
                            }

                            return label;
                        }
                    }


                    //, {
                    //    field: 'AddTime', title: '创建时间', width: 160, templet: function (d) {
                    //        return (new Date(parseInt(d.AddTime.replace("/Date(", "").replace(")/", "").split("+")[0]))).pattern("yyyy-MM-dd HH:mm:ss");
                    //    }}
                    , {
                        field: 'Status', title: '用户状态', width: 110, sort: true,  templet: function (d) {
                            var statusHtml = '';
                            if (d.Levels == 1)
                                return statusHtml;

                            //if (d.Status == 1) {
                            //    statusHtml = '<span class="label label-success">已通过</span>';
                            //} else
                            if (d.Status == 2) {
                                statusHtml = '<span class="label label-danger">已禁用</span>';
                            } else if (d.Status == 3) {
                                statusHtml = '<span class="label label label-warning">待审核</span>';
                            }
                            return statusHtml;
                        }
                    }
                    , { field: 'select', title: '编辑选中行', width: 100,  type: "radio" }
                    //, { fixed: 'right', title: '操作', width: 160, align: 'center', toolbar: '#bartpl' }
                ]],
                done: function () {
                    if ($('#super').val().toLowerCase() == 'true') {
                        $('[lay-event="verify"]').show();
                        $('[lay-event="enable"]').show();
                    }
                }
            });
            table.on('radio(list-filter)', function (obj) {

                console.log(obj.checked); //当前是否选中状态
                console.log(obj.data); //选中行的相关数据
                var user = obj.data
                currentUser = user.RealName
                console.log(encodeURI(JSON.stringify(obj.data)));
                //parent.openmodal('编辑用户', '@(Url.Action("userset", "user"))?data=' + encodeURI(JSON.stringify(obj.data)), '400px', '570px')
                var str = `<form class="edit-modal" style='margin: 20px'>
                       <div>` 
                str += `<div style="margin-top: 20px"> Research系统：
                          <select  class="query" name="research-user">
                            <option ${user.Status == 3 && "selected"}>待审核</option>
                            <option value="cn=normal,ou=research" ${user.Status != 3 && user.StatusResearch != 14 && user.LevelResearch == 10 && "selected"}>普通用户</option>
                            <option value="cn=editor,ou=research" ${user.Status != 3 && user.StatusResearch != 14 && user.LevelResearch == 11 && "selected"}>普通编辑</option>
                            <option value="cn=editorAdmin,ou=research" ${user.Status != 3 && user.StatusResearch != 14 && user.LevelResearch == 12 && "selected"}>编辑管理员</option>
                            <option value="cn=investment,ou=research" ${user.Status != 3 && user.StatusResearch != 14 && user.LevelResearch == 13 && "selected"}>投资团队成员</option>
                            <option value="cn=partner,ou=research" ${user.Status != 3 && user.StatusResearch != 14 && user.LevelResearch == 14 && "selected"}>合伙人</option>
                            <option value="cn=superAdmin,ou=research" ${user.Status != 3 && user.StatusResearch != 14 && user.LevelResearch == 15 && "selected"}>超级管理员</option>
                            <option value="cn=limitedIntern,ou=research" ${user.Status != 3 && user.StatusResearch != 14 && user.LevelResearch == 16 && "selected"}>受限实习生</option>
                            <option value="cn=reviewDenied,ou=research" ${user.Status != 3 && user.StatusResearch == 14 && "selected"}>Research审核不通过</option>
                       </select></div>`
                str += `<div class="layui-input-block" style="margin-top: 200px">
                        <button type="button" class="layui-btn" onclick="saveUser()">保 存</button>
                    </div>`
                str += "</form>"
                layui.layer.open({
                    type: 1,
                    content: str,
                    title: `编辑用户权限：${obj.data.RealName}`,
                    area: ['350px', '370px']
                })
            });
            function genGroup(name,value, checked) {
                return `<div><input type="checkbox" class="query" name=${value} id=${name} ${checked && "checked"}></input>
                          <label for=${name} style="margin-right: 10px">${name}</label></div>`
            }


            table.on('tool(list-filter)', function (obj) {
                var data = obj.data
                    , layEvent = obj.event;

                if (layEvent === 'enable') {
                    fieldset(data.Id, 'enable', data.Status);
                } else if (layEvent === 'modify') {
                    parent.openmodal('编辑用户', '@(Url.Action("userset", "user"))?id=' + data.Id, '400px', '570px')
                }
                return;
            });

            laypage.render({
                elem: 'pageBar'
                , count: 100
                , skin: '#208aee'
                , jump: function (obj, first) {
                    if (!first) {
                        layer.msg('第' + obj.curr + '页');
                    }
                }
            });

            $('#dosearch').on('click', function () {
                queryParams = {
                    role: $('#role').val(),
                    title: $('#uname').val(),
                    filterleave: "on"
                }
                table.reload('table-list', {
                    where: queryParams,
                });
            });

            function fieldset(id, field, state) {
            $.ajax({
                    type: 'POST',
                    url: '@(Url.Action("memberset", "adminapi"))',
                    data: {id: id, field: field, state: state },
                    success: function (data) {
                        if (data.code == 0) {
                            layer.msg('操作成功！');
                            $('#dosearch').click();
                        } else {
                            layer.msg(data.msg);
                        }
                    },
                    error: function () {
                        layui.layer.msg("很抱歉，请求异常！");
                    }
                });
            }
        });
    </script>
}
