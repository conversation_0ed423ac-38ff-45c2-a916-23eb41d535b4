using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using Banyan.Apps;
using Banyan.Code;
using Banyan.Domain;

namespace Banyan.Web.Controllers
{
    /// <summary>
    /// Controller for engagement analytics and reporting
    /// </summary>
    public class EngagementAnalyticsController : Controller
    {
        private readonly EngagementAnalytics _engagementAnalytics;
        private readonly EngagementTracker _engagementTracker;

        /// <summary>
        /// Constructor
        /// </summary>
        public EngagementAnalyticsController()
        {
            _engagementAnalytics = new EngagementAnalytics();
            _engagementTracker = new EngagementTracker();
        }

        /// <summary>
        /// Dashboard view
        /// </summary>
        /// <param name="startDate">Start date for analytics</param>
        /// <param name="endDate">End date for analytics</param>
        /// <returns>Dashboard view</returns>
        public async Task<ActionResult> Index(DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                // Default to last 30 days if no dates provided
                DateTime start = startDate ?? DateTime.Now.AddDays(-30);
                DateTime end = endDate ?? DateTime.Now;

                // Get engagement summary
                var summary = await _engagementAnalytics.GetEngagementSummaryAsync(start, end);

                // Get detailed metrics
                var detailedMetrics = await _engagementAnalytics.GetDetailedEngagementMetricsAsync(start, end);

                // Get effectiveness metrics
                var effectivenessMetrics = await _engagementAnalytics.GetEngagementEffectivenessAsync(start, end);

                // Prepare view model
                var viewModel = new EngagementAnalyticsDashboardViewModel
                {
                    StartDate = start,
                    EndDate = end,
                    Summary = summary,
                    DetailedMetrics = detailedMetrics,
                    EffectivenessMetrics = effectivenessMetrics
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in EngagementAnalytics Index: {ex.Message}", ex);
                ViewBag.ErrorMessage = "An error occurred while loading the engagement analytics dashboard.";
                return View(new EngagementAnalyticsDashboardViewModel());
            }
        }

        /// <summary>
        /// Detailed engagement records view
        /// </summary>
        /// <param name="startDate">Start date for records</param>
        /// <param name="endDate">End date for records</param>
        /// <param name="userName">Optional user name filter</param>
        /// <param name="page">Page number</param>
        /// <returns>Records view</returns>
        public async Task<ActionResult> Records(DateTime? startDate = null, DateTime? endDate = null, string userName = null, int page = 1)
        {
            try
            {
                // Default to last 30 days if no dates provided
                DateTime start = startDate ?? DateTime.Now.AddDays(-30);
                DateTime end = endDate ?? DateTime.Now;

                // Page size
                const int pageSize = 50;

                // Get engagement records
                List<EngagementRecords> records;
                if (!string.IsNullOrEmpty(userName))
                {
                    // Filter by user name
                    records = await _engagementAnalytics.GetUserEngagementHistoryAsync(userName, pageSize * page, start, end);
                    // Take only the current page
                    records = records.Skip((page - 1) * pageSize).Take(pageSize).ToList();
                }
                else
                {
                    // Get all records for the date range
                    records = await _engagementAnalytics.GetAllEngagementRecordsAsync(start, end, pageSize * page);
                    // Take only the current page
                    records = records.Skip((page - 1) * pageSize).Take(pageSize).ToList();
                }

                // Prepare view model
                var viewModel = new EngagementRecordsViewModel
                {
                    StartDate = start,
                    EndDate = end,
                    UserName = userName,
                    Records = records,
                    CurrentPage = page,
                    PageSize = pageSize
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in EngagementAnalytics Records: {ex.Message}", ex);
                ViewBag.ErrorMessage = "An error occurred while loading the engagement records.";
                return View(new EngagementRecordsViewModel());
            }
        }

        /// <summary>
        /// User engagement details view
        /// </summary>
        /// <param name="userName">User name</param>
        /// <param name="startDate">Start date for analytics</param>
        /// <param name="endDate">End date for analytics</param>
        /// <returns>User details view</returns>
        public async Task<ActionResult> UserDetails(string userName, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                if (string.IsNullOrEmpty(userName))
                {
                    return RedirectToAction("Index");
                }

                // Default to last 30 days if no dates provided
                DateTime start = startDate ?? DateTime.Now.AddDays(-30);
                DateTime end = endDate ?? DateTime.Now;

                // Get user engagement history
                var records = await _engagementAnalytics.GetUserEngagementHistoryAsync(userName, 100, start, end);

                // Prepare view model
                var viewModel = new UserEngagementDetailsViewModel
                {
                    UserName = userName,
                    StartDate = start,
                    EndDate = end,
                    Records = records,
                    TotalEngagements = records.Count,
                    WebEngagements = records.Count(r => r.Source.Equals("web", StringComparison.OrdinalIgnoreCase)),
                    EmailEngagements = records.Count(r => r.Source.Equals("email", StringComparison.OrdinalIgnoreCase))
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in EngagementAnalytics UserDetails: {ex.Message}", ex);
                ViewBag.ErrorMessage = "An error occurred while loading the user engagement details.";
                return View(new UserEngagementDetailsViewModel { UserName = userName });
            }
        }

        /// <summary>
        /// Export engagement data to CSV
        /// </summary>
        /// <param name="startDate">Start date for export</param>
        /// <param name="endDate">End date for export</param>
        /// <returns>CSV file</returns>
        public async Task<ActionResult> ExportCsv(DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                // Default to last 30 days if no dates provided
                DateTime start = startDate ?? DateTime.Now.AddDays(-30);
                DateTime end = endDate ?? DateTime.Now;

                // Get CSV data
                string csv = await _engagementAnalytics.ExportEngagementDataToCsvAsync(start, end);

                // Return as file
                string fileName = $"engagement_data_{start:yyyy-MM-dd}_to_{end:yyyy-MM-dd}.csv";
                return File(new System.Text.UTF8Encoding().GetBytes(csv), "text/csv", fileName);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in EngagementAnalytics ExportCsv: {ex.Message}", ex);
                ViewBag.ErrorMessage = "An error occurred while exporting the engagement data.";
                return RedirectToAction("Index");
            }
        }
    }

    /// <summary>
    /// View model for the engagement analytics dashboard
    /// </summary>
    public class EngagementAnalyticsDashboardViewModel
    {
        /// <summary>
        /// Start date for analytics
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// End date for analytics
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// Engagement summary
        /// </summary>
        public EngagementStatistics Summary { get; set; }

        /// <summary>
        /// Detailed engagement metrics
        /// </summary>
        public EngagementDetailedMetrics DetailedMetrics { get; set; }

        /// <summary>
        /// Engagement effectiveness metrics
        /// </summary>
        public EngagementEffectivenessMetrics EffectivenessMetrics { get; set; }
    }

    /// <summary>
    /// View model for engagement records
    /// </summary>
    public class EngagementRecordsViewModel
    {
        /// <summary>
        /// Start date for records
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// End date for records
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// User name filter
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// Engagement records
        /// </summary>
        public List<EngagementRecords> Records { get; set; } = new List<EngagementRecords>();

        /// <summary>
        /// Current page number
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        public int PageSize { get; set; }
    }

    /// <summary>
    /// View model for user engagement details
    /// </summary>
    public class UserEngagementDetailsViewModel
    {
        /// <summary>
        /// User name
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// Start date for analytics
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// End date for analytics
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// Engagement records
        /// </summary>
        public List<EngagementRecords> Records { get; set; } = new List<EngagementRecords>();

        /// <summary>
        /// Total number of engagements
        /// </summary>
        public int TotalEngagements { get; set; }

        /// <summary>
        /// Number of web engagements
        /// </summary>
        public int WebEngagements { get; set; }

        /// <summary>
        /// Number of email engagements
        /// </summary>
        public int EmailEngagements { get; set; }
    }
}