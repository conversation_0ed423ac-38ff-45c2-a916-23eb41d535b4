@model Banyan.Web.Controllers.UserEngagementDetailsViewModel
@{
    ViewBag.Title = "User Engagement Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <h2>User Engagement Details: @Model.UserName</h2>
    
    @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
    {
        <div class="alert alert-danger">
            @ViewBag.ErrorMessage
        </div>
    }
    
    <!-- Date Range Filter -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Date Range Filter</h5>
        </div>
        <div class="card-body">
            <form method="get" action="@Url.Action("UserDetails")" class="form-inline">
                <input type="hidden" name="userName" value="@Model.UserName" />
                <div class="form-group mr-2">
                    <label for="startDate" class="mr-2">Start Date:</label>
                    <input type="date" id="startDate" name="startDate" class="form-control" value="@Model.StartDate.ToString("yyyy-MM-dd")" />
                </div>
                <div class="form-group mr-2">
                    <label for="endDate" class="mr-2">End Date:</label>
                    <input type="date" id="endDate" name="endDate" class="form-control" value="@Model.EndDate.ToString("yyyy-MM-dd")" />
                </div>
                <button type="submit" class="btn btn-primary">Apply Filter</button>
            </form>
        </div>
    </div>
    
    <!-- User Engagement Summary -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Engagement Summary</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="card bg-primary text-white mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Total Engagements</h5>
                            <h2 class="card-text">@Model.TotalEngagements</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Web Engagements</h5>
                            <h2 class="card-text">@Model.WebEngagements</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Email Engagements</h5>
                            <h2 class="card-text">@Model.EmailEngagements</h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Engagement Distribution Chart -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Engagement Source Distribution</h5>
        </div>
        <div class="card-body">
            <canvas id="sourceDistributionChart" height="200"></canvas>
        </div>
    </div>
    
    <!-- User Engagement Records -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Engagement Records</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>News ID</th>
                            <th>News Title</th>
                            <th>Timestamp</th>
                            <th>Source</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.Records != null && Model.Records.Any())
                        {
                            foreach (var record in Model.Records)
                            {
                                <tr>
                                    <td>@record.Id</td>
                                    <td>@record.NewsId</td>
                                    <td>@record.NewsTitle</td>
                                    <td>@record.Timestamp.ToString("yyyy-MM-dd HH:mm:ss")</td>
                                    <td>
                                        @if (record.Source.Equals("web", StringComparison.OrdinalIgnoreCase))
                                        {
                                            <span class="badge badge-primary">Web</span>
                                        }
                                        else if (record.Source.Equals("email", StringComparison.OrdinalIgnoreCase))
                                        {
                                            <span class="badge badge-success">Email</span>
                                        }
                                        else
                                        {
                                            <span class="badge badge-secondary">@record.Source</span>
                                        }
                                    </td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="5" class="text-center">No records found</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Navigation Buttons -->
    <div class="mb-4">
        <a href="@Url.Action("Records", new { startDate = Model.StartDate.ToString("yyyy-MM-dd"), endDate = Model.EndDate.ToString("yyyy-MM-dd") })" class="btn btn-secondary">
            <i class="fa fa-list"></i> All Records
        </a>
        <a href="@Url.Action("Index", new { startDate = Model.StartDate.ToString("yyyy-MM-dd"), endDate = Model.EndDate.ToString("yyyy-MM-dd") })" class="btn btn-primary">
            <i class="fa fa-chart-bar"></i> Back to Dashboard
        </a>
    </div>
</div>

@section scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function () {
            // Source Distribution Chart
            var sourceDistributionCtx = document.getElementById('sourceDistributionChart').getContext('2d');
            var sourceDistributionChart = new Chart(sourceDistributionCtx, {
                type: 'pie',
                data: {
                    labels: ['Web', 'Email'],
                    datasets: [{
                        data: [@Model.WebEngagements, @Model.EmailEngagements],
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(75, 192, 192, 0.7)'
                        ]
                    }]
                },
                options: {
                    responsive: true
                }
            });
        });
    </script>
}