﻿@using Banyan.Domain
@{
    Layout = "/Views/Shared/_Layout.cshtml";
    List<Banyan.Domain.Role> classList = (List<Banyan.Domain.Role>)ViewData["rolelist"];
    List<Banyan.Domain.Member> memberList = (List<Banyan.Domain.Member>)ViewData["creatorList"];
    List<Banyan.Domain.Member> staffList = (List<Banyan.Domain.Member>)ViewData["staffList"];
    int id = (int)ViewData["projectId"];
    var pingLLM = (bool)ViewData["pingLLM"];
    bool isProjectManager = (bool)ViewData["isProjectManager"] && id != 0;
    var member = ViewData["manager"] as Banyan.Domain.Member;
    var isOperate = member.Levels == (byte)Banyan.Domain.MemberLevels.Administrator || member.Levels == (byte)Banyan.Domain.MemberLevels.SuperUser;
}
<link href="~/content/css/views/meet/meet-edit.css" rel="stylesheet" />
<style>
    #ai_block ul {
        display: inline-block;
        white-space: normal; /* 禁止换行 */
    }
    #ai_block hr {
        margin: 0;
    }
    .block-content #ai_reasoning_block p {
        margin-bottom: 0px;
    }
</style>
<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li><a href="@(Url.Action("Index", "Index"))">项目约见管理</a></li>
                <li> @((int)ViewData["projectId"] == 0 ? "新建" : "编辑")项目约见</li>
            </ol>
        </div>
        <div class="block-content block-content-full" style="display:none;" id="page-app">
            <form class="form-horizontal" method="post" id="project-form" name="project-form">
                <input type="hidden" name="id" id="id" v-model="model.Id" />
                <input type="hidden" name="Creator" value="@(member.Id)" />
                <input type="hidden" name="viewcount" v-model="model.ViewCount" />
                <input type="hidden" name="collectecount" v-model="model.CollectCount" />
                <input type="hidden" name="commentcount" v-model="model.CommentCount" />
                <input type="hidden" name="status" id="status" v-model="model.Status" />
                <input type="hidden" name="coverurl" id="coverurl" v-model="model.CoverUrl" />

                <div class="form-group">
                    <label class="col-md-2 control-label"></label>
                    <div class="col-md-6">
                        <input type="file" name="imgFile" id="import" style="display:none;" onchange="projectImport('import')" />

                        <a class="btn btn-default" id="importbtn"><i class="fa fa-upload"></i> 导入项目</a>
                        <a class="btn btn-default" title="2024/05/24 增加创始人姓名字段" href="~/Content/project_template.docx">下载项目模板v1.3(2024/05/24)</a>
                    </div>
                </div>
                @*@if (pingLLM)
                    {
                        <div class="form-group" v-if="id == 0">
                            <label class="col-md-2 control-label" for="ai-extract">智能填写(Beta)</label>
                            <div class="col-md-6">
                                <textarea class="form-control" v-model="notes" id="ai-extract" name="ai-extract" rows="3" placeholder="智能识别，一键填写，粘贴后点击外部区域开始" @@change="AIExtract()"></textarea>
                            </div>
                        </div>
                    }*@
                <div class="form-group">
                    <label class="col-md-2 control-label need-badge" for="title">项目名称</label>
                    <div class="col-md-6">
                        <input class="form-control" type="text" id="Name" name="Name" data-rule="required;" v-model="model.Name" placeholder="项目名称(revisit需输入同名项目,回车或点其他区域弹窗后确认关联,可查看原项目或其负责人)" @@change="projectSimilarNameCheck(model.Name)">
                    </div>
                </div>
                <div v-if="revisitName" class="form-group">
                    <label class="col-md-2 control-label need-badge" for="title">原项目</label>
                    <div class="col-md-6" style="line-height: 32px">
                        <a class="link" v-on:click="preview">{{revisitName}}</a>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="founder">项目创始人</label>
                    <div class="col-md-2">
                        <div id="posit">
                            <input class="form-control" id="founder" name="founder" rows="3" placeholder="创始人姓名" v-model="model.Founder" @@change="founderSimilarCheck(model.Founder)">
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label need-badge" for="ToRoleId">项目组</label>
                    <div class="col-md-6">
                        <select class="form-control" id="group" size="1" data-rule="required;" v-model="model.ToRoleId">
                            @if (member != null)
                            {
                                string[] groups = (string.IsNullOrEmpty(member.Groups) ? "" : member.Groups).Split(',');
                                foreach (var ci in classList)
                                {
                                    if (@ci.RoleName == "互联网组")
                                    {
                                        continue;
                                    }
                                    if (isOperate)
                                    {
                                        <option value="@(ci.Id)">@(ci.RoleName)</option>
                                    }
                                    else
                                    {
                                        if (ci.Id > 0 && groups.Contains(ci.Id.ToString()))
                                        {
                                            <option value="@(ci.Id)">@(ci.RoleName) </option>
                                        }
                                    }
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label need-badge" for="ProjectManager">项目负责人</label>
                    <div class="col-md-6">
                        <select class="form-control select2" id="manager" size="1">
                            <option value="" selected="selected">未选择</option>
                            @foreach (var mi in memberList)
                            {
                                <option value="@(mi.RealName)">@(mi.RealName)</option>
                            }
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="groupMember">其他项目组员</label>
                    <div class="col-md-6">
                        <select class="form-control select2" id="groupMember" multiple="multiple" size="1">
                            @foreach (var mi in memberList)
                            {
                                <option value="@(mi.RealName)">@(mi.RealName)</option>
                            }
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label need-badge" for="Introducer">项目来源</label>
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-xs-6" style="width:50%;float:left">
                                <select class="form-control" id="project-source" v-model="model.Source" size="1">
                                    @*<option :selected="model.Source == '0'" value="0">同事介绍</option>
                                        <option :selected="model.Source == '1'" value="1">FA</option>
                                        <option :selected="model.Source == '2'" value="2">覆盖</option>
                                        <option :selected="model.Source == '3'" value="3">朋友推荐</option>
                                        <option :selected="model.Source == '4'" value="4">主动上门</option>
                                        <option :selected="model.Source == '5'" value="5">老项目</option>*@

                                    <option :selected="model.Source == '0'" value="0">项目负责人人脉</option>
                                    <option :selected="model.Source == '4'" value="4">项目负责人研究</option>
                                    <option :selected="model.Source == '1'" value="1">同事提及</option>
                                    <option :selected="model.Source == '2'" value="2">同事介绍</option>
                                    <option :selected="model.Source == '3'" value="3">FA介绍</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-xs-6" style="width: 100%;">
                                @*<div v-show="model.Source == '0'">
                                        <select class="form-control select2" id="source2" size="1">
                                            <option value="人脉">人脉</option>
                                            <option value="研究">研究</option>
                                        </select>
                                    </div>*@

                                <div v-show="model.Source == '1' || model.Source =='2' " style="height: 32px; margin-top: 15px">
                                    <div style="width:45%;float:left">
                                        <div style="width: 45%; float: left">
                                            <label v-if="model.Source=='2'" style="vertical-align: sub">介绍人姓名</label>
                                            <label v-if="model.Source=='1'" style="vertical-align: sub">提及人姓名</label>
                                        </div>
                                        <div style="width:45%;float:right">
                                            <select class="form-control select2" id="source" multiple="multiple" size="1">
                                                @foreach (var mi in staffList)
                                                {
                                                    <option value="@(mi.RealName)">@(mi.RealName)</option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div v-show="model.Source == '1'" style="width:45%;float:right">
                                        <div style="width:45%;float:left"><label style="vertical-align:sub">联系方式提供人</label></div>
                                        <div style="width:45%;float:right">
                                            <select style="width:25%;float:right" class="form-control select2" id="finder" multiple="multiple" size="1">
                                                @foreach (var mi in staffList)
                                                {
                                                    <option value="@(mi.RealName)">@(mi.RealName)</option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div v-show="model.Source == '3'" style="height: 32px; margin-top: 15px">
                                    <div style="width:40%;float:left">
                                        <input class="form-control" type="text" id="Introducer" name="Introducer"
                                               rows="3" placeholder="FA名称" v-model="model.Introducer">
                                    </div>
                                    <div style="width:45%;float:right">
                                        <div style="width:45%;float:left"><label style="vertical-align:sub">FA推荐同事</label></div>
                                        <div style="width:45%;float:right">
                                            <select style="width:25%;float:right" class="form-control select2" id="finder2" multiple="multiple" size="1">
                                                @foreach (var mi in staffList)
                                                {
                                                    if (member.RealName == mi.RealName)
                                                    {
                                                        <option value="@(mi.RealName)" selected="selected">@(mi.RealName)</option>
                                                    }
                                                    else
                                                    {
                                                        <option value="@(mi.RealName)">@(mi.RealName)</option>
                                                    }

                                                }
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div style="margin-top:15px; height: 32px">
                                    <input class="form-control" type="text" id="contribution-detail" name="Introducer"
                                           rows="3" placeholder="其他形式的同事贡献详情（如有）" v-model="model.ContributionDetail">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label need-badge" for="nextStepStatus">项目状态</label>
                    <div class="col-md-6">
                        <select class="form-control" data-rule="required;" v-model="model.nextStepStatus" ssize="1">
                            <option value="">-项目状态-</option>
                            <option :selected="'已投项目新一轮' == model.nextStepStatus" value="已投项目新一轮">已投项目新一轮</option>
                            <option :selected="'TS已签署' == model.nextStepStatus" value="TS已签署">TS已签署</option>
                            <option :selected="'Pre-DD' == model.nextStepStatus" value="Pre-DD">Pre-DD</option>
                            <option :selected="'安排合伙人见面' == model.nextStepStatus" value="安排合伙人见面">安排合伙人见面</option>
                            <option :selected="'小组讨论' == model.nextStepStatus" value="小组讨论">小组讨论</option>
                            <option :selected="'不推进但持续关注' == model.nextStepStatus" value="不推进但持续关注">不推进但持续关注</option>
                            <option :selected="'Pass' == model.nextStepStatus" value="Pass">Pass</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label need-badge" for="pubtime-label">日期</label>
                    <div class="col-md-2">
                        <input class="form-control" type="text" id="pubtime" name="pubtime" v-model="model.PubTime" placeholder="日期" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label need-badge" for="interalPTCP">高榕参会人</label>
                    <div class="col-md-6">
                        <select class="form-control select2" id="interalPTCP" data-rule="required;" multiple="multiple" size="1">
                            @foreach (var mi in memberList)
                            {
                                <option value="@(mi.RealName)">@(mi.RealName)</option>
                            }
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label need-badge" for="Participant">外部参会人</label>
                    <div class="col-md-6">
                        <input class="form-control" type="text" id="Participant" name="Participant" rows="3" data-rule="required;" placeholder="公司参会人" v-model="model.Participant" />
                    </div>
                </div>


                <div class="form-group">
                    <label v-if="model.nextStepStatus == 'Pre-DD' || model.nextStepStatus == 'TS已签署'" class="col-md-2 control-label need-badge" for="DDManager">DD负责人</label>
                    <label v-else class="col-md-2 control-label" for="DDManager">DD负责人</label>
                    <div class="col-md-6">
                        <select v-if="model.nextStepStatus == 'Pre-DD' || model.nextStepStatus == 'TS已签署'" data-rule="required;" class="form-control select2" id="DDManager" multiple="multiple" size="1">
                            @foreach (var mi in memberList)
                            {
                                <option value="@(mi.RealName)">@(mi.RealName)</option>
                            }
                        </select>
                        <select v-else class="form-control select2" id="DDManager" multiple="multiple" size="1">
                            @foreach (var mi in memberList)
                            {
                                <option value="@(mi.RealName)">@(mi.RealName)</option>
                            }
                        </select>
                    </div>
                </div>
                @if (isOperate)
                {
                    <div class="form-group">
                        <label class="col-md-2 control-label" for="isprivate">是否私密</label>
                        <div class="col-md-2">
                            <select class="form-control" id="isprivate" v-model="model.IsPrivate" ssize="1">
                                <option value=false>否</option>
                                <option value=true>是</option>
                            </select>
                        </div>
                    </div>
                    <div v-show="model.IsPrivate" class="form-group">
                        <label class="col-md-2 control-label" for="private-reader">私密可读</label>
                        <div class="col-md-6">
                            <select class="form-control select2" id="private-reader" multiple="multiple" size="1">
                                @foreach (var mi in memberList)
                                {
                                    <option value="@(mi.RealName)">@(mi.RealName)</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-2 control-label" for="is-silver">是否银子弹项目</label>
                        <div class="col-md-2">
                            <select class="form-control" id="is-silver" v-model="model.IsSilver" ssize="1">
                                <option value=false>否</option>
                                <option value=true>是</option>
                            </select>
                        </div>
                    </div>
                }

                <div class="form-group">
                    <label class="col-md-2 control-label need-badge" for="foundedYear">公司成立年份</label>
                    <div class="col-md-2">
                        <select class="form-control" id="foundedYear" data-rule="required;" name="foundedYear" size="1" v-model="model.foundedYear">
                            <option value="">-成立年份-</option>
                            <option v-for="(item,index) in yearList" :selected="item == model.foundedYear">{{item}}</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label need-badge" for="city">项目所在地</label>
                    <div class="col-md-2">
                        <input class="form-control" type="text" id="city" name="city" placeholder="项目所在地" v-model="model.city" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="currency">融资币种</label>
                    <div class="col-md-2">
                        <select class="form-control" id="currency" v-model="model.Currency" ssize="1">
                            <option value=""></option>
                            <option value="美元">美元</option>
                            <option value="人民币">人民币</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="HeadCount">员工人数</label>
                    <div class="col-md-6">
                        <div id="posit">
                            <input class="form-control" id="HeadCount" name="HeadCount" rows="3" placeholder="员工人数" v-model="model.HeadCount">
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="TotalAsset">总资产规模</label>
                    <div class="col-md-6">
                        <div id="posit">
                            <input class="form-control" id="TotalAsset" name="TotalAsset" rows="3" placeholder="总资产规模" v-model="model.TotalAsset">
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label need-badge" for="summary">项目简介</label>
                    <div class="col-md-6">
                        <textarea class="form-control" id="summary" name="summary" rows="5" placeholder="项目简介（拖动文本框右下角可调整高度）" v-model="model.Summary"></textarea>
                    </div>

                </div>
                <div v-if="model.aisummary && model.Summary.length <= 15" class="form-group">
                    <label class="col-md-2 control-label" for="summary">参考简介</label>
                    <div class="col-md-5">
                        <textarea class="form-control" disabled id="summary" name="summary" rows="2" v-model="model.aisummary" style="cursor: text"></textarea>
                    </div>
                    <div class="col-md-1">
                        <button onclick="event.preventDefault(); copyToClipboard()">一键复制</button>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="Background">团队背景</label>
                    <div class="col-md-6">
                        <div id="posit">
                            <textarea class="form-control" id="Background" name="Background" rows="3" placeholder="团队经验与能力，创始人角色与领导力等" v-model="model.Background"></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="BusinessData">业务数据</label>
                    <div class="col-md-6">
                        <div id="posit">
                            <textarea class="form-control" id="BusinessData" name="BusinessData" rows="3" placeholder="业务数据（市场规模与公司增长策略，行业竞争与壁垒，业务模式与商业化进展，产能与供应链管理，国际化挑战等）" v-model="model.BusinessData"></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="FinancialData">财务数据</label>
                    <div class="col-md-6">
                        <div id="posit">
                            <textarea class="form-control" id="FinancialData" name="FinancialData" rows="3" placeholder="财务数据（现金流情况与资金使用效率等）" v-model="model.FinancialData"></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="ShareStructure">股权结构</label>
                    <div class="col-md-6">
                        <div id="posit">
                            <textarea class="form-control" id="ShareStructure" name="ShareStructure" rows="3" placeholder="股权结构与股东构成" v-model="model.ShareStructure"></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="InvestHistory">历史及当前融资方案</label>
                    <div class="col-md-6">
                        <div id="posit">
                            <textarea class="form-control" id="InvestHistory" name="InvestHistory" rows="3" placeholder="估值与融资结构复杂性，退出策略等" v-model="model.InvestHistory"></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="CompareProduct">提到的同行/供应商</label>
                    <div class="col-md-6">
                        <div id="posit">
                            <textarea class="form-control" id="CompareProduct" name="CompareProduct" rows="3" placeholder="提到的同行/供应商" v-model="model.CompareProduct"></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="HighLight">项目亮点</label>
                    <div class="col-md-6">
                        <div id="posit">
                            <textarea class="form-control" id="HighLight" name="HighLight" rows="3" placeholder="项目亮点" v-model="model.HighLight"></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="Risk">项目风险</label>
                    <div class="col-md-6">
                        <div id="posit">
                            <textarea class="form-control" id="Risk" name="Risk" rows="3" placeholder="项目风险" v-model="model.Risk"></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="NextStep">数据包分析/后续工作</label>
                    <div class="col-md-6">
                        <div id="posit">
                            <textarea class="form-control" id="NextStep" name="NextStep" rows="3" placeholder="数据包分析/后续工作" v-model="model.NextStep"></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="UpdatedNews">新发现</label>
                    <div class="col-md-6">
                        <div id="posit">
                            <textarea class="form-control" id="UpdatedNews" name="UpdatedNews" rows="3" placeholder="新发现" v-model="model.UpdatedNews"></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group" id="ai_block" v-if="model.ai_question || model.ai_reasoning">
                    <label class="col-md-2 control-label">本地AI参考提问</label>
                    <div class="col-md-6">
                        <details v-if="model.ai_reasoning" id="ai_reasoning_block" style=" background: rgba(237, 246, 253, 0.8); padding: 10px; margin-bottom: 5px; border-radius: 4px; ">
                            <summary style="cursor: pointer; color: #43a3e5; display: list-item; ">点击展开/折叠思维链</summary>
                            <div id="posit">
                                <div style="margin-top: 4px; white-space: break-spaces; color: #333; line-height: 1.4 " v-html="model.ai_reasoning"></div>
                            </div>
                        </details>
                        <div id="posit">
                            <div id="ai-question-content" style="white-space: break-spaces; line-height: 1.4 " v-html="model.ai_question"></div>
                        </div>
                    </div>
                </div>
                <div v-if="scorePass">
                    @if (isOperate || isProjectManager)
                    {
                        <div class="form-group">
                            <label class="col-md-2 control-label" for="NextStep">项目贡献</label>
                            @*<div class="block-content tab-content block-content-mini">
                                    <label class="col-md-2 control-label" for="NextStep">项目贡献</label>
                                    <div class="col-md-6 alert alert-info alert-dismissable" style="padding: 10px">
                                        <p>
                                            填写说明: 1、贡献度分值只有合伙人及项目负责人可见。可以将投资团队（不包括DD团队）对项目执行、项目来源、deal谈判、关键人脉、投后管理等维度的贡献体现在分值中。<br />
                                            2、项目贡献表由项目负责人先点击确认一个版本，合伙人点击确认后，系统记录本轮次项目贡献情况；如合伙人对项目负责人填写的贡献表有异议，可以修改分值，修改后项目负责人点击确认后，系统记录本轮次项目贡献情况。<br />
                                            3、后续如该比例发生变化，项目负责人可重新发起贡献度打分。
                                        </p>
                                    </div>
                                </div>
                                <label class="col-md-2 control-label" for="searchType"></label>*@
                            @if (isOperate || isProjectManager)
                            {
                                <div class="col-md-2">
                                    <select class="form-control" id="contributor" v-model="contributor" size="1">
                                        @*<option v-if='searchType=="3"' value="无">无</option>*@
                                        @foreach (var mi in memberList)
                                        {
                                            <option value="@(mi.RealName)">@(mi.RealName)</option>
                                        }
                                    </select>
                                </div>
                                @*<div class="col-md-4 col-sm-11" style="padding-top: 16px">
                                        <div id="slider"></div>
                                    </div>
                                    <span style="float: left;margin-top: 8px">%</span>*@
                            }
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label"></label>
                            <div class="col-md-6">
                                <input class="form-control" v-model="contributionDescription" placeholder="具体贡献说明（选填）">
                            </div>
                            <div>
                                <button class="btn btn-default" type="button" @@click="addContribution()">添加</button>
                            </div>
                        </div>


                        <div class="block-content tab-content block-content-mini" v-if="contributions.length > 0">
                            <label class="col-md-2 control-label"></label>
                            <div class="col-md-6">
                                <table class="table table-striped" style="width:100%;">
                                    <thead>
                                        <tr>
                                            <th>贡献者</th>
                                            @*<th>贡献百分比</th>*@
                                            <th>贡献说明</th>
                                            <th class="text-center" style="width: 100px;">
                                                删除
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(item,index) in contributions" style="width:100%;">
                                            <td>
                                                {{item.username}}
                                            </td>
                                            @*<td>
                                                    {{item.percentage}}%
                                                </td>*@
                                            <td>
                                                {{item.description}}
                                            </td>
                                            <td class="text-center">
                                                @if (isOperate || isProjectManager)
                                                {
                                                    <div class="btn-group">
                                                        <a :data-idx="index" class="btn push-5-r push-10" v-on:click="removeContribution"><i class="fa fa-remove"></i></a>
                                                    </div>
                                                }
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>


                        <div class="form-group" v-if="contributions.length > 0 && id > 0">
                            <label class="col-md-2 control-label"></label>
                            <div class="col-md-6">
                                <div style="padding: 0 30px">
                                    <button v-bind:disabled="model.contributionManagerConfirm" class="btn btn-default" type="button" @@click="contributionManagerConfirm()">负责人{{model.contributionManagerConfirm?"已":""}}确认</button>
                                    <button v-bind:disabled="model.contributionPartnerConfirm" style="float: right" class="btn btn-default" type="button" @@click="contributionPartnerConfirm()">合伙人{{model.contributionPartnerConfirm?"已":""}}确认</button>
                                </div>
                            </div>
                        </div>
                        @*<div class="form-group" v-if="model.contributionLog != ''">
                                <label class="col-md-2 control-label"></label>
                                <div class="col-md-6" style="background:#f9f9f9; padding-top: 10px">
                                    <h4 style="text-align:center; font-weight: bold">项目贡献确认记录(修改后需重新确认)</h4>
                                    <span style="white-space: pre;" v-html="model.contributionLog"></span>

                                </div>
                            </div>*@
                    }
                </div>
                <div class="form-group">
                    <div class="block-content tab-content block-content-mini">
                        <label class="col-md-2 control-label" for="NextStep">文件上传 ></label>
                        <div class="col-md-6 alert alert-info alert-dismissable">
                            <p>支持文件格式：<a class="alert-link" href="javascript:void(0)">doc</a>、<a class="alert-link" href="javascript:void(0)">docx</a>、<a class="alert-link" href="javascript:void(0)">ppt</a>、<a class="alert-link" href="javascript:void(0)">pptx</a>和<a class="alert-link" href="javascript:void(0)">pdf</a>，支持图片格式：<a class="alert-link" href="javascript:void(0)">gif</a>,<a class="alert-link" href="javascript:void(0)">jpg</a>,<a class="alert-link" href="javascript:void(0)">jpeg</a>,<a class="alert-link" href="javascript:void(0)">png</a>,<a class="alert-link" href="javascript:void(0)">bmp</a>，<a class="alert-link" href="javascript:void(0)">excel</a>文件请在下面单独上传；如遇ppt文件转换图片变形，建议在本地转换成pdf文件再上传，文件大小不超过20M。</p>
                        </div>
                    </div>

                    <label class="col-md-2 control-label" for="NextStep2">DD文件</label>
                    <div class="block-content tab-content block-content-mini">
                        <file2picbtn :id="id"
                                     converturl="/adminapi/docconvert"
                                     savedocurl="/adminapi/addprojectdoc"
                                     removeurl="/adminapi/delprojectdoc"
                                     v-bind:filelist.sync="dataListDD"
                                     v-bind:imglist.sync="DD"
                                     type="DD"
                                     elm="docfile-dd">

                        </file2picbtn>
                    </div>

                    <label class="col-md-2 control-label" for="NextStep1">BP或其他文件</label>
                    <div class="block-content tab-content block-content-mini">
                        <file2picbtn :id="id"
                                     converturl="/adminapi/docconvert"
                                     savedocurl="/adminapi/addprojectdoc"
                                     removeurl="/adminapi/delprojectdoc"
                                     v-bind:filelist.sync="dataListBP"
                                     v-bind:imglist.sync="BP"
                                     type="BP"
                                     elm="docfile-bp">

                        </file2picbtn>
                    </div>

                    <label class="col-md-2 control-label" for="NextStep1">详情页图片</label>
                    <div class="block-content tab-content block-content-mini">
                        <fileuploadbtn :id="id"
                                       btntxt="上传图片"
                                       removeurl="/adminapi/delprojectdoc"
                                       savedocurl="/adminapi/addprojectimage"
                                       filetype=""
                                       v-bind:filelist.sync="imgFileList" elm="imagesource">
                        </fileuploadbtn>
                    </div>

                    <label class="col-md-2 control-label" for="NextStep3">Excel数据文件</label>
                    <div class="block-content tab-content block-content-mini">
                        <fileuploadbtn :id="id"
                                       removeurl="/adminapi/delprojectdoc"
                                       savedocurl="/adminapi/addprojectdoc"
                                       v-bind:filelist.sync="fileList" elm="filesource">
                        </fileuploadbtn>
                    </div>


                </div>
                <div class="form-group">
                    <div class="col-md-8 col-md-offset-2">
                        <button class="btn btn-primary" @@click="saveData(@((int)ProjectStatus.normal))" type="button"><i class="fa fa-check push-5-r"></i>发 布</button>
                        <button class="btn btn-info" @@click="saveData(@((int)ProjectStatus.editing))" type="button"><i class="fa fa-save push-5-r"></i>保 存</button>
                        <button class="btn btn-minw btn-warning" @@click="saveData(-1)" type="button">取消并返回</button>
                        @if (pingLLM)
                        {
                            <label>本地AI<span v-if="model.ai_question">重新</span>提问:</label> <button  class="btn btn-minw btn-default" @@click="askStream()" type="button">快速</button>  
                            <button  class="btn btn-minw btn-default" @@click="askStream(true)" type="button">质量</button>
                        }
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section scripts{
    <script src="/content/js/plugins/jquery.ui.widget.js"></script>
    <script src="/content/js/plugins/jquery.iframe-transport.js"></script>
    <script src="/content/js/plugins/jquery.fileupload.js"></script>

    <script src="~/content/js/component/fileuploadbtn.js"></script>

    <link href="/content/js/plugins/select2/select2.min.css" rel="stylesheet" />
    <link href="/content/js/plugins/select2/select2-bootstrap.min.css" rel="stylesheet" />
    <script type="text/javascript" src="/content/js/plugins/select2/select2.min.js"></script>
    <script src="~/Content/js/plugins/city-select/js/lazyload-min.js"></script>

    <script src="/content/js/core/ajaxfileupload.js"></script>
    <link href="~/Content/js/plugins/lightgallery/css/normalize.css" rel="stylesheet" />
    <link href="~/Content/js/plugins/lightgallery/css/lightgallery.min.css" rel="stylesheet" />

    <script src="~/Content/js/plugins/lightgallery/js/picturefill.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lightgallery.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-fullscreen.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-thumbnail.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-video.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-autoplay.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-zoom.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-hash.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-pager.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/jquery.mousewheel.min.js"></script>
    <script src="~/Content/js/marked.js"></script>

    <script type="text/javascript">
        function getParameter(el) {
            var obj = {};
            $(el).each(function (index, item) {
                // 判断元素的类型
                if (item.type == "radio") {
                    // 获取到单选框选中的值
                    var radio_val = $("input[name=" + $(item).attr('name') + "]:checked").val();
                    if (radio_val) {
                        obj[$(item).attr("name")] = radio_val;
                    }
                }
            });
            return obj;
        }


    </script>
    <script type="text/javascript">
        function copyToClipboard() {
          const textarea = document.createElement('textarea');
          textarea.value = app.model.aisummary;
          document.body.appendChild(textarea);
          textarea.select();

          try {
            const successful = document.execCommand('copy');
            if (successful) {
              layer.msg('复制成功！');
            } else {
              throw new Error('复制失败');
            }
          } catch (err) {
            console.error('复制失败:', err);
          } finally {
            document.body.removeChild(textarea);
          }
        }
        function projectImport(ele) {
            $.ajaxFileUpload({
                url: '/adminapi/projectupload?dir=projectimport',
                secureuri: false,
                dataType: 'json',
                fileElementId: 'import',
                success: function (res, status) {
                    if (res.code == -1) {
                        return layer.msg(res.msg);
                    }
                    $("#interalPTCP").val(res.data.InteralPTCP.split(/[,|，| ]/g)).trigger('change');
                    $("#manager").val(res.data.ProjectManager.split(/[,|，| ]/g)).trigger('change');
                    $("#source").val(res.data.Introducer.split(/[,|，| ]/g)).trigger('change');
                    $("#project-source").val(res.data.Source.split(/[,|，| ]/g)).trigger('change');
                    $("#DDManager").val(res.data.DDManager.split(/[,|，| ]/g)).trigger('change');
                    $("#groupMember").val(res.data.groupMember.split(/[,|，| ]/g)).trigger('change');
                    app.model = res.data;

                    var minDate = new Date()
                    minDate.setDate(new Date().getDate() - 14)
                    var maxDate = new Date()
                    maxDate.setDate(new Date().getDate() + 7)
                    var tmpDate = new Date(res.data.PubTimeStr)
                    if(tmpDate >= minDate && tmpDate <= maxDate){
                        app.model.PubTime = res.data.PubTimeStr;
                    } else {
                        app.model.PubTime = undefined
                    }
                    app.projectSimilarNameCheck(app.model.Name);
                    app.founderSimilarCheck(app.model.Founder);
                    $("#import").val("")
                },
                error: function (data, status, e) {
                    console.log(data, status, e);
                    $("#import").val("")
                }
            });
            return false;
        }
        window.onbeforeunload = function (e) {
            e.returnValue = '有未保存的修改'
            return "有未保存的修改"
        }

        var layer, laydate, editing = @((int) ProjectStatus.editing), isdebug = false;
        layui.use(['layer', 'laydate', 'slider'],
            function() {
                var layer = layui.layer, laydate = layui.laydate, slider=layui.slider;
                laydate.render({
                    elem: '#pubtime',
                    min: -14,
                    max: +7,
                    done: function(value, date, endDate) {
                        app.$data.model.PubTime = value;
                    }
                });
                slider.render({
                    elem: '#slider',
                    height: 100,
                    tips: true,
                    input: true,
                    min: 1,
                    theme: "#3c7ac9",
                    change: function (value) {
                        app.$data.percentage = value
                    }
                });
            });



        $(document).ready(function () {
            $('#importbtn').click(function () {
                $('#import').click();
            });
            try {
                $('.select2').select2({
                    language: "zh-CN",
                    width: "100%",
                    height: "32px",
                    theme: "classic"
                });
            } catch (e) {
                if (confirm("页面加载失败，刷新重试?")){
                    history.go(0)
               }
            }

            //$('#Name').onchange(function(){
            //    projectNameCheck(this.value);
            //});
            LazyLoad.css(["/content/js/plugins/city-select/css/cityStyle.css"],
                function() {
                    LazyLoad.js(["/content/js/plugins/city-select/js/cityScript.js"],
                        function() {
                            var cityobj = new citySelector.cityInit("city");
                        });
                });
        });

        function cityCallback(val) {
            console.log(val);
            app.$data.model.city = val;
        }

        function trimVal(val) {
            return $.trim(val);
        }

        var app = new Vue({
            el: '#page-app',
            data: {
                id: @(ViewData["projectId"]),
                model: { contributionLog: "",ai_question: "" },
                notes: "",
                revisitName: undefined,
                revisitManger: undefined,
                showRevisitLink: false,
                progress: 0,
                yearList: [],
                imgFileList: [],
                dataImageFileList: [],
                fileList: [],
                dataListBP: [],
                DD: [],
                BP: [],
                dataListDD: [],
                loadState: -1,
                contributor: "@member.RealName",
                contributionDescription: "",
                percentage: 1,
                contributions: [],
                scoreStageList: [],
                scorePass: false,
                aiObserver: null,
                aiDisplayStartTime: 0,
                aiDisplayTimer: null
            },
            methods: {
                @* 初始化年份 *@
                initYear: function() {
                    var currYear = (new Date()).getFullYear();
                    var yearList = [];
                    for (var i = 0; currYear - i >= 1990; i++) {
                        yearList.push(currYear - i);
                    }
                    this.yearList = yearList;
                },

                @* 初始化页面数据 *@
                initData: function() {
                    var that = this;
                    $.post('/adminapi/projectdetail',
                        { id: that.id, init: 1 },
                        function(res) {
                            if (res.code == 0) {
                                res.data.PubTime = res.data.PubTimeStr;
                                $("#interalPTCP").val(res.data.InteralPTCP.split(',')).trigger('change');
                                $("#manager").val(res.data.ProjectManager.split(',')).trigger('change');
                                $("#source").val(res.data.Introducer.split(',')).trigger('change');
                                $("#finder").val(res.data.finder.split(',')).trigger('change');
                                $("#finder2").val(res.data.finder.split(',')).trigger('change');
                                $("#DDManager").val(res.data.DDManager.split(',')).trigger('change');
                                $("#groupMember").val(res.data.groupMember.split(',')).trigger('change');
                                $("#contribution-detail").val(res.data.gContributionDetail).trigger('change');
                                if (res.data.IsPrivate) {
                                    $("#private-reader").val(res.data.privateReader.split(',')).trigger('change');
                                }
                                that.model = res.data;
                                that.initProjectScore();
                                that.initAttachment();
                                if (that.model.RevisitId) {
                                    that.revisitCheck(that.model.RevisitId);
                                }
                                that.initAIObserver();
                            } else {
                                that.loadState = -999;
                            }
                        }).error(function(xhr, errorText, errorType) {
                        @* 请求失败 *@
                        that.loadState = -999;
                    });
                },

                initAIObserver() {
                    var that = this
                    const observer = new IntersectionObserver((entries) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting) {
                                console.log("in")
                                that.startAITimer();
                            } else {
                                console.log("out")
                                that.resetAITimer();
                            }
                        });
                    }, {
                        root: null, // 相对于视口
                        rootMargin: '0px',
                        threshold: 0.3 // 当10%的元素可见时触发
                    });
                    setTimeout(function () {
                        const element = document.querySelector('#ai-question-content');
                        if (element) {
                            observer.observe(element);
                            that.aiObserver = observer;
                        }
                    },1000)
                },

                startAITimer() {
                    var that = this
                    const startTime = Date.now();
                    that.aiDisplayStartTime = startTime;

                    that.aiDisplayTimer = setInterval(() => {
                        const currentTime = Date.now();
                        if (currentTime - that.aiDisplayStartTime >= 5000) {
                            that.logAIDisplay();
                            that.resetAITimer();
                        }
                    }, 1000);
                },
                logAIDisplay() {
                    var that = this
                    $.post('/adminapi/logset',
                        {
                            Project: that.model.Name,
                            Page: 'ai_question web project set',
                            Action: 'visit',
                        },
                        function (res) {}
                    );
                },
                resetAITimer() {
                    var that = this
                    if (that.aiDisplayTimer) {
                        clearInterval(that.aiDisplayTimer);
                        that.aiDisplayTimer = null;
                        that.aiDisplayStartTime = 0;
                    }
                },


                // 其他方法(startAITimer, resetAITimer等)可以保持不变
                initTemplate() {
                    var that = this
                    $.post('/adminapi/userSingleGroup',
                        { id: that.id, init: 1 },
                        function (res) {
                            if (res.code == 0 && res.data != null) {
                               $("#group").val([res.data]).trigger('change');
                            } else {
                                that.loadState = -999;
                            }
                        }).error(function (xhr, errorText, errorType) {
                               that.loadState = -999;
                        });
                    setTimeout(function () {
                        $("#interalPTCP").val(["@member.RealName"]).trigger('change');
                        $("#manager").val(["@member.RealName"]).trigger('change');
                    })
                },
                AIExtract() {
                    var that = this;
                    console.log(that.notes);
                    var layerIdx = layer.msg("思考中...", { time: 30000 });
                    $.post('/adminapi/LLMExtractProject', that.model, function (res) {
                        layer.close(layerIdx);
                        if (!res) {
                            that.loadState = -1;
                            layer.msg(res.msg || "服务器繁忙，请稍后重试...");
                            return;
                        }
                        //layer.open() 编辑框，可改写抽取内容提交
                    }).error(function (xhr, errorText, errorType) {
                        layer.msg('确认失败！');
                    });
                },
                contributionCheck(func) {
                    var that = this;
                    var contributionSum = that.contributions.reduce(function (pre, cur) {
                        return pre + cur.percentage
                    }, 0)
                    if (contributionSum != 100) {
                        return layer.confirm("累计贡献百分比" + contributionSum + "不等于100, 请先调整为100")
                    }

                    layer.confirm('是否确认项目贡献？ 已确认项目删除后负责人与合伙人需重新确认！', { btn: [ '确认', '取消'] }, func)
                },
                contributionManagerConfirm() {
                    var that = this
                    if (that.model.ProjectManager != '@member.RealName') {
                        return layer.msg('无对应权限，确认失败！');
                    }
                    layer.confirm('是否确认项目贡献？ 已确认项目删除后负责人与合伙人需重新确认！', { btn: ['确认', '取消'] },
                        function() {
                            var contributionLog = that.contributions.reduce(function (pre, cur) {
                                return pre + cur.username + " " + cur.percentage + "% " + (cur.description ? cur.description : "") + "\n"
                            }, "")
                            if (contributionLog) {
                                contributionLog = "项目负责人("+ "@member.RealName" +")确认：\n" + contributionLog;
                            }
                            model = {
                                Id: that.id,
                                contributionManagerConfirm: true,
                                contributionLog
                            }
                            $.post('/adminapi/contributionManagerConfirm', model, function (res) {
                                if (res.code == 0) {
                                    layer.msg('确认成功');
                                    that.model.contributionManagerConfirm = true
                                    that.model.contributionLog = res.data
                                }
                            }).error(function (xhr, errorText, errorType) {
                                layer.msg('确认失败！');
                            });
                        }
                    )
                },
                contributionPartnerConfirm() {
                    var that = this;
                    if ('@isOperate' != 'True') {
                        return layer.msg('无对应权限，确认失败！');
                    }
                    return layer.msg('请使用小程序进行确认！');
                    that.contributionCheck(function () {
                        var contributionLog = that.contributions.reduce(function (pre, cur) {
                            return pre + cur.username + " " + cur.percentage + "% " + (cur.description ? cur.description : "") + "\n"
                        }, "")
                        if (contributionLog) {
                            contributionLog = "合伙人(" + "@member.RealName" + ")确认：\n" + contributionLog;
                        }
                        model = {
                            Id: that.id,
                            contributionPartnerConfirm: true,
                            contributionLog
                        }
                        $.post('/adminapi/contributionPartnerConfirm', model, function (res) {
                            if (res.code == 0) {
                                layer.msg('确认成功');
                                that.model.contributionPartnerConfirm = true
                                that.model.contributionLog = res.data
                            }
                        }).error(function (xhr, errorText, errorType) {
                            layer.msg('确认失败！');
                        })
                    })
                },
                addContribution() {
                    var that = this;
                    model = {
                        projectId: that.id,
                        percentage: that.percentage,
                        username: that.contributor,
                        description: that.contributionDescription
                    }
                    if (!model.username) {
                        return alert("请先选择贡献者后添加！")
                    }
                    var total = 0
                    for (var i = 0; i < that.contributions.length; i++) {
                        total += that.contributions[i].percentage
                        if (that.contributions[i].username == model.username) {
                            return alert("已添加" + model.username + ", 请勿重复添加！")
                        }
                    }
                    total += model.percentage
                    if (total > 100) {
                        return alert("无法添加, 累计百分比" + total + " > 100，请修改后添加！")
                    }

                    if (that.id > 0) {
                        $.post('/adminapi/addContribution', model, function (res) {
                            if (res.code == 0) {
                                model.ID = res.data
                                that.contributor = ""
                                that.contributionDescription = ""
                                that.model.contributionManagerConfirm = false
                                that.model.contributionPartnerConfirm = false
                                that.contributions.push(model);
                            }
                        }).error(function (xhr, errorText, errorType) {
                            that.loadState = -999;
                        });
                    } else {
                        that.contributions.push(model);
                        that.contributor = ""
                        that.contributionDescription = ""
                        //$("#contributor").val("").trigger('change');
                    }
                },
                removeContribution: function (elem) {
                    var idx = +elem.currentTarget.dataset.idx
                    var that = this;
                    if (that.id == 0) {
                        return that.contributions.splice(idx, 1);
                    }
                    layer.confirm('是否确认删除该项目贡献？ 已确认项目删除后负责人与合伙人需重新确认！', { btn: ['确认', '取消'] }, function () {
                        $.post('/adminapi/delContribution', { id: that.contributions[idx].ID }, function (res) {
                            if (res.code != 0) {
                                layer.msg('删除失败');
                                return;
                            }
                            that.contributions.splice(idx, 1);
                            that.model.contributionManagerConfirm = false
                            that.model.contributionPartnerConfirm = false
                            layer.msg('删除成功');
                        }).error(function (xhr, errorText, errorType) {
                            that.loadState = -999;
                        });
                    })
                },
                initAttachment() {
                    var that = this;
                    $.post('/adminapi/getcontributions', { projectID: that.id }, function (res) {
                        if (res.code == 0) {
                            that.contributions = []
                            res.data.map(function (data) {
                                that.contributions.push(data)
                            })
                        }
                    })

                    $.post('/adminapi/projectdocs',
                        { id: that.id },
                        function(res) {
                            if (res.code == 0) {
                                that.dataListBP = [];
                                that.dataListDD = [];
                                that.dataImageFileList = [];
                                that.fileList = res.data || [];
                                that.fileList = that.fileList.filter(function(file) {
                                    if (file.AtSuffix === "BP") {
                                        that.BP.push(file)
                                        that.dataListBP.push(file.AtUrl.split(','));
                                        return false
                                    } else if (file.AtSuffix === "DD") {
                                        that.DD.push(file)
                                        that.dataListDD.push(file.AtUrl.split(','));
                                        return false
                                    } else if (file.AtSuffix === "Image") {
                                        that.imgFileList.push(file)
                                        that.dataImageFileList.push(file.AtUrl.split(','));
                                        return false;
                                    }
                                    return true
                                })
                            } else {
                                that.loadState = -999;
                            }
                        }).error(function(xhr, errorText, errorType) {
                        that.loadState = -999;
                    });
                },
                @* 参数验证 *@
                paramCheck() {
                    var errVal, that = this;
                    if (trimVal(that.model.Name) == '') {
                        errVal = '请输入项目名称';
                    } else if (trimVal(that.model.ToRoleId) == '' || that.model.ToRoleId <= 0) {
                        errVal = '请选择项目组';
                    } else if (trimVal(that.model.PubTime) == '') {
                        errVal = '请选择日期';
                    } else if (trimVal(that.model.InteralPTCP) == '') {
                        errVal = '请选择高榕参会人';
                    } else if (trimVal(that.model.Participant) == '') {
                        errVal = '请输入外部参会人';
                    } else if (trimVal(that.model.Source) == '') {
                        errVal = '请输入项目来源';
                    } else if (trimVal(that.model.ProjectManager) == '') {
                        errVal = '请输入项目负责人';
                    } else if (trimVal(that.model.foundedYear) == '') {
                        errVal = '请选择公司成立年份';
                    } else if (trimVal(that.model.nextStepStatus) == '') {
                        errVal = '请选择项目状态';
                    } else if (trimVal(that.model.Summary) == '') {
                        errVal = '请输入项目简介';
                    } else if (trimVal(that.model.city) == '') {
                        errVal = '请输入项目所在地';
                    } else if ((that.model.nextStepStatus == 'Pre-DD' || that.model.nextStepStatus == 'TS已签署') && trimVal(that.model.DDManager) == '') {
                        errVal = '请输入DD负责人';
                    } else if (trimVal(that.model.Source) == '3' && trimVal(that.model.Introducer) == '') {
                        errVal = '请输入FA名称';
                    } else if (trimVal(that.model.Source) == '3' && trimVal(that.model.finder) == '') {
                        errVal = '请输入FA推荐同事名称';
                    } else if (trimVal(that.model.Source) == '2' && trimVal(that.model.Introducer) == '') {
                        errVal = '请输入介绍人名称';
                    } else if (trimVal(that.model.Source) == '1' && trimVal(that.model.Introducer) == '') {
                        errVal = '请输入提及人名称';
                    } else if (['已投项目新一轮', "TS已签署", "Pre-DD", "安排合伙人见面", "小组讨论", "不推进但持续关注", "Pass"].indexOf(that.model.nextStepStatus) == -1) {
                        errVal = '请选择项目状态';
                    }

                    if (errVal) {
                        layer.msg(errVal);
                        return false;
                    }
                    return true;
                },
                paramCheckSimple() {
                    var errVal, that = this;
                    if (trimVal(that.model.Name) == '') {
                        errVal = '请输入项目名称';
                    } else if (trimVal(that.model.ToRoleId) == '' || that.model.ToRoleId <= 0) {
                        errVal = '请选择项目组';
                    } else if (trimVal(that.model.PubTime) == '') {
                        errVal = '请选择日期';
                    } else if (trimVal(that.model.ProjectManager) == '') {
                        errVal = '请输入项目负责人';
                    }

                    if (errVal) {
                        layer.msg(errVal);
                        return false;
                    }
                    return true;
                },
                paramAttach() {
                    var list = [], that = this;
                    that.fileList.forEach(function(v, i) {
                        if (!v.Id) {
                            list.push(v);
                        }
                    });
                    that.imgFileList.forEach(function (v, i) {
                        if (!v.Id) {
                            v.AtSuffix = "Image";
                            list.push(v);
                        }
                    });
                    return list;
                },
                askStream(isDeepseek) {
                    var that = this;
                    if (!that.model.Id || that.model.Id <= 0) {
                        return layer.msg("请先保存项目后，再进行提问。")
                    }
                    var val3 = $("#manager").select2("val") || [];
                    that.model.ProjectManager = val3;
                    if (!that.paramCheckSimple()) {
                        return;
                    }
                    that.logAIDisplay();
                    var url = '/adminapi/AnalyseStream?id=' + that.model.Id
                    if (isDeepseek) {
                        url += "&model=deepseek-r1-0528"
                    }
                    const eventSource = new EventSource(url);
                    that.model.ai_reasoning = "";
                    that.model.ai_question = "";
                    var markedThink = "";
                    var markedQuestion = "";
                    var isThink = true;
                    //that.model.ai_question = "<think>";
                    var layerIdx = layer.msg("思考中..", { time: 750000 });
                    eventSource.onmessage = (event) => {
                        try {
                            if (layerIdx != -1) {
                                layer.close(layerIdx);
                                layerIdx = -1;
                            }
                            const details = document.getElementById("ai_reasoning_block");
                            if (details && isThink) {
                                details.open = true; // 切换open属性的值
                            }
                            const data = JSON.parse(event.data).replace("<think>", "");
                            //console.log('Received event:', data);
                            //that.model.ai_question += data;
                            if (data == "</think>") {
                                isThink = false;
                                return;
                            }
                            if (isThink) {
                                markedThink += data;
                                that.model.ai_reasoning = marked.parse(markedThink);
                            } else {
                                markedQuestion += data;
                                that.model.ai_question = marked.parse(markedQuestion);
                            }

                        } catch (e) {
                            console.error('解析错误:', e);
                        }
                    };
                    eventSource.addEventListener('end', () => {
                        $.post('/adminapi/updateProjectQuestion', { id: that.model.Id, ai_reasoning: markedThink, ai_question: markedQuestion }).error(function (xhr, errorText, errorType) {
                            layer.msg("服务器繁忙，请稍后重试...");
                        });;
                        console.log('SSE 流正常结束');
                        eventSource.close();
                    });

                    eventSource.onerror = (err) => {
                        console.error('SSE 错误:', err);
                    };

                },
                ask() {
                    var that = this;
                    var val3 = $("#manager").select2("val") || [];
                    that.model.ProjectManager = val3;
                    if (!that.paramCheckSimple()) {
                        return;
                    }
                    let copiedData = { ...that.model };
                    copiedData.ai_question = "";
                    copiedData.ai_reasoning = "";
                    var layerIdx = layer.msg("后台思考中，此页面可退出...", {time: 750000});
                    $.post('/adminapi/AnalyseStream',
                        { p: copiedData },
                        function (res) {
                            layer.close(layerIdx);
                            if (!res) {
                                layer.msg(res.msg || "服务器繁忙，请稍后重试...");
                                return;
                            } else if (res.substr(0, 4) == "信息不足") {
                                layer.msg(res);
                                return;
                            }

                            const parts = res.split("</think>", 1);
                            let think = "";

                            if (parts[0].includes("<think>")) {
                                const temp = parts[0].split("<think>");
                                if (temp.length > 1) {
                                    think = temp[1];
                                }
                            }

                            const newQuestion = res.replace(parts[0] + "</think>", "");

                            that.model.ai_reasoning = think
                            that.model.ai_question = newQuestion

                        }).error(function (xhr, errorText, errorType) {
                            layer.msg("服务器繁忙，请稍后重试...");
                        });
                },
                @* 保存请求 *@
                savePost(status) {
                    var that = this;
                    that.loadState = 0;
                    if (isdebug) {
                        that.loadState = -1;
                        return;
                    }
                    var attach = [];
                    if (that.id <= 0) {
                        attach = that.paramAttach();
                        String.prototype.replaceAll = function(s1, s2) {
                            return this.replace(new RegExp(s1, "gm"), s2);
                        }
                        for (var i = 0; i < that.BP.length; i++) {
                            if (that.BP[i].AtSuffix == "BP") {
                                var BP = {
                                    AtUrl: that.BP[i].AtUrl.replaceAll(window.location.origin, ""),
                                    AtSuffix: that.BP[i].AtSuffix,
                                    AtName: that.BP[i].AtName,
                                    Content: that.BP[i].Content
                                }
                                attach.push(BP)
                            }
                        }
                        for (var i = 0; i < that.DD.length; i++) {
                            if (that.DD[i].AtSuffix == "DD") {
                                var DD = {
                                    AtUrl: that.DD[i].AtUrl.replaceAll(window.location.origin, ""),
                                    AtSuffix: that.DD[i].AtSuffix,
                                    AtName: that.DD[i].AtName,
                                    Content: that.DD[i].Content
                                }
                                attach.push(DD)
                            }
                        }
                    }


                    $.post('/adminapi/projectsave',
                        { model: that.model, attach: JSON.stringify(attach), contributions: JSON.stringify(that.contributions) },
                        function(res) {
                            if (!res || res.code != 0) {
                                that.loadState = -1;
                                layer.msg(res.msg || "服务器繁忙，请稍后重试...");
                                return;
                            }
                            window.onbeforeunload = null
                            if (editing == status) {
                                that.loadState = -1;
                                that.id = res.data;
                                that.model.Id = res.data;
                                layer.msg("当前编辑保存成功，编辑完请按发布按钮");
                                that.initAttachment()
                            } else {
                                layer.msg("发布成功");
                                setTimeout(function() {
                                        window.location.href = '/';
                                    },
                                    1200);
                            }
                        }).error(function(xhr, errorText, errorType) {
                        that.loadState = -1;
                        layer.msg("服务器繁忙，请稍后重试...");
                    });
                },
                @* 保存数据 *@
                saveData(status) {
                    var that = this;
                    if (that.loadState > -1)
                        return;

                    if (status < 0) {
                        window.history.go(-1);
                        return;
                    }
                    that.model.Status = status;
                    var val1 = $("#interalPTCP").select2("val") || [];
                    that.model.InteralPTCP = val1.join(',');
                    if ($("#private-reader").length > 0) {
                        var valreader = $("#private-reader").select2("val") || [];
                        that.model.privateReader = valreader.join(',');
                    }

                    switch (that.model.Source) {
                    case"0":
                            that.model.Introducer = "";
                            break;
                    case "4":
                            that.model.Introducer = "";
                            break;
                    case "1":
                        var val2 = $("#source").select2("val") || [];
                        that.model.Introducer = val2.join(',');
                        val2 = $("#finder").select2("val") || [];
                        that.model.finder = val2.join(',');
                        break;
                    case "3":
                        val2 = $("#finder2").select2("val") || [];
                        that.model.finder = val2.join(',');
                        break;
                    case "2":
                        var val2 = $("#source").select2("val") || [];
                        that.model.Introducer = val2.join(',');
                        break;
                    }
                    //if (that.model.Source != "3") {
                    //    if(that.model.Source == "0"){
                    //        var val2 = $("#source2").select2("val");
                    //        that.model.Introducer = val2;}
                    //    else{var val2 = $("#source").select2("val") || [];
                    //        that.model.Introducer = val2.join(',');
                    //    }
                    //}

                    var val3 = $("#manager").select2("val") || [];
                    that.model.ProjectManager = val3;
                    val3 = $("#DDManager").select2("val") || [];
                    that.model.DDManager = val3.join(',');
                    val3 = $("#groupMember").select2("val") || [];
                    that.model.groupMember = val3.join(',');
                    that.model.city = $('#city').val();

                    if (editing == status) {
                        if (that.paramCheckSimple()) {
                            that.savePost(status);
                        }
                    } else if (that.paramCheck()) {
                        that.projectSameNameCheck(that.model.Name,
                            function(nameCheck) {
                                if (nameCheck && (that.model.Id == 0 || !that.model.Id)) {
                                    layer.confirm('该项目名称已存在，继续创建吗？',
                                        function(ind) {
                                            layer.close(ind);
                                            that.savePost(status);
                                        });
                                } else {
                                    that.savePost(status);
                                }
                            });

                    }
                },
                projectSameNameCheck(val, cb) {
                    $.post('/adminapi/projectSameNameCheck', { pname: val },
                        function(res) {
                            if (res.code == 0) {
                                return cb(res.data);
                            }
                            return 0;
                        }).error(function(xhr, errorText, errorType) {
                        layer.msg("服务器繁忙，请稍后重试...");
                    });
                },
                similarNameCheck(val, cb) {
                    $.post('/adminapi/projectSimilarNameCheck', { pname: val },
                        function (res) {
                            if (res != null) {
                                return cb(res.data);
                            }
                            return 0;
                        }).error(function (xhr, errorText, errorType) {
                            layer.msg("服务器繁忙，请稍后重试...");
                        });
                },
                projectSimilarNameCheck(val) {
                    var that = this;
                    $.post('/adminapi/projectSimilarNameCheck', { pname: val },
                        function (res) {
                            if (res.data.length > 0) {
                                that.selectRevisit(res.data)
                            }
                            return 0;
                        }).error(function (xhr, errorText, errorType) {
                            layer.msg("服务器繁忙，请稍后重试...");
                        });
                },
                founderSimilarCheck(val) {
                    var that = this;
                    if (that.revisitName) {
                        return
                    }
                    $.post('/adminapi/founderSimilarCheck', { pname: val },
                        function (res) {
                            if (res.data.length > 0) {
                                that.selectRevisit(res.data)
                            }
                            return 0;
                        }).error(function (xhr, errorText, errorType) {
                            layer.msg("服务器繁忙，请稍后重试...");
                        });
                },
                selectRevisit(projectList) {
                    var that = this;
                    var str = `<form class="edit-modal" style='margin: 20px'>
                          <div class="roles" style="margin-top: 10px">`
                    for (var i = 0; i < projectList.length; i++) {
                        var p = projectList[i]
                        str += that.genGroup("select-id", p.Id, i == 0, p.Name + "  " + p.ProjectManager);
                    }
                    str += `<div class="layui-input-block" style="margin-top: 50px">
                       <button type="button" class="layui-btn layui-btn-primary" onclick="app.revisit(false)">取消</button>
                        <button type="button" class="layui-btn" onclick="app.revisit()">确定</button>
                    </div>`
                    str += "</form>"
                    layui.layer.open({
                        type: 1,
                        content: str,
                        title: `存在类似项目，是否选择revisit项目进行关联？`,
                        area: ['400px', '340px']
                    })
                },
                genGroup(name, value, checked, displayName) {
                    return `<div><input type="radio" class="query-group" name=${name} id=${value} value=${value} ${checked && "checked"}></input>
                                  <label for=${value}
 style="margin-right: 10px">${displayName}</label></div>`
                },
                revisitCheck(id) {
                    var that = this;
                    that.model.RevisitId = id;
                    $.post('/adminapi/getrevisitright',
                        { id: id },
                        function (res) {
                            if (res.data.Id) {
                                that.showRevisitLink = true;
                            }
                            that.revisitName = res.data.Name;
                            that.revisitManager = res.data.ProjectManager
                            that.model.Name = res.data.Name + " (revisit)";
                        });
                },
                revisit(proceed = true) {
                    if (!proceed) {
                        return layer.closeAll()
                    }
                    var parameter = getParameter(".query-group");
                    var id = null
                    for (key in parameter) {
                        id = parameter[key]
                    }
                    var that = this;
                    that.model.RevisitId = id;
                    $.post('/adminapi/getrevisitright',
                        { id: id},
                        function (res) {
                            if (res.data.Id) {
                                that.showRevisitLink = true;
                            }
                            that.revisitName = res.data.Name;
                            that.revisitManager = res.data.ProjectManager
                            that.model.Name = res.data.Name + " (revisit)";
                        }, layer.closeAll());
                },
                preview() {
                    var that = this;
                    if (!that.showRevisitLink) {
                        layer.msg("无权访问，请联系项目负责人" + that.revisitManager + "分享");
                        return;
                    }
                    layer.open({
                        type: 2,
                        area: ['850px', '667px'],
                        fix: false,
                        maxmin: true,
                        anim: 5,
                        shade: 0,
                        title: "项目预览",
                        content: '/index/preview?id=' + that.model.RevisitId,
                    });
                },
                initProjectScore: function () {
                    var that = this;
                    if (that.model.PubTimeStr < "2023-01-01") {
                        return
                    }
                    $.post('/adminapi/getstagelist', { id: that.id }, function (res) {
                        if (res.code == 0) {
                            that.scoreStageList = res.data.data || [];
                            if (that.scoreStageList.length > 0) {
                                that.scorePass = (that.scoreStageList[0].ProjectManagerScore > 6 || that.scoreStageList[0].EndTime < '2022-04-27') && that.scoreStageList[0].PartnerScoreAvg >= 6 && that.scoreStageList[0].State == 0
                            }

                        }
                    }).error(function (xhr, errorText, errorType) {
                        console.log(errorText);
                    });
                },
            },
            created: function () {
                var that = this
                that.initYear();
                if (that.id > 0) {
                    that.initData();
                } else {
                    that.initTemplate();
                }

                $("#page-app").show();
                //this.initGallery();
            }
        });
    </script>
}