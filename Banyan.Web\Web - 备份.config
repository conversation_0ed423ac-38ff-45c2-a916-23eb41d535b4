﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  有关如何配置 ASP.NET 应用程序的详细信息，请访问
  https://go.microsoft.com/fwlink/?LinkId=169433
-->
<configuration>
  <appSettings>
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="PreserveLoginUrl" value="true" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="appKey" value="wx13a4685c552fa65f" />
    <add key="appSecret" value="5585b96303b020fa1a3b73b6785a866d" />
    <!--Redis服务地址-->
    <add key="RedisPath" value="127.0.0.1:6380" />
    <!--文件服务器地址-->
    <add key="FileDomain" value="https://ims.gaorongvc.com" />
    <!--浏览量更新间隔秒数-->
    <add key="ViewCountUpdateInterval" value="30" />
    <!--文章更新推送模板Id-->
    <!--<add key="ArticlePushTelpletId" value="0G8Pdmi75EzV9Z9GJWsgd4V4pMwwzbDoH91SoQ5-uZc" />-->
  </appSettings>
  <connectionStrings>
    <!--<add name="QLWL" connectionString="Server=tcp:gaorong.database.windows.net,1433;Initial Catalog=project;Persist Security Info=False;User ID=banyanvc;Password=************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"/>-->
    <add name="QLWL" connectionString="Data Source=127.0.0.1;Initial Catalog=DB;Persist Security Info=True;User ID=banyanvc;Password=************" />
    <add name="Survey" connectionString="Data Source=127.0.0.1;Initial Catalog=banyan;Persist Security Info=True;User ID=banyanvc;Password=************;Connection Timeout=30;" />
    <!-- <add name="TestDB" connectionString="Data Source=**************;Initial Catalog=DB;Persist Security Info=True;User ID=banyanvc;Password=************" providerName="System.Data.SqlClient" /> -->
  </connectionStrings>
  <system.web>
    <customErrors mode="Off" />
    <compilation targetFramework="4.6.1" />
    <httpRuntime targetFramework="4.6.1" requestValidationMode="2.0" maxRequestLength="2097151" />
    <pages>
      <namespaces>
        <add namespace="System.Web.Helpers" />
        <add namespace="System.Web.Mvc" />
        <add namespace="System.Web.Mvc.Ajax" />
        <add namespace="System.Web.Mvc.Html" />
        <add namespace="System.Web.Routing" />
        <add namespace="System.Web.WebPages" />
      </namespaces>
    </pages>
  </system.web>
  <system.webServer>
    <security>
      <requestFiltering>
        <!-- 1 GB -->
        <requestLimits maxAllowedContentLength="**********" />
      </requestFiltering>
    </security>
    <!--<staticContent>
      <mimeMap fileExtension=".woff" mimeType="application/x-font-woff" />
      <mimeMap fileExtension=".woff2" mimeType="application/x-font-woff" />
    </staticContent>
    <rewrite></rewrite>-->
    <validation validateIntegratedModeConfiguration="false" />
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
    <httpErrors errorMode="Detailed">
      <remove statusCode="403" subStatusCode="-1" />
      <error statusCode="402" path="https://ims.gaorongvc.com" responseMode="Redirect" />
      <error statusCode="403" prefixLanguageFilePath="" path="https://ims.gaorongvc.com" responseMode="Redirect" />
    </httpErrors>
    <asp scriptErrorSentToBrowser="true" />
    <rewrite>
      <rules>
        <rule name="tohttps" stopProcessing="true">
          <match url="(.*)" />
          <conditions>
            <add input="{HTTPS}" pattern="^OFF$" />
          </conditions>
          <action type="Redirect" url="https://{HTTP_HOST}/{R:1}" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-1.1.0.0" newVersion="1.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
<!--ProjectGuid: 06C08EAD-C0C4-44E7-952E-BF0CBB5546C7-->