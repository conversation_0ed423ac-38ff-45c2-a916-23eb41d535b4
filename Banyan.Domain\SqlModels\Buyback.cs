﻿using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.BusinessCenterPortal)]
    public class Buyback
    {
        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        [SqlField]
        public int DealID { get; set; }

        [SqlField]
        public string abbName { get; set; }

        [SqlField]
        public string Creator { get; set; }

        [SqlField]
        public string Manager { get; set; }
        [SqlField]
        public string Fund { get; set; }
        [SqlField]
        public string Round { get; set; } = string.Empty;
        [SqlField]
        public string Description { get; set; } = string.Empty;

        [SqlField]
        public int Status { get; set; }

        [SqlField]
        public string Result { get; set; } = string.Empty;

        [SqlField]
        public string Progress { get; set; } = string.Empty;

        [SqlField]
        public string Otherinfo { get; set; } = string.Empty;

        [SqlField]
        public int Type { get; set; }

        public bool IsOperate { get; set; } = false;
        [SqlField]
        public DateTime AddDate { get; set; } = DateTime.Now;
        [SqlField]
        public DateTime DueDate { get; set; } = DateTime.MaxValue;
        [SqlField]
        public DateTime ModifiedDate { get; set; } = DateTime.Now;
        [SqlField]
        public string Modifier { get; set; }
    }
    public enum BuybackStatus
    {
        normal = 0, // 正常发布，监控中
        alarm = 1, // 已报警
        processed = 2, //已处理回购
        ignore = 3, // 忽略回购
        editing = 4, // 仅保存
        expired = 5, // 发布或者编辑时报警截止日期已过
        exception = 6, //监控异常
        other = 7, // 正常发布，无需监控
    }
    public enum BuybackType
    {
        duedate = 0,
        duepublic = 1,
        other = 2
    }
}
