﻿using Banyan.Code;
using Banyan.Domain;
using System;
using System.Collections.Specialized;
using System.Linq;

namespace Banyan.Apps
{
    public class CommentBLL : DAL.Base.BaseDAL<Comment>
    {
        private readonly AjaxResult ajaxResult = null;

        public CommentBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        /// <summary>
        /// 分页查询记录
        /// </summary>
        /// <param name="paramValues">页面请求参数</param>
        /// <returns></returns>
        public AjaxResult GetPageList(NameValueCollection paramValues)
        {
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            int parentId = WebHelper.GetValueInt("parentid", 0, paramValues);

            string strWhere = $"Status={(int)ArticleStatus.normal} AND ParentId={parentId} ";

            int aid = WebHelper.GetValueInt("aid", 0, paramValues);
            if (aid > 0)
            {
                strWhere += $"AND ArticleId={aid} ";
            }

            string title = WebHelper.GetValue("title", string.Empty, paramValues);
            if (!string.IsNullOrWhiteSpace(title))
            {
                strWhere += $"AND Content like '%{title}%' ";
            }

            var commentList = GetList(strWhere, pageSize, pageIndex, "*", "Id DESC");
            if (commentList != null && commentList.Count() > 0)
            {
                var articleBll = new ArticleBLL();
                var projectBll = new ProjectBLL();
                var users = new MemberBLL().GetList($"Id in({string.Join(",", commentList.Select(x => x.UserId).Distinct().ToArray())})");
                foreach (var item in commentList)
                {
                    item.UserName = users.Where(x => x.Id == item.UserId)?.FirstOrDefault()?.RealName;
                    item.ArticleTitle = parentId == 0 ? articleBll.GetModelByCache(item.ArticleId)?.Title : projectBll.GetModelByCache(item.ArticleId)?.Name;
                }
            }

            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = commentList;
            ajaxResult.count = GetCount(strWhere);
            return ajaxResult;
        }

        public AjaxResult Save(Comment model)
        {
            AjaxResult ajaxResult = new AjaxResult();
            ArticleBLL articleBll = new ArticleBLL();
            model.Content = WebHelper.Formatstr(model.Content);

            if (model.Id > 0)
            {
                ajaxResult.data = Update(model);
            }
            else
            {
                ajaxResult.data = Convert.ToInt32(Add(model)) > 0;
                articleBll.UpCountField(model.ArticleId, "comment");
            }

            ajaxResult.code = (bool)ajaxResult.data ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }

        /// <summary>
        /// 属性更新
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public AjaxResult FieldSet(NameValueCollection paramValues)
        {
            var id = WebHelper.GetValueInt("id", 0, paramValues);
            var field = WebHelper.GetValue("field", string.Empty, paramValues);
            var state = WebHelper.GetValueInt("state", 0, paramValues);

            if (id <= 0 || string.IsNullOrWhiteSpace(field))
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                return ajaxResult;
            }

            Comment model = new Comment() { Id = id };
            switch (field)
            {
                case "delete":
                    field = "Status";
                    model.Status = (byte)CommentStatus.delete;
                    break;
                default:
                    field = string.Empty;
                    break;
            }

            var result = Update(model, field);
            ajaxResult.code = result ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }

        /// <summary>
        /// 属性更新
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public AjaxResult CommentSet(NameValueCollection paramValues)
        {
            var aid = WebHelper.GetValueInt("aid", 0, paramValues);
            var uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var content = WebHelper.GetValue("content", string.Empty, paramValues);
            var parentId = WebHelper.GetValueInt("pid", 0, paramValues);

            var articleBll = new ArticleBLL();
            var model = new Comment
            {
                Content = WebHelper.Formatstr(content),
                ArticleId = aid,
                UserId = uid,
                ParentId = parentId,
            };

            model.Content = WebHelper.Formatstr(model.Content);
            if (model.Id > 0)
            {
                ajaxResult.data = Update(model);
            }
            else
            {
                ajaxResult.data = Convert.ToInt32(Add(model)) > 0;
                articleBll.UpCountField(model.ArticleId, "comment");
            }
            Logger.Info($"{uid} add comment ${model.Content}");

            ajaxResult.code = (bool)ajaxResult.data ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }

        /// <summary>
        /// 添加评论
        /// </summary>
        /// <param name="user"></param>
        /// <param name="aid"></param>
        /// <param name="content"></param>
        /// <param name="parentId">0：专家访谈，1：项目约见</param>
        /// <returns></returns>
        public AjaxResult CommentSet(Member user, int aid, string content, int parentId)
        {
            if (user == null)
            {
                ajaxResult.code = (int)ResultCode.unlogin;
                ajaxResult.msg = "请先登录";
                return ajaxResult;
            }
            var articleBll = new ArticleBLL();
            var model = new Comment
            {
                Content = WebHelper.Formatstr(content),
                ArticleId = aid,
                UserId = user.Id,
                ParentId = parentId,
            };

            model.Content = WebHelper.Formatstr(model.Content);

            model.Id = Convert.ToInt32(Add(model));
            ajaxResult.data = new
            {
                model.Id,
                model.Content,
                model.ArticleId,
                model.UserId,
                UserName = user?.RealName,
                UserAvatar = user?.Avatar,
                AddTime = model.AddTime.ToString("yyyy-MM-dd HH:mm"),
            };
            Logger.Info($"{user.RealName} add comment ${model.Content}");
            ajaxResult.code = model.Id > 0 ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }

        /// <summary>
        /// 获取评论
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public AjaxResult GetComments(NameValueCollection paramValues)
        {
            int articleId = WebHelper.GetValueInt("aid", 0, paramValues);
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            int parentId = WebHelper.GetValueInt("pid", 0, paramValues);

            if (articleId < 0)
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.paramerror);
                return ajaxResult;
            }

            ajaxResult.code = (int)ResultCode.success;
            var filterStr = parentId > 0 ? $" AND ParentId={parentId}" : string.Empty;
            var commentList = GetList($"Status={(int)CommentStatus.normal} AND ArticleId={articleId} {filterStr}", pageSize, pageIndex, "*", "AddTime DESC");
            if (commentList != null && commentList.Count() > 0)
            {
                var userAvatars = new MemberBLL().GetList($"{string.Join(",", commentList.Select(x => x.UserId).ToArray())}"); //查询用户头像信息
                ajaxResult.data = commentList.Select(item =>
                {
                    var user = userAvatars.Where(x => x.Id == item.UserId)?.FirstOrDefault();
                    return new
                    {
                        item.Id,
                        item.Content,
                        item.ArticleId,
                        item.UserId,
                        UserName = user?.RealName,
                        UserAvatar = user?.Avatar,
                        AddTime = item.AddTime.ToString("yyyy-MM-dd HH:mm"),
                    };
                });
            }
            return ajaxResult;
        }
    }
}
