/**
 * Recommendation System Styles
 */

/* Card Styles */
.news-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.news-card .card-body {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
}

.news-title {
    height: 50px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-bottom: 10px;
}

.news-summary {
    height: 80px;
    overflow: hidden;
    margin-bottom: 15px;
    flex-grow: 1;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    font-size: 0.85rem;
}

.relevance-score {
    margin-top: 10px;
}

.relevance-score .progress {
    height: 5px;
    border-radius: 2px;
    background-color: #e9ecef;
}

.relevance-score .progress-bar {
    background-color: #007bff;
}

.relevance-text {
    display: block;
    text-align: right;
    margin-top: 3px;
}

/* Filter Panel */
.filter-panel {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Loading Indicator */
#loadingIndicator {
    padding: 30px;
    text-align: center;
}

#loadingIndicator .spinner-border {
    width: 3rem;
    height: 3rem;
    margin-bottom: 15px;
}

/* Recommendation Widget */
.recommendation-widget {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 15px;
    margin-bottom: 20px;
}

.widget-header {
    margin-bottom: 15px;
}

.widget-header h5 {
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.widget-header p {
    font-size: 0.85rem;
    margin-bottom: 0;
}

.recommendation-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.recommendation-list.horizontal {
    flex-direction: row;
    flex-wrap: wrap;
}

.recommendation-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s ease;
}

.recommendation-item:last-child {
    border-bottom: none;
}

.recommendation-item:hover {
    background-color: #f8f9fa;
}

.recommendation-item.horizontal {
    display: flex;
    align-items: center;
    width: 100%;
}

.recommendation-item.vertical {
    display: block;
}

.recommendation-title {
    margin-bottom: 5px;
    font-size: 1rem;
    line-height: 1.4;
}

.recommendation-meta {
    display: flex;
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.recommendation-source {
    margin-right: 10px;
}

.recommendation-score {
    margin-top: 5px;
}

.recommendation-score .progress {
    height: 3px;
}

.widget-footer {
    margin-top: 10px;
    text-align: right;
}

.more-link {
    font-size: 0.9rem;
}

.no-recommendations {
    text-align: center;
    padding: 20px;
    color: #6c757d;
}

.widget-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #6c757d;
}

.widget-loading .spinner-border {
    margin-right: 10px;
}

.widget-error {
    text-align: center;
    padding: 15px;
    color: #dc3545;
}

/* Animation Effects */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.news-card {
    animation: fadeIn 0.3s ease-out;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .news-title {
        height: auto;
        max-height: 50px;
    }
    
    .news-summary {
        height: auto;
        max-height: 80px;
    }
    
    .filter-panel .form-inline {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-panel .form-group {
        margin-bottom: 10px;
        width: 100%;
    }
    
    .recommendation-list.horizontal {
        flex-direction: column;
    }
}