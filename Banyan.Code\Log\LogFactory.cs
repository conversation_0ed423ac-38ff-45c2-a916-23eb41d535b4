﻿
using log4net;
using System;
using System.IO;
using System.Web;

namespace Banyan.Code
{
    public class LogFactory
    {
        static LogFactory()
        {
            try
            {
                FileInfo configFile;
                if (HttpContext.Current != null)
                {
                    // Web application context
                    configFile = new FileInfo(HttpContext.Current.Server.MapPath("/Configs/log4net.config"));
                }
                else
                {
                    // Test or console application context
                    string baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
                    string configPath = Path.Combine(baseDirectory, "Configs", "log4net.config");
                    if (!File.Exists(configPath))
                    {
                        // Fallback to a simple configuration for tests
                        log4net.Config.BasicConfigurator.Configure();
                        return;
                    }
                    configFile = new FileInfo(configPath);
                }
                log4net.Config.XmlConfigurator.Configure(configFile);
            }
            catch (Exception)
            {
                // Fallback to basic configuration if anything goes wrong
                log4net.Config.BasicConfigurator.Configure();
            }
        }
        public static Log GetLogger(Type type)
        {
            return new Log(LogManager.GetLogger(type));
        }
        public static Log GetLogger(string str)
        {
            return new Log(LogManager.GetLogger(str));
        }
    }
}
