-- 为UserProfile表添加InterestVector字段的数据库迁移脚本
-- 执行日期: 2025-07-31
-- 描述: 添加用户兴趣向量字段，用于存储1024维向量的JSON表示

USE [QLWL]
GO

-- 检查InterestVector列是否已存在
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[UserProfile]') AND name = 'InterestVector')
BEGIN
    -- 添加InterestVector列
    ALTER TABLE [dbo].[UserProfile]
    ADD [InterestVector] NVARCHAR(MAX) NULL

    PRINT '✓ 成功添加InterestVector列到UserProfile表'
END
ELSE
BEGIN
    PRINT '⚠ InterestVector列已存在于UserProfile表中'
END

-- 检查VectorUpdateTime列是否已存在
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[UserProfile]') AND name = 'VectorUpdateTime')
BEGIN
    -- 添加VectorUpdateTime列
    ALTER TABLE [dbo].[UserProfile]
    ADD [VectorUpdateTime] DATETIME NULL

    PRINT '✓ 成功添加VectorUpdateTime列到UserProfile表'
END
ELSE
BEGIN
    PRINT '⚠ VectorUpdateTime列已存在于UserProfile表中'
END

-- 添加InterestVector列注释
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[UserProfile]') AND name = 'InterestVector')
BEGIN
    EXEC sys.sp_addextendedproperty
        @name = N'MS_Description',
        @value = N'用户兴趣向量JSON（1024维向量）',
        @level0type = N'SCHEMA', @level0name = N'dbo',
        @level1type = N'TABLE', @level1name = N'UserProfile',
        @level2type = N'COLUMN', @level2name = N'InterestVector'

    PRINT '✓ 成功添加InterestVector列注释'
END

-- 添加VectorUpdateTime列注释
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[UserProfile]') AND name = 'VectorUpdateTime')
BEGIN
    EXEC sys.sp_addextendedproperty
        @name = N'MS_Description',
        @value = N'向量更新时间',
        @level0type = N'SCHEMA', @level0name = N'dbo',
        @level1type = N'TABLE', @level1name = N'UserProfile',
        @level2type = N'COLUMN', @level2name = N'VectorUpdateTime'

    PRINT '✓ 成功添加VectorUpdateTime列注释'
END

-- 创建索引以提高查询性能（可选）
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[UserProfile]') AND name = 'IX_UserProfile_UserId_Vector')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_UserProfile_UserId_Vector]
    ON [dbo].[UserProfile] ([UserId])
    INCLUDE ([InterestVector], [VectorUpdateTime])

    PRINT '✓ 成功创建UserProfile向量相关索引'
END
ELSE
BEGIN
    PRINT '⚠ UserProfile向量相关索引已存在'
END

-- 创建VectorUpdateTime索引以支持向量过期检查
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[UserProfile]') AND name = 'IX_UserProfile_VectorUpdateTime')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_UserProfile_VectorUpdateTime]
    ON [dbo].[UserProfile] ([VectorUpdateTime])
    WHERE [VectorUpdateTime] IS NOT NULL

    PRINT '✓ 成功创建VectorUpdateTime索引'
END
ELSE
BEGIN
    PRINT '⚠ VectorUpdateTime索引已存在'
END

PRINT '🎉 UserProfile表向量字段迁移完成！'

-- 验证迁移结果
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'UserProfile'
    AND COLUMN_NAME IN ('InterestVector', 'VectorUpdateTime')
ORDER BY COLUMN_NAME

GO
