﻿using System.Collections.Generic;

namespace Banyan.Apps
{
    #region 数据模型

    /// <summary>
    /// 新闻标签分析结果
    /// </summary>
    public class NewsTagAnalysis
    {
        /// <summary>
        /// 主要标签列表
        /// </summary>
        public List<NewsTag> MainTags { get; set; }

        /// <summary>
        /// 次要标签列表
        /// </summary>
        public List<NewsTag> SecondaryTags { get; set; }

        /// <summary>
        /// 语义关键词列表
        /// </summary>
        public List<NewsTag> SemanticKeywords { get; set; }
    }
}
#endregion