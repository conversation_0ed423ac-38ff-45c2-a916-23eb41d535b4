﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Aspose.Words;
using Aspose.Words.Tables;

namespace Banyan.Code
{
    public class WordDocumentTable
    { // https://www.cnblogs.com/elegant-dancer/p/3383594.html
        public WordDocumentTable(int PiTableID)
        {
            MiTableID = PiTableID;
        }

        public WordDocumentTable() { }
        public WordDocumentTable(int PiTableID, int PiColumnID)
        {
            MiTableID = PiTableID;
            MiColumnID = PiColumnID;
        }

        public WordDocumentTable(int PiTableID, int PiColumnID, int PiRowID)
        {
            MiTableID = PiTableID;
            MiColumnID = PiColumnID;
            MiRowID = PiRowID;
        }

        private int MiTableID = 0;

        public int TableID
        {
            get { return MiTableID; }
            set { MiTableID = value; }
        }

        private int MiRowID = 0;
        public int RowID
        {
            get { return MiRowID; }
            set { MiRowID = value; }
        }

        private int MiColumnID = 0;
        public int ColumnID
        {
            get { return MiColumnID; }
            set { MiColumnID = value; }
        }

        public List<WordDocumentTable> WordDocumentTables
        {
            get
            {
                List<WordDocumentTable> wordDocTable = new List<WordDocumentTable>();
                //Reads the data from the first Table of the document.    
                wordDocTable.Add(new WordDocumentTable(0));
                //Reads the data from the second table and its second column. 
                //This table has only one row.    
                wordDocTable.Add(new WordDocumentTable(1, 1));
                //Reads the data from third table, second row and second cell.    
                wordDocTable.Add(new WordDocumentTable(2, 1, 1));
                return wordDocTable;
            }
        }

        public void getDocument(byte[] PobjData)
        {
            using (MemoryStream LobjStream = new MemoryStream(PobjData))
            {
                Document LobjAsposeDocument = new Document(LobjStream);
            }
        }



        public Dictionary<string, string> ExtractTable(string wordInputPath)
        {
            Document doc = new Document(wordInputPath);
            var dic = ExtractTableHelper(doc);
            return dic;
        }
        public Dictionary<string, string> ExtractTableHelper(Document LobjAsposeDocument)
        {
            var table = (Table)LobjAsposeDocument.GetChild(NodeType.Table, 0, true);

            //NodeCollection LobjCells = table.GetChildNodes(NodeType.Cell, true);
            NodeCollection LobjRows =  table.GetChildNodes(NodeType.Row, true);
            var dic = new Dictionary<string, string>();
            for(var r = 0; r < LobjRows.Count; r++)
            {
                var row = ((Row)LobjRows[r]).Cells;
                dic.Add(row[0].ToString(SaveFormat.Text).Trim(), row[1].ToString(SaveFormat.Text).Trim());
            }
            return dic;
        }
        //    public void ExtractTableDataHelper(Document LobjAsposeDocument)
        //{

        //        Aspose.Words.Tables.Table table = (Aspose.Words.Tables.Table)
        //        LobjAsposeDocument.GetChild
        //        (NodeType.Table, wordDocTable.TableID, true);
        //        string cellData; // = table.Range.Text;

        //        if (wordDocTable.ColumnID > 0)
        //        {
        //            if (wordDocTable.RowID == 0)
        //            {
        //                NodeCollection LobjCells =
        //                table.GetChildNodes(NodeType.Cell, true);
        //                cellData = LobjCells[wordDocTable.ColumnID].ToTxt();
        //            }
        //            else
        //            {
        //                NodeCollection LobjRows =
        //                table.GetChildNodes(NodeType.Row, true);
        //                cellData = ((Row)(LobjRows[wordDocTable.RowID])).
        //                Cells[wordDocTable.ColumnID].ToTxt();
        //            }
        //            Console.WriteLine(String.Format("Data in Table {0},  Row { 1}, Column { 2} : { 3}    ",
        //                            wordDocTable.TableID,
        //                    wordDocTable.RowID,
        //                    wordDocTable.ColumnID,
        //                    cellData));
        //        }
        //}
    }
    
}
