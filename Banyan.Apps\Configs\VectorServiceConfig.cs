using System;

namespace Banyan.Apps.Configs
{
    /// <summary>
    /// 向量服务配置类
    /// 集中管理所有向量化相关的常量配置
    /// </summary>
    public static class VectorServiceConfig
    {
        #region 向量配置

        /// <summary>
        /// 向量维度
        /// </summary>
        public const int VECTOR_DIMENSION = 1024;

        /// <summary>
        /// 向量缓存天数
        /// </summary>
        public const int VECTOR_CACHE_DAYS = 30;

        /// <summary>
        /// 向量过期天数
        /// </summary>
        public const int VECTOR_EXPIRE_DAYS = 30;

        /// <summary>
        /// 向量相似度阈值
        /// </summary>
        public const double VECTOR_SIMILARITY_THRESHOLD = 0.5;

        #endregion

        #region AI分析配置

        /// <summary>
        /// AI模型名称 - 用户画像分析
        /// </summary>
        public const string AI_MODEL_NAME_PROFILE = "qwen3-30b-a3b-mlx";

        /// <summary>
        /// AI模型名称 - 新闻内容分析
        /// </summary>
        public const string AI_MODEL_NAME_NEWS = "deepseek-r1-0528";

        /// <summary>
        /// AI分析超时时间（秒）
        /// </summary>
        public const int AI_ANALYSIS_TIMEOUT = 30;

        /// <summary>
        /// AI分析最大重试次数
        /// </summary>
        public const int AI_ANALYSIS_MAX_RETRIES = 3;

        /// <summary>
        /// AI分析批量大小
        /// </summary>
        public const int AI_ANALYSIS_BATCH_SIZE = 10;

        /// <summary>
        /// AI分析超时时间（新闻）
        /// </summary>
        public const int AI_TIMEOUT_NEWS = 60;

        #endregion

        #region Embedding服务配置

        /// <summary>
        /// Embedding模型名称
        /// </summary>
        public const string EMBEDDING_MODEL_NAME = "text-embedding-bge-m3";

        /// <summary>
        /// Embedding服务超时时间（秒）
        /// </summary>
        public const int EMBEDDING_TIMEOUT = 30;

        /// <summary>
        /// Embedding服务最大重试次数
        /// </summary>
        public const int EMBEDDING_MAX_RETRIES = 3;

        /// <summary>
        /// Embedding服务API URL
        /// </summary>
        public const string EMBEDDING_API_URL = "https://llm.gaorongvc.cn/v1/embeddings";

        /// <summary>
        /// Embedding服务批量处理最大数量
        /// </summary>
        public const int EMBEDDING_BATCH_SIZE = 100;

        /// <summary>
        /// 是否启用真实Embedding服务（false则使用模拟）
        /// </summary>
        public const bool ENABLE_REAL_EMBEDDING = true;

        /// <summary>
        /// Embedding缓存天数
        /// </summary>
        public const int EMBEDDING_CACHE_DAYS = 30;

        #endregion

        #region 缓存配置

        /// <summary>
        /// 用户画像缓存天数
        /// </summary>
        public const int USER_PROFILE_CACHE_DAYS = 30;

        /// <summary>
        /// 标签统计缓存天数
        /// </summary>
        public const int TAG_STATS_CACHE_DAYS = 21;

        /// <summary>
        /// 热门标签缓存小时数
        /// </summary>
        public const int POPULAR_TAGS_CACHE_HOURS = 72;

        /// <summary>
        /// 是否启用向量缓存
        /// </summary>
        public const bool ENABLE_VECTOR_CACHE = true;

        #endregion

        #region 更新配置

        /// <summary>
        /// 画像过期天数
        /// </summary>
        public const int PROFILE_EXPIRE_DAYS = 21;

        /// <summary>
        /// 批量更新并发数
        /// </summary>
        public const int BATCH_UPDATE_CONCURRENCY = 5;

        /// <summary>
        /// 批量更新间隔（毫秒）
        /// </summary>
        public const int BATCH_UPDATE_INTERVAL = 1000;

        /// <summary>
        /// 向量更新间隔小时数
        /// </summary>
        public const int VECTOR_UPDATE_INTERVAL_HOURS = 72;

        #endregion

        #region 权重计算配置

        /// <summary>
        /// AI分析权重比例
        /// </summary>
        public const double AI_ANALYSIS_WEIGHT_RATIO = 0.6;

        /// <summary>
        /// 点击行为权重比例
        /// </summary>
        public const double CLICK_BEHAVIOR_WEIGHT_RATIO = 0.3;

        /// <summary>
        /// 最近交互权重比例
        /// </summary>
        public const double RECENT_INTERACTION_WEIGHT_RATIO = 0.1;

        /// <summary>
        /// 点击权重调整因子
        /// </summary>
        public const double CLICK_WEIGHT_ADJUSTMENT_FACTOR = 0.1;

        /// <summary>
        /// 最大点击权重
        /// </summary>
        public const double MAX_CLICK_WEIGHT = 0.3;

        #endregion

        #region 标签配置

        /// <summary>
        /// 最大推荐标签数
        /// </summary>
        public const int MAX_RECOMMENDATION_TAGS = 10;

        /// <summary>
        /// 标签权重阈值
        /// </summary>
        public const double TAG_WEIGHT_THRESHOLD = 0.1;

        #endregion

        #region 用户画像配置

        /// <summary>
        /// AI模型名称 - 用户画像分析（旧版本兼容）
        /// </summary>
        public const string AI_MODEL_NAME = "qwen3-30b-a3b-mlx";

        /// <summary>
        /// AI分析超时时间（用户画像）
        /// </summary>
        public const int AI_ANALYSIS_TIMEOUT_PROFILE = 30;
        public const int AI_MAX_RETRIES = 30;
        #endregion

        #region 监控配置

        /// <summary>
        /// 是否启用监控
        /// </summary>
        public const bool ENABLE_MONITORING = true;

        /// <summary>
        /// 监控间隔分钟数
        /// </summary>
        public const int MONITORING_INTERVAL_MINUTES = 60;

        /// <summary>
        /// 是否启用性能监控
        /// </summary>
        public const bool ENABLE_PERFORMANCE_MONITORING = true;

        /// <summary>
        /// 是否启用向量监控
        /// </summary>
        public const bool ENABLE_VECTOR_MONITORING = true;

        /// <summary>
        /// 向量监控间隔分钟数
        /// </summary>
        public const int VECTOR_MONITORING_INTERVAL_MINUTES = 60;

        /// <summary>
        /// 是否启用向量性能监控
        /// </summary>
        public const bool ENABLE_VECTOR_PERFORMANCE_MONITORING = true;

        #endregion

        #region 错误处理配置

        /// <summary>
        /// 是否启用回退机制
        /// </summary>
        public const bool ENABLE_FALLBACK = true;

        /// <summary>
        /// 回退超时时间（秒）
        /// </summary>
        public const int FALLBACK_TIMEOUT = 5;

        /// <summary>
        /// 最大错误重试次数
        /// </summary>
        public const int MAX_ERROR_RETRIES = 3;

        #endregion

        #region 批量处理配置

        /// <summary>
        /// 批量处理大小
        /// </summary>
        public const int BATCH_SIZE = 100;

        /// <summary>
        /// 批量处理延迟（毫秒）
        /// </summary>
        public const int BATCH_DELAY_MS = 1000;

        #endregion

        #region 向量化状态

        /// <summary>
        /// 向量化状态：待处理
        /// </summary>
        public const int VECTOR_STATUS_PENDING = 0;

        /// <summary>
        /// 向量化状态：成功
        /// </summary>
        public const int VECTOR_STATUS_SUCCESS = 1;

        /// <summary>
        /// 向量化状态：失败
        /// </summary>
        public const int VECTOR_STATUS_FAILED = 2;

        #endregion
    }
}