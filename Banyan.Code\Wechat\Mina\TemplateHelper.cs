﻿using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Banyan.Code
{
    /// <summary>
    /// 小程序消息模板辅助类
    /// </summary>
    public class TemplateHelper
    {
        public bool Add()
        {
            return true;
        }

        public bool List()
        {
            return true;
        }

        public bool Get()
        {
            return true;
        }

        public bool Delete()
        {
            return true;
        }

        /// <summary>
        /// 拼接发送消息体
        /// </summary>
        /// <param name="openId"></param>
        /// <param name="templateId"></param>
        /// <param name="formId"></param>
        /// <param name="data"></param>
        /// <param name="pageUrl"></param>
        /// <returns></returns>
        public static string ConcatSendData(string openId, string templateId, string formId, string[] data, string pageUrl = "pages/", string color = "#173177", string emphasisKeyword = "")
        {
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < data.Count(); i++)
            {
                stringBuilder.Append("\"keyword" + (i + 1) + "\":{\"value\": \"" + data[i] + "\",\"color\": \"#333333\"}");
                if (i < data.Count() - 1)
                {
                    stringBuilder.Append(",");
                }
            }
            return "{\"touser\": \"" + openId + "\",\"template_id\": \"" + templateId + "\", \"page\": \"" + pageUrl + "\",\"form_id\": \"" + formId + "\",\"data\": {" + stringBuilder.ToString() + "}}";
        }

        /// <summary>
        /// 发送模板消息
        /// </summary>
        /// <param name="openId">接收用户的opneid</param>
        /// <param name="templateId">模板消息id</param>
        /// <param name="formId">formid或prepay_id</param>
        /// <param name="data">发送内容</param>
        /// <param name="color">模板内容字体颜色</param>
        /// <param name="emphasisKeyword">需放大的关键词</param>
        /// <returns></returns>
        public async Task<bool> Send(string openId, string templateId, string formId, string[] data, string accessToken)
        {
            return await Task.Run(() =>
            {
                bool reuslt = false;

                string apiUrl = string.Format(ApiUrls.TemplateSend, accessToken);
                string postData = ConcatSendData(openId, templateId, formId, data);
                string resultBack = HttpMethods.HttpPost(apiUrl, postData);
                if (!resultBack.Contains("ok"))
                {
                    //记录日志
                }

                return reuslt;
            });
        }

        /// <summary>
        /// 消息模板推送
        /// </summary>
        /// <param name="openId"></param>
        /// <param name="templetId"></param>
        /// <param name="formId"></param>
        /// <param name="values"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        public static bool SendSync(string openId, string templetId, string formId, string[] values, string token, string pageUrl = "")
        {
            string apiUrl = string.Format(ApiUrls.TemplateSend, token);
            string postData = ConcatSendData(openId, templetId, formId, values, pageUrl);
            string resultBack = HttpMethods.HttpPost(apiUrl, postData);


            if (!resultBack.Contains("ok"))
            {
                Console.WriteLine(resultBack);
                return false;
            }

            return true;
        }
    }
}
