<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AI.WindowsServer</name>
    </assembly>
    <members>
        <member name="T:Microsoft.ApplicationInsights.WindowsServer.AzureInstanceMetadataTelemetryModule">
            <summary>
            A telemetry module that adds Azure instance metadata context information to the heartbeat, if it is available.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.AzureInstanceMetadataTelemetryModule.Initialize(Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.ApplicationInsights.WindowsServer.AzureInstanceMetadataTelemetryModule" /> class.
            Creates a heartbeat property collector that obtains and inserts data from the Azure Instance
            Metadata service if it is present and available to the currently running process. If it is not
            present no added IMS data is added to the heartbeat.
            </summary>
            <param name="unused">Unused parameter for this TelemetryModule.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.WindowsServer.AzureWebAppRoleEnvironmentTelemetryInitializer">
            <summary>
            A telemetry initializer that will gather Azure Web App Role Environment context information.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.WindowsServer.AzureWebAppRoleEnvironmentTelemetryInitializer.WebAppHostNameEnvironmentVariable">
            <summary>
            Azure Web App Hostname. This will include the deployment slot, but will be 
            same across instances of same slot. Marked as internal to support testing.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.WindowsServer.AzureWebAppRoleEnvironmentTelemetryInitializer.WebAppSuffix">
            <summary>Predefined suffix for Azure Web App Hostname.</summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.AzureWebAppRoleEnvironmentTelemetryInitializer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.ApplicationInsights.WindowsServer.AzureWebAppRoleEnvironmentTelemetryInitializer" /> class.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.AzureWebAppRoleEnvironmentTelemetryInitializer.Initialize(Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Initializes <see cref="T:Microsoft.ApplicationInsights.Channel.ITelemetry" /> role and node context information.
            </summary>
            <param name="telemetry">The telemetry to initialize.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.AzureWebAppRoleEnvironmentTelemetryInitializer.Dispose">
            <summary>
            Remove our event handler from the environment variable monitor.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.WindowsServer.BuildInfoConfigComponentVersionTelemetryInitializer">
            <summary>
            A telemetry context initializer that will set component context version on the base of BuildInfo.config information.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.WindowsServer.BuildInfoConfigComponentVersionTelemetryInitializer.version">
            <summary>
            The version for this component.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.BuildInfoConfigComponentVersionTelemetryInitializer.Initialize(Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Initializes version of the telemetry item with the version obtained from build info if it is available.
            </summary>
            <param name="telemetry">The telemetry context to initialize.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.BuildInfoConfigComponentVersionTelemetryInitializer.LoadBuildInfoConfig">
            <summary>
            Loads BuildInfo.config and returns XElement.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.BuildInfoConfigComponentVersionTelemetryInitializer.GetVersion">
            <summary>
            Gets the version for the current application. If the version cannot be found, we will return the passed in default.
            </summary>
            <returns>The extracted data.</returns>
        </member>
        <member name="T:Microsoft.ApplicationInsights.WindowsServer.DeveloperModeWithDebuggerAttachedTelemetryModule">
            <summary>
            Telemetry module that sets developer mode to true when is not already set AND managed debugger is attached.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.WindowsServer.DeveloperModeWithDebuggerAttachedTelemetryModule.IsDebuggerAttached">
            <summary>
            Function that checks whether debugger is attached with implementation that can be replaced by unit test code.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.DeveloperModeWithDebuggerAttachedTelemetryModule.Initialize(Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration)">
            <summary>
            Gives the opportunity for this telemetry module to initialize configuration object that is passed to it.
            </summary>
            <param name="configuration">Configuration object.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.WindowsServer.DomainNameRoleInstanceTelemetryInitializer">
            <summary>
            Obsolete. A telemetry context initializer that used to populate role instance name. Preserved for backward compatibility. 
            Note that role instance will still be populated with the machine name as in the previous versions.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.DomainNameRoleInstanceTelemetryInitializer.Initialize(Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Obsolete method.
            </summary>
            <param name="telemetry">The telemetry to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.WindowsServer.AppServicesHeartbeatTelemetryModule">
            <summary>
            Provides default values for the heartbeat feature of Application Insights that
            are specific to Azure App Services (Web Apps, Functions, etc...).
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.WindowsServer.AppServicesHeartbeatTelemetryModule.WebHeartbeatPropertyNameEnvVarMap">
            <summary>
            Environment variables and the Application Insights heartbeat field names that accompany them.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.AppServicesHeartbeatTelemetryModule.#ctor">
            <summary>
            Initializes a new instance of the<see cref="T:Microsoft.ApplicationInsights.WindowsServer.AppServicesHeartbeatTelemetryModule" /> class.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.AppServicesHeartbeatTelemetryModule.#ctor(Microsoft.ApplicationInsights.Extensibility.Implementation.Tracing.IHeartbeatPropertyManager)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.ApplicationInsights.WindowsServer.AppServicesHeartbeatTelemetryModule" /> class. This is
            internal, and allows for overriding the Heartbeat Property Manager to test this module with.
            </summary>
            <param name="hbeatPropManager">The heartbeat property manager to use when setting/updating env var values.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.AppServicesHeartbeatTelemetryModule.Initialize(Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration)">
            <summary>
            Initialize the default heartbeat provider for Azure App Services. This module
            looks for specific environment variables and sets them into the heartbeat 
            properties for Application Insights, if they exist.
            </summary>
            <param name="configuration">Unused parameter.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.AppServicesHeartbeatTelemetryModule.UpdateHeartbeatWithAppServiceEnvVarValues">
            <summary>
            Signal the AppServicesHeartbeatTelemetryModule to update the values of the 
            Environment variables we use in our heartbeat payload.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.AppServicesHeartbeatTelemetryModule.Dispose">
            <summary>
            Remove our event handler from the environment variable monitor.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.WindowsServer.Implementation.AppServiceEnvironmentVariableMonitor">
            <summary>
            Utility to monitor the value of environment variables which may change 
            during the run of an application. Checks the environment variables 
            at regular set intervals.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.Implementation.AppServiceEnvironmentVariableMonitor.#ctor">
            <summary>
            Prevents a default instance of the <see cref="T:Microsoft.ApplicationInsights.WindowsServer.Implementation.AppServiceEnvironmentVariableMonitor" /> class from being created.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.WindowsServer.Implementation.AzureComputeMetadataHeartbeatPropertyProvider.ExpectedAzureImsFields">
            <summary>
            Expected fields extracted from Azure IMS to add to the heartbeat properties. 
            Set as internal for testing.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.WindowsServer.Implementation.AzureComputeMetadataHeartbeatPropertyProvider.isAzureMetadataCheckCompleted">
            <summary>
            Flags that will tell us whether or not Azure VM metadata has been attempted to be gathered or not, and
            if we should even attempt to look for it in the first place. 
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.Implementation.AzureComputeMetadataHeartbeatPropertyProvider.#ctor(Microsoft.ApplicationInsights.WindowsServer.Implementation.IAzureMetadataRequestor)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.ApplicationInsights.WindowsServer.Implementation.AzureComputeMetadataHeartbeatPropertyProvider"/> class.
            </summary>
            <param name="azureInstanceMetadataHandler">For testing: Azure metadata request handler to use when requesting data from azure specifically. If left as null, an instance of AzureMetadataRequestor is used.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.Implementation.AzureComputeMetadataHeartbeatPropertyProvider.SetDefaultPayloadAsync(Microsoft.ApplicationInsights.Extensibility.Implementation.Tracing.IHeartbeatPropertyManager)">
            <summary>
            Add all enabled, present Azure IMS fields to the heartbeat properties.
            </summary>
            <param name="provider">Heartbeat provider to set the properties on.</param>
            <returns>True if any property values were successfully set, false if none were set.</returns>
        </member>
        <member name="F:Microsoft.ApplicationInsights.WindowsServer.Implementation.AzureMetadataRequestor.AzureImsApiVersion">
            <summary>
            Azure Instance Metadata Service exists on a single non-routable IP on machines configured
            by the Azure Resource Manager. https://go.microsoft.com/fwlink/?linkid=864683 .
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.WindowsServer.Implementation.AzureMetadataRequestor.AzureImsRequestTimeout">
            <summary>
            Default timeout for the web requests made to obtain Azure IMS data. Internal to expose to tests.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.WindowsServer.Implementation.AzureMetadataRequestor.azureIMSRequestor">
            <summary>
            Private function for mocking out the actual call to IMS in unit tests. Available to internal only.
            </summary>
            parameter sent to the func is a string representing the Uri to request Azure IMS data from.
            <returns>An instance of AzureInstanceComputeMetadata or null.</returns>
        </member>
        <member name="P:Microsoft.ApplicationInsights.WindowsServer.Implementation.AzureMetadataRequestor.BaseAimsUri">
            <summary>
            Gets or sets the base URI for the Azure Instance Metadata service. Internal to allow overriding in test.
            </summary>
            <remarks>
            At this time, this service does not support https. We should monitor their website for more information. https://docs.microsoft.com/azure/virtual-machines/windows/instance-metadata-service .
            </remarks>
        </member>
        <member name="T:Microsoft.ApplicationInsights.WindowsServer.Implementation.DataContracts.AzureInstanceComputeMetadata">
            <summary>
            Class representing the returned structure from an Azure Instance Metadata request
            for Compute information.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.Implementation.DataContracts.AzureInstanceComputeMetadata.VerifyExpectedValue(System.String)">
            <summary>
            Because the Azure IMS is on a non-routable IP we need to do some due diligence in our accepting
            values returned from it. This method takes the fieldname and value received for that field, and
            if we can test that value against known limitations of that field we do so here. If the test fails
            we return the empty string, otherwise we return the string given.
            </summary>
            <param name="fieldName">Name of the field acquired from the call to Azure IMS.</param>
            <returns>The value of the field, verified, or the empty string.</returns>
        </member>
        <member name="T:Microsoft.ApplicationInsights.WindowsServer.Implementation.EnvironmentVariableMonitor">
            <summary>
            Utility to monitor the value of environment variables which may change 
            during the run of an application. Checks the environment variables 
            intermittently.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.Implementation.EnvironmentVariableMonitor.#ctor(System.Collections.Generic.IEnumerable{System.String},System.TimeSpan)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.ApplicationInsights.WindowsServer.Implementation.EnvironmentVariableMonitor" /> class.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.Implementation.EnvironmentVariableMonitor.GetCurrentEnvironmentVariableValue(System.String,System.String@)">
            <summary>
            Get the latest value assigned to an environment variable.
            </summary>
            <param name="envVarName">Name of the environment variable to acquire.</param>
            <param name="value">Current cached value of the environment variable.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.Implementation.EnvironmentVariableMonitor.OnEnvironmentVariableUpdated">
            <summary>
            Method to update subscribers whenever a change in the tracked environment variables is detected.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.Implementation.EnvironmentVariableMonitor.CheckVariablesIntermittent(System.Object)">
            <summary>
            Check and update the variables being tracked and if any updates are detected,
            raise the OnEnvironmentVariableUpdated event. Restart the timer to check again
            in the configured interval once complete.
            </summary>
            <param name="state">Variable left unused in this implementation of TimerCallback.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.WindowsServer.Implementation.WindowsServerEventSource">
            <summary>
            ETW EventSource tracing class.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.WindowsServer.Implementation.WindowsServerEventSource.Log">
            <summary>
            Instance of the WindowsServerEventSource class.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.WindowsServer.Implementation.WindowsServerEventSource.Keywords">
            <summary>
            Keywords for the PlatformEventSource. Those keywords should match keywords in Core.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.WindowsServer.Implementation.WindowsServerEventSource.Keywords.UserActionable">
            <summary>
            Key word for user actionable events.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.WindowsServer.UnobservedExceptionTelemetryModule">
            <summary>
            The module subscribed to TaskScheduler.UnobservedTaskException to send exceptions to ApplicationInsights.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.UnobservedExceptionTelemetryModule.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.ApplicationInsights.WindowsServer.UnobservedExceptionTelemetryModule" /> class.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.UnobservedExceptionTelemetryModule.Initialize(Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration)">
            <summary>
            Initializes the telemetry module.
            </summary>
            <param name="configuration">Telemetry Configuration used for creating TelemetryClient for sending exceptions to ApplicationInsights.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.WindowsServer.UnobservedExceptionTelemetryModule.Dispose">
            <summary>
            Disposing TaskSchedulerOnUnobservedTaskException instance.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.AppMapCorrelationEventSource">
            <summary>
            ETW EventSource tracing class.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.AppMapCorrelationEventSource.Keywords">
            <summary>
            Keywords for the <see cref="T:Microsoft.ApplicationInsights.Common.AppMapCorrelationEventSource"/>.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.AppMapCorrelationEventSource.Keywords.UserActionable">
            <summary>
            Key word for user actionable events.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.AppMapCorrelationEventSource.Keywords.Diagnostics">
            <summary>
            Key word for diagnostics events.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.ArrayExtensions.Empty``1">
            <summary>
            Returns an empty array.
            </summary>
            <remarks>
            Array.Empty() was added to Net Framework in 4.6
            This adds support for Net45.
            </remarks>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.ConditionalWeakTableExtensions">
            <summary>
            Extension methods for the ConditionalWeakTable class.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.ConditionalWeakTableExtensions.AddIfNotExists``2(System.Runtime.CompilerServices.ConditionalWeakTable{``0,``1},``0,``1)">
            <summary>
            Check if a key exists before adding the key/value pair.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.ExceptionUtilities">
            <summary>
            Utility functions for dealing with exceptions.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.ExceptionUtilities.GetExceptionDetailString(System.Exception)">
            <summary>
            Get the string representation of this Exception with special handling for AggregateExceptions.
            </summary>
            <param name="ex">The exception to convert to a string.</param>
            <returns>The detailed string version of the provided exception.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.GuidExtensions.ToStringInvariant(System.Guid,System.String)">
            <summary>
            Overload for Guid.ToString(). 
            </summary>
            <remarks>
            This method encapsulates the language switch for NetStandard and NetFramework and resolves the error "The behavior of guid.ToStrinc() could vary based on the current user's locale settings".
            </remarks>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.HeadersUtilities">
            <summary>
            Generic functions that can be used to get and set Http headers.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.HeadersUtilities.GetHeaderKeyValue(System.Collections.Generic.IEnumerable{System.String},System.String)">
            <summary>
            Get the key value from the provided HttpHeader value that is set up as a comma-separated list of key value pairs. Each key value pair is formatted like (key)=(value).
            </summary>
            <param name="headerValues">The header values that may contain key name/value pairs.</param>
            <param name="keyName">The name of the key value to find in the provided header values.</param>
            <returns>The first key value, if it is found. If it is not found, then null.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.HeadersUtilities.UpdateHeaderWithKeyValue(System.Collections.Generic.IEnumerable{System.String},System.String,System.String)">
            <summary>
            Given the provided list of header value strings, return a list of key name/value pairs
            with the provided keyName and keyValue. If the initial header value strings contains
            the key name, then the original key value should be replaced with the provided key
            value. If the initial header value strings don't contain the key name, then the key
            name/value pair should be added to the list and returned.
            </summary>
            <param name="headerValues">The existing header values that the key/value pair should be added to.</param>
            <param name="keyName">The name of the key to add.</param>
            <param name="keyValue">The value of the key to add.</param>
            <returns>The result of setting the provided key name/value pair into the provided headerValues.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.HeadersUtilities.SanitizeString(System.String)">
            <summary>
            Http Headers only allow Printable US-ASCII characters.
            Remove all other characters.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.Internal.ExperimentalConstants">
            <summary>
            These values are listed to guard against malicious injections by limiting the max size allowed in an HTTP Response.
            These max limits are intentionally exaggerated to allow for unexpected responses, while still guarding against unreasonably large responses.
            Example: While a 32 character response may be expected, 50 characters may be permitted while a 10,000 character response would be unreasonable and malicious.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.Internal.ExperimentalConstants.DeferRequestTrackingProperties">
            <summary>
            This is used to defer setting properties on RequestTelemetry until after Sampling.
            QuickPulse expects these properties so we have to set them here as well.
            Used to set QuickPulseTelemetryProcessor.EvaluateDisabledTrackingProperties and RequestTrackingTelemetryModule.DisableTrackingProperties.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants">
            <summary>
            These values are listed to guard against malicious injections by limiting the max size allowed in an HTTP Response.
            These max limits are intentionally exaggerated to allow for unexpected responses, while still guarding against unreasonably large responses.
            Example: While a 32 character response may be expected, 50 characters may be permitted while a 10,000 character response would be unreasonable and malicious.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants.AppIdMaxLength">
            <summary>
            Max length of AppId allowed in response from Breeze.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants.RequestHeaderMaxLength">
            <summary>
            Max length of incoming Request Header value allowed.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants.ContextHeaderKeyMaxLength">
            <summary>
            Max length of context header key.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants.ContextHeaderValueMaxLength">
            <summary>
            Max length of context header value.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants.TraceParentHeaderMaxLength">
            <summary>
            Max length of traceparent header value.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants.TraceStateHeaderMaxLength">
            <summary>
            Max length of tracestate header value string.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants.TraceStateMaxPairs">
            <summary>
            Max number of key value pairs in the tracestate header.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants.QuickPulseResponseHeaderMaxLength">
            <summary>
            Max length of incoming Response Header value allowed.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.Internal.StringUtilities">
            <summary>
            Generic functions to perform common operations on a string.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.Internal.StringUtilities.EnforceMaxLength(System.String,System.Int32)">
            <summary>
            Check a strings length and trim to a max length if needed.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.Internal.StringUtilities.GenerateTraceId">
            <summary>
            Generates random trace Id as per W3C Distributed tracing specification.
            https://github.com/w3c/distributed-tracing/blob/master/trace_context/HTTP_HEADER_FORMAT.md#trace-id .
            </summary>
            <returns>Random 16 bytes array encoded as hex string.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.Internal.StringUtilities.GenerateSpanId">
            <summary>
            Generates random span Id as per W3C Distributed tracing specification.
            https://github.com/w3c/distributed-tracing/blob/master/trace_context/HTTP_HEADER_FORMAT.md#span-id .
            </summary>
            <returns>Random 8 bytes array encoded as hex string.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.Internal.StringUtilities.FormatRequestId(System.String,System.String)">
            <summary>
            Formats trace Id and span Id into valid Request-Id: |trace.span.
            </summary>
            <param name="traceId">Trace Id.</param>
            <param name="spanId">Span id.</param>
            <returns>valid Request-Id.</returns>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.PropertyFetcher">
            <summary>
            Efficient implementation of fetching properties of anonymous types with reflection.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.PropertyFetcher.PropertyFetch.FetcherForProperty(System.Reflection.PropertyInfo)">
            <summary>
            Create a property fetcher from a .NET Reflection PropertyInfo class that
            represents a property of a particular type.  
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.PropertyFetcher.PropertyFetch.Fetch(System.Object)">
            <summary>
            Given an object, fetch the property that this propertyFetch represents. 
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.RequestResponseHeaders">
            <summary>
            Header names for requests / responses.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.RequestResponseHeaders.RequestContextHeader">
            <summary>
            Request-Context header.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.RequestResponseHeaders.RequestContextCorrelationSourceKey">
            <summary>
            Source key in the request context header that is added by an application while making http requests and retrieved by the other application when processing incoming requests.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.RequestResponseHeaders.RequestContextCorrelationTargetKey">
            <summary>
            Target key in the request context header that is added to the response and retrieved by the calling application when processing incoming responses.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.RequestResponseHeaders.StandardParentIdHeader">
            <summary>
            Legacy parent Id header.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.RequestResponseHeaders.StandardRootIdHeader">
            <summary>
            Legacy root id header.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.RequestResponseHeaders.RequestIdHeader">
            <summary>
            Standard Request-Id Id header.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.RequestResponseHeaders.CorrelationContextHeader">
            <summary>
            Standard Correlation-Context header.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.RequestResponseHeaders.AccessControlExposeHeadersHeader">
            <summary>
            Access-Control-Expose-Headers header indicates which headers can be exposed as part of the response by listing their names.
            Should contain Request-Context value that will allow reading Request-Context in JavaScript SDK on Browser side.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.WebHeaderCollectionExtensions">
            <summary>
            WebHeaderCollection extension methods.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.WebHeaderCollectionExtensions.GetNameValueHeaderValue(System.Collections.Specialized.NameValueCollection,System.String,System.String)">
            <summary>
            For the given header collection, for a given header of name-value type, find the value of a particular key.
            </summary>
            <param name="headers">Header collection.</param>
            <param name="headerName">Name of the header in the collection.</param>
            <param name="keyName">Desired key of the key-value list.</param>
            <returns>Value against the given parameters.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.WebHeaderCollectionExtensions.GetNameValueCollectionFromHeader(System.Collections.Specialized.NameValueCollection,System.String)">
            <summary>
            For the given header collection, for a given header of name-value type, return list of KeyValuePairs.
            </summary>
            <param name="headers">Header collection.</param>
            <param name="headerName">Name of the header in the collection.</param>
            <returns>List of KeyValuePairs in the given header.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.WebHeaderCollectionExtensions.SetNameValueHeaderValue(System.Collections.Specialized.NameValueCollection,System.String,System.String,System.String)">
            <summary>
            For the given header collection, adds KeyValuePair to header.
            </summary>
            <param name="headers">Header collection.</param>
            <param name="headerName">Name of the header that is to contain the name-value pair.</param>
            <param name="keyName">Name in the name value pair.</param>
            <param name="value">Value in the name value pair.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.WebHeaderCollectionExtensions.SetHeaderFromNameValueCollection(System.Collections.Specialized.NameValueCollection,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            For the given header collection, sets the header value based on the name value format.
            </summary>
            <param name="headers">Header collection.</param>
            <param name="headerName">Name of the header that is to contain the name-value pair.</param>
            <param name="keyValuePairs">List of KeyValuePairs to format into header.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.WebHeaderCollectionExtensions.GetHeaderValue(System.Collections.Specialized.NameValueCollection,System.String,System.Int32,System.Int32)">
            <summary>
            For the given header collection, for a given header name, returns collection of header values.
            </summary>
            <param name="headers">Header collection.</param>
            <param name="headerName">Name of the header in the collection.</param>
            <param name="maxStringLength">Maximum allowed header length.</param>
            <param name="maxItems">Maximum allowed number comma separated values in the header.</param>
            <returns>List of comma separated values in the given header.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.WebHeaderCollectionExtensions.ReadActivityBaggage(System.Collections.Specialized.NameValueCollection,System.Diagnostics.Activity)">
            <summary>
            Reads Correlation-Context and populates it on Activity.Baggage following https://github.com/dotnet/corefx/blob/master/src/System.Diagnostics.DiagnosticSource/src/HttpCorrelationProtocol.md#correlation-context.
            Use this method when you want force parsing Correlation-Context is absence of Request-Id or traceparent. 
            </summary>
            <param name="headers">Header collection.</param>
            <param name="activity">Activity to populate baggage on.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants">
            <summary>
            W3C constants.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.TraceParentHeader">
            <summary>
            W3C traceparent header name.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.TraceStateHeader">
            <summary>
            W3C tracestate header name.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.ApplicationIdTraceStateField">
            <summary>
            Name of the field that carry ApplicationInsights application Id in the tracestate header under az key.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.AzureTracestateNamespace">
            <summary>
            Name of the field that carry Azure-specific states in the tracestate header.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.TracestateAzureSeparator">
            <summary>
            Separator between Azure namespace values.
            </summary>
        </member>
    </members>
</doc>
