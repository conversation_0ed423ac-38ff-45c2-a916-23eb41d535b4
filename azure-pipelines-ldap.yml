trigger:
- ldap


variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'

jobs:
- job: build
  pool:
    vmImage: 'windows-latest'

  steps:
  # - task: NuGetToolInstaller@1
  # - task: NuGetCommand@2
  #   inputs:
  #     restoreSolution: '$(solution)'

  - task: VSBuild@1
    inputs:
      solution: '$(solution)'
      msbuildArgs: '/p:DeployOnBuild=true /p:WebPublishMethod=Package /p:PackageAsSingleFile=true /p:SkipInvalidConfigurations=true /p:PackageLocation="$(build.artifactStagingDirectory)"'
      platform: '$(buildPlatform)'
      configuration: '$(buildConfiguration)'
  - task: PublishPipelineArtifact@1
    inputs:
      targetPath: '$(Pipeline.Workspace)' 
      artifactName:  invest

#  - task: VSTest@2
#    inputs:
#      platform: '$(buildPlatform)'
#      configuration: '$(buildConfiguration)'

# - deployment: deploy
#   dependsOn: build
#   pool:
#     vmImage: 'windows-latest'
#   environment: 'test-environment.BANYANVC'
#   strategy: 
#     runOnce:
#       deploy:
#         steps:
#         - script: echo deploy
#       on:
#         success:
#           steps:
#           - script: echo succeed
#         failure:
#           steps: 
#           - script: echo failed
        
