# RecordNewsView 推荐参数功能测试

## 功能概述

为 `RecordNewsView` 方法增加了 `recommend` 参数，用于记录用户查看的新闻是否来自推荐系统。

## 修改内容

### 1. IndexController.News 方法
- **文件**: `Banyan.Web\Controllers\IndexController.cs`
- **修改**: 添加了 `bool recommend = false` 参数
- **功能**: 接收前端传递的推荐标识

### 2. RecordNewsView 方法
- **文件**: `Banyan.Web\Controllers\IndexController.cs`
- **修改**:
  - 添加了 `bool recommend = false` 参数
  - 实现了具体的记录逻辑
  - 当 `recommend=true` 时，额外更新推荐记录的阅读状态

### 3. RecommendationController.ViewNews 方法
- **文件**: `Banyan.Web\Controllers\RecommendationController.cs`
- **修改**: 重定向到新闻详情页面时添加 `recommend=true` 参数

### 4. EmailDigestService.GenerateTrackingUrl 方法
- **文件**: `Banyan.Apps\EmailDigestService.cs`
- **修改**: 修改邮件中的跟踪链接，使其指向 `/Recommendation/ViewNews` 而不是直接的新闻详情页面
- **功能**: 确保从邮件点击的推荐新闻也能被正确标记为来自推荐

## 工作流程

### 普通新闻查看流程
1. 用户点击普通新闻链接
2. 调用 `IndexController.News(id, highlight, recommend=false)`
3. 调用 `RecordNewsView(userId, newsId, recommend=false)`
4. 记录用户阅读行为，但不更新推荐状态

### 推荐新闻查看流程
1. 用户点击推荐新闻链接（网页或邮件）
2. 调用 `RecommendationController.ViewNews(id, source)`
3. 记录点击行为和反馈
4. 重定向到 `IndexController.News(id, recommend=true)`
5. 调用 `RecordNewsView(userId, newsId, recommend=true)`
6. 记录用户阅读行为
7. 额外查找并更新对应推荐记录的 `IsRead` 状态

### 邮件推荐新闻查看流程
1. 用户在邮件中点击推荐新闻链接
2. 链接指向 `/Recommendation/ViewNews/{newsId}?source=email`
3. 执行与网页推荐相同的流程
4. 确保邮件来源的推荐也被正确标记和跟踪

## 技术实现细节

### RecordNewsView 方法逻辑
```csharp
private void RecordNewsView(int userId, int newsId, bool recommend = false)
{
    // 1. 记录基本的用户阅读行为
    var newsVectorSearch = new NewsVectorSearch();
    await newsVectorSearch.RecordUserReadNewsAsync(userId, newsId);
    
    // 2. 如果来自推荐，额外处理推荐相关逻辑
    if (recommend)
    {
        var recommendationBLL = new NewsRecommendationsBLL();
        var recommendations = await recommendationBLL.GetUserRecommendationsAsync(userId, 100, true);
        var recommendation = recommendations.Find(r => r.NewsId == newsId);
        
        if (recommendation != null)
        {
            // 更新推荐的阅读状态
            await recommendationBLL.UpdateReadStatusAsync(recommendation.Id, true);
        }
    }
}
```

## 测试场景

### 测试用例 1: 普通新闻查看
- **URL**: `/Index/News?id=123`
- **预期**: `recommend=false`，只记录基本阅读行为

### 测试用例 2: 推荐新闻查看
- **URL**: `/Recommendation/ViewNews/123`
- **预期**: 
  1. 记录点击行为
  2. 重定向到 `/Index/News?id=123&recommend=true`
  3. `recommend=true`，记录阅读行为并更新推荐状态

### 测试用例 3: 直接访问带推荐参数的新闻
- **URL**: `/Index/News?id=123&recommend=true`
- **预期**: `recommend=true`，记录阅读行为并更新推荐状态

## 数据库影响

### NewsRecommendations 表
- `IsRead` 字段会在用户查看推荐新闻时被更新为 `true`

### 用户行为记录
- 通过 `NewsVectorSearch.RecordUserReadNewsAsync` 记录
- 通过 `EngagementTracker.TrackViewAsync` 记录
- 异步更新用户兴趣画像

## 日志记录

系统会记录以下日志：
- 用户查看新闻的基本信息（包括是否来自推荐）
- 推荐状态更新的结果
- 任何异常情况的错误日志

## 兼容性

- 所有现有的新闻查看功能保持不变
- `recommend` 参数为可选参数，默认值为 `false`
- 不会影响现有的前端代码和链接

## 使用示例

### 前端JavaScript调用示例
```javascript
// 普通新闻链接（不来自推荐）
window.open('/Index/News?id=123');

// 推荐新闻链接（来自推荐）
window.open('/Index/News?id=123&recommend=true');

// 通过推荐控制器访问（推荐）
window.open('/Recommendation/ViewNews/123');
```

### 后端控制器调用示例
```csharp
// 在其他控制器中重定向到新闻详情页面
// 普通新闻
return RedirectToAction("News", "Index", new { id = newsId });

// 推荐新闻
return RedirectToAction("News", "Index", new { id = newsId, recommend = true });
```

### 邮件模板链接示例
```html
<!-- 邮件中的推荐新闻链接 -->
<a href="https://ims.gaorongvc.com/Recommendation/ViewNews/123?source=email">阅读全文</a>
```

## 数据流程图

```
用户点击推荐新闻
    ↓
RecommendationController.ViewNews
    ↓
记录点击行为 (EngagementTracker.TrackClickAsync)
    ↓
重定向到 IndexController.News (recommend=true)
    ↓
RecordNewsView (recommend=true)
    ↓
┌─────────────────────┬─────────────────────┐
│  基本阅读行为记录    │  推荐状态更新        │
│  NewsVectorSearch   │  NewsRecommendations │
│  .RecordUserRead    │  .UpdateReadStatus   │
│  NewsAsync()        │  Async()            │
└─────────────────────┴─────────────────────┘
```
