﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;

namespace Banyan.Apps
{
    public class MeetAttachBLL : BaseDAL<MeetAttach>
    {
        private readonly AjaxResult ajaxResult = null;

        public MeetAttachBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        public List<MeetAttach> GetDocList(int sourceId, SourceTypeEnum typeEnum)
        {
            return GetList($"meetId={sourceId}", int.MaxValue, 0, "*", "Ranking Asc");
        }
        public AjaxResult Rank(int id, int order)
        {
            try
            {
                var model = GetModel(id);
                var exchange = GetModel($"MeetId={model.MeetId} AND ranking={model.Ranking + order}");
                if (exchange != null)
                {
                    model.Ranking = model.Ranking + order;
                    exchange.Ranking = exchange.Ranking - order;
                    Update(model, "ranking");
                    Update(exchange, "ranking");
                    ajaxResult.code = (int)ResultCode.success;
                    return ajaxResult;
                }
            }catch(Exception e)
            {
                Logger.Error(e.Message, e);
            }
            ajaxResult.code = (int)ResultCode.exception;
            return ajaxResult;
        }
            public AjaxResult SaveDoc(MeetAttach model)
        {
            ajaxResult.code = (int)ResultCode.exception;
            if (model.SourceId <= 0 && model.SourceType != 4)
            {
                ajaxResult.msg = $"关联记录不存在";
                return ajaxResult;
            }
            if (model.Speakers == null)
            {
                model.Speakers = "";
            }

            model.Ranking = GetCount($"MeetId={model.MeetId}");
            int docId = Convert.ToInt32(Add(model));
            ajaxResult.data = docId;
            ajaxResult.code = docId > 0 ? (int)ResultCode.success : (int)ResultCode.exception;

            return ajaxResult;
        }

        public AjaxResult DelDoc(int id)
        {
            ajaxResult.code = (int)ResultCode.exception;
            if (id <= 0)
            {
                ajaxResult.msg = $"参数不合法";
                return ajaxResult;
            }
            ajaxResult.code = Delete(id) > 0 ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }

        public void ClearCache(MeetAttach model)
        {
            RedisUtil.Remove(string.Format(RedisKey.attachment_comb, model.Id));
        }
        public override bool Update(MeetAttach model)
        {
            ClearCache(model);
            return base.Update(model);
        }
        public override bool Update(MeetAttach model, string fldList)
        {
            ClearCache(model);
            return base.Update(model, fldList);
        }
        public MeetAttach GetCache(int articleId)
        {
            string cacheKey = string.Format(RedisKey.attachment_comb, articleId);
            MeetAttach model = RedisUtil.Get<MeetAttach>(cacheKey);
            if (model == null)
            {
                model = base.GetModel(articleId);
                RedisUtil.Set<MeetAttach>(cacheKey, model, TimeSpan.FromMinutes(10));
            }
            return model;
        }

        public object JOk(object data, string msg = "", int code = 0, int count = 0)
        {
            return new { code, data, msg, count };
        }

        public object JFail(string msg = "", int code = 1)
        {
            return new { code, msg };
        }
        public static string GetDomain()
        {
            return System.Configuration.ConfigurationManager.AppSettings["FileDomain"];
        }
        public MeetAttach GetMeetAttach(int articleId)
        {
            MeetAttach model = GetCache(articleId);
            if (model.SourceType == (byte)AttachTypeEnum.Login)
            {

            }
            else if (model.SourceType == (byte)AttachTypeEnum.DD)
            {

            }
            else if (model.SourceType == (byte)AttachTypeEnum.Research)
            {

            }
            return model;
        }

        public bool DelDoc(int sourceid, byte sourceType)
        {
            return DeleteByWhere($"SourceId='{sourceid}' AND sourceType='{sourceType}'") > 0 ? true : false;
        }
    }
}
