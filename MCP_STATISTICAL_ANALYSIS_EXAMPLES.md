# MCP 统计分析功能测试用例

## 功能概述

MCP智能工具现在支持**查询类型识别**，能够自动区分"特定搜索"和"统计分析"两种不同的查询需求。

## 查询类型对比

### 特定搜索 (Specific Search)
- **特点**: 针对特定项目、公司、技术、行业的查询
- **策略**: 使用关键词过滤，返回精确匹配的项目
- **数据量**: 较少（5个项目）
- **适用场景**: 查找特定信息、深入了解某个项目

### 统计分析 (Statistical Analysis)  
- **特点**: 对项目组合、整体情况、分布状况的分析
- **策略**: 不使用关键词过滤，返回时间范围内的所有项目
- **数据量**: 较多（20个项目）
- **适用场景**: 投资组合分析、行业分布、趋势统计

## 测试用例对比

### 🔍 特定搜索示例

| 用户输入 | 查询类型 | AI行为 | 预期结果 |
|---------|---------|--------|---------|
| "ABC公司的项目怎么样？" | 特定搜索 | 提取关键词"ABC" | 返回包含ABC的项目 |
| "帮我分析一下AI相关的项目" | 特定搜索 | 提取关键词"AI" | 返回AI领域项目 |
| "新能源项目的风险如何？" | 特定搜索 | 提取关键词"新能源" | 返回新能源项目 |
| "区块链投资项目的表现" | 特定搜索 | 提取关键词"区块链" | 返回区块链项目 |

### 📊 统计分析示例

| 用户输入 | 查询类型 | AI行为 | 预期结果 |
|---------|---------|--------|---------|
| "分析一下当前项目组合的行业分布情况" | 统计分析 | 获取所有近期项目 | 返回20个最新项目供分析 |
| "统计投资项目的阶段分布" | 统计分析 | 获取所有近期项目 | 返回20个最新项目供分析 |
| "评估整体投资组合的表现" | 统计分析 | 获取所有近期项目 | 返回20个最新项目供分析 |
| "项目组合的风险状况如何？" | 统计分析 | 获取所有近期项目 | 返回20个最新项目供分析 |
| "今年的投资情况总体分析" | 统计分析 | 获取今年所有项目 | 返回今年的所有项目 |

### 🕐 时间+统计分析示例

| 用户输入 | 查询类型 | 时间条件 | AI行为 |
|---------|---------|---------|--------|
| "本月项目组合的行业分布" | 统计分析 | 本月 | 获取本月所有项目 |
| "最近三个月的投资统计" | 统计分析 | 最近三个月 | 获取三个月内所有项目 |
| "2024年投资组合分析" | 统计分析 | 2024年 | 获取2024年所有项目 |

## 识别关键词

### 统计分析触发词
- **分析**: "分析一下"、"分析项目组合"
- **统计**: "统计"、"统计分布"  
- **评估**: "评估整体"、"评估组合"
- **分布**: "行业分布"、"阶段分布"、"风险分布"
- **组合**: "项目组合"、"投资组合"
- **整体**: "整体情况"、"整体表现"
- **总体**: "总体分析"、"总体状况"

### 特定搜索触发词
- **具体名称**: 公司名、项目名、创始人名
- **行业关键词**: "AI"、"新能源"、"区块链"、"医疗"
- **技术关键词**: "人工智能"、"机器学习"、"大数据"
- **查找词**: "找一下"、"搜索"、"查看"

## 实际测试步骤

### 测试准备
1. 开启MCP智能工具功能
2. 确保有足够的项目数据
3. 准备测试用例

### 统计分析测试
```
用户: "分析一下当前项目组合的行业分布情况"
预期:
1. AI识别为"统计分析"类型
2. 不提取关键词（或关键词为"无"）
3. 获取近期20个项目数据
4. 状态显示: "🔍 智能检索到 20 个相关项目（统计分析数据）"
5. AI基于所有项目数据进行行业分布分析
```

### 特定搜索测试
```
用户: "AI相关的项目风险如何？"
预期:
1. AI识别为"特定搜索"类型
2. 提取关键词"AI"
3. 获取匹配AI关键词的5个项目
4. 状态显示: "🔍 智能检索到 5 个相关项目"
5. AI基于匹配项目分析AI项目风险
```

## 常见问题处理

### Q1: 如何确保正确识别查询类型？
A: AI会分析问题中的关键词和语义，包含"分析"、"统计"、"分布"、"组合"等词汇的问题更可能被识别为统计分析。

### Q2: 统计分析为什么返回更多数据？
A: 统计分析需要足够的数据样本才能得出准确的分析结果，如行业分布、风险评估等。

### Q3: 如果识别错误怎么办？
A: 可以在问题中使用更明确的表达，如"帮我分析所有项目的..."来触发统计分析，或使用具体关键词来触发特定搜索。

### Q4: 时间条件如何与查询类型结合？
A: 时间条件在两种类型中都有效：
- 特定搜索：在指定时间范围内搜索关键词匹配的项目
- 统计分析：获取指定时间范围内的所有项目

## 最佳实践建议

### 统计分析查询建议
- 使用明确的分析词汇："分析"、"统计"、"评估"
- 指明分析范围："项目组合"、"整体"、"所有"
- 结合时间条件："本月的项目组合分析"

### 特定搜索查询建议
- 使用具体的关键词：公司名、行业名、技术名
- 避免使用分析类词汇
- 明确搜索目标："XX公司的项目"、"AI项目的风险"

---

*通过合理使用MCP的查询类型识别功能，可以更精准地获取所需信息，提高投资分析的效率和准确性。* 