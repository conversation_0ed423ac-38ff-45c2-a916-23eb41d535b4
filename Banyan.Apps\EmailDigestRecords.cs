using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Banyan.Code;
using DAL.Base;
using Banyan.Domain;

namespace Banyan.Apps
{
    public class EmailDigestRecordsBLL : DAL.Base.BaseDAL<EmailDigestRecords>
    {

        /// <summary>
        /// Records a sent email digest
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="newsCount">Number of news items in the digest</param>
        /// <param name="newsIds">Comma-separated list of news IDs</param>
        /// <returns>ID of the newly created digest record</returns>
        public async Task<int> RecordEmailDigestAsync(int userId, int newsCount, string newsIds)
        {
            try
            {
                Logger.Info($"Recording email digest for user {userId}, news count {newsCount}");

                int id = (int)await Task.Run(() => Add(new EmailDigestRecords
                {
                    UserId = userId,
                    SentTime = DateTime.Now,
                    NewsCount = newsCount,
                    NewsIds = newsIds
                }));
                Logger.Info($"Successfully recorded email digest with ID {id}");
                return id;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error recording email digest for user {userId}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets email digest history for a specific user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="limit">Maximum number of records to return</param>
        /// <returns>List of email digest records</returns>
        public async Task<List<EmailDigestRecords>> GetUserEmailDigestHistoryAsync(int userId, int limit = 10)
        {
            try
            {
                Logger.Info($"Getting email digest history for user {userId}, limit {limit}");
                string where = $"UserId = {userId}";
                string orderBy = "SentTime DESC";

                var digests = await Task.Run(() => GetList(where, limit, 1, "*", orderBy));

                Logger.Info($"Retrieved {digests.Count} email digest records for user {userId}");
                return digests;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting email digest history for user {userId}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets the last email digest sent to a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>The last email digest record, or null if none exists</returns>
        public async Task<EmailDigestRecords> GetLastEmailDigestAsync(int userId)
        {
            try
            {
                Logger.Info($"Getting last email digest for user {userId}");

                var digests = await GetUserEmailDigestHistoryAsync(userId, 1);
                var lastDigest = digests.FirstOrDefault();

                if (lastDigest != null)
                {
                    Logger.Info($"Retrieved last email digest for user {userId}, sent on {lastDigest.SentTime}");
                }
                else
                {
                    Logger.Info($"No email digest found for user {userId}");
                }

                return lastDigest;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting last email digest for user {userId}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Converts a DataTable to a list of EmailDigestRecords objects
        /// </summary>
        /// <param name="dataTable">DataTable containing email digest data</param>
        /// <returns>List of EmailDigestRecords objects</returns>
        private List<EmailDigestRecords> ConvertToEmailDigestList(DataTable dataTable)
        {
            var digests = new List<EmailDigestRecords>();

            if (dataTable == null || dataTable.Rows.Count == 0)
            {
                return digests;
            }

            foreach (DataRow row in dataTable.Rows)
            {
                var digest = new EmailDigestRecords
                {
                    Id = Convert.ToInt32(row["Id"]),
                    UserId = Convert.ToInt32(row["UserId"]),
                    SentTime = Convert.ToDateTime(row["SentTime"]),
                    NewsCount = Convert.ToInt32(row["NewsCount"]),
                    NewsIds = row["NewsIds"].ToString()
                };

                digests.Add(digest);
            }

            return digests;
        }

    }
}