# Project Structure and Organization

## Solution Structure

The Banyan project follows a multi-project solution structure:

- **Banyan.Web**: Web application project (ASP.NET MVC)

  - Controllers: MVC controllers handling HTTP requests
  - Views: Razor views for UI rendering
  - Content: Static resources (CSS, JS, images)
  - Migrations: Database migration scripts
  - Configs: Configuration files
  - References: Banyan.Apps, Banyan.Domain, Banyan.Code

- **Banyan.Apps**: Business logic layer

  - Core business logic classes (BLL)
  - Vector processing services
  - News recommendation engine
  - User profile management
  - References: Banyan.Domain, Banyan.Code, DAL.Base

- **Banyan.Domain**: Domain models

  - Entity classes
  - Data transfer objects
  - Mappers for data transformation
  - References: Banyan.Code

- **Banyan.Code**: Utility and infrastructure
  - Common utilities
  - Database connection handling
  - Caching mechanisms
  - Security utilities
  - No project dependencies (base layer)

## Key Components

### News Vectorization System

- **NewsVectorizationService**: Core service for vectorizing news content
- **NewsVectorizationScheduler**: Scheduler for batch processing news articles
- **VectorService**: Service for vector operations and similarity calculations
- **NewsVectorSearch**: Service for searching similar news based on vectors

### User Profile System

- **UserProfileBLL**: Business logic for user profile management
- **UserInterestTagBLL**: Business logic for user interest tags
- **UserTagRelationBLL**: Business logic for user-tag relationships

### Recommendation Engine

- **NewsRecommendationEngine**: Core recommendation engine
- **NewsVectorSearch**: Vector-based news search functionality

## File Naming Conventions

- **Controllers**: `{Feature}Controller.cs` (e.g., NewsVectorSearchController.cs)
- **Views**: Located in `/Views/{Controller}/` folders
- **Business Logic**: `{Entity}BLL.cs` (e.g., NewsBLL.cs)
- **Services**: `{Feature}Service.cs` (e.g., VectorService.cs)
- **Domain Models**: Match entity names (e.g., News.cs)

## Database Structure

- **News**: Core news article data
- **NewsVector**: Vector representations of news articles
- **UserVector**: Vector representations of user interests
- **UserTagRelation**: Relationships between users and interest tags

## Configuration Management

- Web.config: Main application configuration
- App.config: Component-specific configurations
- Custom configuration classes in Banyan.Apps/Configs/
