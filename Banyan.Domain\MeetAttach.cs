﻿using Entity.Base;
using System;
using Utility;


namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public class MeetAttach
    {

        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        /// <summary>
        /// 对应会议ID
        /// </summary>
        [SqlField]
        public int MeetId { get; set; }

        [SqlField]
        public byte SourceType { get; set; }

        [SqlField]
        public string Name { get; set; }

        /// <summary>
        /// 来源ID
        /// </summary>
        [SqlField]
        public int SourceId { get; set; }

        [SqlField]
        public int Ranking { get; set; }

        [SqlField]
        public string Speakers { get; set; }

        /// <summary>
        /// 会议添加时间
        /// </summary>
        [SqlField]
        public DateTime AddTime { get; set; } = DateTime.Now;

    }


    public enum AttachTypeEnum
    {
        Login = 1,
        DD = 2,
        Research = 3
    }

}

//drop TABLE IF EXISTS  MeetAttach;
//CREATE TABLE MeetAttach(
//  "id" INT PRIMARY KEY identity(1,1),
//  "meetId" INT not NULL,
//  "sourceType" tinyint not NULL,
//  "name" varchar(255) NOT NULL,
//  "sourceId" INT not NULL,
//  "speakers" varchar(255) not NULL,
//  "addTime" datetime NOT NULL default getutcdate()
//);

//BEGIN TRANSACTION;
//INSERT INTO MeetAttach("meetId", "sourceType", "name", "sourceId", speakers) VALUES
//(1, 1, '魔急便', '3681', '吕祺'),
//(2, 0, '科杰大数据 ', '3682', 'Leo');
//COMMIT;

//SELECT* from MeetAttach;