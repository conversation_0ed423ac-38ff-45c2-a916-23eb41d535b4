using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Text;
using System.Text.RegularExpressions;
using Banyan.Code;
using Banyan.Domain;
using Banyan.Apps.Configs;
using DAL.Base;
using Newtonsoft.Json;
using System.Web.Caching;

namespace Banyan.Apps
{
    /// <summary>
    /// 新闻向量化服务类
    /// 负责新闻内容的AI分析、标签提取和向量化处理
    /// </summary>
    public class NewsVectorizationService
    {
        private readonly ICache _cache;

        public NewsVectorizationService()
        {
            _cache = CacheFactory.Cache();
        }

        #region 主要业务方法

        /// <summary>
        /// 定期扫描并向量化未向量化的新闻
        /// </summary>
        /// <param name="batchSize">批量大小</param>
        /// <param name="newsBLL">新闻业务逻辑对象</param>
        /// <param name="includeFailedOnly">是否只处理失败的新闻</param>
        /// <param name="maxRetryCount">最大重试次数</param>
        /// <param name="parallelProcessing">是否启用并行处理</param>
        /// <param name="maxDegreeOfParallelism">最大并行度</param>
        /// <returns>处理结果</returns>
        public async Task<VectorizationResult> ScanAndVectorizeNewsAsync(
            int batchSize = VectorServiceConfig.BATCH_SIZE,
            NewsBLL newsBLL = null,
            bool includeFailedOnly = false,
            int maxRetryCount = 3,
            bool parallelProcessing = false,
            int maxDegreeOfParallelism = 5)
        {
            try
            {
                Logger.Info($"开始扫描未向量化的新闻，批量大小: {batchSize}，只处理失败: {includeFailedOnly}，最大重试次数: {maxRetryCount}，并行处理: {parallelProcessing}");

                // 初始化结果对象
                var result = new VectorizationResult
                {
                    StartTime = DateTime.Now,
                    TotalProcessed = 0,
                    SuccessCount = 0,
                    FailedCount = 0,
                    ErrorMessages = new List<string>()
                };

                // 如果没有传入newsBLL，则创建一个新的实例
                newsBLL = newsBLL ?? new NewsBLL();

                // 获取未向量化的新闻
                var pendingNews = GetPendingVectorizationNews(batchSize, newsBLL, includeFailedOnly, maxRetryCount);

                if (pendingNews.Count() == 0)
                {
                    Logger.Info("没有找到需要向量化的新闻");
                    result.EndTime = DateTime.Now;
                    return result;
                }

                Logger.Info($"找到 {pendingNews.Count()} 篇需要向量化的新闻");

                // 根据是否启用并行处理选择不同的处理方式
                if (parallelProcessing)
                {
                    // 并行处理新闻
                    await ProcessNewsInParallel(pendingNews, newsBLL, result, maxDegreeOfParallelism);
                }
                else
                {
                    // 顺序处理新闻
                    await ProcessNewsSequentially(pendingNews, newsBLL, result);
                }

                // 完成处理
                result.EndTime = DateTime.Now;
                Logger.Info($"新闻向量化扫描完成，总计: {result.TotalProcessed}，成功: {result.SuccessCount}，失败: {result.FailedCount}，耗时: {result.Duration.TotalSeconds:F2}秒");

                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"新闻向量化扫描失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 顺序处理新闻向量化
        /// </summary>
        /// <param name="newsList">新闻列表</param>
        /// <param name="newsBLL">新闻业务逻辑对象</param>
        /// <param name="result">处理结果</param>
        /// <returns>任务</returns>
        private async Task ProcessNewsSequentially(List<News> newsList, NewsBLL newsBLL, VectorizationResult result)
        {
            foreach (var news in newsList)
            {
                try
                {
                    Logger.Info($"开始处理新闻，ID: {news.Id}，标题: {news.Title}");

                    // 获取当前重试次数
                    int currentRetryCount = GetCurrentRetryCount(news);

                    // 处理单篇新闻
                    var success = await VectorizeSingleNewsAsync(news, newsBLL);
                    result.TotalProcessed++;

                    if (success)
                    {
                        result.SuccessCount++;
                        Logger.Info($"新闻处理成功，ID: {news.Id}");
                    }
                    else
                    {
                        result.FailedCount++;

                        // 更新重试次数
                        UpdateRetryCount(news.Id, currentRetryCount + 1, newsBLL);

                        Logger.Warn($"新闻处理失败，ID: {news.Id}，重试次数: {currentRetryCount + 1}");
                    }

                    // 添加延迟避免API限流
                    await Task.Delay(VectorServiceConfig.BATCH_DELAY_MS);
                }
                catch (Exception ex)
                {
                    result.FailedCount++;
                    var errorMessage = $"新闻ID {news.Id} 向量化失败: {ex.Message}";
                    result.ErrorMessages.Add(errorMessage);
                    Logger.Error($"新闻向量化失败，新闻ID: {news.Id}，错误: {ex.Message}", ex);

                    // 更新失败状态和错误信息
                    UpdateNewsVectorStatus(news.Id, VectorServiceConfig.VECTOR_STATUS_FAILED, $"处理异常: {ex.Message}", newsBLL);
                }
            }
        }

        /// <summary>
        /// 并行处理新闻向量化
        /// </summary>
        /// <param name="newsList">新闻列表</param>
        /// <param name="newsBLL">新闻业务逻辑对象</param>
        /// <param name="result">处理结果</param>
        /// <param name="maxDegreeOfParallelism">最大并行度</param>
        /// <returns>任务</returns>
        private async Task ProcessNewsInParallel(List<News> newsList, NewsBLL newsBLL, VectorizationResult result, int maxDegreeOfParallelism)
        {
            // 使用信号量限制并发数
            using (var semaphore = new System.Threading.SemaphoreSlim(maxDegreeOfParallelism))
            {
                // 创建任务列表
                var tasks = new List<Task>();

                foreach (var news in newsList)
                {
                    // 等待信号量
                    await semaphore.WaitAsync();

                    // 创建并启动任务
                    var task = Task.Run(async () =>
                    {
                        try
                        {
                            Logger.Info($"开始并行处理新闻，ID: {news.Id}，标题: {news.Title}");

                            // 获取当前重试次数
                            int currentRetryCount = GetCurrentRetryCount(news);

                            // 处理单篇新闻
                            var success = await VectorizeSingleNewsAsync(news, newsBLL);

                            // 线程安全地更新结果
                            lock (result)
                            {
                                result.TotalProcessed++;

                                if (success)
                                {
                                    result.SuccessCount++;
                                    Logger.Info($"新闻并行处理成功，ID: {news.Id}");
                                }
                                else
                                {
                                    result.FailedCount++;

                                    // 更新重试次数
                                    UpdateRetryCount(news.Id, currentRetryCount + 1, newsBLL);

                                    Logger.Warn($"新闻并行处理失败，ID: {news.Id}，重试次数: {currentRetryCount + 1}");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            // 线程安全地更新结果
                            lock (result)
                            {
                                result.FailedCount++;
                                result.TotalProcessed++;
                                var errorMessage = $"新闻ID {news.Id} 向量化失败: {ex.Message}";
                                result.ErrorMessages.Add(errorMessage);
                            }

                            Logger.Error($"新闻并行向量化失败，新闻ID: {news.Id}，错误: {ex.Message}", ex);

                            // 更新失败状态和错误信息
                            UpdateNewsVectorStatus(news.Id, VectorServiceConfig.VECTOR_STATUS_FAILED, $"并行处理异常: {ex.Message}", newsBLL);
                        }
                        finally
                        {
                            // 释放信号量
                            semaphore.Release();
                        }
                    });

                    tasks.Add(task);
                }

                // 等待所有任务完成
                await Task.WhenAll(tasks);
            }
        }

        /// <summary>
        /// 获取当前重试次数
        /// </summary>
        /// <param name="news">新闻对象</param>
        /// <returns>重试次数</returns>
        private int GetCurrentRetryCount(News news)
        {
            if (string.IsNullOrEmpty(news.VectorError))
                return 0;

            // 从错误信息中提取重试次数
            var match = Regex.Match(news.VectorError, @"Retry: (\d+)");
            if (match.Success && match.Groups.Count > 1)
            {
                if (int.TryParse(match.Groups[1].Value, out int retryCount))
                {
                    return retryCount;
                }
            }

            return 0;
        }

        /// <summary>
        /// 更新重试次数
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <param name="retryCount">重试次数</param>
        /// <param name="newsBLL">新闻业务逻辑对象</param>
        private void UpdateRetryCount(int newsId, int retryCount, NewsBLL newsBLL)
        {
            try
            {
                var errorMessage = $"Retry: {retryCount}, Last attempt: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
                UpdateNewsVectorStatus(newsId, VectorServiceConfig.VECTOR_STATUS_FAILED, errorMessage, newsBLL);
            }
            catch (Exception ex)
            {
                Logger.Error($"更新重试次数失败，新闻ID: {newsId}", ex);
            }
        }

        /// <summary>
        /// 向量化单篇新闻
        /// </summary>
        /// <param name="news">新闻对象</param>
        /// <param name="newsBLL">新闻业务逻辑对象</param>
        /// <returns>是否成功</returns>
        public async Task<bool> VectorizeSingleNewsAsync(News news, NewsBLL newsBLL = null)
        {
            try
            {
                Logger.Info($"开始向量化新闻，新闻ID: {news.Id}，标题: {news.Title}");

                // 1. AI分析新闻内容，提取标签
                var tagAnalysis = await AnalyzeNewsContentAsync(news);
                if (tagAnalysis == null)
                {
                    UpdateNewsVectorStatus(news.Id, VectorServiceConfig.VECTOR_STATUS_FAILED, "AI分析失败", newsBLL);
                    return false;
                }

                // 2. 生成新闻向量
                var newsVector = await GenerateNewsVectorAsync(news, tagAnalysis);
                if (newsVector == null)
                {
                    UpdateNewsVectorStatus(news.Id, VectorServiceConfig.VECTOR_STATUS_FAILED, "向量生成失败", newsBLL);
                    return false;
                }

                // 3. 更新新闻数据
                var success = UpdateNewsVectorData(news.Id, newsVector, tagAnalysis, newsBLL);
                if (success)
                {
                    Logger.Info($"新闻向量化成功，新闻ID: {news.Id}");
                    return true;
                }
                else
                {
                    UpdateNewsVectorStatus(news.Id, VectorServiceConfig.VECTOR_STATUS_FAILED, "数据更新失败", newsBLL);
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"新闻向量化失败，新闻ID: {news.Id}", ex);
                UpdateNewsVectorStatus(news.Id, VectorServiceConfig.VECTOR_STATUS_FAILED, ex.Message, newsBLL);
                return false;
            }
        }

        /// <summary>
        /// 获取新闻向量
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <param name="newsBLL">新闻业务逻辑对象</param>
        /// <returns>新闻向量</returns>
        public async Task<double[]> GetNewsVectorAsync(int newsId, NewsBLL newsBLL = null)
        {
            try
            {
                Logger.Info($"获取新闻向量，新闻ID: {newsId}");

                // 1. 优先从缓存获取
                var cacheKey = $"news_vector:{newsId}";
                var cachedVector = _cache.GetCache<double[]>(cacheKey);
                if (cachedVector != null)
                {
                    Logger.Info($"从缓存获取新闻向量成功，新闻ID: {newsId}");
                    return cachedVector;
                }

                // 2. 从数据库获取
                newsBLL = newsBLL ?? new NewsBLL();
                var news = newsBLL.GetModel(newsId);
                if (news == null)
                {
                    Logger.Warn($"新闻不存在，新闻ID: {newsId}");
                    return null;
                }

                if (string.IsNullOrEmpty(news.NewsVector))
                {
                    Logger.Warn($"新闻向量不存在，新闻ID: {newsId}，向量化状态: {news.VectorStatus}");

                    // 如果新闻存在但向量不存在，且状态不是失败，则尝试重新向量化
                    if (news.VectorStatus != VectorServiceConfig.VECTOR_STATUS_FAILED)
                    {
                        Logger.Info($"尝试重新向量化新闻，新闻ID: {newsId}");
                        var success = await VectorizeSingleNewsAsync(news, newsBLL);
                        if (success)
                        {
                            // 重新获取新闻数据
                            news = newsBLL.GetModel(newsId);
                            if (!string.IsNullOrEmpty(news.NewsVector))
                            {
                                var vector = StringToVector(news.NewsVector);
                                if (vector != null)
                                {
                                    // 缓存向量
                                    _cache.WriteCache(vector, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.VECTOR_CACHE_DAYS));
                                    Logger.Info($"新闻重新向量化成功并已缓存，新闻ID: {newsId}");
                                    return vector;
                                }
                            }
                        }
                    }

                    return null;
                }

                var newsVector = StringToVector(news.NewsVector);
                if (newsVector != null)
                {
                    // 缓存向量，设置过期时间
                    _cache.WriteCache(newsVector, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.VECTOR_CACHE_DAYS));
                    Logger.Info($"新闻向量已缓存，新闻ID: {newsId}");
                }
                else
                {
                    Logger.Warn($"新闻向量格式无效，新闻ID: {newsId}");
                }

                return newsVector;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取新闻向量失败，新闻ID: {newsId}", ex);
                return null;
            }
        }

        /// <summary>
        /// 批量获取新闻向量
        /// </summary>
        /// <param name="newsIds">新闻ID列表</param>
        /// <param name="newsBLL">新闻业务逻辑对象</param>
        /// <returns>新闻向量字典</returns>
        public async Task<Dictionary<int, double[]>> GetNewsVectorsBatchAsync(List<int> newsIds, NewsBLL newsBLL = null)
        {
            try
            {
                if (newsIds == null || newsIds.Count == 0)
                {
                    Logger.Warn("批量获取新闻向量失败：新闻ID列表为空");
                    return new Dictionary<int, double[]>();
                }

                Logger.Info($"开始批量获取新闻向量，新闻数量: {newsIds.Count}");
                var result = new Dictionary<int, double[]>();
                var cacheMisses = new List<int>();

                // 1. 首先尝试从缓存获取所有向量
                foreach (var newsId in newsIds)
                {
                    var cacheKey = $"news_vector:{newsId}";
                    var cachedVector = _cache.GetCache<double[]>(cacheKey);

                    if (cachedVector != null)
                    {
                        result[newsId] = cachedVector;
                    }
                    else
                    {
                        cacheMisses.Add(newsId);
                    }
                }

                Logger.Info($"从缓存获取新闻向量，命中: {result.Count}，未命中: {cacheMisses.Count}");

                // 2. 对于缓存未命中的，从数据库批量获取
                if (cacheMisses.Count > 0)
                {
                    newsBLL = newsBLL ?? new NewsBLL();

                    // 构建SQL查询，批量获取新闻数据
                    var idList = string.Join(",", cacheMisses);
                    var fields = "Id, NewsVector, VectorStatus";
                    var where = $"Id IN ({idList}) AND VectorStatus = {VectorServiceConfig.VECTOR_STATUS_SUCCESS}";

                    var newsList = newsBLL.GetList(where, cacheMisses.Count, 1, fields, "Id");

                    // 处理查询结果
                    foreach (var news in newsList)
                    {
                        if (!string.IsNullOrEmpty(news.NewsVector))
                        {
                            var vector = StringToVector(news.NewsVector);
                            if (vector != null)
                            {
                                // 添加到结果集
                                result[news.Id] = vector;

                                // 缓存向量
                                var cacheKey = $"news_vector:{news.Id}";
                                _cache.WriteCache(vector, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.VECTOR_CACHE_DAYS));
                            }
                        }
                    }

                    Logger.Info($"从数据库获取新闻向量，成功: {newsList.Count}，总成功: {result.Count}");

                    // 3. 对于仍然没有向量的新闻，尝试向量化（可选，根据需求决定是否启用）
                    /*
                    var remainingIds = cacheMisses.Except(result.Keys).ToList();
                    if (remainingIds.Count > 0)
                    {
                        Logger.Info($"尝试向量化剩余新闻，数量: {remainingIds.Count}");
                        
                        foreach (var newsId in remainingIds)
                        {
                            var news = newsBLL.GetModel(newsId);
                            if (news != null)
                            {
                                var success = await VectorizeSingleNewsAsync(news, newsBLL);
                                if (success)
                                {
                                    var vector = await GetNewsVectorAsync(newsId, newsBLL);
                                    if (vector != null)
                                    {
                                        result[newsId] = vector;
                                    }
                                }
                            }
                        }
                        
                        Logger.Info($"向量化剩余新闻完成，新增成功: {result.Count - (newsList.Count + (newsIds.Count - cacheMisses.Count))}");
                    }
                    */
                }

                Logger.Info($"批量获取新闻向量完成，请求: {newsIds.Count}，成功: {result.Count}，成功率: {(double)result.Count / newsIds.Count * 100:F2}%");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error("批量获取新闻向量失败", ex);
                return new Dictionary<int, double[]>();
            }
        }

        #endregion

        #region AI分析相关方法

        /// <summary>
        /// AI分析新闻内容，提取标签
        /// </summary>
        /// <param name="news">新闻对象</param>
        /// <returns>标签分析结果</returns>
        private async Task<NewsTagAnalysis> AnalyzeNewsContentAsync(News news)
        {
            if (news == null)
            {
                Logger.Error("无法分析空新闻对象");
                return null;
            }

            try
            {
                Logger.Info($"开始分析新闻内容，新闻ID: {news.Id}，标题: {news.Title}");

                // 检查新闻内容是否为空
                if (string.IsNullOrEmpty(news.Content))
                {
                    Logger.Warn($"新闻内容为空，新闻ID: {news.Id}");
                    // 创建一个基于标题的简单分析结果
                    return CreateSimpleAnalysisFromTitle(news.Title);
                }

                // 构建AI提示词
                var prompt = BuildNewsAnalysisPrompt(news);

                // 尝试从缓存获取分析结果
                var cacheKey = $"news_analysis:{news.Id}";
                var cachedAnalysis = _cache.GetCache<NewsTagAnalysis>(cacheKey);
                if (cachedAnalysis != null)
                {
                    Logger.Info($"从缓存获取新闻分析结果，新闻ID: {news.Id}");
                    return cachedAnalysis;
                }

                // 调用AI服务
                var aiResponse = await CallAIServiceAsync(prompt);
                if (string.IsNullOrEmpty(aiResponse))
                {
                    Logger.Error($"AI服务返回空响应，新闻ID: {news.Id}");
                    return CreateSimpleAnalysisFromTitle(news.Title);
                }

                // 解析AI响应
                var tagAnalysis = ParseAIResponse(aiResponse);
                if (tagAnalysis == null)
                {
                    Logger.Error($"AI响应解析失败，新闻ID: {news.Id}");
                    return CreateSimpleAnalysisFromTitle(news.Title);
                }

                // 缓存分析结果
                _cache.WriteCache(tagAnalysis, cacheKey, DateTime.Now.AddDays(30));

                Logger.Info($"AI分析成功，新闻ID: {news.Id}");
                return tagAnalysis;
            }
            catch (Exception ex)
            {
                Logger.Error($"AI分析新闻内容失败，新闻ID: {news.Id}", ex);
                return CreateSimpleAnalysisFromTitle(news.Title);
            }
        }

        /// <summary>
        /// 构建新闻分析提示词
        /// </summary>
        /// <param name="news">新闻对象</param>
        /// <returns>提示词</returns>
        private string BuildNewsAnalysisPrompt(News news)
        {
            var prompt = new StringBuilder();

            // 基本指令
            prompt.AppendLine("你是一个专业的新闻内容分析专家，请分析以下新闻内容，提取相关标签。");
            prompt.AppendLine();
            prompt.AppendLine("分析要求：");
            prompt.AppendLine("1. 提取3-5个主要标签（权重0.7-1.0）- 这些标签应该是新闻的核心主题和关键概念");
            prompt.AppendLine("2. 提取2-3个次要标签（权重0.3-0.6）- 这些标签是新闻中提到但不是核心的概念");
            prompt.AppendLine("3. 提取2-3个语义关键词（权重0.3）- 这些是能够代表新闻语义的关键词");
            prompt.AppendLine("4. 标签分类必须是以下之一：技术、行业、投资、政策、其他");
            prompt.AppendLine("5. 标签应该是简洁的词语或短语，不要使用长句");
            prompt.AppendLine("6. 标签应该具有商业价值和投资意义");
            prompt.AppendLine("7. 权重应该反映标签在新闻中的重要性，范围在0-1之间");
            prompt.AppendLine();

            // 新闻信息
            prompt.AppendLine("新闻信息：");
            prompt.AppendLine($"标题：{news.Title}");

            // 处理内容，如果内容太长，截取前2000个字符
            string content = news.Content;
            if (!string.IsNullOrEmpty(content) && content.Length > 2000)
            {
                content = content.Substring(0, 2000) + "...（内容已截断）";
            }
            prompt.AppendLine($"内容：{content}");

            // 添加其他元数据
            if (!string.IsNullOrEmpty(news.Source))
            {
                prompt.AppendLine($"来源：{news.Source}");
            }

            if (!string.IsNullOrEmpty(news.Classify))
            {
                prompt.AppendLine($"分类：{news.Classify}");
            }

            if (news.PubTime != null && news.PubTime != DateTime.MinValue)
            {
                prompt.AppendLine($"发布时间：{news.PubTime:yyyy-MM-dd HH:mm:ss}");
            }

            // 如果有标签字段，也提供给AI参考
            if (!string.IsNullOrEmpty(news.Tag))
            {
                prompt.AppendLine($"原始标签：{news.Tag}");
            }

            prompt.AppendLine();

            // 输出格式说明
            prompt.AppendLine("请返回严格的JSON格式，不要包含任何其他文字说明，格式如下：");
            prompt.AppendLine("{");
            prompt.AppendLine("  \"mainTags\": [");
            prompt.AppendLine("    {\"name\": \"标签名1\", \"weight\": 0.9, \"category\": \"技术\"},");
            prompt.AppendLine("    {\"name\": \"标签名2\", \"weight\": 0.8, \"category\": \"投资\"}");
            prompt.AppendLine("  ],");
            prompt.AppendLine("  \"secondaryTags\": [");
            prompt.AppendLine("    {\"name\": \"标签名3\", \"weight\": 0.6, \"category\": \"行业\"},");
            prompt.AppendLine("    {\"name\": \"标签名4\", \"weight\": 0.4, \"category\": \"其他\"}");
            prompt.AppendLine("  ],");
            prompt.AppendLine("  \"semanticKeywords\": [");
            prompt.AppendLine("    {\"name\": \"关键词1\", \"weight\": 0.3, \"category\": \"技术\"},");
            prompt.AppendLine("    {\"name\": \"关键词2\", \"weight\": 0.3, \"category\": \"行业\"}");
            prompt.AppendLine("  ]");
            prompt.AppendLine("}");

            return prompt.ToString();
        }

        /// <summary>
        /// 调用AI服务
        /// </summary>
        /// <param name="prompt">提示词</param>
        /// <returns>AI响应</returns>
        private async Task<string> CallAIServiceAsync(string prompt)
        {
            if (string.IsNullOrEmpty(prompt))
            {
                Logger.Error("AI服务调用失败：提示词为空");
                return null;
            }

            try
            {
                Logger.Info("开始调用AI服务进行内容分析");

                // 生成缓存键
                var cacheKey = $"ai_analysis:{prompt.GetHashCode()}";

                // 先从缓存获取
                var cachedResponse = _cache.GetCache<string>(cacheKey);
                if (cachedResponse != null)
                {
                    Logger.Info("从缓存获取AI分析结果");
                    return cachedResponse;
                }

                // 实现重试机制
                string response = null;
                Exception lastException = null;

                for (int retry = 0; retry < VectorServiceConfig.AI_MAX_RETRIES; retry++)
                {
                    try
                    {
                        Logger.Info($"调用AI服务，第 {retry + 1} 次尝试");

                        // 调用AI服务（这里需要根据实际的AI服务实现）
                        response = await CallLLMServiceAsync(prompt);
                        response = WebHelper.ExtractJsonFromResponse(response);
                        if (!string.IsNullOrEmpty(response))
                        {
                            // 验证响应格式
                            if (IsValidAIResponse(response))
                            {
                                // 缓存结果
                                _cache.WriteCache(response, cacheKey, DateTime.Now.AddDays(7));
                                Logger.Info("AI分析结果已缓存");
                                return response;
                            }
                            else
                            {
                                Logger.Warn($"AI响应格式无效，第 {retry + 1} 次尝试");
                                lastException = new Exception("AI响应格式无效");
                            }
                        }
                        else
                        {
                            Logger.Warn($"AI服务返回空响应，第 {retry + 1} 次尝试");
                            lastException = new Exception("AI服务返回空响应");
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"AI服务调用失败，第 {retry + 1} 次尝试", ex);
                        lastException = ex;
                    }

                    // 如果不是最后一次重试，等待一段时间
                    if (retry < VectorServiceConfig.AI_MAX_RETRIES - 1)
                    {
                        await Task.Delay(1000 * (retry + 1)); // 递增延迟
                    }
                }

                Logger.Error($"AI服务调用失败，已重试 {VectorServiceConfig.AI_MAX_RETRIES} 次");
                throw lastException ?? new Exception("AI服务调用失败");
            }
            catch (Exception ex)
            {
                Logger.Error("调用AI服务失败", ex);
                return null;
            }
        }

        /// <summary>
        /// 调用大模型服务
        /// </summary>
        /// <param name="prompt">提示词</param>
        /// <returns>响应结果</returns>
        private async Task<string> CallLLMServiceAsync(string prompt)
        {
            try
            {
                Logger.Info("开始调用LLM服务进行新闻内容分析");

                // 构建请求数据，使用与LLMController相同的格式
                var requestData = new
                {
                    model = "qwen3-30b-a3b-mlx", // 使用与LLMController相同的默认模型
                    stream = false,
                    messages = new[]
                    {
                        new { role = "user", content = prompt + "/no_think" } // 添加/no_think避免思考过程
                    }
                };

                // 使用Task.Run包装同步的HttpLLMPost调用，使其在后台线程执行
                var response = await Task.Run(() =>
                    HttpMethods.HttpLLMPost(requestData, user: "NewsVectorizationService"));

                if (!string.IsNullOrEmpty(response))
                {
                    Logger.Info("LLM服务调用成功");
                    return response;
                }
                else
                {
                    Logger.Warn("LLM服务返回空响应");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Logger.Error("调用LLM服务失败", ex);
                return null;
            }
        }

        /// <summary>
        /// 验证AI响应格式
        /// </summary>
        /// <param name="response">AI响应</param>
        /// <returns>是否有效</returns>
        private bool IsValidAIResponse(string response)
        {
            if (string.IsNullOrEmpty(response))
            {
                return false;
            }

            try
            {
                // 尝试解析JSON
                var jsonObject = JsonConvert.DeserializeObject<dynamic>(response);

                // 检查必要的字段
                if (jsonObject.mainTags == null && jsonObject.secondaryTags == null && jsonObject.semanticKeywords == null)
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Logger.Error("验证AI响应格式失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 解析AI响应
        /// </summary>
        /// <param name="response">AI响应</param>
        /// <returns>标签分析结果</returns>
        private NewsTagAnalysis ParseAIResponse(string response)
        {
            try
            {
                var analysis = JsonConvert.DeserializeObject<NewsTagAnalysis>(response);
                return analysis;
            }
            catch (Exception ex)
            {
                Logger.Error("解析AI响应失败", ex);
                return null;
            }
        }

        #endregion

        #region 向量化相关方法

        /// <summary>
        /// 生成新闻向量
        /// </summary>
        /// <param name="news">新闻对象</param>
        /// <param name="tagAnalysis">标签分析结果</param>
        /// <returns>新闻向量</returns>
        private async Task<double[]> GenerateNewsVectorAsync(News news, NewsTagAnalysis tagAnalysis)
        {
            try
            {
                var newsVector = new double[VectorServiceConfig.VECTOR_DIMENSION];
                var totalWeight = 0.0;

                // 1. 主要标签向量化
                if (tagAnalysis.MainTags != null)
                {
                    foreach (var tag in tagAnalysis.MainTags)
                    {
                        var tagVector = await GetTagVectorAsync(tag.Name);
                        if (tagVector != null)
                        {
                            // 加权累加
                            for (int i = 0; i < VectorServiceConfig.VECTOR_DIMENSION; i++)
                            {
                                newsVector[i] += tagVector[i] * tag.Weight;
                            }
                            totalWeight += tag.Weight;
                        }
                    }
                }

                // 2. 次要标签向量化（权重降低50%）
                if (tagAnalysis.SecondaryTags != null)
                {
                    foreach (var tag in tagAnalysis.SecondaryTags)
                    {
                        var tagVector = await GetTagVectorAsync(tag.Name);
                        if (tagVector != null)
                        {
                            var adjustedWeight = tag.Weight * 0.5; // 权重降低50%
                            for (int i = 0; i < VectorServiceConfig.VECTOR_DIMENSION; i++)
                            {
                                newsVector[i] += tagVector[i] * adjustedWeight;
                            }
                            totalWeight += adjustedWeight;
                        }
                    }
                }

                // 3. 语义关键词向量化
                if (tagAnalysis.SemanticKeywords != null)
                {
                    foreach (var keyword in tagAnalysis.SemanticKeywords)
                    {
                        var keywordVector = await GetTagVectorAsync(keyword.Name);
                        if (keywordVector != null)
                        {
                            for (int i = 0; i < VectorServiceConfig.VECTOR_DIMENSION; i++)
                            {
                                newsVector[i] += keywordVector[i] * keyword.Weight;
                            }
                            totalWeight += keyword.Weight;
                        }
                    }
                }

                // 4. 向量归一化
                if (totalWeight > 0)
                {
                    for (int i = 0; i < VectorServiceConfig.VECTOR_DIMENSION; i++)
                    {
                        newsVector[i] /= totalWeight;
                    }
                }

                Logger.Info($"新闻向量生成成功，新闻ID: {news.Id}，总权重: {totalWeight}");
                return newsVector;
            }
            catch (Exception ex)
            {
                Logger.Error($"生成新闻向量失败，新闻ID: {news.Id}", ex);
                return null;
            }
        }

        /// <summary>
        /// 获取标签向量
        /// </summary>
        /// <param name="tagName">标签名称</param>
        /// <returns>标签向量</returns>
        private async Task<double[]> GetTagVectorAsync(string tagName)
        {
            try
            {
                // 生成缓存键
                var cacheKey = $"tag_vector:{tagName.GetHashCode()}";

                // 先从缓存获取
                var cachedVector = _cache.GetCache<double[]>(cacheKey);
                if (cachedVector != null)
                {
                    return cachedVector;
                }

                // 调用Embedding服务
                var vector = await CallEmbeddingServiceAsync(tagName);
                if (vector != null)
                {
                    // 缓存结果
                    _cache.WriteCache(vector, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.VECTOR_CACHE_DAYS));
                }

                return vector;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取标签向量失败，标签: {tagName}", ex);
                return null;
            }
        }

        /// <summary>
        /// 调用Embedding服务
        /// </summary>
        /// <param name="text">输入文本</param>
        /// <returns>向量数组</returns>
        private async Task<double[]> CallEmbeddingServiceAsync(string text)
        {
            try
            {
                // 使用统一的VectorService来获取向量
                var vectorService = new VectorService();
                var vector = await vectorService.GetTextEmbeddingAsync(text);

                return vector;
            }
            catch (Exception ex)
            {
                Logger.Error($"调用Embedding服务失败，文本: {text}", ex);
                return null;
            }
        }

        /// <summary>
        /// 调用Embedding API（示例实现）
        /// </summary>
        /// <param name="text">输入文本</param>
        /// <returns>API响应</returns>
        private async Task<string> CallEmbeddingAPIAsync(string text)
        {
            // 这里需要根据实际使用的Embedding服务实现
            try
            {
                // 模拟Embedding服务调用
                await Task.Delay(500);

                // 返回模拟的向量数据
                var random = new Random(text.GetHashCode());
                var vector = new double[VectorServiceConfig.VECTOR_DIMENSION];
                for (int i = 0; i < VectorServiceConfig.VECTOR_DIMENSION; i++)
                {
                    vector[i] = random.NextDouble() * 2 - 1; // -1到1之间的随机数
                }

                return JsonConvert.SerializeObject(new { data = new[] { new { embedding = vector } } });
            }
            catch (Exception ex)
            {
                Logger.Error("调用Embedding API失败", ex);
                return null;
            }
        }

        /// <summary>
        /// 解析Embedding响应
        /// </summary>
        /// <param name="response">API响应</param>
        /// <returns>向量数组</returns>
        private double[] ParseEmbeddingResponse(string response)
        {
            try
            {
                var jsonObject = JsonConvert.DeserializeObject<dynamic>(response);

                // 检查是否有data字段
                if (jsonObject.data != null && jsonObject.data.Count > 0)
                {
                    var embedding = jsonObject.data[0].embedding;
                    if (embedding != null)
                    {
                        var vector = new double[VectorServiceConfig.VECTOR_DIMENSION];

                        // 确保向量维度匹配
                        int actualDimension = embedding.Count;
                        if (actualDimension != VectorServiceConfig.VECTOR_DIMENSION)
                        {
                            Logger.Warn($"Embedding向量维度不匹配，期望: {VectorServiceConfig.VECTOR_DIMENSION}，实际: {actualDimension}");

                            // 如果实际维度小于期望维度，用0填充
                            if (actualDimension < VectorServiceConfig.VECTOR_DIMENSION)
                            {
                                for (int i = 0; i < actualDimension; i++)
                                {
                                    vector[i] = Convert.ToDouble(embedding[i]);
                                }
                                for (int i = actualDimension; i < VectorServiceConfig.VECTOR_DIMENSION; i++)
                                {
                                    vector[i] = 0.0;
                                }
                            }
                            else
                            {
                                // 如果实际维度大于期望维度，截取前VectorServiceConfig.VECTOR_DIMENSION个元素
                                for (int i = 0; i < VectorServiceConfig.VECTOR_DIMENSION; i++)
                                {
                                    vector[i] = Convert.ToDouble(embedding[i]);
                                }
                            }
                        }
                        else
                        {
                            for (int i = 0; i < VectorServiceConfig.VECTOR_DIMENSION; i++)
                            {
                                vector[i] = Convert.ToDouble(embedding[i]);
                            }
                        }

                        return vector;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Logger.Error("解析Embedding响应失败", ex);
                return null;
            }
        }

        #endregion

        #region 数据库操作方法

        /// <summary>
        /// 获取待向量化的新闻
        /// </summary>
        /// <param name="batchSize">批量大小</param>
        /// <param name="newsBLL">新闻业务逻辑对象</param>
        /// <returns>新闻列表</returns>
        /// <summary>
        /// 获取待向量化的新闻列表
        /// </summary>
        /// <param name="batchSize">批量大小</param>
        /// <param name="newsBLL">新闻业务逻辑对象</param>
        /// <param name="includeFailedOnly">是否只包含失败的新闻</param>
        /// <param name="maxRetryCount">最大重试次数</param>
        /// <param name="minAgeHours">最小年龄（小时）</param>
        /// <returns>新闻列表</returns>
        private List<News> GetPendingVectorizationNews(int batchSize, NewsBLL newsBLL = null, bool includeFailedOnly = false, int maxRetryCount = 3, int minAgeHours = 0)
        {
            try
            {
                Logger.Info($"获取待向量化新闻，批量大小: {batchSize}, 只包含失败: {includeFailedOnly}, 最大重试次数: {maxRetryCount}, 最小年龄(小时): {minAgeHours}");

                // 如果没有传入newsBLL，则创建一个新的实例
                newsBLL = newsBLL ?? new NewsBLL();

                // 构建查询条件
                var whereBuilder = new StringBuilder();

                if (includeFailedOnly)
                {
                    // 只包含失败的新闻，并且限制重试次数
                    whereBuilder.Append($"VectorStatus = {VectorServiceConfig.VECTOR_STATUS_FAILED}");

                    // 如果设置了最大重试次数，则添加重试次数限制
                    if (maxRetryCount > 0)
                    {
                        // 假设我们在VectorError字段中记录了重试次数信息，格式如"Retry: N"
                        // 这里使用简单的字符串匹配，实际应用中可能需要更复杂的逻辑
                        whereBuilder.Append($" AND (VectorError IS NULL OR VectorError NOT LIKE 'Retry: {maxRetryCount}%')");
                    }
                }
                else
                {
                    // 包含待处理和失败的新闻
                    whereBuilder.Append($"(VectorStatus = {VectorServiceConfig.VECTOR_STATUS_PENDING}");

                    // 如果不排除失败的新闻，则添加失败状态条件
                    whereBuilder.Append($" OR VectorStatus = {VectorServiceConfig.VECTOR_STATUS_FAILED}");

                    // 如果设置了最大重试次数，则添加重试次数限制
                    if (maxRetryCount > 0)
                    {
                        whereBuilder.Append($" AND (VectorError IS NULL OR VectorError NOT LIKE 'Retry: {maxRetryCount}%')");
                    }

                    whereBuilder.Append(")");
                }

                // 如果设置了最小年龄，则添加时间限制
                if (minAgeHours > 0)
                {
                    var cutoffTime = DateTime.Now.AddHours(-minAgeHours);
                    whereBuilder.Append($" AND CreateTime <= '{cutoffTime:yyyy-MM-dd HH:mm:ss}'");
                }

                // 如果新闻内容为空，则排除
                whereBuilder.Append(" AND Content IS NOT NULL AND Content <> ''");

                var where = whereBuilder.ToString();

                // 优先处理待处理的新闻，然后是失败次数少的新闻
                var orderBy = "VectorStatus ASC, CreateTime DESC";

                // 需要获取的字段
                var fields = "Id, Title, Content, Subject, Source, Classify, PubTime, CreateTime, Tag, VectorStatus, VectorError, VectorUpdateTime";

                Logger.Info($"查询条件: {where}");

                var newsList = newsBLL.GetList(where, batchSize, 1, fields, orderBy);
                Logger.Info($"找到 {newsList.Count} 篇待向量化新闻");

                return newsList;
            }
            catch (Exception ex)
            {
                Logger.Error("获取待向量化新闻失败", ex);
                return new List<News>();
            }
        }

        /// <summary>
        /// 更新新闻向量数据
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <param name="vector">新闻向量</param>
        /// <param name="tagAnalysis">标签分析结果</param>
        /// <param name="newsBLL">新闻业务逻辑对象</param>
        /// <returns>是否成功</returns>
        public bool UpdateNewsVectorData(int newsId, double[] vector, NewsTagAnalysis tagAnalysis, NewsBLL newsBLL = null)
        {
            try
            {
                Logger.Info($"开始更新新闻向量数据，新闻ID: {newsId}");

                // 如果没有传入newsBLL，则创建一个新的实例
                newsBLL = newsBLL ?? new NewsBLL();

                var news = newsBLL.GetModel(newsId);
                if (news == null)
                {
                    Logger.Error($"新闻不存在，新闻ID: {newsId}");
                    return false;
                }

                // 安全地序列化标签分析结果
                string tagAnalysisJson = null;
                try
                {
                    if (tagAnalysis != null)
                    {
                        // 确保标签分析对象的完整性
                        if (tagAnalysis.MainTags == null) tagAnalysis.MainTags = new List<NewsTag>();
                        if (tagAnalysis.SecondaryTags == null) tagAnalysis.SecondaryTags = new List<NewsTag>();
                        if (tagAnalysis.SemanticKeywords == null) tagAnalysis.SemanticKeywords = new List<NewsTag>();

                        tagAnalysisJson = JsonConvert.SerializeObject(tagAnalysis, Formatting.None);

                        // 验证序列化结果
                        if (string.IsNullOrEmpty(tagAnalysisJson) || tagAnalysisJson == "null")
                        {
                            Logger.Warn($"标签分析序列化结果为空，新闻ID: {newsId}");
                            tagAnalysisJson = CreateEmptyTagAnalysisJson();
                        }
                        else
                        {
                            // 验证JSON格式是否正确
                            try
                            {
                                JsonConvert.DeserializeObject<NewsTagAnalysis>(tagAnalysisJson);
                                Logger.Info($"标签分析JSON验证成功，新闻ID: {newsId}，长度: {tagAnalysisJson.Length}");
                            }
                            catch (Exception jsonEx)
                            {
                                Logger.Error($"标签分析JSON验证失败，新闻ID: {newsId}，错误: {jsonEx.Message}");
                                tagAnalysisJson = CreateEmptyTagAnalysisJson();
                            }
                        }
                    }
                    else
                    {
                        Logger.Warn($"标签分析对象为null，新闻ID: {newsId}");
                        tagAnalysisJson = CreateEmptyTagAnalysisJson();
                    }
                }
                catch (Exception jsonEx)
                {
                    Logger.Error($"序列化标签分析失败，新闻ID: {newsId}，错误: {jsonEx.Message}", jsonEx);
                    tagAnalysisJson = CreateEmptyTagAnalysisJson();
                }

                // 更新向量数据
                news.NewsVector = VectorToString(vector);
                news.VectorUpdateTime = DateTime.Now;
                news.TagAnalysis = tagAnalysisJson;
                news.VectorStatus = VectorServiceConfig.VECTOR_STATUS_SUCCESS;
                news.VectorError = null;

                var success = newsBLL.Update(news);
                if (success)
                {
                    // 清除旧缓存
                    var cacheKey = $"news_vector:{newsId}";
                    _cache.RemoveCache(cacheKey);

                    // 添加新缓存
                    _cache.WriteCache(vector, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.VECTOR_CACHE_DAYS));

                    Logger.Info($"新闻向量数据更新成功，新闻ID: {newsId}");
                    return true;
                }
                else
                {
                    Logger.Error($"新闻向量数据更新失败，新闻ID: {newsId}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"更新新闻向量数据异常，新闻ID: {newsId}", ex);
                return false;
            }
        }

        /// <summary>
        /// 创建空的标签分析JSON字符串
        /// </summary>
        /// <returns>空的标签分析JSON</returns>
        private string CreateEmptyTagAnalysisJson()
        {
            var emptyAnalysis = new NewsTagAnalysis
            {
                MainTags = new List<NewsTag>(),
                SecondaryTags = new List<NewsTag>(),
                SemanticKeywords = new List<NewsTag>()
            };
            return JsonConvert.SerializeObject(emptyAnalysis, Formatting.None);
        }



        /// <summary>
        /// 更新新闻向量状态
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <param name="status">状态</param>
        /// <param name="errorMessage">错误信息</param>
        /// <param name="newsBLL">新闻业务逻辑对象</param>
        /// <returns>是否成功</returns>
        public bool UpdateNewsVectorStatus(int newsId, int status, string errorMessage = null, NewsBLL newsBLL = null)
        {
            try
            {
                Logger.Info($"更新新闻向量状态，新闻ID: {newsId}，状态: {status}");

                // 如果没有传入newsBLL，则创建一个新的实例
                newsBLL = newsBLL ?? new NewsBLL();

                var news = newsBLL.GetModel(newsId);
                if (news == null)
                {
                    Logger.Error($"新闻不存在，新闻ID: {newsId}");
                    return false;
                }

                // 更新状态信息
                news.VectorStatus = status;
                news.VectorUpdateTime = DateTime.Now;

                // 如果是失败状态，记录错误信息
                if (status == VectorServiceConfig.VECTOR_STATUS_FAILED && !string.IsNullOrEmpty(errorMessage))
                {
                    // 截断错误信息，避免数据库字段溢出
                    news.VectorError = errorMessage.Length > 500 ? errorMessage.Substring(0, 500) : errorMessage;
                }
                else
                {
                    news.VectorError = null;
                }

                // 如果是成功状态，确保有向量数据
                if (status == VectorServiceConfig.VECTOR_STATUS_SUCCESS && string.IsNullOrEmpty(news.NewsVector))
                {
                    Logger.Warn($"新闻向量状态设置为成功，但向量数据为空，新闻ID: {newsId}");
                }

                // 更新数据库
                var success = newsBLL.Update(news);

                if (success)
                {
                    Logger.Info($"新闻向量状态更新成功，新闻ID: {newsId}，状态: {status}");

                    // 如果状态变更为失败，清除缓存
                    if (status == VectorServiceConfig.VECTOR_STATUS_FAILED)
                    {
                        var cacheKey = $"news_vector:{newsId}";
                        _cache.RemoveCache(cacheKey);
                    }
                }
                else
                {
                    Logger.Error($"新闻向量状态更新失败，新闻ID: {newsId}");
                }

                return success;
            }
            catch (Exception ex)
            {
                Logger.Error($"更新新闻向量状态异常，新闻ID: {newsId}", ex);
                return false;
            }
        }

        #endregion

        #region 工具方法

        /// <summary>
        /// 向量转字符串
        /// </summary>
        /// <param name="vector">向量数组</param>
        /// <returns>字符串</returns>
        private string VectorToString(double[] vector)
        {
            if (vector == null || vector.Length == 0)
                return string.Empty;

            return string.Join(",", vector.Select(v => v.ToString("F6")));
        }

        /// <summary>
        /// 字符串转向量
        /// </summary>
        /// <param name="vectorString">向量字符串</param>
        /// <returns>向量数组</returns>
        private double[] StringToVector(string vectorString)
        {
            if (string.IsNullOrEmpty(vectorString))
                return null;

            try
            {
                var values = vectorString.Split(',');
                var vector = new double[values.Length];

                for (int i = 0; i < values.Length; i++)
                {
                    if (double.TryParse(values[i], out double value))
                    {
                        vector[i] = value;
                    }
                    else
                    {
                        Logger.Warn($"向量字符串解析失败，索引: {i}，值: {values[i]}");
                        return null;
                    }
                }

                return vector;
            }
            catch (Exception ex)
            {
                Logger.Error("向量字符串解析失败", ex);
                return null;
            }
        }

        /// <summary>
        /// 判断是否为停用词
        /// </summary>
        /// <param name="word">词语</param>
        /// <returns>是否为停用词</returns>
        private bool IsStopWord(string word)
        {
            var stopWords = new HashSet<string> { "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个", "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好", "自己", "这", "那", "他", "她", "它", "我们", "你们", "他们", "她们", "它们" };
            return stopWords.Contains(word);
        }

        /// <summary>
        /// 从标题创建简单的分析结果（当新闻内容为空时使用）
        /// </summary>
        /// <param name="title">新闻标题</param>
        /// <returns>标签分析结果</returns>
        private NewsTagAnalysis CreateSimpleAnalysisFromTitle(string title)
        {
            try
            {
                if (string.IsNullOrEmpty(title))
                {
                    Logger.Warn("标题为空，无法创建简单分析结果");
                    return new NewsTagAnalysis
                    {
                        MainTags = new List<NewsTag>(),
                        SecondaryTags = new List<NewsTag>(),
                        SemanticKeywords = new List<NewsTag>()
                    };
                }

                Logger.Info($"从标题创建简单分析结果: {title}");

                var analysis = new NewsTagAnalysis
                {
                    MainTags = new List<NewsTag>(),
                    SecondaryTags = new List<NewsTag>(),
                    SemanticKeywords = new List<NewsTag>()
                };

                // 简单分词，提取关键词
                var words = title.Split(new[] { ' ', ',', '.', '，', '。', '、', '：', ':', ';', '；', '!', '！', '?', '？', '(', ')', '（', '）', '[', ']', '【', '】' },
                    StringSplitOptions.RemoveEmptyEntries);

                // 过滤掉停用词和短词
                var filteredWords = words.Where(w => w.Length > 1 && !IsStopWord(w)).ToList();

                // 添加主要标签（取前2个词）
                for (int i = 0; i < Math.Min(2, filteredWords.Count); i++)
                {
                    analysis.MainTags.Add(new NewsTag
                    {
                        Name = filteredWords[i],
                        Weight = 0.8,
                        Category = "其他"
                    });
                }

                // 添加次要标签（取后2个词，如果有的话）
                for (int i = 2; i < Math.Min(4, filteredWords.Count); i++)
                {
                    analysis.SecondaryTags.Add(new NewsTag
                    {
                        Name = filteredWords[i],
                        Weight = 0.5,
                        Category = "其他"
                    });
                }

                // 添加语义关键词（取整个标题）
                analysis.SemanticKeywords.Add(new NewsTag
                {
                    Name = title.Length > 20 ? title.Substring(0, 20) : title,
                    Weight = 0.3,
                    Category = "其他"
                });

                Logger.Info($"从标题创建简单分析结果成功，主要标签: {analysis.MainTags.Count}，次要标签: {analysis.SecondaryTags.Count}，语义关键词: {analysis.SemanticKeywords.Count}");
                return analysis;
            }
            catch (Exception ex)
            {
                Logger.Error($"从标题创建简单分析结果失败: {title}", ex);
                return new NewsTagAnalysis
                {
                    MainTags = new List<NewsTag>(),
                    SecondaryTags = new List<NewsTag>(),
                    SemanticKeywords = new List<NewsTag>()
                };
            }
        }

        #endregion
    }
}
