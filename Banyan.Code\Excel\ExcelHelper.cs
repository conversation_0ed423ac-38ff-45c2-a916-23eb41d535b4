﻿
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Linq;

namespace Banyan.Code
{
    public static class ExcelHelper
    {
        public static string ExcelContentType
        {
            get
            {
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            }
        }

        /// <summary>
        /// List转DataTable
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data"></param>
        /// <returns></returns>
        public static DataTable ListToDataTable<T>(List<T> data, string[] columnsToTake)
        {
            PropertyDescriptorCollection properties = TypeDescriptor.GetProperties(typeof(T));
            DataTable dataTable = new DataTable();
            for (int i = 0; i < properties.Count; i++)
            {
                PropertyDescriptor property = properties[i];
                dataTable.Columns.Add(property.Name, Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType);
            }
            object[] values = new object[properties.Count];
            foreach (T item in data)
            {
                for (int i = 0; i < values.Length; i++)
                {
                    values[i] = properties[i].GetValue(item);
                }

                dataTable.Rows.Add(values);
            }
            return dataTable;
        }

        /// <summary>
        /// 自定义下载字段排序
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list"></param>
        /// <param name="columnsToTake"></param>
        /// <returns></returns>
        public static DataTable ListToDataTableNew<T>(List<T> list, string[] columnsToTake)
        {
            PropertyDescriptorCollection properties = TypeDescriptor.GetProperties(typeof(T));
            DataTable dataTable = new DataTable();
            for (int i = 0; i < columnsToTake.Length; i++)
                dataTable.Columns.Add(columnsToTake[i]);

            object[] values = new object[columnsToTake.Length];
            foreach (T item in list)
            {
                for (int i = 0; i < values.Length; i++)
                {
                    values[i] = properties[columnsToTake[i]].GetValue(item);
                }
                dataTable.Rows.Add(values);
            }
            return dataTable;
        }

        /// <summary>  
        /// 分解数据表  
        /// </summary>  
        /// <param name="originalTab">需要分解的表</param>  
        /// <param name="rowsNum">每个表包含的数据量</param>  
        /// <returns></returns>  
        public static DataSet SplitDataTable(DataTable originalTab, int rowsNum)
        {
            //获取所需创建的表数量  
            int tableNum = originalTab.Rows.Count / rowsNum;

            //获取数据余数  
            int remainder = originalTab.Rows.Count % rowsNum;

            DataSet ds = new DataSet();

            //如果只需要创建1个表，直接将原始表存入DataSet  
            if (tableNum == 0)
            {
                ds.Tables.Add(originalTab);
            }
            else
            {
                //如果不能整除则需要+1
                if (remainder != 0)
                {
                    tableNum += 1; // 创建表的数量
                }
                DataTable[] tableSlice = new DataTable[tableNum];

                // 将原始列添加到新Table中
                for (int c = 0; c < tableNum; c++)
                {
                    tableSlice[c] = new DataTable();
                    foreach (DataColumn dc in originalTab.Columns)
                    {
                        tableSlice[c].Columns.Add(dc.ColumnName, dc.DataType);
                    }
                }

                // 插入行
                for (int i = 0; i < tableNum; i++)
                {
                    if (i != tableNum - 1) // 如果不是最后一个Table
                    {
                        for (int j = i * rowsNum; j < ((i + 1) * rowsNum); j++)
                        {
                            tableSlice[i].ImportRow(originalTab.Rows[j]);
                        }
                    }
                    else
                    {
                        for (int k = i * rowsNum; k < originalTab.Rows.Count; k++)
                        {
                            tableSlice[i].ImportRow(originalTab.Rows[k]);
                        }
                    }
                }

                // 将所有Table添加到DataSet中                  
                foreach (DataTable dt in tableSlice)
                {
                    ds.Tables.Add(dt);
                }
            }
            return ds;
        }

        /// <summary>
        /// 导出Excel
        /// </summary>
        /// <param name="dataTable">数据源</param>
        /// <param name="heading">工作簿Worksheet</param>
        /// <param name="showSrNo">//是否显示行编号</param>
        /// <param name="columnsToTake">要导出的列</param>
        /// <returns></returns>
        public static byte[] ExportExcel(DataTable dt, List<string> ColnumTitles, string heading = "", bool showSrNo = false, params string[] columnsToTake)
        {
            byte[] result = null;
            int sheetLimitNo = Convert.ToInt32(ConfigurationManager.AppSettings["SheetLimitNo"] ?? "5000"); // 默认单个工作区记录上限5000条
            using (ExcelPackage package = new ExcelPackage())
            {
                DataSet ds = SplitDataTable(dt, sheetLimitNo);
                if (ds.Tables.Count > 0)
                {
                    for (int dsi = 0; dsi < ds.Tables.Count; dsi++)
                    {
                        var dataTable = ds.Tables[dsi];
                        ExcelWorksheet workSheet = package.Workbook.Worksheets.Add(string.Format("{0}工作区{1}", heading, dsi + 1));
                        int startRowFrom = string.IsNullOrEmpty(heading) ? 1 : 3;
                        if (showSrNo) // 是否显示行标
                        {
                            DataColumn dataColumn = dataTable.Columns.Add("#", typeof(int));
                            dataColumn.SetOrdinal(0);
                            int index = 1;
                            foreach (DataRow item in dataTable.Rows)
                            {
                                item[0] = index;
                                index++;
                            }
                        }

                        workSheet.Cells["A" + startRowFrom].LoadFromDataTable(dataTable, true); // 加载DataTable内容到工作区
                        int columnIndex = 1;
                        foreach (DataColumn item in dataTable.Columns)
                        {
                            ExcelRange columnCells = workSheet.Cells[workSheet.Dimension.Start.Row, columnIndex, workSheet.Dimension.End.Row, columnIndex];
                            int maxLength = columnCells.Max(cell => cell.Value == null ? 1 : cell.Value.ToString().Count());
                            if (maxLength < 150)
                            {
                                workSheet.Column(columnIndex).AutoFit();
                            }
                            columnIndex++;
                        }

                        using (ExcelRange r = workSheet.Cells[startRowFrom, 1, startRowFrom, dataTable.Columns.Count]) // 标题栏
                        {
                            r.Style.Font.Color.SetColor(System.Drawing.Color.White);
                            r.Style.Font.Bold = true;
                            r.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                            r.Style.Fill.BackgroundColor.SetColor(System.Drawing.ColorTranslator.FromHtml("#0084FF"));
                        }

                        using (ExcelRange r = workSheet.Cells[startRowFrom + 1, 1, startRowFrom + dataTable.Rows.Count, dataTable.Columns.Count]) // 单元格边框样式
                        {
                            r.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                            r.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                            r.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                            r.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        }

                        for (int i = dataTable.Columns.Count - 1; i >= 0; i--) // 移除非指定的列
                        {
                            if (i == 0 && showSrNo)
                                continue;

                            if (!columnsToTake.Contains(dataTable.Columns[i].ColumnName))
                                workSheet.DeleteColumn(i + 1);
                        }

                        for (int i = 0; i < ColnumTitles.Count; i++) // 替换自定义标题
                            workSheet.Cells[startRowFrom, i + 1].Value = ColnumTitles[i];

                        if (!String.IsNullOrEmpty(heading)) // 标题头部
                        {
                            workSheet.Cells["A1"].Value = heading;
                            workSheet.Cells["A1"].Style.Font.Size = 20;

                            workSheet.InsertColumn(1, 1);
                            workSheet.InsertRow(1, 1);
                            workSheet.Column(1).Width = 5;
                        }
                    }
                }
                result = package.GetAsByteArray();
            }
            return result;
        }

        /// <summary>
        /// 导出Excel
        /// </summary>
        /// <typeparam name="T">对象</typeparam>
        /// <param name="data">对象数组</param>
        /// <param name="ColnumTitles">列标题</param>
        /// <param name="heading">顶部标题</param>
        /// <param name="isShowSlNo">列序号</param>
        /// <param name="ColumnsToTake">列名</param>
        /// <returns></returns>
        public static byte[] ExportExcel<T>(List<T> data, List<string> colnumTitles, string heading = "", bool isShowSlNo = false, params string[] columnsToTake)
        {
            return ExportExcel(ListToDataTableNew<T>(data, columnsToTake), colnumTitles, heading, isShowSlNo, columnsToTake);
        }
    }
}
