# 用户画像管理模块

## 模块概述

用户画像管理模块是智能新闻推荐系统的核心组件，负责通过AI分析用户项目数据，动态生成个性化的兴趣画像，为新闻推荐提供精准的用户兴趣标签。

### 核心特性

- **AI驱动分析**：完全基于大模型分析用户项目数据生成标签
- **动态标签创建**：标签由AI分析产生，不包含任何硬编码标签
- **实时更新**：用户保存新项目时自动更新画像
- **冷启动支持**：为所有用户批量生成初始画像
- **智能缓存**：Redis缓存提升性能
- **批量处理**：支持大批量用户画像更新

## 设计理念

### 动态标签生成
- **无硬编码标签**：代码和配置中不包含任何预定义标签
- **AI分析驱动**：所有标签均由AI分析用户项目数据产生
- **自适应标签体系**：标签体系随用户行为动态演进
- **分类自动识别**：AI自动识别标签所属分类（技术、行业、投资、其他）

### 标签生命周期
1. **AI分析**：分析用户项目数据，识别兴趣领域
2. **标签生成**：AI生成标签名称、分类、权重、关键词
3. **动态创建**：系统自动创建新标签或使用现有标签
4. **权重计算**：结合AI分析、点击行为、最近交互计算权重
5. **持续更新**：随用户行为变化动态调整

## 文件结构

```
Banyan.Apps/
└── UserProfileBLL.cs              # 核心业务逻辑

Banyan.Web/Controllers/
└── UserProfileController.cs       # API控制器

Banyan.Domain/SqlModels/
└── UserProfile.cs                 # 数据模型

Database/
└── UserProfile_Tables.sql         # 数据库表结构

README_UserProfile.md              # 本文档
```

## 核心功能

### 1. AI分析用户画像
```csharp
// 分析用户项目数据并生成兴趣画像
var profile = await userProfileBLL.AnalyzeUserProfileAsync(userId);
```

### 2. 冷启动批量处理
```csharp
// 为所有用户生成初始画像
var result = await userProfileBLL.ColdStartAsync();
```

### 3. 动态更新画像
```csharp
// 用户保存新项目时自动更新画像
var success = await userProfileBLL.UpdateProfileOnNewProjectAsync(userId, projectId);
```

## AI分析流程

### 1. 数据收集
- 获取用户最近100个项目数据
- 提取项目名称、描述、投资金额、行业、技术等信息

### 2. 提示词构建
```json
{
  "interestDescription": "用户兴趣描述",
  "tags": [
    {
      "name": "标签名称",
      "category": "技术|行业|投资|其他",
      "weight": 0.0-1.0,
      "keywords": "相关关键词"
    }
  ]
}
```

### 3. AI分析
- 调用大模型分析用户项目数据
- 识别技术领域、行业领域、投资偏好
- 生成标签权重和关键词

### 4. 标签创建
- 检查标签是否已存在
- 动态创建新标签或使用现有标签
- 建立用户与标签的关联关系

## 数据库设计

### 核心表结构
- **UserProfile**：用户画像基本信息
- **UserInterestTag**：兴趣标签（动态创建）
- **UserTagRelation**：用户标签关联
- **TagCategory**：标签分类（仅基础分类）
- **UserProfileStats**：统计信息

### 动态标签创建
```sql
-- 动态创建或获取标签
EXEC [dbo].[sp_CreateOrGetTag] 
    @TagName = 'AI医疗', 
    @Category = '技术', 
    @Keywords = '人工智能,医疗健康', 
    @TagId OUTPUT;
```

## API接口

### 1. 获取用户画像
```
GET /api/userprofile/{userId}
```

### 2. 分析用户画像
```
POST /api/userprofile/analyze/{userId}
```

### 3. 冷启动批量处理
```
POST /api/userprofile/coldstart
```

### 4. 获取推荐标签
```
GET /api/userprofile/recommendations/{userId}
```

### 5. 记录标签点击
```
POST /api/userprofile/click/{userId}/{tagName}
```

## 配置说明

所有配置参数都在 `UserProfileBLL.cs` 中以常量形式定义，无需外部配置文件：

### 主要配置常量
```csharp
// AI分析配置
private const string AI_MODEL_NAME = "qwen3-30b-a3b-mlx";
private const int AI_ANALYSIS_TIMEOUT = 30;
private const int AI_ANALYSIS_BATCH_SIZE = 10;

// 缓存配置
private const int USER_PROFILE_CACHE_DAYS = 30;
private const int TAG_STATS_CACHE_DAYS = 7;

// 权重计算配置
private const double AI_ANALYSIS_WEIGHT_RATIO = 0.6;
private const double CLICK_BEHAVIOR_WEIGHT_RATIO = 0.3;
private const double RECENT_INTERACTION_WEIGHT_RATIO = 0.1;
```

## 部署指南

### 1. 数据库初始化
```sql
-- 执行数据库表创建脚本
EXEC [Database/UserProfile_Tables.sql]
```

### 2. 配置更新
- 如需修改配置，直接编辑 `UserProfileBLL.cs` 中的常量
- 配置AI服务连接信息
- 设置Redis缓存连接

### 3. 冷启动执行
```csharp
// 首次部署时执行冷启动
var userProfileBLL = new UserProfileBLL();
var result = await userProfileBLL.ColdStartAsync();
```

## 监控指标

### 关键指标
- **用户画像覆盖率**：有画像的用户比例
- **平均标签数量**：每个用户的平均标签数
- **AI分析成功率**：AI分析的成功率
- **标签创建频率**：新标签创建频率
- **画像更新延迟**：画像更新的响应时间

### 监控配置
```csharp
private const bool ENABLE_MONITORING = true;
private const int MONITORING_INTERVAL_MINUTES = 60;
```

## 错误处理

### 降级策略
- **AI服务不可用**：使用规则引擎生成基础画像
- **数据库连接失败**：使用缓存数据
- **解析失败**：记录错误日志，返回默认值

### 重试机制
```csharp
private const int AI_ANALYSIS_MAX_RETRIES = 3;
private const int MAX_ERROR_RETRIES = 3;
```

## 性能优化

### 1. 批量处理
- 批量AI调用减少API调用次数
- 批量数据库操作提高写入性能
- 并发控制避免服务过载

### 2. 缓存策略
- 用户画像缓存30天
- 标签统计缓存7天
- 热门标签缓存24小时

### 3. 数据库优化
- 为常用查询建立索引
- 使用存储过程减少网络开销
- 批量更新减少事务开销

## 扩展计划

### 短期扩展
- **个性化权重**：支持用户自定义标签权重
- **兴趣演化**：跟踪用户兴趣变化趋势
- **标签推荐**：基于相似用户推荐标签

### 长期扩展
- **社交影响**：考虑用户社交网络影响
- **多维度画像**：增加更多用户特征维度
- **实时分析**：支持实时用户行为分析

## 技术栈

- **后端框架**：ASP.NET MVC
- **数据库**：SQL Server
- **缓存**：Redis
- **AI服务**：支持多种大模型（OpenAI、Azure OpenAI、本地模型等）
- **日志**：log4net
- **序列化**：Newtonsoft.Json

## 注意事项

1. **AI服务依赖**：确保AI服务稳定可用
2. **数据隐私**：用户项目数据仅用于画像分析
3. **性能监控**：定期监控AI调用性能和成本
4. **标签质量**：定期评估AI生成标签的质量和准确性
5. **数据一致性**：确保用户画像与标签关联的一致性

## 技术支持

如有问题，请联系开发团队或查看日志文件获取详细错误信息。 