﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.Linq;


namespace Banyan.Apps
{
    public class Fund2PortfolioSummaryBLL : BaseDAL<Fund2PortfolioSummary>
    {
        private readonly AjaxResult ajaxResult = null;

        public Fund2PortfolioSummaryBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        public List<Fund2PortfolioSummary> GetList(string strWhere = "")
        {
            //var user = new MemberBLL().GetLogOnUser();
            string str = "portfolioID != 'P00001' AND portfolioID != 'P00002' AND portfolioID <> ''";
            if(strWhere != "")
            {
                str += " AND (" + strWhere + ")";
            }
            var fundList = GetList(str, 1000, 0, "*", "portfolioID Desc");
            return fundList;
        }
        public List<string> GetListStr()
        {
            var user = new MemberBLL().GetLogOnUser();
            //if (!MemberBLL.adminOrSuper(user) && user.Levels != (int)MemberLevels.DealALL)
            //{
            //    return new List<string>(user.Funds.Split(','));
            //}
            var fundList = GetList("portfolioID != 'P00001' AND portfolioID != 'P00002' AND portfolioID <> ''", 1000, 0, "*", "portfolioID Desc");
            return fundList.Select(a => a.fundFamillyName).Where(a => !a.Equals("Rongqu")).Distinct().OrderBy(a => a).ToList();
        }
        public List<Fund2PortfolioSummary> GetFundList()
        {
            var user = new MemberBLL().GetLogOnUser();

            var fundList = GetList("portfolioID != 'P00001' AND portfolioID != 'P00002' AND portfolioID <> ''", 1000, 0, "*", "portfolioID Desc");
            var list = fundList.Where(a => !a.fundFamillyName.Equals("Rongqu")).Distinct(new Fund2PortfolioSummaryComparer()).OrderBy(a => a.fundFamillyName).ToList();
            return list;
        }


        public List<string> GetFundListByCompany(string company)
        {
            var list = GetList($"Name='{company}'", 1000, 0, "*", "Name Desc");
            return list.Select(val => val.fundFamillyName).Distinct().ToList();
        }
        public List<string> GetFundIDListByCompany(string company)
        {
            var list = GetList($"Name='{company}'", 1000, 0, "*", "Name Desc");
            return list.Select(val => val.fundID).ToList();
        }
        public List<Fund2PortfolioSummary> GetListBySector(string sector)
        {
            var user = new MemberBLL().GetLogOnUser();

            var fundList = GetList($"portfolioID != 'P00001' AND portfolioID != 'P00002' AND portfolioID <> '' AND fundID!='F0013' AND sector='{sector}'", 1000, 0, "*", "portfolioID Desc");
            if (!MemberBLL.adminOrSuper(user) && user.CompanyName == "高榕资本")
            {
                var dic = user.Funds.Split(',').ToDictionary(val => val, val => val);
                return fundList.Where(val => dic.ContainsKey(val.fundFamillyName)).ToList();
            }
            return fundList;
        }
        public Dictionary<string, List<string>> portfolioIDFundDic()
        {
            var fundList = GetList("portfolioID != 'P00001' AND portfolioID != 'P00002' AND portfolioID <> ''", 1000, 0, "*", "fundFamillyName Desc");
            var portList = fundList.Select(a => a.portfolioID).Distinct().ToList();
            var res = new Dictionary<string, List<string>>();
            fundList.ForEach(f =>
            {
                if (res.ContainsKey(f.portfolioID))
                {
                    if (!res[f.portfolioID].Contains(f.fundFamillyName))
                    {
                        res[f.portfolioID].Add(f.fundFamillyName);
                    }
                }
                else
                {
                    res[f.portfolioID] = new List<string>() { f.fundFamillyName };
                }
            });
            return res;
        }
    }
}
