﻿using Banyan.Code;
using Banyan.Domain;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Banyan.Apps
{
    public class ProjectScoreStageBLL : DAL.Base.BaseDAL<ProjectScoreStage>
    {
        
        public object ScoreStages(int projectId)
        {
            Project project = new ProjectBLL().GetModelByCache(projectId);
            if (project == null)
            {
                return AjaxHelper.JFail("项目不存在");
            }

            List<ProjectScoreStage> list = GetList($"ProjectId={projectId}");
            int roleId = new ProjectBLL().GetProjectRoleId(projectId);
            var ss = new ScoreService();
            object data = list.Select(item =>
            {
                return new
                {
                    item.Id,
                    item.State,
                    item.ActName,
                    item.Description,
                    item.Average,
                    PartnerScoreAvg = ss.CalPartnerScoreAvg(item.ProjectId, item.Id, roleId),
                    MedicalScoreAvg = ss.CalMedicalScoreAvg(item.ProjectId, item.Id, roleId),
                    DDScoreAvg = ss.CalDDScoreAvg(item.ProjectId, item.Id, roleId),
                    InvestScoreAvg = ss.CalInvestScoreAvg(item.ProjectId, item.Id, roleId),
                    item.People,
                    item.ProjectManagerScore,
                    AddTime = item.AddTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    EndTime = item.EndTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    ProjectName = project.Name,
                };
            });
            return AjaxHelper.JOk(data);
        }

        public object GetStageList(int projectId) // 和ScoreStages接口区别不大，时间表示不同
        {
            Member user = new MemberBLL().GetLogOnUser();
            return AjaxHelper.JOk(new ScoreService().GetStageList(user, projectId));
        }
        public object ScoreSet(ProjectScoreStage model)
        {
            bool isExist = Exists($"ProjectId={model.ProjectId} AND State=1");
            if (isExist)
                return AjaxHelper.JFail("存在未结束的评分活动，请先结束");

            model.State = 1;
            int modelId = Convert.ToInt32(Add(model));
            return modelId > 0 ? AjaxHelper.JOk("1") : AjaxHelper.JFail("创建失败");
        }

        public object ScoreSwitch(int id, int state)
        {
            ProjectScoreStage model = GetModel(id);
            if (model == null)
                return AjaxHelper.JFail("评分活动不存在");

            model.State = (byte)state;
            model.EndTime = DateTime.Now;
            bool result = Update(model, "State,EndTime");

            return result ? AjaxHelper.JOk("1") : AjaxHelper.JFail("操作失败");
        }

        /// <summary>
        /// 场次统计信息
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public bool UpdateScoreStage(int projectId, int id, string userName, int score)
        {
            ProjectScoreStage stage = GetModel(id);
            if (stage == null)
                return false;

            ProjectScore model = new ScoreBLL().GetModelBySql($"SELECT COUNT(*) AS UserId, round(AVG(score), 1) AS Score FROM ProjectScore WHERE projectId = {projectId} and StageId={id}");
            if (model == null)
                return false;

            var project = new ProjectBLL().GetModel($"id={projectId}");
            if(project.ProjectManager.Equals(userName))
            {
                stage.ProjectManagerScore = score;
            }

            stage.Average = model.Score;
            stage.People = model.UserId;
            return Update(stage, "Average,People,ProjectManagerScore");
        }
    }
}
