﻿using Banyan.Apps;
using Banyan.Code;
using Banyan.Domain;
using Banyan.Web.Filters;
using log4net;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Web.Mvc;
using Newtonsoft.Json;
using Banyan.Code.Azure;
using System.Web;
using System.Text.RegularExpressions;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using System.Net;
using System.Net.Http;

namespace Banyan.Web.Controllers
{
    [AuthFilter]
    public class AdminApiController : BaseController
    {
        private static readonly ILog log = LogManager.GetLogger("admin");
        private SysLogBLL syslogBll = new SysLogBLL();
        //private RpcClient rpcClient = new RpcClient();
        /// <summary>
        /// 保存文章信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateInput(false)]
        public JsonResult ArticleSave(Article model)
        {
            try
            {
                var ajaxResult = articleBll.Save(model);
                return Json(ajaxResult);
            }
            catch (Exception ex)
            {
                AjaxResult ajaxResult = new AjaxResult();
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = "保存失败";
                Logger.Error("专家访谈保存失败", ex);
                return Json(ajaxResult);
            }
        }

        /// <summary>
        /// 文章分页列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult ArticleList()
        {
            var ajaxResult = new ArticleBLL().GetPageList(Request.Params);
            return Json(ajaxResult);
        }
        public JsonResult GetGroupManager()
        {
            return Json(memberBll.getGroupManagerWeb());
        }

        [HttpPost]
        public JsonResult searchAttachName()
        {
            AjaxResult ajaxResult = new AttachmentBLL().searchByName(Request.Params);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult searchProjectName2(string type, string keywords)
        {
            AjaxResult ajaxResult  = new ProjectBLL().searchByName(Request.Params);
            return Json(ajaxResult);
        }

        //[HttpPost]
        //public JsonResult searchProjectMilvus(string type, string Name)
        //{
        //    AjaxResult ajaxResult = new AjaxResult();
        //    ajaxResult.data = rpcClient.search(Name);
        //    // TODO filter with permission
        //    ajaxResult.code = (int)ResultCode.success;
        //    return Json(ajaxResult);
        //}

        [HttpPost]
        public JsonResult searchProjectName()
        {
            var str = new string[] {"需匹配项目" };
            var list = new List<string>();
            var finalres = new List<ProjectBLL.MeetSearchProject>();
            for (var i = 0; i < str.Length; i++) {
                var t = new System.Collections.Specialized.NameValueCollection();
                t.Add("Name", str[i]);
                var res = new ProjectBLL().searchByName(t);
                var tmp = (List<ProjectBLL.MeetSearchProject>)res.data;
                if (tmp.Count > 0)
                {
                    list.Add(str[i]);
                    finalres.AddRange(tmp);
                }
            }
            var ttt = finalres.Select(val => val.Name).ToList();
            // 断点处
            return Json(finalres);

        }
        [HttpPost]
        public JsonResult searchType(string type, string keywords)
        {
            AjaxResult ajaxResult = null;
            switch (type)
            {
                case "0":
                    ajaxResult = new ProjectBLL().searchByName(Request.Params);
                    return Json(ajaxResult);
                case "1":
                    ajaxResult = new ProjectBLL().searchByName(Request.Params);
                    return Json(ajaxResult);
                case "5":
                    ajaxResult = new ProjectBLL().searchByName(Request.Params);
                    return Json(ajaxResult);
                default:
                    ajaxResult = new ResearchBLL().searchByName(Request.Params);
                    return Json(ajaxResult);
            }
        }
        /// <summary>
        /// 导出Excel文件
        /// </summary>
        /// <returns></returns>
        public ActionResult ExportArticles()
        {
            byte[] fileBytes = new ArticleBLL().GetListBytes(Request.Params);
            return File(fileBytes, ExcelHelper.ExcelContentType, $"专家访谈.xlsx");
        }

        /// <summary>
        /// 访谈详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult ArticleDetail(int id = 0)
        {
            var ajaxResult = new ArticleBLL().GetArticleDetail(id);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult ArticleSet()
        {
            var ajaxResult = new ArticleBLL().FieldSet(Request.Params, loginUser);
            return Json(ajaxResult);
        }
        [HttpGet]
        public bool Ping()
        {
            return HttpMethods.HttpLLMPing();
        }
        // LLM相关类定义已移至LLMController，以下为临时兼容性定义
        private LLMController llmController = new LLMController();

       

        [HttpPost]
        public async Task<ActionResult> ExtractText(HttpPostedFileBase file)  // Changed from IFormFile to HttpPostedFileBase
        {
            if (file == null || file.ContentLength == 0)  // Changed from Length to ContentLength
                return Json(new { success = false, message = "No file uploaded" }); // 返回 JSON 错误

            // 获取原始文件扩展名
            var fileExtension = Path.GetExtension(file.FileName).ToLower();
            
            // 检查文件扩展名
            if (!new[] { ".pdf", ".doc", ".docx" }.Contains(fileExtension))
            {
                return Json(new { success = false, message = $"不支持的文件格式: {fileExtension}，仅支持PDF和Word文档" });
            }
            
            // 创建带正确扩展名的临时文件路径
            var tempFilePath = Path.GetTempFileName();
            var tempFileWithCorrectExt = Path.ChangeExtension(tempFilePath, fileExtension);
            
            try
            {
                // 保存上传的文件到临时路径（使用正确的扩展名）
                using (var stream = new FileStream(tempFileWithCorrectExt, FileMode.Create))
                {
                    await file.InputStream.CopyToAsync(stream);  // Changed from CopyToAsync to using InputStream
                }
                
                Logger.Info($"开始处理文件: {file.FileName}, 大小: {file.ContentLength} bytes, 临时路径: {tempFileWithCorrectExt}");
                
                // 检查临时文件是否存在且有内容
                if (!System.IO.File.Exists(tempFileWithCorrectExt))
                {
                    return Json(new { success = false, message = "临时文件创建失败" });
                }
                
                var fileInfo = new FileInfo(tempFileWithCorrectExt);
                if (fileInfo.Length == 0)
                {
                    return Json(new { success = false, message = "上传的文件为空" });
                }
                
                Logger.Info($"临时文件已创建，大小: {fileInfo.Length} bytes");
                
                // 提取文本
                var imageConverter = new ImageConvert();
                var text = imageConverter.extractText(tempFileWithCorrectExt, 1, int.MaxValue);
                
                Logger.Info($"文本提取完成，长度: {(text?.Length ?? 0)} 字符");
                
                if (string.IsNullOrEmpty(text))
                {
                    return Json(new { success = false, message = "文档中没有找到可提取的文本内容，可能是图片PDF或加密文档" });
                }
                
                return Json(new { text = text, success = true });
            }
            catch (Exception ex)
            {
                Logger.Error($"文档文本提取失败: {file.FileName}", ex);
                return Json(new { success = false, message = $"文档处理失败: {ex.Message}" });
            }
            finally
            {
                // 清理临时文件
                try
                {
                    if (System.IO.File.Exists(tempFilePath))
                    {
                        System.IO.File.Delete(tempFilePath);
                        Logger.Info($"原始临时文件已删除: {tempFilePath}");
                    }
                    if (System.IO.File.Exists(tempFileWithCorrectExt))
                    {
                        System.IO.File.Delete(tempFileWithCorrectExt);
                        Logger.Info($"扩展名临时文件已删除: {tempFileWithCorrectExt}");
                    }
                }
                catch (Exception ex)
                {
                    Logger.Warn($"删除临时文件失败", ex);
                }
            }
        }
        
        [HttpPost]
        [ValidateInput(false)]
        public async Task<ActionResult> ChatStreamPost()
        {
            // 设置响应头
            Response.ContentType = "text/event-stream";
            Response.Headers.Add("Cache-Control", "no-cache");
            Response.Headers.Add("Connection", "keep-alive");
            Response.Headers.Add("Access-Control-Allow-Origin", "*");

            var cancellationTokenSource = new CancellationTokenSource();
            var cancellationToken = cancellationTokenSource.Token;

            var user = memberBll.GetLogOnUser();
            
            // 获取参数
            string model = WebHelper.GetValue("Model", "deepseek-r1-0528", Request.Params);
            string originalPrompt = WebHelper.GetValue("Prompt", "", Request.Params);
            string historyJson = WebHelper.GetValue("History", "[]", Request.Params);
            bool enableMCP = WebHelper.GetValue("enableMCP", "false", Request.Params) == "true";
            bool enableWebSearch = WebHelper.GetValue("enableWebSearch", "false", Request.Params) == "true";
            
            // 解析历史记录
            List<LLMController.Message> history = new List<LLMController.Message>();
            try
            {
                if (!string.IsNullOrEmpty(historyJson))
                {
                    var historyData = JsonConvert.DeserializeObject<List<dynamic>>(historyJson);
                    if (historyData != null)
                    {
                        foreach (var item in historyData)
                        {
                            history.Add(new LLMController.Message(
                                item.role?.ToString() ?? "user",
                                item.content?.ToString() ?? ""
                            ));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("解析历史记录失败", ex);
            }
            
            try
            {
                // 使用LLMController中的流式聊天方法
                await llmController.ProcessChatStreamAsync(
                    model,
                    originalPrompt,
                    history,
                    enableMCP,
                    enableWebSearch,
                    user,
                    Response,
                    cancellationToken
                );
            }
            catch (OperationCanceledException)
            {
                Logger.Info("聊天请求被取消 - 客户端断开连接");
            }
            catch (Exception ex)
            {
                if (Response.IsClientConnected)
                {
                    Response.Write($"event: error\ndata: {JsonConvert.SerializeObject(new { error = ex.Message })}\n\n");
                    Response.Flush();
                }
                Logger.Error("聊天流式响应失败", ex);
            }
            finally
            {
                cancellationTokenSource?.Dispose();
            }

            return new EmptyResult();
        }

        [HttpGet]
        public async Task<ActionResult> AnalyseStream(int id, string model= "qwen3-30b-a3b-mlx")
        {
            if (id <= 0)
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                Response.Write("data: 无效请求\n\n");
                Response.Flush();
                return new EmptyResult();
            }

            // 设置 SSE 响应头
            Response.ContentType = "text/event-stream";
            Response.Headers.Add("Cache-Control", "no-cache");
            Response.Headers.Add("Connection", "keep-alive");

            var user = memberBll.GetLogOnUser();
            var cancellationTokenSource = new CancellationTokenSource();
            var cancellationToken = cancellationTokenSource.Token;

            try
            {
                // 使用LLMController中的项目分析流式方法
                await llmController.ProcessAnalyseStreamAsync(
                    id,
                    model,
                    user,
                    Response,
                    cancellationToken
                );
            }
            catch (OperationCanceledException)
            {
                Logger.Info($"项目分析请求被取消 - 项目ID: {id}");
            }
            finally
            {
                cancellationTokenSource?.Dispose();
            }

            return new EmptyResult();
        }

        [HttpPost]
        public string Analyse(Project p)
        {
            var user = memberBll.GetLogOnUser();
            
            // 使用LLMController中的项目分析方法
            return llmController.AnalyseProject(p, user);
        }
        [HttpPost]
        public void updateProjectQuestion(int id, string ai_reasoning, string ai_question)
        {
            new ProjectBLL().UpdateQuestion(id, ai_reasoning, ai_question);
        }
        [HttpPost]
        public string LLMExtractProject(Project p)
        {
            var user = memberBll.GetLogOnUser();
            
            // 使用LLMController中的项目信息提取方法
            return llmController.LLMExtractProject(p, user);
        }
        [HttpPost]
        [ValidateInput(false)]
        public JsonResult ProjectSave(Project model, string attach = "", string contributions = "")
        {
            var ajaxResult = new ProjectBLL().Save(model, attach, contributions);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult ProjectContributionList()
        {
            var ajaxResult = new ContributionExtBLL().GetPageList(Request.Params);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult AddContribution(Contribution model)
        {
            var ajaxResult = new ContributionBLL().Save(model);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult contributionManagerConfirm(Project model)
        {
            var ajaxResult = new ProjectBLL().contributionManagerConfirm(model);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult contributionPartnerConfirm(Project model)
        {
            var ajaxResult = new ProjectBLL().contributionPartnerConfirm(model);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult DelContribution(int id = 0)
        {
            var ajaxResult = new ContributionBLL().Delete(id);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult GetContributions(int projectID = 0)
        {
            var ajaxResult = new ContributionBLL().GetContributions(projectID);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult PortfolioBindProject(PortfolioBasicInfo model)
        {
            var ajaxResult = new PortfolioBLL().Update(model);
            return Json(ajaxResult);
        }
        /// <summary>
        /// 保存项目信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateInput(false)]
        public JsonResult MeetSave(Meet model, string attach = "", string meetAttach = "")
        {
            var ajaxResult = new MeetBLL().Save(model, attach, meetAttach);
            return Json(ajaxResult);
        }

        #region ims score
        [HttpPost]
        public JsonResult ScoreStages(int id)
        {
            var ajaxResult = new ProjectScoreStageBLL().ScoreStages(id);
            return Json(ajaxResult);
        }
        [HttpPost] 
        public JsonResult GetStageList(int id = 0)
        {
            var ajaxResult = new ProjectScoreStageBLL().GetStageList(id);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult StageScores(int id)
        {
            var ajaxResult = new ScoreService().StageScores(id);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult ScoreSet(ProjectScoreStage model)
        {
            var ajaxResult = new ProjectScoreStageBLL().ScoreSet(model);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult ScoreSwitch(int id, int state)
        {
            var ajaxResult = new ProjectScoreStageBLL().ScoreSwitch(id, state);
            return Json(ajaxResult);
        }
        #endregion


        #region exit score
        [HttpPost]
        public JsonResult ExitScoreStages(int id)
        {
            var ajaxResult = new ExitScoreStageBLL().ScoreStages(id);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult GetExitStageList(int id = 0)
        {
            var ajaxResult = new ExitScoreStageBLL().GetStageList(id);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult ExitStageScores(int id)
        {
            var ajaxResult = new ScoreService().ExitStageScores(id);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult ExitScoreSet(ExitScoreStage model)
        {
            var ajaxResult = new ExitScoreStageBLL().ScoreSet(model);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult ExitScoreSwitch(int id, int state)
        {
            var ajaxResult = new ExitScoreStageBLL().ScoreSwitch(id, state);
            return Json(ajaxResult);
        }
        #endregion

        [HttpPost]
        public JsonResult PortfolioExitList()
        {
            var ajaxResult = new PortfolioExitBLL().GetPageList(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult PortfolioExitDetail(int id = 0)
        {
            var ajaxResult = new PortfolioExitBLL().GetDetail(id);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult PortfolioExitSave(PortfolioExitExt model, string attach = "")
        {
            var ajaxResult = new PortfolioExitBLL().Save(model, attach);
            return Json(ajaxResult);
        }
        public JsonResult PortfolioExitDelete(int id)
        {
            var ajaxResult = new PortfolioExitBLL().Delete(id);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult getPortfolio(string name)
        {
            var portfolio = new PortfolioBLL().GetModel($"Name='{name}'");
            return Json(portfolio);
        }
        [HttpPost]
        public JsonResult AttachmentList()
        {
            var ajaxResult = new Meet_project_attachBLL().GetPageList(Request.Params);
            return Json(ajaxResult);
        }
        /// <summary>
        /// 项目分页列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult ProjectList()
        {
            var ajaxResult = new ProjectBLL().GetPageList(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult ProjectListByName()
        {
            var ajaxResult = new ProjectBLL().GetPageListByNameOnly(Request.Params);
            return Json(ajaxResult);
        }

        /// <summary>
        /// 搜索项目用于聊天选择
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult SearchProjectsForChat()
        {
            string keywords = WebHelper.GetValue("keywords", string.Empty, Request.Params);
            var ajaxResult = new ProjectBLL().SearchProjectsForChat(keywords);
            return Json(ajaxResult);
        }

        /// <summary>
        /// 获取项目详细内容用于聊天
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult GetProjectContentForChat(int id = 0)
        {
            var ajaxResult = new ProjectBLL().GetProjectDetail(id, false);
            return Json(ajaxResult);
        }

        /// <summary>
        /// 联网搜索功能
        /// </summary>
        /// <param name="query">搜索关键词</param>
        /// <param name="limit">结果数量限制</param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult GetSuggestedQuestions(string model = "qwen3-30b-a3b-mlx", int count = 6)
        {
            try
            {
                var user = memberBll.GetLogOnUser();
                
                // 使用LLMController中的推荐问题生成方法
                var questions = llmController.GenerateSuggestedQuestions(model, count, user);
                
                return Json(new AjaxResult 
                { 
                    code = (int)ResultCode.success, 
                    data = questions,
                    msg = "获取推荐问题成功"
                });
            }
            catch (Exception ex)
            {
                Logger.Error("获取推荐问题失败", ex);
                
                // 发生错误时返回默认问题
                var fallbackQuestions = new List<string>
                {
                    "帮我分析一下最近的投资项目有哪些亮点？",
                    "请总结一下本周的项目进展情况",
                    "有哪些项目需要重点关注风险问题？",
                    "帮我整理一下待处理的投资决策事项",
                    "分析一下当前项目组合的行业分布情况",
                    "最近有哪些项目的财务数据需要更新？"
                }.Take(count).ToList();
                
                return Json(new AjaxResult 
                { 
                    code = (int)ResultCode.success, 
                    msg = "获取推荐问题成功（使用默认问题）",
                    data = fallbackQuestions
                });
            }
        }

        [HttpPost]
        public async Task<JsonResult> WebSearch(string query, int limit = 8)
        {
            var ajaxResult = new AjaxResult();
            try
            {
                if (string.IsNullOrWhiteSpace(query))
                {
                    ajaxResult.code = (int)ResultCode.failed;
                    ajaxResult.msg = "搜索关键词不能为空";
                    return Json(ajaxResult);
                }

                // 使用LLMController中的联网搜索方法
                var searchResults = await llmController.PerformWebSearchAsync(query, limit);
                
                int enhancedCount = searchResults.Count(r => r.IsContentEnhanced);
                
                ajaxResult.code = (int)ResultCode.success;
                ajaxResult.data = searchResults;
                
                Logger.Info($"LLM_REQUEST:搜索查询: '{query}', 最终返回结果: {searchResults.Count} 条，其中增强内容: {enhancedCount} 条");
                
                string debugInfo = searchResults.Count < limit ? 
                    $"（请求{limit}条，实际获得{searchResults.Count}条）" : "";
                    
                ajaxResult.msg = enhancedCount > 0 
                    ? $"找到 {searchResults.Count} 条搜索结果，其中 {enhancedCount} 条已增强内容，已智能过滤不相关内容{debugInfo}"
                    : $"找到 {searchResults.Count} 条搜索结果，已智能过滤不相关内容{debugInfo}";
            }
            catch (Exception ex)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = "搜索失败：" + ex.Message;
                Logger.Error("Web search failed", ex);
            }
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult SummaryNodes()
        {
            var ajaxResult = new SummaryBLL().GetData(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult SummaryNodesLink()
        {
            var ajaxResult = new SummaryBLL().GetDataLink(Request.Params);
            return Json(ajaxResult);
        }
        /// <summary>
        /// 项目分页列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult MeetList()
        {
            var ajaxResult = new MeetBLL().GetPageList(Request.Params);
            return Json(ajaxResult);
        }
        /// <summary>
        /// 项目分页列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult MyProjectList()
        {
            var ajaxResult = new ProjectBLL().GetPageList(Request.Params);
            return Json(ajaxResult);
        }
        /// <summary>
        /// 导出Excel文件
        /// </summary>
        /// <returns></returns>
        public ActionResult ExportProjects()
        {
            byte[] fileBytes = new ProjectBLL().GetListBytes(Request.Params);
            return File(fileBytes, ExcelHelper.ExcelContentType, $"项目约见.xlsx");
        }

        /// <summary>
        /// 项目详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult ProjectDetail(int id = 0, int init = 0)
        {
            var ajaxResult = new ProjectBLL().GetProjectDetail(id, init > 0);
            return Json(ajaxResult);
        }

        public JsonResult GetRevisitRight(int id)
        {
            var ajaxResult = new ProjectBLL().GetRevisitRight(id);
            return Json(ajaxResult);
        }
        /// <summary>
        /// 研究报告详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult ResearchDetail(int id = 0, int init = 0)
        {
            var ajaxResult = new ResearchBLL().GetDetail(id, init > 0);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult GetDocImage(int id = 0)
        {
            return Json(new ResearchBLL().GetAttach(id));
        }
        [HttpPost]
        public JsonResult GetComments()
        {
            var ajaxResult = new CommentBLL().GetComments(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult ProjectRelatedNews()
        {
            var ajaxResult = new NewsBLL().ProjectedRelatedNewsWeb(Request.Params);
            return Json(ajaxResult);
        }
        /// <summary>
        /// 会议详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult MeetDetail(int id = 0, int init = 0)
        {
            var ajaxResult = new MeetBLL().GetMeetDetail(id, init > 0);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult DealList()
        {
            var ajaxResult = new Project_ActiveBLL().GetPageList(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult getDealsByPortfolioID(string id)
        {
            var ajaxResult = new Project_ActiveBLL().GetDealsByPortfolioID(id);
            return Json(ajaxResult);
        }

        public ActionResult ExportDeals()
        {
            byte[] fileBytes = new Project_ActiveBLL().GetListBytes(Request.Params);
            return File(fileBytes, ExcelHelper.ExcelContentType, $"项目约见.xlsx");
        }

        [HttpPost]
        public JsonResult DealDetail(string id, int init = 0)
        {
            var ajaxResult = new Project_ActiveBLL().GetProjectDetail(id, init > 0);
            return Json(ajaxResult);
        }

        /// <summary>
        /// 导出Excel文件
        /// </summary>
        /// <returns></returns>
        //public ActionResult ExportPortfolioInfos()
        //{
        //    byte[] fileBytes = new ProjectBLL().GetListBytes(Request.Params);
        //    return File(fileBytes, ExcelHelper.ExcelContentType, $"项目.xlsx");
        //}
        [HttpPost]
        public JsonResult PortfolioExitDocs(int id = 0)
        {
            var ajaxResult = new AttachmentFMSBLL().GetPortfolioExitDocs(id);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult ProjectDocs(int id = 0)
        {
            var ajaxResult = new ProjectBLL().GetProjectDocs(id);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult MeetDocs(int id = 0)
        {
            var ajaxResult = new MeetBLL().GetMeetDocs(id);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult MeetAttaches(int id = 0)
        {
            var ajaxResult = new MeetBLL().GetMeetAttaches(id);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult CompleteScore(int pid, string score)
        {
            var ajaxResult = new ProjectBLL().SetProjectCompleteScore(pid, score);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult AddProjectDoc(Attachment model)
        {
            model.AtUrl = model.AtUrl.Replace(System.Configuration.ConfigurationManager.AppSettings["FileDomain"] + "/", "/");
            try
            {
                if (!model.Content.IsEmpty() && !model.Content.Equals("-1"))
                {
                    model.Content = syslogBll.GetModel(Convert.ToInt32(model.Content)).Description;
                }
            }catch(Exception e)
            {
                Logger.Error(e.Message, e);
            }
            //if (model.AtSuffix !="DD" && model.AtSuffix != "BP" && new ProjectBLL().ExistProjectDocType(model.SourceId, model.AtSuffix))
            //{
            //    if(!new AttachmentBLL().DelDoc(model.SourceId, model.AtSuffix))
            //    {
            //        log.Info(model);
            //    }
            //}
            var ajaxResult = new AttachmentBLL().SaveDoc(model);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult AddPortfolioExitDoc(AttachmentFMS model)
        {
            model.AtUrl = model.AtUrl.Replace(System.Configuration.ConfigurationManager.AppSettings["FileDomain"] + "/", "/");
            try
            {
                if (!model.Content.IsEmpty() && !model.Content.Equals("-1"))
                {
                    model.Content = syslogBll.GetModel(Convert.ToInt32(model.Content)).Description;
                }
            }
            catch (Exception e)
            {
                Logger.Error(e.Message, e);
            }
            //if (model.AtSuffix !="DD" && model.AtSuffix != "BP" && new ProjectBLL().ExistProjectDocType(model.SourceId, model.AtSuffix))
            //{
            //    if(!new AttachmentBLL().DelDoc(model.SourceId, model.AtSuffix))
            //    {
            //        log.Info(model);
            //    }
            //}
            var ajaxResult = new AttachmentFMSBLL().SaveDoc(model);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult AddProjectImage(Attachment model)
        {
            model.AtUrl = model.AtUrl.Replace(System.Configuration.ConfigurationManager.AppSettings["FileDomain"] + "/", "/");
            model.AtSuffix = "Image";
            var ajaxResult = new AttachmentBLL().SaveDoc(model);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult AddMeetDoc(Attachment model)
        {
            model.AtUrl = model.AtUrl.Replace(System.Configuration.ConfigurationManager.AppSettings["FileDomain"] + "/", "/");
            try
            {
                if (!model.Content.IsEmpty() && !model.Content.Equals("-1"))
                {
                    model.Content = syslogBll.GetModel(Convert.ToInt32(model.Content)).Description;
                }
            }
            catch (Exception e)
            {
                Logger.Error(e.Message, e);
            }
            model.SourceType = (byte)SourceTypeEnum.Meet;
            //if (model.AtSuffix !="DD" && model.AtSuffix != "BP" && new ProjectBLL().ExistProjectDocType(model.SourceId, model.AtSuffix))
            //{
            //    if(!new AttachmentBLL().DelDoc(model.SourceId, model.AtSuffix))
            //    {
            //        log.Info(model);
            //    }
            //}
            var ajaxResult = new AttachmentBLL().SaveDoc(model);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult AddMeetAttach(MeetAttach model)
        {
            //if (model.AtSuffix !="DD" && model.AtSuffix != "BP" && new ProjectBLL().ExistProjectDocType(model.SourceId, model.AtSuffix))
            //{
            //    if(!new AttachmentBLL().DelDoc(model.SourceId, model.AtSuffix))
            //    {
            //        log.Info(model);
            //    }
            //}
            var ajaxResult = new MeetAttachBLL().SaveDoc(model);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult MeetAttachRank(int id, int order)
        {
            var ajaxResult = new MeetAttachBLL().Rank(id, order);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult DelProjectDoc(int id = 0)
        {
            var ajaxResult = new AttachmentBLL().DelDoc(id);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult DelPortfolioExitDoc(int id = 0)
        {
            var ajaxResult = new AttachmentFMSBLL().DelDoc(id);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult DelMeetDoc(int id = 0)
        {
            var ajaxResult = new AttachmentBLL().DelDoc(id);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult DelMeetAttach(int id = 0)
        {
            var ajaxResult = new MeetAttachBLL().DelDoc(id);
            return Json(ajaxResult);
        }
        /// <summary>
        /// 更新项目字段
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult ProjectSet()
        {
            var ajaxResult = new ProjectBLL().FieldSet(Request.Params, loginUser);
            return Json(ajaxResult);
        }
        /// <summary>
        /// 更新项目字段
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult MeetSet()
        {
            var ajaxResult = new MeetBLL().FieldSet(Request.Params, loginUser);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult ProjectSameNameCheck(string pname = "")
        {
            var ajaxResult = new ProjectService().ProjectSameNameCheck(pname);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult ProjectSimilarNameCheck(string pname = "")
        {
            var ajaxResult = new ProjectService().ProjectSimilarNameCheck(pname);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult founderSimilarCheck(string pname = "")
        {
            var ajaxResult = new ProjectService().FounderSimilarCheck(pname);
            return Json(ajaxResult);
        }
        public JsonResult GetUser(int userid = 0)
        {
            var ajaxResult = new MemberBLL().GetUser(userid);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult UserCreate()
        {
            string attrs = WebHelper.GetValue("attrs", "", Request.Params);
            var attrsDic = JsonConvert.DeserializeObject<Dictionary<string, string>>(attrs);
            var ajaxResult = new MemberBLL().CreateUnderReview(attrsDic);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult UserList() // research admin
        {
            var ajaxResult = new MemberBLL().GetPageList(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult UserListDetail() // all admin
        {
            var ajaxResult = new MemberBLL().GetPageList(Request.Params, true);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult UserSingleGroup()
        { // 项目编辑默认添加用户唯一组
            var ajaxResult = memberBll.UserSingleGroup();
            return Json(ajaxResult);
        }
        /// <summary>
        /// 保存用户
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult UserSaveGroup()
        {
            string groups = WebHelper.GetValue("groups", "", Request.Params);
            string name = WebHelper.GetValue("name", "", Request.Params);
            string mobile  = WebHelper.GetValue("mobile", "", Request.Params);
            string mail = WebHelper.GetValue("mail", "", Request.Params);
            string description = WebHelper.GetValue("description", "", Request.Params);
            int isResearch = WebHelper.GetValueInt("isResearch", 0, Request.Params);
            var groupsDic = JsonConvert.DeserializeObject<Dictionary<string, string>>(groups);
            var ajaxResult = memberBll.SaveGroup(name, groupsDic, mobile, mail, description, isResearch == 1);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult UserMock()
        {

            string groups = WebHelper.GetValue("groups", "", Request.Params);
            string name = WebHelper.GetValue("name", "", Request.Params);
            var groupsDic = JsonConvert.DeserializeObject<Dictionary<string, string>>(groups);
            var ajaxResult = new MemberBLL().SaveGroupMock(groupsDic, name);
            return Json(ajaxResult);
        }
        public JsonResult GetCreators(int all = 0)
        {
            var ajaxResult = new MemberBLL().GetCreators(Request.Params);
            return Json(ajaxResult);
        }
        /// <summary>
        /// Creator Summary
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult GetCreatorsSummary()
        {
            var ajaxResult = new MemberBLL().GetCreatorsSummary(Request.Params);
            return Json(ajaxResult);
        }
   
        /// <summary>
        /// 文章分类列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult CommentList()
        {
            var ajaxResult = new CommentBLL().GetPageList(Request.Params);
            return Json(ajaxResult);
        }

        /// <summary>
        /// 评论设置
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult CommentSet()
        {
            var ajaxResult = new CommentBLL().FieldSet(Request.Params);
            return Json(ajaxResult);
        }
        // rename function so not error calling commentset function
        public JsonResult UserComment(int aid = 0, int pid = -1, string content = "")
        {
            return Json(new CommentBLL().CommentSet(loginUser, aid, content, pid));
        }

        public JsonResult AddMemo()
        {
            return Json(new ProjectMemoBLL().memoSet(Request.Params, loginUser));
        }

        public JsonResult SaveMemo(ProjectMemo model)
        {
            return Json(new ProjectMemoBLL().memoSave(model, loginUser));
        }

        public JsonResult DeleteMemo(ProjectMemo model)
        {
            return Json(new ProjectMemoBLL().memoDelete(model, loginUser));
        }

        public JsonResult GetMemos()
        {
            return Json(new ProjectMemoBLL().Getmemos(Request.Params));
        }

        /// <summary>
        /// 注销退出
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult LogOut()
        {
            var ajaxResult = new MemberBLL().LogOut();
            return Json(ajaxResult);
        }

        public object JOk(object data, string msg = "", int code = 0, int count = 0)
        {
            return new { code, data, msg, count };
        }

        public object JOkStr(object data, string msg = "", string content = "", int code = 0, int count = 0)
        {
            return new { code, data, content, msg, count };
        }

        public object JFail(string msg = "", int code = 1)
        {
            return new { code, msg };
        }
        public JsonResult ProjectUpload()
        {
            var ajaxResult = new ProjectBLL().Import();
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult UploadMeetSummary()
        {
            var file = System.Web.HttpContext.Current.Request.Files[0];
            var fileName = file.FileName.Replace("+", "加").Replace(" ", "_");
            AjaxResult ajaxResult = new AjaxResult();
            String newFileName = fileName;

            String savePath = "/";
            String dirPath = Server.MapPath(savePath);
            String disk = dirPath.Split(':')[0];
            dirPath = disk + ":\\invest\\imsfiles\\";
            string directory = dirPath + $"meetnote\\{DateTime.Now.ToString("yyyyMMdd")}\\";

            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            if (file.ContentLength == 0)
            {
                ajaxResult.code = (int)ResultCode.exception;
                return Json(ajaxResult);
            }
            else if (file.ContentLength > 1024 * 1024 * 15)
            {
                ajaxResult.msg = "上传文件大小不超过15M";
                ajaxResult.code = (int)ResultCode.exception;
                return Json(ajaxResult);
            }
            var filePath = directory + newFileName;
            file.SaveAs(filePath);

            new AzureBlob().UploadToShareFile("imspdf", $"meetsummary", file);
            ajaxResult.code = (int)ResultCode.success;
            //HttpPostedFile imgFile = context.Request.Files["file"];
            ajaxResult.data = filePath;
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult BatchSaveDocs(string[] paths)
        {
            AjaxResult ajaxResult = new AjaxResult();
            var user = memberBll.GetLogOnUser();
            var attachmentBll = new AttachmentBLL();
            string directoryBase = Server.MapPath($"/ppt");
            ajaxResult.msg = "";
            var errCount = 0;
            for (int i = 0; i < paths.Length; i++)
            {
                try
                {
                    var article = new Attachment();
                    var filePath = paths[i];
                    var fileName = Path.GetFileName(filePath);
                    article.Path = filePath.Substring(9).Replace(@"\", "/"); ; // 去掉盘符构建url
                    article.Creator = user.RealName;
                    article.SourceType = (Byte)SourceTypeEnum.Meet;
                    article.AtName = Path.GetFileNameWithoutExtension(filePath);
                    article.AtSuffix = "RECORD";
                    try
                    {
                        var res = Regex.Match(article.AtName, @"(\d{8})$");
                        article.AddTime = DateTime.ParseExact(res.Groups[1].Value, "yyyyMMdd", System.Globalization.CultureInfo.InvariantCulture);
                    }catch(Exception)
                    {
                        throw new Exception($"文件名{article.AtName}日期无效");
                    }
                    int id = Convert.ToInt32(attachmentBll.Add(article));
                    if (id == 0)
                    {
                        throw new Exception($"{article.AtName}未保存");
                    }
                    string directory = directoryBase + $"\\{DateTime.Now.ToString("yyyyMMdd")}\\{id}";
                    if (!Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }
                    ImageConvert converters = new ImageConvert
                    {
                        pptRedisKey = $"ppt"
                    };
                    List<string> fileList = null;
                    string textContent = "";

                    var extension = Path.GetExtension(filePath);
                    // 将源文件转换为图片
                    if (extension == ".doc" || extension == ".docx")
                    {
                        fileList = converters.Word2ImageConverter(filePath, directory, out textContent);
                    }
                    else if (extension == ".pptx" || extension == ".ppt")
                    {
                        fileList = converters.Ppt2ImageConverter(filePath, directory, out textContent);
                    }
                    else if (extension == ".pdf")
                    {
                        fileList = converters.Pdf2ImageConverter(filePath, directory, out textContent);
                    }
                   
                    article.Id = id;
                    article.AtUrl = string.Join(",", fileList.ToArray());
                    article.AtUrl = article.AtUrl.Replace(Server.MapPath("~/"), "/").Replace(@"\", "/");
                    article.Content = textContent;
                    attachmentBll.Update(article, "AtUrl, Content");
                    new AttachmentBLL().SaveDoc(article);
                    Logger.Info($" 上传成功 {article.AtName} ", user.RealName);
                }
                catch (Exception e)
                {
                    errCount++;
                    Logger.Error($" 上传失败 {e.Message} ", e, user.RealName);
                    ajaxResult.msg += $"{e.Message},该文件上传失败!";
                }
            }
            if(ajaxResult.msg == "")
            {
                ajaxResult.code = (int)ResultCode.success;
                ajaxResult.msg = "上传成功";
            }else
            {
                ajaxResult.msg = $"出现{errCount}个错误：{ajaxResult.msg}";
                ajaxResult.code = (int)ResultCode.exception;
            }
            return Json(ajaxResult);
        }
        protected override JsonResult Json(object data, string contentType,
  Encoding contentEncoding, JsonRequestBehavior behavior)
        {
            return new JsonResult()
            {
                Data = data,
                ContentType = contentType,
                ContentEncoding = contentEncoding,
                JsonRequestBehavior = behavior,
                MaxJsonLength = Int32.MaxValue
            };
        }

        [HttpPost]
        public JsonResult PerformanceReport(string PMName, decimal USDtoRMBRate = 7, decimal recommendWeight = 1, decimal PManagerWeight = 1, decimal PIManagerWeight = 1, decimal PMemberWeight = 1, decimal PIMemberWeight = 1, bool combine = false)
        {
            Member user = new MemberBLL().GetLogOnUser();
            var ajaxResult = ReportsGenerator.getPerformanceTable(user.RealName, PMName, USDtoRMBRate, recommendWeight, PManagerWeight, PIManagerWeight, PMemberWeight, PIMemberWeight, combine);
            return Json(ajaxResult, "application/json", Encoding.UTF8, JsonRequestBehavior.AllowGet);
        }
            /// <summary>
            /// 文档转码转换图片
            /// </summary>
            /// <returns></returns>
            [HttpPost]
        public JsonResult DocConvert(int id = 0)
        {
            Member user = new MemberBLL().GetLogOnUser();
            string directoryBase = Server.MapPath($"/ppt");
            string directory = directoryBase + $"/{DateTime.Now.ToString("yyyyMMdd")}/{id}";
            var file = System.Web.HttpContext.Current.Request.Files[0];
            string extension = Path.GetExtension(file.FileName);
            // 判断文件格式
            if (extension != ".doc" && extension != ".docx" && extension != ".pdf" && extension != ".ppt" && extension != ".pptx")
            {
                log.Info("上传文件格式错误：" + extension);
                return Json(JFail("上传文件格式错误"));
            }

            // 判断文件大小
            if (file.ContentLength > 1024 * 1024 * 20)
            {
                log.Info("上传文件大小不超过20M,文件大小：" + file.ContentLength);
                return Json(JFail("上传文件大小不超过20M"));
            }

            try
            {
                String savePath = "/";
                String dirPath = Server.MapPath(savePath);
                String disk = dirPath.Split(':')[0];
                dirPath = disk + ":\\invest\\IMSFiles\\";
                if (!Directory.Exists(dirPath))
                {
                    Directory.CreateDirectory(dirPath);
                }
                String ymd = DateTime.Now.ToString("yyyyMMdd", DateTimeFormatInfo.InvariantInfo);
                dirPath += ymd + "\\";
                if (!Directory.Exists(dirPath))
                {
                    Directory.CreateDirectory(dirPath);
                }
                // 保存源文件               
                string directorySave = dirPath + $"{id}";
                if (!Directory.Exists(directorySave))
                    Directory.CreateDirectory(directorySave);

                string filePathSave = directorySave + $"\\" + file.FileName.Replace("+", "加").Replace(" ", "_");
                if (System.IO.File.Exists(filePathSave))
                    System.IO.File.Delete(filePathSave);
                file.SaveAs(filePathSave);
                //end
                try
                {
#if DEBUG
#else
                    new AzureBlob().UploadToShareFile("imspdf", $"pdf", file);
#endif
                }catch(Exception e)
                {
                    Logger.Error(e.Message, e, user.RealName);
                }

                if (!Directory.Exists(directory))
                    Directory.CreateDirectory(directory);

                ImageConvert converters = new ImageConvert
                {
                    pptRedisKey = $"ppt{id}"
                };
                List<string> fileList = new List<string>();

                string textContent = "";

                // 将源文件转换为图片
                if (extension == ".doc" || extension == ".docx")
                {
                    fileList = converters.Word2ImageConverter(filePathSave, directory, out textContent);
                }
                else if (extension == ".pptx" || extension == ".ppt")
                {
                    fileList = converters.Ppt2ImageConverter(filePathSave, directory, out textContent);
                }
                else if (extension == ".pdf")
                {
                    fileList = converters.Pdf2ImageConverter(filePathSave, directory, out textContent);
                }
                string imgsrclist = string.Join(",", fileList.ToArray());

                imgsrclist = imgsrclist.Replace(Server.MapPath("~/"), "/").Replace(@"\", "/");
                //end
                Logger.Info($"项目id{id} {file.FileName}上传成功，共{fileList.Count}页", user.RealName);

                var contentId = -1;
                try
                {
                    SysLog log = new SysLog
                    {
                        Page = "ImageConvert",
                        Action = "extractText",
                        Description = textContent,
                        CreatedBy = user.RealName,
                        Ip = "",
                        CreatorId = user.Id,
                    };
                    contentId = Convert.ToInt32(syslogBll.Add(log));
                }
                catch (Exception e)
                {
                    Logger.Warn(e.Message, e, user.RealName);
                    textContent = "";
                }

                return Json(JOkStr(imgsrclist, $"{file.FileName}上传成功，共{fileList.Count}页", contentId + ""));
            }
            catch (Exception ex)
            {
                Logger.Info("项目id"+id +" " +file.FileName + "上传失败:" + ex.Message, user.RealName);
                return Json(JFail("上传失败"));
            }
            finally
            {
                //System.IO.File.Delete(filePath); // 不删除源文件
            }
        }
        [HttpPost]
        [ValidateInput(false)]
        public JsonResult AnnualReportSave(AnnualReport model)
        {
            AjaxResult ajaxResult = new AjaxResult();
            try
            {
                var Id = Convert.ToInt32(new AnnualReportBLL().Add(model));
                ajaxResult.code = Id > 0 ? (int)ResultCode.success : (int)ResultCode.exception;
                return Json(ajaxResult);
            }
            catch (Exception ex)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = "保存失败";
                Logger.Error("年终总结保存失败", ex);
                return Json(ajaxResult);
            }
        }
        [HttpPost]
        public JsonResult AnnualReportDetail(int id)
        {
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.code = (int)ResultCode.success;
            if(id == 0)
            {
                id = 16;
            }
            var res = new AnnualReportBLL().GetModel(id);
            if(id == 16)
            {
                res.Id = 0;
            }
            ajaxResult.data = res;
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult AnnualReportList()
        {
            var ajaxResult = new AnnualReportBLL().GetPageList(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult AnnualReportDelete(int id)
        {
            var ajaxResult = new AnnualReportBLL().DeleteStatus(id);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult LogSet(string Project, string Page, string Action)
        {
            var user = memberBll.GetLogOnUser();
            var model = new SysLog();
            model.Project = Project;
            model.Page = Page;
            model.Action = Action;
            model.CreatedBy = user.RealName;
            var ajaxResult = new SysLogBLL().Add(model);
            return Json(ajaxResult);
        }
 
       
        

        /// <summary>
        /// MCP搜索接口
        /// </summary>
        /// <param name="query">搜索查询</param>
        /// <param name="mcpType">MCP类型</param>
        /// <returns>搜索结果</returns>
        [HttpPost]
        [ValidateInput(false)]
        public async Task<JsonResult> McpSearch(string query, string mcpType = "IMS")
        {
            try
            {
                var user = memberBll.GetLogOnUser();
                
                // 使用LLMController中的MCP搜索方法
                var projects = await llmController.McpSearchAsync(query, mcpType, user);
                
                if (projects.Count > 0)
                {
                    var resultContent = new StringBuilder();
                    foreach (var project in projects)
                    {
                        resultContent.AppendLine($"项目: {project.Name}");
                        resultContent.AppendLine($"项目ID: {project.Id}");
                        if (!string.IsNullOrEmpty(project.Summary))
                        {
                            resultContent.AppendLine(project.Summary);
                        }
                        if (!string.IsNullOrEmpty(project.Content))
                        {
                            resultContent.AppendLine(project.Content);
                        }
                        resultContent.AppendLine();
                    }
                    
                    return Json(new AjaxResult
                    {
                        code = (int)ResultCode.success,
                        data = resultContent.ToString(),
                        msg = $"MCP搜索成功，找到 {projects.Count} 个相关项目"
                    });
                }
                else
                {
                    return Json(new AjaxResult
                    {
                        code = (int)ResultCode.success,
                        data = "",
                        msg = "未找到相关项目信息"
                    });
                }
            }
            catch (Exception ex)
            {
                Logger.Error("MCP搜索失败", ex);
                
                return Json(new AjaxResult
                {
                    code = (int)ResultCode.exception,
                    msg = "MCP搜索失败: " + ex.Message
                });
            }
        }

        [HttpPost]
        [ValidateInput(false)]
        public JsonResult GenerateSuggestedQuestionsFromResponse(string aiResponse, string userQuestion = "", string model = "qwen3-30b-a3b-mlx", int count = 6)
        {
            try
            {
                var user = memberBll.GetLogOnUser();
                
                // 使用LLMController中的基于回答内容生成推荐问题的方法
                var questions = llmController.GenerateSuggestedQuestionsFromResponse(aiResponse, userQuestion, model, count, user);

                return Json(new AjaxResult 
                { 
                    code = (int)ResultCode.success, 
                    data = questions,
                    msg = "根据回答内容生成推荐问题成功"
                });
            }
            catch (Exception ex)
            {
                Logger.Error("LLM_REQUEST:根据AI回答生成推荐问题失败", ex);
                
                // 发生错误时返回通用的后续问题
                var fallbackQuestions = new List<string>
                {
                    "还有其他相关的细节需要了解吗？",
                    "这个情况下有什么需要特别注意的风险吗？",
                    "能否提供更多关于这个话题的数据分析？",
                    "基于这个分析，下一步应该如何行动？"
                }.Take(count).ToList();
                
                return Json(new AjaxResult 
                { 
                    code = (int)ResultCode.success, 
                    msg = "生成推荐问题成功（使用默认后续问题）",
                    data = fallbackQuestions
                });
            }
        }

        /// <summary>
        /// 获取新闻向量化统计数据
        /// </summary>
        /// <returns>统计数据</returns>
        [HttpPost]
        public JsonResult GetNewsVectorStats()
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" });
                }

                // 检查管理员权限
                if (user.Levels != (byte)MemberLevels.Administrator && user.Levels != (byte)MemberLevels.SuperUser)
                {
                    return Json(new { code = 1, msg = "权限不足，只有管理员才能查看向量化统计数据" });
                }

                var newsBLL = new NewsBLL();

                // 获取总新闻数
                var totalCount = newsBLL.GetCount("");

                // 获取已向量化新闻数
                var vectorizedCount = newsBLL.GetCount("VectorStatus = 1");

                // 获取待向量化新闻数
                var pendingCount = newsBLL.GetCount("VectorStatus = 0");

                // 获取向量化失败新闻数
                var failedCount = newsBLL.GetCount("VectorStatus = 2");

                return Json(new AjaxResult
                {
                    code = 0,
                    data = new
                    {
                        totalCount,
                        vectorizedCount,
                        pendingCount,
                        failedCount
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Error($"获取新闻向量化统计数据失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "获取统计数据失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取基于用户兴趣的推荐新闻（管理员接口）
        /// </summary>
        /// <param name="userId">用户ID（管理员可指定）</param>
        /// <param name="page">页码（从1开始）</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="threshold">相似度阈值（0-1之间）</param>
        /// <param name="category">新闻分类过滤</param>
        /// <param name="source">新闻来源过滤</param>
        /// <param name="startDate">开始日期过滤</param>
        /// <param name="endDate">结束日期过滤</param>
        /// <param name="tagFilter">标签过滤</param>
        /// <returns>推荐新闻列表</returns>
        [HttpPost]
        public async Task<JsonResult> GetNewsRecommendations(
            int userId = 0,
            int page = 1,
            int pageSize = 20,
            double threshold = 0.4,
            string category = null,
            string source = null,
            string startDate = null,
            string endDate = null,
            string tagFilter = null)
        {
            try
            {
                var currentUser = new MemberBLL().GetLogOnUser();
                if (currentUser == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" });
                }

                // 如果没有指定用户ID，使用当前用户
                var targetUserId = userId > 0 ? userId : currentUser.Id;

                // 使用推荐引擎获取结果
                var recommendationEngine = new NewsRecommendationEngine();
                var result = await recommendationEngine.GetRecommendationsForApiAsync(
                    targetUserId, page, pageSize, threshold, category, source, startDate, endDate, tagFilter);

                return Json(result);
            }
            catch (Exception ex)
            {
                Logger.Error($"获取推荐新闻失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "获取推荐新闻失败: " + ex.Message });
            }
        }

    }
}