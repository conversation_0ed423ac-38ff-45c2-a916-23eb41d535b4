using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Banyan.Code;
using Newtonsoft.Json;

namespace Banyan.Apps
{
    /// <summary>
    /// 分层缓存管理器
    /// 实现内存缓存和Redis缓存的两级缓存策略，提高性能并减少Redis负载
    /// </summary>
    public class TieredCacheManager
    {
        // 单例实例
        private static readonly Lazy<TieredCacheManager> _instance = 
            new Lazy<TieredCacheManager>(() => new TieredCacheManager(), LazyThreadSafetyMode.ExecutionAndPublication);
        
        // 内存缓存，使用ConcurrentDictionary保证线程安全
        private readonly ConcurrentDictionary<string, CacheItem> _memoryCache;
        
        // 内存缓存容量限制
        private const int MAX_MEMORY_CACHE_ITEMS = 1000;
        
        // 内存缓存过期时间（秒）
        private const int MEMORY_CACHE_EXPIRY_SECONDS = 300; // 5分钟
        
        // 缓存清理定时器
        private Timer _cleanupTimer;
        
        /// <summary>
        /// 获取TieredCacheManager的单例实例
        /// </summary>
        public static TieredCacheManager Instance => _instance.Value;
        
        /// <summary>
        /// 私有构造函数，确保单例模式
        /// </summary>
        private TieredCacheManager()
        {
            _memoryCache = new ConcurrentDictionary<string, CacheItem>();
            
            // 启动定时清理任务，每分钟执行一次
            _cleanupTimer = new Timer(CleanupExpiredItems, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
            
            Logger.Info("TieredCacheManager initialized");
        }
        
        /// <summary>
        /// 从缓存获取值
        /// 先尝试从内存缓存获取，如果不存在则从Redis获取
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <returns>缓存值，如果不存在则返回默认值</returns>
        public T Get<T>(string key)
        {
            try
            {
                // 1. 尝试从内存缓存获取
                if (_memoryCache.TryGetValue(key, out CacheItem item) && !item.IsExpired())
                {
                    Logger.Debug($"Memory cache hit: {key}");
                    return (T)item.Value;
                }
                
                // 2. 如果内存缓存不存在或已过期，尝试从Redis获取
                string redisValue = RedisUtil.GetValue(key);
                if (!string.IsNullOrEmpty(redisValue))
                {
                    Logger.Debug($"Redis cache hit: {key}");
                    
                    // 尝试解析压缩数据
                    if (key.EndsWith(":compressed"))
                    {
                        try
                        {
                            byte[] compressedBytes = Convert.FromBase64String(redisValue);
                            redisValue = GZip.Decompress(compressedBytes);
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"Failed to decompress Redis value for key {key}: {ex.Message}", ex);
                            return default(T);
                        }
                    }
                    
                    // 反序列化Redis值
                    T value = JsonConvert.DeserializeObject<T>(redisValue);
                    
                    // 将值添加到内存缓存
                    _memoryCache[key] = new CacheItem(value, DateTime.Now.AddSeconds(MEMORY_CACHE_EXPIRY_SECONDS));
                    
                    return value;
                }
                
                Logger.Debug($"Cache miss: {key}");
                return default(T);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error retrieving from cache for key {key}: {ex.Message}", ex);
                return default(T);
            }
        }
        
        /// <summary>
        /// 将值存入缓存
        /// 同时存入内存缓存和Redis缓存
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="value">缓存值</param>
        /// <param name="redisExpiry">Redis缓存过期时间</param>
        /// <param name="compress">是否压缩（适用于大型对象）</param>
        /// <returns>是否成功</returns>
        public bool Set<T>(string key, T value, TimeSpan redisExpiry, bool compress = false)
        {
            try
            {
                // 1. 存入内存缓存
                _memoryCache[key] = new CacheItem(value, DateTime.Now.AddSeconds(MEMORY_CACHE_EXPIRY_SECONDS));
                
                // 2. 存入Redis缓存
                string jsonValue = JsonConvert.SerializeObject(value);
                
                if (compress && jsonValue.Length > 10240) // 如果数据大于10KB且需要压缩
                {
                    var compressedData = GZip.Compress(jsonValue);
                    RedisUtil.Set(key + ":compressed", Convert.ToBase64String(compressedData), redisExpiry);
                    Logger.Debug($"Set compressed cache: {key}, size: {compressedData.Length} bytes");
                }
                else
                {
                    RedisUtil.Set(key, jsonValue, redisExpiry);
                    Logger.Debug($"Set cache: {key}, size: {jsonValue.Length} bytes");
                }
                
                // 3. 如果内存缓存项数量超过限制，清理最旧的项
                if (_memoryCache.Count > MAX_MEMORY_CACHE_ITEMS)
                {
                    CleanupOldestItems();
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error setting cache for key {key}: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 从缓存中移除项
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <returns>是否成功</returns>
        public bool Remove(string key)
        {
            try
            {
                // 1. 从内存缓存移除
                _memoryCache.TryRemove(key, out _);
                
                // 2. 从Redis缓存移除
                RedisUtil.Delete(key);
                RedisUtil.Delete(key + ":compressed");
                
                Logger.Debug($"Removed from cache: {key}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error removing from cache for key {key}: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 清理过期的缓存项
        /// </summary>
        private void CleanupExpiredItems(object state)
        {
            try
            {
                int removedCount = 0;
                var now = DateTime.Now;
                
                foreach (var key in _memoryCache.Keys)
                {
                    if (_memoryCache.TryGetValue(key, out CacheItem item) && item.IsExpired())
                    {
                        _memoryCache.TryRemove(key, out _);
                        removedCount++;
                    }
                }
                
                if (removedCount > 0)
                {
                    Logger.Debug($"Cleaned up {removedCount} expired items from memory cache");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"Error during cache cleanup: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 清理最旧的缓存项，保持内存缓存大小在限制范围内
        /// </summary>
        private void CleanupOldestItems()
        {
            try
            {
                // 计算需要移除的项数
                int itemsToRemove = _memoryCache.Count - MAX_MEMORY_CACHE_ITEMS;
                if (itemsToRemove <= 0)
                {
                    return;
                }
                
                // 获取所有缓存项并按访问时间排序
                var items = new List<KeyValuePair<string, CacheItem>>();
                foreach (var item in _memoryCache)
                {
                    items.Add(item);
                }
                
                items.Sort((a, b) => a.Value.LastAccessed.CompareTo(b.Value.LastAccessed));
                
                // 移除最旧的项
                int removedCount = 0;
                for (int i = 0; i < itemsToRemove && i < items.Count; i++)
                {
                    if (_memoryCache.TryRemove(items[i].Key, out _))
                    {
                        removedCount++;
                    }
                }
                
                Logger.Debug($"Cleaned up {removedCount} oldest items from memory cache");
            }
            catch (Exception ex)
            {
                Logger.Error($"Error during oldest items cleanup: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 内存缓存项
        /// </summary>
        private class CacheItem
        {
            /// <summary>
            /// 缓存值
            /// </summary>
            public object Value { get; private set; }
            
            /// <summary>
            /// 过期时间
            /// </summary>
            public DateTime ExpiryTime { get; private set; }
            
            /// <summary>
            /// 最后访问时间
            /// </summary>
            public DateTime LastAccessed { get; private set; }
            
            /// <summary>
            /// 构造函数
            /// </summary>
            /// <param name="value">缓存值</param>
            /// <param name="expiryTime">过期时间</param>
            public CacheItem(object value, DateTime expiryTime)
            {
                Value = value;
                ExpiryTime = expiryTime;
                LastAccessed = DateTime.Now;
            }
            
            /// <summary>
            /// 检查缓存项是否已过期
            /// </summary>
            /// <returns>是否已过期</returns>
            public bool IsExpired()
            {
                LastAccessed = DateTime.Now; // 更新最后访问时间
                return DateTime.Now > ExpiryTime;
            }
        }
    }
}