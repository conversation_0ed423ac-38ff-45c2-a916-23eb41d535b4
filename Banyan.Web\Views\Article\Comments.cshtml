﻿@{
    Layout = "/Views/Shared/_Layout.cshtml";
}

<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li>
                    <i class="si si-pointer"></i>
                </li>
                <li>评论管理</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <div class="tab-pane active">
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="pull-right search-bar"></div>
                        <div class="toolbar-btn-action">
                            <div class="form-inline">
                                <div class="form-group">
                                    <select class="form-control" name="parentid" id="parentid" size="1">
                                        <option value="0" selected>专家访谈</option>
                                        <option value="1">项目约见</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="title-name">评论关键词</label>
                                    <input class="form-control" type="text" id="keyname" name="keyname" placeholder="请输入评论关键词...">
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-default" id="dosearch">搜索</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <table class="layui-hide" id="table-list" lay-filter="list-filter"></table>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="bartpl">
    <button class="btn btn-xs btn-danger" lay-event="delete" type="button"><i class="fa fa-close"></i> 删除</button>
</script>

@section scripts{
    <script type="text/javascript">
        var layer, date = new Date();
        var queryParams = {
            parentid: $('#parentid').val(),
        };
        layui.use(['laypage', 'layer', 'table'], function () {
            var laypage = layui.laypage,
                table = layui.table;
            layer = layui.layer;

            table.render({
                elem: '#table-list'
                , height: 500
                , url: '@(Url.Action("commentlist", "adminapi"))'
                , where: queryParams
                , page: true
                , method: 'post'
                , cols: [[
                    { field: 'Id', title: 'ID', width: 80, sort: true, fixed: 'left' }
                    , { field: 'Content', title: '评论内容' }
                    , { field: 'ArticleTitle', title: '评论文章', width: 160}
                    , { field: 'UserId', title: '用户ID', width: 120}
                    , { field: 'UserName', title: '用户名称', width: 160 }
                    , { field: 'AddTime', title: '评论时间', width: 160, templet: function (d) {
                        return (new Date(parseInt(d.AddTime.replace("/Date(", "").replace(")/", "").split("+")[0]))).pattern("yyyy-MM-dd HH:mm:ss");
                      }}
                    , { fixed: 'right', width: 120, align: 'center', toolbar: '#bartpl' }
                ]]
            });

            table.on('tool(list-filter)', function (obj) {
                var data = obj.data
                    , layEvent = obj.event;

                if (layEvent === 'delete') {
                    layer.confirm('确认删除该条评论吗？', function (index) {
                        layer.close(index);
                        fieldset(data.Id, 'delete', data.Status, res => {
                            if (res) obj.del();
                        });
                    });
                }
            });

            laypage.render({
                elem: 'pageBar'
                , count: 100
                , skin: '#208aee'
                , jump: function (obj, first) {
                    if (!first) {
                        layer.msg('第' + obj.curr + '页');
                    }
                }
            });

            $('#dosearch').on('click', function () {
                queryParams = {
                    title: $('#keyname').val(),
                    parentid: $('#parentid').val(),
                }
                console.log(queryParams);
                table.reload('table-list', {
                    where: queryParams,
                });
            });
        });

        function fieldset(id, field, state, callback) {
            $.ajax({
                type: 'POST',
                url: '@(Url.Action("commentset", "adminapi"))',
                data: { id: id, field: field, state: state },
                success: function (data) {
                    if (data.code == 0) {
                        layer.msg('操作成功！');
                    } else {
                        layer.msg('操作失败！');
                    }
                    typeof callback == 'function' && callback(data.code == 0);
                },
                error: function () {
                    layer.msg("很抱歉，请求异常！");
                }
            });
        }
    </script>
}
