﻿using Entity.Base;
using System;
using Utility;

namespace Banyan.MeetDomain
{
    [Serializable]
    [SqlTable(dbEnum.Survey)]
    public partial class Attachment
    {
        public Attachment()
        { }

        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        [SqlField]
        public int KeyId { get; set; } = 0;

        /// <summary>
        /// 类型
        /// </summary>
        [SqlField]
        public Byte Types { get; set; } = (Byte)AttachmentEnum.Document;

        /// <summary>
        /// 值
        /// </summary>
        [SqlField]
        public string KeyValue { get; set; }

        /// <summary>
        /// 添加时间
        /// </summary>
        [SqlField]
        public DateTime AddTime { get; set; } = DateTime.Now;
    }

    public enum AttachmentEnum
    {
        Document = 1,
    }

    public enum GroupsEnum
    {
        Tech = 47,
        Con = 48,
        Med = 49
    }

}
