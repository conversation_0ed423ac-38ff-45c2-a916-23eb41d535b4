using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using Banyan.Apps;
using Banyan.Domain;
using Banyan.Code;

namespace Banyan.Apps.Tests
{
    /// <summary>
    /// NewsVectorSearch类的单元测试
    /// </summary>
    [TestClass]
    public class NewsVectorSearchTests
    {
        private NewsVectorSearch _newsVectorSearch;
        private Mock<ICache> _mockCache;
        private Mock<NewsBLL> _mockNewsBLL;
        private Mock<MemberBLL> _mockMemberBLL;
        private Mock<UserProfileBLL> _mockUserProfileBLL;
        private Mock<UserTagRelationBLL> _mockUserTagRelationBLL;
        private Mock<UserInterestTagBLL> _mockUserInterestTagBLL;
        private Mock<VectorService> _mockVectorService;
        private Mock<EngagementTracker> _mockEngagementTracker;

        [TestInitialize]
        public void Setup()
        {
            // 初始化模拟对象
            _mockCache = new Mock<ICache>();
            _mockNewsBLL = new Mock<NewsBLL>();
            _mockMemberBLL = new Mock<MemberBLL>();
            _mockUserProfileBLL = new Mock<UserProfileBLL>();
            _mockUserTagRelationBLL = new Mock<UserTagRelationBLL>();
            _mockUserInterestTagBLL = new Mock<UserInterestTagBLL>();
            _mockVectorService = new Mock<VectorService>();
            _mockEngagementTracker = new Mock<EngagementTracker>();

            // 创建测试实例
            _newsVectorSearch = new NewsVectorSearch();
        }

        [TestCleanup]
        public void Cleanup()
        {
            _newsVectorSearch = null;
        }

        #region RecordUserReadNewsAsync Tests

        [TestMethod]
        public async Task RecordUserReadNewsAsync_ValidParameters_ReturnsTrue()
        {
            // Arrange
            int userId = 1;
            int newsId = 100;
            var news = new News
            {
                Id = newsId,
                Title = "测试新闻",
                Tag = "科技,投资",
                Classify = "科技新闻",
                Source = "测试来源",
                NewsVector = "0.1,0.2,0.3"
            };

            var member = new Member
            {
                Id = userId,
                RealName = "测试用户",
                OpenId = "test_openid"
            };

            // 模拟NewsBLL返回新闻
            _mockNewsBLL.Setup(x => x.GetModel(newsId)).Returns(news);
            
            // 模拟MemberBLL返回用户
            _mockMemberBLL.Setup(x => x.GetModelByCache(userId)).Returns(member);

            // 模拟缓存操作
            _mockCache.Setup(x => x.GetCache<List<int>>(It.IsAny<string>())).Returns(new List<int>());
            _mockCache.Setup(x => x.WriteCache(It.IsAny<List<int>>(), It.IsAny<string>(), It.IsAny<DateTime>()));

            // 模拟EngagementTracker
            _mockEngagementTracker.Setup(x => x.TrackViewAsync(userId, It.IsAny<string>(), newsId, "NewsVectorSearch"))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _newsVectorSearch.RecordUserReadNewsAsync(userId, newsId);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public async Task RecordUserReadNewsAsync_InvalidUserId_ReturnsFalse()
        {
            // Arrange
            int userId = 0;
            int newsId = 100;

            // Act
            var result = await _newsVectorSearch.RecordUserReadNewsAsync(userId, newsId);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public async Task RecordUserReadNewsAsync_InvalidNewsId_ReturnsFalse()
        {
            // Arrange
            int userId = 1;
            int newsId = 0;

            // Act
            var result = await _newsVectorSearch.RecordUserReadNewsAsync(userId, newsId);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public async Task RecordUserReadNewsAsync_NewsNotFound_ReturnsFalse()
        {
            // Arrange
            int userId = 1;
            int newsId = 999;

            // 模拟NewsBLL返回null
            _mockNewsBLL.Setup(x => x.GetModel(newsId)).Returns((News)null);

            // Act
            var result = await _newsVectorSearch.RecordUserReadNewsAsync(userId, newsId);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public async Task RecordUserReadNewsAsync_UpdatesReadingHistoryCache()
        {
            // Arrange
            int userId = 1;
            int newsId = 100;
            var existingHistory = new List<int> { 50, 60, 70 };
            var news = new News { Id = newsId, Title = "测试新闻" };

            _mockNewsBLL.Setup(x => x.GetModel(newsId)).Returns(news);
            _mockCache.Setup(x => x.GetCache<List<int>>(It.IsAny<string>())).Returns(existingHistory);

            // Act
            await _newsVectorSearch.RecordUserReadNewsAsync(userId, newsId);

            // Assert
            _mockCache.Verify(x => x.WriteCache(
                It.Is<List<int>>(list => list.Contains(newsId) && list.Count == 4),
                It.IsAny<string>(),
                It.IsAny<DateTime>()
            ), Times.Once);
        }

        [TestMethod]
        public async Task RecordUserReadNewsAsync_LimitsHistoryTo100Items()
        {
            // Arrange
            int userId = 1;
            int newsId = 100;
            var existingHistory = Enumerable.Range(1, 100).ToList(); // 100个历史记录
            var news = new News { Id = newsId, Title = "测试新闻" };

            _mockNewsBLL.Setup(x => x.GetModel(newsId)).Returns(news);
            _mockCache.Setup(x => x.GetCache<List<int>>(It.IsAny<string>())).Returns(existingHistory);

            // Act
            await _newsVectorSearch.RecordUserReadNewsAsync(userId, newsId);

            // Assert
            _mockCache.Verify(x => x.WriteCache(
                It.Is<List<int>>(list => list.Count == 100 && list.Contains(newsId) && !list.Contains(1)),
                It.IsAny<string>(),
                It.IsAny<DateTime>()
            ), Times.Once);
        }

        [TestMethod]
        public async Task RecordUserReadNewsAsync_DoesNotDuplicateExistingNews()
        {
            // Arrange
            int userId = 1;
            int newsId = 100;
            var existingHistory = new List<int> { 50, 60, newsId }; // 已包含该新闻
            var news = new News { Id = newsId, Title = "测试新闻" };

            _mockNewsBLL.Setup(x => x.GetModel(newsId)).Returns(news);
            _mockCache.Setup(x => x.GetCache<List<int>>(It.IsAny<string>())).Returns(existingHistory);

            // Act
            await _newsVectorSearch.RecordUserReadNewsAsync(userId, newsId);

            // Assert
            // 验证缓存没有被更新（因为新闻已存在）
            _mockCache.Verify(x => x.WriteCache(It.IsAny<List<int>>(), It.IsAny<string>(), It.IsAny<DateTime>()), Times.Never);
        }

        #endregion

        #region GetUserInterestVector Tests

        [TestMethod]
        public async Task GetUserInterestVector_ValidUserId_ReturnsVectorFromCache()
        {
            // Arrange
            int userId = 1;
            var expectedVector = new double[] { 0.1, 0.2, 0.3, 0.4 };
            var cacheKey = $"user_interest_vector:{userId}";

            _mockCache.Setup(x => x.GetCache<double[]>(cacheKey)).Returns(expectedVector);

            // Act
            var result = await _newsVectorSearch.GetUserInterestVector(userId);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(expectedVector.Length, result.Length);
            CollectionAssert.AreEqual(expectedVector, result);
        }

        [TestMethod]
        public async Task GetUserInterestVector_InvalidUserId_ReturnsNull()
        {
            // Arrange
            int userId = 0;

            // Act
            var result = await _newsVectorSearch.GetUserInterestVector(userId);

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public async Task GetUserInterestVector_NoCacheHit_CallsUserProfileBLL()
        {
            // Arrange
            int userId = 1;
            var expectedVector = new double[] { 0.1, 0.2, 0.3, 0.4 };

            _mockCache.Setup(x => x.GetCache<double[]>(It.IsAny<string>())).Returns((double[])null);
            _mockUserProfileBLL.Setup(x => x.GetUserVectorFromDatabaseAsync(userId)).ReturnsAsync(expectedVector);

            // Act
            var result = await _newsVectorSearch.GetUserInterestVector(userId);

            // Assert
            Assert.IsNotNull(result);
            CollectionAssert.AreEqual(expectedVector, result);
            _mockUserProfileBLL.Verify(x => x.GetUserVectorFromDatabaseAsync(userId), Times.Once);
        }

        #endregion

        #region GetRecommendationsByReadingHistory Tests

        [TestMethod]
        public async Task GetRecommendationsByReadingHistory_ValidParameters_ReturnsRecommendations()
        {
            // Arrange
            int userId = 1;
            int limit = 5;
            double threshold = 0.7;
            var readHistory = new List<int> { 10, 20, 30 };
            var filters = new NewsSearchFilters();

            _mockCache.Setup(x => x.GetCache<List<int>>(It.IsAny<string>())).Returns(readHistory);

            // 模拟相似新闻查询
            var similarNews = new List<NewsVectorSimilarity>
            {
                new NewsVectorSimilarity { NewsId = 40, Similarity = 0.8 },
                new NewsVectorSimilarity { NewsId = 50, Similarity = 0.75 }
            };

            // Act
            var result = await _newsVectorSearch.GetRecommendationsByReadingHistory(userId, limit, threshold, filters);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsInstanceOfType(result, typeof(List<NewsVectorSimilarity>));
        }

        [TestMethod]
        public async Task GetRecommendationsByReadingHistory_NoReadingHistory_ReturnsEmptyList()
        {
            // Arrange
            int userId = 1;
            int limit = 5;
            double threshold = 0.7;
            var filters = new NewsSearchFilters();

            _mockCache.Setup(x => x.GetCache<List<int>>(It.IsAny<string>())).Returns((List<int>)null);

            // Act
            var result = await _newsVectorSearch.GetRecommendationsByReadingHistory(userId, limit, threshold, filters);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.Count);
        }

        [TestMethod]
        public async Task GetRecommendationsByReadingHistory_InvalidUserId_ReturnsEmptyList()
        {
            // Arrange
            int userId = 0;
            int limit = 5;
            double threshold = 0.7;
            var filters = new NewsSearchFilters();

            // Act
            var result = await _newsVectorSearch.GetRecommendationsByReadingHistory(userId, limit, threshold, filters);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.Count);
        }

        [TestMethod]
        public async Task GetRecommendationsByReadingHistory_ExcludesAlreadyReadNews()
        {
            // Arrange
            int userId = 1;
            int limit = 5;
            double threshold = 0.7;
            var readHistory = new List<int> { 10, 20, 30 };
            var filters = new NewsSearchFilters();

            _mockCache.Setup(x => x.GetCache<List<int>>(It.IsAny<string>())).Returns(readHistory);

            // Act
            var result = await _newsVectorSearch.GetRecommendationsByReadingHistory(userId, limit, threshold, filters);

            // Assert
            Assert.IsNotNull(result);
            // 验证结果中不包含已读新闻
            foreach (var recommendation in result)
            {
                Assert.IsFalse(readHistory.Contains(recommendation.NewsId));
            }
        }

        #endregion

        #region Helper Methods Tests

        [TestMethod]
        public void NewsSearchFilters_Clone_CreatesDeepCopy()
        {
            // Arrange
            var original = new NewsSearchFilters
            {
                Category = "科技",
                Source = "测试来源",
                StartDate = DateTime.Now.AddDays(-7),
                EndDate = DateTime.Now,
                Tag = "投资",
                ExcludeNewsIds = new List<int> { 1, 2, 3 }
            };

            // Act
            var clone = original.Clone();

            // Assert
            Assert.AreNotSame(original, clone);
            Assert.AreEqual(original.Category, clone.Category);
            Assert.AreEqual(original.Source, clone.Source);
            Assert.AreEqual(original.StartDate, clone.StartDate);
            Assert.AreEqual(original.EndDate, clone.EndDate);
            Assert.AreEqual(original.Tag, clone.Tag);
            Assert.AreNotSame(original.ExcludeNewsIds, clone.ExcludeNewsIds);
            CollectionAssert.AreEqual(original.ExcludeNewsIds, clone.ExcludeNewsIds);
        }

        #endregion

        #region Integration Tests

        [TestMethod]
        public async Task RecordUserReadNewsAsync_FullWorkflow_UpdatesUserInterest()
        {
            // Arrange
            int userId = 1;
            int newsId = 100;
            var news = new News
            {
                Id = newsId,
                Title = "AI技术突破",
                Tag = "人工智能,科技",
                Classify = "科技新闻",
                Source = "科技日报",
                NewsVector = string.Join(",", Enumerable.Range(1, 1024).Select(i => (i * 0.001).ToString()))
            };

            var member = new Member
            {
                Id = userId,
                RealName = "张三",
                OpenId = "test_user_123"
            };

            var userInterestTag = new UserInterestTag
            {
                Id = 1,
                Name = "人工智能",
                Category = "科技",
                Keywords = "AI,人工智能"
            };

            // 设置模拟
            _mockNewsBLL.Setup(x => x.GetModel(newsId)).Returns(news);
            _mockMemberBLL.Setup(x => x.GetModelByCache(userId)).Returns(member);
            _mockCache.Setup(x => x.GetCache<List<int>>(It.IsAny<string>())).Returns(new List<int>());
            _mockUserInterestTagBLL.Setup(x => x.GetTagByName("人工智能")).Returns(userInterestTag);
            _mockUserTagRelationBLL.Setup(x => x.GetModel(It.IsAny<string>())).Returns((UserTagRelation)null);

            // Act
            var result = await _newsVectorSearch.RecordUserReadNewsAsync(userId, newsId);

            // Assert
            Assert.IsTrue(result);

            // 验证缓存更新
            _mockCache.Verify(x => x.WriteCache(
                It.Is<List<int>>(list => list.Contains(newsId)),
                It.IsAny<string>(),
                It.IsAny<DateTime>()
            ), Times.Once);

            // 验证用户行为记录
            _mockEngagementTracker.Verify(x => x.TrackViewAsync(
                userId,
                It.IsAny<string>(),
                newsId,
                "NewsVectorSearch"
            ), Times.Once);
        }

        [TestMethod]
        public async Task GetUserInterestVector_FallbackChain_WorksCorrectly()
        {
            // Arrange
            int userId = 1;
            var tagRelations = new List<UserTagRelation>
            {
                new UserTagRelation { UserId = userId, TagId = 1, Weight = 0.8 },
                new UserTagRelation { UserId = userId, TagId = 2, Weight = 0.6 }
            };

            var readHistory = new List<int> { 10, 20, 30 };
            var newsWithVector = new News
            {
                Id = 10,
                NewsVector = string.Join(",", Enumerable.Range(1, 1024).Select(i => (i * 0.001).ToString()))
            };

            // 设置模拟：缓存未命中，UserProfileBLL返回null，但有标签关联
            _mockCache.Setup(x => x.GetCache<double[]>(It.IsAny<string>())).Returns((double[])null);
            _mockUserProfileBLL.Setup(x => x.GetUserVectorFromDatabaseAsync(userId)).ReturnsAsync((double[])null);
            _mockUserTagRelationBLL.Setup(x => x.GetList(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
                .Returns(tagRelations);
            _mockCache.Setup(x => x.GetCache<List<int>>(It.IsAny<string>())).Returns(readHistory);
            _mockNewsBLL.Setup(x => x.GetModel(10)).Returns(newsWithVector);

            // Act
            var result = await _newsVectorSearch.GetUserInterestVector(userId);

            // Assert
            // 应该通过某种fallback机制返回向量
            Assert.IsNotNull(result);
        }

        #endregion

        #region Performance Tests

        [TestMethod]
        public async Task RecordUserReadNewsAsync_HighVolume_PerformsWell()
        {
            // Arrange
            var tasks = new List<Task<bool>>();
            var news = new News { Id = 100, Title = "测试新闻" };
            var member = new Member { Id = 1, RealName = "测试用户" };

            _mockNewsBLL.Setup(x => x.GetModel(It.IsAny<int>())).Returns(news);
            _mockMemberBLL.Setup(x => x.GetModelByCache(It.IsAny<int>())).Returns(member);
            _mockCache.Setup(x => x.GetCache<List<int>>(It.IsAny<string>())).Returns(new List<int>());

            var startTime = DateTime.Now;

            // Act - 模拟100个并发请求
            for (int i = 0; i < 100; i++)
            {
                tasks.Add(_newsVectorSearch.RecordUserReadNewsAsync(1, 100 + i));
            }

            var results = await Task.WhenAll(tasks);
            var endTime = DateTime.Now;

            // Assert
            Assert.IsTrue(results.All(r => r == true));
            Assert.IsTrue((endTime - startTime).TotalSeconds < 10); // 应该在10秒内完成
        }

        #endregion

        #region Error Handling Tests

        [TestMethod]
        public async Task RecordUserReadNewsAsync_DatabaseException_ReturnsFalse()
        {
            // Arrange
            int userId = 1;
            int newsId = 100;

            _mockNewsBLL.Setup(x => x.GetModel(newsId)).Throws(new Exception("数据库连接失败"));

            // Act
            var result = await _newsVectorSearch.RecordUserReadNewsAsync(userId, newsId);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public async Task GetUserInterestVector_AllFallbacksFail_ReturnsNull()
        {
            // Arrange
            int userId = 1;

            _mockCache.Setup(x => x.GetCache<double[]>(It.IsAny<string>())).Returns((double[])null);
            _mockUserProfileBLL.Setup(x => x.GetUserVectorFromDatabaseAsync(userId)).ThrowsAsync(new Exception("数据库错误"));
            _mockUserTagRelationBLL.Setup(x => x.GetList(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
                .Throws(new Exception("查询失败"));
            _mockCache.Setup(x => x.GetCache<List<int>>(It.IsAny<string>())).Returns((List<int>)null);

            // Act
            var result = await _newsVectorSearch.GetUserInterestVector(userId);

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public async Task GetRecommendationsByReadingHistory_ExceptionInSimilaritySearch_ContinuesProcessing()
        {
            // Arrange
            int userId = 1;
            int limit = 5;
            double threshold = 0.7;
            var readHistory = new List<int> { 10, 20, 30 };
            var filters = new NewsSearchFilters();

            _mockCache.Setup(x => x.GetCache<List<int>>(It.IsAny<string>())).Returns(readHistory);

            // Act
            var result = await _newsVectorSearch.GetRecommendationsByReadingHistory(userId, limit, threshold, filters);

            // Assert
            Assert.IsNotNull(result);
            // 即使有异常，也应该返回空列表而不是抛出异常
        }

        #endregion
    }
}
