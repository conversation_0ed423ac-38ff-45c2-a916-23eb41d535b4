﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;

namespace Banyan.Apps
{
    public class ContributionExtBLL : BaseDAL<ContributionExt>
    {
        private readonly AjaxResult ajaxResult = null;
        private SysLogBLL logBLL = new SysLogBLL();
        private ProjectBLL projectBll = new ProjectBLL();
        public ContributionExtBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }
        public void updateLog(string page, string action, string description, Member user, string project = "")
        {
            SysLog log = new SysLog
            {
                Page = page,
                Action = action,
                Project = project,
                Description = description,
                CreatedBy = user == null ? "" : user.RealName,
                Ip = Utility.WebHelper.GetIP(),
                CreatorId = user == null ? 0 : user.Id,
            };
            logBLL.Add(log);
        }

      
        public AjaxResult GetPageList(NameValueCollection paramValues)
        {
            Member user = new MemberBLL().GetLogOnUser();
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10000, paramValues);
            int count;
            var ProjectList = searchCommon(paramValues, user, pageIndex, pageSize, out count);
            updateLog("Web, Get Project List", "view", "getpagelist", user);
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = ProjectList;
            ajaxResult.count = count;
            return ajaxResult;
        }

        public List<ContributionExt> searchCommon(NameValueCollection paramValues, Member user,
          int pageIndex, int pageSize, out int count)
        {
            string sort;
            string strWhere = getStrWhere(paramValues, user);

            var strAll = $" select * from ({strWhere})  a where row between {(pageIndex - 1) * pageSize + 1} and {pageIndex * pageSize}";

            Logger.Info("project contribution search str: " + strWhere, user.RealName);

            var ProjectList = GetListBySql(strAll);

            count = GetListBySql(strWhere).Count;
            return ProjectList;
        }
        private string getStrWhere(NameValueCollection paramValues, Member user)
        {
            string strWhere = @" select pb.Name, pb.portfolioID, c.*, row_number() over(order by projectID DESC) as row from Contribution c 
                                left join PortfolioBasicInfo pb
                                on c.projectID = pb.contributionProjectID  where id <> -1 ";

            if (!(user.Levels == (int)MemberLevels.Administrator || user.Levels == (int)MemberLevels.SuperUser))
            {
                strWhere += $"AND (creator='{user.RealName}' OR username='{user.RealName}') ";
            }

            string creator = WebHelper.GetValue("creator", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(creator))
            {
                strWhere += $"AND username='{creator}' ";
            }

            string keyname = WebHelper.GetValue("keyname", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(keyname))
            {
                strWhere += $"AND (projectname like '%{keyname}%' OR Name like '%{keyname}%' OR username like '%{creator}%' ) ";
            }

            //strWhere += " order by  projectID DESC ";
            return strWhere;
        }
    

    }
}
