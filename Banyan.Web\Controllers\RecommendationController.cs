using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;
using Banyan.Apps;
using Banyan.Code;
using Banyan.Domain;
using Banyan.Web.Filters;

namespace Banyan.Web.Controllers
{
    /// <summary>
    /// Controller for displaying personalized news recommendations
    /// </summary>
    public class RecommendationController : BaseController
    {
        private readonly NewsRecommendationEngine _recommendationEngine;
        private readonly EngagementTracker _engagementTracker;
        private readonly NewsRecommendationsBLL _newsRecommendationsBLL;

        /// <summary>
        /// Constructor
        /// </summary>
        public RecommendationController()
        {
            _recommendationEngine = new NewsRecommendationEngine();
            _engagementTracker = new EngagementTracker();
            _newsRecommendationsBLL = new NewsRecommendationsBLL();
        }

        /// <summary>
        /// Main recommendation page
        /// </summary>
        /// <param name="category">Optional category filter</param>
        /// <param name="source">Optional source filter</param>
        /// <param name="startDate">Optional start date filter</param>
        /// <param name="endDate">Optional end date filter</param>
        /// <param name="tag">Optional tag filter</param>
        /// <returns>View with personalized recommendations</returns>
        public async Task<ActionResult> Index(string category = "", string source = "", 
            DateTime? startDate = null, DateTime? endDate = null, string tag = "")
        {
            try
            {
                // Get current user
                var user = loginUser;
                if (user == null)
                {
                    return RedirectToAction("Index", "Login");
                }

                // Create filters
                var filters = new Banyan.Apps.NewsSearchFilters
                {
                    Category = category,
                    Source = source,
                    StartDate = startDate,
                    EndDate = endDate,
                    Tag = tag
                };

                // Store filters in ViewBag for UI
                ViewBag.Category = category;
                ViewBag.Source = source;
                ViewBag.StartDate = startDate;
                ViewBag.EndDate = endDate;
                ViewBag.Tag = tag;

                // Get available categories and sources for filter dropdowns
                var newsBLL = new NewsBLL();
                ViewBag.Categories = newsBLL.GetCategories();
                ViewBag.Sources = newsBLL.GetSources();

                // Get user information
                ViewBag.UserName = user.RealName;
                ViewBag.UserId = user.Id;

                // Log page view
                Logger.Info($"User {user.RealName} viewed recommendations page");

                return View();
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in Recommendation Index: {ex.Message}", ex);
                ViewBag.ErrorMessage = "An error occurred while loading recommendations.";
                return View(new List<News>());
            }
        }

        /// <summary>
        /// API endpoint to get personalized recommendations
        /// </summary>
        /// <param name="limit">Number of recommendations to return</param>
        /// <param name="category">Optional category filter</param>
        /// <param name="source">Optional source filter</param>
        /// <param name="startDate">Optional start date filter</param>
        /// <param name="endDate">Optional end date filter</param>
        /// <param name="tag">Optional tag filter</param>
        /// <returns>JSON result with recommendations</returns>
        [HttpGet]
        public async Task<JsonResult> GetPersonalizedRecommendations(int limit = 10, string category = "", 
            string source = "", DateTime? startDate = null, DateTime? endDate = null, string tag = "")
        {
            var result = new AjaxResult();

            try
            {
                // Get current user
                var user = loginUser;
                if (user == null)
                {
                    result.code = (int)ResultCode.noright;
                    result.msg = "User not logged in";
                    return Json(result, JsonRequestBehavior.AllowGet);
                }

                // Create filters
                var filters = new Banyan.Apps.NewsSearchFilters
                {
                    Category = category,
                    Source = source,
                    StartDate = startDate,
                    EndDate = endDate,
                    Tag = tag
                };

                // Get personalized recommendations
                var recommendations = await _recommendationEngine.GetPersonalizedRecommendationsAsync(
                    user.Id, limit, 0.4, filters);

                // Return success result
                result.code = (int)ResultCode.success;
                result.data = recommendations;
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in GetPersonalizedRecommendations: {ex.Message}", ex);
                result.code = (int)ResultCode.failed;
                result.msg = "Failed to get recommendations: " + ex.Message;
                return Json(result, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// API endpoint to get hybrid recommendations (mix of personalized and popular)
        /// </summary>
        /// <param name="limit">Number of recommendations to return</param>
        /// <param name="interestRatio">Ratio of interest-based to popular recommendations (0-1)</param>
        /// <param name="category">Optional category filter</param>
        /// <param name="source">Optional source filter</param>
        /// <param name="startDate">Optional start date filter</param>
        /// <param name="endDate">Optional end date filter</param>
        /// <param name="tag">Optional tag filter</param>
        /// <returns>JSON result with recommendations</returns>
        [HttpGet]
        public async Task<JsonResult> GetHybridRecommendations(int limit = 10, double interestRatio = 0.7, 
            string category = "", string source = "", DateTime? startDate = null, DateTime? endDate = null, string tag = "")
        {
            var result = new AjaxResult();

            try
            {
                // Get current user
                var user = loginUser;
                if (user == null)
                {
                    result.code = (int)ResultCode.noright;
                    result.msg = "User not logged in";
                    return Json(result, JsonRequestBehavior.AllowGet);
                }

                // Create filters
                var filters = new Banyan.Apps.NewsSearchFilters
                {
                    Category = category,
                    Source = source,
                    StartDate = startDate,
                    EndDate = endDate,
                    Tag = tag
                };

                // Get hybrid recommendations
                var recommendations = await _recommendationEngine.GetHybridRecommendationsAsync(
                    user.Id, limit, interestRatio, filters);

                // Return success result
                result.code = (int)ResultCode.success;
                result.data = recommendations;
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in GetHybridRecommendations: {ex.Message}", ex);
                result.code = (int)ResultCode.failed;
                result.msg = "Failed to get recommendations: " + ex.Message;
                return Json(result, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// API endpoint to track recommendation clicks
        /// </summary>
        /// <param name="newsId">News ID that was clicked</param>
        /// <param name="source">Source of the click (web or email)</param>
        /// <returns>JSON result indicating success or failure</returns>
        [HttpPost]
        public async Task<JsonResult> TrackClick(int newsId, string source = "web")
        {
            var result = new AjaxResult();

            try
            {
                // Get current user
                var user = loginUser;
                if (user == null)
                {
                    result.code = (int)ResultCode.noright;
                    result.msg = "User not logged in";
                    return Json(result);
                }

                // Get news details
                var newsBLL = new NewsBLL();
                var news = newsBLL.GetModel(newsId);
                if (news == null)
                {
                    result.code = (int)ResultCode.failed;
                    result.msg = "News not found";
                    return Json(result);
                }

                // Track the click
                bool success = await _engagementTracker.TrackClickAsync(user.Id, user.RealName, newsId, source);

                if (success)
                {
                    // Record user feedback in recommendation engine
                    await _recommendationEngine.RecordUserFeedbackAsync(user.Id, newsId, "click", 1.0);

                    result.code = (int)ResultCode.success;
                    result.msg = "Click tracked successfully";
                }
                else
                {
                    result.code = (int)ResultCode.failed;
                    result.msg = "Failed to track click";
                }

                return Json(result);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in TrackClick: {ex.Message}", ex);
                result.code = (int)ResultCode.failed;
                result.msg = "Failed to track click: " + ex.Message;
                return Json(result);
            }
        }

        /// <summary>
        /// View details of a recommended news article
        /// </summary>
        /// <param name="id">News ID</param>
        /// <param name="source">Source of the click (web or email)</param>
        /// <returns>Redirect to news details page</returns>
        public async Task<ActionResult> ViewNews(int id, string source = "web")
        {
            try
            {
                // Get current user
                var user = loginUser;
                if (user == null)
                {
                    return RedirectToAction("Index", "Login");
                }

                // Track the click
                if (id > 0)
                {
                    var newsBLL = new NewsBLL();
                    var news = newsBLL.GetModel(id);
                    if (news != null)
                    {
                        await _engagementTracker.TrackClickAsync(user.Id, user.RealName, id, source);
                        await _recommendationEngine.RecordUserFeedbackAsync(user.Id, id, "click", 1.0);
                    }
                }

                // Redirect to the news details page with recommend flag
                return RedirectToAction("News", "Index", new { id = id, recommend = true });
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in ViewNews: {ex.Message}", ex);
                return RedirectToAction("Index");
            }
        }
        
        /// <summary>
        /// View user's recommendation history
        /// </summary>
        /// <param name="startDate">Optional start date filter</param>
        /// <param name="endDate">Optional end date filter</param>
        /// <returns>History view</returns>
        public ActionResult History(DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                // Get current user
                var user = loginUser;
                if (user == null)
                {
                    return RedirectToAction("Index", "Login");
                }

                // Set default date range if not provided
                if (!startDate.HasValue)
                {
                    startDate = DateTime.Now.AddDays(-30);
                }
                
                if (!endDate.HasValue)
                {
                    endDate = DateTime.Now;
                }

                // Store date range in ViewBag
                ViewBag.StartDate = startDate;
                ViewBag.EndDate = endDate;
                ViewBag.UserName = user.RealName;

                return View();
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in Recommendation History: {ex.Message}", ex);
                ViewBag.ErrorMessage = "An error occurred while loading recommendation history.";
                return View();
            }
        }
        
        /// <summary>
        /// API endpoint to get user engagement history
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="startDate">Optional start date filter</param>
        /// <param name="endDate">Optional end date filter</param>
        /// <returns>JSON result with engagement history</returns>
        [HttpGet]
        public async Task<JsonResult> GetUserEngagementHistory(int page = 1, int pageSize = 10, 
            DateTime? startDate = null, DateTime? endDate = null)
        {
            var result = new AjaxResult();

            try
            {
                // Get current user
                var user = loginUser;
                if (user == null)
                {
                    result.code = (int)ResultCode.noright;
                    result.msg = "User not logged in";
                    return Json(result, JsonRequestBehavior.AllowGet);
                }

                // Set default date range if not provided
                if (!startDate.HasValue)
                {
                    startDate = DateTime.Now.AddDays(-30);
                }
                
                if (!endDate.HasValue)
                {
                    endDate = DateTime.Now;
                }

                // Get engagement history
                var totalRecords = await _engagementTracker.GetUserEngagementCountAsync(user.RealName, startDate, endDate);
                var records = await _newsRecommendationsBLL.GetUserEngagementHistoryAsync(
                    user.RealName, pageSize, startDate, endDate);
                
                // Apply pagination
                var pagedRecords = records
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();
                
                // Calculate total pages
                int totalPages = (int)Math.Ceiling((double)totalRecords / pageSize);

                // Return success result
                result.code = (int)ResultCode.success;
                result.data = new
                {
                    records = pagedRecords,
                    totalRecords = totalRecords,
                    totalPages = totalPages,
                    currentPage = page
                };
                
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in GetUserEngagementHistory: {ex.Message}", ex);
                result.code = (int)ResultCode.failed;
                result.msg = "Failed to get engagement history: " + ex.Message;
                return Json(result, JsonRequestBehavior.AllowGet);
            }
        }
        
        /// <summary>
        /// Display similar news to a specific article
        /// </summary>
        /// <param name="id">News ID</param>
        /// <param name="limit">Number of similar news to display</param>
        /// <returns>Similar news view</returns>
        public ActionResult SimilarNews(int id, int limit = 5)
        {
            try
            {
                // Get current user
                var user = loginUser;
                if (user == null)
                {
                    return RedirectToAction("Index", "Login");
                }

                // Get news details
                var newsBLL = new NewsBLL();
                var news = newsBLL.GetModel(id);
                if (news == null)
                {
                    return RedirectToAction("Index");
                }

                // Get similar news
                var similarNews = _recommendationEngine.GetSimilarNews(id, limit);

                // Pass the original news and similar news to the view
                ViewBag.OriginalNews = news;
                
                return View(similarNews);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in SimilarNews: {ex.Message}", ex);
                ViewBag.ErrorMessage = "An error occurred while loading similar news.";
                return View(new List<News>());
            }
        }
    }
}