﻿
@{
    ViewBag.Name = "项目退出评分管理";
    Layout = "/Views/Shared/_Layout.cshtml";

    int id = Convert.ToInt32(ViewData["id"]);
    var manager = ViewData["manager"] as Banyan.Domain.Member;
    var isAdmin = manager.Levels == (byte)Banyan.Domain.MemberLevels.Administrator || manager.Levels == (byte)Banyan.Domain.MemberLevels.SuperUser;
}

<style>
    .label {
        padding: .3em .6em .3em;
    }
</style>
<div class="col-md-12" id="app">
    <div class="block" v-if="stageId <= 0">
        <div class="block-header">
            <div class="block-options">
                <a class="btn btn-primary" href="javascript:;" @@click="addScoreStage"><i class="fa fa-plus"></i>&nbsp;&nbsp;新建评分</a>
            </div>
            <h3 class="block-title" v-if="dataList && dataList.length > 0">"{{dataList[0].ProjectName}}" - 评分轮次管理</h3>
        </div>
        <div class="block-content">
            <table class="table table-striped" style="width: 100%;">
                <thead>
                    <tr>
                        <th class="text-center" style="width:80px;">轮次</th>
                        <th class="text-center" style="width:150px;">轮次名称</th>
                        <th class="text-center" style="width:150px;">添加时间</th>
                        <th class="text-center" style="width:150px;">结束时间</th>
                        <th class="text-center" style="width:150px;">合伙人平均分</th>
                        <th class="text-center" style="width:150px;">平均分</th>
                        <th class="text-center" style="width:150px;">人数</th>
                        <th style="width:150px;">状态</th>
                        <th class="text-center" style="width: 100px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <template v-if="dataList && dataList.length > 0">
                        <tr v-for="(item, index) in dataList">
                            <td class="text-center">{{index + 1}}</td>
                            <td>{{item.ActName}}</td>
                            <td>{{item.AddTime}}</td>
                            <td>{{item.EndTime}}</td>
                            <td class="text-center">{{item.People > 0 ? item.PartnerScoreAvg : '-'}}</td>
                            <td class="text-center">{{item.People > 0 ? item.Average : '-'}}</td>
                            <td class="text-center">{{item.People}}</td>
                            <td class="hidden-xs">
                                <template v-if="item.State == 0">
                                    <span class="label label-danger">已结束</span>
                                </template>
                                <template v-else-if="item.State == 1">
                                    <span class="label label-success">进行中</span>
                                </template>
                            </td>
                            @if (isAdmin)
                            {
                            <td class="text-center">
                                <div class="btn-group">
                                    <a class="btn btn-xs btn-default" data-toggle="tooltip" @@click="getStageScoreList(item.Id)" data-original-title="明细"><i class="fa fa-file-text-o"></i>&nbsp;&nbsp;明细</a>
                                    <button class="btn btn-xs btn-danger" @@click="closeScoreStage(item.Id)" type="button" data-toggle="tooltip" data-original-title="结束" v-if="item.State > 0"><i class="fa fa-times"></i>&nbsp;&nbsp;关闭</button>
                                </div>
                            </td>
                            }
                        </tr>
                    </template>
                    <template v-else>
                        <tr>
                            <td class="text-center" colspan="20">还未创建评分活动.</td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>

    <div class="block" v-else>
        <div class="block-header">
            <div class="block-options">
                <a class="btn btn-primary" href="javascript:;" @@click="back"><i class="fa fa-angle-left"></i>&nbsp;&nbsp;返回</a>
            </div>
            <h3 class="block-title">评分明细</h3>
        </div>
        <div class="block-content">
            <table class="table table-striped" style="width: 100%;">
                <thead>
                    <tr>
                        <th class="text-center" style="width: 80px;">#</th>
                        <th>用户名</th>
                        <th class="text-center" style="width:150px;">评分时间</th>
                        <th class="text-center" style="width:150px;">分值</th>
                    </tr>
                </thead>
                <tbody>
                    <template v-if="scoreList && scoreList.length > 0">
                        <tr v-for="(item, index) in scoreList">
                            <td class="text-center">{{index + 1}}</td>
                            <td>{{item.RealName}}</td>
                            <td>{{item.AddTime}}</td>
                            <td class="text-center">{{item.Score}}</td>
                        </tr>
                    </template>
                    <template v-else>
                        <tr>
                            <td class="text-center" colspan="20">还没有评分记录.</td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>
</div>

@section scripts{
    <script type="text/javascript">
        var app = new Vue({
            el: '#app',
            data: {
                id: @(id),
                loadState: 0,
                dataList: [],
                erroInfo: '',
                stageId: 0,
                scoreList: [],
            },
            methods: {
                initList: function () {
                    var that = this;
                    $.post('/adminapi/exitscorestages', { id: that.id }, function (res) {
                        if (res.code == 0) {
                            that.dataList = res.data || [];
                        } else {
                            that.erroInfo = res.msg;
                        }
                    }).error(function (xhr, errorText, errorType) {
                        console.log(errorText);
                    });
                },
                addScoreStage: function () {
                    var that = this;
                    parent.openmodal('新建评分', '@(Url.Action("exitscoreset", "index"))?id=' + that.id, '400px', '270px')
                },
                closeScoreStage: function (id) {
                    var that = this;
                    layer.confirm('确认关闭该活动吗？', function (index) {
                        layer.close(index);
                        $.post('/adminapi/exitscoreswitch', { id: id, state: 0 }, function (res) {
                            if (res.code == 0) {
                                layer.msg('操作成功');
                                that.initList();
                            } else {
                                that.erroInfo = res.msg;
                            }
                        }).error(function (xhr, errorText, errorType) {
                            console.log(errorText);
                        });
                    });
                },
                back: function () {
                    var that = this;
                    that.stageId = 0;
                    that.scoreList = [];
                },
                getStageScoreList: function (stageId) {
                    var that = this;
                    that.stageId = stageId;

                    $.post('/adminapi/exitstagescores', { id: stageId }, function (res) {
                        if (res.code == 0) {
                            that.scoreList = res.data || [];
                        } else {
                            that.erroInfo = res.msg;
                        }
                    }).error(function (xhr, errorText, errorType) {
                        console.log(errorText);
                    });
                },
            },
            created: function () {
                $("#app").show();

                this.initList();
            }
        });
    </script>
}
