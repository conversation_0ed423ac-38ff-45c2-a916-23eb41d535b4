using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public partial class News
    {
        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        [SqlField]
        public string Title { get; set; }
        [SqlField]
        public string Subject { get; set; }
        [SqlField]
        public string Content { get; set; }
        [SqlField]
        public string Html { get; set; }
        [SqlField]
        public bool Translate { get; set; }
        [SqlField]
        public DateTime PubTime { get; set; } = DateTime.Now;

        [SqlField]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        [SqlField]
        public string Source { get; set; }
        [SqlField]
        public string Tag { get; set; } = string.Empty;
        [SqlField]
        public string Classify { get; set; }
        [SqlField]
        public string Url { get; set; }

        /// <summary>
        /// 新闻向量（1024维）
        /// 存储格式：逗号分隔的浮点数字符串
        /// 示例：0.123456,0.234567,0.345678,...
        /// </summary>
        [SqlField]
        public string NewsVector { get; set; }

        /// <summary>
        /// 向量更新时间
        /// 用于判断向量是否需要重新生成
        /// </summary>
        [SqlField]
        public DateTime VectorUpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 标签分析结果（JSON格式）
        /// 包含主要标签、次要标签、标签权重等信息
        /// </summary>
        [SqlField]
        public string TagAnalysis { get; set; }

        /// <summary>
        /// 向量化状态
        /// 0：未向量化，1：已向量化，2：向量化失败
        /// </summary>
        [SqlField]
        public int VectorStatus { get; set; } = 0;

        /// <summary>
        /// 向量化错误信息
        /// 当向量化失败时记录错误原因
        /// </summary>
        [SqlField]
        public string VectorError { get; set; }
        public int? ViewCount { get; set; }
        
        /// <summary>
        /// 向量相似度匹配分数
        /// 用于存储搜索结果的相似度分数
        /// 非数据库字段，仅用于运行时
        /// </summary>
        public double MatchScore { get; set; }
    }

}
