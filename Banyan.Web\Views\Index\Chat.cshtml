﻿@using Banyan.Domain
@{
    Layout = "/Views/Shared/_Layout.cshtml";
    var pingLLM = (bool)ViewData["pingLLM"];
    var embedded = (bool)ViewData["embedded"];
    if(embedded)
    {
        Layout = "/Views/Shared/_LayoutClean.cshtml";
    }
}
<style>
    .block-content p {
        margin-bottom: 0;
    }
    
    /* 确保visible-content内的内容没有额外空白 */
    .visible-content > *:last-child {
        margin-bottom: 0 !important;
    }
    
    .visible-content::after {
        content: "";
        display: block;
        clear: both;
    }
    /* 聊天页面整体布局 */
    .chat-page-container {
        display: flex !important;
        flex-direction: column;
        height: calc(100vh - 120px); /* 减去页面头部和其他元素的高度 */
        max-height: none; /* 移除最大高度限制，让对话框能充分利用屏幕空间 */
        min-height: 600px; /* 设置最小高度 */
    }

    .chat-messages-area {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    /* 添加聊天容器样式 */
    .chat-container {
        display: flex;
        flex-direction: column;
        gap: 16px;
        flex: 1;
        overflow-y: auto;
        padding: 15px;
        background-color: #f9f9f9;
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(0,0,0,0.05);
    }

    .empty-chat-placeholder {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f9f9f9;
        border-radius: 10px;
/*        margin: 15px;*/
    }
 
    /* 消息气泡样式 */
    .chat-message {
        max-width: 85%;
        display: flex;
        align-items: flex-start;
        gap: 12px;
    }

    .user-message {
        white-space: pre-wrap;
        align-self: flex-end;
        background-color: #e3f2fd;
        border-radius: 18px 18px 4px 18px;
        padding: 12px 16px;
        margin-left: auto;
    }

    .ai-message {
        align-self: flex-start;
        background-color: #ffffff;
        border-radius: 18px 18px 18px 4px;
        padding: 12px 16px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    /* 消息元信息 */
    .message-meta {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
        font-size: 12px;
        color: #666;
    }

    .message-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: white;
        flex-shrink: 0;
    }

    .user-avatar {
        background-color: #42a5f5;
    }

    .ai-avatar {
        background-color: #66bb6a;
    }

    .message-content {
        flex-grow: 1;
    }

    .message-time {
        margin-left: 8px;
        font-size: 11px;
        opacity: 0.7;
    }

    /* 输入区域样式 */
    .input-container-fixed {
        display: flex;
        gap: 12px;
        padding: 15px;
        background-color: #ffffff;
        border-top: 1px solid #e0e0e0;
        border-radius: 0 0 10px 10px;
        align-items: flex-end;
        flex-shrink: 0; /* 防止被压缩 */
    }

    .message-input {
        flex-grow: 1;
        border-radius: 12px;
        padding: 8px 12px;
        border: 1px solid #ddd;
        resize: none;
        min-height: 50px;
        max-height: 120px; /* 限制最大高度，避免占用太多空间 */
        transition: border-color 0.3s,height 0.2s ease;
    }

        .message-input:focus {
            border-color: #42a5f5;
            outline: none;
            box-shadow: 0 0 0 2px rgba(66, 165, 245, 0.2);
        }



    /* AI参考区域优化 */
    #ai_block {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f0f7ff;
        border-radius: 10px;
        border-left: 4px solid #42a5f5;
    }

    /* 思考内容折叠样式 */
    .think-container {
        margin: 10px 0;
        background: #f8f9fa;
        border-left: 3px solid #4CAF50;
        border-radius: 0 4px 4px 0;
    }

    .think-summary {
        cursor: pointer;
        padding: 8px 12px;
        color: #4CAF50;
        font-weight: 500;
        display: flex;
        align-items: center;
    }

        .think-summary::before {
            content: "▶";
            display: inline-block;
            margin-right: 8px;
            font-size: 10px;
            transition: transform 0.2s;
        }

    .think-details[open] .think-summary::before {
        transform: rotate(90deg);
    }

    .think-content {
        padding: 8px 12px 12px 28px;
        white-space: pre-wrap;
        color: #333;
/*        border-top: 1px solid #e0e0e0;*/
        background: #fff;
    }

    .visible-content {
        white-space: pre-wrap;
        margin: 0;
        padding: 0;
/*        line-height: 1.6;*/
    }
 
    .loading-spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid rgba(200,200,200,0.1); 
        border-radius: 50%;
        border-top-color:  #bbb;
        -webkit-animation: spin 1s ease-in-out infinite;
        animation: spin 1s ease-in-out infinite;
    }

    @@-webkit-keyframes spin {
        to { -webkit-transform: rotate(360deg); }
    }

    @@keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* 防止Vue初始化时的闪烁 */
    [v-cloak] {
        display: none !important;
    }

    /* 思考中动画效果 */
    .thinking-animation {
        color: #999 !important;
        font-style: italic;
        animation: thinking-pulse 2s ease-in-out infinite;
        display: inline-block;
    }

    .thinking-dots::after {
        content: '';
        animation: thinking-dots 1.5s steps(4, end) infinite;
        display: inline-block;
        width: 0;
    }

    @@keyframes thinking-pulse {
        0%, 100% {
            opacity: 0.6;
            transform: scale(1);
        }
        50% {
            opacity: 1;
            transform: scale(1.02);
        }
    }

    @@keyframes thinking-dots {
        0% { content: ''; }
        25% { content: '.'; }
        50% { content: '..'; }
        75% { content: '...'; }
        100% { content: ''; }
    }

    /* 智能检索动画 */
    .mcp-thinking {
        color: #42a5f5 !important;
        font-style: italic;
        animation: mcp-glow 2.5s ease-in-out infinite;
        display: inline-block;
    }

    @@keyframes mcp-glow {
        0%, 100% {
            opacity: 0.7;
            text-shadow: 0 0 5px rgba(66, 165, 245, 0.3);
        }
        50% {
            opacity: 1;
            text-shadow: 0 0 10px rgba(66, 165, 245, 0.6);
        }
    }

    /* 联网搜索动画 */
    .web-search-thinking {
        color: #4CAF50 !important;
        font-style: italic;
        animation: web-search-pulse 2s linear infinite;
        display: inline-block;
    }

    @@keyframes web-search-pulse {
        0%, 100% {
            opacity: 0.6;
            transform: translateX(0);
        }
        25% {
            opacity: 0.8;
            transform: translateX(2px);
        }
        50% {
            opacity: 1;
            transform: translateX(0);
        }
        75% {
            opacity: 0.8;
            transform: translateX(-2px);
        }
    }

    /* 状态标签通用样式 */
    .status-tag {
        display: flex;
        align-items: center;
        margin-right: 12px;
        margin-bottom: 4px;
        padding: 4px 8px;
        border-radius: 4px;
        border-left: 3px solid;
        font-size: 13px;
    }

    .status-tag.large {
        margin-bottom: 8px;
        font-size: 12px;
    }

    .status-tag i {
        font-size: 12px;
        margin-right: 6px;
    }

    .status-tag .status-text {
        font-weight: 500;
    }

    .status-tag .status-meta {
        font-size: 11px;
        color: #666;
        margin-left: 6px;
    }

    /* 不同类型的状态标签 */
    .status-tag.project {
        background: #e8f4fd;
        border-left-color: #42a5f5;
        margin-bottom: 0; /* 移除底边距 */
        margin-right: 0; /* 移除右边距，使用gap来控制间隔 */
        display: inline-flex; /* 改为inline-flex让它们能在同一行 */
    }

    .status-tag.project .status-text {
        color: #1976d2;
    }

    .status-tag.project i {
        color: #42a5f5;
    }

    .status-tag.file {
        background: #e8f4fd;
        border-left-color: #42a5f5;
        margin-bottom: 0; /* 移除底边距 */
        margin-right: 0; /* 移除右边距，使用gap来控制间隔 */
        display: inline-flex; /* 改为inline-flex让它们能在同一行 */
    }

    .status-tag.file .status-text {
        color: #1976d2;
    }

    .status-tag.file i {
        color: #42a5f5;
    }

    .status-tag.searching {
        background: #fff3e0;
        border-left-color: #FF9800;
    }

    .status-tag.searching .status-text {
        color: #F57C00;
    }

    .status-tag.search-success {
        background: #e8f5e8;
        border-left-color: #4CAF50;
    }

    .status-tag.search-success .status-text {
        color: #2E7D32;
    }

    .status-tag.search-success i {
        color: #4CAF50;
    }

    /* 搜索结果容器，让搜索结果紧密排列 */
    .search-results-container {
        display: flex;
        flex-wrap: wrap;
        gap: 2px; /* 搜索结果之间的小间隔 */
        margin-bottom: 8px;
    }

    /* 项目和文件容器，让它们也紧密排列 */
    .projects-container, .files-container {
        display: flex;
        flex-wrap: wrap;
        gap: 2px; /* 项目/文件之间的小间隔 */
        margin-bottom: 8px;
    }

    .status-tag.search-result {
        background: #e8f4fd;
        border-left-color: #42a5f5;
        margin-bottom: 0; /* 移除底边距 */
        margin-right: 0; /* 移除右边距，使用gap来控制间隔 */
        display: inline-flex; /* 改为inline-flex让它们能在同一行 */
    }

    .status-tag.search-result .status-text {
        color: #1976d2;
    }

    .status-tag.search-result i {
        color: #42a5f5;
    }

    .status-tag.search-result a {
        font-weight: 500;
        color: #1976d2;
        text-decoration: none;
    }

    .status-tag.warning {
        background: #fff3e0;
        border-left-color: #FF9800;
    }

    .status-tag.warning .status-text {
        color: #F57C00;
    }

    .status-tag.warning i {
        color: #F57C00;
    }

    .status-tag.error {
        background: #ffebee;
        border-left-color: #f44336;
    }

    .status-tag.error .status-text {
        color: #f44336;
    }

    .status-tag.error i {
        color: #f44336;
    }

    .status-tag.tool-status {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border: 1px solid #90caf9;
        color: #0d47a1;
    }

    .status-tag.tool-status .status-text {
        color: #0d47a1;
        font-weight: 600;
    }

    .status-tag.tool-status i {
        color: #1976d2;
    }

    .status-tag.tool-status.warning {
        background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
        border: 1px solid #ffb74d;
        color: #e65100;
    }

    .status-tag.tool-status.warning .status-text {
        color: #e65100;
    }

    .status-tag.tool-status.warning i {
        color: #ff9800;
    }

    /* 底部控制区域样式优化 */
    .controls-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 12px;
        gap: 12px;
    }

    .left-controls {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
    }

    .right-controls {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    /* 模型选择器样式 */
    .model-selector {
        min-width: 200px;
        height: 40px;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        background-color: #ffffff;
        padding: 0 12px;
        font-size: 14px;
        color: #333;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .model-selector:focus {
        outline: none;
        border-color: #42a5f5;
        box-shadow: 0 0 0 2px rgba(66, 165, 245, 0.2);
    }

    .model-selector:hover {
        border-color: #42a5f5;
    }

    /* 文件上传按钮样式 */
    .upload-btn {
        height: 40px;
        padding: 0 16px;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #495057;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .upload-btn i {
        font-size: 14px;
        line-height: 1;
        vertical-align: middle;
    }

    .upload-btn:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        border-color: #adb5bd;
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }

    .upload-btn:active {
        transform: translateY(0);
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* 发送按钮样式 */
    .send-btn {
        height: 40px;
        width: 80px;
        padding: 0;
        border-radius: 8px;
        border: none;
        background: linear-gradient(135deg, #42a5f5 0%, #1976d2 100%);
        color: white;
        font-weight: 600;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        box-shadow: 0 2px 6px rgba(66, 165, 245, 0.3);
    }

    .send-btn:hover:not(:disabled) {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(66, 165, 245, 0.4);
    }

    .send-btn:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 6px rgba(66, 165, 245, 0.3);
    }

    .send-btn:disabled {
        background: linear-gradient(135deg, #bdbdbd 0%, #9e9e9e 100%);
        cursor: not-allowed;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* 终止按钮样式 */
    .stop-btn {
        height: 40px;
        width: 80px;
        padding: 0;
        border-radius: 8px;
        border: none;
        background: linear-gradient(135deg, #ff5722 0%, #d32f2f 100%);
        color: white;
        font-weight: 600;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        box-shadow: 0 2px 6px rgba(255, 87, 34, 0.3);
    }

    .stop-btn i {
        font-size: 12px;
        line-height: 1;
        vertical-align: middle;
    }

    .stop-btn:hover {
        background: linear-gradient(135deg, #d32f2f 0%, #c62828 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(255, 87, 34, 0.4);
    }

    .stop-btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 6px rgba(255, 87, 34, 0.3);
    }

    /* 清除按钮样式 */
    .clear-btn {
        height: 40px;
        width: 80px;
        padding: 0;
        border-radius: 8px;
        border: none;
        background: linear-gradient(135deg, #757575 0%, #616161 100%);
        color: white;
        font-weight: 600;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        box-shadow: 0 2px 6px rgba(117, 117, 117, 0.3);
    }

    .clear-btn i {
        font-size: 12px;
        line-height: 1;
        vertical-align: middle;
    }

    .clear-btn:hover:not(:disabled) {
        background: linear-gradient(135deg, #616161 0%, #424242 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(117, 117, 117, 0.4);
    }

    .clear-btn:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 6px rgba(117, 117, 117, 0.3);
    }

    .clear-btn:disabled {
        background: linear-gradient(135deg, #bdbdbd 0%, #9e9e9e 100%);
        cursor: not-allowed;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* 发送图标动画 */
    .send-icon {
        font-size: 16px;
        transition: transform 0.2s ease;
    }

    .send-btn:hover .send-icon {
        transform: translateX(2px);
    }

    /* 文件列表样式优化 */
    .file-list {
        margin-bottom: 12px;
    }

    .file-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        padding: 12px;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border: 1px solid #e9ecef;
        border-radius: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    .file-item:hover {
        border-color: #42a5f5;
        box-shadow: 0 2px 6px rgba(66, 165, 245, 0.1);
    }

    .file-icon {
        font-size: 20px;
        margin-right: 12px;
        color: #42a5f5;
    }

    .file-info {
        flex: 1;
    }

    .file-name {
        font-weight: 500;
        color: #333;
        margin-bottom: 2px;
    }

    .file-size {
        font-size: 12px;
        color: #666;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .file-status {
        font-size: 12px;
        font-weight: 500;
    }

    .file-status.processing {
        color: #FF9800;
    }

    .file-status.completed {
        color: #4CAF50;
    }

    .file-status.error {
        color: #f44336;
    }

    /* 项目选择器样式 */
    .project-selector-container {
        margin-bottom: 15px;
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        height: 400px; /* 固定高度，避免跳动 */
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .project-selector-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #e0e0e0;
    }

    .project-selector-header h4 {
        margin: 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        color: #666;
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .close-btn:hover {
        background-color: #f0f0f0;
        color: #333;
    }

    .project-search {
        padding: 15px 20px;
        border-bottom: 1px solid #e0e0e0;
    }

    .project-search-input {
        width: 100%;
        padding: 10px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.3s ease;
    }

    .project-search-input:focus {
        outline: none;
        border-color: #42a5f5;
        box-shadow: 0 0 0 2px rgba(66, 165, 245, 0.2);
    }

    .search-status {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 8px;
        padding: 6px 12px;
        background-color: #f8f9fa;
        border-radius: 6px;
        font-size: 12px;
        color: #666;
        border-left: 3px solid #42a5f5;
    }

    .search-status .loading-spinner {
        width: 12px;
        height: 12px;
        border-width: 1px;
    }

    .status-text {
        font-weight: 500;
    }

    .project-list-container {
        flex: 1;
        overflow-y: auto;
        min-height: 0; /* 确保flex容器可以正确收缩 */
    }

    .project-item {
        padding: 15px 20px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .project-item:hover {
        background-color: #f8f9ff;
        border-left: 4px solid #42a5f5;
    }

    .project-item:last-child {
        border-bottom: none;
    }

    .project-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
        font-size: 14px;
    }

    .project-meta {
        display: flex;
        gap: 15px;
        font-size: 12px;
        color: #666;
        flex-wrap: wrap;
    }

    .project-summary {
        font-size: 12px;
        color: #888;
        margin-top: 5px;
        line-height: 1.4;
    }

    .no-projects, .loading-projects {
        padding: 30px 20px;
        text-align: center;
        color: #999;
        font-style: italic;
    }

    .loading-projects {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    /* 已选项目列表样式 */
    .selected-projects-list {
        margin-bottom: 12px;
    }

    .selected-project-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        padding: 12px;
        background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(66, 165, 245, 0.1);
    }

    .selected-project-item:hover {
        border-color: #42a5f5;
        box-shadow: 0 2px 6px rgba(66, 165, 245, 0.2);
    }

    .project-icon {
        font-size: 20px;
        margin-right: 12px;
        color: #42a5f5;
    }

    .selected-project-info {
        flex: 1;
    }

    .selected-project-name {
        font-weight: 600;
        color: #1976d2;
        margin-bottom: 2px;
        font-size: 14px;
    }

    .selected-project-status {
        font-size: 12px;
        color: #4CAF50;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .remove-project-btn {
        background: #ff5722;
        color: white;
        border: none;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        cursor: pointer;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .remove-project-btn:hover {
        background: #d32f2f;
        transform: scale(1.1);
    }

    .remove-file-btn {
        background: #ff5722;
        color: white;
        border: none;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        cursor: pointer;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        flex-shrink: 0;
    }

    .remove-file-btn:hover {
        background: #d32f2f;
        transform: scale(1.1);
    }

    /* 联网搜索开关样式 */
    .web-search-enabled {
        background: linear-gradient(135deg, #4CAF50 0%, #66bb6a 100%) !important;
        color: white !important;
        border-color: #4CAF50 !important;
        box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3) !important;
    }

    .web-search-enabled:hover {
        background: linear-gradient(135deg, #66bb6a 0%, #4CAF50 100%) !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4) !important;
    }

    /* MCP工具状态样式 */
    .mcp-status {
        background: #e8f4fd;
        border-left: 4px solid #42a5f5;
        border-radius: 4px;
        padding: 8px 12px;
        margin: 8px 0;
        color: #1976d2;
        font-style: italic;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .mcp-status i {
        color: #42a5f5;
        font-size: 14px;
    }

    /* MCP选择器容器样式 */
    .mcp-selector-container {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .mcp-selector-container select {
        height: 40px;
        border-radius: 6px;
        border: 1px solid #e0e0e0;
        padding: 0 8px;
        font-size: 12px;
        background: white;
        min-width: 100px;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .mcp-selector-container select:focus {
        outline: none;
        border-color: #42a5f5;
        box-shadow: 0 0 0 2px rgba(66, 165, 245, 0.2);
    }

    .mcp-selector-container select:hover {
        border-color: #42a5f5;
    }

    /* 文件列表项样式 */
    .file-display-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        padding: 12px;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border: 1px solid #e9ecef;
        border-radius: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    .file-display-item:hover {
        border-color: #42a5f5;
        box-shadow: 0 2px 6px rgba(66, 165, 245, 0.1);
    }

    .file-display-item .file-icon {
        font-size: 20px;
        margin-right: 12px;
        color: #42a5f5;
    }

    .file-display-item .file-content {
        flex: 1;
    }

    .file-display-item .file-title {
        font-weight: 500;
        color: #333;
        margin-bottom: 2px;
    }

    .file-display-item .file-info {
        font-size: 12px;
        color: #666;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    /* 推荐问题样式 */
    .suggested-questions {
        max-width: 650px;
        margin: 0 auto;
    }

    .suggestions-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
    }

    .suggestions-header h4 {
        margin: 0;
        color: #666;
        font-size: 13px;
    }

    .suggestions-header .refresh-suggestions-btn {
        margin: 0;
    }

    .questions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 8px;
        margin-bottom: 16px;
    }

    .question-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 10px 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: left;
        display: flex;
        align-items: flex-start;
        gap: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        color: #333;
        font-size: 14px;
        line-height: 1.3;
    }

    .question-card:hover {
        border-color: #42a5f5;
        box-shadow: 0 3px 8px rgba(66, 165, 245, 0.15);
        transform: translateY(-1px);
        background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
    }

    .question-card i {
        color: #42a5f5;
        font-size: 14px;
        margin-top: 1px;
        flex-shrink: 0;
    }

    .question-card span {
        flex: 1;
        font-weight: 500;
    }

    .question-card-disabled {
        cursor: not-allowed !important;
        opacity: 0.6;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        border-color: #dee2e6 !important;
        color: #6c757d !important;
    }

    .question-card-disabled:hover {
        border-color: #dee2e6 !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05) !important;
        transform: none !important;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    }

    .question-card-disabled i {
        color: #6c757d !important;
    }

    .refresh-suggestions-btn {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid #e0e7ff;
        border-radius: 8px;
        padding: 8px 14px;
        color: #6366f1;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: inline-flex;
        align-items: center;
        gap: 6px;
        box-shadow: 0 1px 3px rgba(99, 102, 241, 0.1);
        position: relative;
        overflow: hidden;
    }

    .refresh-suggestions-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: left 0.5s;
    }

    .refresh-suggestions-btn:hover:not(:disabled) {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        border-color: #6366f1;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    }

    .refresh-suggestions-btn:hover:not(:disabled)::before {
        left: 100%;
    }

    .refresh-suggestions-btn:active:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(99, 102, 241, 0.25);
    }

    .refresh-suggestions-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background: #f1f5f9;
        border-color: #e2e8f0;
        color: #94a3b8;
        box-shadow: none;
    }

    .refresh-suggestions-btn i {
        font-size: 12px;
        transition: transform 0.3s ease;
    }

    .refresh-suggestions-btn:hover:not(:disabled) i {
        transform: rotate(180deg);
    }

    .loading-suggestions {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #666;
        font-size: 14px;
    }

    /* 服务不可用提示样式 */
    .service-unavailable {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 400px;
        text-align: center;
        color: #666;
    }

    .service-unavailable i {
        font-size: 64px;
        color: #f44336;
        margin-bottom: 20px;
    }

    .service-unavailable h3 {
        color: #333;
        margin-bottom: 10px;
    }

    .service-unavailable p {
        color: #666;
        font-size: 16px;
    }
</style>

<div class="col-md-12">
    <div class="block">
        <div class="block-content block-content-full chat-page-container" style="display:none;" id="page-app" v-cloak>
            @if (!pingLLM)
            {
                <div class="service-unavailable">
                    <i class="fa fa-exclamation-triangle"></i>
                    <h3>AI智能助手暂时无法服务</h3>
                    <p>服务当前不可用，请稍后重试或联系系统管理员</p>
                </div>
            }
            else
            {
            <div class="chat-messages-area">
                <div class="chat-container" v-if="conversation.length > 0" ref="chatContainer">
                    <div v-for="(msg, index) in conversation" :key="index" class="chat-message" v-cloak>
                        <div :class="['message-avatar', msg.role === 'user' ? 'user-avatar' : 'ai-avatar']">
                            {{ msg.role === 'user' ? '您' : 'AI' }}
                        </div>
                        <div class="message-content">
                            <div class="message-meta">
                                <strong>{{ msg.role === 'user' ? '您' : 'AI助手' }}</strong>
                                <span class="message-time">{{ formatTime(msg.timestamp) }}</span>
                            </div>
                            <div :class="msg.role === 'user' ? 'user-message' : 'ai-message'">
                                <div v-if="msg.role === 'user'" class="visible-content" v-html="msg.visibleContent"></div>
                                <div v-else>
                                    <!-- 思考内容折叠显示 -->
                                    <div class="think-container" v-if="msg.thinkContent">
                                        <details class="think-details" :open="msg.thinkExpanded">
                                            <summary class="think-summary">AI思考过程</summary>
                                            <div class="think-content" v-html="msg.thinkContent"></div>
                                        </details>
                                    </div>
                                    <div class="visible-content" v-html="msg.visibleContent"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 在聊天记录后显示推荐问题（仅在非生成状态且有推荐问题时显示） -->
                    <div v-if="!isGenerating && suggestedQuestions.length > 0 && !loadingSuggestions" class="suggested-questions" style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e9ecef;">
                        <div class="suggestions-header">
                            <h4>@(embedded ? "💼 相关基金分析" : "💡 相关推荐问题")</h4>
                            <button class="refresh-suggestions-btn" @@click="refreshSuggestions" 
                                    :disabled="loadingSuggestions">
                                <i class="fa fa-refresh"></i>
                                换一批
                            </button>
                        </div>
                        <div class="questions-grid">
                            <div v-for="(question, index) in suggestedQuestions" :key="index" 
                                 :class="['question-card', {'question-card-disabled': isEmbedded && conversation.length === 0}]" 
                                 @@click="isEmbedded && conversation.length === 0 ? null : sendSuggestedQuestion(question)">
                                <i class="fa fa-lightbulb-o"></i>
                                <span>{{ question }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else class="empty-chat-placeholder">
                    <div style="text-align: center; color: #999; padding: 50px;">
                        <i class="fa @(embedded ? "fa-pie-chart" : "fa-comments-o")" style="font-size: 48px; margin-bottom: 24px;"></i>
                        <p style="margin-bottom: 32px;">@(embedded ? "基金管理智能助手已就绪，开始基金分析与文件审查吧" : "开始您的对话吧")</p>
                        
                        <!-- 推荐问题区域 -->
                        <div class="suggested-questions" v-if="!loadingSuggestions">
                            <div class="suggestions-header" style="margin-bottom: 16px;">
                                <h4 style="font-size: 14px;">@(embedded ? "💡 推荐问题（参考模版）" : "💡 推荐问题")</h4>
                                <button class="refresh-suggestions-btn" @@click="refreshSuggestions" 
                                        :disabled="loadingSuggestions">
                                    <i class="fa fa-refresh"></i>
                                    换一批
                                </button>
                            </div>
                            <div class="questions-grid">
                                <div v-for="(question, index) in suggestedQuestions" :key="index" 
                                     :class="['question-card', {'question-card-disabled': isEmbedded && conversation.length === 0}]" 
                                     @@click="isEmbedded && conversation.length === 0 ? null : sendSuggestedQuestion(question)">
                                    <i class="fa fa-lightbulb-o"></i>
                                    <span>{{ question }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 加载推荐问题状态 -->
                        <div v-if="loadingSuggestions" class="loading-suggestions">
                            <span class="loading-spinner"></span>
                            <span style="margin-left: 8px;">正在生成推荐问题...</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="input-container-fixed">
                <div style="display: flex; flex-direction: column; width: 100%;">
                    <!-- 项目选择器 -->
                    <div class="project-selector-container" v-if="!isEmbedded && showProjectSelector" ref="projectSelector">
                        <div class="project-selector-header">
                            <h4>选择项目添加到对话</h4>
                            <button class="close-btn" @@click="showProjectSelector = false">×</button>
                        </div>
                        <div class="project-search">
                            <input type="text" v-model="projectSearch" placeholder="搜索项目名称、内容、创始人..." class="project-search-input" @@input="searchProjects">
                            <div v-if="searchStatus" class="search-status">
                                <span class="loading-spinner" v-if="searchStatus === 'searching'"></span>
                                <span v-if="searchStatus === 'waiting'">⏱️</span>
                                <span class="status-text">{{ getSearchStatusText() }}</span>
                            </div>
                        </div>
                        <div class="project-list-container">
                            <div class="project-item" v-for="project in searchResults" :key="project.Id" @@click="selectProject(project)">
                                <div class="project-name">{{ project.Name }}</div>
                                <div class="project-meta">
                                    <span class="project-date">{{ project.PubTimeStr }}</span>
                                    <span class="project-editor">{{ project.EditorName }}</span>
                                    <span v-if="project.ProjectManager" class="project-manager">{{ project.ProjectManager }}</span>
                                </div>
                                <div v-if="project.Summary" class="project-summary">{{ project.Summary.substring(0, 100) }}...</div>
                            </div>
                            <div v-if="searchResults.length === 0 && !loadingProjects && projectSearch.trim()" class="no-projects">
                                没有找到匹配的项目
                            </div>
                            <div v-if="searchResults.length === 0 && !loadingProjects && !projectSearch.trim()" class="no-projects">
                                请输入关键词搜索项目，或显示最近项目
                            </div>
                            <div v-if="loadingProjects" class="loading-projects">
                                <span class="loading-spinner"></span> 搜索中...
                            </div>
                        </div>
                    </div>



                    <!-- 已选项目展示区域 -->
                    <div class="selected-projects-list" v-if="selectedProjects.length > 0" v-html="selectedProjectsHtml"></div>
                    
                    <!-- 新增文件展示区域 -->
                    <div class="file-list" v-if="files.length > 0" v-html="fileListHtml"></div>
                    
                    <textarea id="input-textarea" class="form-control message-input" v-model="currentMessage"
                              placeholder="@(embedded ? "基金管理智能助手！💼 组合分析 | 📊 业绩监控 | 🎯 退出策略 | ⚖️ 文件审查 | 🌐 市场洞察（Shift+Enter换行）" : "有问题，尽管问！📚 项目分析 | ✍️ 文件审阅 | ⚖️ 合同初审 | 🌐 联网搜索 | 更多功能开发中…（Shift+Enter换行）（beta版）")" rows="2" ref="messageInput" @@input="adjustInputHeight"></textarea>
                    <div class="controls-row">
                        <div class="left-controls">
                            <select v-model="model.selected" class="model-selector" title="选择AI模型">
                                <option value="qwen3-30b-a3b-mlx">🚀 Qwen3-30b (极速，推荐)</option>
                                <option value="qwenlong-l1-32b-mlx">🧠 QwenLong-32B (均衡)</option>
                                <option value="deepseek-r1-0528">💎 DeepSeek R1 (质量)</option>
                                <option value="qwen2.5-14b-instruct-1m">🔥 Qwen2.5-14B (超长文本)</option>
                            </select>
                            <input type="file" id="file-upload" ref="fileUpload" style="display: none;" @@change="handleFileUpload">
                            <button class="upload-btn" @@click="$refs.fileUpload.click()">
                                <i class="fa fa-upload"></i>
                                上传文件
                            </button>
                            <button class="upload-btn" v-if="!isEmbedded" @@click="showProjectSelector = !showProjectSelector">
                                <i class="fa fa-folder-open"></i>
                                选择项目
                            </button>
                            <div style="display: flex; align-items: center; gap: 4px;">
                                <button class="upload-btn" 
                                       @@click="webSearchEnabled = !webSearchEnabled"
                                       :class="{'web-search-enabled': webSearchEnabled}">
                                    <i class="fa fa-globe"></i>
                                    {{ webSearchEnabled ? '联网已开启' : '联网搜索' }}
                                </button>
                                <select v-if="webSearchEnabled" v-model="webSearchLimit" 
                                        style="height: 40px; border-radius: 6px; border: 1px solid #e0e0e0; padding: 0 8px; font-size: 12px; background: white;"
                                        title="选择联网搜索结果数量，系统会搜索更多原始结果并智能过滤">
                                    <option value="8">8条 (推荐)</option>
                                    <option value="15">15条 (全面)</option>
                                    <option value="25">25条 (深度)</option>
                                    <option value="50">50条 (广泛)</option>
                                </select>
                            </div>
                            <div class="mcp-selector-container">
                                <button class="upload-btn" 
                                       @@click="toggleMcpSelector"
                                       :class="{'web-search-enabled': mcpSelected !== ''}"
                                       title="选择智能工具进行自动检索">
                                    <i class="fa fa-cogs"></i>
                                    {{ mcpSelected ? `MCP: ${mcpSelected}` : '智能工具' }}
                                </button>
                                <select v-if="mcpSelected !== ''" v-model="mcpSelected" 
                                        title="选择MCP工具类型">
                                    <option value="">关闭MCP</option>
                                    <option v-if="!isEmbedded" value="IMS">IMS (投资管理)</option>
                                    <option v-if="isEmbedded" value="FMS">基金管理 (参考模版)</option>
                                    <!-- 未来可以在这里添加更多MCP工具选项 -->
                                    <!-- <option value="CRM">CRM (客户关系)</option> -->
                                    <!-- <option value="HR">HR (人力资源)</option> -->
                                </select>
                            </div>
                        </div>
                        <div class="right-controls">
                            <button class="clear-btn" v-on:click="clearConversation"
                                    :disabled="isGenerating || conversation.length === 0"
                                    title="清除当前对话历史，重新开始">
                                <i class="fa fa-trash"></i> 清除
                            </button>
                            <button class="send-btn" v-on:click="sendMessage"
                                    :disabled="isGenerating"
                                    v-if="!isGenerating">
                                <span v-if="hasProcessingFiles">
                                    等待文件 ({{ readyFilesCount }}/{{ files.length }})
                                </span>
                                <span v-else>发送</span>
                                <span class="send-icon">&#8629;</span>
                            </button>
                            <button class="stop-btn" v-on:click="stopGeneration"
                                    v-if="isGenerating">
                                <i class="fa fa-stop"></i> 终止
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            }
        </div>
    </div>
</div>

@section scripts{
    <script src="~/Content/js/marked.js"></script>
    <script type="text/javascript">
        
        // 在Vue data部分添加模型选择数据
        var app = new Vue({
            el: '#page-app',
            data: {
                files: [],
                selectedProjects: [],
                currentMessage: '',
                conversation: [],
                model: {
                    selected: 'qwen3-30b-a3b-mlx'
                },
                autoScrollEnabled: true,
                userScrolledUp: false,
                isGenerating: false,
                abortController: null,
                showProjectSelector: false,
                projectSearch: '',
                searchResults: [],
                loadingProjects: false,
                searchTimeout: null,
                searchStatus: '', // 搜索状态：'waiting' | 'searching' | ''
                webSearchEnabled: false, // 联网搜索开关
                webSearchLimit: 8, // 联网搜索结果数量限制
                currentWebSearchResults: [], // 当前搜索到的结果（不显示给用户）
                suggestedQuestions: [], // 推荐问题列表
                loadingSuggestions: false, // 是否正在加载推荐问题
                mcpSelected: '', // 选中的MCP工具类型 ('IMS' 或其他，空字符串表示未选择)
                mcpStatus: '', // MCP状态信息
                mcpProjects: [], // MCP检索到的项目
                isEmbedded: @embedded.ToString().ToLower() // 从服务器端获取embedded状态
            },
            computed: {
                fileListHtml() {
                   let html = '';
                    
                    this.files.forEach((file, index) => {
                        const iconClass = file.type === 'pdf' ? 'fa-file-pdf-o' : 'fa-file-word-o';
                        let statusHtml;
                        
                        if (file.extracted) {
                            statusHtml = '<span class="file-status completed">✓ 已提取，可发送</span>';
                        } else if (file.content && (file.content.startsWith('文档解析失败') || file.content.startsWith('文档处理失败'))) {
                            statusHtml = '<span class="file-status error">✗ 解析失败</span>';
                        } else {
                            statusHtml = '<span class="file-status processing"><span class="loading-spinner"></span> 处理中，请稍候</span>';
                        }
                        
                        html += `
        <div class="file-display-item">
            <i class="fa ${iconClass} file-icon"></i>
            <div class="file-content">
                <div class="file-title">${file.name}</div>
                <div class="file-info">
                    ${file.size}
                    ${statusHtml}
                </div>
            </div>
            <button class="remove-file-btn" onclick="app.removeFile(${index})">×</button>
        </div>`;
                    })
                    return html
                },
                selectedProjectsHtml() {
                    let html = '';
                    
                    this.selectedProjects.forEach((project, index) => {
                        html += `
                            <div class="selected-project-item">
                                <i class="fa fa-folder project-icon"></i>
                                <div class="selected-project-info">
                                    <div class="selected-project-name">${project.Name}</div>
                                    <div class="selected-project-status">
                                        ✓ 已添加到对话
                                    </div>
                                </div>
                                <button class="remove-project-btn" onclick="app.removeProject(${index})">×</button>
                            </div>`;
                    });
                    
                    return html;
                },
                hasProcessingFiles() {
                    console.log('hasProcessingFiles开始计算，文件列表:', this.files.map(f => ({
                        name: f.name,
                        extracted: f.extracted,
                        hasContent: !!f.content,
                        contentLength: f.content ? f.content.length : 0
                    })));
                    
                    const result = this.files.some(file => {
                        // 文件正在处理的条件：
                        // 1. 没有extracted标记 且 没有content
                        // 2. 或者 content是错误信息（以"文档解析失败"或"文档处理失败"开头）
                        const condition1 = (!file.extracted && !file.content);
                        const condition2 = (file.content && (
                            file.content.startsWith('文档解析失败') || 
                            file.content.startsWith('文档处理失败')
                        ));
                        const isProcessing = condition1 || condition2;
                        
                        console.log('文件处理状态检查:', {
                            name: file.name,
                            extracted: file.extracted,
                            hasContent: !!file.content,
                            condition1: condition1,
                            condition2: condition2,
                            isProcessing: isProcessing,
                            contentStart: file.content ? file.content.substring(0, 20) : 'null'
                        });
                        
                        return isProcessing;
                    });
                    
                    console.log('hasProcessingFiles最终结果:', result, '文件总数:', this.files.length);
                    return result;
                },
                readyFilesCount() {
                    const count = this.files.filter(file => 
                        file.extracted && file.content && file.content.trim() && 
                        !file.content.startsWith('文档解析失败') && 
                        !file.content.startsWith('文档处理失败')
                    ).length;
                    
                    console.log('readyFilesCount计算结果:', count, '文件总数:', this.files.length);
                    return count;
                }
            },
            methods: {
                // 获取搜索状态文本
                getSearchStatusText() {
                    switch (this.searchStatus) {
                        case 'waiting':
                            return '准备搜索...';
                        case 'searching':
                            return '搜索中...';
                        default:
                            return '';
                    }
                },
                formatTime(timestamp) {
                    if (!timestamp) return '';
                    const date = new Date(timestamp);
                    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                },
                handleScroll() {
                    const container = this.$refs.chatContainer;
                    if (!container) return;

                    // 检查用户是否手动滚动（非底部）
                    const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 10;

                    // 更新滚动状态
                    this.autoScrollEnabled = isAtBottom;
                    this.userScrolledUp = !isAtBottom;
                },

                // 确保滚动监听器正确绑定
                bindScrollListener() {
                    this.$nextTick(() => {
                        const container = this.$refs.chatContainer;
                        if (container && !container.hasScrollListener) {
                            container.addEventListener('scroll', this.handleScroll);
                            container.hasScrollListener = true;
                        }
                    });
                },

                scrollToBottom() {
                    // 只有在自动滚动启用时才滚动
                    if (!this.autoScrollEnabled) return;

                    this.$nextTick(() => {
                        const container = this.$refs.chatContainer;
                        if (container) {
                            container.scrollTop = container.scrollHeight;
                        }
                    });
                },

                // 提取和处理思考内容
                processThinkContent(content, conversation) {
                    conversation.content = content
                    const thinkStart = content.indexOf('<think>');
                    const thinkEnd = content.indexOf('</think>');

                    if (thinkStart !== -1 && thinkEnd !== -1 && thinkEnd > thinkStart) {
                        // 完整的think标签对：<think>...</think>
                        const thinkContent = content.substring(thinkStart + 7, thinkEnd);
                        const visibleContent = content.substring(0, thinkStart);
                        const afterContent = content.substring(thinkEnd + 8);

                        conversation.visibleContent = marked.parse(visibleContent + afterContent)
                        conversation.thinkContent = marked.parse(thinkContent)
                        return
                    } else if (thinkStart === -1 && thinkEnd !== -1) {
                        // 只有结束标签</think>，把前面的内容作为思考内容
                        const thinkContent = content.substring(0, thinkEnd);
                        const visibleContent = content.substring(thinkEnd + 8);

                        conversation.visibleContent = marked.parse(visibleContent)
                        conversation.thinkContent = marked.parse(thinkContent)
                        return
                    } else if (thinkStart !== -1 && thinkEnd === -1) {
                        // 只有开始标签<think>，在流式响应中等待</think>标签
                        // 暂时将所有内容作为可见内容，不进行思考内容分离
                        conversation.visibleContent = marked.parse(content)
                        conversation.thinkContent = null
                        return
                    }

                    // 没有think标签，全部作为可见内容
                    conversation.visibleContent = marked.parse(content)
                    conversation.thinkContent = null
                },

                async sendMessage() {
                    if (!this.currentMessage.trim() && this.files.length === 0 && this.selectedProjects.length === 0) return;
                    if (this.isGenerating) return;

                    // 检查是否有文件正在处理中（更精确的检测）
                    const processingFiles = this.files.filter(file => {
                        // 文件正在处理的条件：
                        // 1. 没有extracted标记 且 没有content
                        // 2. 或者 content是错误信息（以"文档解析失败"或"文档处理失败"开头）
                        return (!file.extracted && !file.content) || 
                               (file.content && (file.content.startsWith('文档解析失败') || file.content.startsWith('文档处理失败')));
                    });
                    
                    const readyFiles = this.files.filter(file => 
                        file.extracted && file.content && file.content.trim() && 
                        !file.content.startsWith('文档解析失败') && 
                        !file.content.startsWith('文档处理失败')
                    );
                    
                    console.log('文件状态检查:', {
                        总文件数: this.files.length,
                        处理中文件数: processingFiles.length,
                        就绪文件数: readyFiles.length,
                        处理中文件: processingFiles.map(f => ({ name: f.name, extracted: f.extracted, hasContent: !!f.content })),
                        就绪文件: readyFiles.map(f => ({ name: f.name, contentLength: f.content ? f.content.length : 0 }))
                    });
                    
                    if (processingFiles.length > 0) {
                        const fileNames = processingFiles.map(f => f.name).join('、');
                        const shouldWait = confirm(
                            `以下文件正在处理中或处理失败：\n${fileNames}\n\n` +
                            `已处理完成的文件：${readyFiles.length}个\n\n` +
                            `是否等待文件处理完成后再发送？\n\n` +
                            `点击"确定"等待处理完成\n` +
                            `点击"取消"仅发送文本内容和已完成的文件`
                        );
                        
                                                 if (shouldWait) {
                             // 等待文件处理完成
                             const maxWaitTime = 30000; // 最多等待30秒
                             const startTime = Date.now();
                             
                             console.log('开始等待文件处理完成...');
                             while (true) {
                                 // 重新检查文件状态
                                 const stillProcessing = this.files.filter(file => {
                                     return (!file.extracted && !file.content) || 
                                            (file.content && (file.content.startsWith('文档解析失败') || file.content.startsWith('文档处理失败')));
                                 });
                                 
                                 if (stillProcessing.length === 0) {
                                     console.log('所有文件处理完成');
                                     break;
                                 }
                                 
                                 if (Date.now() - startTime > maxWaitTime) {
                                     const stillProcessingNames = stillProcessing.map(f => f.name).join('、');
                                     alert(`文件处理超时，以下文件仍在处理中：${stillProcessingNames}\n\n请稍后重试或移除未处理完成的文件`);
                                     return;
                                 }
                                 
                                 await new Promise(resolve => setTimeout(resolve, 500));
                                 console.log('等待中，剩余处理文件数:', stillProcessing.length);
                             }
                         } else {
                             // 用户选择不等待，移除正在处理的文件，保留已完成的文件
                             const originalCount = this.files.length;
                             this.files = this.files.filter(file => 
                                 file.extracted && file.content && file.content.trim() && 
                                 !file.content.startsWith('文档解析失败') && 
                                 !file.content.startsWith('文档处理失败')
                             );
                             console.log(`用户选择不等待，移除了${originalCount - this.files.length}个未完成的文件，保留${this.files.length}个已完成的文件`);
                         }
                    }

                    // 保存当前消息内容，然后立即清空输入框以提升用户体验
                    const currentMessageText = this.currentMessage.trim();
                    const currentFiles = [...this.files];
                    const currentProjects = [...this.selectedProjects];
                    
                    console.log('sendMessage开始 - 保存的状态:', {
                        messageText: currentMessageText.length,
                        filesCount: currentFiles.length,
                        projectsCount: currentProjects.length,
                        filesDetail: currentFiles.map(f => ({ name: f.name, hasContent: !!f.content })),
                        projectsDetail: currentProjects.map(p => ({ name: p.Name, hasContent: !!p.content }))
                    });
                    
                    // 立即清空输入框和相关状态
                    this.currentMessage = '';
                    this.files = [];
                    this.selectedProjects = [];

                    // 计算总文本长度并检查是否需要提示（考虑三个模型的token限制）
                    const totalTextLength = this.calculateTotalTextLengthFromData(currentMessageText, currentFiles, currentProjects);
                    
                    // 估算token数量（中文约1.5字符=1token，英文约4字符=1token，这里保守估算2字符=1token）
                    const estimatedTokens = Math.round(totalTextLength / 2);
                    
                    // 模型上下文限制
                    const qwenTokenLimit = 40960;        // qwen3-30b的token限制
                    const deepseekTokenLimit = 4000;    // DeepSeek R1的实用token限制（超过会很慢）
                    const qwenlongTokenLimit = 61072;    // QwenLong-32B的token限制
                    const qwen25TokenLimit = 1000000;    // Qwen2.5-14B-Instruct-1M的token限制
                    
                    if (estimatedTokens > qwen25TokenLimit * 0.8) { // 超过Qwen2.5 80%就警告
                        const shouldContinue = confirm(
                            `⚠️ 警告：输入内容过长风险\n\n` +
                            `检测到您的输入内容很长（约${Math.round(totalTextLength/1000)}k字符，估算${estimatedTokens} tokens）。\n\n` +
                            `这可能超出AI模型的上下文长度限制：\n` +
                            `• Qwen3-30b: 40960 tokens\n` +
                            `• DeepSeek R1: 4000 tokens (推荐范围)\n` +
                            `• QwenLong-32B: 61072 tokens\n` +
                            `• Qwen2.5-14B: 1M tokens\n\n` +
                            `建议您：\n` +
                            `• 减少文件数量或项目数量\n` +
                            `• 关闭联网搜索\n` +
                            `• 将问题拆分为多个简单问题\n` +
                            `• 清除对话历史重新开始\n\n` +
                            `是否仍要继续发送？（可能会收到空回复或错误）`
                        );
                        
                        if (!shouldContinue) {
                            // 恢复输入内容，让用户自己调整
                            this.currentMessage = currentMessageText;
                            this.files = currentFiles;
                            this.selectedProjects = currentProjects;
                            return;
                        }
                    } else if (estimatedTokens > qwenTokenLimit * 0.8) { // 超过qwen限制，根据内容长度智能推荐模型
                        if (!this.model.selected.includes('qwen2.5-14b-instruct-1m') && !this.model.selected.includes('qwenlong-32b')) {
                            // 如果内容超过QwenLong限制，直接推荐Qwen2.5
                            if (estimatedTokens > qwenlongTokenLimit * 0.8 && this.model.selected != 'qwen2.5-14b-instruct-1m') {
                                const shouldSwitch = confirm(
                                    `💡 模型推荐\n\n` +
                                    `检测到您的输入内容很长（约${Math.round(totalTextLength/1000)}k字符，估算${estimatedTokens} tokens）。\n\n` +
                                    `建议切换到Qwen2.5-14B模型以获得更好的处理效果：\n\n` +
                                    `• Qwen2.5-14B: 支持长文本（1M tokens），处理大量内容速度适中\n` +
                                    `• QwenLong-32B: 推理能力强，但上下文较短（61K tokens）\n` +
                                    `• DeepSeek R1: 推理质量好，但超过4000 tokens会很慢\n\n` +
                                    `是否切换到Qwen2.5-14B模型？`
                                );
                                
                                if (shouldSwitch) {
                                    this.model.selected = 'qwen2.5-14b-instruct-1m';
                                    // 给用户一点时间看到模型切换
                                    await new Promise(resolve => setTimeout(resolve, 500));
                                }
                            } else if (this.model.selected != 'qwenlong-l1-32b-mlx') {
                                // 内容适中，推荐QwenLong作为平衡选择
                                const shouldSwitch = confirm(
                                    `💡 模型推荐\n\n` +
                                    `检测到您的输入内容较长（约${Math.round(totalTextLength/1000)}k字符，估算${estimatedTokens} tokens）。\n\n` +
                                    `建议切换模型以获得更好的处理效果：\n\n` +
                                    `• QwenLong-32B: 推理能力强，支持中长文本（61K tokens）- 推荐\n` +
                                    `• Qwen2.5-14B: 支持长文本（1M tokens），处理大量内容速度适中\n` +
                                    `• DeepSeek R1: 推理质量好，但超过4000 tokens会很慢\n\n` +
                                    `是否切换到QwenLong-32B模型？`
                                );
                                
                                if (shouldSwitch) {
                                    this.model.selected = 'qwenlong-l1-32b-mlx';
                                    // 给用户一点时间看到模型切换
                                    await new Promise(resolve => setTimeout(resolve, 500));
                                }
                            }
                        }
                    } else if (estimatedTokens > qwenlongTokenLimit * 0.8 && this.model.selected != 'qwen2.5-14b-instruct-1m') {
                        // QwenLong超过限制，建议切换到Qwen2.5
                        const shouldSwitch = confirm(
                            `⚠️ 模型容量提醒\n\n` +
                            `检测到您的输入内容较长（约${Math.round(totalTextLength/1000)}k字符，估算${estimatedTokens} tokens）。\n\n` +
                            `QwenLong-32B模型可能接近上下文长度限制（61K tokens），建议切换到Qwen2.5-14B模型：\n\n` +
                            `• Qwen2.5-14B: 支持长文本（1M tokens），处理大量内容更稳定\n` +
                            `• QwenLong-32B: 推理能力强，但上下文有限（61K tokens）\n\n` +
                            `是否切换到Qwen2.5-14B模型？`
                        );
                        
                        if (shouldSwitch) {
                            this.model.selected = 'qwen2.5-14b-instruct-1m';
                            // 给用户一点时间看到模型切换
                            await new Promise(resolve => setTimeout(resolve, 500));
                        }
                    } else if (this.model.selected === 'deepseek-r1-0528' && totalTextLength > 5000) {
                        // DeepSeek处理较长内容时的速度提示
                        const shouldContinue = confirm(
                            `检测到您的输入内容较长（约${Math.round(totalTextLength/1000)}k字符），使用DeepSeek模型处理可能需要较长时间。\n\n` +
                            `如果希望更快的响应速度，建议选择：\n` +
                            `• "Qwen2.5-14B (长文本)"模型 - 处理长文本速度更快\n` +
                            `• "Qwen3-30b (极速)"模型 - 最快速度\n\n` +
                            `是否继续使用DeepSeek模型？\n\n` +
                            `点击"确定"继续，点击"取消"自动切换到长文本模型。`
                        );
                        
                        if (!shouldContinue) {
                            this.model.selected = 'qwen2.5-14b-instruct-1m';
                            // 给用户一点时间看到模型切换
                            await new Promise(resolve => setTimeout(resolve, 500));
                        }
                    }

                    // 确保之前的异步操作已完成
                    if (this.abortController) {
                        try {
                            this.abortController.abort();
                        } catch (e) {
                            console.warn('清理之前的abortController时出错:', e);
                        }
                        this.abortController = null;
                    }

                    console.log('模型推荐逻辑完成后 - 检查保存的状态:', {
                        messageText: currentMessageText.length,
                        filesCount: currentFiles.length,
                        projectsCount: currentProjects.length,
                        model: this.model.selected
                    });

                    // 设置生成状态
                    this.isGenerating = true;

                    // 清理之前可能存在的abortController
                    if (this.abortController) {
                        try {
                            this.abortController.abort();
                        } catch (e) {
                            console.warn('清理之前的abortController时出错:', e);
                        }
                        this.abortController = null;
                    }

                    // 先将文档和项目内容作为历史消息添加到对话中
                    let contextMsgIndex = -1;
                    let contextVisibleContent = '';
                    
                    console.log('准备添加上下文消息:', {
                        currentFiles: currentFiles.length,
                        currentProjects: currentProjects.length,
                        filesDetail: currentFiles.map(f => ({ name: f.name, hasContent: !!f.content, contentLength: f.content ? f.content.length : 0 })),
                        projectsDetail: currentProjects.map(p => ({ name: p.Name, hasContent: !!p.content, contentLength: p.content ? p.content.length : 0 }))
                    });
                    
                    // 首先检查实际有内容的文件和项目
                    const filesWithContent = currentFiles.filter(file => file.content && file.content.trim() && 
                        !file.content.startsWith('文档解析失败') && 
                        !file.content.startsWith('文档处理失败'));
                    const projectsWithContent = currentProjects.filter(project => project.content && project.content.trim());
                    
                    console.log('内容检查详情:', {
                        总文件数: currentFiles.length,
                        有内容的文件数: filesWithContent.length,
                        总项目数: currentProjects.length,
                        有内容的项目数: projectsWithContent.length,
                        文件详情: currentFiles.map(f => ({ 
                            name: f.name, 
                            hasContent: !!f.content,
                            contentLength: f.content ? f.content.length : 0,
                            contentPreview: f.content ? f.content.substring(0, 50) + '...' : 'null',
                            extracted: f.extracted
                        }))
                    });
                    
                    // 检查是否有文档或项目内容需要添加到历史中
                    if (filesWithContent.length > 0 || projectsWithContent.length > 0) {
                        let contextContent = '';
                        
                        // 添加项目信息到历史
                        if (projectsWithContent.length > 0) {
                            let projectInfo = '';
                            let projectVisibleInfo = '<div class="projects-container">';
                            
                            projectsWithContent.forEach(project => {
                                projectInfo += `[项目信息: ${project.Name}]\n${project.content}\n\n`;
                                
                                projectVisibleInfo += `
                                    <div class="status-tag project">
                                        <i class="fa fa-folder"></i>
                                        <span class="status-text">${project.Name}</span>
                                        <span class="status-meta">(项目)</span>
                                    </div>`;
                            });
                            
                            projectVisibleInfo += '</div>';
                            contextContent += projectInfo;
                            contextVisibleContent += projectVisibleInfo;
                        }
                        
                        // 添加文件内容到历史
                        if (filesWithContent.length > 0) {
                            let fileInfo = '';
                            let fileVisibleInfo = '<div class="files-container">';
                            
                            filesWithContent.forEach(file => {
                                fileInfo += `[附件: ${file.name}]\n${file.content}\n\n`;
                                
                                const iconClass = file.type === 'pdf' ? 'fa-file-pdf-o' : 'fa-file-word-o';
                                fileVisibleInfo += `
                                    <div class="status-tag file">
                                        <i class="fa ${iconClass}"></i>
                                        <span class="status-text">${file.name}</span>
                                        <span class="status-meta">(${file.size})</span>
                                    </div>`;
                            });
                            
                            fileVisibleInfo += '</div>';
                            contextContent += fileInfo;
                            contextVisibleContent += fileVisibleInfo;
                        }
                        
                        // 只有当有实际内容时才创建上下文消息
                        if (contextContent.trim()) {
                            contextMsgIndex = this.conversation.push({
                                role: 'user',
                                content: contextContent.trim(),
                                visibleContent: contextVisibleContent,
                                timestamp: new Date().getTime(),
                                isContextMessage: true // 标记为上下文消息
                            }) - 1;
                            
                            console.log('上下文消息已添加:', {
                                contextMsgIndex: contextMsgIndex,
                                contentLength: contextContent.trim().length,
                                visibleContentLength: contextVisibleContent.length,
                                isContextMessage: true,
                                包含文件数: filesWithContent.length,
                                包含项目数: projectsWithContent.length
                            });
                        } else {
                            console.log('虽然有文件或项目，但没有有效内容，不创建上下文消息');
                        }
                    } else {
                        console.log('没有文档或项目内容需要添加到历史中 - 文件数:', currentFiles.length, '项目数:', currentProjects.length);
                        if (currentFiles.length > 0) {
                            console.log('文件状态详情:', currentFiles.map(f => ({ 
                                name: f.name, 
                                extracted: f.extracted,
                                hasContent: !!f.content,
                                contentType: typeof f.content,
                                isProcessing: !f.extracted && !f.content 
                            })));
                        }
                    }
                    
                    // 构建当前用户问题消息
                    let userContent = '';
                    let userVisibleContent = '';
                    
                    // 如果开启智能检索，先显示检索状态
                    if (this.mcpSelected !== '' && currentMessageText) {
                        userVisibleContent += `<div class="status-tag searching">
                                <span class="loading-spinner" style="width:12px;height:12px;margin-right:6px;"></span>
                                <span class="status-text">正在智能检索...</span>
                            </div>`;
                    }
                    
                    // 如果开启联网搜索，先显示搜索状态
                    if (this.webSearchEnabled && currentMessageText) {
                        userVisibleContent += `<div class="status-tag searching">
                                <span class="loading-spinner" style="width:12px;height:12px;margin-right:6px;"></span>
                                <span class="status-text">正在联网搜索...</span>
                            </div>`;
                    }
                    
                    // 添加用户的具体问题
                    if (currentMessageText) {
                        userContent = currentMessageText;
                        userVisibleContent += currentMessageText;
                    }

                    // 添加当前用户问题消息
                    const userMsgIndex = this.conversation.push({
                        role: 'user',
                        content: userContent,
                        visibleContent: userVisibleContent,
                        timestamp: new Date().getTime()
                    }) - 1;

                    // 确保滚动监听器绑定
                    this.bindScrollListener();
                    this.scrollToBottom();

                    // 判断是否需要进行MCP和联网搜索
                    const needMcp = this.mcpSelected !== '';
                    const needWebSearch = this.webSearchEnabled && currentMessageText;
                    
                    // 添加AI消息占位符，根据需要的操作显示不同状态
                    let aiInitialMessage = "<div class='thinking-animation thinking-dots'>思考中</div>";
                    if (needMcp && needWebSearch) {
                        aiInitialMessage = "<div class='mcp-thinking thinking-dots'>正在进行智能检索</div>";
                    } else if (needMcp) {
                        aiInitialMessage = "<div class='mcp-thinking thinking-dots'>正在检索相关信息</div>";
                    } else if (needWebSearch) {
                        aiInitialMessage = "<div class='web-search-thinking thinking-dots'>等待联网搜索完成</div>";
                    }
                    
                    const aiMsgIndex = this.conversation.push({
                        role: 'assistant',
                        content: '', // 状态信息不应该放入content中
                        visibleContent: aiInitialMessage,
                        thinkContent: null,
                        thinkExpanded: false,
                        timestamp: new Date().getTime()
                    }) - 1;

                    this.scrollToBottom();

                    try {
                        // 按顺序执行：MCP -> 联网搜索 -> AI处理
                        await this.processWithMcpAndWebSearch(aiMsgIndex, userMsgIndex, currentMessageText, userContent, contextMsgIndex);
                    } catch (error) {
                        console.error("sendMessage Error:", error);
                        
                        // 检查askStreamPost是否已经设置了详细的错误信息
                        const currentContent = this.conversation[aiMsgIndex].content;
                        const hasDetailedError = currentContent && currentContent.includes('<div style="color:');
                        
                        // 检查是否已经有实际生成的内容
                        const hasRealContent = this.conversation[aiMsgIndex].content && 
                            this.conversation[aiMsgIndex].content.trim() && 
                            !this.conversation[aiMsgIndex].content.includes('thinking-animation') &&
                            !this.conversation[aiMsgIndex].content.includes('mcp-thinking') &&
                            !this.conversation[aiMsgIndex].content.includes('web-search-thinking') &&
                            !this.conversation[aiMsgIndex].content.includes('生成已终止');
                        
                        // 如果askStreamPost已经设置了详细错误信息，就不要覆盖
                        if (!hasDetailedError) {
                            // 只有在没有详细错误信息时才设置简单的错误信息
                            if (error.name === 'AbortError' || error.message.includes('被用户终止')) {
                                if (hasRealContent) {
                                    // 保留已有内容，添加终止提示
                                    const terminationNotice = `<div style="color:#FF9800;background:#fff8e1;border:1px solid #FFB74D;border-radius:8px;padding:12px;margin:8px 0;">
                                        <div style="display:flex;align-items:center;margin-bottom:8px;">
                                            <i class="fa fa-stop-circle" style="color:#FF9800;margin-right:8px;"></i>
                                            <strong>生成已终止</strong>
                                        </div>
                                        <div style="font-size:14px;">您主动停止了AI回答生成，以上是已生成的内容。</div>
                                    </div>`;
                                    
                                    this.conversation[aiMsgIndex].visibleContent = this.conversation[aiMsgIndex].visibleContent + terminationNotice;
                                    this.conversation[aiMsgIndex].content = this.conversation[aiMsgIndex].content + '\n\n[生成已终止]';
                                } else {
                                    // 没有内容时显示终止提示
                                    this.conversation[aiMsgIndex].content = `<div style="color:#FF9800;background:#fff8e1;border:1px solid #FFB74D;border-radius:8px;padding:12px;margin:8px 0;">
                                        <div style="display:flex;align-items:center;margin-bottom:8px;">
                                            <i class="fa fa-stop-circle" style="color:#FF9800;margin-right:8px;"></i>
                                            <strong>生成已终止</strong>
                                        </div>
                                        <div style="font-size:14px;">您主动停止了AI回答生成，可以重新发送问题。</div>
                                    </div>`;
                                    this.conversation[aiMsgIndex].visibleContent = this.conversation[aiMsgIndex].content;
                                }
                            } else {
                                if (hasRealContent) {
                                    // 保留已有内容，添加错误提示
                                    const errorNotice = `<div style="color:#e53535;background:#ffebee;border:1px solid #ef5350;border-radius:8px;padding:12px;margin:8px 0;">
                                        <div style="display:flex;align-items:center;margin-bottom:8px;">
                                            <i class="fa fa-exclamation-triangle" style="color:#e53535;margin-right:8px;"></i>
                                            <strong>生成中断</strong>
                                        </div>
                                        <div style="font-size:14px;line-height:1.5;">
                                            生成过程中出现错误：${error.message || '未知错误'}<br/>
                                            以上是已生成的内容，您可以重新发送问题继续对话。
                                        </div>
                                    </div>`;
                                    
                                    this.conversation[aiMsgIndex].visibleContent = this.conversation[aiMsgIndex].visibleContent + errorNotice;
                                    this.conversation[aiMsgIndex].content = this.conversation[aiMsgIndex].content + '\n\n[生成中断: ' + (error.message || '未知错误') + ']';
                                } else {
                                    // 没有内容时显示完整错误信息
                                    this.conversation[aiMsgIndex].content = `<div style="color:#e53535;background:#ffebee;border:1px solid #ef5350;border-radius:8px;padding:12px;margin:8px 0;">
                                        <div style="display:flex;align-items:center;margin-bottom:8px;">
                                            <i class="fa fa-exclamation-triangle" style="color:#e53535;margin-right:8px;"></i>
                                            <strong>请求失败</strong>
                                        </div>
                                        <div style="font-size:14px;line-height:1.5;">
                                            ${error.message || '未知错误，请重试'}<br/><br/>
                                            请尝试重新发送消息，如果问题持续请联系技术支持。
                                        </div>
                                        <button onclick="app.sendMessage()" style="margin-top:8px;padding:6px 12px;background:#42a5f5;color:white;border:none;border-radius:4px;cursor:pointer;">
                                            重试
                                        </button>
                                    </div>`;
                                    this.conversation[aiMsgIndex].visibleContent = this.conversation[aiMsgIndex].content;
                                }
                            }
                            this.conversation[aiMsgIndex].thinkContent = null;
                        }
                        
                        // 强制更新UI
                        this.$forceUpdate();
                        
                        // 滚动到底部显示错误信息
                        if (this.autoScrollEnabled) {
                            this.scrollToBottom();
                        }
                    } finally {
                        // 重置生成状态
                        this.isGenerating = false;
                        this.abortController = null;
                    }
                },

                // 并行处理MCP和联网搜索，然后进行AI对话
                async processWithMcpAndWebSearch(aiMsgIndex, userMsgIndex, currentMessageText, baseUserContent, contextMsgIndex = -1) {
                    const needMcp = this.mcpSelected !== '';
                    const needWebSearch = this.webSearchEnabled && currentMessageText;
                    
                    // 检查是否已经被中断（防止并发问题）
                    if (!this.isGenerating) {
                        console.log('检测到生成已被中断，停止处理');
                        this.cleanupAnimations();
                        return;
                    }
                    
                    try {
                        // 并行执行MCP和联网搜索（都基于原始用户输入）
                        const tasks = [];
                        let mcpTask = null;
                        let webSearchTask = null;
                        
                        // 创建MCP任务
                        if (needMcp) {
                            console.log('创建MCP检索任务，基于原始用户输入:', currentMessageText);
                            mcpTask = this.performMcpSearch(currentMessageText);
                            tasks.push(mcpTask);
                        }
                        
                        // 创建联网搜索任务 - 注意：也基于原始用户输入，不使用MCP结果
                        if (needWebSearch) {
                            console.log('创建联网搜索任务，基于原始用户输入:', currentMessageText);
                            webSearchTask = this.performWebSearchAndUpdate(userMsgIndex, currentMessageText, baseUserContent, contextMsgIndex);
                            tasks.push(webSearchTask);
                        }
                        
                        // 显示正在处理的状态
                        if (needMcp && needWebSearch) {
                            this.conversation[aiMsgIndex].visibleContent = "<div class='mcp-thinking thinking-dots'>正在并行执行智能检索和联网搜索</div>";
                        } else if (needMcp) {
                            this.conversation[aiMsgIndex].visibleContent = "<div class='mcp-thinking thinking-dots'>正在进行智能检索</div>";
                        } else if (needWebSearch) {
                            this.conversation[aiMsgIndex].visibleContent = "<div class='web-search-thinking thinking-dots'>正在联网搜索</div>";
                        }
                        this.$forceUpdate();
                        
                        // 并行执行所有任务
                        if (tasks.length > 0) {
                            console.log('开始并行执行', tasks.length, '个任务');
                            try {
                                await Promise.all(tasks);
                                
                                // 检查任务执行完成后是否已被终止
                                if (!this.isGenerating || (this.abortController && this.abortController.signal.aborted)) {
                                    console.log('检测到任务完成后已被终止，停止后续处理');
                                    return;
                                }
                            } catch (taskError) {
                                // 如果任何任务被终止，停止整个流程
                                if (taskError.name === 'AbortError' || taskError.message.includes('被用户终止')) {
                                    console.log('检测到任务被终止，停止整个流程');
                                    this.cleanupAnimations();
                                    throw taskError;
                                }
                                // 其他错误继续抛出
                                throw taskError;
                            }
                        }
                        
                        // 处理MCP结果（如果有）
                        let finalUserContent = baseUserContent;
                        if (needMcp && mcpTask) {
                            try {
                                console.log('等待MCP检索完成...');
                                const mcpResults = await mcpTask;
                                console.log('MCP检索完成，结果长度:', mcpResults ? mcpResults.length : 0);
                                
                                // 检查MCP完成后是否已被终止
                                if (!this.isGenerating || (this.abortController && this.abortController.signal.aborted)) {
                                    console.log('检测到MCP完成后已被终止，停止处理');
                                    this.cleanupAnimations();
                                    return;
                                }
                                
                                if (mcpResults && mcpResults.trim()) {
                                    // 清理MCP结果，移除项目ID等仅用于显示的信息
                                    const cleanedMcpResults = this.cleanMcpResultsForAI(mcpResults);
                                    
                                    // 将清理后的MCP结果添加到用户内容中
                                    finalUserContent = cleanedMcpResults + '\n\n' + finalUserContent;
                                    
                                    // 更新用户消息显示MCP成功状态
                                    this.updateUserMessageWithMcpResults(userMsgIndex, mcpResults);
                                    
                                    // 显示MCP成功状态到AI消息区域
                                    if (!needWebSearch) {
                                        this.conversation[aiMsgIndex].visibleContent = "<div class='thinking-animation thinking-dots'>智能检索完成，正在思考中</div>";
                                        this.$forceUpdate();
                                    }
                                } else if (mcpResults === null) {
                                    // MCP没有找到结果
                                    console.log('MCP检索未找到相关项目');
                                    this.updateUserMessageWithMcpFailure(userMsgIndex, '未找到相关项目信息');
                                } else {
                                    // MCP出错（空字符串）
                                    console.log('MCP检索失败');
                                    this.updateUserMessageWithMcpFailure(userMsgIndex, 'MCP检索失败');
                                }
                            } catch (mcpError) {
                                console.error('MCP任务执行失败:', mcpError);
                                this.updateUserMessageWithMcpFailure(userMsgIndex, 'MCP检索失败');
                            }
                        }
                        
                        // 处理联网搜索结果（如果有）
                        if (needWebSearch && webSearchTask) {
                            try {
                                console.log('等待联网搜索完成...');
                                await webSearchTask;
                                console.log('联网搜索完成');
                                
                                // 检查联网搜索完成后是否已被终止
                                if (!this.isGenerating || (this.abortController && this.abortController.signal.aborted)) {
                                    console.log('检测到联网搜索完成后已被终止，停止处理');
                                    this.cleanupAnimations();
                                    return;
                                }
                                
                                // 获取更新后的用户内容（包含联网搜索结果）
                                finalUserContent = this.conversation[userMsgIndex].content;
                                
                                // 如果MCP和联网搜索都完成了，更新AI消息状态
                                if (needMcp) {
                                    this.conversation[aiMsgIndex].visibleContent = "<div class='thinking-animation thinking-dots'>智能检索和联网搜索都已完成，正在思考中</div>";
                                    this.$forceUpdate();
                                }
                            } catch (webSearchError) {
                                console.error('联网搜索任务执行失败:', webSearchError);
                            }
                        }
                        
                        // 最后进行AI对话
                        console.log('开始AI对话，最终用户内容长度:', finalUserContent.length);
                        
                        // 在开始AI对话前最后检查一次是否已被终止
                        if (!this.isGenerating || (this.abortController && this.abortController.signal.aborted)) {
                            console.log('检测到在开始AI对话前已被终止，停止处理');
                            this.cleanupAnimations();
                            return;
                        }
                        
                        // 根据执行的工具显示不同的思考状态
                        let thinkingMessage = "<div class='thinking-animation thinking-dots'>思考中</div>";
                        if (needMcp && needWebSearch) {
                            thinkingMessage = "<div class='thinking-animation thinking-dots'>基于智能检索和联网搜索结果，正在思考中</div>";
                        } else if (needMcp) {
                            thinkingMessage = "<div class='thinking-animation thinking-dots'>基于智能检索结果，正在思考中</div>";
                        } else if (needWebSearch) {
                            thinkingMessage = "<div class='thinking-animation thinking-dots'>基于联网搜索结果，正在思考中</div>";
                        }
                        
                        this.conversation[aiMsgIndex].visibleContent = thinkingMessage;
                        this.$forceUpdate();
                        
                        // 现在finalUserContent就是当前用户问题的内容，历史中已经包含了上下文
                        await this.askStreamPost(aiMsgIndex, userMsgIndex, null, finalUserContent);
                        
                    } catch (error) {
                        console.error('MCP和联网搜索处理过程中出错:', error);
                        throw error; // 重新抛出错误，由上层处理
                    }
                },

                // 执行MCP搜索
                async performMcpSearch(query) {
                    try {
                        console.log('开始MCP搜索，查询内容:', query, '工具类型:', this.mcpSelected);
                        
                        // 检查是否已被终止
                        if (!this.isGenerating || (this.abortController && this.abortController.signal.aborted)) {
                            console.log('MCP搜索被终止');
                            throw new Error('MCP搜索被用户终止');
                        }
                        
                        const response = await fetch('/adminapi/McpSearch', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: `query=${encodeURIComponent(query)}&mcpType=${encodeURIComponent(this.mcpSelected)}`,
                            signal: this.abortController ? this.abortController.signal : undefined
                        });
                        
                        if (!response.ok) {
                            console.error(`MCP搜索API请求失败: HTTP ${response.status}`);
                            throw new Error(`MCP搜索API请求失败: HTTP ${response.status}`);
                        }
                        
                        const result = await response.json();
                        console.log('MCP搜索API响应结果:', {
                            code: result.code,
                            msg: result.msg,
                            hasData: !!result.data,
                            dataLength: result.data ? result.data.length : 0
                        });
                        
                        if (result.code === 0 && result.data && result.data.trim()) {
                            console.log('MCP搜索成功，返回数据长度:', result.data.length);
                            return result.data;
                        } else {
                            console.log('MCP搜索无结果:', result.msg || '无错误信息');
                            return null; // 返回null表示没有找到结果，空字符串表示出错
                        }
                    } catch (error) {
                        if (error.name === 'AbortError' || error.message.includes('被用户终止')) {
                            console.log('MCP搜索被用户终止');
                            throw new Error('MCP搜索被用户终止');
                        }
                        console.error('MCP搜索出错:', error);
                        return ''; // 返回空字符串表示出错
                    }
                },

                // 更新用户消息显示MCP检索结果
                updateUserMessageWithMcpResults(userMsgIndex, mcpResults) {
                    try {
                        if (!mcpResults) return;
                        
                        // 在用户消息中添加MCP状态标签
                        let updatedVisibleContent = this.conversation[userMsgIndex].visibleContent;
                        
                        // 移除"正在智能检索..."的部分
                        updatedVisibleContent = updatedVisibleContent.replace(
                            /<div class="status-tag searching">[\s\S]*?正在智能检索...[\s\S]*?<\/div>/,
                            ''
                        );
                        
                        // 解析MCP检索结果，提取项目信息
                        const projects = this.parseMcpResults(mcpResults);
                        const projectCount = projects.length;
                        
                        // 构建MCP成功结果显示
                        let mcpStatusHtml = '';
                        
                        if (projectCount > 0) {
                            // 有检索结果的情况
                            mcpStatusHtml = `<div><div class="status-tag search-success large">
                                    <i class="fa fa-cogs"></i>
                                    <span class="status-text">智能检索完成，找到${projectCount}个相关项目</span>
                                    <span class="status-meta">(${this.mcpSelected})</span>
                                </div>`;
                            
                            // 添加项目结果容器
                            mcpStatusHtml += '<div class="search-results-container">';
                            
                            projects.forEach(project => {
                                // 判断是否有项目ID来决定是否生成链接
                                if (project.id) {
                                    // 有ID时生成可点击的链接
                                    mcpStatusHtml += `
                                        <div class="status-tag search-result">
                                            <i class="fa fa-folder"></i>
                                            <a href="/index/preview?id=${project.id}" target="_blank" style="color: #1976d2; text-decoration: none; font-weight: 500;">${project.name}</a>
                                            <span class="status-meta">(${project.type || '项目'})</span>
                                        </div>`;
                                } else {
                                    // 没有ID时显示普通文本
                                    mcpStatusHtml += `
                                        <div class="status-tag search-result">
                                            <i class="fa fa-folder"></i>
                                            <span class="status-text">${project.name}</span>
                                            <span class="status-meta">(${project.type || '项目'})</span>
                                        </div>`;
                                }
                            });
                            
                            mcpStatusHtml += '</div></div>';
                        } else {
                            // 没有检索结果的情况
                            mcpStatusHtml = `<div class="status-tag search-success">
                                    <i class="fa fa-cogs"></i>
                                    <span class="status-text">智能检索完成，未找到匹配项目</span>
                                    <span class="status-meta">(${this.mcpSelected})</span>
                                </div>`;
                        }
                        
                        // 在用户问题之前插入MCP状态
                        const userQuestionMatch = this.conversation[userMsgIndex].content.match(/用户问题:\s*(.+)$/);
                        const userQuestion = userQuestionMatch ? userQuestionMatch[1] : '';
                        
                        if (userQuestion && updatedVisibleContent.includes(userQuestion)) {
                            const messageTextPos = updatedVisibleContent.lastIndexOf(userQuestion);
                            updatedVisibleContent = updatedVisibleContent.substring(0, messageTextPos) + 
                                                  mcpStatusHtml + 
                                                  updatedVisibleContent.substring(messageTextPos);
                        } else {
                            updatedVisibleContent += mcpStatusHtml;
                        }
                        
                        this.conversation[userMsgIndex].visibleContent = updatedVisibleContent;
                        this.$forceUpdate();
                        
                    } catch (error) {
                        console.error('更新MCP结果显示时出错:', error);
                    }
                },

                // 解析MCP检索结果，提取项目信息
                parseMcpResults(mcpResults) {
                    const projects = [];
                    
                    try {
                        // 按项目分块处理MCP结果，每个项目信息通常包含项目名称和ID
                        const projectBlocks = mcpResults.split(/(?=项目名称:)/);
                        
                        projectBlocks.forEach(block => {
                            if (!block.trim()) return;
                            
                            // 提取项目名称
                            const nameMatch = block.match(/项目名称:\s*([^\n]+)/);
                            // 提取项目ID (可能的格式: ID: 123, 项目ID: 123, Id: 123等)
                            const idMatch = block.match(/(?:项目)?I[Dd][:：]\s*(\d+)/);
                            
                            if (nameMatch) {
                                const projectName = nameMatch[1].trim();
                                const projectId = idMatch ? idMatch[1] : null;
                                
                                if (projectName) {
                                    projects.push({
                                        name: projectName,
                                        id: projectId,
                                        type: '投资项目'
                                    });
                                }
                            }
                        });
                        
                        // 如果没有找到标准格式，尝试其他可能的格式
                        if (projects.length === 0) {
                            // 尝试匹配其他可能的项目标识和ID
                            const lines = mcpResults.split('\n');
                            let currentProject = null;
                            
                            lines.forEach(line => {
                                const projectMatch = line.match(/(?:项目|Project)[:：]\s*([^\n]+)/i);
                                const idMatch = line.match(/(?:项目)?I[Dd][:：]\s*(\d+)/);
                                
                                if (projectMatch) {
                                    const projectName = projectMatch[1].trim();
                                    if (projectName && projectName.length > 0 && projectName.length < 100) {
                                        currentProject = {
                                            name: projectName,
                                            id: null,
                                            type: '项目信息'
                                        };
                                        projects.push(currentProject);
                                    }
                                } else if (idMatch && currentProject && !currentProject.id) {
                                    // 如果找到ID且有当前项目且还没有ID，则分配给当前项目
                                    currentProject.id = idMatch[1];
                                }
                            });
                        }
                        
                        // 去重处理
                        const uniqueProjects = projects.filter((project, index, self) => 
                            index === self.findIndex(p => p.name === project.name)
                        );
                        
                        //console.log('解析MCP结果，找到项目数量:', uniqueProjects.length, '项目列表:', uniqueProjects.map(p => ({
                        //    name: p.name, 
                        //    id: p.id, 
                        //    hasId: !!p.id
                        //})));
                        return uniqueProjects;
                        
                    } catch (error) {
                        console.error('解析MCP结果时出错:', error);
                        return [];
                    }
                },

                // 清理MCP结果用于发送给AI，移除项目ID等仅用于显示的信息
                cleanMcpResultsForAI(mcpResults) {
                    try {
                        console.log('开始清理MCP结果，移除仅用于显示的信息');
                        
                        // 移除所有项目ID相关信息
                        let cleanedResults = mcpResults;
                        
                        // 移除各种格式的项目ID
                        cleanedResults = cleanedResults.replace(/(?:项目)?I[Dd][:：]\s*\d+/g, '');
                        
                        
                        // 移除多余的空行和空白字符
                        cleanedResults = cleanedResults.replace(/\n\s*\n/g, '\n');
                        cleanedResults = cleanedResults.replace(/^\s+|\s+$/g, '');
                        
                        // 移除每行开头和结尾的空白字符
                        cleanedResults = cleanedResults.split('\n')
                            .map(line => line.trim())
                            .filter(line => line.length > 0)
                            .join('\n');
                        
                        console.log('MCP结果清理完成:', {
                            原始长度: mcpResults.length,
                            清理后长度: cleanedResults.length,
                            减少字符数: mcpResults.length - cleanedResults.length
                        });
                        
                        return cleanedResults;
                        
                    } catch (error) {
                        console.error('清理MCP结果时出错:', error);
                        // 如果清理出错，返回原始结果（但这不应该发生）
                        return mcpResults;
                    }
                },

                // 更新用户消息显示MCP检索失败状态
                updateUserMessageWithMcpFailure(userMsgIndex, errorMessage) {
                    try {
                        // 在用户消息中添加MCP失败状态标签
                        let updatedVisibleContent = this.conversation[userMsgIndex].visibleContent;
                        
                        // 移除"正在智能检索..."的部分
                        updatedVisibleContent = updatedVisibleContent.replace(
                            /<div class="status-tag searching">[\s\S]*?正在智能检索...[\s\S]*?<\/div>/,
                            ''
                        );
                        
                        // 构建MCP失败状态显示
                        const mcpStatusHtml = `<div class="status-tag warning">
                                <i class="fa fa-exclamation-triangle"></i>
                                <span class="status-text">${errorMessage}</span>
                                <span class="status-meta">(${this.mcpSelected})</span>
                            </div>`;
                        
                        // 在用户问题之前插入MCP状态
                        const userQuestionMatch = this.conversation[userMsgIndex].content.match(/用户问题:\s*(.+)$/);
                        const userQuestion = userQuestionMatch ? userQuestionMatch[1] : '';
                        
                        if (userQuestion && updatedVisibleContent.includes(userQuestion)) {
                            const messageTextPos = updatedVisibleContent.lastIndexOf(userQuestion);
                            updatedVisibleContent = updatedVisibleContent.substring(0, messageTextPos) + 
                                                  mcpStatusHtml + 
                                                  updatedVisibleContent.substring(messageTextPos);
                        } else {
                            updatedVisibleContent += mcpStatusHtml;
                        }
                        
                        this.conversation[userMsgIndex].visibleContent = updatedVisibleContent;
                        this.$forceUpdate();
                        
                    } catch (error) {
                        console.error('更新MCP失败状态显示时出错:', error);
                    }
                },

                // 更新用户消息显示工具状态
                updateUserMessageWithToolStatus(userMsgIndex, statusMessage) {
                    try {
                        // 检查是否已经存在工具状态标签，避免重复添加
                        let updatedVisibleContent = this.conversation[userMsgIndex].visibleContent;
                        
                        // 如果已经有工具状态标签，先移除旧的
                        updatedVisibleContent = updatedVisibleContent.replace(/<div class="status-tag[^"]*tool-status[^"]*">.*?<\/div>/g, '');
                        
                        // 确定状态图标和样式
                        let statusIcon = 'fa-cog';
                        let statusClass = 'tool-status';
                        
                        if (statusMessage.includes('🔍')) {
                            statusIcon = 'fa-search';
                        } else if (statusMessage.includes('🌐')) {
                            statusIcon = 'fa-globe';
                        } else if (statusMessage.includes('失败') || statusMessage.includes('错误')) {
                            statusIcon = 'fa-exclamation-triangle';
                            statusClass = 'tool-status warning';
                        }
                        
                        // 构建工具状态显示
                        const toolStatusHtml = `<div class="status-tag ${statusClass}">
                                <i class="fa ${statusIcon}"></i>
                                <span class="status-text">${statusMessage}</span>
                                <span class="status-meta">(智能工具)</span>
                            </div>`;
                        
                        // 在用户问题之前插入工具状态
                        const userQuestionMatch = this.conversation[userMsgIndex].content.match(/用户问题:\s*(.+)$/);
                        const userQuestion = userQuestionMatch ? userQuestionMatch[1] : '';
                        
                        if (userQuestion && updatedVisibleContent.includes(userQuestion)) {
                            const messageTextPos = updatedVisibleContent.lastIndexOf(userQuestion);
                            updatedVisibleContent = updatedVisibleContent.substring(0, messageTextPos) + 
                                                  toolStatusHtml + 
                                                  updatedVisibleContent.substring(messageTextPos);
                        } else {
                            // 如果找不到用户问题，在现有状态标签后添加
                            const statusContainerMatch = updatedVisibleContent.match(/<\/div>(\s*<div[^>]*class="[^"]*message-text[^"]*")/);
                            if (statusContainerMatch) {
                                const insertPos = updatedVisibleContent.indexOf(statusContainerMatch[1]);
                                updatedVisibleContent = updatedVisibleContent.substring(0, insertPos) + 
                                                      toolStatusHtml + 
                                                      updatedVisibleContent.substring(insertPos);
                            } else {
                                // 如果都找不到，添加到开头
                                updatedVisibleContent = toolStatusHtml + updatedVisibleContent;
                            }
                        }
                        
                        this.conversation[userMsgIndex].visibleContent = updatedVisibleContent;
                        this.$forceUpdate();
                        
                        // 自动滚动到显示状态
                        if (this.autoScrollEnabled) {
                            this.scrollToBottom();
                        }
                        
                    } catch (error) {
                        console.error('更新工具状态显示时出错:', error);
                    }
                },

                async askStreamPost(aiMsgIndex, userMsgIndex, webSearchPromise, finalUserContent = null) {
                    try {
                        // 使用传入的finalUserContent或者从conversation中获取
                        let Prompt = finalUserContent || this.conversation[userMsgIndex].content;
                        //console.log('最终发送给AI的Prompt:', Prompt);
                        
                        // 创建AbortController用于终止请求
                        this.abortController = new AbortController();
                        
                        // 设置请求超时
                        const timeoutId = setTimeout(() => {
                            console.warn('AI请求超时，终止请求');
                            this.abortController.abort();
                        }, 600000); // 10分钟超时
                        
                        // 构建历史记录，包含上下文消息，但排除当前正在处理的用户消息
                        const allMessages = this.conversation.slice(0, aiMsgIndex);
                        console.log('所有消息(slice前):', allMessages.map(msg => ({ 
                            role: msg.role, 
                            contentLength: msg.content ? msg.content.length : 0,
                            isContextMessage: msg.isContextMessage,
                            contentPreview: msg.content ? msg.content.substring(0, 50) : 'null'
                        })));
                        
                        const validHistory = allMessages.filter(msg => {
                            // 上下文消息（文件和项目内容）直接包含，不需要复杂过滤
                            if (msg.isContextMessage) {
                                console.log('上下文消息直接包含:', {
                                    contentPreview: msg.content ? msg.content.substring(0, 50) : 'null',
                                    contentLength: msg.content ? msg.content.length : 0,
                                    isContextMessage: true,
                                    role: msg.role
                                });
                                return msg.content && msg.content.trim();
                            }
                            
                            // 其他消息的过滤逻辑
                            const hasValidContent = msg.content && msg.content.trim();
                            
                            // 检查是否是UI状态信息（包含HTML标签的状态信息）
                            const isUIStatusMessage = msg.content && (
                                // 错误信息（包含styled div）
                                msg.content.includes('<div style') ||
                                // 状态标签（包含class的div）
                                (msg.content.includes('<div class="status-tag') && (
                                    msg.content.includes('正在') || 
                                    msg.content.includes('思考中') ||
                                    msg.content.includes('搜索') ||
                                    msg.content.includes('处理')
                                )) ||
                                // 加载动画（包含loading-spinner）
                                msg.content.includes('loading-spinner') ||
                                // 思考动画
                                msg.content.includes('thinking-animation') ||
                                // MCP状态
                                msg.content.includes('mcp-thinking') ||
                                // 搜索状态
                                msg.content.includes('web-search-thinking')
                            );
                            
                            const isValidRole = (msg.role === 'user' || (msg.role === 'assistant' && msg.content.length > 10));
                            
                            console.log('普通消息过滤:', {
                                contentPreview: msg.content ? msg.content.substring(0, 50) : 'null',
                                hasValidContent,
                                isUIStatusMessage,
                                isValidRole,
                                isContextMessage: false,
                                role: msg.role,
                                contentLength: msg.content ? msg.content.length : 0,
                                最终包含: hasValidContent && !isUIStatusMessage && isValidRole
                            });
                            
                            return hasValidContent && !isUIStatusMessage && isValidRole;
                        }).map(val => {
                            // 清理AI回答中的HTML标签和think标签，避免ASP.NET请求验证错误
                            let cleanContent = val.content;
                            
                            if (val.role === 'assistant') {
                                // 移除<think>...</think>标签及其内容
                                cleanContent = cleanContent.replace(/<think>[\s\S]*?<\/think>/g, '');
                                
                                // 移除其他HTML标签但保留内容
                                cleanContent = cleanContent.replace(/<[^>]*>/g, '');
                                
                                // 清理多余的空白字符
                                cleanContent = cleanContent.replace(/\s+/g, ' ').trim();
                            }
                            
                            return {
                                role: val.role,
                                content: cleanContent
                            }
                        });

                        console.log('发送历史记录数量:', validHistory.length, '总对话数量:', this.conversation.length, '当前用户消息索引:', userMsgIndex, 'AI消息索引:', aiMsgIndex);
                        console.log('历史记录详情:', validHistory.map((msg, idx) => ({
                            index: idx,
                            role: msg.role,
                            contentLength: msg.content.length,
                            contentPreview: msg.content.substring(0, 100),
                            isContextMessage: this.conversation.find(c => c.content === msg.content)?.isContextMessage || false
                        })));
                        
                        let response;
                        try {
                            response = await fetch('/adminapi/ChatStreamPost', {
                                method: "POST",
                                headers: {
                                    "Content-Type": "application/x-www-form-urlencoded",
                                    "Accept": "text/event-stream",
                                },
                                signal: this.abortController.signal,
                                body: `Model=${encodeURIComponent(this.model.selected)}&Prompt=${encodeURIComponent(Prompt)}&enableMCP=false&mcpType=&History=${encodeURIComponent(JSON.stringify(validHistory))}`,
                            });
                        } catch (fetchError) {
                            clearTimeout(timeoutId);
                            
                            if (fetchError.name === 'AbortError') {
                                throw new Error('请求被终止');
                            } else if (fetchError.message.includes('Failed to fetch') || fetchError.message.includes('NetworkError')) {
                                throw new Error('网络连接失败，请检查网络状态');
                            } else {
                                throw new Error(`网络请求失败: ${fetchError.message}`);
                            }
                        }
                        
                        clearTimeout(timeoutId);
                        
                        if (!response.ok) {
                            let errorMessage = `服务器错误 (HTTP ${response.status})`;
                            
                            // 尝试获取详细错误信息
                            try {
                                const errorText = await response.text();
                                if (errorText) {
                                    errorMessage += `: ${errorText.substring(0, 100)}`;
                                }
                            } catch (e) {
                                // 忽略获取错误详情的失败
                            }
                            
                            throw new Error(errorMessage);
                        }

                        const reader = response.body.getReader();
                        const decoder = new TextDecoder();
                        let buffer = '';
                        let aiResponse = '';
                        let hasReceivedData = false; // 标记是否收到过任何数据

                        try {
                            while (true) {
                                const { done, value } = await reader.read();
                                if (done) break;

                                hasReceivedData = true;
                                const chunk = decoder.decode(value, { stream: true });
                                buffer += chunk;

                                while (buffer.indexOf('\n\n') >= 0) {
                                    const eventEndIndex = buffer.indexOf('\n\n');
                                    const eventData = buffer.substring(0, eventEndIndex);
                                    buffer = buffer.substring(eventEndIndex + 2);

                                    const dataLine = eventData.split('\n').find(line => line.startsWith('data: '));
                                    if (!dataLine) continue;

                                    const jsonData = dataLine.substring(6);

                                    try {
                                        const parsedData = JSON.parse(jsonData);

                                        if (parsedData.status === "done") {
                                            // Stream completed - 检查是否是上下文长度超限
                                            const responseLength = aiResponse.trim().length;
                                            
                                            // 改进检测逻辑：
                                            // 1. 完全没有收到任何内容 (长度为0)
                                            // 2. 收到的内容非常少 (少于20字符) 且不包含常见的开头词汇
                                            // 3. 如果选择的是qwen模型且内容极少，更可能是上下文超限
                                            const isLikelyContextLimit = (
                                                responseLength === 0 || 
                                                (responseLength < 20 && 
                                                 !aiResponse.includes('您好') && 
                                                 !aiResponse.includes('抱歉') && 
                                                 !aiResponse.includes('我') &&
                                                 !aiResponse.includes('可以') &&
                                                 !aiResponse.includes('问题'))
                                            );
                                            
                                            if (isLikelyContextLimit) {
                                                console.warn('检测到可能的上下文长度超限 - AI回答长度:', responseLength, 'AI回答内容:', aiResponse, '当前模型:', this.model.selected);
                                                
                                                // 获取当前模型的token限制信息
                                                const getModelLimitInfo = () => {
                                                    if (this.model.selected.includes('qwen3-30b')) {
                                                        return { 
                                                            name: 'qwen3-30b', 
                                                            limit: '40960 tokens', 
                                                            suggestions: [
                                                                '尝试切换到QwenLong-32B (61072 tokens)',
                                                                '尝试切换到Qwen2.5-14B (1M tokens)',
                                                                '尝试切换到DeepSeek R1 (4000 tokens推荐范围)'
                                                            ]
                                                        };
                                                    } else if (this.model.selected.includes('deepseek')) {
                                                        return { 
                                                            name: 'DeepSeek R1', 
                                                            limit: '4000 tokens推荐范围',
                                                            suggestions: [
                                                                '尝试切换到QwenLong-32B (61072 tokens)',
                                                                '尝试切换到Qwen2.5-14B (1M tokens)'
                                                            ]
                                                        };
                                                    } else if (this.model.selected.includes('qwenlong-l1-32b-mlx')) {
                                                        return { 
                                                            name: 'QwenLong-32B', 
                                                            limit: '61072 tokens',
                                                            suggestions: [
                                                                '尝试切换到Qwen2.5-14B (1M tokens)'
                                                            ]
                                                        };
                                                    } else if (this.model.selected.includes('qwen2.5-14b-instruct-1m')) {
                                                        return { 
                                                            name: 'Qwen2.5-14B', 
                                                            limit: '1M tokens',
                                                            suggestions: []
                                                        };
                                                    } else {
                                                        return { name: '当前模型', limit: '未知限制', suggestions: [] };
                                                    }
                                                };
                                                
                                                const modelInfo = getModelLimitInfo();
                                                
                                                let switchSuggestions = '';
                                                if (modelInfo.suggestions.length > 0) {
                                                    switchSuggestions = modelInfo.suggestions.map(s => `• ${s}`).join('<br/>') + '<br/>';
                                                }
                                                
                                                // 显示上下文超限提示
                                                const contextLimitMessage = `
                                                    <div style="color:#FF9800;background:#fff8e1;border:1px solid #FFB74D;border-radius:8px;padding:12px;margin:8px 0;">
                                                        <div style="display:flex;align-items:center;margin-bottom:8px;">
                                                            <i class="fa fa-exclamation-triangle" style="color:#FF9800;margin-right:8px;"></i>
                                                            <strong>疑似上下文长度超限</strong>
                                                        </div>
                                                        <div style="font-size:14px;line-height:1.5;">
                                                            AI模型没有返回有效内容，可能超过了上下文长度限制（${modelInfo.name}: ${modelInfo.limit}）。<br/><br/>
                                                            <strong>建议您：</strong><br/>
                                                            • <strong>点击"清除"按钮</strong>清除当前对话历史重新开始<br/>
                                                            • 减少输入的文本量（移除部分文件或项目）<br/>
                                                            • 分段提问，将复杂问题拆分为多个简单问题<br/>
                                                            • 关闭联网搜索以减少上下文长度<br/>
                                                            ${switchSuggestions}
                                                        </div>
                                                    </div>`;
                                                    
                                                this.conversation[aiMsgIndex].content = contextLimitMessage;
                                                this.conversation[aiMsgIndex].visibleContent = contextLimitMessage;
                                                this.$forceUpdate();
                                            }
                                                                } else if (parsedData.tool_status) {
                                            // 显示工具状态更新
                                            console.log('收到工具状态:', parsedData.tool_status);
                                            
                                            // 在用户消息中更新工具状态显示
                                            this.updateUserMessageWithToolStatus(userMsgIndex, parsedData.tool_status);
                                        } else if (parsedData.c) {
                                            aiResponse += parsedData.c;

                                            // 实时更新可见内容
                                            this.processThinkContent(aiResponse, this.conversation[aiMsgIndex]);
                                            
                                            // 检查是否应该滚动
                                            if (this.autoScrollEnabled) {
                                                this.scrollToBottom();
                                            }
                                        }
                                    } catch (e) {
                                        console.error("SSE parsing error:", e);
                                        aiResponse += jsonData;

                                        this.processThinkContent(aiResponse, this.conversation[aiMsgIndex]);
                                        if (this.autoScrollEnabled) {
                                            this.scrollToBottom();
                                        }
                                    }
                                }
                            }
                        } catch (streamError) {
                            console.error('流式读取过程中出错:', streamError);
                            
                            // 检查是否因为没有收到任何数据导致的错误
                            if (!hasReceivedData) {
                                throw new Error('服务器未返回任何数据，请检查后端服务状态');
                            } else {
                                throw new Error(`数据流读取中断: ${streamError.message}`);
                            }
                        }

                        // 处理最后剩余的数据
                        if (buffer.trim()) {
                            aiResponse += buffer;
                            this.processThinkContent(aiResponse, this.conversation[aiMsgIndex]);
                        }

                        // 标记思考内容为折叠状态
                        this.conversation[aiMsgIndex].thinkExpanded = false;
                        // 最后尝试滚动到底部（如果用户允许的话）
                        if (this.autoScrollEnabled) {
                            this.scrollToBottom();
                        }

                        // AI回答完成后，根据回答内容生成新的推荐问题（异步执行，不阻塞主流程）
                        // 使用setTimeout确保推荐问题生成不会与后续操作冲突
                        setTimeout(() => {
                            this.generateSuggestedQuestionsFromResponse(aiResponse, userMsgIndex).catch(error => {
                                console.error('生成推荐问题时出错，但不影响主对话流程:', error);
                            });
                        }, 500);
                        
                    } catch (error) {
                        console.error("AI请求处理错误:", error);
                        
                        // 检查是否已经有实际生成的内容
                        const hasRealContent = this.conversation[aiMsgIndex].content && 
                            this.conversation[aiMsgIndex].content.trim() && 
                            !this.conversation[aiMsgIndex].content.includes('thinking-animation') &&
                            !this.conversation[aiMsgIndex].content.includes('mcp-thinking') &&
                            !this.conversation[aiMsgIndex].content.includes('web-search-thinking') &&
                            !this.conversation[aiMsgIndex].content.includes('生成已终止');
                        
                        if (error.name === 'AbortError' || error.message.includes('请求被终止')) {
                            // 对于中断生成，如果已有内容则保留，否则显示终止提示
                            if (hasRealContent) {
                                // 保留已有内容，添加终止提示
                                const terminationNotice = `<div style="color:#FF9800;background:#fff8e1;border:1px solid #FFB74D;border-radius:8px;padding:12px;margin:8px 0;">
                                    <div style="display:flex;align-items:center;margin-bottom:8px;">
                                        <i class="fa fa-stop-circle" style="color:#FF9800;margin-right:8px;"></i>
                                        <strong>生成已终止</strong>
                                    </div>
                                    <div style="font-size:14px;">您主动停止了AI回答生成，以上是已生成的内容。</div>
                                </div>`;
                                
                                this.conversation[aiMsgIndex].visibleContent = this.conversation[aiMsgIndex].visibleContent + terminationNotice;
                                this.conversation[aiMsgIndex].content = this.conversation[aiMsgIndex].content + '\n\n[生成已终止]';
                            } else {
                                // 没有内容时显示终止提示
                                const errorMessage = `<div style="color:#FF9800;background:#fff8e1;border:1px solid #FFB74D;border-radius:8px;padding:12px;margin:8px 0;">
                                    <div style="display:flex;align-items:center;margin-bottom:8px;">
                                        <i class="fa fa-stop-circle" style="color:#FF9800;margin-right:8px;"></i>
                                        <strong>生成已终止</strong>
                                    </div>
                                    <div style="font-size:14px;">您主动停止了AI回答生成，可以重新发送问题。</div>
                                </div>`;
                                
                                this.conversation[aiMsgIndex].content = errorMessage;
                                this.conversation[aiMsgIndex].visibleContent = errorMessage;
                            }
                        } else {
                            // 对于其他错误，如果已有内容则保留内容并添加错误提示，否则显示完整错误信息
                            if (hasRealContent) {
                                // 保留已有内容，添加错误提示
                                const errorNotice = `<div style="color:#e53935;background:#ffebee;border:1px solid #ef5350;border-radius:8px;padding:12px;margin:8px 0;">
                                    <div style="display:flex;align-items:center;margin-bottom:8px;">
                                        <i class="fa fa-exclamation-triangle" style="color:#e53935;margin-right:8px;"></i>
                                        <strong>生成中断</strong>
                                    </div>
                                    <div style="font-size:14px;line-height:1.5;">
                                        生成过程中出现错误：${error.message || '未知错误'}<br/>
                                        以上是已生成的内容，您可以重新发送问题继续对话。
                                    </div>
                                </div>`;
                                
                                this.conversation[aiMsgIndex].visibleContent = this.conversation[aiMsgIndex].visibleContent + errorNotice;
                                this.conversation[aiMsgIndex].content = this.conversation[aiMsgIndex].content + '\n\n[生成中断: ' + (error.message || '未知错误') + ']';
                            } else {
                                // 没有内容时显示完整错误信息
                                let errorMessage = '';
                                
                                if (error.message.includes('网络连接失败') || error.message.includes('NetworkError')) {
                                    errorMessage = `
                                        <div style="color:#e53935;background:#ffebee;border:1px solid #ef5350;border-radius:8px;padding:12px;margin:8px 0;">
                                            <div style="display:flex;align-items:center;margin-bottom:8px;">
                                                <i class="fa fa-wifi" style="color:#e53935;margin-right:8px;"></i>
                                                <strong>网络连接失败</strong>
                                            </div>
                                            <div style="font-size:14px;line-height:1.5;">
                                                无法连接到AI服务，请检查：<br/>
                                                • 网络连接是否正常<br/>
                                                • 是否可以访问其他网站<br/>
                                                • 稍后重试或联系技术支持
                                            </div>
                                            <button onclick="app.sendMessage()" style="margin-top:8px;padding:6px 12px;background:#42a5f5;color:white;border:none;border-radius:4px;cursor:pointer;">
                                                重试
                                            </button>
                                        </div>`;
                                } else if (error.message.includes('服务器错误') || error.message.includes('HTTP')) {
                                    errorMessage = `
                                        <div style="color:#e53935;background:#ffebee;border:1px solid #ef5350;border-radius:8px;padding:12px;margin:8px 0;">
                                            <div style="display:flex;align-items:center;margin-bottom:8px;">
                                                <i class="fa fa-server" style="color:#e53935;margin-right:8px;"></i>
                                                <strong>服务器错误</strong>
                                            </div>
                                            <div style="font-size:14px;line-height:1.5;">
                                                ${error.message}<br/><br/>
                                                可能的原因：<br/>
                                                • AI服务暂时不可用<br/>
                                                • 服务器负载过高<br/>
                                                • 请稍后重试
                                            </div>
                                            <button onclick="app.sendMessage()" style="margin-top:8px;padding:6px 12px;background:#42a5f5;color:white;border:none;border-radius:4px;cursor:pointer;">
                                                重试
                                            </button>
                                        </div>`;
                                } else if (error.message.includes('超时')) {
                                    errorMessage = `
                                        <div style="color:#FF9800;background:#fff8e1;border:1px solid #FFB74D;border-radius:8px;padding:12px;margin:8px 0;">
                                            <div style="display:flex;align-items:center;margin-bottom:8px;">
                                                <i class="fa fa-clock-o" style="color:#FF9800;margin-right:8px;"></i>
                                                <strong>请求超时</strong>
                                            </div>
                                            <div style="font-size:14px;line-height:1.5;">
                                                AI服务响应超时，可能是由于：<br/>
                                                • 问题过于复杂，处理时间较长<br/>
                                                • 服务器负载较高<br/>
                                                • 网络延迟较大<br/><br/>
                                                建议：简化问题或稍后重试
                                            </div>
                                            <button onclick="app.sendMessage()" style="margin-top:8px;padding:6px 12px;background:#42a5f5;color:white;border:none;border-radius:4px;cursor:pointer;">
                                                重试
                                            </button>
                                        </div>`;
                                } else {
                                    // 通用错误
                                    errorMessage = `
                                        <div style="color:#e53935;background:#ffebee;border:1px solid #ef5350;border-radius:8px;padding:12px;margin:8px 0;">
                                            <div style="display:flex;align-items:center;margin-bottom:8px;">
                                                <i class="fa fa-exclamation-triangle" style="color:#e53935;margin-right:8px;"></i>
                                                <strong>请求失败</strong>
                                            </div>
                                            <div style="font-size:14px;line-height:1.5;">
                                                ${error.message || '未知错误，请重试'}<br/><br/>
                                                如果问题持续出现，请：<br/>
                                                • 尝试刷新页面<br/>
                                                • 清除对话历史重新开始<br/>
                                                • 联系技术支持
                                            </div>
                                        </div>`;
                                }
                                
                                this.conversation[aiMsgIndex].content = errorMessage;
                                this.conversation[aiMsgIndex].visibleContent = errorMessage;
                            }
                        }
                        
                        this.conversation[aiMsgIndex].thinkContent = null;
                        
                        // 强制更新UI显示错误状态
                        this.$forceUpdate();
                        
                        // 滚动到底部显示错误信息
                        if (this.autoScrollEnabled) {
                            this.scrollToBottom();
                        }
                    }
                },

                // 终止生成方法
                stopGeneration() {
                    console.log('用户点击终止按钮');
                    this.isGenerating = false;
                    
                    // 终止所有正在进行的请求
                    if (this.abortController) {
                        this.abortController.abort();
                        console.log('已终止当前请求');
                    }
                    
                    // 清理所有动画状态
                    this.cleanupAnimations();
                    
                    // 强制更新UI
                    this.$forceUpdate();
                },
                
                // 清理动画状态
                cleanupAnimations() {
                    // 找到当前正在生成的AI消息，清理其动画状态
                    const currentAiMessage = this.conversation.find(msg => 
                        msg.role === 'assistant' && 
                        (msg.visibleContent.includes('thinking-animation') || 
                         msg.visibleContent.includes('mcp-thinking') || 
                         msg.visibleContent.includes('web-search-thinking'))
                    );
                    
                    if (currentAiMessage) {
                        // 检查是否已经有实际生成的内容
                        const hasRealContent = currentAiMessage.content && 
                            currentAiMessage.content.trim() && 
                            !currentAiMessage.content.includes('thinking-animation') &&
                            !currentAiMessage.content.includes('mcp-thinking') &&
                            !currentAiMessage.content.includes('web-search-thinking') &&
                            !currentAiMessage.content.includes('生成已终止');
                        
                        if (hasRealContent) {
                            // 如果已经有生成的内容，保留内容并在末尾添加终止提示
                            const terminationNotice = `<div style="color:#FF9800;background:#fff8e1;border:1px solid #FFB74D;border-radius:8px;padding:12px;margin:8px 0;">
                                <div style="display:flex;align-items:center;margin-bottom:8px;">
                                    <i class="fa fa-stop-circle" style="color:#FF9800;margin-right:8px;"></i>
                                    <strong>生成已终止</strong>
                                </div>
                                <div style="font-size:14px;">您主动停止了AI回答生成，以上是已生成的内容。</div>
                            </div>`;
                            
                            // 保留原有内容，添加终止提示
                            currentAiMessage.visibleContent = currentAiMessage.visibleContent + terminationNotice;
                            currentAiMessage.content = currentAiMessage.content + '\n\n[生成已终止]';
                        } else {
                            // 如果没有实际内容，显示终止提示
                            currentAiMessage.visibleContent = `<div style="color:#FF9800;background:#fff8e1;border:1px solid #FFB74D;border-radius:8px;padding:12px;margin:8px 0;">
                                <div style="display:flex;align-items:center;margin-bottom:8px;">
                                    <i class="fa fa-stop-circle" style="color:#FF9800;margin-right:8px;"></i>
                                    <strong>生成已终止</strong>
                                </div>
                                <div style="font-size:14px;">您主动停止了AI回答生成，可以重新发送问题。</div>
                            </div>`;
                            currentAiMessage.content = currentAiMessage.visibleContent;
                        }
                        currentAiMessage.thinkContent = null;
                    }
                    
                    // 清理用户消息中的搜索状态动画
                    const currentUserMessage = this.conversation.find(msg => 
                        msg.role === 'user' && 
                        (msg.visibleContent.includes('loading-spinner') || 
                         msg.visibleContent.includes('正在智能检索') || 
                         msg.visibleContent.includes('正在联网搜索'))
                    );
                    
                    if (currentUserMessage) {
                        // 移除搜索状态动画
                        currentUserMessage.visibleContent = currentUserMessage.visibleContent
                            .replace(/<div class="status-tag searching">[\s\S]*?<\/div>/g, '')
                            .replace(/<div class="status-tag[^"]*">[\s\S]*?loading-spinner[\s\S]*?<\/div>/g, '');
                    }
                },

                // 清除对话历史方法
                clearConversation() {
                    if (this.isGenerating) {
                        alert('正在生成中，无法清除对话历史');
                        return;
                    }
                    
                    if (this.conversation.length === 0) {
                        return;
                    }
                    
                    const confirmed = confirm('确定要清除当前对话历史吗？此操作不可撤销。');
                    if (confirmed) {
                        // 清除对话历史
                        this.conversation = [];
                        
                        // 清除推荐问题，重新生成初始推荐问题
                        this.suggestedQuestions = [];
                        this.getSuggestedQuestions();
                        
                        console.log('对话历史已清除');
                    }
                },
                adjustInputHeight() {
                    var that = this
                    const textarea = that.$refs.messageInput;
                    
                    // 检查textarea是否存在
                    if (!textarea) {
                        console.warn('messageInput textarea not found');
                        return;
                    }
                    
                    // 重置高度以便正确计算
                    textarea.style.height = 'auto';
                    // 计算内容高度（包括最小50px和最大120px限制）
                    const minHeight = 50;
                    const maxHeight = 120;
                    let contentHeight = textarea.scrollHeight;

                    // 应用高度限制
                    if (contentHeight < minHeight) {
                        contentHeight = minHeight;
                    } else if (contentHeight > maxHeight) {
                        contentHeight = maxHeight;
                        textarea.style.overflowY = 'auto'; 
                    } else {
                        textarea.style.overflowY = 'hidden'; 
                    }

                    textarea.style.height = contentHeight + 'px';
                },

                
                handleFileUpload(event) {
                    const files = Array.from(event.target.files);
                    if (!files.length) return;
                    
                    // 统一处理所有支持的文件类型
                    files.forEach(file => {
                        const fileExt = file.name.split('.').pop().toLowerCase();
                        
                        if (['pdf', 'doc', 'docx'].includes(fileExt)) {
                            this.processFile(file);
                        } else {
                            alert('仅支持PDF和Word文档');
                        }
                    });
                },
                
                processFile(file) {
                    // 确定文件类型
                    const fileExt = file.name.split('.').pop().toLowerCase();
                    const fileType = fileExt === 'pdf' ? 'pdf' : 'doc';
                    
                    // 添加到文件列表
                    const fileObj = {
                        id: Date.now() + Math.random().toString(36).substr(2, 9),
                        name: file.name,
                        type: fileType,
                        size: (file.size / 1024).toFixed(2) + ' KB',
                        content: null,
                        extracted: false
                    };
                    this.files.push(fileObj);
                    
                    // 检查文件大小，如果较大则推荐极速模型
                    if (file.size > 500 * 1024) { // 大于500KB
                        const fileTypeDesc = fileType === 'pdf' ? 'PDF文件' : 'Word文档';
                        const currentTotalLength = this.calculateTotalTextLength();
                        this.suggestFastModel(`上传了较大的${fileTypeDesc}`, currentTotalLength);
                    }
                    
                    const formData = new FormData();
                    formData.append('file', file);
                    
                    // 添加调试信息
                    console.log('开始上传文件:', file.name, '大小:', file.size, '类型:', fileType);
                    
                    // 统一使用 ExtractText 接口，后端会根据文件类型自动处理
                    fetch('/adminapi/ExtractText', {
                        method: 'POST',
                        body: formData
                    })
                    .then(async response => {
                        const fileIndex = this.files.findIndex(f => f.id === fileObj.id);
                        
                        console.log('收到响应:', {
                            status: response.status,
                            statusText: response.statusText,
                            headers: Object.fromEntries(response.headers.entries()),
                            ok: response.ok
                        });
                        
                        if (!response.ok) {
                            // 尝试获取错误响应的内容
                            let errorText = '';
                            try {
                                errorText = await response.text();
                                console.error('服务器错误响应:', errorText);
                            } catch (e) {
                                console.error('无法读取错误响应:', e);
                            }
                            throw new Error(`HTTP ${response.status}: ${response.statusText}${errorText ? ' - ' + errorText.substring(0, 100) : ''}`);
                        }
                        
                        // 获取响应文本，检查是否为空
                        const responseText = await response.text();
                        console.log('响应文本长度:', responseText ? responseText.length : 0);
                        console.log('响应内容预览:', responseText ? responseText.substring(0, 200) : 'empty');
                        
                        if (!responseText || responseText.trim() === '') {
                            throw new Error('服务器返回空响应，请检查后端ExtractText接口是否正常');
                        }
                        
                        // 尝试解析JSON
                        let data;
                        try {
                            data = JSON.parse(responseText);
                            console.log('解析后的数据:', {
                                success: data.success,
                                hasText: !!data.text,
                                textLength: data.text ? data.text.length : 0,
                                message: data.message
                            });
                        } catch (parseError) {
                            console.error('JSON解析失败:', parseError);
                            console.error('服务器响应内容:', responseText.substring(0, 500));
                            throw new Error(`服务器返回无效的JSON格式: ${parseError.message}`);
                        }
                        
                        if (fileIndex !== -1) {
                            if (data.success === false) {
                                // 处理错误情况 - 使用Vue.set确保响应式更新
                                this.$set(this.files[fileIndex], 'content', '文档解析失败: ' + (data.message || '未知错误'));
                                this.$set(this.files[fileIndex], 'extracted', false);
                                console.error('文档解析失败:', data.message);
                            } else {
                                // 成功提取文本 - 使用Vue.set确保响应式更新
                                this.$set(this.files[fileIndex], 'content', data.text || '文档内容为空');
                                this.$set(this.files[fileIndex], 'extracted', true);
                                console.log('文档解析成功，文本长度:', data.text ? data.text.length : 0);
                                console.log('文件已就绪，可以发送:', this.files[fileIndex].name);
                                
                                // 强制更新Vue响应式数据
                                this.$forceUpdate();
                                console.log('强制更新Vue数据，当前文件状态:', {
                                    hasProcessingFiles: this.hasProcessingFiles,
                                    readyFilesCount: this.readyFilesCount,
                                    totalFiles: this.files.length
                                });
                                
                                // 检查提取的文本长度，如果很长则推荐极速模型
                                if (data.text && data.text.length > 5000) {
                                    const currentTotalLength = this.calculateTotalTextLength();
                                    this.suggestFastModel('文件包含大量文本内容', currentTotalLength);
                                }
                            }
                        }
                    })
                    .catch(error => {
                        console.error('文件处理错误:', error);
                        console.error('错误详情:', {
                            message: error.message,
                            stack: error.stack,
                            fileName: file.name,
                            fileSize: file.size,
                            fileType: fileType
                        });
                        
                        const fileIndex = this.files.findIndex(f => f.id === fileObj.id);
                        if (fileIndex !== -1) {
                            this.$set(this.files[fileIndex], 'content', `文档处理失败: ${error.message}`);
                            this.$set(this.files[fileIndex], 'extracted', false);
                        }
                    });
                },
                

                
                generateFilesDisplay() {
                    if (this.files.length === 0) return '';

                    let html = '<div style="margin-bottom:12px;">已上传文件:</div>';

                    this.files.forEach(file => {
                        const iconClass = file.type === 'pdf' ? 'fa-file-pdf-o' : 'fa-file-word-o';
                        let status;
                        
                        if (file.extracted) {
                            status = '<span style="color:#4CAF50;margin-left:8px;">✓</span>';
                        } else if (file.content && (file.content.startsWith('文档解析失败') || file.content.startsWith('文档处理失败'))) {
                            status = '<span style="color:#f44336;margin-left:8px;">✗</span>';
                        } else {
                            status = '<span style="color:#FF9800;margin-left:8px;">处理中...</span>';
                        }

                        html += `
        <div class="file-display-item">
            <i class="fa ${iconClass} file-icon"></i>
            <div class="file-content">
                <div class="file-title">${file.name}</div>
                <div class="file-info">
                    ${file.size}
                    ${status}
                </div>
            </div>
        </div>`;
                    });

                    return html;
                },

                // 项目搜索方法 - 后端实时搜索
                async searchProjects() {
                    // 清除之前的搜索定时器
                    if (this.searchTimeout) {
                        clearTimeout(this.searchTimeout);
                    }
                    
                    // 如果输入框为空，清除状态和结果
                    if (!this.projectSearch.trim()) {
                        this.searchStatus = '';
                        this.searchResults = [];
                        await this.initializeSearch(); // 显示最近项目
                        return;
                    }
                    
                    // 设置等待状态
                    this.searchStatus = 'waiting';
                    
                    // 防抖：500ms后执行搜索
                    this.searchTimeout = setTimeout(async () => {
                        await this.performSearch();
                    }, 500);
                },

                async performSearch() {
                    // 设置搜索中状态
                    this.searchStatus = 'searching';
                    this.loadingProjects = true;
                    
                    try {
                        const response = await fetch('/adminapi/SearchProjectsForChat', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: `keywords=${encodeURIComponent(this.projectSearch.trim())}`
                        });
                        const result = await response.json();
                        if (result.code === 0) {
                            this.searchResults = result.data || [];
                        } else {
                            console.error('搜索失败:', result.msg);
                            this.searchResults = [];
                        }
                    } catch (error) {
                        console.error('搜索项目失败:', error);
                        this.searchResults = [];
                    } finally {
                        this.loadingProjects = false;
                        // 搜索完成，清除状态
                        this.searchStatus = '';
                    }
                },

                // 初始化搜索（显示最近项目）
                async initializeSearch() {
                    this.loadingProjects = true;
                    try {
                        const response = await fetch('/adminapi/SearchProjectsForChat', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'keywords=' // 空关键词获取最近项目
                        });
                        const result = await response.json();
                        if (result.code === 0) {
                            this.searchResults = result.data || [];
                        }
                    } catch (error) {
                        console.error('初始化项目列表失败:', error);
                        this.searchResults = [];
                    } finally {
                        this.loadingProjects = false;
                    }
                },

                async selectProject(project) {
                    // 检查是否已经选择过这个项目
                    if (this.selectedProjects.some(p => p.Id === project.Id)) {
                        alert('该项目已经添加过了');
                        return;
                    }

                    try {
                        // 获取项目详细内容
                        const response = await fetch('/adminapi/GetProjectContentForChat', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: `id=${project.Id}`
                        });
                        const result = await response.json();
                        
                        if (result.code === 0 && result.data) {
                            const projectData = result.data;
                            // 构建项目内容
                            let projectContent = `项目名称: ${projectData.Name}\n`;
                            if (projectData.Summary) projectContent += `项目摘要: ${projectData.Summary}\n`;
                            if (projectData.BusinessData) projectContent += `商业数据: ${projectData.BusinessData}\n`;
                            if (projectData.FinancialData) projectContent += `财务数据: ${projectData.FinancialData}\n`;
                            if (projectData.HighLight) projectContent += `项目亮点: ${projectData.HighLight}\n`;
                            if (projectData.Risk) projectContent += `风险分析: ${projectData.Risk}\n`;
                            if (projectData.NextStep) projectContent += `下一步计划: ${projectData.NextStep}\n`;
                            
                            // 添加到已选项目列表
                            this.selectedProjects.push({
                                ...project,
                                content: projectContent
                            });
                            
                            // 检查项目内容长度，推荐模型
                            if (projectContent.length > 5000) {
                                const currentTotalLength = this.calculateTotalTextLength();
                                this.suggestFastModel('选择了内容较多的项目', currentTotalLength);
                            }
                            
                            // 关闭选择器
                            this.showProjectSelector = false;
                            this.projectSearch = '';
                        } else {
                            alert('获取项目详情失败');
                        }
                    } catch (error) {
                        console.error('获取项目详情失败:', error);
                        alert('获取项目详情失败');
                    }
                },

                removeProject(index) {
                    this.selectedProjects.splice(index, 1);
                },

                removeFile(index) {
                    this.files.splice(index, 1);
                },

                // 自动联网搜索方法
                async autoWebSearch(query) {
                    try {
                        console.log('开始调用后端搜索API...');
                        
                        // 检查是否已被终止
                        if (!this.isGenerating || (this.abortController && this.abortController.signal.aborted)) {
                            console.log('联网搜索被终止');
                            throw new Error('联网搜索被用户终止');
                        }
                        
                        // 使用主要的abortController，而不是创建新的
                        const signal = this.abortController ? this.abortController.signal : undefined;
                        
                        // 设置超时
                        const timeoutId = setTimeout(() => {
                            console.warn('联网搜索API调用超时，终止请求');
                            if (this.abortController) {
                                this.abortController.abort();
                            }
                        }, 25000); // 25秒超时
                        
                        const response = await fetch('/adminapi/WebSearch', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: `query=${encodeURIComponent(query)}&limit=${this.webSearchLimit}`,
                            signal: signal
                        });
                        
                        clearTimeout(timeoutId);
                        console.log('后端搜索API响应状态:', response.status);
                        
                        const result = await response.json();
                        console.log('后端搜索API响应结果:', result);
                        
                        if (result.code === 0) {
                            return result.data || [];
                        } else {
                            console.error('自动搜索失败:', result.msg);
                            return [];
                        }
                    } catch (error) {
                        if (error.name === 'AbortError' || error.message.includes('被用户终止')) {
                            console.log('联网搜索被用户终止');
                            throw new Error('联网搜索被用户终止');
                        } else {
                            console.error('自动联网搜索失败:', error);
                        }
                        return [];
                    }
                },

                // 异步执行联网搜索并更新用户消息
                async performWebSearchAndUpdate(userMsgIndex, currentMessageText, baseUserContent, contextMsgIndex = -1) {
                    try {
                        console.log('开始联网搜索...');
                        const searchResults = await this.autoWebSearch(currentMessageText);
                        console.log('联网搜索完成，结果数量:', searchResults.length);
                        
                        // 构建搜索结果的内容和显示
                        let searchInfo = '';
                        let searchVisibleInfo = '';
                        const resultCount = searchResults.length;
                        const enhancedCount = searchResults.filter(r => r.IsContentEnhanced).length;
                        
                        if (searchResults.length > 0) {
                            // 添加搜索状态信息
                            const statusText = enhancedCount > 0 
                                ? `找到${resultCount}条相关结果，${enhancedCount}条已增强内容`
                                : `找到${resultCount}条相关结果`;
                            
                            searchVisibleInfo = `<div><div class="status-tag search-success large">
                                    <i class="fa fa-check-circle"></i>
                                    <span class="status-text">${statusText}，已过滤不相关内容</span>
                                </div>`;
                            
                            // 开始搜索结果容器
                            searchVisibleInfo += '<div class="search-results-container">';
                            
                            searchResults.forEach(result => {
                                if (result.Title) {
                                    // 发送给LLM的内容：不包含网址，只包含标题和内容
                                    searchInfo += `[搜索结果: ${result.Title}]\n`;
                                    if (result.Content) {
                                        searchInfo += `内容: ${result.Content}\n`;
                                    }
                                    searchInfo += '\n';
                                    
                                    // 检查内容是否被增强过：只根据后端标记判断，不自行判断内容长度
                                    const isEnhanced = result.IsContentEnhanced;
                                    const enhancedBadge = isEnhanced ? '<span style="font-size:10px;color:#4CAF50;margin-left:4px;">✓增强</span>' : '';
                                    
                                    searchVisibleInfo += `
                                        <div class="status-tag search-result">
                                            <i class="fa fa-globe"></i>
                                            <a href="${result.Url}" target="_blank">${result.Title}</a>
                                            <span class="status-meta">(联网${enhancedBadge})</span>
                                        </div>`;
                                }
                            });
                            
                            // 结束搜索结果容器
                            searchVisibleInfo += '</div>';
                            searchVisibleInfo += '</div>';
                        } else {
                            // 没有搜索结果
                            searchVisibleInfo = `
                                <div class="status-tag warning">
                                    <i class="fa fa-exclamation-triangle"></i>
                                    <span class="status-text">未找到相关搜索结果</span>
                                </div>`;
                        }
                        
                        // 确保更新用户消息的内容和显示
                        try {
                            // 更新用户消息的内容和显示（移除"正在搜索"状态，添加搜索结果）
                            let updatedVisibleContent = this.conversation[userMsgIndex].visibleContent;
                            
                            // 移除"正在联网搜索..."的部分
                            updatedVisibleContent = updatedVisibleContent.replace(
                                /<div class="status-tag searching">[\s\S]*?正在联网搜索...[\s\S]*?<\/div>/,
                                ''
                            );
                            
                            // 在用户问题之前插入搜索结果
                            const messageTextPos = updatedVisibleContent.lastIndexOf(currentMessageText);
                            if (messageTextPos !== -1) {
                                updatedVisibleContent = updatedVisibleContent.substring(0, messageTextPos) + 
                                                      searchVisibleInfo + 
                                                      updatedVisibleContent.substring(messageTextPos);
                            } else {
                                updatedVisibleContent += searchVisibleInfo;
                            }
                            
                            // 更新conversation中的消息 - 现在只需要添加搜索信息到当前用户问题，不需要baseUserContent
                            this.conversation[userMsgIndex].content = searchInfo + currentMessageText;
                            this.conversation[userMsgIndex].visibleContent = updatedVisibleContent;
                            
                            console.log('用户消息内容已更新，联网搜索内容长度:', searchInfo.length);
                            
                            // 检查联网搜索后的总内容长度，推荐快速模型
                            if (searchInfo.length > 0) {
                                const searchContentLength = searchInfo.length;
                                const totalMessageLength = (searchInfo + baseUserContent).length;
                                
                                // 添加调试日志
                                console.log(`联网搜索完成 - 发送给LLM内容长度: ${searchContentLength}字符 (不含网址), 总内容长度: ${totalMessageLength}, 结果数量: ${searchResults.length}`);
                                console.log(`增强内容统计 - 总结果: ${resultCount}条, 增强内容: ${enhancedCount}条 (${resultCount > 0 ? ((enhancedCount/resultCount)*100).toFixed(1) : 0}%)`);
                                console.log(`当前模型: ${this.model.selected}`);
                                console.log(`优化说明: 网址仅在前端显示，不发送给LLM以节省token`);
                                
                                // 优化模型推荐条件，更敏感地检测大内容
                                if (searchContentLength > 3000) { // 进一步降低到3000
                                    console.log('触发模型推荐: 搜索内容过多');
                                    this.suggestFastModel(`联网搜索返回了大量内容（约${Math.round(searchContentLength/1000)}k字符）`, totalMessageLength);
                                } else if (totalMessageLength > 6000) { // 进一步降低到6000
                                    console.log('触发模型推荐: 总内容过多');
                                    this.suggestFastModel(`当前对话内容较多（约${Math.round(totalMessageLength/1000)}k字符，包含联网搜索结果）`, totalMessageLength);
                                } else if (searchResults.length >= 15) { // 进一步降低到15
                                    console.log('触发模型推荐: 搜索结果数量多');
                                    this.suggestFastModel(`联网搜索返回了${searchResults.length}条结果，内容丰富`, totalMessageLength);
                                } else if (searchResults.length >= 8 && searchContentLength > 2000) { // 降低条件
                                    console.log('触发模型推荐: 中等数量但内容详细');
                                    this.suggestFastModel(`联网搜索返回了${searchResults.length}条内容详细的结果`, totalMessageLength);
                                } else {
                                    console.log('未触发模型推荐');
                                }
                            }
                            
                            // 强制同步更新Vue组件
                            this.$forceUpdate();
                            
                            // 确保Promise正确resolve
                            console.log('联网搜索和用户消息更新完成');
                            
                        } catch (updateError) {
                            console.error('更新用户消息时出错:', updateError);
                            // 即使更新失败，也要继续流程
                        }
                        
                    } catch (error) {
                        console.error('联网搜索失败:', error);
                        
                        try {
                            // 搜索失败时更新显示
                            let updatedVisibleContent = this.conversation[userMsgIndex].visibleContent;
                            updatedVisibleContent = updatedVisibleContent.replace(
                                /<div class="status-tag searching">[\s\S]*?正在联网搜索...[\s\S]*?<\/div>/,
                                `<div class="status-tag error">
                                    <i class="fa fa-exclamation-circle"></i>
                                    <span class="status-text">联网搜索失败</span>
                                </div>`
                            );
                            
                            this.conversation[userMsgIndex].visibleContent = updatedVisibleContent;
                            this.$forceUpdate();
                        } catch (errorUpdateError) {
                            console.error('更新错误显示时出错:', errorUpdateError);
                        }
                        
                        // 即使搜索失败，也不要阻塞后续流程，继续让MCP和AI处理
                        console.log('联网搜索失败，但继续后续流程');
                    }
                },

                // 计算总文本长度（包括用户输入、文件内容、项目内容）
                calculateTotalTextLength() {
                    let totalLength = 0;
                    
                    // 用户输入文本长度
                    if (this.currentMessage.trim()) {
                        totalLength += this.currentMessage.trim().length;
                    }
                    
                    // 文件内容长度
                    this.files.forEach(file => {
                        if (file.content) {
                            totalLength += file.content.length;
                        }
                    });
                    
                    // 项目内容长度
                    this.selectedProjects.forEach(project => {
                        if (project.content) {
                            totalLength += project.content.length;
                        }
                    });
                    
                    // 联网搜索内容长度估算
                    if (this.webSearchEnabled && this.currentMessage.trim()) {
                        // 根据搜索限制数量估算搜索结果长度
                        // 每个搜索结果平均约300-500字符（标题+内容摘要）
                        const avgLengthPerResult = 400;
                        const estimatedSearchLength = this.webSearchLimit * avgLengthPerResult;
                        totalLength += estimatedSearchLength;
                    }
                    
                    // MCP检索内容长度估算（如果开启）
                    if (this.mcpSelected !== '' && this.currentMessage.trim()) {
                        // MCP检索通常返回项目详细信息，估算每个项目约1000-2000字符
                        const avgProjectLength = 1500;
                        const estimatedMcpLength = 3 * avgProjectLength; // 假设平均检索3个项目
                        totalLength += estimatedMcpLength;
                    }
                    
                    return totalLength;
                },

                // 根据传入的数据计算文本长度
                calculateTotalTextLengthFromData(messageText, files, projects, includeWebSearch = true, includeMcp = true) {
                    let totalLength = 0;
                    
                    // 用户输入文本长度
                    if (messageText) {
                        totalLength += messageText.length;
                    }
                    
                    // 文件内容长度
                    files.forEach(file => {
                        if (file.content) {
                            totalLength += file.content.length;
                        }
                    });
                    
                    // 项目内容长度
                    projects.forEach(project => {
                        if (project.content) {
                            totalLength += project.content.length;
                        }
                    });
                    
                    // 联网搜索内容长度估算
                    if (includeWebSearch && this.webSearchEnabled && messageText && messageText.trim()) {
                        // 根据搜索限制数量估算搜索结果长度
                        // 每个搜索结果平均约300-500字符（标题+内容摘要）
                        const avgLengthPerResult = 400;
                        const estimatedSearchLength = this.webSearchLimit * avgLengthPerResult;
                        totalLength += estimatedSearchLength;
                    }
                    
                    // MCP检索内容长度估算（如果开启）
                    if (includeMcp && this.mcpSelected !== '' && messageText && messageText.trim()) {
                        // MCP检索通常返回项目详细信息，估算每个项目约1000-2000字符
                        const avgProjectLength = 1500;
                        const estimatedMcpLength = 3 * avgProjectLength; // 假设平均检索3个项目
                        totalLength += estimatedMcpLength;
                    }
                    
                    return totalLength;
                },

                // 智能模型推荐方法
                suggestFastModel(reason, actualTotalLength = null) {
                    // 计算当前总内容长度，如果传入了实际长度则使用传入的值
                    const totalLength = actualTotalLength !== null ? actualTotalLength : this.calculateTotalTextLength();
                    const estimatedTokens = Math.round(totalLength / 2);
                    const qwenTokenLimit = 40960;
                    const deepseekTokenLimit = 4000;    // DeepSeek R1的实用token限制（超过会很慢）
                    const qwenlongTokenLimit = 61072;    // QwenLong-32B的token限制
                    const qwen25TokenLimit = 1000000;
                    
                    // 如果内容超过Qwen2.5的80%限制，建议减少内容而非切换模型
                    if (estimatedTokens > qwen25TokenLimit * 0.8) {
                        const shouldOptimize = confirm(
                            `⚠️ 内容长度警告\n\n` +
                            `您${reason}，当前内容已经很长（约${Math.round(totalLength/1000)}k字符，估算${estimatedTokens} tokens）。\n\n` +
                            `即使使用最大的Qwen2.5-14B模型（1M tokens），也可能接近上下文长度限制。\n\n` +
                            `建议您：\n` +
                            `• 移除部分文件或项目\n` +
                            `• 关闭联网搜索\n` +
                            `• 将问题拆分为多个简单问题\n\n` +
                            `是否仍要继续？`
                        );
                        return; // 不进行模型切换推荐
                    }
                    
                    // 如果内容超过qwen限制，根据长度推荐合适的模型
                    if (estimatedTokens > qwenTokenLimit * 0.8 && !this.model.selected.includes('qwen2.5-14b-instruct-1m') && !this.model.selected.includes('qwenlong-l1-32b-mlx')) {
                        // 如果超过QwenLong限制，直接推荐Qwen2.5
                        if (estimatedTokens > qwenlongTokenLimit * 0.8) {
                            const shouldSwitch = confirm(
                                `💡 模型推荐\n\n` +
                                `您${reason}，当前内容很长（约${Math.round(totalLength/1000)}k字符，估算${estimatedTokens} tokens）。\n\n` +
                                `建议切换到Qwen2.5-14B模型以获得更好的处理效果：\n\n` +
                                `• Qwen2.5-14B: 支持长文本（1M tokens），处理大量内容速度适中\n` +
                                `• QwenLong-32B: 推理能力强，但上下文较短（61K tokens）\n` +
                                `• DeepSeek R1: 推理质量好，但超过4000 tokens会很慢\n\n` +
                                `是否切换到Qwen2.5-14B模型？`
                            );
                            
                            if (shouldSwitch) {
                                this.model.selected = 'qwen2.5-14b-instruct-1m';
                            }
                        } else {
                            // 内容适中，推荐QwenLong作为平衡选择
                            const shouldSwitch = confirm(
                                `💡 模型推荐\n\n` +
                                `您${reason}，当前内容较长（约${Math.round(totalLength/1000)}k字符，估算${estimatedTokens} tokens）。\n\n` +
                                `建议切换模型以获得更好的处理效果：\n\n` +
                                `• QwenLong-32B: 推理能力强，支持中长文本（61K tokens）- 推荐\n` +
                                `• Qwen2.5-14B: 支持长文本（1M tokens），处理大量内容速度适中\n` +
                                `• DeepSeek R1: 推理质量好，但超过4000 tokens会很慢\n\n` +
                                `是否切换到QwenLong-32B模型？`
                            );
                            
                            if (shouldSwitch) {
                                this.model.selected = 'qwenlong-l1-32b-mlx';
                            }
                        }
                        return;
                    }
                    
                    // 如果使用QwenLong且内容超过其限制，建议切换到Qwen2.5
                    if (estimatedTokens > qwenlongTokenLimit * 0.8 && this.model.selected.includes('qwenlong-l1-32b-mlx')) {
                        const shouldSwitch = confirm(
                            `⚠️ 模型容量提醒\n\n` +
                            `您${reason}，当前内容较长（约${Math.round(totalLength/1000)}k字符，估算${estimatedTokens} tokens）。\n\n` +
                            `QwenLong-32B模型可能接近上下文长度限制（61K tokens），建议切换到Qwen2.5-14B模型：\n\n` +
                            `• Qwen2.5-14B: 支持长文本（1M tokens），处理大量内容更稳定\n` +
                            `• QwenLong-32B: 推理能力强，但上下文有限（61K tokens）\n\n` +
                            `是否切换到Qwen2.5-14B模型？`
                        );
                        
                        if (shouldSwitch) {
                            this.model.selected = 'qwen2.5-14b-instruct-1m';
                        }
                        return;
                    }
                    
                    // 如果使用DeepSeek且内容超过8000 tokens，建议切换到Qwen2.5
                    if (estimatedTokens > deepseekTokenLimit * 0.8 && this.model.selected.includes('deepseek')) {
                        const shouldSwitch = confirm(
                            `⚠️ 速度提醒\n\n` +
                            `您${reason}，当前内容较长（约${Math.round(totalLength/1000)}k字符，估算${estimatedTokens} tokens）。\n\n` +
                            `使用DeepSeek R1处理超过8000 tokens的内容会很慢，建议切换到Qwen2.5-14B模型：\n\n` +
                            `• Qwen2.5-14B: 支持长文本（1M tokens），处理大量内容速度更快\n` +
                            `• DeepSeek R1: 推理质量好，但长文本处理较慢\n\n` +
                            `是否切换到Qwen2.5-14B模型？`
                        );
                        
                        if (shouldSwitch) {
                            this.model.selected = 'qwen2.5-14b-instruct-1m';
                        }
                        return;
                    }
                    
                    // 内容适中，使用DeepSeek时的速度提示
                    if (this.model.selected === 'deepseek-r1-0528') {
                        const shouldSwitch = confirm(
                            `提示：您${reason}，为了获得更快的响应速度，建议使用"Qwen3-30b (极速)"模型。\n\n` +
                            `当前选择的是"DeepSeek R1 (质量)"模型，处理大量文本时可能需要较长时间。\n\n` +
                            `内容长度：约${Math.round(totalLength/1000)}k字符，估算${estimatedTokens} tokens\n\n` +
                            `是否切换到极速模型？`
                        );
                        
                        if (shouldSwitch) {
                            this.model.selected = 'qwen3-30b-a3b-mlx';
                        }
                    }
                },

                // 获取推荐问题 - 初始状态下直接使用默认问题
                async getSuggestedQuestions() {
                    if (this.loadingSuggestions) return;
                    
                    // 如果还没有对话记录，直接使用默认推荐问题
                    if (this.conversation.length === 0) {
                        console.log('初始状态，使用默认推荐问题');
                        this.useDefaultSuggestions();
                        return;
                    }
                    
                    // 如果已有对话，基于最近的对话内容生成推荐问题
                    this.loadingSuggestions = true;
                    try {
                        // 获取最近的AI回答和用户问题
                        const lastAiMessage = this.conversation.slice().reverse().find(msg => msg.role === 'assistant');
                        const lastUserMessage = this.conversation.slice().reverse().find(msg => msg.role === 'user');
                        
                        if (lastAiMessage && lastUserMessage) {
                            // 使用最近的对话内容生成推荐问题
                            await this.generateSuggestedQuestionsFromConversation(lastAiMessage.content, lastUserMessage.content);
                        } else {
                            // 如果找不到合适的对话内容，使用默认问题
                            this.useDefaultSuggestions();
                        }
                    } catch (error) {
                        console.error('获取推荐问题失败:', error);
                        // 如果请求失败，使用默认的推荐问题
                        this.useDefaultSuggestions();
                    } finally {
                        this.loadingSuggestions = false;
                    }
                },

                // 使用默认推荐问题（作为后备方案）
                useDefaultSuggestions() {
                    const isEmbedded = @embedded.ToString().ToLower();
                    
                    let defaultQuestions;
                    if (isEmbedded) {
                        // 风险投资基金管理系统相关的推荐问题
                        defaultQuestions = [
                            "帮我分析一下基金当前的整体投资组合表现如何？",
                            "哪些被投企业的估值出现了显著变化？",
                            "有哪些项目已经到了退出时机，建议采取什么策略？",
                            "基金的行业配置是否需要调整，风险是否过于集中？",
                            "哪些被投企业面临融资需求，我们是否跟投？",
                            "分析一下基金的投资节奏和退出规划是否合理",
                            "请帮我初审这份投资协议的法律风险点",
                            "这份LP协议中的关键条款是否符合监管要求？",
                            "分析一下这个对赌条款的法律效力如何？",
                            "这份股权转让协议存在哪些潜在的法律风险？"
                        ];
                    } else {
                        // 投资系统相关的推荐问题
                        defaultQuestions = [
                            "帮我分析一下最近的投资项目有哪些亮点？",
                            "请总结一下本周的项目进展情况",
                            "有哪些项目需要重点关注风险问题？",
                            "帮我整理一下待处理的投资决策事项",
                            "分析一下当前项目组合的行业分布情况",
                            "请帮我初审这份投资协议的法律风险点",
                            "这份合同中的关键条款是否符合监管要求？",
                            "分析一下这个对赌条款的法律效力如何？",
                            "这份股权转让协议存在哪些潜在的法律风险？",
                            "帮我检查一下这份保密协议是否完整"
                        ];
                    }
                    
                    // 随机选择4-6个问题
                    const shuffled = defaultQuestions.sort(() => 0.5 - Math.random());
                    this.suggestedQuestions = shuffled.slice(0, Math.floor(Math.random() * 3) + 4);
                },

                // 刷新推荐问题
                async refreshSuggestions() {
                    await this.getSuggestedQuestions();
                },

                // 基于对话内容生成推荐问题（用于换一批功能）
                async generateSuggestedQuestionsFromConversation(aiContent, userContent) {
                    try {
                        console.log('基于对话内容生成推荐问题');
                        
                        // 获取用户的原始问题（去除项目和文件信息，只保留用户问题部分）
                        const userQuestionMatch = userContent.match(/用户问题:\s*(.+)$/);
                        const userQuestion = userQuestionMatch ? userQuestionMatch[1] : userContent;
                        
                        // 去掉<think></think>部分的内容
                        let cleanedAiResponse = aiContent;
                        const thinkStart = aiContent.indexOf('<think>');
                        const thinkEnd = aiContent.indexOf('</think>');
                        if (thinkStart !== -1 && thinkEnd !== -1 && thinkEnd > thinkStart) {
                            cleanedAiResponse = aiContent.substring(0, thinkStart) + aiContent.substring(thinkEnd + 8);
                        }
                        
                        // 移除所有HTML标签，避免ASP.NET请求验证错误
                        cleanedAiResponse = cleanedAiResponse.replace(/<[^>]*>/g, '');
                        
                        // 清理多余的空白字符
                        cleanedAiResponse = cleanedAiResponse.replace(/\s+/g, ' ').trim();
                        
                        // 限制AI回答内容的长度，避免URL过长
                        const limitedAiResponse = cleanedAiResponse.length > 2000 ? cleanedAiResponse.substring(0, 2000) + '...' : cleanedAiResponse;
                        
                        console.log('换一批推荐问题 - 发送请求参数:', {
                            aiResponseLength: limitedAiResponse.length,
                            userQuestionLength: userQuestion.length,
                            aiResponsePreview: limitedAiResponse.substring(0, 100)
                        });
                        
                        const response = await fetch('/adminapi/GenerateSuggestedQuestionsFromResponse', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: `aiResponse=${encodeURIComponent(limitedAiResponse)}&userQuestion=${encodeURIComponent(userQuestion)}&model=qwen3-30b-a3b-mlx&count=6`
                        });
                        
                        console.log('换一批推荐问题 - API响应状态:', response.status, response.statusText);
                        
                        if (response.ok) {
                            const responseText = await response.text();
                            console.log('换一批推荐问题 - API响应文本长度:', responseText ? responseText.length : 0);
                            
                            if (!responseText || responseText.trim() === '') {
                                console.error('换一批推荐问题 - API返回空响应');
                                this.useFollowUpQuestions();
                                return;
                            }
                            
                            let result;
                            try {
                                result = JSON.parse(responseText);
                            } catch (parseError) {
                                console.error('换一批推荐问题 - JSON解析失败:', parseError);
                                this.useFollowUpQuestions();
                                return;
                            }
                            
                            if (result.code === 0 && result.data && result.data.length > 0) {
                                this.suggestedQuestions = result.data;
                                console.log('换一批推荐问题 - 成功生成新的推荐问题:', result.data.length, '个');
                            } else {
                                console.error('换一批推荐问题 - 生成失败:', result.msg || '未知错误');
                                this.useFollowUpQuestions();
                            }
                        } else {
                            console.error('换一批推荐问题 - API请求失败:', response.status, response.statusText);
                            this.useFollowUpQuestions();
                        }
                    } catch (error) {
                        console.error('换一批推荐问题时出错:', error);
                        this.useFollowUpQuestions();
                    }
                },

                // 根据AI回答内容生成推荐问题
                async generateSuggestedQuestionsFromResponse(aiResponse, userMsgIndex) {
                    try {
                        console.log('AI回答完成，准备生成新的推荐问题');
                        
                        // 获取用户的原始问题（去除项目和文件信息，只保留用户问题部分）
                        const userFullContent = userMsgIndex >= 0 ? this.conversation[userMsgIndex].content : '';
                        const userQuestionMatch = userFullContent.match(/用户问题:\s*(.+)$/);
                        const userQuestion = userQuestionMatch ? userQuestionMatch[1] : userFullContent;
                        
                        // 去掉<think></think>部分的内容
                        let cleanedAiResponse = aiResponse;
                        const thinkStart = aiResponse.indexOf('<think>');
                        const thinkEnd = aiResponse.indexOf('</think>');
                        if (thinkStart !== -1 && thinkEnd !== -1 && thinkEnd > thinkStart) {
                            cleanedAiResponse = aiResponse.substring(0, thinkStart) + aiResponse.substring(thinkEnd + 8);
                        }
                        
                        // 移除所有HTML标签，避免ASP.NET请求验证错误
                        cleanedAiResponse = cleanedAiResponse.replace(/<[^>]*>/g, '');
                        
                        // 清理多余的空白字符
                        cleanedAiResponse = cleanedAiResponse.replace(/\s+/g, ' ').trim();
                        
                        // 限制AI回答内容的长度，避免URL过长
                        const limitedAiResponse = cleanedAiResponse.length > 2000 ? cleanedAiResponse.substring(0, 2000) + '...' : cleanedAiResponse;
                        
                        console.log('发送请求参数:', {
                            aiResponseLength: limitedAiResponse.length,
                            userQuestionLength: userQuestion.length,
                            aiResponsePreview: limitedAiResponse.substring(0, 100)
                        });
                        
                        const response = await fetch('/adminapi/GenerateSuggestedQuestionsFromResponse', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: `aiResponse=${encodeURIComponent(limitedAiResponse)}&userQuestion=${encodeURIComponent(userQuestion)}&model=qwen3-30b-a3b-mlx&count=6`
                        });
                        
                        console.log('API响应状态:', response.status, response.statusText);
                        
                        if (response.ok) {
                            // 先获取响应文本，然后再尝试解析JSON
                            const responseText = await response.text();
                            console.log('API响应文本长度:', responseText ? responseText.length : 0);
                            console.log('API响应内容预览:', responseText ? responseText.substring(0, 200) : 'empty');
                            
                            if (!responseText || responseText.trim() === '') {
                                console.error('API返回空响应');
                                // 使用默认的后续问题
                                this.useFollowUpQuestions();
                                return;
                            }
                            
                            let result;
                            try {
                                result = JSON.parse(responseText);
                            } catch (parseError) {
                                console.error('JSON解析失败:', parseError);
                                console.error('响应内容:', responseText.substring(0, 500));
                                // 使用默认的后续问题
                                this.useFollowUpQuestions();
                                return;
                            }
                            
                            if (result.code === 0 && result.data && result.data.length > 0) {
                                this.suggestedQuestions = result.data;
                                console.log('成功根据AI回答生成新的推荐问题:', result.data.length, '个');
                            } else {
                                console.error('生成推荐问题失败:', result.msg || '未知错误');
                                // 使用默认的后续问题
                                this.useFollowUpQuestions();
                            }
                        } else {
                            console.error('API请求失败:', response.status, response.statusText);
                            // 使用默认的后续问题
                            this.useFollowUpQuestions();
                        }
                    } catch (error) {
                        console.error('生成推荐问题时出错:', error);
                        // 使用默认的后续问题
                        this.useFollowUpQuestions();
                    }
                },

                // 使用默认的后续问题
                useFollowUpQuestions() {
                    const isEmbedded = @embedded.ToString().ToLower();
                    
                    let followUpQuestions;
                    if (isEmbedded) {
                        // 风险投资基金管理相关的后续问题
                        followUpQuestions = [
                            "这个分析对基金的投资策略有什么影响？",
                            "需要调整投资组合配置来优化风险收益比吗？",
                            "能否进一步分析被投企业的运营指标变化？",
                            "类似基金在这种情况下通常采取什么策略？",
                            "这个法律风险对基金的投资决策有什么影响？",
                            "需要请外部律师进一步审查这个合同吗？",
                            "这个条款是否符合最新的监管政策要求？"
                        ];
                    } else {
                        // 投资系统相关的后续问题
                        followUpQuestions = [
                            "还有其他相关的细节需要了解吗？",
                            "这个情况下有什么需要特别注意的风险吗？",
                            "能否提供更多关于这个话题的数据分析？",
                            "基于这个分析，下一步应该如何行动？",
                            "类似的情况在历史上是如何处理的？",
                            "这个结论对其他项目有什么启示？",
                            "这个法律风险对投资决策有什么影响？",
                            "需要请外部律师进一步审查这个合同吗？",
                            "这个条款是否符合最新的监管政策要求？",
                            "能否提供类似案例的法律分析？"
                        ];
                    }
                    
                    // 随机选择4-5个问题
                    const shuffled = followUpQuestions.sort(() => 0.5 - Math.random());
                    this.suggestedQuestions = shuffled.slice(0, Math.floor(Math.random() * 2) + 4);
                    console.log('使用默认后续问题:', this.suggestedQuestions.length, '个');
                },

                // 发送推荐问题
                async sendSuggestedQuestion(question) {
                    // 添加调试信息
                    console.log('sendSuggestedQuestion - isEmbedded值:', this.isEmbedded, '类型:', typeof this.isEmbedded);

                    if (this.conversation.length === 0) {
                        // 仅非embedded模式下自动开启MCP
                        if (!this.isEmbedded) {
                            this.mcpSelected = 'IMS';
                            console.log('首次对话点击推荐问题，自动开启MCP工具:', this.mcpSelected, 'isEmbedded:', this.isEmbedded);
                        } else {
                            this.mcpSelected = '';
                            console.log('embedded模式下不自动开启MCP');
                        }
                    } else {
                        // 后续推荐问题点击，不自动开启MCP
                        this.mcpSelected = '';
                        console.log('后续推荐问题点击，不自动开启MCP:', this.mcpSelected, 'isEmbedded:', this.isEmbedded);
                    }

                    // 将问题设置到输入框
                    this.currentMessage = question;

                    // 稍微延迟一下，让用户看到问题被填入了输入框
                    await new Promise(resolve => setTimeout(resolve, 100));

                    // 发送消息
                    await this.sendMessage();
                },

                // 处理点击外部关闭项目选择器
                handleClickOutside(event) {
                    if (this.showProjectSelector && this.$refs.projectSelector) {
                        // 检查点击的目标是否在项目选择器内部
                        if (!this.$refs.projectSelector.contains(event.target)) {
                            // 点击在外部，关闭选择器
                            this.showProjectSelector = false;
                        }
                    }
                },

                // 绑定全局点击事件监听器
                bindClickOutsideListener() {
                    document.addEventListener('click', this.handleClickOutside);
                },

                // 移除全局点击事件监听器  
                unbindClickOutsideListener() {
                    document.removeEventListener('click', this.handleClickOutside);
                },

                // 切换MCP选择器显示状态
                toggleMcpSelector() {
                    if (this.mcpSelected === '') {
                        // 如果当前未选择，根据embedded状态默认选择对应的MCP工具
                        this.mcpSelected = this.isEmbedded === true ? 'FMS' : 'IMS';
                    } else {
                        // 如果已选择，则关闭
                        this.mcpSelected = '';
                    }
                },

                // 清理MCP结果用于发送给AI，移除项目ID等仅用于显示的信息
                cleanMcpResultsForAI(mcpResults) {
                    try {
                        console.log('开始清理MCP结果，移除仅用于显示的信息');
                        
                        // 移除所有项目ID相关信息
                        let cleanedResults = mcpResults;
                        
                        // 移除各种格式的项目ID
                        cleanedResults = cleanedResults.replace(/(?:项目)?I[Dd][:：]\s*\d+/g, '');
                        
                        // 移除可能的其他ID格式
                        cleanedResults = cleanedResults.replace(/\bID[:：]\s*\d+/gi, '');
                        cleanedResults = cleanedResults.replace(/\b编号[:：]\s*\d+/g, '');
                        cleanedResults = cleanedResults.replace(/\b序号[:：]\s*\d+/g, '');
                        
                        // 移除多余的空行和空白字符
                        cleanedResults = cleanedResults.replace(/\n\s*\n/g, '\n');
                        cleanedResults = cleanedResults.replace(/^\s+|\s+$/g, '');
                        
                        // 移除每行开头和结尾的空白字符
                        cleanedResults = cleanedResults.split('\n')
                            .map(line => line.trim())
                            .filter(line => line.length > 0)
                            .join('\n');
                        
                        console.log('MCP结果清理完成:', {
                            原始长度: mcpResults.length,
                            清理后长度: cleanedResults.length,
                            减少字符数: mcpResults.length - cleanedResults.length
                        });
                        
                        return cleanedResults;
                        
                    } catch (error) {
                        console.error('清理MCP结果时出错:', error);
                        // 如果清理出错，返回原始结果（但这不应该发生）
                        return mcpResults;
                    }
                }

 
            },
           
            mounted() {
                var that = this;
                
                // 添加调试信息
                console.log('Vue mounted - isEmbedded值:', this.isEmbedded, '类型:', typeof this.isEmbedded);
                
                // 确保Vue完全初始化后再显示页面
                that.$nextTick(() => {
                    $("#page-app").show();
                    
                    // 在$nextTick中调用adjustInputHeight，确保DOM已渲染
                    if (that.$refs.messageInput) {
                        that.adjustInputHeight();
                    }
                });
                
                // 绑定resize事件监听器
                that.handleResize = () => {
                    if (that.$refs.messageInput) {
                        that.adjustInputHeight();
                    }
                };
                window.addEventListener('resize', that.handleResize);
                
                // 绑定keydown事件监听器
                $('#input-textarea').on('keydown', function (e) {
                    if (event.keyCode === 13) {
                        if (e.shiftKey) {
                            document.execCommand('insertLineBreak');
                            e.preventDefault();
                            return 
                        }
                        e.preventDefault(); // 阻止默认的回车行为
                        that.sendMessage();
                    }
                });
                
                // 初始绑定滚动监听器（如果容器存在）
                this.bindScrollListener();
                
                // 绑定点击外部关闭项目选择器的监听器
                this.bindClickOutsideListener();
                
                // 初始化推荐问题
                this.getSuggestedQuestions();
            },
            beforeDestroy() {
                const container = this.$refs.chatContainer;
                if (container) {
                    container.removeEventListener('scroll', this.handleScroll);
                }
                // 移除resize事件监听器
                if (this.handleResize) {
                    window.removeEventListener('resize', this.handleResize);
                }
                
                // 移除点击外部监听器
                this.unbindClickOutsideListener();
                
                // 清理搜索定时器
                if (this.searchTimeout) {
                    clearTimeout(this.searchTimeout);
                }
            },
            created: function () {
                // 移除立即显示，改在mounted中的$nextTick中显示
            },
            watch: {
                conversation: {
                    handler: function(newVal) {
                        if (newVal.length > 0) {
                            // 当有对话内容时，确保滚动监听器已绑定
                            this.bindScrollListener();
                        }
                    },
                    deep: true
                },
                showProjectSelector: {
                    handler: function(newVal) {
                        if (newVal) {
                            // 显示时初始化搜索（显示最近项目）
                            this.initializeSearch();
                        } else {
                            // 隐藏时清空搜索结果、搜索框和状态
                            this.searchResults = [];
                            this.projectSearch = '';
                            this.searchStatus = '';
                            // 清除搜索定时器
                            if (this.searchTimeout) {
                                clearTimeout(this.searchTimeout);
                                this.searchTimeout = null;
                            }
                        }
                    }
                }
            }
        });
    </script>
}
