using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using Newtonsoft.Json;
using Banyan.Apps.Configs;

namespace Banyan.Apps
{
    /// <summary>
    /// 新闻向量搜索服务类
    /// 提供基于向量相似度的新闻搜索功能
    /// </summary>
    public class NewsVectorSearch
    {
        private readonly ICache _cache;
        private readonly NewsVectorizationService _vectorizationService;
        private readonly VectorService _vectorService;
        private readonly NewsPrecomputeService _precomputeService;

        // 配置常量
        private const double DEFAULT_SIMILARITY_THRESHOLD = 0.5;
        private const int DEFAULT_SEARCH_LIMIT = 10;
        private const int CACHE_DURATION_HOURS = 72;

        /// <summary>
        /// 构造函数
        /// </summary>
        public NewsVectorSearch()
        {
            _cache = CacheFactory.Cache();
            _vectorizationService = new NewsVectorizationService();
            _vectorService = new VectorService();
            _precomputeService = NewsPrecomputeService.Instance;
        }

        /// <summary>
        /// 批量获取新闻向量
        /// </summary>
        /// <param name="newsIds">新闻ID列表</param>
        /// <returns>新闻ID到向量的映射</returns>
        public async Task<Dictionary<int, double[]>> GetNewsVectorsBatchAsync(List<int> newsIds)
        {
            if (newsIds == null || newsIds.Count == 0)
            {
                Logger.Warn("批量获取新闻向量失败：新闻ID列表为空");
                return new Dictionary<int, double[]>();
            }

            try
            {
                Logger.Info($"开始批量获取新闻向量，数量: {newsIds.Count}");

                // 使用NewsVectorizationService的批量获取方法
                var newsBLL = new NewsBLL();
                var result = await _vectorizationService.GetNewsVectorsBatchAsync(newsIds, newsBLL);

                Logger.Info($"批量获取新闻向量完成，成功获取: {result.Count}/{newsIds.Count}");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"批量获取新闻向量失败: {ex.Message}", ex);
                return new Dictionary<int, double[]>();
            }
        }

        /// <summary>
        /// 根据向量搜索相似新闻
        /// </summary>
        /// <param name="queryVector">查询向量</param>
        /// <param name="limit">返回结果数量限制</param>
        /// <param name="threshold">相似度阈值（0-1之间）</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>相似新闻列表</returns>
        public async Task<List<NewsVectorSimilarity>> SearchSimilarNews(
            double[] queryVector,
            int limit = DEFAULT_SEARCH_LIMIT,
            double threshold = DEFAULT_SIMILARITY_THRESHOLD,
            NewsSearchFilters filters = null)
        {
            if (queryVector == null || queryVector.Length == 0)
            {
                Logger.Error("搜索相似新闻失败：查询向量为空");
                return new List<NewsVectorSimilarity>();
            }

            // 添加向量质量诊断
            var vectorQuality = DiagnoseVectorQuality(queryVector);
            Logger.Info($"查询向量质量诊断: {vectorQuality}");

            try
            {
                Logger.Info($"开始搜索相似新闻，限制数量: {limit}，相似度阈值: {threshold}");
                
                // 构建缓存键
                string cacheKey = BuildSearchCacheKey(queryVector, limit, threshold, filters);
                
                // 尝试从缓存获取结果
                var cachedResult = _cache.GetCache<List<NewsVectorSimilarity>>(cacheKey);
                if (cachedResult != null && cachedResult.Count > 0)
                {
                    Logger.Info($"从缓存获取相似新闻搜索结果，数量: {cachedResult.Count}");
                    return cachedResult;
                }
                
                // 获取所有已向量化的新闻ID - 优化查询，只获取最近30天的新闻以提高性能
                var newsBLL = new NewsBLL();
                var where = "VectorStatus = 1"; // 只获取成功向量化的新闻
                
                // 默认添加时间限制，只查询最近30天的新闻
                var thirtyDaysAgo = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd");
                where += $" AND PubTime >= '{thirtyDaysAgo}'";
                
                // 应用过滤条件
                if (filters != null)
                {
                    where = ApplyFilters(where, filters);
                }
                
                // 添加索引提示以优化查询
                where = "/*+ INDEX(News IX_News_VectorStatus_PubTime) */ " + where;
                
                // 优化：只获取必要的字段，减少数据传输量
                string fields = "Id";
                
                // 优化：使用分页查询，避免一次性获取过多数据
                const int pageSize = 200;
                int pageIndex = 1;
                var allNewsIds = new List<int>();
                
                // 分页获取新闻ID
                while (true)
                {
                    var pageNewsIds = newsBLL.GetList(where, pageSize, pageIndex, fields, "PubTime DESC, Id DESC")
                        .Select(n => n.Id)
                        .ToList();
                    
                    if (pageNewsIds.Count == 0)
                    {
                        break;
                    }
                    
                    allNewsIds.AddRange(pageNewsIds);
                    
                    // 如果获取的数量小于页大小，说明已经获取完所有数据
                    if (pageNewsIds.Count < pageSize || allNewsIds.Count >= 500)
                    {
                        break;
                    }
                    
                    pageIndex++;
                }

                // 限制最大数量为500
                var newsIds = allNewsIds;//.Take(500).ToList();
                
                if (newsIds.Count == 0)
                {
                    Logger.Warn("没有找到已向量化的新闻");
                    return new List<NewsVectorSimilarity>();
                }
                
                Logger.Info($"找到 {newsIds.Count} 篇已向量化的新闻");
                
                // 批量获取新闻向量
                var newsVectors = await _vectorizationService.GetNewsVectorsBatchAsync(newsIds, newsBLL);
                if (newsVectors.Count == 0)
                {
                    Logger.Warn("没有找到有效的新闻向量");
                    return new List<NewsVectorSimilarity>();
                }
                
                // 预计算查询向量的长度，避免重复计算
                double queryVectorMagnitude = CalculateVectorMagnitude(queryVector);
                
                // 使用并行计算提高相似度计算性能
                var similarities = new List<NewsVectorSimilarity>(newsVectors.Count);
                var similaritiesLock = new object();
                var allSimilarities = new List<double>(); // 记录所有相似度用于统计
                var allSimilaritiesLock = new object();

                // 创建并行选项，限制并行度以避免过度消耗CPU资源
                var parallelOptions = new ParallelOptions {
                    MaxDegreeOfParallelism = Math.Min(Environment.ProcessorCount, 4)
                };

                Parallel.ForEach(newsVectors, parallelOptions, entry => {
                    int newsId = entry.Key;
                    double[] vector = entry.Value;

                    // 使用预计算的查询向量长度进行优化的相似度计算
                    double dotProduct = 0;
                    double magnitude2 = 0;

                    for (int i = 0; i < vector.Length; i++)
                    {
                        dotProduct += queryVector[i] * vector[i];
                        magnitude2 += vector[i] * vector[i];
                    }

                    magnitude2 = Math.Sqrt(magnitude2);

                    double similarity = 0;
                    if (queryVectorMagnitude > 0 && magnitude2 > 0)
                    {
                        similarity = dotProduct / (queryVectorMagnitude * magnitude2);
                    }

                    // 记录所有相似度用于统计
                    lock (allSimilaritiesLock)
                    {
                        allSimilarities.Add(similarity);
                    }

                    // 只添加超过阈值的结果
                    if (similarity >= threshold)
                    {
                        lock (similaritiesLock)
                        {
                            similarities.Add(new NewsVectorSimilarity
                            {
                                NewsId = newsId,
                                Similarity = similarity
                            });
                        }
                    }
                });

                // 记录相似度统计信息
                if (allSimilarities.Count > 0)
                {
                    var maxSim = allSimilarities.Max();
                    var minSim = allSimilarities.Min();
                    var avgSim = allSimilarities.Average();
                    var aboveThreshold = allSimilarities.Count(s => s >= threshold);
                    Logger.Info($"相似度统计: 最大={maxSim:F4}, 最小={minSim:F4}, 平均={avgSim:F4}, " +
                               $"超过阈值({threshold})的数量={aboveThreshold}/{allSimilarities.Count}");
                }
                
                // 按相似度降序排序并限制数量
                var result = similarities
                    .OrderByDescending(s => s.Similarity)
                    .Take(limit)
                    .ToList();
                
                // 填充新闻详情
                await FillNewsDetails(result, newsBLL);
                
                Logger.Info($"相似新闻搜索完成，结果数量: {result.Count}");
                
                // 缓存结果
                if (result.Count > 0)
                {
                    _cache.WriteCache(result, cacheKey, DateTime.Now.AddHours(CACHE_DURATION_HOURS));
                }
                
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error("搜索相似新闻失败", ex);
                return new List<NewsVectorSimilarity>();
            }
        }

        /// <summary>
        /// 根据文本搜索相似新闻
        /// </summary>
        /// <param name="queryText">查询文本</param>
        /// <param name="limit">返回结果数量限制</param>
        /// <param name="threshold">相似度阈值（0-1之间）</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>相似新闻列表</returns>
        public async Task<List<NewsVectorSimilarity>> SearchSimilarNewsByText(
            string queryText, 
            int limit = DEFAULT_SEARCH_LIMIT, 
            double threshold = DEFAULT_SIMILARITY_THRESHOLD,
            NewsSearchFilters filters = null)
        {
            if (string.IsNullOrEmpty(queryText))
            {
                Logger.Error("搜索相似新闻失败：查询文本为空");
                return new List<NewsVectorSimilarity>();
            }

            try
            {
                Logger.Info($"开始根据文本搜索相似新闻，文本: {queryText}，限制数量: {limit}，相似度阈值: {threshold}");
                
                // 构建缓存键
                string cacheKey = $"news_search_text:{queryText.GetHashCode()}:{limit}:{threshold}:{filters?.GetHashCode() ?? 0}";
                
                // 尝试从缓存获取结果
                var cachedResult = _cache.GetCache<List<NewsVectorSimilarity>>(cacheKey);
                if (cachedResult != null && cachedResult.Count > 0)
                {
                    Logger.Info($"从缓存获取文本搜索结果，数量: {cachedResult.Count}");
                    return cachedResult;
                }
                
                // 将文本转换为向量
                var queryVector = await _vectorService.GetTextEmbeddingAsync(queryText);
                if (queryVector == null || queryVector.Length == 0)
                {
                    Logger.Error("文本转换为向量失败");
                    return new List<NewsVectorSimilarity>();
                }
                
                // 使用向量搜索
                var result = await SearchSimilarNews(queryVector, limit, threshold, filters);
                
                // 缓存结果
                if (result.Count > 0)
                {
                    _cache.WriteCache(result, cacheKey, DateTime.Now.AddHours(CACHE_DURATION_HOURS));
                }
                
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"根据文本搜索相似新闻失败，文本: {queryText}", ex);
                return new List<NewsVectorSimilarity>();
            }
        }

        /// <summary>
        /// 获取与特定新闻相似的新闻
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <param name="limit">返回结果数量限制</param>
        /// <param name="threshold">相似度阈值（0-1之间）</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>相似新闻列表</returns>
        public async Task<List<NewsVectorSimilarity>> GetSimilarNews(
            int newsId, 
            int limit = DEFAULT_SEARCH_LIMIT, 
            double threshold = DEFAULT_SIMILARITY_THRESHOLD,
            NewsSearchFilters filters = null)
        {
            if (newsId <= 0)
            {
                Logger.Error("获取相似新闻失败：新闻ID无效");
                return new List<NewsVectorSimilarity>();
            }

            try
            {
                Logger.Info($"开始获取与新闻ID {newsId} 相似的新闻，限制数量: {limit}，相似度阈值: {threshold}");
                
                // 构建缓存键
                string cacheKey = $"news_similar:{newsId}:{limit}:{threshold}:{filters?.GetHashCode() ?? 0}";

                // 尝试从缓存获取结果
                var cachedResult = _cache.GetCache<List<NewsVectorSimilarity>>(cacheKey);
                if (cachedResult != null && cachedResult.Count > 0)
                {
                    Logger.Info($"从缓存获取相似新闻，数量: {cachedResult.Count}");
                    return cachedResult;
                }
                
                // 尝试获取预计算的相似新闻
                var precomputedResult = _precomputeService.GetPrecomputedSimilarNews(newsId);
                if (precomputedResult != null && precomputedResult.Count > 0)
                {
                    Logger.Info($"从预计算缓存获取相似新闻，数量: {precomputedResult.Count}");
                    
                    // 应用过滤条件和限制
                    var filteredResult = ApplyFiltersToSimilarityResults(precomputedResult, filters)
                        .Where(s => s.Similarity >= threshold)
                        .Take(limit)
                        .ToList();
                    
                    if (filteredResult.Count > 0)
                    {
                        // 缓存过滤后的结果
                        _cache.WriteCache(filteredResult, cacheKey, DateTime.Now.AddHours(CACHE_DURATION_HOURS));
                        return filteredResult;
                    }
                }
                
                // 如果预计算结果不足，计算相似新闻
                var newsBLL = new NewsBLL();
                var newsVector = await _vectorizationService.GetNewsVectorAsync(newsId, newsBLL);
                if (newsVector == null || newsVector.Length == 0)
                {
                    Logger.Warn($"新闻ID {newsId} 没有有效的向量");
                    return new List<NewsVectorSimilarity>();
                }
                
                // 使用向量搜索，排除自身
                if (filters == null)
                {
                    filters = new NewsSearchFilters();
                }
                filters.ExcludeNewsIds = filters.ExcludeNewsIds ?? new List<int>();
                filters.ExcludeNewsIds.Add(newsId);
                
                var result = await SearchSimilarNews(newsVector, limit, threshold, filters);
                
                // 缓存结果
                if (result.Count > 0)
                {
                    _cache.WriteCache(result, cacheKey, DateTime.Now.AddHours(CACHE_DURATION_HOURS));
                }
                
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取与新闻ID {newsId} 相似的新闻失败", ex);
                return new List<NewsVectorSimilarity>();
            }
        }
        
        /// <summary>
        /// 对相似度结果应用过滤条件
        /// </summary>
        /// <param name="similarities">相似度结果列表</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>过滤后的结果</returns>
        private List<NewsVectorSimilarity> ApplyFiltersToSimilarityResults(List<NewsVectorSimilarity> similarities, NewsSearchFilters filters)
        {
            if (similarities == null || similarities.Count == 0 || filters == null)
            {
                return similarities;
            }
            
            var result = similarities;
            
            // 分类过滤
            if (!string.IsNullOrEmpty(filters.Category))
            {
                result = result.Where(s => s.News?.Classify == filters.Category).ToList();
            }
            
            // 来源过滤
            if (!string.IsNullOrEmpty(filters.Source))
            {
                result = result.Where(s => s.News?.Source == filters.Source).ToList();
            }
            
            // 日期范围过滤
            if (filters.StartDate.HasValue)
            {
                result = result.Where(s => s.News?.PubTime >= filters.StartDate.Value).ToList();
            }
            
            if (filters.EndDate.HasValue)
            {
                result = result.Where(s => s.News?.PubTime < filters.EndDate.Value.AddDays(1)).ToList();
            }
            
            // 标签过滤
            if (!string.IsNullOrEmpty(filters.Tag))
            {
                result = result.Where(s => s.News?.Tag != null && s.News.Tag.Contains(filters.Tag)).ToList();
            }
            
            // 排除特定新闻ID
            if (filters.ExcludeNewsIds != null && filters.ExcludeNewsIds.Count > 0)
            {
                result = result.Where(s => !filters.ExcludeNewsIds.Contains(s.NewsId)).ToList();
            }
            
            return result;
        }

        /// <summary>
        /// 计算余弦相似度
        /// </summary>
        /// <param name="vector1">向量1</param>
        /// <param name="vector2">向量2</param>
        /// <returns>相似度（0-1之间）</returns>
        private double CalculateCosineSimilarity(double[] vector1, double[] vector2)
        {
            if (vector1 == null || vector2 == null || vector1.Length != vector2.Length)
            {
                return 0;
            }

            double dotProduct = 0;
            double magnitude1 = 0;
            double magnitude2 = 0;

            // 使用SIMD优化的向量计算
            // 避免使用Math.Pow，直接平方更高效
            for (int i = 0; i < vector1.Length; i++)
            {
                dotProduct += vector1[i] * vector2[i];
                magnitude1 += vector1[i] * vector1[i];
                magnitude2 += vector2[i] * vector2[i];
            }

            magnitude1 = Math.Sqrt(magnitude1);
            magnitude2 = Math.Sqrt(magnitude2);

            if (magnitude1 == 0 || magnitude2 == 0)
            {
                return 0;
            }

            return dotProduct / (magnitude1 * magnitude2);
        }
        
        /// <summary>
        /// 预计算向量的长度（模）以提高相似度计算性能
        /// </summary>
        /// <param name="vector">输入向量</param>
        /// <returns>向量的长度</returns>
        private double CalculateVectorMagnitude(double[] vector)
        {
            if (vector == null || vector.Length == 0)
            {
                return 0;
            }
            
            double sumOfSquares = 0;
            for (int i = 0; i < vector.Length; i++)
            {
                sumOfSquares += vector[i] * vector[i];
            }
            
            return Math.Sqrt(sumOfSquares);
        }

        /// <summary>
        /// 填充新闻详情
        /// </summary>
        /// <param name="similarities">相似度结果列表</param>
        /// <param name="newsBLL">新闻业务逻辑对象</param>
        /// <returns>任务</returns>
        private async Task FillNewsDetails(List<NewsVectorSimilarity> similarities, NewsBLL newsBLL)
        {
            if (similarities == null || similarities.Count == 0)
            {
                return;
            }

            //await Task.Delay(1); // 避免编译器警告

            try
            {
                // 获取所有新闻ID
                var newsIds = similarities.Select(s => s.NewsId).ToList();
                
                // 构建SQL查询，批量获取新闻数据
                var idList = string.Join(",", newsIds);
                var fields = "Id, Title, Source, Classify, PubTime";
                var where = $"Id IN ({idList})";
                
                var newsList = newsBLL.GetList(where, newsIds.Count, 1, fields, "Id");
                
                // 创建ID到新闻的映射
                var newsMap = newsList.ToDictionary(n => n.Id);
                
                // 填充详情
                foreach (var similarity in similarities)
                {
                    if (newsMap.TryGetValue(similarity.NewsId, out var news))
                    {
                        similarity.News = news;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("填充新闻详情失败", ex);
            }
        }

        /// <summary>
        /// 填充新闻详情，包括标签分析信息
        /// </summary>
        /// <param name="similarities">相似度结果列表</param>
        /// <param name="newsBLL">新闻业务逻辑对象</param>
        private void FillNewsDetailsWithTags(List<NewsVectorSimilarity> similarities, NewsBLL newsBLL)
        {
            if (similarities == null || similarities.Count == 0)
            {
                return;
            }

            try
            {
                // 获取所有新闻ID
                var newsIds = similarities.Select(s => s.NewsId).ToList();

                // 构建SQL查询，批量获取新闻数据，包括标签分析信息
                var idList = string.Join(",", newsIds);
                var fields = "Id, Title, Source, Classify, PubTime, TagAnalysis, Subject";
                var where = $"Id IN ({idList})";

                var newsList = newsBLL.GetList(where, newsIds.Count, 1, fields, "Id");

                // 创建ID到新闻的映射
                var newsMap = newsList.ToDictionary(n => n.Id);

                // 填充详情
                foreach (var similarity in similarities)
                {
                    if (newsMap.TryGetValue(similarity.NewsId, out var news))
                    {
                        similarity.News = news;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("填充新闻详情（包含标签）失败", ex);
            }
        }

        /// <summary>
        /// 应用过滤条件
        /// </summary>
        /// <param name="where">原始条件</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>更新后的条件</returns>
        private string ApplyFilters(string where, NewsSearchFilters filters)
        {
            if (filters == null)
            {
                return where;
            }

            // 分类过滤
            if (!string.IsNullOrEmpty(filters.Category))
            {
                where += $" AND Classify = '{filters.Category}'";
            }

            // 来源过滤
            if (!string.IsNullOrEmpty(filters.Source))
            {
                where += $" AND Source = '{filters.Source}'";
            }

            // 日期范围过滤
            if (filters.StartDate.HasValue)
            {
                where += $" AND PubTime >= '{filters.StartDate.Value:yyyy-MM-dd}'";
            }

            if (filters.EndDate.HasValue)
            {
                where += $" AND PubTime < '{filters.EndDate.Value.AddDays(1):yyyy-MM-dd}'";
            }

            // 标签过滤
            if (!string.IsNullOrEmpty(filters.Tag))
            {
                where += $" AND Tag LIKE '%{filters.Tag}%'";
            }

            // 排除特定新闻ID
            if (filters.ExcludeNewsIds != null && filters.ExcludeNewsIds.Count > 0)
            {
                var excludeList = string.Join(",", filters.ExcludeNewsIds);
                where += $" AND Id NOT IN ({excludeList})";
            }

            return where;
        }

        /// <summary>
        /// 构建搜索缓存键
        /// </summary>
        /// <param name="queryVector">查询向量</param>
        /// <param name="limit">限制数量</param>
        /// <param name="threshold">相似度阈值</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>缓存键</returns>
        private string BuildSearchCacheKey(double[] queryVector, int limit, double threshold, NewsSearchFilters filters)
        {
            // 计算向量的哈希值
            int vectorHash = 0;
            foreach (var value in queryVector)
            {
                vectorHash = unchecked(vectorHash * 31 + value.GetHashCode());
            }

            return $"news_search_vector:{vectorHash}:{limit}:{threshold}:{filters?.GetHashCode() ?? 0}";
        }

        /// <summary>
        /// 诊断向量质量
        /// </summary>
        /// <param name="vector">向量</param>
        /// <returns>诊断信息</returns>
        private string DiagnoseVectorQuality(double[] vector)
        {
            if (vector == null || vector.Length == 0)
            {
                return "向量为空";
            }

            var magnitude = CalculateVectorMagnitude(vector);
            var nonZeroCount = vector.Count(v => Math.Abs(v) > 1e-10);
            var maxValue = vector.Max(Math.Abs);
            var avgValue = vector.Average(Math.Abs);

            var isAllZero = magnitude < 1e-10;
            var isNormalized = Math.Abs(magnitude - 1.0) < 0.1;
            var sparsity = (double)nonZeroCount / vector.Length;

            return $"维度:{vector.Length}, 模长:{magnitude:F4}, 非零元素:{nonZeroCount}({sparsity:P}), " +
                   $"最大值:{maxValue:F4}, 平均值:{avgValue:F4}, 全零:{isAllZero}, 已归一化:{isNormalized}";
        }

        /// <summary>
        /// 根据用户兴趣获取推荐新闻
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="limit">返回结果数量限制</param>
        /// <param name="threshold">相似度阈值</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>推荐新闻列表</returns>
        public async Task<List<NewsVectorSimilarity>> GetRecommendedNewsByInterest(
            int userId,
            int limit = DEFAULT_SEARCH_LIMIT,
            double threshold = DEFAULT_SIMILARITY_THRESHOLD,
            NewsSearchFilters filters = null)
        {
            try
            {
                Logger.Info($"开始为用户 {userId} 获取推荐新闻，限制数量: {limit}，相似度阈值: {threshold}");

                var result = new List<NewsVectorSimilarity>();

                // 首先尝试获取预计算的推荐结果
                var precomputedRecommendations = _precomputeService.GetPrecomputedUserRecommendations(userId);
                if (precomputedRecommendations != null && precomputedRecommendations.Count > 0)
                {
                    Logger.Info($"从预计算缓存获取用户 {userId} 的推荐结果，数量: {precomputedRecommendations.Count}");

                    // 应用过滤条件和阈值
                    result = ApplyFiltersToSimilarityResults(precomputedRecommendations, filters)
                        .Where(s => s.Similarity >= threshold)
                        .Take(limit)
                        .ToList();

                    if (result.Count > 0)
                    {
                        Logger.Info($"预计算结果过滤后返回 {result.Count} 条推荐新闻");

                        // 填充新闻详情，包括标签分析信息
                        var newsBLL = new NewsBLL();
                        FillNewsDetailsWithTags(result, newsBLL);

                        return result;
                    }
                }

                // 尝试获取用户兴趣向量
                var userInterestVector = await GetUserInterestVector(userId);
                if (userInterestVector != null && userInterestVector.Length > 0)
                {
                    // 使用用户兴趣向量搜索相似新闻
                    result = await SearchSimilarNews(userInterestVector, limit, threshold, filters);
                    Logger.Info($"基于用户兴趣向量找到 {result.Count} 条推荐新闻");
                }

                // Fallback 1: 如果基于兴趣向量的推荐结果不足，尝试基于用户阅读历史
                if (result.Count < limit)
                {
                    var historyBasedRecommendations = await GetRecommendationsByReadingHistory(userId, limit - result.Count, threshold, filters);
                    if (historyBasedRecommendations.Count > 0)
                    {
                        // 合并结果，避免重复
                        var existingIds = new HashSet<int>(result.Select(r => r.NewsId));
                        var newRecommendations = historyBasedRecommendations.Where(h => !existingIds.Contains(h.NewsId)).ToList();
                        result.AddRange(newRecommendations);
                        Logger.Info($"基于阅读历史补充了 {newRecommendations.Count} 条推荐新闻");
                    }
                }

                // Fallback 2: 如果结果仍然不足，使用热门新闻
                if (result.Count < limit)
                {
                    var popularNews = await GetPopularNewsRecommendations(userId, limit - result.Count, filters);
                    if (popularNews.Count > 0)
                    {
                        // 合并结果，避免重复
                        var existingIds = new HashSet<int>(result.Select(r => r.NewsId));
                        var newRecommendations = popularNews.Where(p => !existingIds.Contains(p.NewsId)).ToList();
                        result.AddRange(newRecommendations);
                        Logger.Info($"基于热门新闻补充了 {newRecommendations.Count} 条推荐新闻");
                    }
                }

                // 填充新闻详情，包括标签分析信息
                if (result.Count > 0)
                {
                    var newsBLL = new NewsBLL();
                    FillNewsDetailsWithTags(result, newsBLL);
                }

                Logger.Info($"用户 {userId} 最终获得 {result.Count} 条推荐新闻");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"为用户 {userId} 获取推荐新闻失败: {ex.Message}", ex);

                // 最后的fallback：返回最新的新闻
                try
                {
                    return await GetLatestNewsAsFallback(limit, filters);
                }
                catch (Exception fallbackEx)
                {
                    Logger.Error($"Fallback获取最新新闻也失败: {fallbackEx.Message}", fallbackEx);
                    return new List<NewsVectorSimilarity>();
                }
            }
        }

        /// <summary>
        /// 根据用户兴趣获取推荐新闻（分页版本）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="page">页码（从1开始）</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="threshold">相似度阈值</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>分页推荐新闻结果</returns>
        public async Task<PaginatedResponse<NewsVectorSimilarity>> GetRecommendedNewsByInterestPaginated(
            int userId,
            int page = 1,
            int pageSize = 20,
            double threshold = DEFAULT_SIMILARITY_THRESHOLD,
            NewsSearchFilters filters = null)
        {
            try
            {
                Logger.Info($"开始为用户 {userId} 获取分页推荐新闻，页码: {page}，每页大小: {pageSize}，相似度阈值: {threshold}");

                // 计算偏移量
                int offset = (page - 1) * pageSize;
                
                // 为了支持分页，我们需要获取更多的结果然后进行分页
                // 这里我们获取足够多的结果来支持分页
                int totalLimit = Math.Max(pageSize * page + 100, 500); // 获取足够的结果用于分页
                
                var allResults = new List<NewsVectorSimilarity>();

                // 尝试获取用户兴趣向量
                var userInterestVector = await GetUserInterestVector(userId);
                if (userInterestVector != null && userInterestVector.Length > 0)
                {
                    // 使用用户兴趣向量搜索相似新闻
                    allResults = await SearchSimilarNews(userInterestVector, totalLimit, threshold, filters);
                    Logger.Info($"基于用户兴趣向量找到 {allResults.Count} 条推荐新闻");
                }

                // Fallback 1: 如果基于兴趣向量的推荐结果不足，尝试基于用户阅读历史
                if (allResults.Count < totalLimit)
                {
                    var historyBasedRecommendations = await GetRecommendationsByReadingHistory(userId, totalLimit - allResults.Count, threshold, filters);
                    if (historyBasedRecommendations.Count > 0)
                    {
                        // 合并结果，避免重复
                        var existingIds = new HashSet<int>(allResults.Select(r => r.NewsId));
                        var newRecommendations = historyBasedRecommendations.Where(h => !existingIds.Contains(h.NewsId)).ToList();
                        allResults.AddRange(newRecommendations);
                        Logger.Info($"基于阅读历史补充了 {newRecommendations.Count} 条推荐新闻");
                    }
                }

                // Fallback 2: 如果结果仍然不足，使用热门新闻
                if (allResults.Count < totalLimit)
                {
                    var popularNews = await GetPopularNewsRecommendations(userId, totalLimit - allResults.Count, filters);
                    if (popularNews.Count > 0)
                    {
                        // 合并结果，避免重复
                        var existingIds = new HashSet<int>(allResults.Select(r => r.NewsId));
                        var newRecommendations = popularNews.Where(p => !existingIds.Contains(p.NewsId)).ToList();
                        allResults.AddRange(newRecommendations);
                        Logger.Info($"基于热门新闻补充了 {newRecommendations.Count} 条推荐新闻");
                    }
                }

                // 填充新闻详情，包括标签分析信息
                if (allResults.Count > 0)
                {
                    var newsBLL = new NewsBLL();
                    FillNewsDetailsWithTags(allResults, newsBLL);
                }

                // 计算分页信息
                int totalCount = allResults.Count;
                int totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
                bool hasNextPage = page < totalPages;

                // 获取当前页的数据
                var pageResults = allResults.Skip(offset).Take(pageSize).ToList();

                Logger.Info($"用户 {userId} 分页推荐完成，总数: {totalCount}，当前页: {page}，返回: {pageResults.Count} 条");

                return new PaginatedResponse<NewsVectorSimilarity>
                {
                    Data = pageResults,
                    TotalCount = totalCount,
                    CurrentPage = page,
                    PageSize = pageSize,
                    HasNextPage = hasNextPage,
                    TotalPages = totalPages
                };
            }
            catch (Exception ex)
            {
                Logger.Error($"为用户 {userId} 获取分页推荐新闻失败: {ex.Message}", ex);

                // 返回空的分页结果
                return new PaginatedResponse<NewsVectorSimilarity>
                {
                    Data = new List<NewsVectorSimilarity>(),
                    TotalCount = 0,
                    CurrentPage = page,
                    PageSize = pageSize,
                    HasNextPage = false,
                    TotalPages = 0
                };
            }
        }

        /// <summary>
        /// 记录用户阅读新闻行为
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="newsId">新闻ID</param>
        /// <returns>是否记录成功</returns>
        public async Task<bool> RecordUserReadNewsAsync(int userId, int newsId)
        {
            try
            {
                Logger.Info($"记录用户 {userId} 阅读新闻 {newsId}");

                if (userId <= 0 || newsId <= 0)
                {
                    Logger.Warn($"无效的参数：用户ID {userId}，新闻ID {newsId}");
                    return false;
                }

                // 1. 获取新闻信息
                var newsBLL = new NewsBLL();
                var news = newsBLL.GetModel(newsId);
                if (news == null)
                {
                    Logger.Warn($"新闻不存在，新闻ID: {newsId}");
                    return false;
                }

                // 2. 记录到缓存中，用于快速查询用户阅读历史
                var cacheKey = $"user_read_history:{userId}";
                var readHistory = _cache.GetCache<List<int>>(cacheKey) ?? new List<int>();

                // 避免重复记录
                if (!readHistory.Contains(newsId))
                {
                    readHistory.Add(newsId);

                    // 保持最近100条阅读记录
                    if (readHistory.Count > 100)
                    {
                        readHistory.RemoveAt(0);
                    }

                    // 缓存7天
                    _cache.WriteCache(readHistory, cacheKey, DateTime.Now.AddDays(7));
                    Logger.Info($"已更新用户 {userId} 的阅读历史缓存，当前记录数: {readHistory.Count}");
                }

                // 3. 使用现有的EngagementTracker记录用户行为
                try
                {
                    var engagementTracker = new EngagementTracker();
                    var memberBLL = new MemberBLL();
                    var user = memberBLL.GetModelByCache(userId);

                    if (user != null)
                    {
                        // 记录阅读行为（作为view事件）
                        await engagementTracker.TrackViewAsync(userId, user.RealName ?? user.OpenId, newsId, "NewsVectorSearch");
                        Logger.Info($"已通过EngagementTracker记录用户 {userId} 阅读新闻 {newsId}");
                    }
                }
                catch (Exception engagementEx)
                {
                    Logger.Warn($"记录用户行为到EngagementTracker失败: {engagementEx.Message}");
                    // 不影响主流程，继续执行
                }

                // 4. 异步更新用户兴趣画像
                try
                {
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await UpdateUserInterestBasedOnReadingAsync(userId, news);
                        }
                        catch (Exception updateEx)
                        {
                            Logger.Error($"异步更新用户兴趣画像失败: {updateEx.Message}", updateEx);
                        }
                    });
                }
                catch (Exception taskEx)
                {
                    Logger.Warn($"启动异步更新任务失败: {taskEx.Message}");
                }

                Logger.Info($"成功记录用户 {userId} 阅读新闻 {newsId}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"记录用户 {userId} 阅读新闻 {newsId} 失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取用户兴趣向量
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户兴趣向量</returns>
        private async Task<double[]> GetUserInterestVector(int userId)
        {
            try
            {
                Logger.Info($"开始获取用户 {userId} 的兴趣向量");

                if (userId <= 0)
                {
                    Logger.Warn($"无效的用户ID: {userId}");
                    return null;
                }

                // 1. 尝试从缓存获取用户兴趣向量
                var cacheKey = $"user_interest_vector:{userId}";
                var cachedVector = _cache.GetCache<double[]>(cacheKey);
                if (cachedVector != null && cachedVector.Length > 0)
                {
                    Logger.Info($"从缓存获取用户 {userId} 兴趣向量成功，维度: {cachedVector.Length}");
                    return cachedVector;
                }

                // 2. 使用UserProfileBLL获取用户兴趣向量
                var userProfileBLL = new UserProfileBLL();
                var userVector = await userProfileBLL.GetUserVectorFromDatabaseAsync(userId);

                if (userVector != null && userVector.Length > 0)
                {
                    Logger.Info($"通过UserProfileBLL获取用户 {userId} 兴趣向量成功，维度: {userVector.Length}");

                    // 缓存向量，有效期7天
                    _cache.WriteCache(userVector, cacheKey, DateTime.Now.AddDays(7));
                    return userVector;
                }

                // 3. Fallback: 基于用户标签关联生成向量
                var fallbackVector = await GenerateUserVectorFromTagRelations(userId);
                if (fallbackVector != null && fallbackVector.Length > 0)
                {
                    Logger.Info($"通过标签关联生成用户 {userId} 兴趣向量成功，维度: {fallbackVector.Length}");

                    // 缓存向量，有效期较短（1天）
                    _cache.WriteCache(fallbackVector, cacheKey, DateTime.Now.AddDays(1));
                    return fallbackVector;
                }

                // 4. 最后的Fallback: 基于用户阅读历史生成向量
                var historyVector = await GenerateUserVectorFromReadingHistory(userId);
                if (historyVector != null && historyVector.Length > 0)
                {
                    Logger.Info($"通过阅读历史生成用户 {userId} 兴趣向量成功，维度: {historyVector.Length}");

                    // 缓存向量，有效期很短（6小时）
                    _cache.WriteCache(historyVector, cacheKey, DateTime.Now.AddHours(6));
                    return historyVector;
                }

                Logger.Warn($"无法为用户 {userId} 生成兴趣向量");
                return null;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取用户 {userId} 兴趣向量失败: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 基于用户阅读历史获取推荐新闻
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="limit">返回结果数量限制</param>
        /// <param name="threshold">相似度阈值</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>推荐新闻列表</returns>
        private async Task<List<NewsVectorSimilarity>> GetRecommendationsByReadingHistory(
            int userId, int limit, double threshold, NewsSearchFilters filters)
        {
            try
            {
                Logger.Info($"基于用户 {userId} 阅读历史获取推荐新闻，限制数量: {limit}，相似度阈值: {threshold}");

                if (userId <= 0 || limit <= 0)
                {
                    Logger.Warn($"无效的参数：用户ID {userId}，限制数量 {limit}");
                    return new List<NewsVectorSimilarity>();
                }

                // 1. 从缓存获取用户阅读历史
                var cacheKey = $"user_read_history:{userId}";
                var readHistory = _cache.GetCache<List<int>>(cacheKey);

                if (readHistory == null || readHistory.Count == 0)
                {
                    Logger.Info($"用户 {userId} 没有阅读历史记录");
                    return new List<NewsVectorSimilarity>();
                }

                Logger.Info($"用户 {userId} 有 {readHistory.Count} 条阅读历史记录");

                // 2. 获取最近阅读的新闻（最多取20篇）
                var recentNewsIds = readHistory.Count > 20
                    ? readHistory.Skip(readHistory.Count - 20).ToList()
                    : readHistory;

                // 3. 基于阅读历史中的新闻找相似新闻
                var recommendations = new Dictionary<int, NewsVectorSimilarity>();
                var newsBLL = new NewsBLL();

                foreach (var readNewsId in recentNewsIds)
                {
                    try
                    {
                        // 为每篇已读新闻找相似新闻
                        var similarNews = await GetSimilarNews(readNewsId, limit * 2, threshold, filters);

                        foreach (var similar in similarNews)
                        {
                            // 避免推荐已读过的新闻
                            if (readHistory.Contains(similar.NewsId))
                            {
                                continue;
                            }

                            // 如果已经有这篇新闻，取更高的相似度
                            if (recommendations.ContainsKey(similar.NewsId))
                            {
                                if (similar.Similarity > recommendations[similar.NewsId].Similarity)
                                {
                                    recommendations[similar.NewsId] = similar;
                                }
                            }
                            else
                            {
                                recommendations[similar.NewsId] = similar;
                            }
                        }

                        // 如果已经收集到足够的推荐，可以提前结束
                        if (recommendations.Count >= limit * 3)
                        {
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Warn($"为已读新闻 {readNewsId} 查找相似新闻失败: {ex.Message}");
                    }
                }

                // 4. 按相似度排序并限制数量
                var result = recommendations.Values
                    .OrderByDescending(r => r.Similarity)
                    .Take(limit)
                    .ToList();

                // 5. 填充新闻详情
                if (result.Count > 0)
                {
                    await FillNewsDetails(result, newsBLL);
                }

                Logger.Info($"基于阅读历史为用户 {userId} 生成了 {result.Count} 条推荐新闻");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"基于阅读历史获取推荐新闻失败: {ex.Message}", ex);
                return new List<NewsVectorSimilarity>();
            }
        }

        /// <summary>
        /// 获取热门新闻作为推荐
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="limit">返回结果数量限制</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>推荐新闻列表</returns>
        private async Task<List<NewsVectorSimilarity>> GetPopularNewsRecommendations(
            int userId, int limit, NewsSearchFilters filters)
        {
            try
            {
                Logger.Info($"为用户 {userId} 获取热门新闻推荐");

                var newsBLL = new NewsBLL();

                // 构建查询条件
                var where = "VectorStatus = 1"; // 只获取已向量化的新闻

                // 应用过滤条件
                if (filters != null)
                {
                    if (!string.IsNullOrEmpty(filters.Category))
                    {
                        where += $" AND Classify = '{filters.Category}'";
                    }
                    if (!string.IsNullOrEmpty(filters.Source))
                    {
                        where += $" AND Source = '{filters.Source}'";
                    }
                    if (filters.StartDate.HasValue)
                    {
                        where += $" AND PubTime >= '{filters.StartDate.Value:yyyy-MM-dd}'";
                    }
                    if (filters.EndDate.HasValue)
                    {
                        where += $" AND PubTime < '{filters.EndDate.Value.AddDays(1):yyyy-MM-dd}'";
                    }
                }

                // 按发布时间和优先级排序获取热门新闻
                var orderBy = "PubTime DESC, priority DESC";
                var fields = "Id, Title, Content, Source, Classify, PubTime, Tag, Url, TagAnalysis";

                var popularNews = newsBLL.GetList(where, limit, 1, fields, orderBy);

                if (popularNews == null || popularNews.Count == 0)
                {
                    return new List<NewsVectorSimilarity>();
                }

                // 转换为NewsVectorSimilarity格式
                var result = popularNews.Select(news => new NewsVectorSimilarity
                {
                    NewsId = news.Id,
                    Similarity = 0.8, // 热门新闻给一个固定的相似度分数
                    News = news
                }).ToList();

                Logger.Info($"为用户 {userId} 获取了 {result.Count} 条热门新闻推荐");
                await Task.Delay(1); // 避免编译器警告
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取热门新闻推荐失败: {ex.Message}", ex);
                return new List<NewsVectorSimilarity>();
            }
        }

        /// <summary>
        /// 获取最新新闻作为最后的fallback
        /// </summary>
        /// <param name="limit">返回结果数量限制</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>推荐新闻列表</returns>
        private async Task<List<NewsVectorSimilarity>> GetLatestNewsAsFallback(int limit, NewsSearchFilters filters)
        {
            try
            {
                Logger.Info("获取最新新闻作为fallback推荐");

                var newsBLL = new NewsBLL();

                // 构建查询条件 - 获取最新的新闻
                var where = "id <> -1"; // 基本条件

                // 应用过滤条件
                if (filters != null)
                {
                    if (!string.IsNullOrEmpty(filters.Category))
                    {
                        where += $" AND Classify = '{filters.Category}'";
                    }
                    if (!string.IsNullOrEmpty(filters.Source))
                    {
                        where += $" AND Source = '{filters.Source}'";
                    }
                    if (filters.StartDate.HasValue)
                    {
                        where += $" AND PubTime >= '{filters.StartDate.Value:yyyy-MM-dd}'";
                    }
                    if (filters.EndDate.HasValue)
                    {
                        where += $" AND PubTime < '{filters.EndDate.Value.AddDays(1):yyyy-MM-dd}'";
                    }
                }

                // 按发布时间倒序获取最新新闻
                var orderBy = "PubTime DESC, CreateTime DESC";
                var fields = "Id, Title, Content, Source, Classify, PubTime, Tag, Url, TagAnalysis";

                var latestNews = newsBLL.GetList(where, limit, 1, fields, orderBy);

                if (latestNews == null || latestNews.Count == 0)
                {
                    return new List<NewsVectorSimilarity>();
                }

                // 转换为NewsVectorSimilarity格式
                var result = latestNews.Select(news => new NewsVectorSimilarity
                {
                    NewsId = news.Id,
                    //Similarity = 0.6, // 最新新闻给一个较低的相似度分数
                    News = news
                }).ToList();

                Logger.Info($"获取了 {result.Count} 条最新新闻作为fallback推荐");
                await Task.Delay(1); // 避免编译器警告
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取最新新闻fallback失败: {ex.Message}", ex);
                return new List<NewsVectorSimilarity>();
            }
        }

        /// <summary>
        /// 基于阅读行为更新用户兴趣画像
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="news">新闻对象</param>
        /// <returns>更新任务</returns>
        private async Task UpdateUserInterestBasedOnReadingAsync(int userId, News news)
        {
            try
            {
                Logger.Info($"开始基于阅读行为更新用户 {userId} 的兴趣画像");

                // 1. 提取新闻标签
                var tags = new List<string>();
                if (!string.IsNullOrEmpty(news.Tag))
                {
                    tags.AddRange(news.Tag.Split(',', '，', ';', '；').Select(t => t.Trim()).Where(t => !string.IsNullOrEmpty(t)));
                }

                // 2. 从新闻分类中提取标签
                if (!string.IsNullOrEmpty(news.Classify))
                {
                    tags.Add(news.Classify.Trim());
                }

                // 3. 从新闻来源中提取标签
                if (!string.IsNullOrEmpty(news.Source))
                {
                    tags.Add(news.Source.Trim());
                }

                // 4. 使用UserProfileBLL更新用户兴趣
                if (tags.Count > 0)
                {
                    var userProfileBLL = new UserProfileBLL();

                    // 获取新闻向量
                    double[] newsVector = null;
                    if (!string.IsNullOrEmpty(news.NewsVector))
                    {
                        try
                        {
                            newsVector = _vectorService.StringToVector(news.NewsVector);
                        }
                        catch (Exception vectorEx)
                        {
                            Logger.Warn($"解析新闻向量失败: {vectorEx.Message}");
                        }
                    }

                    // 更新用户兴趣画像
                    if (newsVector != null)
                    {
                        await userProfileBLL.UpdateUserInterestWithNewsAsync(userId, news.Id, newsVector, tags);
                    }
                    else
                    {
                        // 如果没有向量，仅更新标签权重
                        await UpdateUserTagWeightsFromReadingAsync(userId, tags);
                    }

                    Logger.Info($"成功更新用户 {userId} 的兴趣画像，标签数量: {tags.Count}");
                }
                else
                {
                    Logger.Info($"新闻 {news.Id} 没有可提取的标签，跳过兴趣画像更新");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"基于阅读行为更新用户兴趣画像失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 基于阅读行为更新用户标签权重
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="tags">标签列表</param>
        /// <returns>更新任务</returns>
        private async Task UpdateUserTagWeightsFromReadingAsync(int userId, List<string> tags)
        {
            try
            {
                await Task.Delay(1); // 避免编译器警告
                var userProfileBLL = new UserProfileBLL();

                foreach (var tagName in tags)
                {
                    if (string.IsNullOrEmpty(tagName)) continue;

                    // 创建或获取标签
                    var userInterestTagBLL = new UserInterestTagBLL();
                    var tag = userInterestTagBLL.GetTagByName(tagName);

                    if (tag == null)
                    {
                        // 创建新标签
                        tag = new UserInterestTag
                        {
                            Name = tagName,
                            Category = "其他", // 默认分类
                            Keywords = tagName,
                            IsActive = 1,
                            CreateTime = DateTime.Now,
                            UpdateTime = DateTime.Now
                        };

                        var tagId = userInterestTagBLL.Add(tag);
                        if (tagId != null)
                        {
                            tag.Id = Convert.ToInt32(tagId);
                        }
                    }

                    if (tag.Id > 0)
                    {
                        // 更新用户与标签的关联
                        var userTagRelationBLL = new UserTagRelationBLL();
                        var relation = userTagRelationBLL.GetModel($"UserId = {userId} AND TagId = {tag.Id}");

                        if (relation != null)
                        {
                            // 增加权重（阅读行为权重较低）
                            relation.Weight = Math.Min(1.0, relation.Weight + 0.02);
                            relation.ClickCount++;
                            relation.LastClickTime = DateTime.Now;
                            relation.UpdateTime = DateTime.Now;
                            userTagRelationBLL.Update(relation);
                        }
                        else
                        {
                            // 创建新的关联
                            relation = new UserTagRelation
                            {
                                UserId = userId,
                                TagId = tag.Id,
                                Weight = 0.05, // 阅读行为初始权重较低
                                ClickCount = 1,
                                LastClickTime = DateTime.Now,
                                CreateTime = DateTime.Now,
                                UpdateTime = DateTime.Now
                            };
                            userTagRelationBLL.Add(relation);
                        }
                    }
                }

                Logger.Info($"成功更新用户 {userId} 的标签权重，标签数量: {tags.Count}");
            }
            catch (Exception ex)
            {
                Logger.Error($"更新用户标签权重失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 基于用户标签关联生成向量
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户向量</returns>
        private async Task<double[]> GenerateUserVectorFromTagRelations(int userId)
        {
            try
            {
                Logger.Info($"开始基于标签关联生成用户 {userId} 的向量");

                // 获取用户标签关联
                var userTagRelationBLL = new UserTagRelationBLL();
                var relations = userTagRelationBLL.GetList($"UserId = {userId}", 50, 1, "*", "Weight DESC");

                if (relations == null || relations.Count == 0)
                {
                    Logger.Warn($"用户 {userId} 没有标签关联");
                    return null;
                }

                var userInterestTagBLL = new UserInterestTagBLL();
                var userVector = new double[VectorServiceConfig.VECTOR_DIMENSION];
                var totalWeight = 0.0;
                var validTagCount = 0;

                foreach (var relation in relations)
                {
                    try
                    {
                        // 获取标签向量
                        var tagVector = await userInterestTagBLL.GetTagVectorAsync(relation.TagId);
                        if (tagVector != null && tagVector.Length == VectorServiceConfig.VECTOR_DIMENSION)
                        {
                            var weight = Math.Min(relation.Weight, 1.0);

                            for (int i = 0; i < VectorServiceConfig.VECTOR_DIMENSION; i++)
                            {
                                userVector[i] += tagVector[i] * weight;
                            }

                            totalWeight += weight;
                            validTagCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Warn($"处理标签 {relation.TagId} 向量失败: {ex.Message}");
                    }
                }

                // 归一化
                if (validTagCount > 0 && totalWeight > 0)
                {
                    for (int i = 0; i < VectorServiceConfig.VECTOR_DIMENSION; i++)
                    {
                        userVector[i] /= totalWeight;
                    }

                    Logger.Info($"基于标签关联生成用户 {userId} 向量成功，有效标签数: {validTagCount}");
                    return userVector;
                }

                Logger.Warn($"用户 {userId} 没有有效的标签向量");
                return null;
            }
            catch (Exception ex)
            {
                Logger.Error($"基于标签关联生成用户向量失败: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 基于用户阅读历史生成向量
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户向量</returns>
        private async Task<double[]> GenerateUserVectorFromReadingHistory(int userId)
        {
            try
            {
                await Task.Delay(1); // 避免编译器警告
                Logger.Info($"开始基于阅读历史生成用户 {userId} 的向量");

                // 从缓存获取用户阅读历史
                var cacheKey = $"user_read_history:{userId}";
                var readHistory = _cache.GetCache<List<int>>(cacheKey);

                if (readHistory == null || readHistory.Count == 0)
                {
                    Logger.Warn($"用户 {userId} 没有阅读历史");
                    return null;
                }

                var newsBLL = new NewsBLL();
                var userVector = new double[VectorServiceConfig.VECTOR_DIMENSION];
                var validNewsCount = 0;

                // 取最近的20篇新闻
                var recentNewsIds = readHistory.Count > 20
                    ? readHistory.Skip(readHistory.Count - 20).ToList()
                    : readHistory;

                foreach (var newsId in recentNewsIds)
                {
                    try
                    {
                        var news = newsBLL.GetModel(newsId);
                        if (news != null && !string.IsNullOrEmpty(news.NewsVector))
                        {
                            var newsVector = _vectorService.StringToVector(news.NewsVector);
                            if (newsVector != null && newsVector.Length == VectorServiceConfig.VECTOR_DIMENSION)
                            {
                                for (int i = 0; i < VectorServiceConfig.VECTOR_DIMENSION; i++)
                                {
                                    userVector[i] += newsVector[i];
                                }
                                validNewsCount++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Warn($"处理新闻 {newsId} 向量失败: {ex.Message}");
                    }
                }

                // 平均化
                if (validNewsCount > 0)
                {
                    for (int i = 0; i < VectorServiceConfig.VECTOR_DIMENSION; i++)
                    {
                        userVector[i] /= validNewsCount;
                    }

                    Logger.Info($"基于阅读历史生成用户 {userId} 向量成功，有效新闻数: {validNewsCount}");
                    return userVector;
                }

                Logger.Warn($"用户 {userId} 没有有效的新闻向量");
                return null;
            }
            catch (Exception ex)
            {
                Logger.Error($"基于阅读历史生成用户向量失败: {ex.Message}", ex);
                return null;
            }
        }
    }

    /// <summary>
    /// 分页响应结果
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public class PaginatedResponse<T>
    {
        /// <summary>
        /// 数据列表
        /// </summary>
        public List<T> Data { get; set; }
        
        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }
        
        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPage { get; set; }
        
        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; }
        
        /// <summary>
        /// 是否有下一页
        /// </summary>
        public bool HasNextPage { get; set; }
        
        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages { get; set; }
    }

    /// <summary>
    /// 新闻搜索过滤条件
    /// </summary>
    public class NewsSearchFilters
    {
        /// <summary>
        /// 新闻分类
        /// </summary>
        public string Category { get; set; }
        
        /// <summary>
        /// 新闻来源
        /// </summary>
        public string Source { get; set; }
        
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }
        
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }
        
        /// <summary>
        /// 标签
        /// </summary>
        public string Tag { get; set; }
        
        /// <summary>
        /// 排除的新闻ID列表
        /// </summary>
        public List<int> ExcludeNewsIds { get; set; }
        
        /// <summary>
        /// 创建过滤条件的副本
        /// </summary>
        /// <returns>过滤条件副本</returns>
        public NewsSearchFilters Clone()
        {
            var clone = new NewsSearchFilters
            {
                Category = this.Category,
                Source = this.Source,
                StartDate = this.StartDate,
                EndDate = this.EndDate,
                Tag = this.Tag
            };
            
            if (this.ExcludeNewsIds != null)
            {
                clone.ExcludeNewsIds = new List<int>(this.ExcludeNewsIds);
            }
            
            return clone;
        }

        /// <summary>
        /// 获取哈希码
        /// </summary>
        /// <returns>哈希码</returns>
        public override int GetHashCode()
        {
            unchecked
            {
                int hash = 17;
                hash = hash * 23 + (Category?.GetHashCode() ?? 0);
                hash = hash * 23 + (Source?.GetHashCode() ?? 0);
                hash = hash * 23 + (StartDate?.GetHashCode() ?? 0);
                hash = hash * 23 + (EndDate?.GetHashCode() ?? 0);
                hash = hash * 23 + (Tag?.GetHashCode() ?? 0);
                
                if (ExcludeNewsIds != null && ExcludeNewsIds.Count > 0)
                {
                    foreach (var id in ExcludeNewsIds)
                    {
                        hash = hash * 23 + id.GetHashCode();
                    }
                }
                
                return hash;
            }
        }
    }

    /// <summary>
    /// 新闻向量相似度结果
    /// </summary>
    public class NewsVectorSimilarity
    {
        /// <summary>
        /// 新闻ID
        /// </summary>
        public int NewsId { get; set; }

        /// <summary>
        /// 相似度分数（0-1之间）
        /// </summary>
        public double Similarity { get; set; }

        /// <summary>
        /// 新闻对象
        /// </summary>
        public News News { get; set; }
    }
}  
 