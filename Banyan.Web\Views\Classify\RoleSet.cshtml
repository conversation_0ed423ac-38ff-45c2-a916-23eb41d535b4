﻿@model Banyan.Domain.Role
@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>高榕资本-GAORONG VENTURES</title>

    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" href="~/content/img/favicons/favicon.ico">
    <link rel="icon" type="image/png" href="~/content/img/favicons/favicon.ico" sizes="16x16">
    <title>添加项目组</title>
    <link type="text/css" href="/content/js/plugins/layui/css/layui.css" rel="stylesheet" />
    <style>
        .layui-form-label {
            padding: 9px 0;
        }

        .layui-input-block {
            margin-left: 90px;
        }
    </style>
</head>
<body>
    <div style="padding:50px 20px 20px">
        <form class="layui-form" id="role-form">
            <input type="hidden" name="id" id="id" value="@(Model.Id)" />
            <div class="layui-form-item">
                <label class="layui-form-label">用户分类：</label>
                <div class="layui-input-block">
                    <input type="text" name="name" id="name" value="@(Model.RoleName)" data-target="#name-tip" placeholder="请输入分类名称" class="layui-input">
                </div>
                <div id="name-tip"></div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">排序值：</label>
                <div class="layui-input-block">
                    <input type="number" name="sort" id="sort" value="@(Model.Sort > 0 ? Model.Sort : 255)" data-target="#sort-tip" placeholder="排序越大越前" class="layui-input">
                </div>
                <div id="sort-tip"></div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn">保 存</button>
                </div>
            </div>
        </form>
    </div>

    <script type="text/javascript" src="~/Content/js/core/jquery.min.js"></script>
    <script type="text/javascript" src="~/content/js/plugins/layui/layui.js"></script>

    <link href="~/Content/js/plugins/nice-validator/jquery.validator.css" rel="stylesheet" />
    <script src="~/Content/js/plugins/nice-validator/jquery.validator.min.js"></script>
    <script src="~/Content/js/plugins/nice-validator/local/zh-CN.js"></script>

    <script type="text/javascript">
        var layer;
        layui.use(['layer'], function () {
            var layer = layui.layer;
        });

        $(function () {
            $('#role-form').validator({
                theme: 'yellow_top_effect',
                timely: 2,
                stopOnError: true,
                fields: {
                    name: "required;",
                    sort: "required;byte",
                },
                valid: function (form) {
                    $.ajax({
                        type: 'post',
                        url: '@(Url.Action("rolesave", "adminapi"))',
                        data: { id: $('#id').val(), rolename: $('#name').val(), sort: $('#sort').val()},
                        success: function (data) {
                            if (data.code == 0) {
                                parent.layer.closeAll();
                                parent.location.reload();
                            } else {
                                layer.msg(data.msg);
                            }
                        },
                        error: function () {
                            layer.msg("很抱歉，请求异常！");
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>
