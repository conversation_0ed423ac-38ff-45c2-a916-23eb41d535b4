﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Text.RegularExpressions;

namespace Banyan.Apps
{
    public class RoleBLL
    {
        private readonly AjaxResult ajaxResult = null;

        public RoleBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        public bool ClearCache(Role model)
        {
            try
            {
                RedisUtil.Remove(RedisKey.role_valid_list);
                RedisUtil.Remove(string.Format(RedisKey.role_model, model.Id));
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 角色信息缓存数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Role GetModelByCache(int id)
        {
            string cacheKey = string.Format(RedisKey.role_model, id);
            Role model = RedisUtil.Get<Role>(cacheKey);
            if (model == null)
            {
                model = GetModel(id);
                RedisUtil.Set<Role>(cacheKey, model, TimeSpan.FromMinutes(10));
            }
            return model;
        }

        public string GetUserRoleLabel(Member user)
        {
            if (string.IsNullOrEmpty(user.Groups)) // || user.Levels > 0)
            {
                return string.Empty;
            }

            List<Role> roles = GetList(false);
            if (roles == null || roles.Count() == 0)
            {
                return string.Empty;
            }

            string groupStr = $",{user.Groups},";
            List<Role> mlist = (from p in roles
                                where groupStr.Contains("," + p.Id.ToString() + ",")
                                select p).ToList();

            return string.Join(",", mlist.Select(x => x.RoleName).ToArray());
        }

        public Role convertLdapEntryToRole(Dictionary<string, List<string>> roleEntry)
        {
            var model = new Role();
            string pattern = @"^(\d+)\w+";
            var m = Regex.Match(roleEntry["cn"][0], pattern);
            if (m.Success)
            {
                model.Id = int.Parse(m.Groups[1].Value);
                model.RoleName = roleEntry["description"][0];
                model.RoleLdap = roleEntry["cn"][0];
                if (roleEntry.ContainsKey("owner"))
                {
                    model.Owner = roleEntry["owner"][0];
                }
                model.status = (byte)(roleEntry.ContainsKey("o") ? RoleStatus.disable : RoleStatus.enable);
                return model;
            } else
            {
                return null;
            }
            
        }
            /// <summary>
            /// 获取全部角色列表
            /// </summary>
            /// <returns></returns>
            public List<Role> GetList(bool getAll = true, string groupIds = "")
        {
            string cacheKey = string.Format(RedisKey.role_valid_list);
            List<Role> list = RedisUtil.Get<List<Role>>(cacheKey);
            if (list == null || list.Count() == 0)
            {
                list = Ldap.getGroups().Select(convertLdapEntryToRole).Where(r => r != null).ToList();

                RedisUtil.Set<List<Role>>(cacheKey, list, TimeSpan.FromMinutes(10));
            }
            if (!getAll)
            {
                return list.Where(r => r.status == (byte)RoleStatus.enable).ToList();
            }
            if (groupIds.IsEmpty())
            {
                return list;
            }
            var set = new HashSet<string>(groupIds.Split(','));
            return list.Where(role => set.Contains(role.Id + "")).ToList();
        }

        public Role GetModel(int id)
        {
            try
            {
                return GetList(true, id + "").First();
            } catch(Exception e)
            {
                Member user = new MemberBLL().GetLogOnUser();
                if (id == 0)
                {
                    if (user.Levels == (int)MemberLevels.Administrator)
                    {
                        return null;
                    }
                }
                Logger.Error("user role id is not valid", user.RealName);
                throw e;
            }
        }

        public AjaxResult GetGroups(NameValueCollection paramValues)
        {
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            Member userModel = null;
            if (uid != 0)
            {
                userModel = new MemberBLL().GetModelByCache(uid);
            }
            

            if (userModel != null && userModel.Status == (int)MemberStatus.enable
                && !(userModel.Levels == (int)MemberLevels.Administrator || userModel.Levels == (int)MemberLevels.SuperUser))
            {
                if (!string.IsNullOrEmpty(userModel.Groups))
                {
                    ajaxResult.data = GetList(false, userModel.Groups);
                    ajaxResult.code = (int)ResultCode.success;
                    return ajaxResult;
                }
            }

            ajaxResult.data = GetList(false);
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;
        }

        //public AjaxResult GetGroups(NameValueCollection paramValues, Member user)
        //{
        //    string whereSql = $"Status={(int)RoleStatus.enable}";
        //    if (!(user.Levels == (int)MemberLevels.Administrator || user.Levels == (int)MemberLevels.SuperUser)) {
        //        string roleIds = string.IsNullOrEmpty(user.Groups) ? "0" : $"{user.Groups}";
        //        whereSql += $" AND Id in({roleIds})";
        //    }
        //    ajaxResult.data = GetList(whereSql);
        //    ajaxResult.code = (int)ResultCode.success;
        //    return ajaxResult;
        //}
    }
}
