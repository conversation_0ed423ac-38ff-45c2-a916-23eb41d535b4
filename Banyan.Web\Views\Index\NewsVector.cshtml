﻿@using Banyan.Domain
@{
    ViewBag.Name = "新闻向量化浏览";
    Layout = "/Views/Shared/_Layout.cshtml";
    var manager = ViewData["manager"] as Banyan.Domain.Member;
    var isAdmin = manager != null && (manager.Levels == (byte)Banyan.Domain.MemberLevels.Administrator || manager.Levels == (byte)Banyan.Domain.MemberLevels.SuperUser);
}

<style>
    h1 {
        font-size: medium;
    }
    a {
        color: #4E6EF2;
    }
    .news-item {
        padding: 15px;
        border-bottom: 1px solid #eee;
        transition: background-color 0.2s;
    }
    .news-item:hover {
        background-color: #f9f9f9;
    }
    .news-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 8px;
    }
    .news-meta {
        font-size: 12px;
        color: #888;
        margin-bottom: 8px;
    }
    .news-content {
        font-size: 14px;
        color: #333;
        line-height: 1.5;
    }
    .news-category {
        display: inline-block;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 12px;
        margin-right: 5px;
        font-weight: 500;
    }
    .category-finance {
        background-color: #e3f2fd;
        color: #1976d2;
        border-left: 3px solid #1976d2;
    }
    .category-tech {
        background-color: #e8f5e9;
        color: #388e3c;
        border-left: 3px solid #388e3c;
    }
    .category-policy {
        background-color: #fff3e0;
        color: #f57c00;
        border-left: 3px solid #f57c00;
    }
    .category-other {
        background-color: #f3e5f5;
        color: #8e24aa;
        border-left: 3px solid #8e24aa;
    }
    .news-source {
        display: inline-block;
        margin-right: 10px;
        color: #666;
    }
    .news-date {
        display: inline-block;
        color: #666;
    }
    .news-relevance {
        display: inline-block;
        margin-left: 10px;
        color: #ff5722;
        font-size: 12px;
    }
    .highlight {
        background-color: #fff59d;
        padding: 0 2px;
    }
    .search-box {
        margin-bottom: 20px;
    }
    .filter-section {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f5f5f5;
        border-radius: 4px;
    }
    .pagination-container {
        text-align: center;
        margin-top: 20px;
        padding: 10px 0;
    }
    .filter-tags-container {
        margin: 10px 0;
        padding: 5px 0;
    }
    .filter-tag {
        display: inline-block;
        margin-right: 5px;
        margin-bottom: 5px;
        padding: 5px 8px;
        font-size: 12px;
    }
    .filter-tag a {
        color: white;
        margin-left: 5px;
    }
    .loading-container {
        text-align: center;
        padding: 30px 0;
    }
    .no-results {
        padding: 20px;
        text-align: center;
        background-color: #f9f9f9;
        border-radius: 4px;
        margin: 20px 0;
    }
    .no-results p {
        margin-bottom: 10px;
    }
    .no-results ul {
        text-align: left;
        display: inline-block;
        margin: 0 auto;
    }
    .pagination-info {
        margin-bottom: 10px;
        color: #666;
        font-size: 12px;
    }
    .vector-status-badge {
        display: inline-block;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 12px;
        margin-left: 5px;
    }
    .vector-status-pending {
        background-color: #e3f2fd;
        color: #1976d2;
        border-left: 3px solid #1976d2;
    }
    .vector-status-success {
        background-color: #e8f5e9;
        color: #388e3c;
        border-left: 3px solid #388e3c;
    }
    .vector-status-failed {
        background-color: #ffebee;
        color: #d32f2f;
        border-left: 3px solid #d32f2f;
    }
    .vectorize-btn {
        transition: all 0.3s ease;
    }
    .vectorize-btn:hover {
        transform: scale(1.05);
    }
    .news-item {
        transition: all 0.3s ease;
        border-left: 3px solid transparent;
    }
    .news-item:hover {
        border-left-color: #4E6EF2;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .news-title a {
        font-weight: 600;
        color: #333;
        transition: color 0.2s;
    }
    .news-title a:hover {
        color: #4E6EF2;
        text-decoration: none;
    }
    .filter-section {
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .filter-tag {
        transition: all 0.2s;
    }
    .filter-tag:hover {
        transform: translateY(-2px);
    }
    .stats-container {
        margin-bottom: 20px;
        display: flex;
        flex-wrap: wrap;
    }
    .stat-box {
        flex: 1;
        min-width: 200px;
        padding: 15px;
        margin-right: 10px;
        margin-bottom: 10px;
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        text-align: center;
    }
    .stat-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    .stat-label {
        font-size: 14px;
        color: #666;
    }
    .stat-box.total {
        background-color: #e3f2fd;
        border-left: 4px solid #1976d2;
    }
    .stat-box.vectorized {
        background-color: #e8f5e9;
        border-left: 4px solid #388e3c;
    }
    .stat-box.pending {
        background-color: #fff3e0;
        border-left: 4px solid #f57c00;
    }
    .stat-box.failed {
        background-color: #ffebee;
        border-left: 4px solid #d32f2f;
    }
    .scheduler-info p {
        margin-bottom: 8px;
    }
    .scheduler-controls {
        margin-top: 10px;
    }
    #scheduler-messages {
        max-height: 200px;
        overflow-y: auto;
    }
    .scheduler-status-running {
        color: #388e3c;
    }
    .scheduler-status-stopped {
        color: #d32f2f;
    }
    .panel-title {
        font-weight: 600;
    }
</style>

<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li>新闻资讯浏览</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <div class="tab-pane active">
                @if (isAdmin)
                {
                    <!-- Statistics Section -->
                    <div class="stats-container">
                        <div class="stat-box total">
                            <div class="stat-value" id="total-news-count">--</div>
                            <div class="stat-label">总新闻数</div>
                        </div>
                        <div class="stat-box vectorized">
                            <div class="stat-value" id="vectorized-count">--</div>
                            <div class="stat-label">已向量化</div>
                        </div>
                        <div class="stat-box pending">
                            <div class="stat-value" id="pending-count">--</div>
                            <div class="stat-label">待向量化</div>
                        </div>
                        <div class="stat-box failed">
                            <div class="stat-value" id="failed-count">--</div>
                            <div class="stat-label">向量化失败</div>
                        </div>
                    </div>
                }

                @if (isAdmin)
                {
                    <!-- Scheduler Control Section -->
                    <div class="panel panel-default" style="margin-bottom: 20px;">
                        <div class="panel-heading">
                            <h3 class="panel-title">
                                <i class="fa fa-clock"></i> 向量化调度器控制
                                <span class="pull-right">
                                    <span id="scheduler-status-badge" class="label label-default">未知状态</span>
                                </span>
                            </h3>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="scheduler-info">
                                        <p><strong>状态:</strong> <span id="scheduler-status">加载中...</span></p>
                                        <p><strong>上次运行:</strong> <span id="scheduler-last-run">--</span></p>
                                        <p><strong>下次运行:</strong> <span id="scheduler-next-run">--</span></p>
                                        <p><strong>扫描间隔:</strong> <span id="scheduler-interval">--</span> </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="scheduler-controls text-right">
                                        <button id="start-scheduler-btn" class="btn btn-success">
                                            <i class="fa fa-play"></i> 启动调度器
                                        </button>
                                        <button id="stop-scheduler-btn" class="btn btn-danger">
                                            <i class="fa fa-stop"></i> 停止调度器
                                        </button>
                                        <button id="restart-scheduler-btn" class="btn btn-warning">
                                            <i class="fa fa-sync"></i> 重启调度器
                                        </button>
                                        <button id="refresh-status-btn" class="btn btn-info">
                                            <i class="fa fa-refresh"></i> 刷新状态
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 15px;">
                                <div class="col-md-12">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">最近状态消息</h4>
                                        </div>
                                        <div class="panel-body" style="max-height: 200px; overflow-y: auto;">
                                            <ul id="scheduler-messages" class="list-group">
                                                <li class="list-group-item text-center">加载中...</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
                
                <!-- Search and Filter Section -->
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                                <div class="filter-section">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="sr-only" for="keywords">搜索</label>
                                                <input class="form-control" style="width: 100%" type="text" id="keywords" name="keywords" placeholder="输入关键词搜索">
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <select class="form-control" id="category" name="category" size="1">
                                                    <option value="">全部分类</option>
                                                    <option value="资讯">资讯</option>
                                                    <option value="融资">融资</option>
                                                    <option value="国内融资">国内融资</option>
                                                    <option value="国际融资">国际融资</option>
                                                    <option value="新股">新股</option>
                                                    <option value="政策新闻">政策新闻</option>
                                                </select>
                                            </div>
                                        </div>
                                        @if (isAdmin)
                                        {
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <select class="form-control" id="vectorStatus" name="vectorStatus" size="1">
                                                        <option value="">全部向量状态</option>
                                                        <option value="1">已向量化</option>
                                                        <option value="0">未向量化</option>
                                                        <option value="2">向量化失败</option>
                                                    </select>
                                                </div>
                                            </div>
                                        }
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <div class="input-daterange input-group">
                                                    <input class="form-control" type="text" id="startdate" name="startdate" placeholder="开始日期" size="7">
                                                    <span class="input-group-addon" style="border-left:none;border-right:none;"><i class="fa fa-chevron-right"></i></span>
                                                    <input class="form-control" type="text" id="enddate" name="enddate" placeholder="结束日期" size="7">
                                                </div>
                                            </div>
                                        </div>
                                        @if (isAdmin)
                                        {
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <select class="form-control" id="sort" name="sort" size="1">
                                                    <option value="popularity">按热度排序</option>
                                                    <option value="date">按日期排序</option>
                                                    <option value="relevance">按相关度排序</option>
                                                </select>
                                            </div>
                                        </div>
                                        }
                                    </div>
                                    <div class="row" style="margin-top: 10px;">
                                        <div class="col-md-12">
                                            <button class="btn btn-primary" id="search-btn">搜索</button>
                                            <button class="btn btn-default" id="reset-btn">重置</button>
                                            @if (isAdmin)
                                            {
                                                <button class="btn btn-success" id="batch-vectorize-btn" style="margin-left: 20px;">
                                                    <i class="fa fa-magic"></i> 批量向量化
                                                </button>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- News List Section -->
                <div id="news-list-container">
                    <!-- News items will be loaded here -->
                </div>

                <!-- Pagination Controls -->
                <div class="pagination-container">
                    <div class="pagination-info" id="pagination-info"></div>
                    <div id="pagination" class="layui-box layui-laypage layui-laypage-default"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- News Item Template -->
<script type="text/template" id="news-item-template">
    <div class="news-item" data-id="{{id}}">
        <div class="news-title">
            <a href="javascript:void(0);" onclick="viewNews({{id}})">{{title}}</a>
            {{vectorDetailsLink}}
        </div>
        <div class="news-meta">
            <span class="news-category category-{{categoryClass}}">{{classify}}</span>
            <span class="news-date"><i class="fa fa-calendar"></i> {{pubTime}}</span> | 
            <span class="news-source"><i class="fa fa-globe"></i> {{source}}</span>
            {{#if matchScore}}
            <span class="news-relevance"><i class="fa fa-star"></i> 相关度: {{matchScore}}</span>
            {{/if}}
            {{vectorStatus}}
            {{vectorizeButton}}
        </div>
        <div class="news-content">{{content}}</div>
        {{#if tags}}
        <div class="news-tags">
            <i class="fa fa-tags"></i> 
            {{#each tags}}
            {{/each}}
        </div>
        {{/if}}
    </div>
</script>

<!-- No Results Template -->
<script type="text/template" id="no-results-template">
    <div class="no-results">
        <p>没有找到匹配的新闻。</p>
        <p>建议：</p>
        <ul>
            <li>检查您的搜索关键词是否有拼写错误</li>
            <li>尝试使用不同的关键词</li>
            <li>尝试使用更广泛的类别</li>
            <li>扩大日期范围</li>
        </ul>
    </div>
</script>

@section scripts{
    <script type="text/javascript">
        var isAdmin = @Html.Raw(isAdmin.ToString().ToLower());

        $(function () {
            // Initialize date pickers
            layui.use(['laydate'], function () {
                var laydate = layui.laydate;
                
                laydate.render({
                    elem: '#startdate'
                });

                laydate.render({
                    elem: '#enddate'
                });
            });

            // Initialize pagination
            layui.use(['laypage'], function () {
                var laypage = layui.laypage;
                
                // This will be updated when we load news data
                initPagination(laypage, 0, 1);
            });

            // Load initial news data
            loadNews(1);

            // Load statistics (only for admin)
            if (isAdmin) {
                updateStatistics();
            }

            // Bind search button click
            $('#search-btn').on('click', function() {
                loadNews(1);
            });

            // Bind reset button click
            $('#reset-btn').on('click', function() {
                $('#keywords').val('');
                $('#category').val('');
                $('#startdate').val('');
                $('#enddate').val('');
                $('#sort').val('relevance');
                loadNews(1);
            });

            // Bind category change
            $('#category').on('change', function() {
                loadNews(1);
            });

            // Bind sort change
            $('#sort').on('change', function() {
                loadNews(1);
            });
            
            // Bind enter key in search box
            $('#keywords').on('keypress', function(e) {
                if (e.which === 13) {
                    loadNews(1);
                    return false;
                }
            });
            
            // Add filter tags display
            var filterTagsContainer = $('<div class="filter-tags-container"></div>');
            $('.filter-section').after(filterTagsContainer);
            
            // Update filter tags when filters change
            function updateFilterTags() {
                var filterTagsContainer = $('.filter-tags-container');
                filterTagsContainer.empty();

                var hasFilters = false;

                // Add keyword filter tag
                var keywords = $('#keywords').val();
                if (keywords) {
                    hasFilters = true;
                    filterTagsContainer.append(
                        '<span class="label label-primary filter-tag">' +
                        '<i class="fa fa-search"></i> 关键词: ' + keywords +
                        ' <a href="javascript:void(0);" onclick="clearFilter(\'keywords\')"><i class="fa fa-times"></i></a>' +
                        '</span>'
                    );
                }

                // Add category filter tag
                var category = $('#category').val();
                if (category) {
                    hasFilters = true;
                    filterTagsContainer.append(
                        '<span class="label label-success filter-tag">' +
                        '<i class="fa fa-tag"></i> 分类: ' + category +
                        ' <a href="javascript:void(0);" onclick="clearFilter(\'category\')"><i class="fa fa-times"></i></a>' +
                        '</span>'
                    );
                }

                // Add date range filter tag
                var startdate = $('#startdate').val();
                var enddate = $('#enddate').val();
                if (startdate || enddate) {
                    hasFilters = true;
                    var dateText = '';
                    if (startdate && enddate) {
                        dateText = startdate + ' 至 ' + enddate;
                    } else if (startdate) {
                        dateText = startdate + ' 之后';
                    } else {
                        dateText = enddate + ' 之前';
                    }

                    filterTagsContainer.append(
                        '<span class="label label-info filter-tag">' +
                        '<i class="fa fa-calendar"></i> 日期: ' + dateText +
                        ' <a href="javascript:void(0);" onclick="clearFilter(\'date\')"><i class="fa fa-times"></i></a>' +
                        '</span>'
                    );
                }

                // Add sort filter tag
                var sort = $('#sort').val();
                if (sort && sort !== 'relevance') {
                    hasFilters = true;
                    var sortText = '';
                    switch (sort) {
                        case 'date':
                            sortText = '按日期排序';
                            break;
                        case 'popularity':
                            sortText = '按热度排序';
                            break;
                    }

                    filterTagsContainer.append(
                        '<span class="label label-warning filter-tag">' +
                        '<i class="fa fa-sort"></i> ' + sortText +
                        ' <a href="javascript:void(0);" onclick="clearFilter(\'sort\')"><i class="fa fa-times"></i></a>' +
                        '</span>'
                    );
                }

                // Add vector status filter tag (only for admin)
                if (isAdmin) {
                    var vectorStatus = $('#vectorStatus').val();
                    if (vectorStatus) {
                        hasFilters = true;
                        var statusText = '';
                        var statusClass = '';

                        switch (vectorStatus) {
                            case '0':
                                statusText = '未向量化';
                                statusClass = 'label-default';
                                break;
                            case '1':
                                statusText = '已向量化';
                                statusClass = 'label-success';
                                break;
                            case '2':
                                statusText = '向量化失败';
                                statusClass = 'label-danger';
                                break;
                        }

                        filterTagsContainer.append(
                            '<span class="label ' + statusClass + ' filter-tag">' +
                            '<i class="fa fa-vector-square"></i> 向量状态: ' + statusText +
                            ' <a href="javascript:void(0);" onclick="clearFilter(\'vectorStatus\')"><i class="fa fa-times"></i></a>' +
                            '</span>'
                        );
                    }
                }

                // Show or hide the filter tags container
                if (hasFilters) {
                    filterTagsContainer.show();
                } else {
                    filterTagsContainer.hide();
                }
            }
            
            // Update filter tags on input change
            $('#keywords, #category, #startdate, #enddate, #sort').on('change', function() {
                updateFilterTags();
            });
            
            // Initial update of filter tags
            updateFilterTags();
            
            // Load scheduler status (only for admin)
            if (isAdmin) {
                loadSchedulerStatus();

                // Bind scheduler control buttons
                $('#start-scheduler-btn').on('click', function() {
                    controlScheduler('start');
                });

                $('#stop-scheduler-btn').on('click', function() {
                    controlScheduler('stop');
                });

                $('#restart-scheduler-btn').on('click', function() {
                    controlScheduler('restart');
                });

                $('#refresh-status-btn').on('click', function() {
                    loadSchedulerStatus();
                });
            }
        });

        // Function to load news data
        function loadNews(page) {
            var params = {
                page: page,
                pageSize: 10,
                keywords: $('#keywords').val(),
                category: $('#category').val(),
                startdate: $('#startdate').val(),
                enddate: $('#enddate').val(),
                sort: $('#sort').val()
            };

            // Only add vectorStatus parameter for admin users
            if (isAdmin) {
                params.vectorStatus = $('#vectorStatus').val();
            }

            // Show loading indicator
            $('#news-list-container').html('<div class="text-center"><i class="fa fa-spinner fa-spin fa-3x"></i></div>');

            // Make AJAX request to get news data
            $.ajax({
                url: '/Index/GetNewsVector',
                type: 'POST',
                data: params,
                success: function(response) {
                    if (response.code === 0) {
                        renderNewsList(response.data, response.count, page);
                    } else {
                        // Show error message
                        $('#news-list-container').html('<div class="alert alert-danger">' + response.msg + '</div>');
                    }
                },
                error: function() {
                    // Show error message
                    $('#news-list-container').html('<div class="alert alert-danger">加载新闻数据失败，请稍后再试。</div>');
                }
            });
        }

        // Function to render news list
        function renderNewsList(data, total, currentPage) {
            var container = $('#news-list-container');
            container.empty();

            if (data && data.length > 0) {
                // Render each news item
                $.each(data, function(index, item) {
                    var template = $('#news-item-template').html();
                    
                    // Determine category class
                    var categoryClass = 'other';
                    if (item.Classify === '融资' || item.Classify === '国内融资' || item.Classify === '国际融资' || item.Classify === '新股') {
                        categoryClass = 'finance';
                    } else if (item.Classify === '资讯') {
                        categoryClass = 'tech';
                    } else if (item.Classify === '政策新闻') {
                        categoryClass = 'policy';
                    }
                    
                    // Format date
                    var pubTime = new Date(parseInt(item.PubTime.replace("/Date(", "").replace(")/", "").split("+")[0])).pattern("yyyy-MM-dd");
                    
                    // Highlight search terms in title and content
                    var keywords = $('#keywords').val();
                    var title = item.Title;
                    var content = item.Content;
                    
                    if (keywords) {
                        // Split keywords by space to handle multiple search terms
                        var keywordArray = keywords.split(' ').filter(function(k) { return k.trim() !== ''; });
                        
                        // Highlight each keyword in title and content
                        keywordArray.forEach(function(keyword) {
                            var regex = new RegExp('(' + keyword + ')', 'gi');
                            title = title.replace(regex, '<span class="highlight">$1</span>');
                            content = content.replace(regex, '<span class="highlight">$1</span>');
                        });
                    }
                    
                    // Prepare tags if available
                    var tagsHtml = '';
                    if (item.Tag) {
                        var tags = item.Tag.split(',');
                        tagsHtml = tags.map(function(tag) {
                            return '<span class="label label-info">' + tag.trim() + '</span>';
                        }).join(' ');
                    }
                    
                    // Prepare match score if available
                    var matchScoreHtml = '';
                    if (item.MatchScore) {
                        matchScoreHtml = '<span class="news-relevance"><i class="fa fa-star"></i> 相关度: ' + 
                            (item.MatchScore * 100).toFixed(0) + '%</span>';
                    }
                    
                    // Add vector status indicator and buttons (only for admin)
                    var vectorStatusHtml = '';
                    var vectorizeButtonHtml = '';
                    var vectorDetailsLinkHtml = '';

                    if (isAdmin) {
                        // Add vector details link
                        vectorDetailsLinkHtml = '<a href="/Index/NewsVectorDetails/' + item.Id + '" class="btn btn-xs btn-info pull-right" title="查看向量详情">' +
                                               '<i class="fa fa-chart-bar"></i> 向量详情</a>';

                        if (item.VectorStatus !== undefined) {
                            var statusClass = '';
                            var statusText = '';

                            switch (item.VectorStatus) {
                                case 0:
                                    statusClass = 'label-default';
                                    statusText = '未向量化';
                                    vectorizeButtonHtml = '<button class="btn btn-xs btn-primary vectorize-btn" style="margin-left: 10px;" onclick="vectorizeNews(' + item.Id + ')"><i class="fa fa-magic"></i> 向量化</button>';
                                    break;
                                case 1:
                                    statusClass = 'label-success';
                                    statusText = '已向量化';
                                    vectorizeButtonHtml = '<button class="btn btn-xs btn-default vectorize-btn" style="margin-left: 10px;" onclick="vectorizeNews(' + item.Id + ')"><i class="fa fa-refresh"></i> 重新向量化</button>';
                                    break;
                                case 2:
                                    statusClass = 'label-danger';
                                    statusText = '向量化失败';
                                    vectorizeButtonHtml = '<button class="btn btn-xs btn-warning vectorize-btn" style="margin-left: 10px;" onclick="vectorizeNews(' + item.Id + ')"><i class="fa fa-repeat"></i> 重试向量化</button>';
                                    break;
                            }

                            if (statusText) {
                                vectorStatusHtml = '<span class="label ' + statusClass + '" style="margin-left: 10px;"><i class="fa fa-vector-square"></i> ' + statusText + '</span>';
                            }
                        }
                    }
                    
                    // Replace template placeholders
                    template = template.replace(/{{id}}/g, item.Id)
                                      .replace('{{title}}', title)
                                      .replace('{{categoryClass}}', categoryClass)
                                      .replace('{{classify}}', item.Classify)
                                      .replace('{{pubTime}}', pubTime)
                                      .replace('{{source}}', item.Source)
                                      .replace('{{content}}', content)
                                      .replace('{{#if matchScore}}', matchScoreHtml ? '' : '<!--')
                                      .replace('{{matchScore}}', item.MatchScore ? (item.MatchScore * 100).toFixed(0) + '%' : '')
                                      .replace('{{/if}}', matchScoreHtml ? '' : '-->')
                                      .replace('{{#if tags}}', tagsHtml ? '' : '<!--')
                                      .replace('{{/if}}', tagsHtml ? '' : '-->')
                                      .replace('{{#each tags}}', tagsHtml)
                                      .replace('{{/each}}', '')
                                      .replace('{{vectorStatus}}', vectorStatusHtml)
                                      .replace('{{vectorizeButton}}', vectorizeButtonHtml)
                                      .replace('{{vectorDetailsLink}}', vectorDetailsLinkHtml);
                    
                    container.append(template);
                });

                // Initialize pagination
                layui.use(['laypage'], function () {
                    var laypage = layui.laypage;
                    initPagination(laypage, total, currentPage);
                });
            } else {
                // Show no results message
                container.html($('#no-results-template').html());
            }
        }

        // Function to initialize pagination
        function initPagination(laypage, total, currentPage) {
            // Update pagination info
            var startItem = (currentPage - 1) * 10 + 1;
            var endItem = Math.min(currentPage * 10, total);
            
            if (total > 0) {
                $('#pagination-info').html('显示第 ' + startItem + ' 到 ' + endItem + ' 条，共 ' + total + ' 条');
            } else {
                $('#pagination-info').html('没有找到匹配的新闻');
            }
            
            // Render pagination controls
            laypage.render({
                elem: 'pagination',
                count: total,
                limit: 10,
                curr: currentPage,
                layout: ['count', 'prev', 'page', 'next', 'skip'],
                jump: function(obj, first) {
                    if (!first) {
                        // Scroll to top of news list when changing page
                        $('html, body').animate({
                            scrollTop: $('#news-list-container').offset().top - 20
                        }, 200);
                        
                        loadNews(obj.curr);
                    }
                }
            });
        }

        // Function to view news detail
        function viewNews(id) {
            var keywords = $('#keywords').val();
            var url = '/Index/News?id=' + id;
            
            if (keywords) {
                url += '&highlight=' + encodeURIComponent(keywords);
            }
            
            // Open in a modal dialog
            var h = document.documentElement.clientHeight || document.body.clientHeight;
            layer.open({
                type: 2,
                area: ['850px', h*0.82 + 'px'],
                fix: false,
                maxmin: true,
                anim: 5,
                shade: 0,
                title: "新闻详情",
                content: url,
            });
        }
        
        // Function to clear filter
        function clearFilter(filterType) {
            switch (filterType) {
                case 'keywords':
                    $('#keywords').val('');
                    break;
                case 'category':
                    $('#category').val('');
                    break;
                case 'date':
                    $('#startdate').val('');
                    $('#enddate').val('');
                    break;
                case 'sort':
                    $('#sort').val('relevance');
                    break;
                case 'vectorStatus':
                    $('#vectorStatus').val('');
                    break;
            }
            
            // Update filter tags and reload news
            updateFilterTags();
            loadNews(1);
        }

        // Function to vectorize news
        function vectorizeNews(newsId) {
            // Show loading indicator on the button
            var button = $('.news-item[data-id="' + newsId + '"] .vectorize-btn');
            var originalText = button.html();
            button.html('<i class="fa fa-spinner fa-spin"></i> 处理中...');
            button.prop('disabled', true);

            // Make AJAX request to trigger vectorization
            $.ajax({
                url: '/Index/VectorizeNews',
                type: 'POST',
                data: { newsId: newsId },
                success: function (response) {
                    if (response.code === 0) {
                        // Show success message
                        layer.msg('向量化处理已成功触发', { icon: 1 });

                        // Reload the current page to update status
                        setTimeout(function () {
                            loadNews($('#pagination .layui-laypage-curr em').text() || 1);
                        }, 1500);
                    } else {
                        // Show error message
                        layer.msg('向量化处理失败: ' + response.msg, { icon: 2 });

                        // Restore button
                        button.html(originalText);
                        button.prop('disabled', false);
                    }
                },
                error: function () {
                    // Show error message
                    layer.msg('向量化处理请求失败，请稍后再试', { icon: 2 });

                    // Restore button
                    button.html(originalText);
                    button.prop('disabled', false);
                }
            });
        }
        // Function to update statistics
        function updateStatistics() {
            // Make AJAX request to get statistics
            $.ajax({
                url: '/Adminapi/GetNewsVectorStats',
                type: 'POST',
                success: function (response) {
                    if (response.code === 0) {
                        // Update statistics
                        $('#total-news-count').text(response.data.totalCount);
                        $('#vectorized-count').text(response.data.vectorizedCount);
                        $('#pending-count').text(response.data.pendingCount);
                        $('#failed-count').text(response.data.failedCount);
                    }
                }
            });
        }

        // Function to vectorize news
        function vectorizeNews(newsId) {
            // Show loading indicator on the button
            var button = $('.news-item[data-id="' + newsId + '"] .vectorize-btn');
            var originalText = button.html();
            button.html('<i class="fa fa-spinner fa-spin"></i> 处理中...');
            button.prop('disabled', true);

            // Make AJAX request to trigger vectorization
            $.ajax({
                url: '/Index/VectorizeNews',
                type: 'POST',
                data: { newsId: newsId },
                success: function (response) {
                    if (response.code === 0) {
                        // Show success message
                        layer.msg('向量化处理已成功触发', { icon: 1 });

                        // Reload the current page to update status
                        setTimeout(function () {
                            loadNews($('#pagination .layui-laypage-curr em').text() || 1);
                        }, 1500);
                    } else {
                        // Show error message
                        layer.msg('向量化处理失败: ' + response.msg, { icon: 2 });

                        // Restore button
                        button.html(originalText);
                        button.prop('disabled', false);
                    }
                },
                error: function () {
                    // Show error message
                    layer.msg('向量化处理请求失败，请稍后再试', { icon: 2 });

                    // Restore button
                    button.html(originalText);
                    button.prop('disabled', false);
                }
            });
        }
        // Bind batch vectorize button click (only for admin)
        if (isAdmin) {
            $('#batch-vectorize-btn').on('click', function () {
            // Show confirmation dialog
            layer.confirm('确定要批量向量化未处理的新闻吗？这可能需要一些时间。', {
                btn: ['确定', '取消'],
                title: '批量向量化确认'
            }, function (index) {
                // User confirmed, close dialog
                layer.close(index);

                // Show loading indicator on the button
                var button = $('#batch-vectorize-btn');
                var originalText = button.html();
                button.html('<i class="fa fa-spinner fa-spin"></i> 处理中...');
                button.prop('disabled', true);

                // Make AJAX request to trigger batch vectorization
                $.ajax({
                    url: '/Index/BatchVectorizeNews',
                    type: 'POST',
                    data: {
                        vectorStatus: $('#vectorStatus').val() || '0', // Default to pending news if no filter
                        batchSize: 50 // Process 50 news at a time
                    },
                    success: function (response) {
                        if (response.code === 0) {
                            // Show success message
                            layer.msg('批量向量化处理已成功触发，共 ' + response.data.count + ' 条新闻', { icon: 1 });

                            // Update statistics after a delay
                            setTimeout(function () {
                                updateStatistics();
                                loadNews(1);
                            }, 2000);
                        } else {
                            // Show error message
                            layer.msg('批量向量化处理失败: ' + response.msg, { icon: 2 });
                        }

                        // Restore button
                        button.html(originalText);
                        button.prop('disabled', false);
                    },
                    error: function () {
                        // Show error message
                        layer.msg('批量向量化处理请求失败，请稍后再试', { icon: 2 });

                        // Restore button
                        button.html(originalText);
                        button.prop('disabled', false);
                    }
                });
            });
        });
        }


        // Helper function to format dates properly
        function formatDate(date) {
            if (!date || isNaN(date.getTime())) {
                return '未知';
            }
            
            try {
                var year = date.getFullYear();
                var month = (date.getMonth() + 1).toString().padStart(2, '0');
                var day = date.getDate().toString().padStart(2, '0');
                var hours = date.getHours().toString().padStart(2, '0');
                var minutes = date.getMinutes().toString().padStart(2, '0');
                var seconds = date.getSeconds().toString().padStart(2, '0');
                
                return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
            } catch (e) {
                return '日期格式错误';
            }
        }
        
        // Function to load scheduler status
        function loadSchedulerStatus() {
            // Show loading indicators
            $('#scheduler-status').text('加载中...');
            $('#scheduler-last-run').text('加载中...');
            $('#scheduler-next-run').text('加载中...');
            $('#scheduler-interval').text('加载中...');
            $('#scheduler-messages').html('<li class="list-group-item text-center"><i class="fa fa-spinner fa-spin"></i> 加载中...</li>');

            // Make AJAX request to get scheduler status
            $.ajax({
                url: '/Index/GetNewsVectorizationSchedulerStatus',
                type: 'POST',
                data: { includeDetailedStats: true },
                success: function (response) {
                    if (response.code === 0) {
                        updateSchedulerStatusUI(response.data);
                    } else {
                        // Show error message
                        $('#scheduler-status').text('获取状态失败');
                        $('#scheduler-messages').html('<li class="list-group-item text-danger">获取调度器状态失败: ' + response.msg + '</li>');
                    }
                },
                error: function () {
                    // Show error message
                    $('#scheduler-status').text('获取状态失败');
                    $('#scheduler-messages').html('<li class="list-group-item text-danger">获取调度器状态失败，请稍后再试</li>');
                }
            });
        }

        // Function to update scheduler status UI
        function updateSchedulerStatusUI(data) {
            // Update status badge
            var statusBadge = $('#scheduler-status-badge');
            var statusText = $('#scheduler-status');

            if (data.IsRunning) {
                statusBadge.removeClass('label-default label-danger').addClass('label-success');
                statusBadge.text('运行中');
                statusText.text('运行中');

                // Enable/disable buttons
                $('#start-scheduler-btn').prop('disabled', true);
                $('#stop-scheduler-btn').prop('disabled', false);
                $('#restart-scheduler-btn').prop('disabled', false);
            } else {
                statusBadge.removeClass('label-default label-success').addClass('label-danger');
                statusBadge.text('已停止');
                statusText.text('已停止');

                // Enable/disable buttons
                $('#start-scheduler-btn').prop('disabled', false);
                $('#stop-scheduler-btn').prop('disabled', true);
                $('#restart-scheduler-btn').prop('disabled', true);
            }

            // Update last run time
            if (data.LastRunTime && data.LastRunTime !== '0001-01-01T00:00:00') {
                try {
                    // Try to parse the date
                    var lastRunDate = new Date(data.LastRunTime);
                    
                    // Check if the date is valid
                    if (!isNaN(lastRunDate.getTime())) {
                        var formattedDate = lastRunDate.getFullYear() + '-' + 
                                          padZero(lastRunDate.getMonth() + 1) + '-' + 
                                          padZero(lastRunDate.getDate()) + ' ' + 
                                          padZero(lastRunDate.getHours()) + ':' + 
                                          padZero(lastRunDate.getMinutes()) + ':' + 
                                          padZero(lastRunDate.getSeconds());
                        $('#scheduler-last-run').text(formattedDate);
                    } else {
                        $('#scheduler-last-run').text('从未运行');
                    }
                } catch (e) {
                    $('#scheduler-last-run').text('从未运行');
                }
            } else {
                $('#scheduler-last-run').text('从未运行');
            }

            // Update next scheduled run time
            if (data.NextScheduledRunTime && data.NextScheduledRunTime !== '0001-01-01T00:00:00') {
                try {
                    // Try to parse the date
                    var nextRunDate = new Date(data.NextScheduledRunTime);
                    
                    // Check if the date is valid
                    if (!isNaN(nextRunDate.getTime())) {
                        var formattedDate = nextRunDate.getFullYear() + '-' + 
                                          padZero(nextRunDate.getMonth() + 1) + '-' + 
                                          padZero(nextRunDate.getDate()) + ' ' + 
                                          padZero(nextRunDate.getHours()) + ':' + 
                                          padZero(nextRunDate.getMinutes()) + ':' + 
                                          padZero(nextRunDate.getSeconds());
                        $('#scheduler-next-run').text(formattedDate);
                    } else {
                        $('#scheduler-next-run').text('未计划');
                    }
                } catch (e) {
                    $('#scheduler-next-run').text('未计划');
                }
            } else {
                $('#scheduler-next-run').text('未计划');
            }

            // Update scan interval
            $('#scheduler-interval').text(data.ScanIntervalMinutes + "分钟");

            // Update status messages
            var messagesContainer = $('#scheduler-messages');
            messagesContainer.empty();

            if (data.RecentStatusMessages && data.RecentStatusMessages.length > 0) {
                $.each(data.RecentStatusMessages, function (index, message) {
                    var messageClass = '';

                    // Apply different styles based on message content
                    if (message.indexOf('失败') !== -1 || message.indexOf('错误') !== -1 || message.indexOf('异常') !== -1) {
                        messageClass = 'list-group-item-danger';
                    } else if (message.indexOf('成功') !== -1 || message.indexOf('完成') !== -1) {
                        messageClass = 'list-group-item-success';
                    } else if (message.indexOf('开始') !== -1 || message.indexOf('触发') !== -1) {
                        messageClass = 'list-group-item-info';
                    } else if (message.indexOf('警告') !== -1 || message.indexOf('跳过') !== -1) {
                        messageClass = 'list-group-item-warning';
                    }

                    messagesContainer.append('<li class="list-group-item ' + messageClass + '">' + message + '</li>');
                });
            } else {
                messagesContainer.html('<li class="list-group-item text-center">没有状态消息</li>');
            }

            // If we have last run result, update statistics
            if (data.LastRunResult) {
                var lastRun = data.LastRunResult;

                // You could add more detailed statistics here if needed
                var statsHtml = '<div class="alert alert-info">' +
                    '<p><strong>上次运行结果:</strong> 总计: ' + lastRun.TotalProcessed +
                    ', 成功: ' + lastRun.SuccessCount +
                    ', 失败: ' + lastRun.FailedCount +
                    ', 耗时: ' + lastRun.Duration.toFixed(2) + ' 秒</p>' +
                    '</div>';

                // Add stats before messages
                messagesContainer.before(statsHtml);
            }
        }

        // Function to control scheduler
        function controlScheduler(action) {
            // Show loading indicator on the button
            var button;
            var originalText;

            switch (action) {
                case 'start':
                    button = $('#start-scheduler-btn');
                    originalText = button.html();
                    button.html('<i class="fa fa-spinner fa-spin"></i> 启动中...');
                    break;
                case 'stop':
                    button = $('#stop-scheduler-btn');
                    originalText = button.html();
                    button.html('<i class="fa fa-spinner fa-spin"></i> 停止中...');
                    break;
                case 'restart':
                    button = $('#restart-scheduler-btn');
                    originalText = button.html();
                    button.html('<i class="fa fa-spinner fa-spin"></i> 重启中...');
                    break;
            }

            // Disable all buttons during operation
            $('#start-scheduler-btn, #stop-scheduler-btn, #restart-scheduler-btn, #refresh-status-btn').prop('disabled', true);

            // Set a timeout to restore buttons if the request takes too long
            var timeoutId = setTimeout(function() {
                // Restore button
                button.html(originalText);
                $('#start-scheduler-btn, #stop-scheduler-btn, #restart-scheduler-btn, #refresh-status-btn').prop('disabled', false);
                // Re-enable buttons
                $('#start-scheduler-btn').prop('disabled', false);
                $('#stop-scheduler-btn').prop('disabled', false);
                $('#restart-scheduler-btn').prop('disabled', false);
                $('#refresh-status-btn').prop('disabled', false);
                
                // Show error message
                layer.msg('操作超时，请刷新状态查看结果', { icon: 0 });
                
                // Reload scheduler status
                loadSchedulerStatus();
            }, 10000); // 10 seconds timeout

            // Make AJAX request to control scheduler
            $.ajax({
                url: '/Index/ControlNewsVectorizationScheduler',
                type: 'POST',
                data: { action: action },
                timeout: 8000, // 8 seconds timeout
                success: function (response) {
                    // Clear the timeout
                    clearTimeout(timeoutId);
                    
                    if (response.code === 0) {
                        // Show success message
                        layer.msg(response.msg, { icon: 1 });

                        // Reload scheduler status after a short delay
                        setTimeout(function () {
                            loadSchedulerStatus();
                        }, 1000);
                    } else {
                        // Show error message
                        layer.msg(response.msg, { icon: 2 });

                        // Restore button
                        button.html(originalText);

                        // Re-enable buttons based on current status
                        loadSchedulerStatus();
                    }
                },
                error: function () {
                    // Clear the timeout
                    clearTimeout(timeoutId);
                    
                    // Show error message
                    layer.msg('操作失败，请稍后再试', { icon: 2 });

                    // Restore button
                    button.html(originalText);

                    // Re-enable buttons
                    $('#start-scheduler-btn, #stop-scheduler-btn, #restart-scheduler-btn, #refresh-status-btn').prop('disabled', false);
                }
            });
        }

        // Helper function to pad numbers with leading zeros
        function padZero(num) {
            return num < 10 ? '0' + num : num;
        }
    </script>
}
            