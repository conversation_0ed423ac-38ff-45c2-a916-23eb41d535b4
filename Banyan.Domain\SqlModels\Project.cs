﻿using Entity.Base;
using System;
using System.Collections.Generic;
using Utility;

namespace Banyan.Domain
{
    public class ProjectComparer : IEqualityComparer<Project>
    {
        public bool Equals(Project x, Project y)
        {
            if (x == null)
                return y == null;
            return x.Id.Equals(y.Id);
        }

        public int GetHashCode(Project obj)
        {
            return 1;
        }
    }
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public class Project
    {
        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        /// <summary>
        /// 创建人用户ID
        /// </summary>
        [SqlField]
        public int Creator { get; set; }

        /// <summary>
        /// 撰稿人
        /// </summary>
        [SqlField]
        public string EditorName { get; set; } = string.Empty;

        /// <summary>
        /// 项目标题
        /// </summary>
        [SqlField]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 参会人
        /// </summary>
        [SqlField]
        public string Participant { get; set; } = string.Empty;

        /// <summary>
        /// 参会分类（内部参会人，外部参会人）
        /// </summary>
        [SqlField]
        public string InteralPTCP { get; set; } = string.Empty;

        /// <summary>
        /// 项目来源
        /// </summary>
        /// 0:人脉
        /// 1：同事提及
        /// 2：同事介绍
        /// 3：FA
        ///
        /// 0：主动覆盖人脉
        /// 1：同事提及
        /// 2：同事介绍
        /// 3：FA介绍
        /// 4：主动覆盖研究

        [SqlField]
        public string Source { get; set; } = string.Empty;
        // 其他同事贡献具体内容
        [SqlField]
        public string ContributionDetail { get; set; } = string.Empty;
        /// <summary>
        /// 项目介绍人/提及人
        /// </summary>
        [SqlField]
        public string Introducer { get; set; } = string.Empty;

        /// <summary>
        /// 项目简述
        /// </summary>
        [SqlField]
        public string Summary { get; set; } = string.Empty;

        // 创始人姓名
        [SqlField]
        public string Founder { get; set; } = string.Empty;
        /// <summary>
        /// 团队背景
        /// </summary>
        [SqlField]
        public string Background { get; set; } = string.Empty;
        /// <summary>
        /// 业务数据
        /// </summary>
        [SqlField]
        public string BusinessData { get; set; } = string.Empty;

        /// <summary>
        /// 财务数据
        /// </summary>
        [SqlField]
        public string FinancialData { get; set; } = string.Empty;

        /// <summary>
        ///股权结构
        /// </summary>
        [SqlField]
        public string ShareStructure { get; set; } = string.Empty;

        /// <summary>
        /// 融资历史
        /// </summary>
        [SqlField]
        public string InvestHistory { get; set; } = string.Empty;

        /// <summary>
        /// 竞品
        /// </summary>
        [SqlField]
        public string CompareProduct { get; set; } = string.Empty;

        /// <summary>
        /// 项目亮点
        /// </summary>
        [SqlField]
        public string HighLight { get; set; } = string.Empty;

        /// <summary>
        /// 项目风险
        /// </summary>
        [SqlField]
        public string Risk { get; set; } = string.Empty;

        /// <summary>
        /// 下一步安排
        /// </summary>
        [SqlField]
        public string NextStep { get; set; } = string.Empty;

        /// <summary>
        /// 项目负责人
        /// </summary>
        [SqlField]
        public string ProjectManager { get; set; } = string.Empty;

        /// <summary>
        /// 分类Id{逗号分隔}
        /// </summary>
        [SqlField]
        public int ToRoleId { get; set; }
        // case "消费组": p.ToRoleId = 1; break;
        // case "技术组": p.ToRoleId = 2; break;
        // case "互联网组": p.ToRoleId = 3; break;
        // case "医疗组": p.ToRoleId = 5; break;
        // case "参谋部": p.ToRoleId = 6; break;
        // case "产业组": p.ToRoleId = 7; break;
        /// <summary>
        /// 分类名称
        /// </summary>
        public string RoleName { get; set; } = string.Empty;


        /// <summary>
        /// 是否是私密项目
        /// </summary>
        [SqlField]
        public bool IsPrivate { get; set; }
        [SqlField]
        public string privateReader { get; set; } = string.Empty;
        /// <summary>
        /// 是否是银子弹项目
        /// </summary>
        [SqlField]
        public bool IsSilver { get; set; }

        /// <summary>
        /// 项目状态
        /// </summary>
        [SqlField]
        public byte Status { get; set; } = (int)ProjectStatus.wait;

        /// <summary>
        /// 项目发布时间
        /// </summary>
        [SqlField]
        public DateTime PubTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 项目最后编辑时间
        /// </summary>
        [SqlField]
        public DateTime LastTime { get; set; }

        [SqlField]
        public DateTime AddTime { get; set; }
        /// <summary>
        /// 浏览人数
        /// </summary>
        [SqlField]
        public int ViewCount { get; set; }

        /// <summary>
        /// 评论数
        /// </summary>
        [SqlField]
        public int CommentCount { get; set; }

        /// <summary>
        /// 收藏人数
        /// </summary>
        [SqlField]
        public int CollectCount { get; set; }

        /// <summary>
        /// 点赞数
        /// </summary>
        [SqlField]
        public int PriseCount { get; set; }


        /// <summary>
        /// 是否允许操作
        /// </summary>
        public bool IsOperate { get; set; } = false;

        /// <summary>
        /// 发布时间字符串
        /// </summary>
        public string PubTimeStr { get; set; } = string.Empty;

        /// <summary>
        /// 成立年份
        /// </summary>
        [SqlField]
        public string foundedYear { get; set; } = string.Empty;

        /// <summary>
        /// 下一步状态
        /// </summary>
        [SqlField]
        public string nextStepStatus { get; set; } = string.Empty;

        /// <summary>
        /// 下一步状态ID，只用来排序
        /// </summary>
        [SqlField]
        public int nextStepStatusID { get; set; }
        // case "安排合伙人见面":
        //     model.nextStepStatusID = 100;
        //     break;
        // case "小组讨论":
        //     model.nextStepStatusID = 90;
        //     break;
        // case "不推进但持续关注":
        //     model.nextStepStatusID = 80;
        //     break;
        // case "Pass":
        //     model.nextStepStatusID = 70;
        /// <summary>
        /// 评分平均分
        /// </summary>
        [SqlField]
        public decimal AvgScore { get; set; }

        /// <summary>
        /// 评论总数
        /// </summary>
        [SqlField]
        public int CountScore { get; set; }

        /// <summary>
        /// 城市
        /// </summary>
        [SqlField]
        public string city { get; set; } = string.Empty;
        /// <summary>
        /// DD
        /// </summary>
        [SqlField]
        public string DDManager { get; set; } = string.Empty;
        /// <summary>
        /// finder  联系方式提供人/FA推荐同事
        /// </summary>
        [SqlField]
        public string finder { get; set; } = string.Empty;
        // 新发现
        [SqlField]
        public string UpdatedNews { get; set; } = string.Empty;

        [SqlField]
        public string HeadCount { get; set; } = string.Empty; // 员工人数
        [SqlField]
        public string TotalAsset { get; set; } = string.Empty;// 总资产
        [SqlField]
        public string groupMember { get; set; } = string.Empty;   //其他项目组员

        /// <summary>
        /// 项目填写完成度打分
        /// </summary>
        [SqlField]
        public string CompleteScore { get; set; } = string.Empty;
        [SqlField]
        public int RevisitId { get; set; }
        [SqlField]
        public string CommonName { get; set; } = string.Empty;
        [SqlField]
        public string FundFamilyName { get; set; } = string.Empty;
        [SqlField]
        public string Currency { get; set; } = string.Empty;
        [SqlField]
        public bool contributionManagerConfirm { get; set; }
        [SqlField]
        public bool contributionPartnerConfirm { get; set; }
        [SqlField]
        public string aisummary { get; set; } = string.Empty;
        [SqlField]
        public string ai_reasoning { get; set; } = string.Empty;
        [SqlField]
        public string ai_question { get; set; } = string.Empty;
        [SqlField]
        public DateTime ai_question_date { get; set; } = DateTime.Now;
        [SqlField]
        public string contributionLog { get; set; } = string.Empty;

        [SqlField] // 用于个人中心summary
        public int scoreprojectid { get; set; } = 0;

        public bool isNews = false;
    }

    public enum ProjectStatus
    {
        /// <summary>
        /// 编辑中
        /// </summary>
        editing = 3,

        /// <summary>
        /// 已下架
        /// </summary>
        wait = 2,

        /// <summary>
        /// 已上架
        /// </summary>
        normal = 1,

        /// <summary>
        /// 已删除
        /// </summary>
        delete = 0,
    }

}
