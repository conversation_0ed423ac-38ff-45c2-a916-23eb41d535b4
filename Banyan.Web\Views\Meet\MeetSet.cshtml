﻿@using Banyan.Domain
@{
    Layout = "/Views/Shared/_Layout.cshtml";
    List<Banyan.Domain.Role> classList = (List<Banyan.Domain.Role>)ViewData["rolelist"];
    List<Banyan.Domain.Member> memberList = (List<Banyan.Domain.Member>)ViewData["creatorList"];
    List<Banyan.Domain.Member> staffList = (List<Banyan.Domain.Member>)ViewData["staffList"];
    var id = ViewData["Id"];
    var member = ViewData["manager"] as Banyan.Domain.Member;
    var isAdmin = member.Levels == (byte)Banyan.Domain.MemberLevels.Administrator || member.Levels == (byte)Banyan.Domain.MemberLevels.SuperUser;
}
<link href="~/content/css/views/meet/meet-edit.css" rel="stylesheet" />
<style>
    .arr-btn{
        padding: 6px;
        cursor: pointer;
    }
    .arr-btn:hover {
        color: #208aee;
    }
</style>
<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li><a href="@(Url.Action("Meets","Meet"))">会议管理</a></li>
                <li> @((int)ViewData["Id"] == 0 ? "新建" : "编辑")会议</li>
            </ol>
        </div>
        <div class="block-content block-content-full" style="display:none;" id="page-app">
            <form class="form-horizontal" method="post" id="project-form" name="project-form">
                <input type="hidden" name="id" id="id" v-model="model.Id" />
                <input type="hidden" name="Creator" value="@(member.Id)" />
                <input type="hidden" name="status" id="status" v-model="model.Status" />
                <div class="form-group">
                    <label class="col-md-2 control-label need-badge" for="title">会议名称</label>
                    <div class="col-md-6">
                        <input class="form-control" type="text" id="title" name="title" data-rule="required;" v-model="model.Title" placeholder="会议名称">
                    </div>
                </div>

                @if (member.Levels == (byte)Banyan.Domain.MemberLevels.Administrator)
                {
                    <div class="form-group">
                        <label class="col-md-2 control-label need-badge" for="manager">会议负责人</label>
                        <div class="col-md-6">
                            <select class="form-control select2" id="manager" size="1">
                                @foreach (var mi in memberList)
                                {
                                    <option value="@(mi.RealName)">@(mi.RealName)</option>
                                }
                            </select>
                        </div>
                    </div>
                }

                <div class="form-group">
                    <label class="col-md-2 control-label need-badge" for="StartTime">开始时间</label>
                    <div class="col-md-4">
                        <input class="form-control" type="text" id="StartTime" autocomplete="off" name="StartTime" v-model="model.StartTime" placeholder="日期" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label need-badge" for="duration">预计时长(分钟)</label>
                    <div class="col-md-6">
                        <input class="form-control" type="text" id="duration" name="duration" data-rule="required;" v-model="model.Duration" placeholder="持续时间">
                    </div>
                </div>

                <div v-if="model.IsOperate" class="form-group">
                    <label class="col-md-2 control-label" for="isRepeat">是否重复</label>
                    <div class="col-md-6">
                        <select class="form-control select2" id="isRepeat" v-model="model.IsRepeat">
                            <option value="0">不重复</option>
                            <option value="1">每周重复</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label need-badge" for="InternalPTCP">高榕参会人</label>
                    <div class="col-md-6">
                        <select class="form-control select2" id="InternalPTCP" data-rule="required;" multiple="multiple" size="1">
                            @foreach (var mi in memberList)
                            {
                                <option value="@(mi.RealName)">@(mi.RealName)</option>
                            }
                        </select>
                    </div>
                </div>

                @*<div class="form-group">
                        <label class="col-md-2 control-label" for="Participant">外部参会人</label>
                        <div class="col-md-6">
                            <input class="form-control" type="text" id="Participant" name="Participant" rows="3" data-rule="required;" v-model="model.Participant" />
                        </div>
                    </div>*@
                <div class="form-group">
                    <label class="col-md-2 control-label" for="duration">会议说明</label>
                    <div class="col-md-6">
                        <input class="form-control" type="text" id="notice" name="notice" v-model="model.Summary">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label need-badge" for="searchType">讨论事项</label>
                    <div class="col-md-2">
                        <select class="form-control select2" lay-filter="search" id="searchType">
                            <option value="0">Login项目</option>
                            <option value="4">Login待定</option>
                            <option value="1">DD报告</option>
                            <option value="5">其他项目报告</option>
                            <option value="2">行业研究</option>
                            <option value="3">其他事项</option>
                            <option value="-1" selected="selected">未选择</option>
                        </select>
                    </div>
                    <div v-if='searchType == "3"' class="col-md-4">
                        <input class="form-control" id="summary" name="summary" v-model="summary" placeholder="请填写主题" />
                    </div>
                    <div v-if='searchType == "4"'>
                        <button class="btn btn-default" type="button" @@click="addSearchData()">添加</button>
                    </div>
                    <div v-show='searchType !== "-1" && searchType !== "3" && searchType !== "4"' class="col-md-4">
                        <select class="search-type form-control">
                        </select>
                        @*<input type="search" autocomplete="off" placeholder="请输入关键字搜索"
               id="searchType"
               v-on:input="onSearchData"
               list="searchRes"
               v-model="keywords">
        <datalist id="searchRes">
            <option v-for="(item, idx) in searchList" :value="item">{{item}}</option>
        </datalist>*@
                    </div>
                </div>

                <div class="form-group" v-show='searchType !== "-1" && searchType !== "4" '>
                    <label class="col-md-2 control-label need-badge" for="groupMember">选择主讲人</label>
                    <div class="col-md-5">
                        <select class="form-control select2" id="groupMember" v-model="speakers" multiple="multiple" size="1">
                            @*<option v-if='searchType=="3"' value="无">无</option>*@
                            @foreach (var mi in memberList)
                            {
                                <option value="@(mi.RealName)">@(mi.RealName)</option>
                            }
                        </select>
                    </div>
                    <div v-if='searchType !== "-1"'>
                        <button :disabled='(searchAdd == "" && ["0", "1", "2"].indexOf(searchType) != -1 ) || speakers.length == 0 || (searchType == "3" && summary == "" ) ' class="btn btn-default" type="button" @@click="addSearchData()">添加</button>
                    </div>
                </div>
                <div class="block-content tab-content block-content-mini" v-if="attachLogin.length > 0 || attachDD.length > 0 || attachResearch.length > 0 || attachOther.length > 0">
                    <label class="col-md-2 control-label"></label>
                    <div class="col-md-6">
                        <table class="table table-striped" style="width:100%;">
                            <thead>
                                <tr>
                                    <th class="hidden-xs" style="width: 15%;">类型</th>
                                    <th>主题/链接</th>
                                    <th>主讲人</th>
                                    <th>同类别排序</th>
                                    <th class="text-center" style="width: 100px;">
                                        删除
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(item,index) in attachLogin" style="width:100%;">
                                    <td class="hidden-xs" style="width: 15%;">
                                        <span v-if="item.SourceType == 5" class="label label-info">其他项目报告</span>
                                        <span v-else class="label label-info">Login项目</span>
                                    </td>
                                    <td v-if="item.SourceId != -1"><a class="link" :data-idx="item.SourceId" data-type="login" v-on:click="preview">{{item.Name}}</a></td>
                                    <td v-else>{{item.Name}}</td>
                                    <td>
                                        {{item.Speakers}}
                                    </td>
                                    <td>
                                        <span v-if="index!=attachLogin.length-1" class="fa fa-long-arrow-down arr-btn" v-on:click="rank(item.Id, +1)"></span>
                                        <span v-if="index != 0" span class="fa fa-long-arrow-up arr-btn" v-on:click="rank(item.Id, -1)"></span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group">
                                            <a :data-idx="index" class="btn push-5-r push-10" v-on:click="removeAttachLogin"><i class="fa fa-remove"></i></a>
                                        </div>
                                    </td>
                                </tr>

                                <tr v-for="(item,index) in attachDD" style="width:100%;">
                                    <td class="hidden-xs" style="width: 15%;">
                                        <span class="label label-info">DD报告</span>
                                    </td>
                                    <td><a class="link" :data-idx="item.SourceId" data-type="dd" v-on:click="preview">{{item.Name}}</a></td>
                                    <td>
                                        {{item.Speakers}}
                                    </td>
                                    <td>
                                        <span v-if="index!=attachDD.length-1" class="fa fa-long-arrow-down arr-btn" v-on:click="rank(item.Id, +1)"></span>
                                        <span v-if="index != 0" span class="fa fa-long-arrow-up arr-btn" v-on:click="rank(item.Id, -1)"></span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group">
                                            <a :data-idx="index" class="btn push-5-r push-10" v-on:click="removeAttachDD"><i class="fa fa-remove"></i></a>
                                        </div>
                                    </td>
                                </tr>

                                <tr v-for="(item,index) in attachResearch" style="width:100%;">
                                    <td class="hidden-xs" style="width: 15%;">
                                        <span class="label label-info">行业研究</span>
                                    </td>
                                    <td><a class="link" :data-idx="item.SourceId" data-type="research" v-on:click="preview">{{item.Name}}</a></td>
                                    <td>
                                        {{item.Speakers}}
                                    </td>
                                    <td>
                                        <span v-if="index!=attachResearch.length-1" class="fa fa-long-arrow-down arr-btn" v-on:click="rank(item.Id, +1)"></span>
                                        <span v-if="index != 0" span class="fa fa-long-arrow-up arr-btn" v-on:click="rank(item.Id, -1)"></span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group">
                                            <a :data-idx="index" class="btn push-5-r push-10" v-on:click="removeAttachResearch"><i class="fa fa-remove"></i></a>
                                        </div>
                                    </td>
                                </tr>

                                <tr v-for="(item,index) in attachOther" style="width:100%;">
                                    <td class="hidden-xs" style="width: 15%;">
                                        <span class="label label-info">其他事项</span>
                                    </td>
                                    <td>{{item.Name}}</td>
                                    <td>
                                        {{item.Speakers}}
                                    </td>
                                    <td>
                                        <span v-if="index!=attachOther.length-1" class="fa fa-long-arrow-down arr-btn" v-on:click="rank(item.Id, +1)"></span>
                                        <span v-if="index != 0" span class="fa fa-long-arrow-up arr-btn" v-on:click="rank(item.Id, -1)"></span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group">
                                            <a :data-idx="index" class="btn push-5-r push-10" v-on:click="removeAttachOther"><i class="fa fa-remove"></i></a>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label" for="NextStep2">会议附件</label>
                    <div class="block-content tab-content block-content-mini" title="支持文件格式：doc、docx、ppt、pptx、pdf，excel文件请在下面单独上传;如遇ppt文件转换图片变形，建议在本地转换成pdf文件再上传，文件大小不超过20M。">
                        <div v-if="model.IsOperate">
                            <file2picbtn :id="id"
                                         converturl="/adminapi/docconvert"
                                         savedocurl="/adminapi/addmeetdoc"
                                         removeurl="/adminapi/delmeetdoc"
                                         v-bind:filelist.sync="dataListBP"
                                         v-bind:imglist.sync="BP"
                                         type="RECORD"
                                         btnclass="btn btn-warning push-5-r push-10"
                                         btntxt="纪要上传"
                                         filenamepre="【会议纪要】"
                                         elm="docfile-bp">
                        </div>
                        <file2picbtn :id="id"
                                     converturl="/adminapi/docconvert"
                                     savedocurl="/adminapi/addmeetdoc"
                                     removeurl="/adminapi/delmeetdoc"
                                     v-bind:filelist.sync="dataListDD"
                                     v-bind:imglist.sync="DD"
                                     type="DD"
                                     btntxt="文件上传"
                                     elm="docfile-dd">

                        </file2picbtn>
                    </div>

                    <label class="col-md-2 control-label" for="NextStep3"></label>
                    <div class="block-content tab-content block-content-mini">
                        <fileuploadbtn :id="id"
                            removeurl="/adminapi/delmeetdoc"
                            savedocurl="/adminapi/addmeetdoc"
                            v-bind:filelist.sync="fileList"
                            btntxt="Excel上传"
                            elm="filesource">
                        </fileuploadbtn>
                    </div>

                </div>

                <div class="form-group" v-if="model.IsOperate">
                    <label class="col-md-2 control-label" for="Comment">会议纪要</label>
                    <div class="col-md-6">
                        <div id="posit">
                            <textarea class="form-control" id="Comment" name="Comment" rows="8" placeholder="会议记录（可检索）" v-model="model.Comment"></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-8 col-md-offset-2">

                        <button class="btn btn-primary" @@click="saveData(@((int)MeetStatus.editing), '@(isAdmin)')" type="button"><i class="fa fa-save push-5-r"></i>保 存</button>
                        <button class="btn btn-minw btn-warning" @@click="saveData(-1)" type="button">取消并返回</button>
                        <button v-if="model.IsOperate" class="btn btn-info" @@click="saveData(@((int)MeetStatus.normal))" type="button"><i class="fa fa-check push-5-r"></i>发布(不可修改)</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section scripts{
    <script src="/content/js/plugins/jquery.ui.widget.js"></script>
    <script src="/content/js/plugins/jquery.iframe-transport.js"></script>
    <script src="/content/js/plugins/jquery.fileupload.js"></script>

    <script src="~/content/js/component/fileuploadbtn.js"></script>

    <link href="/content/js/plugins/select2/select2.min.css" rel="stylesheet" />
    <link href="/content/js/plugins/select2/select2-bootstrap.min.css" rel="stylesheet" />
    <script type="text/javascript" src="/content/js/plugins/select2/select2.min.js"></script>
    <script src="~/Content/js/plugins/city-select/js/lazyload-min.js"></script>

    <script src="/content/js/core/ajaxfileupload.js"></script>
    <link href="~/Content/js/plugins/lightgallery/css/normalize.css" rel="stylesheet" />
    <link href="~/Content/js/plugins/lightgallery/css/lightgallery.min.css" rel="stylesheet" />

    <script src="~/Content/js/plugins/lightgallery/js/picturefill.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lightgallery.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-fullscreen.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-thumbnail.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-video.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-autoplay.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-zoom.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-hash.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-pager.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/jquery.mousewheel.min.js"></script>
    <script type="text/javascript">
        editing = @((int)MeetStatus.editing)
        id = @(ViewData["Id"])
    </script>
    <script src="~/Content/js/views/meet/edit-common-v2.js">
    </script>
}
