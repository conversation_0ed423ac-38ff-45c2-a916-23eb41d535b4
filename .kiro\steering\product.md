# Banyan - Intelligent News Recommendation System

## Product Overview
Banyan is an intelligent news recommendation system designed for investment professionals. The system analyzes user project data to generate personalized interest profiles, and uses vector-based similarity matching to recommend relevant news articles to users.

## Core Features
- **User Profile Management**: AI-powered analysis of user projects to generate interest tags and vectors
- **News Vectorization**: Automatic analysis and vectorization of news content
- **Interest Recommendation Engine**: Similarity-based matching between user interests and news content
- **Recommendation Caching**: High-performance caching for real-time queries

## Target Users
- Investment professionals (approximately 30 users)
- Users with overlapping interests in investment projects
- Users who need timely, relevant news recommendations

## Key Metrics
- Response time: < 100ms for recommendation queries
- Cache hit rate: > 90%
- Recommendation accuracy: > 85%

## Business Value
- Provides personalized news recommendations based on user interests
- Improves efficiency by delivering relevant information to users
- Enhances user experience with semantic understanding of content