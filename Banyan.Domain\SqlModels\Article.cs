﻿using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public class Article
    {
        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        /// <summary>
        /// 创建人用户ID
        /// </summary>
        [SqlField]
        public int Creator { get; set; }

        /// <summary>
        /// 撰稿人
        /// </summary>
        [SqlField]
        public string EditorName { get; set; } = string.Empty;

        /// <summary>
        /// 文章标题
        /// </summary>
        [SqlField]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 文章简述
        /// </summary>
        [SqlField]
        public string Summary { get; set; } = string.Empty;

        /// <summary>
        /// 文章内容
        /// </summary>
        [SqlField]
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// PDF文件路径
        /// </summary>
        [SqlField]
        public string PdfUrl { get; set; } = string.Empty;

        /// <summary>
        /// 封面图
        /// </summary>
        [SqlField]
        public string CoverUrl { get; set; } = string.Empty;

        /// <summary>
        /// 栏目Id{案例、周报、专题}
        /// </summary>
        [SqlField]
        public int ColumnId { get; set; }

        /// <summary>
        /// 分类Id{逗号分隔}
        /// </summary>
        [SqlField]
        public int ClassId { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        public string RoleName { get; set; } = string.Empty;

        /// <summary>
        /// 是否置顶
        /// </summary>
        [SqlField]
        public bool IsStick { get; set; }

        /// <summary>
        /// 是否设置推荐
        /// </summary>
        [SqlField]
        public bool IsRecommend { get; set; }

        /// <summary>
        /// 是否加入轮播
        /// </summary>
        [SqlField]
        public bool IsSlide { get; set; }

        /// <summary>
        /// 是否是私密文章
        /// </summary>
        [SqlField]
        public bool IsPrivate { get; set; }

        /// <summary>
        /// 排序值，降序排列
        /// </summary>
        [SqlField]
        public byte Sort { get; set; }
        /// <summary>
        /// 是否置顶
        /// </summary>
        [SqlField]
        public int ToRoleId { get; set; }
        /// <summary>
        /// 阅读角色
        /// </summary>
        [SqlField]
        public string ToRoleIds { get; set; } = string.Empty;

        /// <summary>
        /// 文章状态
        /// </summary>
        [SqlField]
        public byte Status { get; set; } = (int)ArticleStatus.wait;

        /// <summary>
        /// 文章发布时间
        /// </summary>
        [SqlField]
        public DateTime PubTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 文章添加时间
        /// </summary>
        [SqlField]
        public DateTime AddTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 文章最后编辑时间
        /// </summary>
        [SqlField]
        public DateTime LastTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 浏览人数
        /// </summary>
        [SqlField]
        public int ViewCount { get; set; }

        /// <summary>
        /// 评论数
        /// </summary>
        [SqlField]
        public int CommentCount { get; set; }

        /// <summary>
        /// 收藏人数
        /// </summary>
        [SqlField]
        public int CollectCount { get; set; }

        /// <summary>
        /// 点赞数
        /// </summary>
        [SqlField]
        public int PriseCount { get; set; }

        /// <summary>
        /// 是否给用户进行过推送
        /// </summary>
        [SqlField]
        public bool IsRemind { get; set; } = false;

        /// <summary>
        /// 是否允许操作
        /// </summary>
        public bool IsOperate { get; set; } = false;

        public string PubTimeStr { get; set; } = string.Empty;
    }

    public enum ArticleStatus
    {
        /// <summary>
        /// 编辑中
        /// </summary>
        editing = 3,

        /// <summary>
        /// 已下架
        /// </summary>
        wait = 2,

        /// <summary>
        /// 已上架
        /// </summary>
        normal = 1,

        /// <summary>
        /// 已删除
        /// </summary>
        delete = 0,
    }

    public enum ColumnId
    {
        /// <summary>
        /// 案例
        /// </summary>
        cases = 1,

        /// <summary>
        /// 周报
        /// </summary>
        weekly = 2,

        /// <summary>
        /// 专题
        /// </summary>
        subject = 3,

        /// <summary>
        /// 每周热点
        /// </summary>
        weekhot = 4,

        /// Research 专用
        /// <summary>
        /// 内部报告
        /// </summary>
        interalReport = 5,
    }

    public static class ArticleEnum
    {
        public static string GetColumnName(this int columnId)
        {
            string columnName = string.Empty;
            switch (columnId)
            {
                case 1:
                    columnName = "案例";
                    break;
                case 2:
                    columnName = "周报";
                    break;
                case 3:
                    columnName = "专题";
                    break;
                default:
                    break;
            }
            return columnName;
        }
    }

}
