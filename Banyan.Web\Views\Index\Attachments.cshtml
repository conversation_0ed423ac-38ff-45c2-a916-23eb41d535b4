﻿@using Banyan.Domain
@{
    ViewBag.Name = "附件检索";
    Layout = "/Views/Shared/_Layout.cshtml";
    var manager = ViewData["manager"] as Banyan.Domain.Member;
    var isAdmin = manager.Levels == (byte)Banyan.Domain.MemberLevels.Administrator || manager.Levels == (byte)Banyan.Domain.MemberLevels.SuperUser;
}

<style>
    td .layui-table-cell, .layui-table-cell img {
        height: 35px !important;
        line-height: 35px !important;
        padding: 0 15px;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
        box-sizing: border-box;
    }
    a {
        color: #4E6EF2;
    }
</style>

<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li>文件检索</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <div class="tab-pane active">
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="pull-right search-bar"></div>
                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                                @*<div class="form-group">
            <select class="search-name form-control"></select>
        </div>*@

                                <div class="form-group">
                                    <select class="form-control" id="suffix" name="suffix" size="1">
                                        <option value="">文件类型</option>
                                        <option value="BP">BP</option>
                                        <option value="DD">DD</option>
                                        <option value="MEET">会议文件</option>
                                        @if (isAdmin)
                                        {
                                            <option value="RECORD">会议纪要</option>
                                        }
                                        <option value=".xlsx">EXCEL</option>
                                        <option value="Image">图片</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="sr-only" for="title-name">搜索</label>
                                    <input class="form-control" type="text" id="keyname" name="keyname" placeholder="含文件文本内容搜索">
                                </div>

                                <div class="form-group">
                                    <a class="btn btn-default" id="dosearch">搜索</a>
                                </div>

                                <div class="form-group">
                                    <div class="input-daterange input-group">
                                        <input class="form-control" type="text" id="startdate" name="startdate" placeholder="开始日期">
                                        <span class="input-group-addon" style="border-left:none;border-right:none;"><i class="fa fa-chevron-right"></i></span>
                                        <input class="form-control" type="text" id="enddate" name="enddate" placeholder="结束日期">
                                    </div>
                                </div>
                                <div class="form-group">
                                    @if (isAdmin)
                                    {
                                        <button type="button" class="layui-btn" id="uploadBtn">
                                            <i class="layui-icon">&#xe67c;</i>批量上传纪要
                                        </button>
                                        <div id="progress"></div>
                                    }
                                </div>
                            </form>

                        </div>
                    </div>
                </div>
                <table class="layui-hide" id="table-list" lay-filter="list-filter"></table>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="bartpl">
    <div class="btn-group" role="group" data-toggle="buttons">
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="预览" lay-event="preview" data-original-title="预览"><i class="fa fa-eye"></i></button>
    </div>
</script>


@section scripts{
    <link href="/content/js/plugins/select2/select2.min.css" rel="stylesheet" />
    <link href="/content/js/plugins/select2/select2-bootstrap.min.css" rel="stylesheet" />
    <script type="text/javascript" src="/content/js/plugins/select2/select2.min.js"></script>
    <link href="~/Content/js/plugins/lightgallery/css/lightgallery.min.css" rel="stylesheet" />

    <script src="~/Content/js/plugins/lightgallery/js/picturefill.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lightgallery.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-fullscreen.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-thumbnail.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-video.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-autoplay.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-zoom.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-hash.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-pager.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/jquery.mousewheel.min.js"></script>
    <script type="text/javascript">
        function GetStringDate(dateStr) {
            if (!dateStr) return ''
            var date = eval('new ' + dateStr.substr(1, dateStr.length - 2));
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            var res = year + "-" + month + "-" + day;
            if (res == "1-1-1") return "";
            return res;
        }

        function preview(id) {
            var h = document.documentElement.clientHeight || document.body.clientHeight;
            layer.open({
                type: 2,
                area: ['850px', h * 0.8 + 'px'],
                fix: false,
                maxmin: true,
                anim: 5,
                shade: 0,
                title: "项目预览",
                content: '/index/preview?id=' + id,
            });
        }
        var paths = []
        layui.use(['laypage', 'layer', 'table', 'upload', 'laydate'], function () {
                 var uploadInst = layui.upload.render({
            elem: '#uploadBtn' //绑定元素
            ,url: '/adminapi/UploadMeetSummary' //上传接口
            ,accept: 'file'
            ,multiple: true
            ,acceptMime: "application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation"
            ,exts: "pdf|doc|docx|ppt|pptx"
            ,error: function(){
            //请求异常回调
            }
            , before: function () {
                paths = []
            }
            ,progress: function(n, elem, res, index){
                var percent = n + '%' //获取进度百分比
                console.log(percent)
                if (n < 100 && n > 0) {
                    $("#progress").html(`Uploading..${percent}`)
                } else {
                    $("#progress").html("")
                }
            }
            , done: function (res) {
                paths.push(res.data)
            }
            ,allDone: function(obj){
                console.log(obj)
                var index = layer.msg('Uploading...',
                    {
                        icon: 16,
                        time: 0,
                        shade: 0.01
                    });
                $.ajax({
                    type: 'POST',
                    url: '@(Url.Action("BatchSaveDocs", "adminapi"))',
                    data: { paths,},
                    success: function (data) {
                        layer.close(index);
                        if (data.code == 0) {
                            layer.msg('操作成功！', {time: 3500});
                            $('#dosearch').click();
                        } else {
                            layer.confirm(data.msg);
                        }
                    },
                    error: function () {
                        layer.close(index);
                        layui.layer.msg("很抱歉，请求异常！");
                    }
                });
            }
        });
            var laypage = layui.laypage,
                layer = layui.layer,
                laydate = layui.laydate,
                table = layui.table;

            table.render({
                elem: '#table-list'
                , height: 570
                , url: '@(Url.Action("attachmentlist", "adminapi"))'
                , page: { limit: 10, hash: 'page', curr: location.hash.replace('#!page=', '') }
                , method: 'post'
                , cols: [[
                    {
                        field: 'AtName', title: '文件名', fixed: 'left', width: 360, templet: function (d) {
                            if (d.AtSuffix != ".xlsx") {
                                return `<a href="/Index/PicPreview/${d.Id}" target="_blank">${d.AtName}</a>`
                            } else {
                                return `<a href=${d.AtUrl}>${d.AtName}</a>`;
                            }
                        }
                    }
                    , {
                        field: 'AtSuffix', title: '类型', width: 100, sort: true, templet: function (d) {
                            if (d.AtSuffix == 'RECORD') {
                                return "会议纪要"
                            } else if (d.AtSuffix == 'DD') {
                                if (d.SourceType == 1) {
                                    return 'DD'
                                } else {
                                    return "会议文件"
                                }
                            }
                            else {
                                return d.AtSuffix
                            }
                        }
                    }
                    , {
                        field: 'pname', title: '项目/会议名', width: 120, templet: function (d) {
                            if (d.SourceType == 1) {
                                return `<a href="javascript:void(0);"  onclick="preview(${d.pid})"">${d.pname} </a>`
                            } else {
                                return d.pname
                            }
                        }
                    }
                    , {
                        field: 'Path', title: '下载', width: 70, templet: function (d) {
                            if (d.Path) {
                                return `<a href=${d.Path} target="_blank">下载</a>`;
                            } else if (d.AtSuffix == ".xlsx") {
                                return `<a href=${d.AtUrl} target="_blank">下载</a>`;
                            } else {
                                return ""
                            }
                        }
                    }
                    @if(isAdmin) {
                        <text>
                        , {
                            field: 'pisprivate', title: '私密项目', width: 120, sort: true, templet: function (d) {
                                if (d.pisprivate == 1) {
                                    return "私密"
                                } else {
                                    return ""
                                }
                            }
                        } </text>
                    }
                    , {
                        field: 'AddTime', title: '添加时间', sort:true, width: 110, templet: function (d) {
                            return GetStringDate(d.AddTime)
                        }
                    }
                    , {
                        field: 'Creator', title: '创建人', width: 100
                    }

                    , { field: 'Content', title: '文本内容', width: 4000}

                ]],
                done: function () { }
            });


            laydate.render({
                elem: '#startdate',
                done: dosearch
            });

            laydate.render({
                elem: '#enddate',
                done: dosearch
            });

            $('#keyname').on('keypress', function (event) {
                if (event.keyCode === 13) {
                    $('#dosearch').trigger('click');
                }
            });
            $('#dosearch').on('click', dosearch);
            function dosearch() {
                queryParams = {
                    Name: $('#keyname').val(),
                    startdate: $('#startdate').val(),
                    enddate: $('#enddate').val(),
                    suffix: $('#suffix').val(),
                }
                table.reload('table-list', {
                    where: queryParams, page: { curr: 1 },
                });
            }

            $('#suffix').on('change', dosearch);

        })

        $(document).ready(function () {
            $('.search-name').select2({
                language: "zh-CN",
                width: "200px",
                height: "32px",
                theme: "classic",
                minimumResultsForSearch: 1,
                minimumInputLength: 1,
                placeholder: '按文件名匹配',
                ajax: {
                    url: '/adminapi/searchAttachName',
                    dataType: 'json',
                    type: 'POST',
                    processResults: function (data) {
                        // Transforms the top-level key of the response object from 'items' to 'results'
                        return {
                            results: data.data.map(val => {
                                val.id = val.Id
                                val.text = val.Name
                                return val
                            })
                        };
                    },
                    data: function (params) {
                        var query = {
                            Name: params.term,
                        }

                        // Query parameters will be ?search=[term]&type=public
                        return query;
                    }
                }
            });

            $('.search-name').on('select2:select', function (e) {
                var data = e.params.data;
                console.log(data);
                that.searchAdd = data.Name
                that.searchAddId = data.Id
            })
        })
    </script>
}
