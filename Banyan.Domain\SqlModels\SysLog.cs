using Entity.Base;
using System;
using Utility;
using System.Collections.Generic;
namespace Banyan.Domain
{
    public class SysLogComparer : IEqualityComparer<SysLog>
    {
        public bool Equals(SysLog x, SysLog y)
        {
            if (x == null)
                return y == null;
            return x.LogID.Equals(y.LogID);
        }

        public int GetHashCode(SysLog obj)
        {
            return 1;
        }
    }
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public partial class SysLog
    {
        public SysLog() { }

        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int LogID { get; set; }

        /// <summary>
        /// 页面
        /// </summary>
        [SqlField]
        public string Page { get; set; }

        /// <summary>
        /// 操作
        /// </summary>
        [SqlField]
        public string Action { get; set; }

        /// <summary>
        /// 操作描述
        /// </summary>
        [SqlField]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 创建人Id
        /// </summary>
        [SqlField]
        public int CreatorId { get; set; } = 0;

        /// <summary>
        /// 操作人用户名
        /// </summary>
        [SqlField]
        public string CreatedBy { get; set; }

        /// <summary>
        /// 添加时间
        /// </summary>
        [SqlField]
        public DateTime AddTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 客户端Ip地址
        /// </summary>
        [SqlField]
        public string Ip { get; set; } = string.Empty;

        [SqlField]
        public string Name { get; set; } = string.Empty;

        public string Project { get; set; } = string.Empty;

        public override string ToString()
        {
            return "id: " + LogID + " page: " + Page + " Action: " + Action + " Name:" + Name + " Description: " + (Description.Length > 10240 ? Description.Substring(0, 10240) : Description) + " CreatorId: " + CreatorId + " CreatedyBy: " + CreatedBy + " AddTime: " + AddTime.Formating() + " Ip: " + Ip;
        }
    }
}
