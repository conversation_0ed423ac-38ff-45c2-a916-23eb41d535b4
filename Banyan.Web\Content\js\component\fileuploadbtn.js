﻿Vue.component('fileuploadbtn', {
    props: {
        filelist: {
            type: Array,
            required: true
        },
        elm: {
            type: String,
            required: true
        },
        savedocurl: {
            type: String,
            required: true
        },
        removeurl: {
            type: String,
            required: true
        },
        btntxt: {
            type: String
        },
        id: {
            type: [Number, String],
            required: true
        },
        filetype: {
            type: String,
            default: "?filetype=excel"
        }
    },
    data: function () {
        return {
            btn: this.btntxt ? this.btntxt : "上传文件",
        }
    },
    mounted: function () {
        var that = this;
        that.jqueryRebind(that);
    },
     template: `<div>
                 <input type="file" name="imgFile" :id="elm" style="display:none;"   />
                    <a class="btn btn-primary push-5-r push-10" @click="handleClick" ><i class="fa fa-upload"></i> {{btn}}</a>
                    <div v-if="filelist && filelist.length > 0">
                        <div v-for="(item,index) in filelist" style="width:100%;">
                            <span> <a :href="item.AtUrl">{{ item.AtName }}</a> </span>
                            <button class="btn btn-xs btn-default" 
                              type="button" data-toggle="tooltip" 
                               @click="removeAttach(item.Id, index)">
                               <i class="fa fa-times"></i>
                             </button>
                             <img v-if="filetype==''" :src="item.AtUrl" style="height:300px;display:block"/>
                        </div>
                    </div>
               </div>`,
     methods: {
         handleClick: function () {
             $("#" + this.elm).click();
         },
         jqueryRebind: function (that) {
             $("#" + that.elm)[0].onchange = function () {
                 that.ajaxUpload(that.elm);
             }
         },
         ajaxUpload: function (elm) {
             var that = this;
             $.ajaxFileUpload({
                 url: '/content/js/plugins/kindeditor/asp.net/upload.ashx'+that.filetype,
                 secureuri: false,
                 dataType: 'json',
                 fileElementId: elm,
                 success: function (data, status) {
                     if (data.error == 0) {
                         that.addAttach(data);
                     } else {
                        layer.msg(data.message);
                     }
                     that.jqueryRebind(that);
                 },
                 error: function (data, status, e) {
                     console.log(data, status, e);
                     that.jqueryRebind(that);
                 }
             });
             return false;
         },
         addAttach(doc) {
             var that = this;
             var model = {
                 SourceId: this.id,
                 AtUrl: doc.dirurl,
                 AtSuffix: doc.fileext,
                 AtLength: doc.filelen,
                 AtName: doc.filename,
             }
             var arrayLen = that.filelist.push(model);
             if (that.id > 0) {
                 $.post(that.savedocurl,
                     model,
                     function (res) {
                         if (res.code == 0) {
                             that.filelist[arrayLen - 1].Id = res.data;
                         }
                     });
             }
         },
         removeAttach(id, index) {
             var that = this;
             layer.confirm('确认删除该文档吗？',
                 function (ind) {
                     layer.close(ind);
                     if (!id) {
                         that.filelist.splice(index, 1);
                         return;
                     }
                     $.post(that.removeurl,
                         { id: id },
                         function (res) {
                             if (res.code != 0) {
                                 layer.msg('移除失败');
                                 return;
                             }
                             that.filelist.splice(index, 1);
                         });
                 });
         },
     }
})

Vue.component('file2picbtn', {
    props: {
        filelist: {
            type: Array,
            required: true
        },
        imglist: {
            type: Array,
            required: true
        },
        type: {
            type: String,
            required: true
        },
        elm: {
            type: String,
            required: true
        },
        savedocurl: {
            type: String,
            required: true
        },
        converturl: {
            type: String,
            required: true
        },
        removeurl: {
            type: String,
            required: true
        },
        btntxt: {
            type: String
        },
        btnclass: {
            type: String
        },
        filenamepre: {
            type: String,
        },
        id: {
            type: [Number, String],
            required: true
        }
    },
    data: function () {
        return {
            saveurl: this.converturl + "/" + this.id,
            lightid: this.elm + "-lightgallery",
            index: 0,
            btn: this.btntxt ? this.btntxt : "上传文件",
            bclass: this.btnclass ? this.btnclass : "btn btn-primary push-5-r push-10",
            fPre: this.filenamepre ? this.filenamepre :""
        }
    },
    mounted: function () {
        this.adduploadevent();
    },
    template: `<div>
                <input type="file" :id="elm" :name="elm" :data-url="saveurl"  style="display:none;" />
                        <a :class="bclass" @click="handleClick"><i class="fa fa-upload"></i> {{btn}}</a>
                        <div v-for="(itemDD, idxDD) in filelist" class="demo-gallery">
                            <a :data-idx="idxDD" class="btn btn-danger push-5-r push-10" @click="removeAttach(imglist[idxDD].Id, idxDD)"><i class="fa fa-remove"></i> 删除文件</a>
                             <span>{{fPre}} {{imglist[idxDD].AtName}} </span>
                            <br />
                            <ul class="lightgalleryDD list-unstyled row" :id="lightid">
                                <li class="col-xs-6 col-sm-4 col-md-3" v-for="(item, index) in itemDD" 
                                    data-responsive="" :data-src="item" data-sub-html="">
                                    <a href="javascript:;">
                                        <img class="img-responsive" :src="item">
                                        <div class="demo-gallery-poster">
                                            <img src="/content/js/plugins/lightgallery/img/zoom.png" />
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </div>
              </div>`,
    methods: {
        handleClick: function () {
            $("#" + this.elm).click();
        },

        adduploadevent: function () {
            var that = this
            setTimeout(that.lightDD,1500)
            $("#" + that.elm).fileupload({
                dataType: 'json',
                done: function (e, data) {
                    layer.msg('上传完成');
                    layer.close(that.index);
                    if (data.result.code != 0) {
                        layer.msg(data.result.msg);
                        return;
                    }
                    that.loadGalleryDD(data.result.data);
                    that.addDD(data.result);
                },
                add: function (e, data) {
                    if (data.originalFiles[0] && data.originalFiles[0]['size'] > 20 * 1024 * 1024) {
                        layer.msg("文件大小不能超过20MB");
                        return;
                    }
                    that.index = layer.msg('文件上传中...',
                        {
                            icon: 16,
                            time: 0,
                            shade: 0.01
                        });
                    data.submit();
                },
                fail: function (err) {
                    console.log(err);
                    layer.close(that.index);
                    layer.msg("上传文件失败，请注意文件内容格式");
                }
            });
        },
        loadGalleryDD(val) {
            var that = this
            that.filelist.push(val.split(','));
            setTimeout(that.lightDD, 400);
        },
        lightDD() {
            try {
                $(".lightgalleryDD").lightGallery({
                    speed: 40,
                    download: false,
                    showThumbByDefault: false
                })
            } catch (e) {
                if (confirm("页面加载失败，刷新重试?")) {
                    history.go(0)
                }
            }
        },
        addDD(res) {
            var that = this;
            var doc = res.data
            var fileName = res.msg.match(/(.*)上传成功/)
            if (fileName.length > 0) {
                fileName = fileName[1]
            } else {
                fileName = '名字异常'
            }
            var model = {
                SourceId: that.id,
                AtUrl: doc,
                AtSuffix: that.type,
                AtLength: 0,
                Content: res.content,
                AtName: fileName
            }
            that.imglist.push(model)
            if (that.id > 0) {
                that.saveDD(model);
            }
        },
        saveDD(doc) {
            var that = this;
            $.post(that.savedocurl,
                doc,
                function (res) {
                    if (res.code == 0) {
                        that.imglist[that.imglist.length - 1].Id = res.data;
                    }
                }).error(function (xhr, errorText, errorType) {
                    that.loadState = -999;
                });
        },
        removeAttach(id, index) {
            var that = this;
            layer.confirm('确认删除该文档吗？',
                function (ind) {
                    layer.close(ind);
                    if (!id) {
                        that.filelist.splice(index, 1);
                        that.imglist.splice(index, 1);
                        return setTimeout(that.lightDD, 1000);
                    }
                    $.post(that.removeurl,
                        { id: that.imglist[index].Id },
                        function (res) {
                            if (res.code != 0) {
                                layer.msg('移除失败');
                                return;
                            }
                            that.filelist.splice(index, 1);
                            that.imglist.splice(index, 1);
                            setTimeout(that.lightDD, 1000);
                        });
                });
        },
    }
})