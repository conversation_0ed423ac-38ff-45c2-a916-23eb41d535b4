﻿using System.Web.Mvc;
using System.Web.Routing;

namespace Banyan.Web
{
    public class RouteConfig
    {
        public static void RegisterRoutes(RouteCollection routes)
        {
            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");

            routes.MapRoute(
                name: "<PERSON><PERSON>",
                url: "{controller}/{action}/{id}",
                defaults: new { controller = "Index", action = "Index", id = UrlParameter.Optional },
                namespaces: new string[] { "Banyan.Web.Controllers" }
            );
        }
    }
}
