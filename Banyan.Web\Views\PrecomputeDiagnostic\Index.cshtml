@{
    ViewBag.Title = "预计算系统诊断";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h2>预计算系统诊断与监控</h2>
            <hr />
        </div>
    </div>

    <!-- 统计信息面板 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h3 class="panel-title">系统统计信息</h3>
                    <button type="button" class="btn btn-sm btn-default pull-right" onclick="loadStatistics()" style="margin-top: -5px;">
                        <i class="fa fa-refresh"></i> 刷新
                    </button>
                </div>
                <div class="panel-body">
                    <div id="statistics-content">
                        <div class="text-center">
                            <i class="fa fa-spinner fa-spin"></i> 加载中...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作按钮面板 -->
    <div class="row">
        <div class="col-md-6">
            <div class="panel panel-info">
                <div class="panel-heading">
                    <h3 class="panel-title">预计算操作</h3>
                </div>
                <div class="panel-body">
                    <div class="btn-group-vertical btn-block">
                        <button type="button" class="btn btn-primary" onclick="triggerComprehensivePrecompute()">
                            <i class="fa fa-cogs"></i> 执行综合预计算
                        </button>
                        <button type="button" class="btn btn-info" onclick="triggerUserPrecompute()">
                            <i class="fa fa-users"></i> 执行用户推荐预计算
                        </button>
                        <button type="button" class="btn btn-info" onclick="triggerHotNewsPrecompute()">
                            <i class="fa fa-newspaper-o"></i> 执行热门新闻预计算
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="panel panel-warning">
                <div class="panel-heading">
                    <h3 class="panel-title">缓存管理</h3>
                </div>
                <div class="panel-body">
                    <div class="btn-group-vertical btn-block">
                        <button type="button" class="btn btn-warning" onclick="clearAllCache()">
                            <i class="fa fa-trash"></i> 清除所有预计算缓存
                        </button>
                        <div class="input-group" style="margin-top: 10px;">
                            <input type="number" class="form-control" id="userIdInput" placeholder="输入用户ID">
                            <span class="input-group-btn">
                                <button class="btn btn-warning" type="button" onclick="clearUserCache()">
                                    <i class="fa fa-user-times"></i> 清除用户缓存
                                </button>
                            </span>
                        </div>
                        <div class="input-group" style="margin-top: 10px;">
                            <input type="number" class="form-control" id="refreshUserIdInput" placeholder="输入用户ID">
                            <span class="input-group-btn">
                                <button class="btn btn-info" type="button" onclick="forceRefreshUserRecommendations()">
                                    <i class="fa fa-refresh"></i> 强制刷新用户推荐
                                </button>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 诊断结果面板 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h3 class="panel-title">系统诊断</h3>
                    <button type="button" class="btn btn-sm btn-success pull-right" onclick="runFullDiagnostic()" style="margin-top: -5px;">
                        <i class="fa fa-stethoscope"></i> 运行完整诊断
                    </button>
                </div>
                <div class="panel-body">
                    <div id="diagnostic-content">
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> 点击"运行完整诊断"按钮开始系统诊断
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作日志面板 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">操作日志</h3>
                    <button type="button" class="btn btn-sm btn-default pull-right" onclick="clearLog()" style="margin-top: -5px;">
                        <i class="fa fa-eraser"></i> 清除日志
                    </button>
                </div>
                <div class="panel-body">
                    <div id="operation-log" style="height: 300px; overflow-y: auto; background-color: #f5f5f5; padding: 10px; font-family: monospace; font-size: 12px;">
                        <!-- 操作日志将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    loadStatistics();
    addLog('页面加载完成');
});

// 加载统计信息
function loadStatistics() {
    addLog('正在加载统计信息...');
    
    $.get('/PrecomputeDiagnostic/GetStatistics', function(response) {
        if (response.success) {
            displayStatistics(response.data);
            addLog('统计信息加载成功');
        } else {
            addLog('统计信息加载失败: ' + response.message, 'error');
        }
    }).fail(function() {
        addLog('统计信息加载失败: 网络错误', 'error');
    });
}

// 显示统计信息
function displayStatistics(stats) {
    var html = '<div class="row">';
    
    html += '<div class="col-md-3"><div class="well text-center">';
    html += '<h4>' + stats.TotalUsersWithVectors + '</h4>';
    html += '<p>有兴趣向量的用户</p></div></div>';
    
    html += '<div class="col-md-3"><div class="well text-center">';
    html += '<h4>' + stats.UsersWithPrecomputedRecommendations + '</h4>';
    html += '<p>有预计算推荐的用户</p></div></div>';
    
    html += '<div class="col-md-3"><div class="well text-center">';
    html += '<h4>' + stats.UserRecommendationCoverageRate.toFixed(1) + '%</h4>';
    html += '<p>用户推荐覆盖率</p></div></div>';
    
    html += '<div class="col-md-3"><div class="well text-center">';
    html += '<h4>' + stats.CandidateNewsCount + '</h4>';
    html += '<p>候选新闻数量</p></div></div>';
    
    html += '</div>';
    
    html += '<div class="row">';
    html += '<div class="col-md-4"><div class="well text-center">';
    html += '<h4>' + stats.HotNewsCount + '</h4>';
    html += '<p>热门新闻数量</p></div></div>';
    
    html += '<div class="col-md-4"><div class="well text-center">';
    html += '<h4>' + stats.HotNewsWithPrecomputedSimilarity + '</h4>';
    html += '<p>有预计算相似新闻</p></div></div>';
    
    html += '<div class="col-md-4"><div class="well text-center">';
    html += '<h4 class="' + (stats.RedisConnectionStatus ? 'text-success' : 'text-danger') + '">';
    html += stats.RedisConnectionStatus ? '正常' : '异常';
    html += '</h4>';
    html += '<p>Redis连接状态</p></div></div>';
    
    html += '</div>';
    
    $('#statistics-content').html(html);
}

// 运行完整诊断
function runFullDiagnostic() {
    addLog('开始运行完整系统诊断...');
    $('#diagnostic-content').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> 正在运行诊断，请稍候...</div>');
    
    $.get('/PrecomputeDiagnostic/RunFullDiagnostic', function(response) {
        if (response.success) {
            displayDiagnosticResult(response.data);
            addLog('系统诊断完成，状态: ' + response.data.OverallStatus);
        } else {
            addLog('系统诊断失败: ' + response.message, 'error');
            $('#diagnostic-content').html('<div class="alert alert-danger">诊断失败: ' + response.message + '</div>');
        }
    }).fail(function() {
        addLog('系统诊断失败: 网络错误', 'error');
        $('#diagnostic-content').html('<div class="alert alert-danger">诊断失败: 网络错误</div>');
    });
}

// 显示诊断结果
function displayDiagnosticResult(result) {
    var statusClass = result.OverallStatus === 'Healthy' ? 'success' : 
                     result.OverallStatus === 'Warning' ? 'warning' : 'danger';
    
    var html = '<div class="alert alert-' + statusClass + '">';
    html += '<h4><i class="fa fa-' + (result.OverallStatus === 'Healthy' ? 'check' : 'exclamation-triangle') + '"></i> ';
    html += '整体状态: ' + result.OverallStatus + '</h4>';
    html += '<p>诊断耗时: ' + result.Duration + '</p>';
    html += '</div>';
    
    // 详细结果
    html += '<div class="row">';
    html += '<div class="col-md-6">';
    html += '<h5>Redis连接测试</h5>';
    html += '<p>状态: <span class="label label-' + getStatusClass(result.RedisConnectionTest.Status) + '">' + result.RedisConnectionTest.Status + '</span></p>';
    html += '<p>写入延迟: ' + result.RedisConnectionTest.WriteLatency.toFixed(2) + 'ms</p>';
    html += '<p>读取延迟: ' + result.RedisConnectionTest.ReadLatency.toFixed(2) + 'ms</p>';
    html += '</div>';
    
    html += '<div class="col-md-6">';
    html += '<h5>缓存命中率分析</h5>';
    html += '<p>状态: <span class="label label-' + getStatusClass(result.CacheHitRateAnalysis.Status) + '">' + result.CacheHitRateAnalysis.Status + '</span></p>';
    html += '<p>命中率: ' + result.CacheHitRateAnalysis.HitRate.toFixed(1) + '%</p>';
    html += '<p>总请求: ' + result.CacheHitRateAnalysis.TotalRequests + ', 命中: ' + result.CacheHitRateAnalysis.CacheHits + '</p>';
    html += '</div>';
    html += '</div>';
    
    $('#diagnostic-content').html(html);
}

// 获取状态样式类
function getStatusClass(status) {
    return status === 'Healthy' ? 'success' : status === 'Warning' ? 'warning' : 'danger';
}

// 触发综合预计算
function triggerComprehensivePrecompute() {
    if (!confirm('确定要执行综合预计算吗？这可能需要较长时间。')) return;
    
    addLog('开始执行综合预计算...');
    
    $.post('/PrecomputeDiagnostic/TriggerComprehensivePrecompute', function(response) {
        if (response.success) {
            addLog('综合预计算完成: ' + response.message);
            loadStatistics(); // 刷新统计信息
        } else {
            addLog('综合预计算失败: ' + response.message, 'error');
        }
    }).fail(function() {
        addLog('综合预计算失败: 网络错误', 'error');
    });
}

// 触发用户推荐预计算
function triggerUserPrecompute() {
    addLog('开始执行用户推荐预计算...');
    
    $.post('/PrecomputeDiagnostic/TriggerUserPrecompute', function(response) {
        if (response.success) {
            addLog('用户推荐预计算完成: ' + response.message);
            loadStatistics();
        } else {
            addLog('用户推荐预计算失败: ' + response.message, 'error');
        }
    }).fail(function() {
        addLog('用户推荐预计算失败: 网络错误', 'error');
    });
}

// 触发热门新闻预计算
function triggerHotNewsPrecompute() {
    addLog('开始执行热门新闻预计算...');
    
    $.post('/PrecomputeDiagnostic/TriggerHotNewsPrecompute', function(response) {
        if (response.success) {
            addLog('热门新闻预计算完成: ' + response.message);
            loadStatistics();
        } else {
            addLog('热门新闻预计算失败: ' + response.message, 'error');
        }
    }).fail(function() {
        addLog('热门新闻预计算失败: 网络错误', 'error');
    });
}

// 清除所有缓存
function clearAllCache() {
    if (!confirm('确定要清除所有预计算缓存吗？')) return;
    
    addLog('开始清除所有预计算缓存...');
    
    $.post('/PrecomputeDiagnostic/ClearAllCache', function(response) {
        if (response.success) {
            addLog('成功清除所有预计算缓存');
            loadStatistics();
        } else {
            addLog('清除缓存失败: ' + response.message, 'error');
        }
    }).fail(function() {
        addLog('清除缓存失败: 网络错误', 'error');
    });
}

// 清除用户缓存
function clearUserCache() {
    var userId = $('#userIdInput').val();
    if (!userId) {
        alert('请输入用户ID');
        return;
    }
    
    addLog('开始清除用户 ' + userId + ' 的预计算缓存...');
    
    $.post('/PrecomputeDiagnostic/ClearUserCache', { userId: userId }, function(response) {
        if (response.success) {
            addLog('成功清除用户 ' + userId + ' 的预计算缓存');
            $('#userIdInput').val('');
        } else {
            addLog('清除用户缓存失败: ' + response.message, 'error');
        }
    }).fail(function() {
        addLog('清除用户缓存失败: 网络错误', 'error');
    });
}

// 强制刷新用户推荐
function forceRefreshUserRecommendations() {
    var userId = $('#refreshUserIdInput').val();
    if (!userId) {
        alert('请输入用户ID');
        return;
    }
    
    addLog('开始强制刷新用户 ' + userId + ' 的推荐...');
    
    $.post('/PrecomputeDiagnostic/ForceRefreshUserRecommendations', { userId: userId }, function(response) {
        if (response.success) {
            addLog('成功强制刷新用户 ' + userId + ' 的推荐');
            $('#refreshUserIdInput').val('');
        } else {
            addLog('强制刷新用户推荐失败: ' + response.message, 'error');
        }
    }).fail(function() {
        addLog('强制刷新用户推荐失败: 网络错误', 'error');
    });
}

// 添加日志
function addLog(message, type) {
    var timestamp = new Date().toLocaleString();
    var logClass = type === 'error' ? 'text-danger' : type === 'warning' ? 'text-warning' : 'text-info';
    var logEntry = '<div class="' + logClass + '">[' + timestamp + '] ' + message + '</div>';
    
    $('#operation-log').append(logEntry);
    $('#operation-log').scrollTop($('#operation-log')[0].scrollHeight);
}

// 清除日志
function clearLog() {
    $('#operation-log').empty();
}
</script>
