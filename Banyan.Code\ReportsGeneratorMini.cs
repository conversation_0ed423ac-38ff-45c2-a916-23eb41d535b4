﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using igos_data;
using System.Text;
using System.Threading.Tasks;

namespace Banyan.Code
{   // 小程序个人中心用，未上线
    public class ReportsGeneratorMini
    {
        protected static decimal ChinaRound(decimal value, int decimals)
        {
            decimal result = 0;
            if (value < 0)
            {
                decimal t = Math.Round(-value, decimals, MidpointRounding.AwayFromZero);
                result = -t;
            }
            else
            {
                result = Math.Round(value, decimals, MidpointRounding.AwayFromZero);
            }
            return result;
        }
        protected static DataTable getShareStructureTable(string portfolioID)
        {
            DataTable dt = new DataTable();
            if (!string.IsNullOrEmpty(portfolioID))
            {
                string search = @"select distinct FundBasicInfo.fundFamillyName,PortfolioShareStructure.investType,PortfolioShareStructure.currency, PortfolioShareStructure.portfolioID,PortfolioShareStructure.fundID,
PortfolioShareStructure.closeDate,PortfolioShareStructure.shareType,PortfolioShareStructure.shareOwner,PortfolioShareStructure.abbName,
isnull(PortfolioShareStructure.cost,0)+isnull(PortfolioShareStructure.otherFees,0) as cost,
(isnull(PortfolioShareStructure.shareOwnedNo,0) *cast(isnull( case when conversionRatio='' then 1 else conversionRatio end,1) as decimal(18,8))) as shareOwnedNo, 
case when shareOwnedNo!=0 then cost/shareOwnedNo else 0 end as maxPrice,securityType,round,isnull(preMoney,0) as preMoney,isnull(postMoney,0) as postMoney 
   from PortfolioShareStructure
inner join DDL_SecurityType on DDL_SecurityType.securityTypeID=PortfolioShareStructure.securityTypeID
left outer join (select distinct case when fundFamillyName='RMB IV北京' or fundFamillyName='RMB IV成都' then 'RMB IV' else fundFamillyName end as fundFamillyName,fundID from FundBasicInfo) as FundBasicInfo
on FundBasicInfo.fundID=PortfolioShareStructure.fundID";
                search += @" where PortfolioShareStructure.portfolioID='" + portfolioID + @"' order by closedate,round desc;";
                Data_basic db = new Data_basic();
                if (db.ExecuteDataset(search) && db.AffectedRow > 0)
                    dt = db.Dataset.Tables[0];
            }
            return dt;
        }
        public static string getperformanceTableMini(string PMName, decimal USDtoRMBRate, decimal recommendWeight, decimal PManagerWeight, decimal PIManagerWeight, decimal PMemberWeight, decimal PIMemberWeight, bool combine)
        {
            string search = @"SELECT [ViewPortfolioInfo].[portfolioID],[currency],[ViewPortfolioInfo].abbName,[ViewPortfolioInfo].contributionProjectID,[FundFamily2PortfolioInvestSummary].fundFamillyName
 ,[ViewPortfolioInfo].projectManager,[portfolioIntroducer],[portfolioManager],[groupMember],[postInvestMember],[postInvestManager]
,sum(isnull([shareProceeds],0)+isnull([otherProceeds],0)) as proceeds,sum([loanProceeds]) as [loanProceeds],sum(isnull([cost],0)) as [cost],sum(isnull([loan],0)) as [loan]
,sum(isnull([loanRelization],0)) as [loanRelization],sum(isnull([costRelization],0)) as [costRelization],sum(isnull([totalCost],0)) as [totalCost] ,sum(isnull([carryingCost],0)) as [carryingCost],
sum(isnull([GL],0)) as [GL],sum(isnull(shareOwnedNo,0)) as shareOwnedNo,Valuation.Valuation,Valuation.loanValuation,valuationDate FROM 
(select case when fundFamillyName='RMB IV北京' or fundFamillyName='RMB IV成都' then 'RMB IV' else fundFamillyName end as fundFamillyName,
 [portfolioID],[initialDate],[shareProceeds],[otherProceeds],[loanProceeds],[cost],[loan],[loanRelization],[costRelization],[shareOwnedNo],[carryingCost],[totalCost],[GL],[sumProceeds],[currency]
 FROM FundFamily2PortfolioInvestSummary) as FundFamily2PortfolioInvestSummary 
left outer join [ViewPortfolioInfo] on [ViewPortfolioInfo].portfolioID=[FundFamily2PortfolioInvestSummary].portfolioID
left outer join (select distinct sum(isnull(Valuation,0)) as Valuation,sum(isnull(loanValuation,0)) as loanValuation,fundFamillyName,portfolioID,max(valuationDate) as valuationDate FROM 
  (select case when fundFamillyName='RMB IV北京' or fundFamillyName='RMB IV成都' then 'RMB IV' else fundFamillyName end as fundFamillyName,Valuation,loanValuation,portfolioID, valuationDate 
  from dbo.PortfolioValuation_Latest inner join FundBasicInfo on FundBasicInfo.fundID=PortfolioValuation_Latest.fundID ) as a group by FundFamillyName,portfolioID) as Valuation 
 on Valuation.portfolioID=[FundFamily2PortfolioInvestSummary].portfolioID and Valuation.fundFamillyName=[FundFamily2PortfolioInvestSummary].fundFamillyName";

            string searchInsestHistory = @"select [InvestHistorySummary].portfolioid,closeDate,currency,sum(cost) as cost,sum(proceeds) as proceeds,sum(costRelization) as costRelization,
fundFamillyName,projectManager,[portfolioIntroducer],[portfolioManager],[groupMember],[postInvestMember],[postInvestManager]
 from (select [InvestHistoryForIRR].portfolioid,closeDate,currency,cost,proceeds,costRelization,
fundFamillyName from [InvestHistoryForIRR]) as [InvestHistorySummary] 
 left outer join ViewPortfolioInfo on ViewPortfolioInfo.portfolioID =[InvestHistorySummary].portfolioID";
            //if (!string.IsNullOrEmpty(PMName))
            //{
            //    search += @" where projectManager='" + PMName + "' ";
            //    searchInsestHistory += " where projectManager = '" + PMName + "' ";
            //}

            search += @" group by [ViewPortfolioInfo].[portfolioID],[currency],abbName,[ViewPortfolioInfo].contributionProjectID,projectManager,[FundFamily2PortfolioInvestSummary].fundFamillyName,valuation,loanValuation,[portfolioIntroducer],[portfolioManager],[groupMember],[postInvestMember],[postInvestManager],valuationDate order by projectManager,[currency],fundFamillyName,abbName;";
            searchInsestHistory += " group by [InvestHistorySummary].portfolioid,closeDate,currency,fundFamillyName,projectManager,[portfolioIntroducer],[portfolioManager],[groupMember],[postInvestMember],[postInvestManager] order by portfolioid, closeDate, currency; ";

            var searchContribution = @"SELECT
	            [ViewPortfolioInfo].[portfolioID],
	            [currency],
	            [ViewPortfolioInfo].abbName,
	            [ViewPortfolioInfo].contributionProjectID,
	            [FundFamily2PortfolioInvestSummary].fundFamillyName ,
	            [ViewPortfolioInfo].projectManager,
	            [portfolioIntroducer],
	            [portfolioManager],
	            [groupMember],
	            [postInvestMember],
	            [postInvestManager] ,
	            sum(isnull([shareProceeds], 0)+ isnull([otherProceeds], 0)) as proceeds,
	            sum([loanProceeds]) as [loanProceeds],
	            sum(isnull([cost], 0)) as [cost],
	            sum(isnull([loan], 0)) as [loan] ,
	            sum(isnull([loanRelization], 0)) as [loanRelization],
	            sum(isnull([costRelization], 0)) as [costRelization],
	            sum(isnull([totalCost], 0)) as [totalCost] ,
	            sum(isnull([carryingCost], 0)) as [carryingCost],
	            sum(isnull([GL], 0)) as [GL],
	            sum(isnull(shareOwnedNo, 0)) as shareOwnedNo,
	            Valuation.Valuation,
	            Valuation.loanValuation,
	            valuationDate
            FROM
	            (
	            select
		            case
			            when fundFamillyName = 'RMB IV北京'
			            or fundFamillyName = 'RMB IV成都' then 'RMB IV'
			            else fundFamillyName end as fundFamillyName,
			            [portfolioID],
			            [initialDate],
			            [shareProceeds],
			            [otherProceeds],
			            [loanProceeds],
			            [cost],
			            [loan],
			            [loanRelization],
			            [costRelization],
			            [shareOwnedNo],
			            [carryingCost],
			            [totalCost],
			            [GL],
			            [sumProceeds],
			            [currency]
		            FROM
			            FundFamily2PortfolioInvestSummary) as FundFamily2PortfolioInvestSummary
            left outer join [ViewPortfolioInfo] on
	            [ViewPortfolioInfo].portfolioID = [FundFamily2PortfolioInvestSummary].portfolioID
            inner join (
	            select distinct projectID from Contribution c  
            )	as contributions
            on [ViewPortfolioInfo].contributionProjectID = contributions.projectID
            left outer join (
	            select
		            distinct sum(isnull(Valuation, 0)) as Valuation,
		            sum(isnull(loanValuation, 0)) as loanValuation,
		            fundFamillyName,
		            portfolioID,
		            max(valuationDate) as valuationDate
	            FROM
		            (
		            select
			            case
				            when fundFamillyName = 'RMB IV北京'
				            or fundFamillyName = 'RMB IV成都' then 'RMB IV'
				            else fundFamillyName end as fundFamillyName,
				            Valuation,
				            loanValuation,
				            portfolioID,
				            valuationDate
			            from
				            dbo.PortfolioValuation_Latest
			            inner join FundBasicInfo on
				            FundBasicInfo.fundID = PortfolioValuation_Latest.fundID ) as a
	            group by
		            FundFamillyName,
		            portfolioID) as Valuation on
	            Valuation.portfolioID = [FundFamily2PortfolioInvestSummary].portfolioID
	            and Valuation.fundFamillyName = [FundFamily2PortfolioInvestSummary].fundFamillyName
            group by
	            [ViewPortfolioInfo].[portfolioID],
	            [currency],
	            abbName,
	            [ViewPortfolioInfo].contributionProjectID,
	            projectManager,
	            [FundFamily2PortfolioInvestSummary].fundFamillyName,
	            valuation,
	            loanValuation,
	            [portfolioIntroducer],
	            [portfolioManager],
	            [groupMember],
	            [postInvestMember],
	            [postInvestManager],
	            valuationDate
            order by
	            projectManager,
	            [currency],
	            fundFamillyName,
	            abbName;";

            DataTable dt = new DataTable();
            DataTable dt2 = new DataTable();
            Data_basic db = new Data_basic();
            DataTable investHistory = new DataTable();
            string html = @"";
            if (db.ExecuteDataset(search + searchInsestHistory + searchContribution) && db.AffectedRow > 0)
            {
                dt = db.Dataset.Tables[0];
                investHistory = db.Dataset.Tables[1];
                dt2 = db.Dataset.Tables[2];
            }
            USDtoRMBRate = USDtoRMBRate == 0 ? (decimal)6.5 : USDtoRMBRate;
            if (!string.IsNullOrEmpty(PMName))
            {
                if (combine)
                {
                    html += string.Format(@"<div style=""width:1400px;"">", PMName);
                    html += @"<table class=""mini-table"" border=""0"" cellspacing=""0"" style=""width:100%;margin: 0px auto;"" >";
                    string filter = string.Format(@"portfolioManager like '%{0}%' OR portfolioIntroducer like '%{0}%' OR groupMember like '%{0}%' OR postInvestManager like '%{0}%' OR postInvestMember like '%{0}%'", PMName);
                    DataRow[] performanceRows = dt.Select(filter, "currency,fundFamillyName,abbName");
                    html += generateReport(performanceRows.CopyToDataTable(), investHistory.Select(filter, "").CopyToDataTable(), PMName, USDtoRMBRate, PManagerWeight, PMemberWeight, recommendWeight, PIManagerWeight, PIMemberWeight);
                    html += @"</table>";
                    //html += generateIMSReport2(PMName, "", "");
                    html += "</div>";
                }
                else
                {

                    String summaryHtml = "";
                    String tableTr = "";
                    String PM = "";
                    List<String> listHtml = new List<String>();
                    listHtml = generateReport(dt, investHistory, "负责项目", USDtoRMBRate, "portfolioManager like '%" + PMName + "%'", PManagerWeight);
                    if (listHtml != null && listHtml.Count() > 1 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                    {
                        PM = listHtml[0];
                        summaryHtml += listHtml[0];
                        tableTr += listHtml[1] + @"<tr><td></td><td colspan=""11"" style=""border-top-style:solid;border-top-width:medium;height:40px;""></td><td></td></tr>";
                    }
                    //decimal totalMarkup = 0;
                    string filter = "portfolioManager not like '%" + PMName + "%'";
                    listHtml = generateReport(dt, investHistory, "Sourcing项目", USDtoRMBRate, filter + " and portfolioIntroducer like '%" + PMName + "%'", recommendWeight);
                    if (listHtml != null && listHtml.Count() > 2 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                    {
                        summaryHtml += listHtml[0];
                        tableTr += listHtml[1] + @"<tr><td></td><td colspan=""11"" style=""border-top-style:solid;border-top-width:medium;height:40px;""></td><td></td></tr>";
                        //totalMarkup += string.IsNullOrEmpty(listHtml[0]) ? 0 : decimal.Parse(listHtml[2]);
                    }

                    listHtml = generateReport(dt, investHistory, "参与项目", USDtoRMBRate, filter + " and (groupMember like '%" + PMName + "%' OR postInvestManager like '%" + PMName + "%' OR postInvestMember like '%" + PMName + "%')", PMemberWeight);
                    if (listHtml != null && listHtml.Count() > 2 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                    {
                        summaryHtml += listHtml[0];
                        tableTr += listHtml[1] + @"<tr><td></td><td colspan=""11"" style=""border-top-style:solid;border-top-width:medium;height:40px;""></td><td></td></tr>";
                        //totalMarkup += string.IsNullOrEmpty(listHtml[0]) ? 0 : decimal.Parse(listHtml[2]);
                    }

                    listHtml = generateReport(dt2, investHistory, "百分比参与项目", USDtoRMBRate, "portfolioManager not like '%" + "占位用" + "%'", PMemberWeight, PMName);
                    if (listHtml != null && listHtml.Count() > 2 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                    {
                        summaryHtml += listHtml[0];
                        tableTr += listHtml[1] + @"<tr><td></td><td colspan=""11"" style=""border-top-style:solid;border-top-width:medium;height:40px;""></td><td></td></tr>";
                        //totalMarkup += string.IsNullOrEmpty(listHtml[0]) ? 0 : decimal.Parse(listHtml[2]);
                    }


                    html += @"<div style=""width:1140px;margin:auto;"">";
                    if (!string.IsNullOrEmpty(summaryHtml) && !string.IsNullOrEmpty(tableTr))
                    {
                        html += string.Format(@"<hr>", PMName);

                        html += @"<table class=""mini-table"" border=""0"" cellspacing=""0"" style=""width:100%;margin: 0px auto;"" >";
                        html += addHeader(true);
                        html += summaryHtml + @"<tr><td></td><td colspan=""11"" style=""border-top-style:solid;border-top-width:medium;height:40px;""></td><td></td></tr>" + tableTr;
                        html += @"</table>";
                    }

                    //html += generateIMSReport3(PMName, "", "");
                    html += @"</div>";
                }
            }
            else
            {
                html += @"<div style=""width:1140px""><table class=""mini-table"" border=""0"" cellspacing=""0"" style=""width:100%;margin: 0px auto;"" id=""investmentSummary"">";
                if (db.ExecuteDataset("SELECT DISTINCT lastNameChi+firstNameChi as Name FROM Staff WHERE PM='1' order by Name") && db.AffectedRow > 0)
                {
                    var distinctNames = (from row in db.Dataset.Tables[0].AsEnumerable() select row.Field<string>("Name")).Distinct();
                    foreach (var projectManager in distinctNames)
                    {
                        if (!string.IsNullOrEmpty(projectManager))
                        {
                            List<String> listHtml = new List<String>();

                            listHtml = generateReport(dt, investHistory, projectManager + "负责项目", USDtoRMBRate, "portfolioManager like '%" + projectManager + "%'", 1);
                            if (listHtml != null && listHtml.Count() > 1 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                                html += listHtml[1];
                            else
                                html += addHeader(true) + string.Format(@"<tr><td></td><td colspan=""2"" style=""height:20px;font-size:18px;text-align:right;border-bottom-style:solid;border-bottom-width:thin;"">{0} 负责项目汇总（CNY）</td><td style=""font-size:18px;text-align:center;border-bottom-style:solid;border-bottom-width:thin;"">0</td><td colspan=""6""></td></tr>", projectManager);

                            string summaryHtml = "";
                            string filter = "portfolioManager not like '%" + projectManager + "%'";
                            listHtml = generateReport(dt, investHistory, projectManager + "Sourcing", USDtoRMBRate, filter + " and portfolioIntroducer like '%" + projectManager + "%'", recommendWeight);
                            if (listHtml != null && listHtml.Count() > 2 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                                summaryHtml += listHtml[0];

                            listHtml = generateReport(dt, investHistory, projectManager + "参与项目", USDtoRMBRate, filter + " and (groupMember like '%" + projectManager + "%' OR postInvestManager like '%" + projectManager + "%' OR postInvestMember like '%" + projectManager + "%')", PMemberWeight);
                            if (listHtml != null && listHtml.Count() > 2 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                                summaryHtml += listHtml[0];

                            html += summaryHtml + @"<tr><td colspan=""9"" style=""height:20px;""></td></tr>";

                        }
                    }
                }
                html += @"</table></div>";
            }
            return html;
        }


        public static string getperformanceTable(string PMName, decimal USDtoRMBRate, decimal recommendWeight, decimal PManagerWeight, decimal PIManagerWeight, decimal PMemberWeight, decimal PIMemberWeight, bool combine)
        {
            string search = @"SELECT [ViewPortfolioInfo].[portfolioID],[currency],[ViewPortfolioInfo].abbName,[ViewPortfolioInfo].contributionProjectID,[FundFamily2PortfolioInvestSummary].fundFamillyName
 ,[ViewPortfolioInfo].projectManager,[portfolioIntroducer],[portfolioManager],[groupMember],[postInvestMember],[postInvestManager]
,sum(isnull([shareProceeds],0)+isnull([otherProceeds],0)) as proceeds,sum([loanProceeds]) as [loanProceeds],sum(isnull([cost],0)) as [cost],sum(isnull([loan],0)) as [loan]
,sum(isnull([loanRelization],0)) as [loanRelization],sum(isnull([costRelization],0)) as [costRelization],sum(isnull([totalCost],0)) as [totalCost] ,sum(isnull([carryingCost],0)) as [carryingCost],
sum(isnull([GL],0)) as [GL],sum(isnull(shareOwnedNo,0)) as shareOwnedNo,Valuation.Valuation,Valuation.loanValuation,valuationDate FROM 
(select case when fundFamillyName='RMB IV北京' or fundFamillyName='RMB IV成都' then 'RMB IV' else fundFamillyName end as fundFamillyName,
 [portfolioID],[initialDate],[shareProceeds],[otherProceeds],[loanProceeds],[cost],[loan],[loanRelization],[costRelization],[shareOwnedNo],[carryingCost],[totalCost],[GL],[sumProceeds],[currency]
 FROM FundFamily2PortfolioInvestSummary) as FundFamily2PortfolioInvestSummary 
left outer join [ViewPortfolioInfo] on [ViewPortfolioInfo].portfolioID=[FundFamily2PortfolioInvestSummary].portfolioID
left outer join (select distinct sum(isnull(Valuation,0)) as Valuation,sum(isnull(loanValuation,0)) as loanValuation,fundFamillyName,portfolioID,max(valuationDate) as valuationDate FROM 
  (select case when fundFamillyName='RMB IV北京' or fundFamillyName='RMB IV成都' then 'RMB IV' else fundFamillyName end as fundFamillyName,Valuation,loanValuation,portfolioID, valuationDate 
  from dbo.PortfolioValuation_Latest inner join FundBasicInfo on FundBasicInfo.fundID=PortfolioValuation_Latest.fundID ) as a group by FundFamillyName,portfolioID) as Valuation 
 on Valuation.portfolioID=[FundFamily2PortfolioInvestSummary].portfolioID and Valuation.fundFamillyName=[FundFamily2PortfolioInvestSummary].fundFamillyName";

            string searchInsestHistory = @"select [InvestHistorySummary].portfolioid,closeDate,currency,sum(cost) as cost,sum(proceeds) as proceeds,sum(costRelization) as costRelization,
fundFamillyName,projectManager,[portfolioIntroducer],[portfolioManager],[groupMember],[postInvestMember],[postInvestManager]
 from (select [InvestHistoryForIRR].portfolioid,closeDate,currency,cost,proceeds,costRelization,
fundFamillyName from [InvestHistoryForIRR]) as [InvestHistorySummary] 
 left outer join ViewPortfolioInfo on ViewPortfolioInfo.portfolioID =[InvestHistorySummary].portfolioID";
            //if (!string.IsNullOrEmpty(PMName))
            //{
            //    search += @" where projectManager='" + PMName + "' ";
            //    searchInsestHistory += " where projectManager = '" + PMName + "' ";
            //}

            search += @" group by [ViewPortfolioInfo].[portfolioID],[currency],abbName,[ViewPortfolioInfo].contributionProjectID,projectManager,[FundFamily2PortfolioInvestSummary].fundFamillyName,valuation,loanValuation,[portfolioIntroducer],[portfolioManager],[groupMember],[postInvestMember],[postInvestManager],valuationDate order by projectManager,[currency],fundFamillyName,abbName;";
            searchInsestHistory += " group by [InvestHistorySummary].portfolioid,closeDate,currency,fundFamillyName,projectManager,[portfolioIntroducer],[portfolioManager],[groupMember],[postInvestMember],[postInvestManager] order by portfolioid, closeDate, currency; ";

            var searchContribution = @"SELECT
	            [ViewPortfolioInfo].[portfolioID],
	            [currency],
	            [ViewPortfolioInfo].abbName,
	            [ViewPortfolioInfo].contributionProjectID,
	            [FundFamily2PortfolioInvestSummary].fundFamillyName ,
	            [ViewPortfolioInfo].projectManager,
	            [portfolioIntroducer],
	            [portfolioManager],
	            [groupMember],
	            [postInvestMember],
	            [postInvestManager] ,
	            sum(isnull([shareProceeds], 0)+ isnull([otherProceeds], 0)) as proceeds,
	            sum([loanProceeds]) as [loanProceeds],
	            sum(isnull([cost], 0)) as [cost],
	            sum(isnull([loan], 0)) as [loan] ,
	            sum(isnull([loanRelization], 0)) as [loanRelization],
	            sum(isnull([costRelization], 0)) as [costRelization],
	            sum(isnull([totalCost], 0)) as [totalCost] ,
	            sum(isnull([carryingCost], 0)) as [carryingCost],
	            sum(isnull([GL], 0)) as [GL],
	            sum(isnull(shareOwnedNo, 0)) as shareOwnedNo,
	            Valuation.Valuation,
	            Valuation.loanValuation,
	            valuationDate
            FROM
	            (
	            select
		            case
			            when fundFamillyName = 'RMB IV北京'
			            or fundFamillyName = 'RMB IV成都' then 'RMB IV'
			            else fundFamillyName end as fundFamillyName,
			            [portfolioID],
			            [initialDate],
			            [shareProceeds],
			            [otherProceeds],
			            [loanProceeds],
			            [cost],
			            [loan],
			            [loanRelization],
			            [costRelization],
			            [shareOwnedNo],
			            [carryingCost],
			            [totalCost],
			            [GL],
			            [sumProceeds],
			            [currency]
		            FROM
			            FundFamily2PortfolioInvestSummary) as FundFamily2PortfolioInvestSummary
            left outer join [ViewPortfolioInfo] on
	            [ViewPortfolioInfo].portfolioID = [FundFamily2PortfolioInvestSummary].portfolioID
            inner join (
	            select distinct projectID from Contribution c  
            )	as contributions
            on [ViewPortfolioInfo].contributionProjectID = contributions.projectID
            left outer join (
	            select
		            distinct sum(isnull(Valuation, 0)) as Valuation,
		            sum(isnull(loanValuation, 0)) as loanValuation,
		            fundFamillyName,
		            portfolioID,
		            max(valuationDate) as valuationDate
	            FROM
		            (
		            select
			            case
				            when fundFamillyName = 'RMB IV北京'
				            or fundFamillyName = 'RMB IV成都' then 'RMB IV'
				            else fundFamillyName end as fundFamillyName,
				            Valuation,
				            loanValuation,
				            portfolioID,
				            valuationDate
			            from
				            dbo.PortfolioValuation_Latest
			            inner join FundBasicInfo on
				            FundBasicInfo.fundID = PortfolioValuation_Latest.fundID ) as a
	            group by
		            FundFamillyName,
		            portfolioID) as Valuation on
	            Valuation.portfolioID = [FundFamily2PortfolioInvestSummary].portfolioID
	            and Valuation.fundFamillyName = [FundFamily2PortfolioInvestSummary].fundFamillyName
            group by
	            [ViewPortfolioInfo].[portfolioID],
	            [currency],
	            abbName,
	            [ViewPortfolioInfo].contributionProjectID,
	            projectManager,
	            [FundFamily2PortfolioInvestSummary].fundFamillyName,
	            valuation,
	            loanValuation,
	            [portfolioIntroducer],
	            [portfolioManager],
	            [groupMember],
	            [postInvestMember],
	            [postInvestManager],
	            valuationDate
            order by
	            projectManager,
	            [currency],
	            fundFamillyName,
	            abbName;";

            DataTable dt = new DataTable();
            DataTable dt2 = new DataTable();
            Data_basic db = new Data_basic();
            DataTable investHistory = new DataTable();
            string html = @"";
            if (db.ExecuteDataset(search + searchInsestHistory + searchContribution) && db.AffectedRow > 0)
            {
                dt = db.Dataset.Tables[0];
                investHistory = db.Dataset.Tables[1];
                dt2 = db.Dataset.Tables[2];
            }
            USDtoRMBRate = USDtoRMBRate == 0 ? (decimal)6.5 : USDtoRMBRate;
            if (!string.IsNullOrEmpty(PMName))
            {
                if (combine)
                {
                    html += string.Format(@"<div style=""width:1400px;""><hr>", PMName);
                    html += @"<table class=""mini-table"" border=""0"" cellspacing=""0"" style=""width:100%;margin: 0px auto;"" >";
                    string filter = string.Format(@"portfolioManager like '%{0}%' OR portfolioIntroducer like '%{0}%' OR groupMember like '%{0}%' OR postInvestManager like '%{0}%' OR postInvestMember like '%{0}%'", PMName);
                    DataRow[] performanceRows = dt.Select(filter, "currency,fundFamillyName,abbName");
                    html += generateReport(performanceRows.CopyToDataTable(), investHistory.Select(filter, "").CopyToDataTable(), PMName, USDtoRMBRate, PManagerWeight, PMemberWeight, recommendWeight, PIManagerWeight, PIMemberWeight);
                    html += @"</table>";
                    //html += generateIMSReport2(PMName, "", "");
                    html += "</div>";
                }
                else
                {

                    String summaryHtml = "";
                    String tableTr = "";
                    String PM = "";
                    List<String> listHtml = new List<String>();
                    listHtml = generateReport(dt, investHistory, "负责项目", USDtoRMBRate, "portfolioManager like '%" + PMName + "%'", PManagerWeight);
                    if (listHtml != null && listHtml.Count() > 1 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                    {
                        PM = listHtml[0];
                        summaryHtml += listHtml[0];
                        tableTr += listHtml[1] + @"<tr><td></td><td colspan=""11"" style=""border-top-style:solid;border-top-width:medium;height:40px;""></td><td></td></tr>";
                    }
                    //decimal totalMarkup = 0;
                    string filter = "portfolioManager not like '%" + PMName + "%'";
                    listHtml = generateReport(dt, investHistory, "Sourcing项目", USDtoRMBRate, filter + " and portfolioIntroducer like '%" + PMName + "%'", recommendWeight);
                    if (listHtml != null && listHtml.Count() > 2 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                    {
                        summaryHtml += listHtml[0];
                        tableTr += listHtml[1] + @"<tr><td></td><td colspan=""11"" style=""border-top-style:solid;border-top-width:medium;height:40px;""></td><td></td></tr>";
                        //totalMarkup += string.IsNullOrEmpty(listHtml[0]) ? 0 : decimal.Parse(listHtml[2]);
                    }

                    listHtml = generateReport(dt, investHistory, "参与项目", USDtoRMBRate, filter + " and (groupMember like '%" + PMName + "%' OR postInvestManager like '%" + PMName + "%' OR postInvestMember like '%" + PMName + "%')", PMemberWeight);
                    if (listHtml != null && listHtml.Count() > 2 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                    {
                        summaryHtml += listHtml[0];
                        tableTr += listHtml[1] + @"<tr><td></td><td colspan=""11"" style=""border-top-style:solid;border-top-width:medium;height:40px;""></td><td></td></tr>";
                        //totalMarkup += string.IsNullOrEmpty(listHtml[0]) ? 0 : decimal.Parse(listHtml[2]);
                    }

                    listHtml = generateReport(dt2, investHistory, "百分比参与项目", USDtoRMBRate, "portfolioManager not like '%" + "占位用" + "%'", PMemberWeight, PMName);
                    if (listHtml != null && listHtml.Count() > 2 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                    {
                        summaryHtml += listHtml[0];
                        tableTr += listHtml[1] + @"<tr><td></td><td colspan=""11"" style=""border-top-style:solid;border-top-width:medium;height:40px;""></td><td></td></tr>";
                        //totalMarkup += string.IsNullOrEmpty(listHtml[0]) ? 0 : decimal.Parse(listHtml[2]);
                    }


                    html += @"<div style=""width:1140px;margin:auto;"">";
                    if (!string.IsNullOrEmpty(summaryHtml) && !string.IsNullOrEmpty(tableTr))
                    {
                        html += string.Format(@"<hr>", PMName);

                        html += @"<table class=""mini-table"" border=""0"" cellspacing=""0"" style=""width:100%;margin: 0px auto;"" >";
                        html += addHeader(true);
                        html += summaryHtml + @"<tr><td></td><td colspan=""11"" style=""border-top-style:solid;border-top-width:medium;height:40px;""></td><td></td></tr>" + tableTr;
                        html += @"</table>";
                    }

                    //html += generateIMSReport3(PMName, "", "");
                    html += @"</div>";
                }
            }
            else
            {
                html += @"<div style=""width:1140px""><table class=""mini-table"" border=""0"" cellspacing=""0"" style=""width:100%;margin: 0px auto;"" id=""investmentSummary"">";
                if (db.ExecuteDataset("SELECT DISTINCT lastNameChi+firstNameChi as Name FROM Staff WHERE PM='1' order by Name") && db.AffectedRow > 0)
                {
                    var distinctNames = (from row in db.Dataset.Tables[0].AsEnumerable() select row.Field<string>("Name")).Distinct();
                    foreach (var projectManager in distinctNames)
                    {
                        if (!string.IsNullOrEmpty(projectManager))
                        {
                            List<String> listHtml = new List<String>();

                            listHtml = generateReport(dt, investHistory, projectManager + "负责项目", USDtoRMBRate, "portfolioManager like '%" + projectManager + "%'", 1);
                            if (listHtml != null && listHtml.Count() > 1 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                                html += listHtml[1];
                            else
                                html += addHeader(true) + string.Format(@"<tr><td></td><td colspan=""2"" style=""height:20px;font-size:18px;text-align:right;border-bottom-style:solid;border-bottom-width:thin;"">{0} 负责项目汇总（CNY）</td><td style=""font-size:18px;text-align:center;border-bottom-style:solid;border-bottom-width:thin;"">0</td><td colspan=""6""></td></tr>", projectManager);

                            string summaryHtml = "";
                            string filter = "portfolioManager not like '%" + projectManager + "%'";
                            listHtml = generateReport(dt, investHistory, projectManager + "Sourcing", USDtoRMBRate, filter + " and portfolioIntroducer like '%" + projectManager + "%'", recommendWeight);
                            if (listHtml != null && listHtml.Count() > 2 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                                summaryHtml += listHtml[0];

                            listHtml = generateReport(dt, investHistory, projectManager + "参与项目", USDtoRMBRate, filter + " and (groupMember like '%" + projectManager + "%' OR postInvestManager like '%" + projectManager + "%' OR postInvestMember like '%" + projectManager + "%')", PMemberWeight);
                            if (listHtml != null && listHtml.Count() > 2 && !string.IsNullOrEmpty(listHtml[0]) && !string.IsNullOrEmpty(listHtml[1]))
                                summaryHtml += listHtml[0];

                            html += summaryHtml + @"<tr><td colspan=""9"" style=""height:20px;""></td></tr>";

                        }
                    }
                }
                html += @"</table></div>";
            }
            return html;
        }

        protected static string addHeader(bool summary)
        {

            //        @"<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">Equity</td>
            //<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">Loan</td>"
            string html = summary == true ? @"<tr><td style=""width:1%;""></td><td colspan=""2"" style=""width:17%;height:20px;font-size:16px;font-weight:bold;text-align:center;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium""></td>" : string.Format(@"<tr><td style=""width:1%;""></td>
<td style=""width:7%;height:20px;font-size:16px;font-weight:bold;text-align:left;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium"">Fund</td>
<td style=""width:10%;height:20px;font-size:16px;font-weight:bold;text-align:left;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium"">Project</td>");
            html += string.Format(@"
<td style=""width:3%;height:20px;font-size:16px;font-weight:bold;text-align:center;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium"">No.</td>
<td style=""width:8%;height:20px;font-size:16px;font-weight:bold;;text-align:right;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium;border-left-style:solid;border-left-width:thin;"">Total Cost</td>
<td style=""width:8%;height:20px;font-size:16px;font-weight:bold;;text-align:right;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium"">Realized</td>
<td style=""width:8%;height:20px;font-size:16px;font-weight:bold;;text-align:right;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium"">UnRealized</td>
<td style=""width:8%;height:20px;font-size:15px;font-weight:bold;;text-align:right;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium"">Total Value</td>
</tr>");
            //"<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;;border-top-style:solid;border-top-width:thin;border-left-style:solid;border-left-width:thin;"">Proceeds</td>
            //<td style=""width:7%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">Valuation</td>"
            return html;
        }


        protected static List<String> generateReport(DataTable performanceTable, DataTable investHistory, string PMName, decimal USDtoRMBRate, string summary, decimal Weight, string username = "")
        {
            List<String> listHtml = new List<String>();
            if (performanceTable == null || performanceTable.Rows.Count == 0 || investHistory == null || investHistory.Rows.Count == 0)
                return listHtml;
            String html = "";
            String summaryHtml = "";

            int totalNum = 0;
            decimal totalInvestmentCost = 0;
            decimal totalInvestmentLoan = 0;
            decimal totalInvestmentTotalCost = 0;
            decimal totalProceeds = 0;
            //decimal totalCostRealization = 0;
            decimal totalValuation = 0;
            decimal totalLoanRealization = 0;
            decimal YYUnRelized = 0;
            try
            {
                if (!string.IsNullOrEmpty(summary))
                {
                    double IRR = 0;
                    string Names = "";
                    var currencys = (from row in performanceTable.AsEnumerable()
                                     select row.Field<string>("currency")).Distinct();
                    foreach (var currency in currencys)
                    {
                        string filter = summary + @" and currency = '" + currency + "'";
                        DataRow[] performanceRows = performanceTable.Select(filter, "currency,fundFamillyName,abbName");
                        if (performanceRows != null && performanceRows.Count() > 0 && totalNum == 0)
                            html += addHeader(false);
                        int i = 0;
                        string str = "";
                        decimal totalInvestmentCostTemp = 0;
                        decimal totalInvestmentLoanTemp = 0;
                        decimal totalInvestmentTotalCostTemp = 0;
                        decimal totalProceedsTemp = 0;
                        decimal totalLoanRealizationTemp = 0;
                        decimal totalValuationTemp = 0;
                        decimal totalValuationForContribution = 0;
                        foreach (DataRow dr in performanceRows)
                        {
                            if (!Names.Contains(dr["abbName"].ToString()))
                            {
                                i++;
                                Names += dr["abbName"].ToString() + ",";
                                str = i.ToString();
                            }
                            else
                                str = "";

                            decimal spvOwnership = 1;
                            if (!String.IsNullOrEmpty(username))
                            {
                                DataTable dt = new DataTable();
                                Data_basic db = new Data_basic();
                                var contributionProjectID = dr["contributionProjectID"].ToString();
                                if (db.ExecuteDataset($"select percentage from Contribution where projectID = {contributionProjectID} AND username='{username}'" ) && db.AffectedRow == 1)
                                {
                                    dt = db.Dataset.Tables[0];
                                    DataRow drr = dt.Rows[0];
                                    filter += $" AND portfolioid='{dr["portfolioID"].ToString()}' ";
                                    spvOwnership = Convert.ToDecimal(drr["percentage"].ToString()) / 100;
                                } else
                                {
                                    continue;
                                }
                            }

                            
                            //decimal fundOwnerShip = 0;
                            //if (!string.IsNullOrEmpty(dr["fundOwnerShip"].ToString()))
                            //    fundOwnerShip = ChinaRound(decimal.Parse(dr["fundOwnerShip"].ToString()) * spvOwnership, 2);
                            decimal cost = ChinaRound(decimal.Parse(dr["cost"].ToString()) * spvOwnership, 0);
                            decimal totalCost = ChinaRound(decimal.Parse(dr["totalCost"].ToString()) * spvOwnership, 0);
                            decimal proceeds = ChinaRound(decimal.Parse(dr["proceeds"].ToString()) * spvOwnership, 0);
                            decimal costRelization = ChinaRound(decimal.Parse(dr["costRelization"].ToString()) * spvOwnership, 0);
                            decimal carryingCost = ChinaRound(decimal.Parse(dr["carryingCost"].ToString()) * spvOwnership, 0);
                            decimal Valuation = 0;
                            decimal loan = ChinaRound(decimal.Parse(dr["loan"].ToString()) * spvOwnership, 0);
                            decimal loanProceeds = ChinaRound(decimal.Parse(dr["loanProceeds"].ToString()) * spvOwnership, 0);
                            decimal loanRelization = ChinaRound(decimal.Parse(dr["loanRelization"].ToString()) * spvOwnership, 0);
                            #region valuation compute
                            decimal LatestBanyanTotalValuation = 0;
                            //if ("P03159" == dr["portfolioID"].ToString())
                            //    spvOwnership = 1;
                            decimal banyanSumSharesNo = Convert.ToDecimal(dr["shareOwnedNo"].ToString());
                            if (!(cost <= 0 && banyanSumSharesNo == 0) && dr["portfolioID"] != null && !string.IsNullOrEmpty(dr["portfolioID"].ToString()))
                            {
                                DataTable dt = new DataTable();
                                dt = getShareStructureTable(dr["portfolioID"].ToString());

                                if (dt != null && dt.Rows.Count > 0 && !(loan > 0 && banyanSumSharesNo == 0))//ignore no equity
                                    LatestBanyanTotalValuation = Xirr.Program.ValuationCompute(dt, dr["fundFamillyName"].ToString(), USDtoRMBRate, cost, currency, dr) * spvOwnership;
                            }
                            //else
                            //    Valuation = ChinaRound(decimal.Parse(dr["valuation"].ToString()), 0);
                            if (Valuation <= 0 || (Valuation <= totalCost && LatestBanyanTotalValuation > 0 && Math.Abs(LatestBanyanTotalValuation - Valuation) > 10000) && LatestBanyanTotalValuation > 10000)
                            {
                                Valuation = LatestBanyanTotalValuation;
                            }
                            if (carryingCost <= 0 && LatestBanyanTotalValuation == 0)
                            {
                                Valuation = 0;
                                carryingCost = 0;
                            }
                            if (Math.Abs(carryingCost - Valuation) < 100)
                            {
                                Valuation = carryingCost;
                            }
                            #endregion
                            //if (!string.IsNullOrEmpty(dr["valuation"].ToString()))
                            //    decimal.TryParse(dr["valuation"].ToString(), out Valuation);
                            decimal loanValuation = loan;
                            if (!string.IsNullOrEmpty(dr["loanValuation"].ToString()))
                                decimal.TryParse(dr["loanValuation"].ToString(), out loanValuation);
                            loanValuation *= spvOwnership;
                            //if (Valuation == 0 && loanValuation == 0 && carryingCost == 0 && totalCost == 0 && proceeds == 0 && loanProceeds == 0)
                            //    continue ;

                            //    @"<td style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman'"">{3}</td>
                            //<td style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman'"">{4}</td>"
                            decimal Realized = proceeds + loanProceeds;
                            decimal UnRealized = Valuation + loanValuation;
                            if (dr["abbName"].ToString() == "慧科" && currency == "CNY")
                            {
                                totalCost -= loanRelization;
                                Realized -= loanRelization;
                            }
                            if (dr["abbName"].ToString() == "YY" && currency == "USD")
                            {
                                YYUnRelized = UnRealized;
                                continue;
                            }
                            if (dr["abbName"].ToString() == "微会（BIGO）" && currency == "USD")
                            {
                                UnRealized += YYUnRelized;
                            }
                            //if (!string.IsNullOrEmpty(dr["IRR"].ToString()))
                            //    IRR = ChinaRound(decimal.Parse(dr["IRR"].ToString()), 0);
                            string portfolioFilter = filter;
                            if (!string.IsNullOrEmpty(dr["portfolioID"].ToString()))
                                portfolioFilter += " and portfolioID = '" + dr["portfolioID"].ToString() + "'";
                            if (!string.IsNullOrEmpty(dr["fundFamillyName"].ToString()))
                                portfolioFilter += " and fundFamillyName = '" + dr["fundFamillyName"].ToString() + "'";
                            if (investHistory.Select(portfolioFilter, "").Length > 0)
                                IRR = 100 * Xirr.Program.IRRCal(investHistory.Select(portfolioFilter, ""), DateTime.Now, double.Parse((UnRealized).ToString())/(double)spvOwnership);
                            string DPI = totalCost == 0 ? "" : string.Format("{0:N2}", ChinaRound(Realized / (totalCost), 2));
                            string mutipiles = totalCost == 0 || Realized + UnRealized == 0 ? "-" : string.Format("{0:N1}", ChinaRound((Realized + UnRealized) / (totalCost), 1));
                            html += string.Format(@"<tr style=""{0}""><td></td>
                    <td style=""font-size:16px;font-family:'Times New Roman'"">{1}</td>
                    <td style=""font-size:16px;font-family:'Times New Roman'"">{2}{3}</td>
<td style=""text-align:center;font-size:16px;height:20px;font-family:'Times New Roman'"">{4}</td>
                    <td style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman';border-left-style:solid;border-left-width:thin;"">{5}</td>
                    <td style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman';"">{6}</td>
                    <td style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman'"">{7}</td>
                    <td style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman'"">{8}</td>
 </tr>",
    currency == "CNY" ? "" : "background-color: lightgray;", dr["fundFamillyName"].ToString(), dr["abbName"].ToString().Replace("（跟谁学拆分）", "").Replace("（美利金融分拆）", "").Replace("（度周末补偿）", ""), "", str,
    //cost + costRelization == 0 ? "" : string.Format("{0:N0}", cost + costRelization),loan + loanRelization == 0 ? "" : string.Format("{0:N0}", loan + loanRelization),
    totalCost == 0 ? "-" : string.Format("{0:N0}", totalCost), Realized == 0 ? "-" : string.Format("{0:N0}", Realized),
    UnRealized == 0 ? "-" : string.Format("{0:N0}", UnRealized), Realized + UnRealized == 0 ? "-" : string.Format("{0:N0}", Realized + UnRealized),

    Realized + UnRealized - totalCost == 0 ? "-" : string.Format("{0:N0}", Realized + UnRealized - totalCost),
    mutipiles, Math.Abs(IRR) < 0.1 ? "" : string.Format("{0:N1}", ChinaRound((decimal)IRR, 1)) + "%", DPI == "0.00" || DPI == "" ? "-" : DPI);

                            totalInvestmentCost += cost + costRelization;
                            //totalInvestmentLoan += loan;
                            totalLoanRealization += loanRelization;
                            totalInvestmentTotalCost += totalCost;
                            totalProceeds += Realized;
                            totalValuation += UnRealized;
                            if (currency == "USD")
                            {
                                totalInvestmentCost += (cost + costRelization) * (USDtoRMBRate - 1);
                                //totalInvestmentLoan += (loan) * (USDtoRMBRate - 1);
                                totalLoanRealization += loanRelization * (USDtoRMBRate - 1);
                                totalInvestmentTotalCost += totalCost * (USDtoRMBRate - 1);
                                totalProceeds += (Realized) * (USDtoRMBRate - 1);
                                totalValuation += (UnRealized) * (USDtoRMBRate - 1);
                            }

                            totalInvestmentCostTemp += cost + costRelization;
                            totalInvestmentLoanTemp += loan;
                            totalLoanRealizationTemp += loanRelization;
                            totalInvestmentTotalCostTemp += totalCost;
                            totalProceedsTemp += Realized;
                            totalValuationTemp += UnRealized;
                            totalValuationForContribution += UnRealized / spvOwnership;
                        }

                        IRR = 100 * Xirr.Program.IRRCal(investHistory.Select(filter, ""), DateTime.Now, double.Parse((totalValuationForContribution).ToString()));
                        html += i == 0 ? "" : string.Format(@"<tr style=""{0}""><td></td>                   
 <td colspan=""2"" style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman'"">{1}{2}</td>
<td style=""text-align:center;font-size:16px;height:20px;font-family:'Times New Roman'"">{3}</td>
 <td style=""border-bottom-style:solid;border-bottom-width:thin;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right;border-left-style:solid;border-left-width:thin;"">{4}</td>
                    <td style=""border-bottom-style:solid;border-bottom-width:thin;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{5}</td>
                    <td style=""border-bottom-style:solid;border-bottom-width:thin;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{6}</td>
                    <td style=""border-bottom-style:solid;border-bottom-width:thin;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{7}</td>
 </tr>",
                        currency == "CNY" ? "" : "background-color: lightgray;", "", "", currency,
           totalInvestmentTotalCostTemp == 0 ? "-" : string.Format("{0:N0}", ChinaRound(totalInvestmentTotalCostTemp, 0)),
           totalProceedsTemp == 0 ? "-" : string.Format("{0:N0}", ChinaRound(totalProceedsTemp, 0)),
           totalValuationTemp == 0 ? "-" : string.Format("{0:N0}", ChinaRound(totalValuationTemp, 0)),
            totalProceedsTemp + totalValuationTemp == 0 ? "-" : string.Format("{0:N0}", ChinaRound(totalProceedsTemp + totalValuationTemp, 0)),

    totalProceedsTemp + totalValuationTemp - (totalInvestmentTotalCostTemp) == 0 ? "-" : string.Format("{0:N0}", ChinaRound(totalProceedsTemp + totalValuationTemp - (totalInvestmentTotalCostTemp), 0)),
    totalInvestmentTotalCostTemp == 0 ? "-" : string.Format("{0:N1}", ChinaRound((totalProceedsTemp + totalValuationTemp) / (totalInvestmentTotalCostTemp), 1)),
     Math.Abs(IRR) < 0.1 ? "" : string.Format("{0:N1}", IRR) + "%",
    totalProceedsTemp + totalInvestmentTotalCostTemp == 0 || totalProceedsTemp == 0 ? "-" :
    string.Format("{0:N2}", ChinaRound(totalProceedsTemp / (totalInvestmentTotalCostTemp), 2)) == "0.00" ? "-" : string.Format("{0:N2}", ChinaRound(totalProceedsTemp / (totalInvestmentTotalCostTemp), 2)));
                        //string.Format("{0:N0}", ChinaRound(totalInvestmentCostTemp, 0)),totalInvestmentLoanTemp + totalLoanRealizationTemp == 0 ? "" : string.Format("{0:N0}", ChinaRound(totalInvestmentLoanTemp + totalLoanRealizationTemp, 0)),
                        //@"                    <td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{2}</td>
                        //                    <td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{3}</td>"
                        totalNum += i;
                    }
                    double IRRTotal = 0;
                    //RMB end valuation
                    IRRTotal = 100 * Xirr.Program.IRRCal(investHistory.Select(summary, ""), DateTime.Now, double.Parse((totalValuation).ToString()), (double)USDtoRMBRate);
                    summaryHtml = totalNum == 0 ? "" : string.Format(@"<tr style=""""><td></td>               
                    <td colspan=""2"" style=""border-bottom-style:solid;border-bottom-width:thin;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{1}{2}</td>
<td style=""text-align:center;font-size:16px;height:20px;font-family:'Times New Roman';border-bottom-style:solid;border-bottom-width:thin;"">{0}</td>
<td style=""border-bottom-style:solid;border-bottom-width:thin;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right;border-left-style:solid;border-left-width:thin;"">{3}</td>
                    <td style=""border-bottom-style:solid;border-bottom-width:thin;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{4}</td>
                    <td style=""border-bottom-style:solid;border-bottom-width:thin;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{5}</td>
                    <td style=""border-bottom-style:solid;border-bottom-width:thin;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{6}</td>
</tr>"
            , (totalNum).ToString(), PMName, "汇总（CNY）",
    //string.Format("{0:N0}", ChinaRound(totalInvestmentCost, 0)), totalInvestmentLoan + totalLoanRealization == 0 ? "" : string.Format("{0:N0}", ChinaRound(totalInvestmentLoan + totalLoanRealization, 0)),
    //@"                    <td style=""text-align:center;font-size:16px;height:20px;font-family:'Times New Roman';border-bottom-style:double;border-bottom-width:medium;"">{1}</td>    
    //<td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{1}</td>
    //                    <td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{2}</td>"
    totalInvestmentTotalCost == 0 ? "-" : string.Format("{0:N0}", ChinaRound(totalInvestmentTotalCost, 0)), totalProceeds == 0 ? "-" : string.Format("{0:N0}", ChinaRound(totalProceeds, 0)),
    totalValuation == 0 ? "-" : string.Format("{0:N0}", ChinaRound(totalValuation, 0)), string.Format("{0:N0}", ChinaRound(totalProceeds + totalValuation, 0)),

    totalProceeds + totalValuation - (totalInvestmentTotalCost) == 0 ? "-" : string.Format("{0:N0}", ChinaRound(totalProceeds + totalValuation - totalInvestmentTotalCost, 0)),
    totalInvestmentTotalCost == 0 || totalProceeds + totalValuation == 0 ? "-" : string.Format("{0:N1}", ChinaRound((totalProceeds + totalValuation) / (totalInvestmentTotalCost), 1)),
    (IRRTotal < 0.01 && IRRTotal > -0.01) ? "" : string.Format("{0:N1}", ChinaRound((decimal)IRRTotal, 1)) + "%",
    totalInvestmentCost == 0 || totalProceeds == 0 ? "-" : string.Format("{0:N2}", ChinaRound(totalProceeds / (totalInvestmentTotalCost), 2)) == "0.00" ? "-" : string.Format("{0:N2}", ChinaRound(totalProceeds / (totalInvestmentTotalCost), 2)));//PMName == "负责投资项目" ? "font-weight: bold;" : "");
                    html += summaryHtml;
                    //                summaryHtml = totalNum == 0 ? "" : string.Format(@"<tr>              
                    //                    <td colspan=""2"" style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{1}{2}</td>
                    //<td style=""text-align:center;font-size:16px;height:20px;font-family:'Times New Roman';border-bottom-style:double;border-bottom-width:medium;"">{0}</td>
                    //<td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right;border-left-style:solid;border-left-width:thin;"">{3}</td>
                    //                    <td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{4}</td>
                    //                    <td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{5}</td>
                    //                    <td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{6}</td>

                    //                    <td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:center;border-left-style:solid;border-left-width:thin;"">{7}</td>
                    //                    <td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{8}</td>
                    //                    <td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{9}</td>
                    //                    <td></td></tr>"
                    //, (totalNum).ToString(), PMName, "汇总（CNY）",

                    //string.Format("{0:N0}", ChinaRound(totalInvestmentTotalCost * Weight, 0)), string.Format("{0:N0}", ChinaRound(totalProceeds * Weight, 0)),
                    //string.Format("{0:N0}", ChinaRound(totalValuation * Weight, 0)), string.Format("{0:N0}", ChinaRound((totalProceeds + totalValuation) * Weight, 0)),

                    //totalInvestmentTotalCost == 0 ? "" : string.Format("{0:N1}", ChinaRound((totalProceeds + totalValuation) / (totalInvestmentTotalCost), 1)),
                    //totalProceeds + totalValuation - totalInvestmentTotalCost == 0 ? "" : string.Format("{0:N0}", ChinaRound((totalProceeds + totalValuation - totalInvestmentTotalCost) * Weight, 0)),
                    //(IRRTotal < 0.01 && IRRTotal > -0.01) ? "" : string.Format("{0:N2}", IRRTotal) + "%");
                }

                listHtml.Add(summaryHtml);
                listHtml.Add(html);
                listHtml.Add(string.Format("{0:N0}", ChinaRound((totalProceeds + totalValuation - totalInvestmentTotalCost) * Weight, 0)));
                return listHtml;
            }
            catch (Exception e)
            {
                listHtml.Add(summaryHtml);
                listHtml.Add(html);
                listHtml.Add(string.Format("{0:N0}", ChinaRound((totalProceeds + totalValuation - totalInvestmentTotalCost) * Weight, 0)));
                return listHtml;
            }
        }

        protected static string generateReport(DataTable performanceTable, DataTable investHistory, string PMName, decimal USDtoRMBRate, decimal PManagerWeight, decimal PMemberWeight, decimal recommendWeight, decimal PIManagerWeight, decimal PIMemberWeight)
        {
            if (performanceTable == null || performanceTable.Rows.Count == 0 || investHistory == null || investHistory.Rows.Count == 0)
                return "";
            string html = "";

            try
            {
                int totalNum = 0;
                decimal totalInvestmentCost = 0;
                decimal totalInvestmentLoan = 0;
                decimal totalInvestmentTotalCost = 0;
                decimal totalProceeds = 0;
                //decimal totalCostRealization = 0;
                decimal totalValuation = 0;
                decimal totalLoanRealization = 0;
                double IRR = 0;
                decimal totalWeightMarkUp = 0;
                var currencys = (from row in performanceTable.AsEnumerable()
                                 select row.Field<string>("currency")).Distinct();
                string Names = "";
                foreach (var currency in currencys)
                {
                    string filter = @" currency = '" + currency + "'";
                    DataRow[] performanceRows = performanceTable.Select(filter, "currency,fundFamillyName,abbName");
                    if (performanceRows != null && performanceRows.Count() > 0 && totalNum == 0)
                        html += string.Format(@"<tr><td style=""width:1%;""></td>
<td style=""width:8%;height:20px;font-size:18px;font-weight:bold;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">Fund</td>
<td style=""width:8%;height:20px;font-size:18px;font-weight:bold;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">Project</td>
<td style=""width:2%;height:20px;font-size:18px;font-weight:bold;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">No.</td>
<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin;border-left-style:solid;border-left-width:thin;"">Total Cost</td>
<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">Realized</td>
<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">UnRealized</td>
<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">Total Value</td>

<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;;border-top-style:solid;border-top-width:thin;border-left-style:solid;border-left-width:thin;"">Multiples</td>
<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin""> Mark-up </td>
<td style=""width:4%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">IRR</td>
<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;;border-top-style:solid;border-top-width:thin;border-left-style:solid;border-left-width:thin;"">贡献价值</td>
<td style=""width:3%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">权重</td>
<td style=""width:4%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">项目负责人</td>
<td style=""width:4%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">项目来源</td>
<td style=""width:4%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">项目组成员</td>
<td style=""width:4%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">投后负责人</td>
<td style=""width:4%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">投后组成员</td>
</tr>");
                    int i = 0;
                    string str = "";
                    decimal totalInvestmentCostTemp = 0;
                    decimal totalInvestmentLoanTemp = 0;
                    decimal totalInvestmentTotalCostTemp = 0;
                    decimal totalProceedsTemp = 0;
                    decimal totalLoanRealizationTemp = 0;
                    decimal totalValuationTemp = 0;
                    decimal totalWeightMarkUpTemp = 0;
                    foreach (DataRow dr in performanceRows)
                    {
                        if (!Names.Contains(dr["abbName"].ToString()))
                        {
                            i++;
                            Names += dr["abbName"].ToString() + ",";
                            str = i.ToString();
                        }
                        else
                            str = "";
                        decimal spvOwnership = 1;
                        //decimal fundOwnerShip = 0;
                        //if (!string.IsNullOrEmpty(dr["fundOwnerShip"].ToString()))
                        //    fundOwnerShip = ChinaRound(decimal.Parse(dr["fundOwnerShip"].ToString()) * spvOwnership, 2);
                        decimal cost = ChinaRound(decimal.Parse(dr["cost"].ToString()) * spvOwnership, 0);
                        decimal totalCost = ChinaRound(decimal.Parse(dr["totalCost"].ToString()) * spvOwnership, 0);
                        decimal proceeds = ChinaRound(decimal.Parse(dr["proceeds"].ToString()) * spvOwnership, 0);
                        decimal costRelization = ChinaRound(decimal.Parse(dr["costRelization"].ToString()) * spvOwnership, 0);
                        decimal carryingCost = ChinaRound(decimal.Parse(dr["carryingCost"].ToString()) * spvOwnership, 0);
                        decimal Valuation = 0;
                        decimal loan = ChinaRound(decimal.Parse(dr["loan"].ToString()) * spvOwnership, 0);
                        decimal loanProceeds = ChinaRound(decimal.Parse(dr["loanProceeds"].ToString()) * spvOwnership, 0);
                        decimal loanRelization = ChinaRound(decimal.Parse(dr["loanRelization"].ToString()) * spvOwnership, 0);
                        #region valuation compute
                        decimal LatestBanyanTotalValuation = 0;

                        if (cost > 0 && dr["portfolioID"] != null && !string.IsNullOrEmpty(dr["portfolioID"].ToString()))
                        {
                            DataTable dt = new DataTable();
                            dt = getShareStructureTable(dr["portfolioID"].ToString());
                            decimal banyanSumSharesNo = Convert.ToDecimal(dr["shareOwnedNo"].ToString());
                            if (dt != null && dt.Rows.Count > 0 && !(loan > 0 && banyanSumSharesNo == 0))//ignore no equity
                                LatestBanyanTotalValuation = Xirr.Program.ValuationCompute(dt, dr["fundFamillyName"].ToString(), USDtoRMBRate, cost, currency, dr);
                        }
                        //else
                        //    Valuation = ChinaRound(decimal.Parse(dr["valuation"].ToString()), 0);
                        if (Valuation <= 0 || (Valuation <= totalCost && LatestBanyanTotalValuation > 0 && Math.Abs(LatestBanyanTotalValuation - Valuation) > 10000) && LatestBanyanTotalValuation > 10000)
                        {
                            Valuation = LatestBanyanTotalValuation;
                        }
                        if (carryingCost <= 0)
                        {
                            Valuation = 0;
                            carryingCost = 0;
                        }
                        if (Math.Abs(carryingCost - Valuation) < 100)
                        {
                            Valuation = carryingCost;
                        }
                        #endregion
                        //if (!string.IsNullOrEmpty(dr["valuation"].ToString()))
                        //    decimal.TryParse(dr["valuation"].ToString(), out Valuation);
                        decimal loanValuation = loan;
                        if (!string.IsNullOrEmpty(dr["loanValuation"].ToString()))
                            decimal.TryParse(dr["loanValuation"].ToString(), out loanValuation);
                        //if (!string.IsNullOrEmpty(dr["IRR"].ToString()))
                        //    IRR = ChinaRound(decimal.Parse(dr["IRR"].ToString()), 0);
                        string portfolioFilter = filter;
                        if (!string.IsNullOrEmpty(dr["portfolioID"].ToString()))
                            portfolioFilter += " and portfolioID = '" + dr["portfolioID"].ToString() + "'";
                        if (!string.IsNullOrEmpty(dr["fundFamillyName"].ToString()))
                            portfolioFilter += " and fundFamillyName = '" + dr["fundFamillyName"].ToString() + "'";
                        if (investHistory.Select(portfolioFilter, "").Length > 0)
                            IRR = 100 * Xirr.Program.IRRCal(investHistory.Select(portfolioFilter, ""), DateTime.Now, double.Parse((Valuation + loanValuation).ToString()));

                        decimal weight = 0;
                        weight = WeightCal(dr, PMName, PManagerWeight, PMemberWeight, recommendWeight, PIManagerWeight, PIMemberWeight);

                        html += string.Format(@"<tr><td style=""width:1%;""></td>
                    <td style=""font-size:16px;font-family:'Times New Roman'"">{1}</td>
                    <td style=""font-size:16px;font-family:'Times New Roman'"">{2}{3}{4}</td>
<td style=""text-align:center;font-size:16px;height:20px;font-family:'Times New Roman'"">{0}</td>
                    <td style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman';border-left-style:solid;border-left-width:thin;"">{5}</td>
                    <td style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman';"">{6}</td>
                    <td style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman'"">{7}</td>
                    <td style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman'"">{8}</td>

                    <td style=""text-align:center;font-size:16px;height:20px;font-family:'Times New Roman';border-left-style:solid;border-left-width:thin;"">{9}</td>
                    <td style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman'"">{10}</td>
                    <td style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman'"">{11}</td>
<td style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman';border-left-style:solid;border-left-width:thin;""><strong>{12}</strong></td>
                    <td style=""text-align:center;font-size:16px;height:20px;font-family:'Times New Roman'"">{13}</td>
                    <td style=""text-align:center;font-size:16px;height:20px;font-family:'Times New Roman'"">{14}</td>
                    </tr>",
    str, dr["fundFamillyName"].ToString(), dr["abbName"].ToString(), "", "",
    //cost + costRelization == 0 ? "" : string.Format("{0:N0}", cost + costRelization),loan + loanRelization == 0 ? "" : string.Format("{0:N0}", loan + loanRelization),
    totalCost == 0 ? "" : string.Format("{0:N0}", totalCost),
    proceeds + loanProceeds == 0 ? "" : string.Format("{0:N0}", proceeds + loanProceeds),
    Valuation + loanValuation == 0 ? "-" : string.Format("{0:N0}", Valuation + loanValuation),
    proceeds + Valuation + loanProceeds + loanValuation == 0 ? "" : string.Format("{0:N0}", proceeds + Valuation + loanProceeds + loanValuation),

    (totalCost) != 0 ? string.Format("{0:N1}", (Valuation + proceeds + loanProceeds + loanValuation) / (totalCost)) : "",
    Valuation + proceeds + loanValuation - totalCost + loanProceeds == 0 ? "" : string.Format("{0:N0}", Valuation + proceeds + loanProceeds + loanValuation - totalCost),
    Math.Abs(IRR) < 0.1 ? "" : string.Format("{0:N2}", IRR) + "%",
    Valuation + proceeds + loanValuation - totalCost + loanProceeds == 0 ? "" : string.Format("{0:N0}", (Valuation + proceeds + loanProceeds + loanValuation - totalCost) * weight),
    string.Format("{0:N1}", weight * 100) + "%",
    dr["portfolioManager"].ToString(), dr["portfolioIntroducer"].ToString(), dr["groupMember"].ToString(), dr["postInvestManager"].ToString(), dr["postInvestMember"].ToString());

                        totalInvestmentCost += cost + costRelization;
                        //totalInvestmentLoan += loan;
                        totalLoanRealization += loanRelization;
                        totalInvestmentTotalCost += totalCost;
                        totalProceeds += proceeds + loanProceeds;
                        totalValuation += Valuation + loanValuation;
                        totalWeightMarkUp += (Valuation + proceeds + loanProceeds + loanValuation - totalCost) * weight;
                        if (currency == "USD")
                        {
                            totalInvestmentCost += (cost + costRelization) * (USDtoRMBRate - 1);
                            //totalInvestmentLoan += (loan) * (USDtoRMBRate - 1);
                            totalLoanRealization += loanRelization * (USDtoRMBRate - 1);
                            totalInvestmentTotalCost += totalCost * (USDtoRMBRate - 1);
                            totalProceeds += (proceeds + loanProceeds) * (USDtoRMBRate - 1);
                            totalValuation += (Valuation + loanValuation) * (USDtoRMBRate - 1);
                            totalWeightMarkUp += (Valuation + proceeds + loanProceeds + loanValuation - totalCost) * weight * (USDtoRMBRate - 1);
                        }

                        totalInvestmentCostTemp += cost + costRelization;
                        //totalInvestmentLoanTemp += loan;
                        totalLoanRealizationTemp += loanRelization;
                        totalInvestmentTotalCostTemp += totalCost;
                        totalProceedsTemp += proceeds + loanProceeds;
                        totalValuationTemp += Valuation + loanValuation;
                        totalWeightMarkUpTemp += (Valuation + proceeds + loanProceeds + loanValuation - totalCost) * weight;
                    }

                    IRR = 100 * Xirr.Program.IRRCal(investHistory.Select(filter, ""), DateTime.Now, double.Parse((totalValuationTemp).ToString()));
                    html += i == 0 ? "" : string.Format(@"<tr><td style=""width:1%;""></td>                   
                    <td colspan=""2"" style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman'"">{1}{2}{3}</td>
                    <td style=""text-align:center;font-size:16px;height:20px;font-family:'Times New Roman'"">{0}</td>
                    <td style=""border-bottom-style:double;border-bottom-width:thin;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right;border-left-style:solid;border-left-width:thin;"">{4}</td>
                    <td style=""border-bottom-style:double;border-bottom-width:thin;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{5}</td>
                    <td style=""border-bottom-style:double;border-bottom-width:thin;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{6}</td>
                    <td style=""border-bottom-style:double;border-bottom-width:thin;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{7}</td>
                   </tr>", currency, "", "", "",
        //string.Format("{0:N0}", ChinaRound(totalInvestmentCostTemp, 0)),totalInvestmentLoanTemp + totalLoanRealizationTemp == 0 ? "" : string.Format("{0:N0}", ChinaRound(totalInvestmentLoanTemp + totalLoanRealizationTemp, 0)),
        //@"                    <td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{2}</td>
        //                    <td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{3}</td>"

        totalInvestmentTotalCostTemp == 0 ? "" : string.Format("{0:N0}", ChinaRound(totalInvestmentTotalCostTemp, 0)),
        totalProceedsTemp == 0 ? "" : string.Format("{0:N0}", ChinaRound(totalProceedsTemp, 0)),
        totalValuationTemp == 0 ? "" : string.Format("{0:N0}", ChinaRound(totalValuationTemp, 0)),
        string.Format("{0:N0}", ChinaRound(totalProceedsTemp + totalValuationTemp, 0)),

    (totalInvestmentTotalCostTemp) == 0 ? "" : string.Format("{0:N1}", ChinaRound((totalProceedsTemp + totalValuationTemp) / (totalInvestmentTotalCostTemp), 1)),
    totalProceedsTemp + totalValuationTemp - (totalInvestmentTotalCostTemp) == 0 ? "" : string.Format("{0:N0}", ChinaRound(totalProceedsTemp + totalValuationTemp - (totalInvestmentTotalCostTemp), 0)),
        Math.Abs(IRR) < 0.1 ? "" : string.Format("{0:N2}", IRR) + "%", string.Format("{0:N0}", ChinaRound(totalWeightMarkUpTemp, 0)));

                    totalNum += i;
                }
                double IRRTotal = 0;
                //RMB end valuation
                IRRTotal = 100 * Xirr.Program.IRRCal(investHistory.Select("", ""), DateTime.Now, double.Parse((totalValuation).ToString()), (double)USDtoRMBRate);
                html += totalNum == 0 ? "" : string.Format(@"<tr><td style=""width:1%;""></td>               
                    <td colspan=""2"" style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{1}{2}</td>
<td style=""text-align:center;font-size:16px;height:20px;font-family:'Times New Roman';border-bottom-style:double;border-bottom-width:medium;"">{0}</td>
<td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right;border-left-style:solid;border-left-width:thin;"">{3}</td>
                    <td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{4}</td>
                    <td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{5}</td>
                    <td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{6}</td>
                    </tr>"
        , (totalNum).ToString(), PMName, "汇总（CNY）",
    //string.Format("{0:N0}", ChinaRound(totalInvestmentCost, 0)), totalInvestmentLoan + totalLoanRealization == 0 ? "" : string.Format("{0:N0}", ChinaRound(totalInvestmentLoan + totalLoanRealization, 0)),
    //@"                    <td style=""text-align:center;font-size:16px;height:20px;font-family:'Times New Roman';border-bottom-style:double;border-bottom-width:medium;"">{1}</td>    
    //<td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{1}</td>
    //                    <td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{2}</td>"
    totalInvestmentTotalCost == 0 ? "" : string.Format("{0:N0}", ChinaRound(totalInvestmentTotalCost, 0)), totalProceeds == 0 ? "" : string.Format("{0:N0}", ChinaRound(totalProceeds, 0)),
    totalValuation == 0 ? "" : string.Format("{0:N0}", ChinaRound(totalValuation, 0)), string.Format("{0:N0}", ChinaRound(totalProceeds + totalValuation, 0)),

    totalInvestmentTotalCost == 0 || totalProceeds + totalValuation == 0 ? "" : string.Format("{0:N1}", ChinaRound((totalProceeds + totalValuation) / (totalInvestmentTotalCost), 1)),
    totalProceeds + totalValuation - totalInvestmentTotalCost == 0 ? "" : string.Format("{0:N0}", ChinaRound(totalProceeds + totalValuation - totalInvestmentTotalCost, 0)),
    (IRRTotal < 0.01 && IRRTotal > -0.01) ? "" : string.Format("{0:N2}", IRRTotal) + "%", string.Format("{0:N0}", ChinaRound(totalWeightMarkUp, 0)));

                return html;
            }
            catch (Exception)
            {
                //html += @"</table></div>";
                return html;
            }
        }
        protected static decimal WeightCal(DataRow dr, string PMName, decimal PManagerWeight, decimal PMemberWeight, decimal recommendWeight, decimal PIManagerWeight, decimal PIMemberWeight)
        {
            decimal weight = 0;
            decimal myWeight = 0;
            decimal totalWeight = 0;

            if (!string.IsNullOrEmpty(dr["portfolioManager"].ToString()))
            {
                totalWeight += PManagerWeight;
                if (dr["portfolioManager"].ToString().Contains(PMName))
                {
                    if (dr["portfolioManager"].ToString().Split(',').Count() > 1)
                        myWeight += PManagerWeight / dr["portfolioManager"].ToString().Split(',').Count();
                    else
                        myWeight += PManagerWeight;
                }
            }
            if (!string.IsNullOrEmpty(dr["portfolioIntroducer"].ToString()))
            {
                totalWeight += recommendWeight;
                if (dr["portfolioIntroducer"].ToString().Contains(PMName))
                {
                    if (dr["portfolioIntroducer"].ToString().Split(',').Count() > 1)
                        myWeight += recommendWeight / dr["portfolioIntroducer"].ToString().Split(',').Count();
                    else
                        myWeight += recommendWeight;
                }
            }
            if (!string.IsNullOrEmpty(dr["groupMember"].ToString()))
            {
                totalWeight += PMemberWeight;
                if (dr["groupMember"].ToString().Contains(PMName))
                {
                    if (dr["groupMember"].ToString().Split(',').Count() > 1)
                        myWeight += PMemberWeight / dr["groupMember"].ToString().Split(',').Count();
                    else
                        myWeight += PMemberWeight;
                }
            }
            if (!string.IsNullOrEmpty(dr["postInvestManager"].ToString()))
            {
                totalWeight += PIManagerWeight;
                if (dr["postInvestManager"].ToString().Contains(PMName))
                {
                    if (dr["postInvestManager"].ToString().Split(',').Count() > 1)
                        myWeight += PIManagerWeight / dr["postInvestManager"].ToString().Split(',').Count();
                    else
                        myWeight += PIManagerWeight;
                }
            }
            if (!string.IsNullOrEmpty(dr["postInvestMember"].ToString()))
            {
                totalWeight += PIMemberWeight;
                if (dr["postInvestMember"].ToString().Contains(PMName))
                {
                    if (dr["postInvestMember"].ToString().Split(',').Count() > 1)
                        myWeight += PIMemberWeight / dr["postInvestMember"].ToString().Split(',').Count();
                    else
                        myWeight += PIMemberWeight;
                }
            }
            if (totalWeight != 0)
                weight = myWeight / totalWeight;
            return weight;
        }
    }
}
