﻿using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.BusinessCenterPortal)]
    public class Project_ActiveStatus
    {
        [SqlField(IsPrimaryKey = true)]
        public int statusID { get; set; }

        [SqlField]
        public string projectID { get; set; } = string.Empty;

        [SqlField]
        public string discussType { get; set; } = string.Empty;

        [SqlField]
        public string discussContent { get; set; } = string.Empty;
        [SqlField]
        public DateTime dicussDate { get; set; } = DateTime.Now;
        /// <summary>
        /// 项目最后编辑时间
        /// </summary>
        [SqlField]
        public DateTime modifiedDate { get; set; } = DateTime.Now;
        /// <summary>
        /// 创建时间
        /// </summary>
        [SqlField]
        public DateTime createdDate { get; set; } = DateTime.Now;
    }

}
