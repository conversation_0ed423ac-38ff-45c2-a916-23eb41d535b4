﻿namespace Banyan.Code
{
    public class DBConnection
    {
        public static bool Encrypt { get; set; }

        public DBConnection(bool encrypt)
        {
            Encrypt = encrypt;
        }

        public static string connectionString
        {
            get
            {
                string connection = System.Configuration.ConfigurationManager.ConnectionStrings["BanyanDbContext"].ConnectionString;
                if (Encrypt == true)
                {
                    return DESEncrypt.Decrypt(connection);
                }
                else
                {
                    return connection;
                }
            }
        }
    }
}
