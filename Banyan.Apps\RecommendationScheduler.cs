using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Configuration;
using Banyan.Code;
using Banyan.Domain;

namespace Banyan.Apps
{
    /// <summary>
    /// 推荐调度器
    /// 负责定期更新用户的推荐内容，确保推荐结果的时效性
    /// </summary>
    public class RecommendationScheduler
    {
        private readonly NewsRecommendationEngine _recommendationEngine;
        private readonly RecommendationCacheManager _cacheManager;
        private readonly UserInterestVectorRetrieval _userInterestRetrieval;
        private readonly MemberBLL _memberBLL;
        private readonly ICache _cache;
        
        // 默认配置
        private const int DEFAULT_BATCH_SIZE = 10;
        private const int DEFAULT_UPDATE_INTERVAL_HOURS = 6; // 根据需求文档，每6小时更新一次
        private const int DEFAULT_MAX_RETRY_COUNT = 3;
        private const int DEFAULT_RETRY_DELAY_SECONDS = 60;
        
        // 调度器状态
        private bool _isRunning = false;
        private DateTime _lastRunTime = DateTime.MinValue;
        private CancellationTokenSource _cancellationTokenSource;
        
        // 单例实例
        private static readonly Lazy<RecommendationScheduler> _instance = 
            new Lazy<RecommendationScheduler>(() => new RecommendationScheduler(), LazyThreadSafetyMode.ExecutionAndPublication);
        
        /// <summary>
        /// 获取RecommendationScheduler的单例实例
        /// </summary>
        public static RecommendationScheduler Instance => _instance.Value;
        
        /// <summary>
        /// 私有构造函数，确保单例模式
        /// </summary>
        private RecommendationScheduler()
        {
            _recommendationEngine = new NewsRecommendationEngine();
            _cacheManager = RecommendationCacheManager.Instance;
            _userInterestRetrieval = new UserInterestVectorRetrieval();
            _memberBLL = new MemberBLL();
            _cache = CacheFactory.Cache();
            
            Logger.Info("RecommendationScheduler initialized");
        }
        
        /// <summary>
        /// 获取调度器当前状态
        /// </summary>
        public bool IsRunning => _isRunning;
        
        /// <summary>
        /// 获取上次运行时间
        /// </summary>
        public DateTime LastRunTime => _lastRunTime;
        
        /// <summary>
        /// 启动推荐调度器
        /// </summary>
        /// <param name="updateIntervalHours">更新间隔（小时）</param>
        /// <returns>是否成功启动</returns>
        public bool Start(int updateIntervalHours = DEFAULT_UPDATE_INTERVAL_HOURS)
        {
            if (_isRunning)
            {
                Logger.Warn("RecommendationScheduler is already running");
                return false;
            }
            
            try
            {
                _isRunning = true;
                _cancellationTokenSource = new CancellationTokenSource();
                
                // 启动后台任务
                Task.Run(async () =>
                {
                    Logger.Info($"RecommendationScheduler started with update interval: {updateIntervalHours} hours");
                    
                    while (!_cancellationTokenSource.Token.IsCancellationRequested)
                    {
                        try
                        {
                            // 执行推荐更新
                            await UpdateAllRecommendationsAsync();
                            _lastRunTime = DateTime.Now;
                            
                            // 等待指定的时间间隔
                            await Task.Delay(TimeSpan.FromHours(updateIntervalHours), _cancellationTokenSource.Token);
                        }
                        catch (TaskCanceledException)
                        {
                            // 任务被取消，退出循环
                            break;
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"Error in RecommendationScheduler background task: {ex.Message}", ex);
                            
                            // 出错后等待一段时间再重试
                            try
                            {
                                await Task.Delay(TimeSpan.FromMinutes(5), _cancellationTokenSource.Token);
                            }
                            catch (TaskCanceledException)
                            {
                                break;
                            }
                        }
                    }
                    
                    _isRunning = false;
                    Logger.Info("RecommendationScheduler stopped");
                });
                
                return true;
            }
            catch (Exception ex)
            {
                _isRunning = false;
                Logger.Error($"Failed to start RecommendationScheduler: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 停止推荐调度器
        /// </summary>
        /// <returns>是否成功停止</returns>
        public bool Stop()
        {
            if (!_isRunning)
            {
                Logger.Warn("RecommendationScheduler is not running");
                return false;
            }
            
            try
            {
                _cancellationTokenSource?.Cancel();
                _isRunning = false;
                Logger.Info("RecommendationScheduler stopping...");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Failed to stop RecommendationScheduler: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 手动触发推荐更新
        /// </summary>
        /// <returns>操作结果</returns>
        public async Task<bool> TriggerUpdateAsync()
        {
            try
            {
                Logger.Info("Manually triggering recommendation update");
                await UpdateAllRecommendationsAsync();
                _lastRunTime = DateTime.Now;
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Failed to trigger recommendation update: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 更新所有用户的推荐
        /// </summary>
        /// <returns>操作结果</returns>
        public async Task<bool> UpdateAllRecommendationsAsync()
        {
            try
            {
                Logger.Info("Starting update of recommendations for all users");
                
                // 首先执行综合预计算：热门新闻相似度 + 用户推荐
                Logger.Info("Starting comprehensive precomputation: hot news + user recommendations");

                try
                {
                    var precomputeService = NewsPrecomputeService.Instance;
                    var combinedResult = await precomputeService.PrecomputeAllAsync(50, 10, 0.5, 20, 0.4);

                    Logger.Info($"Comprehensive precomputation completed. Duration: {combinedResult.Duration.TotalSeconds:F2}s");
                    Logger.Info($"Hot news precomputation - Success: {combinedResult.NewsPrecomputeResult.SuccessCount}, Failed: {combinedResult.NewsPrecomputeResult.FailedCount}");
                    Logger.Info($"User recommendations precomputation - Success: {combinedResult.UserPrecomputeResult.SuccessCount}, Failed: {combinedResult.UserPrecomputeResult.FailedCount}");
                }
                catch (Exception ex)
                {
                    Logger.Error($"Comprehensive precomputation failed: {ex.Message}", ex);
                }
                
                // 获取所有活跃用户
                var activeUsers = GetActiveUsers();
                int totalUsers = activeUsers.Count;
                int processedUsers = 0;
                int successCount = 0;
                int failedCount = 0;
                
                Logger.Info($"Found {totalUsers} active users for recommendation update");
                
                // 分批处理用户
                int batchSize = GetConfiguredBatchSize();
                for (int i = 0; i < totalUsers; i += batchSize)
                {
                    // 获取当前批次的用户
                    var userBatch = activeUsers.Skip(i).Take(batchSize).ToList();
                    
                    // 批量更新推荐
                    var batchResult = await UpdateRecommendationsForUserBatchAsync(userBatch);
                    
                    // 更新统计信息
                    processedUsers += userBatch.Count;
                    successCount += batchResult.SuccessCount;
                    failedCount += batchResult.FailedCount;
                    
                    // 记录进度
                    double progressPercentage = (double)processedUsers / totalUsers * 100;
                    Logger.Info($"Recommendation update progress: {progressPercentage:F2}% ({processedUsers}/{totalUsers})");
                    
                    // 检查是否被取消
                    if (_cancellationTokenSource?.Token.IsCancellationRequested == true)
                    {
                        Logger.Info("Recommendation update cancelled");
                        break;
                    }
                    
                    // 避免过度占用系统资源，在批次之间添加短暂延迟
                    if (i + batchSize < totalUsers)
                    {
                        await Task.Delay(1000);
                    }
                }
                
                // 更新完成后，预热缓存，提高用户访问性能
                await PreheatPopularUserCacheAsync();
                
                Logger.Info($"Recommendation update completed. Total: {totalUsers}, Processed: {processedUsers}, Success: {successCount}, Failed: {failedCount}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Failed to update recommendations for all users: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 预热活跃用户的缓存
        /// </summary>
        /// <param name="topUserCount">预热的用户数量</param>
        /// <returns>操作结果</returns>
        private async Task<bool> PreheatPopularUserCacheAsync(int topUserCount = 20)
        {
            try
            {
                Logger.Info($"开始预热热门用户的推荐缓存，用户数量: {topUserCount}");
                
                // 获取最活跃的用户
                var activeUsers = GetMostActiveUsers(topUserCount);
                if (activeUsers.Count == 0)
                {
                    Logger.Warn("没有找到活跃用户，预热结束");
                    return false;
                }
                
                // 为每个活跃用户预热缓存
                int successCount = 0;
                foreach (var userId in activeUsers)
                {
                    try
                    {
                        // 触发推荐生成，结果会自动缓存
                        var recommendations = await _recommendationEngine.GetPersonalizedRecommendationsAsync(userId);
                        if (recommendations != null && recommendations.Count > 0)
                        {
                            successCount++;
                            Logger.Debug($"成功预热用户 {userId} 的推荐缓存，数量: {recommendations.Count}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"预热用户 {userId} 的推荐缓存失败: {ex.Message}", ex);
                    }
                    
                    // 避免过度占用系统资源，添加短暂延迟
                    await Task.Delay(100);
                }
                
                Logger.Info($"预热完成，总数: {activeUsers.Count}，成功: {successCount}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"预热用户推荐缓存失败: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 获取最活跃的用户
        /// </summary>
        /// <param name="topN">返回数量</param>
        /// <returns>用户ID列表</returns>
        private List<int> GetMostActiveUsers(int topN)
        {
            try
            {
                // 从缓存获取活跃用户列表
                string cacheKey = "most_active_users";
                var cachedUsers = _cache.GetCache<List<int>>(cacheKey);
                
                if (cachedUsers != null && cachedUsers.Count > 0)
                {
                    return cachedUsers.Take(topN).ToList();
                }
                
                // 如果缓存中没有，则从数据库获取
                // 这里可以根据实际业务逻辑定义"活跃"，例如登录频率、操作次数等
                // 简化实现，直接返回所有活跃用户 - 使用带缓存的后台任务专用方法
                var allUsers = _memberBLL.GetAllListForBackgroundTask(false); // 不包括审核中的用户，带缓存
                var activeUsers = allUsers.Where(u => u.Status == 1).Take(topN).Select(u => u.Id).ToList();
                
                // 缓存结果，有效期1小时
                if (activeUsers.Count > 0)
                {
                    _cache.WriteCache(activeUsers, cacheKey, DateTime.Now.AddHours(1));
                }
                
                return activeUsers;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取最活跃用户失败: {ex.Message}", ex);
                return new List<int>();
            }
        }
        
        /// <summary>
        /// 为单个用户更新推荐
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        public async Task<bool> UpdateRecommendationsForUserAsync(int userId)
        {
            try
            {
                Logger.Info($"Updating recommendations for user {userId}");
                
                // 获取用户信息
                var user = _memberBLL.GetModelByCache(userId);
                if (user == null)
                {
                    Logger.Warn($"User {userId} not found");
                    return false;
                }
                
                // 清除用户的缓存推荐
                await _cacheManager.InvalidateUserCacheAsync(userId);
                
                // 生成个性化推荐
                var personalizedRecommendations = await _recommendationEngine.GetPersonalizedRecommendationsAsync(userId);
                
                // 生成混合推荐
                var hybridRecommendations = await _recommendationEngine.GetHybridRecommendationsAsync(userId);
                
                Logger.Info($"Successfully updated recommendations for user {userId}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Failed to update recommendations for user {userId}: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 批量生成用户推荐
        /// 优化数据库和向量操作，提高批处理效率
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="limit">每个用户的推荐数量</param>
        /// <param name="threshold">相似度阈值</param>
        /// <returns>用户ID到推荐结果的映射</returns>
        public async Task<Dictionary<int, List<NewsVectorSimilarity>>> GenerateBatchRecommendationsAsync(
            List<int> userIds,
            int limit = 10,
            double threshold = 0.4)
        {
            if (userIds == null || userIds.Count == 0)
            {
                Logger.Warn("Empty user ID list for batch recommendation generation");
                return new Dictionary<int, List<NewsVectorSimilarity>>();
            }

            try
            {
                Logger.Info($"Starting batch recommendation generation for {userIds.Count} users");
                var startTime = DateTime.Now;

                // 结果字典
                var results = new Dictionary<int, List<NewsVectorSimilarity>>();

                // 1. 批量获取用户兴趣向量
                var userVectors = await new UserInterestVectorRetrieval().GetUserInterestVectorsBatchAsync(userIds);
                if (userVectors.Count == 0)
                {
                    Logger.Warn("No user vectors found for batch recommendation");
                    return results;
                }

                // 2. 获取最近的新闻文章（已向量化的）
                var newsBLL = new NewsBLL();
                var newsVectorSearch = new NewsVectorSearch();

                // 获取最近30天的新闻，确保有足够的候选项
                var endDate = DateTime.Now;
                var startDate = endDate.AddDays(-30);

                var where = $"VectorStatus = 1 AND PubTime >= '{startDate:yyyy-MM-dd}' AND PubTime <= '{endDate:yyyy-MM-dd}'";
                var orderBy = "PubTime DESC, priority DESC";
                
                // 优化：只获取必要的字段，减少数据传输量
                var fields = "Id, Title, Source, Classify, PubTime, Tag, Url";

                // 获取足够多的新闻以确保每个用户都能获得足够的推荐
                // 限制为1000条以避免内存问题
                var candidateNews = newsBLL.GetList(where, 1000, 1, fields, orderBy);

                if (candidateNews == null || candidateNews.Count == 0)
                {
                    Logger.Warn("No candidate news found for batch recommendation");
                    return results;
                }

                Logger.Info($"Found {candidateNews.Count} candidate news articles for batch processing");

                // 3. 批量获取新闻向量
                var newsIds = candidateNews.Select(n => n.Id).ToList();
                var newsVectors = await newsVectorSearch.GetNewsVectorsBatchAsync(newsIds);

                if (newsVectors.Count == 0)
                {
                    Logger.Warn("No news vectors found for batch recommendation");
                    return results;
                }

                Logger.Info($"Retrieved {newsVectors.Count} news vectors for similarity calculation");

                // 创建新闻ID到新闻对象的映射，提高查找效率
                var newsMap = candidateNews.ToDictionary(n => n.Id);

                // 4. 为每个用户计算相似度并生成推荐 - 使用并行处理提高性能
                var vectorService = new VectorService();
                var processedUsers = 0;
                var resultsLock = new object();
                var progressLock = new object();
                
                // 创建并行选项，限制并行度以避免过度消耗CPU资源
                var parallelOptions = new ParallelOptions { 
                    MaxDegreeOfParallelism = Math.Min(Environment.ProcessorCount, 4) 
                };

                // 预计算所有新闻向量的长度，避免重复计算
                var newsVectorMagnitudes = new Dictionary<int, double>();
                foreach (var entry in newsVectors)
                {
                    newsVectorMagnitudes[entry.Key] = CalculateVectorMagnitude(entry.Value);
                }

                await Task.Run(() => {
                    Parallel.ForEach(userIds, parallelOptions, userId => {
                        try
                        {
                            if (!userVectors.TryGetValue(userId, out double[] userVector))
                            {
                                // 跳过没有兴趣向量的用户
                                return;
                            }

                            // 预计算用户向量的长度
                            double userVectorMagnitude = CalculateVectorMagnitude(userVector);
                            if (userVectorMagnitude == 0)
                            {
                                return;
                            }

                            // 计算用户向量与所有新闻向量的相似度
                            var similarities = new List<NewsVectorSimilarity>(newsVectors.Count);

                            foreach (var entry in newsVectors)
                            {
                                int newsId = entry.Key;
                                double[] newsVector = entry.Value;
                                
                                // 使用预计算的向量长度进行优化的相似度计算
                                double newsVectorMagnitude = newsVectorMagnitudes[newsId];
                                if (newsVectorMagnitude == 0)
                                {
                                    continue;
                                }
                                
                                double dotProduct = 0;
                                for (int i = 0; i < newsVector.Length; i++)
                                {
                                    dotProduct += userVector[i] * newsVector[i];
                                }
                                
                                double similarity = dotProduct / (userVectorMagnitude * newsVectorMagnitude);

                                // 如果相似度超过阈值，添加到结果中
                                if (similarity >= threshold && newsMap.TryGetValue(newsId, out var news))
                                {
                                    similarities.Add(new NewsVectorSimilarity
                                    {
                                        NewsId = newsId,
                                        Similarity = similarity,
                                        News = news
                                    });
                                }
                            }

                            // 按相似度降序排序并限制数量 - 使用堆排序优化Top-N查询
                            var topRecommendations = similarities
                                .OrderByDescending(s => s.Similarity)
                                .Take(limit)
                                .ToList();

                            // 添加到结果字典
                            lock (resultsLock)
                            {
                                results[userId] = topRecommendations;
                            }

                            // 更新进度
                            lock (progressLock)
                            {
                                processedUsers++;
                                if (processedUsers % 10 == 0 || processedUsers == userIds.Count)
                                {
                                    double progressPercentage = (double)processedUsers / userIds.Count * 100;
                                    Logger.Info($"Batch recommendation progress: {progressPercentage:F2}% ({processedUsers}/{userIds.Count})");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"Error generating recommendations for user {userId}: {ex.Message}", ex);
                        }
                    });
                });
                
                // 辅助方法：计算向量长度
                double CalculateVectorMagnitude(double[] vector)
                {
                    if (vector == null || vector.Length == 0)
                    {
                        return 0;
                    }
                    
                    double sumOfSquares = 0;
                    for (int i = 0; i < vector.Length; i++)
                    {
                        sumOfSquares += vector[i] * vector[i];
                    }
                    
                    return Math.Sqrt(sumOfSquares);
                }

                var duration = DateTime.Now - startTime;
                Logger.Info($"Batch recommendation generation completed for {results.Count}/{userIds.Count} users in {duration.TotalSeconds:F2} seconds");

                return results;
            }
            catch (Exception ex)
            {
                Logger.Error($"Failed to generate batch recommendations: {ex.Message}", ex);
                return new Dictionary<int, List<NewsVectorSimilarity>>();
            }
        }

        /// <summary>
        /// 为一批用户更新推荐
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <returns>批处理结果</returns>
        public async Task<RecommendationBatchProcessResult> UpdateRecommendationsForUserBatchAsync(List<int> userIds)
        {
            var result = new RecommendationBatchProcessResult
            {
                TotalCount = userIds.Count,
                SuccessCount = 0,
                FailedCount = 0,
                StartTime = DateTime.Now
            };
            
            try
            {
                Logger.Info($"Batch updating recommendations for {userIds.Count} users");
                
                // 获取用户兴趣向量
                var userVectors = await _userInterestRetrieval.GetUserInterestVectorsBatchAsync(userIds);
                
                // 对于有兴趣向量的用户，批量生成推荐
                if (userVectors.Count > 0)
                {
                    var usersWithVectors = userVectors.Keys.ToList();
                    
                    // 批量生成推荐
                    var recommendations = await GenerateBatchRecommendationsAsync(usersWithVectors);
                    
                    // 处理结果
                    foreach (var userId in usersWithVectors)
                    {
                        try
                        {
                            // 清除用户的缓存推荐
                            await _cacheManager.InvalidateUserCacheAsync(userId);
                            
                            // 如果成功生成了推荐，则更新缓存
                            if (recommendations.ContainsKey(userId) && recommendations[userId].Count > 0)
                            {
                                // 将推荐结果转换为News对象列表
                                var newsList = recommendations[userId].Select(r => r.News).ToList();
                                
                                // 缓存个性化推荐
                                await _cacheManager.SetCachedPersonalizedRecommendationsAsync(
                                    userId, newsList, 10, 0.4, null);
                                
                                // 同时缓存混合推荐
                                await _cacheManager.SetCachedHybridRecommendationsAsync(
                                    userId, newsList, 10, 0.7, null);
                                
                                result.SuccessCount++;
                                Logger.Debug($"Successfully generated and cached recommendations for user {userId}");
                            }
                            else
                            {
                                // 如果没有生成推荐，则使用默认推荐
                                var defaultRecommendations = await _recommendationEngine.GetDefaultRecommendationsAsync();
                                
                                // 缓存默认推荐
                                if (defaultRecommendations != null && defaultRecommendations.Count > 0)
                                {
                                    await _cacheManager.SetCachedPersonalizedRecommendationsAsync(
                                        userId, defaultRecommendations, 10, 0.4, null);
                                    
                                    await _cacheManager.SetCachedHybridRecommendationsAsync(
                                        userId, defaultRecommendations, 10, 0.7, null);
                                }
                                
                                result.SuccessCount++;
                                Logger.Debug($"Used default recommendations for user {userId}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"Failed to process recommendations for user {userId}: {ex.Message}", ex);
                            result.FailedCount++;
                        }
                    }
                }
                
                // 对于没有兴趣向量的用户，使用默认推荐
                var usersWithoutVectors = userIds.Where(id => !userVectors.ContainsKey(id)).ToList();
                if (usersWithoutVectors.Count > 0)
                {
                    Logger.Info($"Processing {usersWithoutVectors.Count} users without interest vectors");
                    
                    // 批量获取默认推荐
                    var defaultRecommendations = await _recommendationEngine.GetDefaultRecommendationsAsync();
                    
                    foreach (var userId in usersWithoutVectors)
                    {
                        try
                        {
                            // 清除用户的缓存推荐
                            await _cacheManager.InvalidateUserCacheAsync(userId);
                            
                            // 缓存默认推荐
                            if (defaultRecommendations != null && defaultRecommendations.Count > 0)
                            {
                                await _cacheManager.SetCachedPersonalizedRecommendationsAsync(
                                    userId, defaultRecommendations, 10, 0.4, null);
                                
                                await _cacheManager.SetCachedHybridRecommendationsAsync(
                                    userId, defaultRecommendations, 10, 0.7, null);
                            }
                            
                            result.SuccessCount++;
                            Logger.Debug($"Used default recommendations for user without vector {userId}");
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"Failed to process default recommendations for user {userId}: {ex.Message}", ex);
                            result.FailedCount++;
                        }
                    }
                }
                
                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;
                
                Logger.Info($"Batch recommendation update completed. Total: {result.TotalCount}, Success: {result.SuccessCount}, Failed: {result.FailedCount}, Duration: {result.Duration.TotalSeconds:F2}s");
                return result;
            }
            catch (Exception ex)
            {
                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;
                result.FailedCount = result.TotalCount - result.SuccessCount;
                
                Logger.Error($"Failed to batch update recommendations: {ex.Message}", ex);
                return result;
            }
        }
        
        /// <summary>
        /// 获取活跃用户列表
        /// </summary>
        /// <returns>活跃用户ID列表</returns>
        private List<int> GetActiveUsers()
        {
            try
            {
                // 从缓存获取活跃用户列表
                string cacheKey = "active_users_for_recommendation";
                var cachedUsers = _cache.GetCache<List<int>>(cacheKey);
                
                if (cachedUsers != null && cachedUsers.Count > 0)
                {
                    return cachedUsers;
                }
                
                // 如果缓存中没有，则从数据库获取
                var activeUsers = new List<int>();
                
                // 获取所有启用状态的用户 - 使用带缓存的后台任务专用方法
                var allUsers = _memberBLL.GetAllListForBackgroundTask(false); // 不包括审核中的用户，带缓存
                if (allUsers != null && allUsers.Count > 0)
                {
                    // 只选择启用状态的用户
                    var enabledUsers = allUsers
                        .Where(u => u.Status == (int)MemberStatus.enable)
                        .Select(u => u.Id)
                        .ToList();
                    
                    // 进一步筛选出有用户兴趣向量的用户（即有标签关系的用户）
                    var userTagRelationBLL = new UserTagRelationBLL();
                    foreach (var userId in enabledUsers)
                    {
                        try
                        {
                            // 检查用户是否有兴趣标签关系
                            var userTags = userTagRelationBLL.GetUserTagRelationsAsync(userId).Result;
                            if (userTags != null && userTags.Count > 0)
                            {
                                activeUsers.Add(userId);
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"Failed to check user tags for user {userId}: {ex.Message}", ex);
                        }
                    }
                }
                
                // 缓存结果，有效期1小时
                if (activeUsers.Count > 0)
                {
                    _cache.WriteCache(activeUsers, cacheKey, DateTime.Now.AddHours(1));
                }
                
                Logger.Info($"Found {activeUsers.Count} active users with interest vectors");
                return activeUsers;
            }
            catch (Exception ex)
            {
                Logger.Error($"Failed to get active users: {ex.Message}", ex);
                return new List<int>();
            }
        }
        
        /// <summary>
        /// 获取配置的批处理大小
        /// </summary>
        /// <returns>批处理大小</returns>
        private int GetConfiguredBatchSize()
        {
            try
            {
                string batchSizeStr = ConfigurationManager.AppSettings["RecommendationBatchSize"];
                if (!string.IsNullOrEmpty(batchSizeStr) && int.TryParse(batchSizeStr, out int batchSize))
                {
                    return batchSize;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"Failed to get batch size from configuration: {ex.Message}", ex);
            }
            
            return DEFAULT_BATCH_SIZE;
        }
        
        /// <summary>
        /// 获取配置的更新间隔（小时）
        /// </summary>
        /// <returns>更新间隔（小时）</returns>
        private int GetConfiguredUpdateInterval()
        {
            try
            {
                string intervalStr = ConfigurationManager.AppSettings["RecommendationUpdateIntervalHours"];
                if (!string.IsNullOrEmpty(intervalStr) && int.TryParse(intervalStr, out int interval))
                {
                    return interval;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"Failed to get update interval from configuration: {ex.Message}", ex);
            }
            
            return DEFAULT_UPDATE_INTERVAL_HOURS;
        }
    }
    
    /// <summary>
    /// 批处理结果
    /// </summary>
    public class RecommendationBatchProcessResult
    {
        /// <summary>
        /// 总处理数量
        /// </summary>
        public int TotalCount { get; set; }
        
        /// <summary>
        /// 成功数量
        /// </summary>
        public int SuccessCount { get; set; }
        
        /// <summary>
        /// 失败数量
        /// </summary>
        public int FailedCount { get; set; }
        
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }
        
        /// <summary>
        /// 处理时长
        /// </summary>
        public TimeSpan Duration { get; set; }
    }
    
  
}