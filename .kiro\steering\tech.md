# Technical Stack and Development Guidelines

## Technology Stack

- **Framework**: ASP.NET MVC + Web API (.NET Framework 4.6.1)
- **Database**: SQL Server (primary database)
- **Caching**: Redis
- **AI Models**:
  - Large Language Model: deepseek-r1-0528 (for structured output)
  - Embedding Model: text-embedding-bge-m3 (1024-dimensional vectors)
- **Frontend**: jQuery, Bootstrap

## Libraries and Dependencies

- **ORM**: DAL.Base (custom data access layer, with GetModel、GetList methods etc.), Data model should be wrapped like '[Serializable][SqlTable(dbEnum.QLWL)]'
- **Logging**: Banyan.Code.Logger
- **JSON Processing**: Newtonsoft.Json
- **Monitoring**: Microsoft.ApplicationInsights
- **Vector Operations**: Custom VectorService implementation

## Development Environment

- **IDE**: Visual Studio
- **Source Control**: Git
- **Build System**: MSBuild
- **CI/CD**: Azure Pipelines (azure-pipelines.yml)

## Common Commands

### Build Commands

```powershell
# Build the solution
msbuild Invest.sln /p:Configuration=Debug

# Build specific project
msbuild Banyan.Web/Banyan.Web.csproj /p:Configuration=Debug
```

### Database Commands

```powershell
# Run database migrations
cd Banyan.Web
dotnet run -- migrate
```

### Testing

```powershell
# Run unit tests
# (Add specific test commands when test framework is implemented)
```

## Coding Standards

- Use C# naming conventions (PascalCase for public members, camelCase for parameters)
- Add XML documentation comments for public methods and classes
- Implement proper error handling and logging
- Use async/await for asynchronous operations
- Follow the existing architecture patterns (BLL classes for business logic)
- Implement proper caching strategies for performance-critical operations

## Vector Processing Guidelines

- Vector dimension: 1024 (consistent across all vector operations)
- Normalize vectors before storage
- Use cosine similarity for vector matching
- Cache vector operations to improve performance
- Implement fallback mechanisms for when AI services are unavailable
