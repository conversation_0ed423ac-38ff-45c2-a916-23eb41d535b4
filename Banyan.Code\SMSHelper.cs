﻿using Aliyun.Acs.Core;
using Aliyun.Acs.Core.Exceptions;
using Aliyun.Acs.Core.Profile;
using Aliyun.Acs.Dysmsapi.Model.V20170525;
using System;

namespace Banyan.Code
{

    public class SMSHelper
    {
        private static readonly string accessKeyId = "LTAIVUQ3nVSqnhTi";
        private static readonly string accessKeySecret = "ajzIcPtYm1STZ6mLqWgGREn3auzcTI";

        /// <summary>
        /// 阿里大于短信接口
        /// </summary>
        /// <param name="phoneNumbers">接收的手机号</param>
        /// <param name="param">"{\"code\":\"1234\"}"</param>
        /// <returns></returns>
        public static bool AliDySend(string phoneNumbers, string param)
        {
            String accessKeyId = "LTAIVUQ3nVSqnhTi";
            String accessKeySecret = "ajzIcPtYm1STZ6mLqWgGREn3auzcTI";

            IClientProfile profile = DefaultProfile.GetProfile("cn-hangzhou", accessKeyId, accessKeySecret);
            IAcsClient acsClient = new DefaultAcsClient(profile);
            SendSmsRequest request = new SendSmsRequest();
            try
            {
                //必填:待发送手机号。支持以逗号分隔的形式进行批量调用，批量上限为20个手机号码,批量调用相对于单条调用及时性稍有延迟,验证码类型的短信推荐使用单条调用的方式
                request.PhoneNumbers = phoneNumbers;
                //必填:短信签名-可在短信控制台中找到
                request.SignName = "注册验证码";
                //必填:短信模板-可在短信控制台中找到
                request.TemplateCode = "SMS_133130183";
                //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
                request.TemplateParam = param;
                //请求失败这里会抛ClientException异常
                SendSmsResponse sendSmsResponse = acsClient.GetAcsResponse(request);
                return sendSmsResponse.Code.Equals("OK");
            }
            catch (ServerException e)
            {
                System.Console.WriteLine(e.Message);
                return false;
            }
            catch (ClientException e)
            {
                System.Console.WriteLine(e.Message);
                return false;
            }
        }

        /// <summary>
        /// 阿里云短信接口
        /// </summary>
        /// <param name="phoneNumbers">手机号码</param>
        /// <param name="param"></param>
        /// <returns></returns>
        public static bool AliSmsSend(string phoneNumbers, string param)
        {
            IClientProfile profile = DefaultProfile.GetProfile("cn-hangzhou", accessKeyId, accessKeySecret);
            DefaultProfile.AddEndpoint("cn-hangzhou", "cn-hangzhou", "Dysmsapi", "dysmsapi.aliyuncs.com");
            IAcsClient acsClient = new DefaultAcsClient(profile);
            SendSmsRequest request = new SendSmsRequest();
            try
            {
                request.PhoneNumbers = phoneNumbers;
                request.SignName = "北京高榕资本管理咨询";
                request.TemplateCode = "SMS_174279876";
                request.TemplateParam = param;
                SendSmsResponse sendSmsResponse = acsClient.GetAcsResponse(request);
                System.Console.WriteLine(sendSmsResponse.Message);
                return sendSmsResponse.Code.Equals("OK");
            }
            catch (ServerException e)
            {
                System.Console.WriteLine($"ServerException:{e.Message}");
                return false;
            }
            catch (ClientException e)
            {
                System.Console.WriteLine($"ClientException:{e.Message}");
                return false;
            }
        }
    }
}
