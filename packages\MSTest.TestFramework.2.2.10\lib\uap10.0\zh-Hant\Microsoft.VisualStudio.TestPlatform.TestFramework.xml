<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod">
            <summary>
            用於執行的 TestMethod。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestMethodName">
            <summary>
            取得測試方法的名稱。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestClassName">
            <summary>
            取得測試類別的名稱。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ReturnType">
            <summary>
            取得測試方法的傳回型別。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ParameterTypes">
            <summary>
            取得測試方法的參數。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.MethodInfo">
            <summary>
            取得測試方法的 methodInfo。
            </summary>
            <remarks>
            This is just to retrieve additional information about the method.
            Do not directly invoke the method using MethodInfo. Use ITestMethod.Invoke instead.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.Invoke(System.Object[])">
            <summary>
            叫用測試方法。
            </summary>
            <param name="arguments">
            要傳遞至測試方法的引數。(例如，針對資料驅動)
            </param>
            <returns>
            測試方法引動過程結果。
            </returns>
            <remarks>
            This call handles asynchronous test methods as well.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAllAttributes(System.Boolean)">
            <summary>
            取得測試方法的所有屬性。
            </summary>
            <param name="inherit">
            父類別中定義的屬性是否有效。
            </param>
            <returns>
            所有屬性。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAttributes``1(System.Boolean)">
            <summary>
            取得特定類型的屬性。
            </summary>
            <typeparam name="AttributeType"> System.Attribute type. </typeparam>
            <param name="inherit">
            父類別中定義的屬性是否有效。
            </param>
            <returns>
            指定類型的屬性。
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Helper">
            <summary>
            協助程式。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNull(System.Object,System.String,System.String)">
            <summary>
            檢查參數不為 null。
            </summary>
            <param name="param">
            參數。
            </param>
            <param name="parameterName">
            參數名稱。
            </param>
            <param name="message">
            訊息。
            </param>
            <exception cref="T:System.ArgumentNullException"> Throws argument null exception when parameter is null. </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNullOrEmpty(System.String,System.String,System.String)">
            <summary>
            檢查參數不為 null 或為空白。
            </summary>
            <param name="param">
            參數。
            </param>
            <param name="parameterName">
            參數名稱。
            </param>
            <param name="message">
            訊息。
            </param>
            <exception cref="T:System.ArgumentException"> Throws ArgumentException when parameter is null. </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod">
            <summary>
            如何在資料驅動測試中存取資料列的列舉。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Sequential">
            <summary>
            會以循序順序傳回資料列。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random">
            <summary>
            會以隨機順序傳回資料列。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute">
            <summary>
            用以定義測試方法之內嵌資料的屬性。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> 類別的新執行個體。
            </summary>
            <param name="data1"> 資料物件。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object,System.Object[])">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> 類別 (其採用引數的陣列) 的新執行個體。
            </summary>
            <param name="data1"> 資料物件。 </param>
            <param name="moreData"> 其他資料。 </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.Data">
            <summary>
            取得用於呼叫測試方法的資料。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.DisplayName">
            <summary>
            取得或設定測試結果中的顯示名稱來進行自訂。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            <summary>
            判斷提示結果不明例外狀況。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String,System.Exception)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> 類別的新執行個體。
            </summary>
            <param name="msg"> 訊息。 </param>
            <param name="ex"> 例外狀況。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> 類別的新執行個體。
            </summary>
            <param name="msg"> 訊息。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> 類別的新執行個體。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException">
            <summary>
            InternalTestFailureException 類別。用來表示測試案例的內部失敗
            </summary>
            <remarks>
            This class is only added to preserve source compatibility with the V1 framework.
            For all practical purposes either use AssertFailedException/AssertInconclusiveException.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String,System.Exception)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> 類別的新執行個體。
            </summary>
            <param name="msg"> 例外狀況訊息。 </param>
            <param name="ex"> 例外狀況。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> 類別的新執行個體。
            </summary>
            <param name="msg"> 例外狀況訊息。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> 類別的新執行個體。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute">
            <summary>
            屬性，其指定預期所指定類型的例外狀況
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type)">
            <summary>
            初始化具預期類型之 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> 類別的新執行個體
            </summary>
            <param name="exceptionType">預期的例外狀況類型</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type,System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> 類別
            (其具預期類型及訊息，用以在測試未擲回任何例外狀況時予以納入) 的新執行個體。
            </summary>
            <param name="exceptionType">預期的例外狀況類型</param>
            <param name="noExceptionMessage">
            測試因未擲回例外狀況而失敗時，要包含在測試結果中的訊息
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.ExceptionType">
            <summary>
            取得值，指出預期例外狀況的類型
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.AllowDerivedTypes">
            <summary>
            取得或設定值，指出是否允許類型衍生自預期例外狀況類型，
            以符合預期
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.NoExceptionMessage">
            <summary>
            如果測試因未擲回例外狀況而失敗，則取得測試結果中要包含的訊息
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.Verify(System.Exception)">
            <summary>
            驗證預期有單元測試所擲回的例外狀況類型
            </summary>
            <param name="exception">單元測試所擲回的例外狀況</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute">
            <summary>
            指定以預期單元測試發生例外狀況之屬性的基底類別
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor">
            <summary>
            使用預設無例外狀況訊息初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> 類別的新執行個體
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor(System.String)">
            <summary>
            初始化具無例外狀況訊息之 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> 類別的新執行個體
            </summary>
            <param name="noExceptionMessage">
            測試因未擲回例外狀況而失敗時，要包含在測試結果中的
            訊息
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.NoExceptionMessage">
            <summary>
            如果測試因未擲回例外狀況而失敗，則取得測試結果中要包含的訊息
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.SpecifiedNoExceptionMessage">
            <summary>
            如果測試因未擲回例外狀況而失敗，則取得測試結果中要包含的訊息
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.GetDefaultNoExceptionMessage(System.String)">
            <summary>
            取得預設無例外狀況訊息
            </summary>
            <param name="expectedExceptionAttributeTypeName">ExpectedException 屬性類型名稱</param>
            <returns>預設無例外狀況訊息</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.Verify(System.Exception)">
            <summary>
            判斷是否預期會發生例外狀況。如果傳回方法，則了解
            預期會發生例外狀況。如果方法擲回例外狀況，則了解
            預期不會發生例外狀況，而且測試結果中
            會包含所擲回例外狀況的訊息。<see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert"/> 類別可以基於便利
            使用。如果使用 <see cref="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive"/> 並且判斷提示失敗，
            則測試結果設定為 [結果不明]。
            </summary>
            <param name="exception">單元測試所擲回的例外狀況</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.RethrowIfAssertException(System.Exception)">
            <summary>
            如果它是 AssertFailedException 或 AssertInconclusiveException，會重新擲回例外狀況
            </summary>
            <param name="exception">如果是判斷提示例外狀況，則重新擲回例外狀況</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper">
            <summary>
            這個類別的設計目的是要協助使用者執行使用泛型型別之類型的單元測試。
            GenericParameterHelper 滿足一些常用泛型型別條件約束
            例如:
            1. 公用預設建構函式
            2. 實作公用介面: IComparable、IEnumerable
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> 類別 (其符合 C# 泛型中的 'newable' 限制式)
            的新執行個體。
            </summary>
            <remarks>
            This constructor initializes the Data property to a random value.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor(System.Int32)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> 類別 (其將 Data 屬性初始化為使用者提供的值)
            的新執行個體。
            </summary>
            <param name="data">任何整數值</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Data">
            <summary>
            取得或設定資料
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Equals(System.Object)">
            <summary>
            執行兩個 GenericParameterHelper 物件的值比較
            </summary>
            <param name="obj">要與之執行比較的物件</param>
            <returns>如果 obj 的值與 'this' GenericParameterHelper 物件相同，則為 true。
            否則為 false。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetHashCode">
            <summary>
            傳回這個物件的雜湊碼。
            </summary>
            <returns>雜湊碼。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.CompareTo(System.Object)">
            <summary>
            比較這兩個 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> 物件的資料。
            </summary>
            <param name="obj">要比較的物件。</param>
            <returns>
            已簽署的編號，表示此執行個體及值的相對值。
            </returns>
            <exception cref="T:System.NotSupportedException">
            Thrown when the object passed in is not an instance of <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetEnumerator">
            <summary>
            傳回長度衍生自 Data 屬性的
            IEnumerator 物件。
            </summary>
            <returns>IEnumerator 物件</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Clone">
            <summary>
            傳回等於目前物件的
            GenericParameterHelper 物件。
            </summary>
            <returns>複製的物件。</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger">
            <summary>
            讓使用者從單位測試記錄/寫入追蹤以進行診斷。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessageHandler">
            <summary>
            LogMessage 的處理常式。
            </summary>
            <param name="message">要記錄的訊息。</param>
        </member>
        <member name="E:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.OnLogMessage">
            <summary>
            要接聽的事件。在單元測試寫入器寫入一些訊息時引發。
            主要由配接器取用。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessage(System.String,System.Object[])">
            <summary>
            API，供測試寫入者呼叫以記錄訊息。
            </summary>
            <param name="format">含預留位置的字串格式。</param>
            <param name="args">預留位置的參數。</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute">
            <summary>
            TestCategory 屬性; 用來指定單元測試的分類。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.#ctor(System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute"/> 類別的新執行個體，並將分類套用至測試。
            </summary>
            <param name="testCategory">
            測試「分類」。
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.TestCategories">
            <summary>
            取得已套用至測試的測試分類。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute">
            <summary>
            "Category" 屬性的基底類別
            </summary>
            <remarks>
            The reason for this attribute is to let the users create their own implementation of test categories.
            - test framework (discovery, etc) deals with TestCategoryBaseAttribute.
            - The reason that TestCategories property is a collection rather than a string,
              is to give more flexibility to the user. For instance the implementation may be based on enums for which the values can be OR'ed
              in which case it makes sense to have single attribute rather than multiple ones on the same test.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.#ctor">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute"/> 類別的新執行個體。
             將分類套用至測試。TestCategories 所傳回的字串
            會與 /category 命令搭配使用，以篩選測試
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.TestCategories">
            <summary>
            取得已套用至測試的測試分類。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            <summary>
            AssertFailedException 類別。用來表示測試案例失敗
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String,System.Exception)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> 類別的新執行個體。
            </summary>
            <param name="msg"> 訊息。 </param>
            <param name="ex"> 例外狀況。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> 類別的新執行個體。
            </summary>
            <param name="msg"> 訊息。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> 類別的新執行個體。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert">
            <summary>
            要測試單元測試內各種條件的協助程式類別集合。
            如果不符合正在測試的條件，則會擲回
            例外狀況。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.That">
            <summary>
            取得 Assert 功能的單一執行個體。
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void IsOfType&lt;T&gt;(this Assert assert, object obj)"
            Users could then use a syntax similar to the default assertions which in this case is "Assert.That.IsOfType&lt;Dog&gt;(animal);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean)">
            <summary>
            測試指定的條件是否為 true，並在條件為 false 時擲回
            例外狀況。
            </summary>
            <param name="condition">
            測試預期為 true 的條件。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String)">
            <summary>
            測試指定的條件是否為 true，並在條件為 false 時擲回
            例外狀況。
            </summary>
            <param name="condition">
            測試預期為 true 的條件。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="condition"/>
            為 false。訊息會顯示在測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String,System.Object[])">
            <summary>
            測試指定的條件是否為 true，並在條件為 false 時擲回
            例外狀況。
            </summary>
            <param name="condition">
            測試預期為 true 的條件。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="condition"/>
            為 false。訊息會顯示在測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean)">
            <summary>
            測試指定的條件是否為 false，並在條件為 true 時擲回
            例外狀況。
            </summary>
            <param name="condition">
            測試預期為 false 的條件。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String)">
            <summary>
            測試指定的條件是否為 false，並在條件為 true 時擲回
            例外狀況。
            </summary>
            <param name="condition">
            測試預期為 false 的條件。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="condition"/>
            為 true。訊息會顯示在測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String,System.Object[])">
            <summary>
            測試指定的條件是否為 false，並在條件為 true 時擲回
            例外狀況。
            </summary>
            <param name="condition">
            測試預期為 false 的條件。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="condition"/>
            為 true。訊息會顯示在測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object)">
            <summary>
            測試指定的物件是否為 null，並在不是時擲回
            例外狀況。
            </summary>
            <param name="value">
            測試預期為 null 的物件。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String)">
            <summary>
            測試指定的物件是否為 null，並在不是時擲回
            例外狀況。
            </summary>
            <param name="value">
            測試預期為 null 的物件。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="value"/>
            不為 null。訊息會顯示在測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String,System.Object[])">
            <summary>
            測試指定的物件是否為 null，並在不是時擲回
            例外狀況。
            </summary>
            <param name="value">
            測試預期為 null 的物件。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="value"/>
            不為 null。訊息會顯示在測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object)">
            <summary>
            測試指定的物件是否為非 null，並在為 null 時擲回
            例外狀況。
            </summary>
            <param name="value">
            測試預期不為 null 的物件。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String)">
            <summary>
            測試指定的物件是否為非 null，並在為 null 時擲回
            例外狀況。
            </summary>
            <param name="value">
            測試預期不為 null 的物件。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="value"/>
            為 null。訊息會顯示在測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String,System.Object[])">
            <summary>
            測試指定的物件是否為非 null，並在為 null 時擲回
            例外狀況。
            </summary>
            <param name="value">
            測試預期不為 null 的物件。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="value"/>
            為 null。訊息會顯示在測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object)">
            <summary>
            測試指定的物件是否都參照相同物件，並在兩個輸入
            未參照相同的物件時擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個物件。這是測試所預期的值。
            </param>
            <param name="actual">
            要比較的第二個物件。這是正在測試的程式碼所產生的值。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String)">
            <summary>
            測試指定的物件是否都參照相同物件，並在兩個輸入
            未參照相同的物件時擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個物件。這是測試所預期的值。
            </param>
            <param name="actual">
            要比較的第二個物件。這是正在測試的程式碼所產生的值。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            與下者不同: <paramref name="expected"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            測試指定的物件是否都參照相同物件，並在兩個輸入
            未參照相同的物件時擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個物件。這是測試所預期的值。
            </param>
            <param name="actual">
            要比較的第二個物件。這是正在測試的程式碼所產生的值。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            與下者不同: <paramref name="expected"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object)">
            <summary>
            測試指定的物件是否參照不同物件，並在兩個輸入
            參照相同的物件時擲回例外狀況。
            </summary>
            <param name="notExpected">
            要比較的第一個物件。測試預期這個值
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個物件。這是正在測試的程式碼所產生的值。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String)">
            <summary>
            測試指定的物件是否參照不同物件，並在兩個輸入
            參照相同的物件時擲回例外狀況。
            </summary>
            <param name="notExpected">
            要比較的第一個物件。測試預期這個值
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個物件。這是正在測試的程式碼所產生的值。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            與下者相同: <paramref name="notExpected"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            測試指定的物件是否參照不同物件，並在兩個輸入
            參照相同的物件時擲回例外狀況。
            </summary>
            <param name="notExpected">
            要比較的第一個物件。測試預期這個值
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個物件。這是正在測試的程式碼所產生的值。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            與下者相同: <paramref name="notExpected"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0)">
            <summary>
            測試指定的值是否相等，並在兩個值不相等時
            擲回例外狀況。不同的數值類型會視為不相等，
            即使邏輯值相等也是一樣。42L 不等於 42。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            要比較的第一個值。這是測試所預期的值。
            </param>
            <param name="actual">
            要比較的第二個值。這是正在測試的程式碼所產生的值。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String)">
            <summary>
            測試指定的值是否相等，並在兩個值不相等時
            擲回例外狀況。不同的數值類型會視為不相等，
            即使邏輯值相等也是一樣。42L 不等於 42。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            要比較的第一個值。這是測試所預期的值。
            </param>
            <param name="actual">
            要比較的第二個值。這是正在測試的程式碼所產生的值。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            不等於 <paramref name="expected"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            測試指定的值是否相等，並在兩個值不相等時
            擲回例外狀況。不同的數值類型會視為不相等，
            即使邏輯值相等也是一樣。42L 不等於 42。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            要比較的第一個值。這是測試所預期的值。
            </param>
            <param name="actual">
            要比較的第二個值。這是正在測試的程式碼所產生的值。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            不等於 <paramref name="expected"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0)">
            <summary>
            測試指定的值是否不相等，並在兩個值相等時
            擲回例外狀況。不同的數值類型會視為不相等，
            即使邏輯值相等也是一樣。42L 不等於 42。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            要比較的第一個值。測試預期這個值
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個值。這是正在測試的程式碼所產生的值。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String)">
            <summary>
            測試指定的值是否不相等，並在兩個值相等時
            擲回例外狀況。不同的數值類型會視為不相等，
            即使邏輯值相等也是一樣。42L 不等於 42。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            要比較的第一個值。測試預期這個值
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個值。這是正在測試的程式碼所產生的值。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            等於 <paramref name="notExpected"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            測試指定的值是否不相等，並在兩個值相等時
            擲回例外狀況。不同的數值類型會視為不相等，
            即使邏輯值相等也是一樣。42L 不等於 42。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            要比較的第一個值。測試預期這個值
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個值。這是正在測試的程式碼所產生的值。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            等於 <paramref name="notExpected"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object)">
            <summary>
            測試指定的物件是否相等，並在兩個物件不相等時
            擲回例外狀況。不同的數值類型會視為不相等，
            即使邏輯值相等也是一樣。42L 不等於 42。
            </summary>
            <param name="expected">
            要比較的第一個物件。這是測試所預期的物件。
            </param>
            <param name="actual">
            要比較的第二個物件。這是正在測試的程式碼所產生的物件。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String)">
            <summary>
            測試指定的物件是否相等，並在兩個物件不相等時
            擲回例外狀況。不同的數值類型會視為不相等，
            即使邏輯值相等也是一樣。42L 不等於 42。
            </summary>
            <param name="expected">
            要比較的第一個物件。這是測試所預期的物件。
            </param>
            <param name="actual">
            要比較的第二個物件。這是正在測試的程式碼所產生的物件。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            不等於 <paramref name="expected"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            測試指定的物件是否相等，並在兩個物件不相等時
            擲回例外狀況。不同的數值類型會視為不相等，
            即使邏輯值相等也是一樣。42L 不等於 42。
            </summary>
            <param name="expected">
            要比較的第一個物件。這是測試所預期的物件。
            </param>
            <param name="actual">
            要比較的第二個物件。這是正在測試的程式碼所產生的物件。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            不等於 <paramref name="expected"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object)">
            <summary>
            測試指定的物件是否不相等，並在兩個物件相等時
            擲回例外狀況。不同的數值類型會視為不相等，
            即使邏輯值相等也是一樣。42L 不等於 42。
            </summary>
            <param name="notExpected">
            要比較的第一個物件。測試預期這個值
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個物件。這是正在測試的程式碼所產生的物件。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String)">
            <summary>
            測試指定的物件是否不相等，並在兩個物件相等時
            擲回例外狀況。不同的數值類型會視為不相等，
            即使邏輯值相等也是一樣。42L 不等於 42。
            </summary>
            <param name="notExpected">
            要比較的第一個物件。測試預期這個值
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個物件。這是正在測試的程式碼所產生的物件。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            等於 <paramref name="notExpected"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            測試指定的物件是否不相等，並在兩個物件相等時
            擲回例外狀況。不同的數值類型會視為不相等，
            即使邏輯值相等也是一樣。42L 不等於 42。
            </summary>
            <param name="notExpected">
            要比較的第一個物件。測試預期這個值
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個物件。這是正在測試的程式碼所產生的物件。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            等於 <paramref name="notExpected"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single)">
            <summary>
            測試指定的 float 是否相等，並在不相等時
            擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個 float。這是測試所預期的 float。
            </param>
            <param name="actual">
            要比較的第二個 float。這是正在測試的程式碼所產生的 float。
            </param>
            <param name="delta">
            所需的精確度。只有在下列情況才會擲回例外狀況:
            <paramref name="actual"/> 不同於 <paramref name="expected"/>
            超過 <paramref name="delta"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            測試指定的 float 是否相等，並在不相等時
            擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個 float。這是測試所預期的 float。
            </param>
            <param name="actual">
            要比較的第二個 float。這是正在測試的程式碼所產生的 float。
            </param>
            <param name="delta">
            所需的精確度。只有在下列情況才會擲回例外狀況:
            <paramref name="actual"/> 不同於 <paramref name="expected"/>
            超過 <paramref name="delta"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            不同於 <paramref name="expected"/> 超過
            <paramref name="delta"/>。訊息會顯示在測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            測試指定的 float 是否相等，並在不相等時
            擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個 float。這是測試所預期的 float。
            </param>
            <param name="actual">
            要比較的第二個 float。這是正在測試的程式碼所產生的 float。
            </param>
            <param name="delta">
            所需的精確度。只有在下列情況才會擲回例外狀況:
            <paramref name="actual"/> 不同於 <paramref name="expected"/>
            超過 <paramref name="delta"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            不同於 <paramref name="expected"/> 超過
            <paramref name="delta"/>。訊息會顯示在測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single)">
            <summary>
            測試指定的 float 是否不相等，並在相等時
            擲回例外狀況。
            </summary>
            <param name="notExpected">
            要比較的第一個 float。測試預期這個 float 不
            符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個 float。這是正在測試的程式碼所產生的 float。
            </param>
            <param name="delta">
            所需的精確度。只有在下列情況才會擲回例外狀況:
            <paramref name="actual"/> 不同於 <paramref name="notExpected"/>
            最多 <paramref name="delta"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            測試指定的 float 是否不相等，並在相等時
            擲回例外狀況。
            </summary>
            <param name="notExpected">
            要比較的第一個 float。測試預期這個 float 不
            符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個 float。這是正在測試的程式碼所產生的 float。
            </param>
            <param name="delta">
            所需的精確度。只有在下列情況才會擲回例外狀況:
            <paramref name="actual"/> 不同於 <paramref name="notExpected"/>
            最多 <paramref name="delta"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            等於 <paramref name="notExpected"/> 或差異小於
            <paramref name="delta"/>。訊息會顯示在測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            測試指定的 float 是否不相等，並在相等時
            擲回例外狀況。
            </summary>
            <param name="notExpected">
            要比較的第一個 float。測試預期這個 float 不
            符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個 float。這是正在測試的程式碼所產生的 float。
            </param>
            <param name="delta">
            所需的精確度。只有在下列情況才會擲回例外狀況:
            <paramref name="actual"/> 不同於 <paramref name="notExpected"/>
            最多 <paramref name="delta"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            等於 <paramref name="notExpected"/> 或差異小於
            <paramref name="delta"/>。訊息會顯示在測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double)">
            <summary>
            測試指定的雙精度浮點數是否相等，並在不相等時
            擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個雙精度浮點數。這是測試所預期的雙精度浮點數。
            </param>
            <param name="actual">
            要比較的第二個雙精度浮點數。這是正在測試的程式碼所產生的雙精度浮點數。
            </param>
            <param name="delta">
            所需的精確度。只有在下列情況才會擲回例外狀況:
            <paramref name="actual"/> 不同於 <paramref name="expected"/>
            超過 <paramref name="delta"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            測試指定的雙精度浮點數是否相等，並在不相等時
            擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個雙精度浮點數。這是測試所預期的雙精度浮點數。
            </param>
            <param name="actual">
            要比較的第二個雙精度浮點數。這是正在測試的程式碼所產生的雙精度浮點數。
            </param>
            <param name="delta">
            所需的精確度。只有在下列情況才會擲回例外狀況:
            <paramref name="actual"/> 不同於 <paramref name="expected"/>
            超過 <paramref name="delta"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            不同於 <paramref name="expected"/> 超過
            <paramref name="delta"/>。訊息會顯示在測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            測試指定的雙精度浮點數是否相等，並在不相等時
            擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個雙精度浮點數。這是測試所預期的雙精度浮點數。
            </param>
            <param name="actual">
            要比較的第二個雙精度浮點數。這是正在測試的程式碼所產生的雙精度浮點數。
            </param>
            <param name="delta">
            所需的精確度。只有在下列情況才會擲回例外狀況:
            <paramref name="actual"/> 不同於 <paramref name="expected"/>
            超過 <paramref name="delta"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            不同於 <paramref name="expected"/> 超過
            <paramref name="delta"/>。訊息會顯示在測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double)">
            <summary>
            測試指定的雙精度浮點數是否不相等，並在相等時
            擲回例外狀況。
            </summary>
            <param name="notExpected">
            要比較的第一個雙精度浮點數。測試預期這個雙精度浮點數
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個雙精度浮點數。這是正在測試的程式碼所產生的雙精度浮點數。
            </param>
            <param name="delta">
            所需的精確度。只有在下列情況才會擲回例外狀況:
            <paramref name="actual"/> 不同於 <paramref name="notExpected"/>
            最多 <paramref name="delta"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            測試指定的雙精度浮點數是否不相等，並在相等時
            擲回例外狀況。
            </summary>
            <param name="notExpected">
            要比較的第一個雙精度浮點數。測試預期這個雙精度浮點數
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個雙精度浮點數。這是正在測試的程式碼所產生的雙精度浮點數。
            </param>
            <param name="delta">
            所需的精確度。只有在下列情況才會擲回例外狀況:
            <paramref name="actual"/> 不同於 <paramref name="notExpected"/>
            最多 <paramref name="delta"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            等於 <paramref name="notExpected"/> 或差異小於
            <paramref name="delta"/>。訊息會顯示在測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            測試指定的雙精度浮點數是否不相等，並在相等時
            擲回例外狀況。
            </summary>
            <param name="notExpected">
            要比較的第一個雙精度浮點數。測試預期這個雙精度浮點數
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個雙精度浮點數。這是正在測試的程式碼所產生的雙精度浮點數。
            </param>
            <param name="delta">
            所需的精確度。只有在下列情況才會擲回例外狀況:
            <paramref name="actual"/> 不同於 <paramref name="notExpected"/>
            最多 <paramref name="delta"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            等於 <paramref name="notExpected"/> 或差異小於
            <paramref name="delta"/>。訊息會顯示在測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean)">
            <summary>
            測試指定的字串是否相等，並在不相等時
            擲回例外狀況。進行比較時不因文化特性 (Culture) 而異。
            </summary>
            <param name="expected">
            要比較的第一個字串。這是測試所預期的字串。
            </param>
            <param name="actual">
            要比較的第二個字串。這是正在測試的程式碼所產生的字串。
            </param>
            <param name="ignoreCase">
            表示區分大小寫或不區分大小寫的比較的布林值 (true
            表示不區分大小寫的比較)。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            測試指定的字串是否相等，並在不相等時
            擲回例外狀況。進行比較時不因文化特性 (Culture) 而異。
            </summary>
            <param name="expected">
            要比較的第一個字串。這是測試所預期的字串。
            </param>
            <param name="actual">
            要比較的第二個字串。這是正在測試的程式碼所產生的字串。
            </param>
            <param name="ignoreCase">
            表示區分大小寫或不區分大小寫的比較的布林值 (true
            表示不區分大小寫的比較)。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            不等於 <paramref name="expected"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            測試指定的字串是否相等，並在不相等時
            擲回例外狀況。進行比較時不因文化特性 (Culture) 而異。
            </summary>
            <param name="expected">
            要比較的第一個字串。這是測試所預期的字串。
            </param>
            <param name="actual">
            要比較的第二個字串。這是正在測試的程式碼所產生的字串。
            </param>
            <param name="ignoreCase">
            表示區分大小寫或不區分大小寫的比較的布林值 (true
            表示不區分大小寫的比較)。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            不等於 <paramref name="expected"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            測試指定的字串是否相等，並在不相等時
            擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個字串。這是測試所預期的字串。
            </param>
            <param name="actual">
            要比較的第二個字串。這是正在測試的程式碼所產生的字串。
            </param>
            <param name="ignoreCase">
            表示區分大小寫或不區分大小寫的比較的布林值 (true
            表示不區分大小寫的比較)。
            </param>
            <param name="culture">
            提供文化特性 (culture) 特定比較資訊的 CultureInfo 物件。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            測試指定的字串是否相等，並在不相等時
            擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個字串。這是測試所預期的字串。
            </param>
            <param name="actual">
            要比較的第二個字串。這是正在測試的程式碼所產生的字串。
            </param>
            <param name="ignoreCase">
            表示區分大小寫或不區分大小寫的比較的布林值 (true
            表示不區分大小寫的比較)。
            </param>
            <param name="culture">
            提供文化特性 (culture) 特定比較資訊的 CultureInfo 物件。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            不等於 <paramref name="expected"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            測試指定的字串是否相等，並在不相等時
            擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個字串。這是測試所預期的字串。
            </param>
            <param name="actual">
            要比較的第二個字串。這是正在測試的程式碼所產生的字串。
            </param>
            <param name="ignoreCase">
            表示區分大小寫或不區分大小寫的比較的布林值 (true
            表示不區分大小寫的比較)。
            </param>
            <param name="culture">
            提供文化特性 (culture) 特定比較資訊的 CultureInfo 物件。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            不等於 <paramref name="expected"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean)">
            <summary>
            測試指定的字串是否不相等，並在相等時
            擲回例外狀況。進行比較時不因文化特性 (Culture) 而異。
            </summary>
            <param name="notExpected">
            要比較的第一個字串。測試預期這個字串
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個字串。這是正在測試的程式碼所產生的字串。
            </param>
            <param name="ignoreCase">
            表示區分大小寫或不區分大小寫的比較的布林值 (true
            表示不區分大小寫的比較)。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            測試指定的字串是否不相等，並在相等時
            擲回例外狀況。進行比較時不因文化特性 (Culture) 而異。
            </summary>
            <param name="notExpected">
            要比較的第一個字串。測試預期這個字串
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個字串。這是正在測試的程式碼所產生的字串。
            </param>
            <param name="ignoreCase">
            表示區分大小寫或不區分大小寫的比較的布林值 (true
            表示不區分大小寫的比較)。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            等於 <paramref name="notExpected"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            測試指定的字串是否不相等，並在相等時
            擲回例外狀況。進行比較時不因文化特性 (Culture) 而異。
            </summary>
            <param name="notExpected">
            要比較的第一個字串。測試預期這個字串
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個字串。這是正在測試的程式碼所產生的字串。
            </param>
            <param name="ignoreCase">
            表示區分大小寫或不區分大小寫的比較的布林值 (true
            表示不區分大小寫的比較)。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            等於 <paramref name="notExpected"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            測試指定的字串是否不相等，並在相等時
            擲回例外狀況。
            </summary>
            <param name="notExpected">
            要比較的第一個字串。測試預期這個字串
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個字串。這是正在測試的程式碼所產生的字串。
            </param>
            <param name="ignoreCase">
            表示區分大小寫或不區分大小寫的比較的布林值 (true
            表示不區分大小寫的比較)。
            </param>
            <param name="culture">
            提供文化特性 (culture) 特定比較資訊的 CultureInfo 物件。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            測試指定的字串是否不相等，並在相等時
            擲回例外狀況。
            </summary>
            <param name="notExpected">
            要比較的第一個字串。測試預期這個字串
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個字串。這是正在測試的程式碼所產生的字串。
            </param>
            <param name="ignoreCase">
            表示區分大小寫或不區分大小寫的比較的布林值 (true
            表示不區分大小寫的比較)。
            </param>
            <param name="culture">
            提供文化特性 (culture) 特定比較資訊的 CultureInfo 物件。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            等於 <paramref name="notExpected"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            測試指定的字串是否不相等，並在相等時
            擲回例外狀況。
            </summary>
            <param name="notExpected">
            要比較的第一個字串。測試預期這個字串
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個字串。這是正在測試的程式碼所產生的字串。
            </param>
            <param name="ignoreCase">
            表示區分大小寫或不區分大小寫的比較的布林值 (true
            表示不區分大小寫的比較)。
            </param>
            <param name="culture">
            提供文化特性 (culture) 特定比較資訊的 CultureInfo 物件。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            等於 <paramref name="notExpected"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type)">
            <summary>
            測試指定的物件是否為預期類型的執行個體，
            並在預期類型不在物件的繼承階層中時
            擲回例外狀況。
            </summary>
            <param name="value">
            測試預期為所指定類型的物件。
            </param>
            <param name="expectedType">
            下者的預期類型: <paramref name="value"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            測試指定的物件是否為預期類型的執行個體，
            並在預期類型不在物件的繼承階層中時
            擲回例外狀況。
            </summary>
            <param name="value">
            測試預期為所指定類型的物件。
            </param>
            <param name="expectedType">
            下者的預期類型: <paramref name="value"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="value"/>
            不是下者的執行個體: <paramref name="expectedType"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            測試指定的物件是否為預期類型的執行個體，
            並在預期類型不在物件的繼承階層中時
            擲回例外狀況。
            </summary>
            <param name="value">
            測試預期為所指定類型的物件。
            </param>
            <param name="expectedType">
            下者的預期類型: <paramref name="value"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="value"/>
            不是下者的執行個體: <paramref name="expectedType"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type)">
            <summary>
            測試指定的物件是否不是錯誤類型的執行個體，
            並在指定的類型位於物件的繼承階層中時
            擲回例外狀況。
            </summary>
            <param name="value">
            測試預期不為所指定類型的物件。
            </param>
            <param name="wrongType">
            下者不應該屬於的類型: <paramref name="value"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            測試指定的物件是否不是錯誤類型的執行個體，
            並在指定的類型位於物件的繼承階層中時
            擲回例外狀況。
            </summary>
            <param name="value">
            測試預期不為所指定類型的物件。
            </param>
            <param name="wrongType">
            下者不應該屬於的類型: <paramref name="value"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="value"/>
            是下者的執行個體: <paramref name="wrongType"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            測試指定的物件是否不是錯誤類型的執行個體，
            並在指定的類型位於物件的繼承階層中時
            擲回例外狀況。
            </summary>
            <param name="value">
            測試預期不為所指定類型的物件。
            </param>
            <param name="wrongType">
            下者不應該屬於的類型: <paramref name="value"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="value"/>
            是下者的執行個體: <paramref name="wrongType"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail">
            <summary>
            擲回 AssertFailedException。
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String)">
            <summary>
            擲回 AssertFailedException。
            </summary>
            <param name="message">
            要包含在例外狀況中的訊息。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String,System.Object[])">
            <summary>
            擲回 AssertFailedException。
            </summary>
            <param name="message">
            要包含在例外狀況中的訊息。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive">
            <summary>
            擲回 AssertInconclusiveException。
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String)">
            <summary>
            擲回 AssertInconclusiveException。
            </summary>
            <param name="message">
            要包含在例外狀況中的訊息。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String,System.Object[])">
            <summary>
            擲回 AssertInconclusiveException。
            </summary>
            <param name="message">
            要包含在例外狀況中的訊息。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Equals(System.Object,System.Object)">
            <summary>
            「靜態等於多載」用於比較兩種類型的執行個體的參考
            相等。這種方法<b></b>不應該用於比較兩個執行個體是否
            相等。這個物件一律<b></b>會擲出 Assert.Fail。請在單元測試中使用
            Assert.AreEqual 和相關聯多載。
            </summary>
            <param name="objA"> 物件 A </param>
            <param name="objB"> 物件 B </param>
            <returns> 一律為 False。 </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action)">
            <summary>
            測試委派 <paramref name="action"/> 所指定的程式碼會擲回 <typeparamref name="T"/> 類型的確切指定例外狀況 (而非衍生類型)
            並擲回
            <code>
            AssertFailedException
            </code>
            (若程式碼未擲回例外狀況或擲回非 <typeparamref name="T"/> 類型的例外狀況)。
            </summary>
            <param name="action">
            要測試程式碼並預期擲回例外狀況的委派。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            預期擲回的例外狀況類型。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String)">
            <summary>
            測試委派 <paramref name="action"/> 所指定的程式碼會擲回 <typeparamref name="T"/> 類型的確切指定例外狀況 (而非衍生類型)
            並擲回
            <code>
            AssertFailedException
            </code>
            (若程式碼未擲回例外狀況或擲回非 <typeparamref name="T"/> 類型的例外狀況)。
            </summary>
            <param name="action">
            要測試程式碼並預期擲回例外狀況的委派。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="action"/>
            未擲回下列類型的例外狀況: <typeparamref name="T"/>。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            預期擲回的例外狀況類型。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object})">
            <summary>
            測試委派 <paramref name="action"/> 所指定的程式碼會擲回 <typeparamref name="T"/> 類型的確切指定例外狀況 (而非衍生類型)
            並擲回
            <code>
            AssertFailedException
            </code>
            (若程式碼未擲回例外狀況或擲回非 <typeparamref name="T"/> 類型的例外狀況)。
            </summary>
            <param name="action">
            要測試程式碼並預期擲回例外狀況的委派。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            預期擲回的例外狀況類型。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String)">
            <summary>
            測試委派 <paramref name="action"/> 所指定的程式碼會擲回 <typeparamref name="T"/> 類型的確切指定例外狀況 (而非衍生類型)
            並擲回
            <code>
            AssertFailedException
            </code>
            (若程式碼未擲回例外狀況或擲回非 <typeparamref name="T"/> 類型的例外狀況)。
            </summary>
            <param name="action">
            要測試程式碼並預期擲回例外狀況的委派。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="action"/>
            未擲回下列類型的例外狀況: <typeparamref name="T"/>。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            預期擲回的例外狀況類型。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String,System.Object[])">
            <summary>
            測試委派 <paramref name="action"/> 所指定的程式碼會擲回 <typeparamref name="T"/> 類型的確切指定例外狀況 (而非衍生類型)
            並擲回
            <code>
            AssertFailedException
            </code>
            (若程式碼未擲回例外狀況或擲回非 <typeparamref name="T"/> 類型的例外狀況)。
            </summary>
            <param name="action">
            要測試程式碼並預期擲回例外狀況的委派。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="action"/>
            未擲回下列類型的例外狀況: <typeparamref name="T"/>。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throw exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            預期擲回的例外狀況類型。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String,System.Object[])">
            <summary>
            測試委派 <paramref name="action"/> 所指定的程式碼會擲回 <typeparamref name="T"/> 類型的確切指定例外狀況 (而非衍生類型)
            並擲回
            <code>
            AssertFailedException
            </code>
            (若程式碼未擲回例外狀況或擲回非 <typeparamref name="T"/> 類型的例外狀況)。
            </summary>
            <param name="action">
            要測試程式碼並預期擲回例外狀況的委派。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="action"/>
            未擲回下列類型的例外狀況: <typeparamref name="T"/>。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            預期擲回的例外狀況類型。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task})">
            <summary>
            測試委派 <paramref name="action"/> 所指定的程式碼會擲回 <typeparamref name="T"/> 類型的確切指定例外狀況 (而非衍生類型)
            並擲回
            <code>
            AssertFailedException
            </code>
            (若程式碼未擲回例外狀況或擲回非 <typeparamref name="T"/> 類型的例外狀況)。
            </summary>
            <param name="action">
            要測試程式碼並預期擲回例外狀況的委派。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
             <see cref="T:System.Threading.Tasks.Task"/> 執行委派。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            測試委派 <paramref name="action"/> 所指定的程式碼是否會擲回 <typeparamref name="T"/> 類型的確切指定例外狀況 (而非衍生類型)
            並於程式碼未擲回例外狀況或擲回非 <typeparamref name="T"/> 類型的例外狀況時，擲回 <code>AssertFailedException</code>。
            </summary>
            <param name="action">委派給要進行測試且預期會擲回例外狀況的程式碼。</param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="action"/>
            未擲回下列類型的例外狀況: <typeparamref name="T"/>。
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
             <see cref="T:System.Threading.Tasks.Task"/> 執行委派。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String,System.Object[])">
            <summary>
            測試委派 <paramref name="action"/> 所指定的程式碼是否會擲回 <typeparamref name="T"/> 類型的確切指定例外狀況 (而非衍生類型)
            並於程式碼未擲回例外狀況或擲回非 <typeparamref name="T"/> 類型的例外狀況時，擲回 <code>AssertFailedException</code>。
            </summary>
            <param name="action">委派給要進行測試且預期會擲回例外狀況的程式碼。</param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="action"/>
            未擲回下列類型的例外狀況: <typeparamref name="T"/>。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
             <see cref="T:System.Threading.Tasks.Task"/> 執行委派。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNullChars(System.String)">
            <summary>
            以 "\\0" 取代 null 字元 ('\0')。
            </summary>
            <param name="input">
            要搜尋的字串。
            </param>
            <returns>
            null 字元以 "\\0" 取代的已轉換字串。
            </returns>
            <remarks>
            This is only public and still present to preserve compatibility with the V1 framework.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.HandleFail(System.String,System.String,System.Object[])">
            <summary>
            建立並擲回 AssertionFailedException 的 Helper 函數
            </summary>
            <param name="assertionName">
            擲回例外狀況的判斷提示名稱
            </param>
            <param name="message">
            描述判斷提示失敗條件的訊息
            </param>
            <param name="parameters">
            參數。
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.CheckParameterNotNull(System.Object,System.String,System.String,System.String,System.Object[])">
            <summary>
            檢查參數的有效條件
            </summary>
            <param name="param">
            參數。
            </param>
            <param name="assertionName">
            判斷提示「名稱」。
            </param>
            <param name="parameterName">
            參數名稱
            </param>
            <param name="message">
            無效參數例外狀況的訊息
            </param>
            <param name="parameters">
            參數。
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNulls(System.Object)">
            <summary>
            將物件安全地轉換成字串，並處理 null 值和 null 字元。
            Null 值會轉換成 "(null)"。Null 字元會轉換成 "\\0"。
            </summary>
            <param name="input">
            要轉換為字串的物件。
            </param>
            <returns>
            已轉換的字串。
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert">
            <summary>
            字串判斷提示。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.That">
            <summary>
            取得 CollectionAssert 功能的單一執行個體。
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void ContainsWords(this StringAssert cusomtAssert, string value, ICollection substrings)"
            Users could then use a syntax similar to the default assertions which in this case is "StringAssert.That.ContainsWords(value, substrings);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String)">
            <summary>
            測試指定的字串是否包含指定的子字串，
            並在子字串未出現在測試字串內時
            擲回例外狀況。
            </summary>
            <param name="value">
            預期包含下者的字串: <paramref name="substring"/>。
            </param>
            <param name="substring">
            預期在下列時間內發生的字串: <paramref name="value"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String)">
            <summary>
            測試指定的字串是否包含指定的子字串，
            並在子字串未出現在測試字串內時
            擲回例外狀況。
            </summary>
            <param name="value">
            預期包含下者的字串: <paramref name="substring"/>。
            </param>
            <param name="substring">
            預期在下列時間內發生的字串: <paramref name="value"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="substring"/>
            未位於 <paramref name="value"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String,System.Object[])">
            <summary>
            測試指定的字串是否包含指定的子字串，
            並在子字串未出現在測試字串內時
            擲回例外狀況。
            </summary>
            <param name="value">
            預期包含下者的字串: <paramref name="substring"/>。
            </param>
            <param name="substring">
            預期在下列時間內發生的字串: <paramref name="value"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="substring"/>
            未位於 <paramref name="value"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String)">
            <summary>
            測試指定的字串開頭是否為指定的子字串，
            並在測試字串的開頭不是子字串時
            擲回例外狀況。
            </summary>
            <param name="value">
            字串預期開頭為 <paramref name="substring"/>。
            </param>
            <param name="substring">
            字串預期為下者的前置詞: <paramref name="value"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String)">
            <summary>
            測試指定的字串開頭是否為指定的子字串，
            並在測試字串的開頭不是子字串時
            擲回例外狀況。
            </summary>
            <param name="value">
            字串預期開頭為 <paramref name="substring"/>。
            </param>
            <param name="substring">
            字串預期為下者的前置詞: <paramref name="value"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="value"/>
            的開頭不是 <paramref name="substring"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            測試指定的字串開頭是否為指定的子字串，
            並在測試字串的開頭不是子字串時
            擲回例外狀況。
            </summary>
            <param name="value">
            字串預期開頭為 <paramref name="substring"/>。
            </param>
            <param name="substring">
            字串預期為下者的前置詞: <paramref name="value"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="value"/>
            的開頭不是 <paramref name="substring"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String)">
            <summary>
            測試指定的字串結尾是否為指定的子字串，
            並在測試字串的結尾不是子字串時
            擲回例外狀況。
            </summary>
            <param name="value">
            字串預期結尾為 <paramref name="substring"/>。
            </param>
            <param name="substring">
            字串預期為下者的字尾: <paramref name="value"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String)">
            <summary>
            測試指定的字串結尾是否為指定的子字串，
            並在測試字串的結尾不是子字串時
            擲回例外狀況。
            </summary>
            <param name="value">
            字串預期結尾為 <paramref name="substring"/>。
            </param>
            <param name="substring">
            字串預期為下者的字尾: <paramref name="value"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="value"/>
            的結尾不是 <paramref name="substring"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            測試指定的字串結尾是否為指定的子字串，
            並在測試字串的結尾不是子字串時
            擲回例外狀況。
            </summary>
            <param name="value">
            字串預期結尾為 <paramref name="substring"/>。
            </param>
            <param name="substring">
            字串預期為下者的字尾: <paramref name="value"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="value"/>
            的結尾不是 <paramref name="substring"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            測試指定的字串是否符合規則運算式，
            並在字串不符合運算式時擲回例外狀況。
            </summary>
            <param name="value">
            預期符合下者的字串: <paramref name="pattern"/>。
            </param>
            <param name="pattern">
            規則運算式，<paramref name="value"/> 
            預期相符。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            測試指定的字串是否符合規則運算式，
            並在字串不符合運算式時擲回例外狀況。
            </summary>
            <param name="value">
            預期符合下者的字串: <paramref name="pattern"/>。
            </param>
            <param name="pattern">
            規則運算式，<paramref name="value"/> 
            預期相符。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="value"/>
            不符合 <paramref name="pattern"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            測試指定的字串是否符合規則運算式，
            並在字串不符合運算式時擲回例外狀況。
            </summary>
            <param name="value">
            預期符合下者的字串: <paramref name="pattern"/>。
            </param>
            <param name="pattern">
            規則運算式，<paramref name="value"/> 
            預期相符。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="value"/>
            不符合 <paramref name="pattern"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            測試指定的字串是否不符合規則運算式，
            並在字串符合運算式時擲回例外狀況。
            </summary>
            <param name="value">
            預期不符合下者的字串: <paramref name="pattern"/>。
            </param>
            <param name="pattern">
            規則運算式，<paramref name="value"/> 
            預期不相符。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            測試指定的字串是否不符合規則運算式，
            並在字串符合運算式時擲回例外狀況。
            </summary>
            <param name="value">
            預期不符合下者的字串: <paramref name="pattern"/>。
            </param>
            <param name="pattern">
            規則運算式，<paramref name="value"/> 
            預期不相符。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="value"/>
            符合 <paramref name="pattern"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            測試指定的字串是否不符合規則運算式，
            並在字串符合運算式時擲回例外狀況。
            </summary>
            <param name="value">
            預期不符合下者的字串: <paramref name="pattern"/>。
            </param>
            <param name="pattern">
            規則運算式，<paramref name="value"/> 
            預期不相符。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="value"/>
            符合 <paramref name="pattern"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert">
            <summary>
            要測試與單元測試內集合相關聯之各種條件的
            協助程式類別集合。如果不符合正在測試的條件，
            則會擲回例外狀況。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.That">
            <summary>
            取得 CollectionAssert 功能的單一執行個體。
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void AreEqualUnordered(this CollectionAssert cusomtAssert, ICollection expected, ICollection actual)"
            Users could then use a syntax similar to the default assertions which in this case is "CollectionAssert.That.AreEqualUnordered(list1, list2);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object)">
            <summary>
            測試指定的集合是否包含指定的元素，
            並在元素不在集合中時擲回例外狀況。
            </summary>
            <param name="collection">
            在其中搜尋元素的集合。
            </param>
            <param name="element">
            預期在集合中的元素。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            測試指定的集合是否包含指定的元素，
            並在元素不在集合中時擲回例外狀況。
            </summary>
            <param name="collection">
            在其中搜尋元素的集合。
            </param>
            <param name="element">
            預期在集合中的元素。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="element"/>
            未位於 <paramref name="collection"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            測試指定的集合是否包含指定的元素，
            並在元素不在集合中時擲回例外狀況。
            </summary>
            <param name="collection">
            在其中搜尋元素的集合。
            </param>
            <param name="element">
            預期在集合中的元素。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="element"/>
            未位於 <paramref name="collection"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object)">
            <summary>
            測試指定的集合是否未包含指定的元素，
            並在元素在集合中時擲回例外狀況。
            </summary>
            <param name="collection">
            在其中搜尋元素的集合。
            </param>
            <param name="element">
            預期不在集合中的元素。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            測試指定的集合是否未包含指定的元素，
            並在元素在集合中時擲回例外狀況。
            </summary>
            <param name="collection">
            在其中搜尋元素的集合。
            </param>
            <param name="element">
            預期不在集合中的元素。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="element"/>
            位於 <paramref name="collection"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            測試指定的集合是否未包含指定的元素，
            並在元素在集合中時擲回例外狀況。
            </summary>
            <param name="collection">
            在其中搜尋元素的集合。
            </param>
            <param name="element">
            預期不在集合中的元素。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="element"/>
            位於 <paramref name="collection"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection)">
            <summary>
            測試所指定集合中的所有項目是否都為非 null，並在有任何元素為 null 時
            擲回例外狀況。
            </summary>
            <param name="collection">
            要在其中搜尋 null 元素的集合。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String)">
            <summary>
            測試所指定集合中的所有項目是否都為非 null，並在有任何元素為 null 時
            擲回例外狀況。
            </summary>
            <param name="collection">
            要在其中搜尋 null 元素的集合。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="collection"/>
            包含 null 元素。訊息會顯示在測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            測試所指定集合中的所有項目是否都為非 null，並在有任何元素為 null 時
            擲回例外狀況。
            </summary>
            <param name="collection">
            要在其中搜尋 null 元素的集合。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="collection"/>
            包含 null 元素。訊息會顯示在測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection)">
            <summary>
            測試所指定集合中的所有項目是否都是唯一的，
            並在集合中的任兩個元素相等時擲回例外狀況。
            </summary>
            <param name="collection">
            在其中搜尋重複元素的集合。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String)">
            <summary>
            測試所指定集合中的所有項目是否都是唯一的，
            並在集合中的任兩個元素相等時擲回例外狀況。
            </summary>
            <param name="collection">
            在其中搜尋重複元素的集合。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="collection"/>
            包含至少一個重複元素。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            測試所指定集合中的所有項目是否都是唯一的，
            並在集合中的任兩個元素相等時擲回例外狀況。
            </summary>
            <param name="collection">
            在其中搜尋重複元素的集合。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="collection"/>
            包含至少一個重複元素。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            測試其中一個集合是否為另一個集合的子集，
            並在子集中的任何元素也不在超集中時擲回
            例外狀況。
            </summary>
            <param name="subset">
            集合預期為下者的子集: <paramref name="superset"/>。
            </param>
            <param name="superset">
            集合預期為下者的超集: <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            測試其中一個集合是否為另一個集合的子集，
            並在子集中的任何元素也不在超集中時擲回
            例外狀況。
            </summary>
            <param name="subset">
            集合預期為下者的子集: <paramref name="superset"/>。
            </param>
            <param name="superset">
            集合預期為下者的超集: <paramref name="subset"/>
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: 下者中的元素:
            <paramref name="subset"/> 在下者中找不到: <paramref name="superset"/>。
            訊息會顯示在測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            測試其中一個集合是否為另一個集合的子集，
            並在子集中的任何元素也不在超集中時擲回
            例外狀況。
            </summary>
            <param name="subset">
            集合預期為下者的子集: <paramref name="superset"/>。
            </param>
            <param name="superset">
            集合預期為下者的超集: <paramref name="subset"/>
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: 下者中的元素:
            <paramref name="subset"/> 在下者中找不到: <paramref name="superset"/>。
            訊息會顯示在測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            測試其中一個集合是否不為另一個集合的子集，
            並在子集中的所有元素也都在超集中時擲回
            例外狀況。
            </summary>
            <param name="subset">
            集合預期不為下者的子集: <paramref name="superset"/>。
            </param>
            <param name="superset">
            集合預期不為下者的超集: <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            測試其中一個集合是否不為另一個集合的子集，
            並在子集中的所有元素也都在超集中時擲回
            例外狀況。
            </summary>
            <param name="subset">
            集合預期不為下者的子集: <paramref name="superset"/>。
            </param>
            <param name="superset">
            集合預期不為下者的超集: <paramref name="subset"/>
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: 下者中的每個元素:
            <paramref name="subset"/> 也會在下者中找到: <paramref name="superset"/>。
            訊息會顯示在測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            測試其中一個集合是否不為另一個集合的子集，
            並在子集中的所有元素也都在超集中時擲回
            例外狀況。
            </summary>
            <param name="subset">
            集合預期不為下者的子集: <paramref name="superset"/>。
            </param>
            <param name="superset">
            集合預期不為下者的超集: <paramref name="subset"/>
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: 下者中的每個元素:
            <paramref name="subset"/> 也會在下者中找到: <paramref name="superset"/>。
            訊息會顯示在測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            測試兩個集合是否包含相同元素，
            並在任一集合包含不在其他集合中的元素時
            擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個集合。這包含測試所預期的
            元素。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            測試兩個集合是否包含相同元素，
            並在任一集合包含不在其他集合中的元素時
            擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個集合。這包含測試所預期的
            元素。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <param name="message">
            在其中一個集合中找到元素但在另一個集合中找不到元素時
            要包含在例外狀況中的訊息。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            測試兩個集合是否包含相同元素，
            並在任一集合包含不在其他集合中的元素時
            擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個集合。這包含測試所預期的
            元素。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <param name="message">
            在其中一個集合中找到元素但在另一個集合中找不到元素時
            要包含在例外狀況中的訊息。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            測試兩個集合是否包含不同元素，並在兩個集合
            包含不管順序的相同元素時
            擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個集合。這包含測試預期與實際集合
            不同的元素。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            測試兩個集合是否包含不同元素，並在兩個集合
            包含不管順序的相同元素時
            擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個集合。這包含測試預期與實際集合
            不同的元素。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            包含與下者相同的元素: <paramref name="expected"/>。訊息
            會顯示在測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            測試兩個集合是否包含不同元素，並在兩個集合
            包含不管順序的相同元素時
            擲回例外狀況。
            </summary>
            <param name="expected">
            要比較的第一個集合。這包含測試預期與實際集合
            不同的元素。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            包含與下者相同的元素: <paramref name="expected"/>。訊息
            會顯示在測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type)">
            <summary>
            測試所指定集合中的所有元素是否為預期類型的執行個體，
            並在預期類型不在一或多個元素的繼承階層中時
            擲回例外狀況。
            </summary>
            <param name="collection">
            包含測試預期為所指定類型之元素
            的集合。
            </param>
            <param name="expectedType">
            下者的每個元素的預期類型: <paramref name="collection"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String)">
            <summary>
            測試所指定集合中的所有元素是否為預期類型的執行個體，
            並在預期類型不在一或多個元素的繼承階層中時
            擲回例外狀況。
            </summary>
            <param name="collection">
            包含測試預期為所指定類型之元素
            的集合。
            </param>
            <param name="expectedType">
            下者的每個元素的預期類型: <paramref name="collection"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: 下者中的元素:
            <paramref name="collection"/> 不是下者的執行個體:
            <paramref name="expectedType"/>。訊息會顯示在測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String,System.Object[])">
            <summary>
            測試所指定集合中的所有元素是否為預期類型的執行個體，
            並在預期類型不在一或多個元素的繼承階層中時
            擲回例外狀況。
            </summary>
            <param name="collection">
            包含測試預期為所指定類型之元素
            的集合。
            </param>
            <param name="expectedType">
            下者的每個元素的預期類型: <paramref name="collection"/>。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: 下者中的元素:
            <paramref name="collection"/> 不是下者的執行個體:
            <paramref name="expectedType"/>。訊息會顯示在測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            測試指定的集合是否相等，並在兩個集合不相等時
            擲回例外狀況。「相等」定義為具有相同順序和數量的
            相同元素。相同值的不同參考視為
            相等。
            </summary>
            <param name="expected">
            要比較的第一個集合。這是測試所預期的集合。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            測試指定的集合是否相等，並在兩個集合不相等時
            擲回例外狀況。「相等」定義為具有相同順序和數量的
            相同元素。相同值的不同參考視為
            相等。
            </summary>
            <param name="expected">
            要比較的第一個集合。這是測試所預期的集合。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            不等於 <paramref name="expected"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            測試指定的集合是否相等，並在兩個集合不相等時
            擲回例外狀況。「相等」定義為具有相同順序和數量的
            相同元素。相同值的不同參考視為
            相等。
            </summary>
            <param name="expected">
            要比較的第一個集合。這是測試所預期的集合。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            不等於 <paramref name="expected"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            測試指定的集合是否不相等，並在兩個集合相等時
            擲回例外狀況。「相等」定義為具有相同順序和數量的
            相同元素。相同值的不同參考視為
            相等。
            </summary>
            <param name="notExpected">
            要比較的第一個集合。測試預期這個集合
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            測試指定的集合是否不相等，並在兩個集合相等時
            擲回例外狀況。「相等」定義為具有相同順序和數量的
            相同元素。相同值的不同參考視為
            相等。
            </summary>
            <param name="notExpected">
            要比較的第一個集合。測試預期這個集合
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            等於 <paramref name="notExpected"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            測試指定的集合是否不相等，並在兩個集合相等時
            擲回例外狀況。「相等」定義為具有相同順序和數量的
            相同元素。相同值的不同參考視為
            相等。
            </summary>
            <param name="notExpected">
            要比較的第一個集合。測試預期這個集合
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            等於 <paramref name="notExpected"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            測試指定的集合是否相等，並在兩個集合不相等時
            擲回例外狀況。「相等」定義為具有相同順序和數量的
            相同元素。相同值的不同參考視為
            相等。
            </summary>
            <param name="expected">
            要比較的第一個集合。這是測試所預期的集合。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <param name="comparer">
            要在比較集合元素時使用的比較實作。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            測試指定的集合是否相等，並在兩個集合不相等時
            擲回例外狀況。「相等」定義為具有相同順序和數量的
            相同元素。相同值的不同參考視為
            相等。
            </summary>
            <param name="expected">
            要比較的第一個集合。這是測試所預期的集合。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <param name="comparer">
            要在比較集合元素時使用的比較實作。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            不等於 <paramref name="expected"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            測試指定的集合是否相等，並在兩個集合不相等時
            擲回例外狀況。「相等」定義為具有相同順序和數量的
            相同元素。相同值的不同參考視為
            相等。
            </summary>
            <param name="expected">
            要比較的第一個集合。這是測試所預期的集合。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <param name="comparer">
            要在比較集合元素時使用的比較實作。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            不等於 <paramref name="expected"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            在將下者格式化時要使用的參數陣列: <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            測試指定的集合是否不相等，並在兩個集合相等時
            擲回例外狀況。「相等」定義為具有相同順序和數量的
            相同元素。相同值的不同參考視為
            相等。
            </summary>
            <param name="notExpected">
            要比較的第一個集合。測試預期這個集合
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <param name="comparer">
            要在比較集合元素時使用的比較實作。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            測試指定的集合是否不相等，並在兩個集合相等時
            擲回例外狀況。「相等」定義為具有相同順序和數量的
            相同元素。相同值的不同參考視為
            相等。
            </summary>
            <param name="notExpected">
            要比較的第一個集合。測試預期這個集合
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <param name="comparer">
            要在比較集合元素時使用的比較實作。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            等於 <paramref name="notExpected"/>。訊息會顯示在
            測試結果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            測試指定的集合是否不相等，並在兩個集合相等時
            擲回例外狀況。「相等」定義為具有相同順序和數量的
            相同元素。相同值的不同參考視為
            相等。
            </summary>
            <param name="notExpected">
            要比較的第一個集合。測試預期這個集合
            不符合 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比較的第二個集合。這是正在測試的程式碼
            所產生的集合。
            </param>
            <param name="comparer">
            要在比較集合元素時使用的比較實作。
            </param>
            <param name="message">
            在下列情況下，要包含在例外狀況中的訊息: <paramref name="actual"/>
            等於 <paramref name="notExpected"/>。訊息會顯示在
            測試結果中。
            </param>
            <param name="parameters">
            參數陣列，使用時機為格式 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOfHelper(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            判斷第一個集合是否為第二個集合的子集。
            如果任一個集合包含重複的元素，則元素
            在子集中的出現次數必須小於或
            等於在超集中的出現次數。
            </summary>
            <param name="subset">
            測試預期包含在下者中的集合: <paramref name="superset"/>。
            </param>
            <param name="superset">
            測試預期包含下者的集合: <paramref name="subset"/>。
            </param>
            <returns>
            True 的情況為 <paramref name="subset"/> 是下者的子集:
            <paramref name="superset"/>，否則為 false。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.GetElementCounts(System.Collections.ICollection,System.Int32@)">
            <summary>
            建構字典，內含每個元素在所指定集合中
            的出現次數。
            </summary>
            <param name="collection">
            要處理的集合。
            </param>
            <param name="nullCount">
            集合中的 null 元素數目。
            </param>
            <returns>
            包含每個元素在所指定集合內之出現次數
            的字典。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.FindMismatchedElement(System.Collections.ICollection,System.Collections.ICollection,System.Int32@,System.Int32@,System.Object@)">
            <summary>
            尋找兩個集合之間不相符的元素。不相符的元素
            為出現在預期集合中的次數
            不同於它在實際集合中出現的次數。
            集合假設為具有數目相同之元素的不同非 null 參考。
            呼叫者負責這個層級的驗證。
            如果沒有不相符的元素，則函數會傳回 false，
            而且不應該使用 out 參數。
            </summary>
            <param name="expected">
            要比較的第一個集合。
            </param>
            <param name="actual">
            要比較的第二個集合。
            </param>
            <param name="expectedCount">
            下者的預期出現次數:
            <paramref name="mismatchedElement"/> 或 0 (如果沒有不相符的
            元素)。
            </param>
            <param name="actualCount">
            下者的實際出現次數:
            <paramref name="mismatchedElement"/> 或 0 (如果沒有不相符的
            元素)。
            </param>
            <param name="mismatchedElement">
            不相符的元素 (可能為 null) 或 null (如果沒有
            不相符的元素)。
            </param>
            <returns>
            如果找到不相符的元素，則為 true，否則為 false。
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.ObjectComparer">
            <summary>
            使用 object.Equals 來比較物件
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException">
            <summary>
            架構例外狀況的基底類別。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> 類別的新執行個體。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String,System.Exception)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> 類別的新執行個體。
            </summary>
            <param name="msg"> 訊息。 </param>
            <param name="ex"> 例外狀況。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> 類別的新執行個體。
            </summary>
            <param name="msg"> 訊息。 </param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages">
            <summary>
              強型別資源類別，用於查詢當地語系化字串等。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ResourceManager">
            <summary>
              傳回這個類別所使用的快取的 ResourceManager 執行個體。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Culture">
            <summary>
              針對使用這個強型別資源類別的所有資源查閱，
              覆寫目前執行緒的 CurrentUICulture 屬性。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AccessStringInvalidSyntax">
            <summary>
              查閱與「存取字串有無效的語法。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ActualHasMismatchedElements">
            <summary>
              查閱與「預期在集合中包含 {1} 項 &lt;{2}&gt;，但實際的集合卻有 {3} 項。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AllItemsAreUniqueFailMsg">
            <summary>
              查閱與「找到重複的項目:&lt;{1}&gt;。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualCaseFailMsg">
            <summary>
              查閱與「預期:&lt;{1}&gt;。大小寫與下列實際值不同:&lt;{2}&gt;。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDeltaFailMsg">
            <summary>
              查閱與「預期值 &lt;{1}&gt; 和實際值 &lt;{2}&gt; 之間的預期差異不大於 &lt;{3}&gt;。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDifferentTypesFailMsg">
            <summary>
              查閱與「預期:&lt;{1} ({2})&gt;。實際:&lt;{3} ({4})&gt;。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualFailMsg">
            <summary>
              查閱與「預期:&lt;{1}&gt;。實際:&lt;{2}&gt;。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualDeltaFailMsg">
            <summary>
              查閱與「預期值 &lt;{1}&gt; 和實際值 &lt;{2}&gt; 之間的預期差異大於 &lt;{3}&gt;。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualFailMsg">
            <summary>
              查閱與「預期任何值 (&lt;{1}&gt; 除外)。實際:&lt;{2}&gt;。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreSameGivenValues">
            <summary>
              查閱與「不要將實值型別傳遞給 AreSame()。轉換成 Object 的值從此不再一樣。請考慮使用 AreEqual()。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AssertionFailed">
            <summary>
              查閱與「{0} 失敗。{1}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AsyncUITestMethodNotSupported">
            <summary>
              不支援查詢類似非同步處理 TestMethod 與 UITestMethodAttribute 的當地語系化字串。移除非同步處理或使用 TestMethodAttribute。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsEmpty">
            <summary>
              查閱與「兩個集合都是空的。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameElements">
            <summary>
              查閱與「兩個集合含有相同的元素。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameReference">
            <summary>
              查閱與「兩個集合參考都指向同一個集合物件。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothSameElements">
            <summary>
              查閱與「兩個集合含有相同的元素。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.CollectionEqualReason">
            <summary>
              查閱與「{0}({1})」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_NullInMessages">
            <summary>
              查閱與「(null)」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_ObjectString">
            <summary>
              查閱與「(物件)」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ContainsFail">
            <summary>
              查閱與「字串 '{0}' 未包含字串 '{1}'。{2}。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DataDrivenResultDisplayName">
            <summary>
              查閱與「{0}({1})」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DoNotUseAssertEquals">
            <summary>
              查閱與「Assert.Equals 不應使用於判斷提示。請改用 Assert.AreEqual 及多載。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementNumbersDontMatch">
            <summary>
              查閱與「集合中的元素數目不符。預期:&lt;{1}&gt;。實際:&lt;{2}&gt;。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementsAtIndexDontMatch">
            <summary>
              查閱與「位於索引 {0} 的元素不符。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch">
            <summary>
              查閱與「位於索引 {1} 的項目不是預期的類型。預期的類型:&lt;{2}&gt;。實際的類型:&lt;{3}&gt;。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch2">
            <summary>
              查閱與「位於索引 {1} 的元素是 (null)。預期的類型:&lt;{2}&gt;。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EndsWithFail">
            <summary>
              查閱與「字串 '{0}' 不是以字串 '{1}' 結尾。{2}。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EqualsTesterInvalidArgs">
            <summary>
              查閱與「無效的引數 - EqualsTester 無法使用 null。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ErrorInvalidCast">
            <summary>
              查閱與「無法將 {0} 類型的物件轉換為 {1}。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InternalObjectNotValid">
            <summary>
              查閱與「所參考的內部物件已不再有效。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidParameterToAssert">
            <summary>
              查閱與「參數 '{0}' 無效。{1}。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidPropertyType">
            <summary>
              查閱與「屬性 {0} 具有類型 {1}; 預期為類型 {2}。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsInstanceOfFailMsg">
            <summary>
              查閱與「{0} 預期的類型:&lt;{1}&gt;。實際的類型:&lt;{2}&gt;。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsMatchFail">
            <summary>
              查閱與「字串 '{0}' 與模式 '{1}' 不符。{2}。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotInstanceOfFailMsg">
            <summary>
              查閱與「錯誤的類型:&lt;{1}&gt;。實際的類型:&lt;{2}&gt;。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotMatchFail">
            <summary>
              查閱與「字串 '{0}' 與模式 '{1}' 相符。{2}。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoDataRow">
            <summary>
              查閱與「未指定 DataRowAttribute。至少一個 DataRowAttribute 必須配合 DataTestMethodAttribute 使用。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoExceptionThrown">
            <summary>
              查閱與「未擲回任何例外狀況。預期為 {1} 例外狀況。{0}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NullParameterToAssert">
            <summary>
              查閱與「參數 '{0}' 無效。值不能為 null。{1}。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NumberOfElementsDiff">
            <summary>
              查閱與「元素數目不同。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorConstructorNotFound">
            <summary>
              查閱與「找不到具有所指定簽章的建構函式。
                 您可能必須重新產生私用存取子，或者該成員可能為私用，
                 並且定義在基底類別上。如果是後者，您必須將定義
                 該成員的類型傳送至 PrivateObject 的建構函式。」
                 類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorMemberNotFound">
            <summary>
              查閱與「找不到所指定的成員 ({0})。
                 您可能必須重新產生私用存取子，
                 或者該成員可能為私用，並且定義在基底類別上。如果是後者，您必須將定義該成員的類型
               傳送至 PrivateObject 的建構函式。」
            類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.StartsWithFail">
            <summary>
              查閱與「字串 '{0}' 不是以字串 '{1}' 開頭。{2}。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_ExpectedExceptionTypeMustDeriveFromException">
            <summary>
              查閱與「預期的例外狀況類型必須是 System.Exception 或衍生自 System.Exception 的類型。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_FailedToGetExceptionMessage">
            <summary>
              查閱與「(由於發生例外狀況，所以無法取得 {0} 類型之例外狀況的訊息。)」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoException">
            <summary>
              查閱與「測試方法未擲回預期的例外狀況 {0}。{1}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoExceptionDefault">
            <summary>
              查閱與「測試方法未擲回例外狀況。測試方法上定義的屬性 {0} 需要例外狀況。」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongException">
            <summary>
              查閱與「測試方法擲回例外狀況 {0}，但是需要的是例外狀況 {1}。例外狀況訊息: {2}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongExceptionDerivedAllowed">
            <summary>
              查閱與「測試方法擲回例外狀況 {0}，但是需要的是例外狀況 {1} 或由它衍生的類型。例外狀況訊息: {2}」類似的當地語系化字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.WrongExceptionThrown">
             <summary>
               查閱與「擲回例外狀況 {2}，但需要的是例外狀況 {1}。{0}
            例外狀況訊息: {3}
            堆疊追蹤: {4}」類似的當地語系化字串。
             </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome">
            <summary>
            單元測試結果
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Failed">
            <summary>
            已執行測試，但發生問題。
            問題可能包含例外狀況或失敗的判斷提示。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Inconclusive">
            <summary>
            測試已完成，但是無法指出成功還是失敗。
            可能用於已中止測試。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Passed">
            <summary>
            已執行測試且沒有任何問題。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.InProgress">
            <summary>
            目前正在執行測試。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Error">
            <summary>
            嘗試執行測試時發生系統錯誤。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Timeout">
            <summary>
            測試逾時。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Aborted">
            <summary>
            使用者已中止測試。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Unknown">
            <summary>
            測試處於未知狀態
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper">
            <summary>
            提供單元測試架構的協助程式功能
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper.GetExceptionMsg(System.Exception)">
            <summary>
            遞迴地取得例外狀況訊息 (包含所有內部例外狀況
            的訊息)
            </summary>
            <param name="ex">要為其取得訊息的例外狀況</param>
            <returns>含有錯誤訊息資訊的字串</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout">
            <summary>
            逾時的列舉，可以與 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> 類別搭配使用。
             列舉的類型必須相符
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout.Infinite">
            <summary>
            無限。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute">
            <summary>
            測試類別屬性。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute.GetTestMethodAttribute(Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute)">
            <summary>
            取得可讓您執行此測試的測試方法屬性。
            </summary>
            <param name="testMethodAttribute">此方法上所定義的測試方法屬性執行個體。</param>
            <returns>此 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/> 要用來執行此測試。</returns>
            <remarks>Extensions can override this method to customize how all methods in a class are run.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute">
            <summary>
            測試方法屬性。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            執行測試方法。
            </summary>
            <param name="testMethod">要執行的測試方法。</param>
            <returns>代表測試結果的 TestResult 物件陣列。</returns>
            <remarks>Extensions can override this method to customize running a TestMethod.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestInitializeAttribute">
            <summary>
            測試初始化屬性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCleanupAttribute">
            <summary>
            測試清除屬性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute">
            <summary>
            Ignore 屬性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute">
            <summary>
            測試屬性 (property) 屬性 (attribute)。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.#ctor(System.String,System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute"/> 類別的新執行個體。
            </summary>
            <param name="name">
            名稱。
            </param>
            <param name="value">
            值。
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Name">
            <summary>
            取得名稱。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Value">
            <summary>
            取得值。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute">
            <summary>
            類別會將屬性初始化。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute">
            <summary>
            類別清除屬性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyInitializeAttribute">
            <summary>
            組件會將屬性初始化。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyCleanupAttribute">
            <summary>
            組件清除屬性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute">
            <summary>
            測試擁有者
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.#ctor(System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute"/> 類別的新執行個體。
            </summary>
            <param name="owner">
            擁有者。
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.Owner">
            <summary>
            取得擁有者。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute">
            <summary>
            Priority 屬性; 用來指定單元測試的優先順序。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.#ctor(System.Int32)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute"/> 類別的新執行個體。
            </summary>
            <param name="priority">
            優先順序。
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.Priority">
            <summary>
            取得優先順序。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute">
            <summary>
            測試描述
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.#ctor(System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute"/> 類別的新執行個體來描述測試。
            </summary>
            <param name="description">描述。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.Description">
            <summary>
            取得測試的描述。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute">
            <summary>
            CSS 專案結構 URI
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.#ctor(System.String)">
            <summary>
            初始化用於 CSS 專案結構 URI 之 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute"/> 類別的新執行個體。
            </summary>
            <param name="cssProjectStructure">CSS 專案結構 URI。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.CssProjectStructure">
            <summary>
            取得 CSS 專案結構 URI。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute">
            <summary>
            CSS 反覆項目 URI
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.#ctor(System.String)">
            <summary>
            初始化用於 CSS 反覆項目 URI 之 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute"/> 類別的新執行個體。
            </summary>
            <param name="cssIteration">CSS 反覆項目 URI。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.CssIteration">
            <summary>
            取得 CSS 反覆項目 URI。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute">
            <summary>
            工作項目屬性; 用來指定與這個測試相關聯的工作項目。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.#ctor(System.Int32)">
            <summary>
            初始化用於工作項目屬性之 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute"/> 類別的新執行個體。
            </summary>
            <param name="id">工作項目的識別碼。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.Id">
            <summary>
            取得建立關聯之工作項目的識別碼。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute">
            <summary>
            Timeout 屬性; 用來指定單元測試的逾時。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(System.Int32)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> 類別的新執行個體。
            </summary>
            <param name="timeout">
            逾時。
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout)">
            <summary>
            初始化具有預設逾時之 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> 類別的新執行個體
            </summary>
            <param name="timeout">
            逾時
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.Timeout">
            <summary>
            取得逾時。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult">
            <summary>
            要傳回給配接器的 TestResult 物件。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.#ctor">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/> 類別的新執行個體。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DisplayName">
            <summary>
            取得或設定結果的顯示名稱。適用於傳回多個結果時。
            如果為 null，則使用「方法名稱」當成 DisplayName。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Outcome">
            <summary>
            取得或設定測試執行的結果。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestFailureException">
            <summary>
            取得或設定測試失敗時所擲回的例外狀況。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogOutput">
            <summary>
            取得或設定測試程式碼所記錄之訊息的輸出。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogError">
            <summary>
            取得或設定測試程式碼所記錄之訊息的輸出。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DebugTrace">
            <summary>
            透過測試程式碼取得或設定偵錯追蹤。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestContextMessages">
            <summary>
            Gets or sets the debug traces by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Duration">
            <summary>
            取得或設定測試執行的持續時間。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DatarowIndex">
            <summary>
            取得或設定資料來源中的資料列索引。僅針對個別執行資料驅動測試之資料列
            的結果所設定。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ReturnValue">
            <summary>
            取得或設定測試方法的傳回值 (目前一律為 null)。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ResultFiles">
            <summary>
            取得或設定測試所附加的結果檔案。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute">
            <summary>
            指定連接字串、表格名稱和資料列存取方法來進行資料驅動測試。
            </summary>
            <example>
            [DataSource("Provider=SQLOLEDB.1;Data Source=source;Integrated Security=SSPI;Initial Catalog=EqtCoverage;Persist Security Info=False", "MyTable")]
            [DataSource("dataSourceNameFromConfigFile")]
            </example>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultProviderName">
            <summary>
            資料來源的預設提供者名稱。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultDataAccessMethod">
            <summary>
            預設資料存取方法。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String,System.String,Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> 類別的新執行個體。將使用資料提供者、連接字串、運算列表和資料存取方法將這個執行個體初始化，以存取資料來源。
            </summary>
            <param name="providerInvariantName">非變異資料提供者名稱 (例如 System.Data.SqlClient)</param>
            <param name="connectionString">
            資料提供者特定連接字串。
            警告: 連接字串可能會包含敏感性資料 (例如，密碼)。
            連接字串是以純文字形式儲存在原始程式碼中和編譯的組件中。
            限制對原始程式碼和組件的存取，以保護這項機密資訊。
            </param>
            <param name="tableName">運算列表的名稱。</param>
            <param name="dataAccessMethod">指定資料的存取順序。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> 類別的新執行個體。此執行個體將使用連接字串和表格名稱進行初始化。
            指定連接字串和運算列表以存取 OLEDB 資料來源。
            </summary>
            <param name="connectionString">
            資料提供者特定連接字串。
            警告: 連接字串可能會包含敏感性資料 (例如，密碼)。
            連接字串是以純文字形式儲存在原始程式碼中和編譯的組件中。
            限制對原始程式碼和組件的存取，以保護這項機密資訊。
            </param>
            <param name="tableName">運算列表的名稱。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> 類別的新執行個體。將使用與設定名稱相關聯的資料提供者和連接字串將這個執行個體初始化。
            </summary>
            <param name="dataSourceSettingName">在 app.config 檔案的 &lt;microsoft.visualstudio.qualitytools&gt; 區段中找到資料來源名稱。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ProviderInvariantName">
            <summary>
            取得值，代表資料來源的資料提供者。
            </summary>
            <returns>
            資料提供者名稱。如果未在物件初始化時指定資料提供者，將會傳回 System.Data.OleDb 的預設提供者。
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ConnectionString">
            <summary>
            取得值，代表資料來源的連接字串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.TableName">
            <summary>
            取得值，指出提供資料的表格名稱。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataAccessMethod">
             <summary>
             取得用來存取資料來源的方法。
             </summary>
            
             <returns>
             下列其中之一:<see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod"/> 值。如果 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> 未進行初始化，則這會傳回預設值 <see cref="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random"/>。
             </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataSourceSettingName">
            <summary>
            取得在 app.config 檔案 &lt;microsoft.visualstudio.qualitytools&gt; 區段中找到的資料來源名稱。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute">
            <summary>
            可在其中內嵌指定資料之資料驅動測試的屬性。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            尋找所有資料列，並執行。
            </summary>
            <param name="testMethod">
            測試「方法」。
            </param>
            <returns>
            下列項目的陣列: <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.RunDataDrivenTest(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod,Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute[])">
            <summary>
            執行資料驅動測試方法。
            </summary>
            <param name="testMethod"> 要執行的測試方法。 </param>
            <param name="dataRows"> 資料列。 </param>
            <returns> 執行結果。 </returns>
        </member>
    </members>
</doc>
