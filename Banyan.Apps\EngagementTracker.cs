using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using Banyan.Code;
using Banyan.Domain;

namespace Banyan.Apps
{
    /// <summary>
    /// Tracks user engagement with recommendations and updates user interest profiles accordingly.
    /// This class is responsible for recording user interactions with recommendations and
    /// updating user interest profiles based on those interactions.
    /// </summary>
    public class EngagementTracker
    {
        private readonly NewsRecommendationsBLL _recommendationBLL;
        private readonly NewsBLL _newsBLL;
        private readonly UserProfileBLL _userProfileBLL;
        private readonly NewsVectorizationService _vectorizationService;
        private readonly RecommendationCacheManager _cacheManager;
        private readonly EngagementRecordsBLL _engagementRecordsBLL;
        private readonly UserInterestProfileUpdater _profileUpdater;

        /// <summary>
        /// Constructor
        /// </summary>
        public EngagementTracker()
        {
            _recommendationBLL = new NewsRecommendationsBLL();
            _newsBLL = new NewsBLL();
            _userProfileBLL = new UserProfileBLL();
            _vectorizationService = new NewsVectorizationService();
            _cacheManager = RecommendationCacheManager.Instance;
            _engagementRecordsBLL = new EngagementRecordsBLL();
            _profileUpdater = new UserInterestProfileUpdater();
        }

        /// <summary>
        /// Tracks a click event on a recommended news article
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="userName">User name</param>
        /// <param name="newsId">News ID</param>
        /// <param name="source">Source of the click (web or email)</param>
        /// <returns>True if tracking was successful, false otherwise</returns>
        public async Task<bool> TrackClickAsync(int userId, string userName, int newsId, string source)
        {
            try
            {
                // Validate input parameters
                if (userId <= 0)
                {
                    Logger.Error("Cannot track click: Invalid user ID");
                    return false;
                }

                if (string.IsNullOrWhiteSpace(userName))
                {
                    Logger.Error("Cannot track click: Empty user name");
                    return false;
                }

                if (newsId <= 0)
                {
                    Logger.Error("Cannot track click: Invalid news ID");
                    return false;
                }

                if (string.IsNullOrWhiteSpace(source))
                {
                    Logger.Error("Cannot track click: Empty source");
                    return false;
                }

                // Validate source is either "web" or "email"
                if (source != "web" && source != "email")
                {
                    Logger.Warn($"Invalid source '{source}' for engagement tracking, defaulting to 'web'");
                    source = "web";
                }

                Logger.Info($"Tracking click for user {userId} ({userName}) on news {newsId} from {source}");

                // Get the news details
                var news = _newsBLL.GetModel(newsId);
                if (news == null)
                {
                    Logger.Warn($"Cannot track click: News {newsId} not found");
                    return false;
                }

                // Record the engagement
                await _engagementRecordsBLL.RecordEngagementAsync(userName, newsId, news.Title, source);

                // Update the recommendation click status if it exists
                await UpdateRecommendationClickStatusAsync(userId, newsId);

                // Update the user's interest profile based on the clicked article
                await UpdateUserInterestProfileAsync(userId, newsId, source);

                // Invalidate the user's recommendation cache to ensure fresh recommendations on next request
                await _cacheManager.InvalidateUserCacheAsync(userId);

                Logger.Info($"Successfully tracked click for user {userId} on news {newsId}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error tracking click for user {userId} on news {newsId}: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// Updates the click status of a recommendation if it exists
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="newsId">News ID</param>
        /// <returns>True if update was successful, false otherwise</returns>
        private async Task<bool> UpdateRecommendationClickStatusAsync(int userId, int newsId)
        {
            try
            {
                // Find the recommendation record
                var recommendations = await _recommendationBLL.GetUserRecommendationsAsync(userId, 100, true);
                var recommendation = recommendations.Find(r => r.NewsId == newsId);
                
                if (recommendation != null)
                {
                    // Update the clicked status
                    await _recommendationBLL.UpdateClickedStatusAsync(recommendation.Id, true);
                    Logger.Info($"Updated click status for recommendation {recommendation.Id}");
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error updating recommendation click status for user {userId}, news {newsId}: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// Gets the engagement history for a specific user
        /// </summary>
        /// <param name="userName">User name</param>
        /// <param name="limit">Maximum number of records to return</param>
        /// <param name="startDate">Optional start date filter</param>
        /// <param name="endDate">Optional end date filter</param>
        /// <returns>List of engagement records</returns>
        public async Task<List<EngagementRecords>> GetUserEngagementHistoryAsync(string userName, int limit = 100, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                Logger.Info($"Getting engagement history for user {userName}");
                return await _recommendationBLL.GetUserEngagementHistoryAsync(userName, limit, startDate, endDate);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting engagement history for user {userName}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets the total count of engagement records for a specific user within a date range
        /// </summary>
        /// <param name="userName">User name</param>
        /// <param name="startDate">Start date filter</param>
        /// <param name="endDate">End date filter</param>
        /// <returns>Total count of engagement records</returns>
        public async Task<int> GetUserEngagementCountAsync(string userName, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                Logger.Info($"Getting engagement count for user {userName} from {startDate} to {endDate}");

                // Build where clause for filtering
                var whereConditions = new List<string> { "UserName = @UserName" };
                var parameters = new List<SqlParameter> { new SqlParameter("@UserName", userName) };

                if (startDate.HasValue)
                {
                    whereConditions.Add("Timestamp >= @StartDate");
                    parameters.Add(new SqlParameter("@StartDate", startDate.Value));
                }

                if (endDate.HasValue)
                {
                    whereConditions.Add("Timestamp <= @EndDate");
                    parameters.Add(new SqlParameter("@EndDate", endDate.Value));
                }

                string where = string.Join(" AND ", whereConditions);

                // Get count using DAL.Base
                var records = await Task.Run(() => _engagementRecordsBLL.GetList(where, parameters.ToArray()));
                int count = records?.Count ?? 0;

                Logger.Info($"Found {count} engagement records for user {userName}");
                return count;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting engagement count for user {userName}: {ex.Message}", ex);
                return 0;
            }
        }

        /// <summary>
        /// Gets engagement statistics for a specific period
        /// </summary>
        /// <param name="startDate">Start date for statistics</param>
        /// <param name="endDate">End date for statistics</param>
        /// <returns>Engagement statistics</returns>
        public async Task<EngagementStatistics> GetEngagementStatisticsAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                // Validate date parameters
                if (startDate > endDate)
                {
                    Logger.Error($"Invalid date range: start date {startDate:yyyy-MM-dd} is after end date {endDate:yyyy-MM-dd}");
                    throw new ArgumentException("Start date must be before or equal to end date");
                }

                Logger.Info($"Getting engagement statistics from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
                return await _recommendationBLL.GetEngagementStatisticsAsync(startDate, endDate);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting engagement statistics: {ex.Message}", ex);
                throw;
            }
        }
        
        /// <summary>
        /// Tracks a view event on a recommended news article
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="userName">User name</param>
        /// <param name="newsId">News ID</param>
        /// <param name="source">Source of the view (web or email)</param>
        /// <returns>True if tracking was successful, false otherwise</returns>
        public async Task<bool> TrackViewAsync(int userId, string userName, int newsId, string source)
        {
            try
            {
                // Validate input parameters
                if (userId <= 0 || string.IsNullOrWhiteSpace(userName) || newsId <= 0 || string.IsNullOrWhiteSpace(source))
                {
                    Logger.Error($"Cannot track view: Invalid parameters - userId: {userId}, userName: {userName}, newsId: {newsId}, source: {source}");
                    return false;
                }

                Logger.Info($"Tracking view for user {userId} ({userName}) on news {newsId} from {source}");

                // Get the news details
                var news = _newsBLL.GetModel(newsId);
                if (news == null)
                {
                    Logger.Warn($"Cannot track view: News {newsId} not found");
                    return false;
                }

                // Update the recommendation read status if it exists
                var recommendations = await _recommendationBLL.GetUserRecommendationsAsync(userId, 100, true);
                var recommendation = recommendations.Find(r => r.NewsId == newsId);
                
                if (recommendation != null)
                {
                    // Update the read status
                    await _recommendationBLL.UpdateReadStatusAsync(recommendation.Id, true);
                    Logger.Info($"Updated read status for recommendation {recommendation.Id}");
                }

                Logger.Info($"Successfully tracked view for user {userId} on news {newsId}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error tracking view for user {userId} on news {newsId}: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// Updates a user's interest profile based on a clicked news article
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="newsId">News ID</param>
        /// <param name="source">Source of the engagement (defaults to "web")</param>
        /// <returns>True if update was successful, false otherwise</returns>
        private async Task<bool> UpdateUserInterestProfileAsync(int userId, int newsId, string source = "web")
        {
            try
            {
                Logger.Info($"Updating interest profile for user {userId} based on news {newsId} from {source}");
                
                // Use the new UserInterestProfileUpdater to update the profile with weighted engagement
                bool success = await _profileUpdater.UpdateProfileBasedOnEngagementAsync(userId, newsId, source);

                if (success)
                {
                    Logger.Info($"Successfully updated interest profile for user {userId} using weighted engagement");
                }
                else
                {
                    Logger.Warn($"Failed to update interest profile for user {userId}");
                }

                return success;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error updating interest profile for user {userId}: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// Tracks multiple engagement events in batch
        /// </summary>
        /// <param name="engagements">List of engagement records to track</param>
        /// <returns>Number of successfully tracked engagements</returns>
        public async Task<int> TrackEngagementBatchAsync(List<(int userId, string userName, int newsId, string source)> engagements)
        {
            if (engagements == null || engagements.Count == 0)
            {
                Logger.Warn("Empty engagement list provided for batch tracking");
                return 0;
            }

            try
            {
                Logger.Info($"Tracking batch of {engagements.Count} engagements");
                
                int successCount = 0;
                foreach (var (userId, userName, newsId, source) in engagements)
                {
                    try
                    {
                        bool success = await TrackClickAsync(userId, userName, newsId, source);
                        if (success)
                        {
                            successCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"Error tracking engagement for user {userId}, news {newsId}: {ex.Message}", ex);
                    }
                }

                Logger.Info($"Successfully tracked {successCount}/{engagements.Count} engagements in batch");
                return successCount;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error tracking engagement batch: {ex.Message}", ex);
                return 0;
            }
        }

        /// <summary>
        /// Gets daily engagement counts for a specific period
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Dictionary mapping dates to engagement counts</returns>
        public async Task<Dictionary<DateTime, int>> GetDailyEngagementCountsAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                // Validate date parameters
                if (startDate > endDate)
                {
                    Logger.Error($"Invalid date range: start date {startDate:yyyy-MM-dd} is after end date {endDate:yyyy-MM-dd}");
                    throw new ArgumentException("Start date must be before or equal to end date");
                }

                Logger.Info($"Getting daily engagement counts from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
                
                var result = new Dictionary<DateTime, int>();
                
                // Use DAL.Base GetList API instead of direct SQL
                string where = "Timestamp BETWEEN @StartDate AND @EndDate";
                var parameters = new SqlParameter[]
                {
                    new SqlParameter("@StartDate", startDate),
                    new SqlParameter("@EndDate", endDate)
                };
                
                // Get all engagement records for the period
                var records = await Task.Run(() => _engagementRecordsBLL.GetList(where, parameters));
                
                // Group by date and count
                var groupedRecords = records
                    .GroupBy(r => r.Timestamp.Date)
                    .ToDictionary(g => g.Key, g => g.Count());
                
                // Add grouped records to result
                foreach (var group in groupedRecords)
                {
                    result[group.Key] = group.Value;
                }
                
                // Fill in missing dates with zero counts
                for (DateTime date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
                {
                    if (!result.ContainsKey(date))
                    {
                        result[date] = 0;
                    }
                }
                
                Logger.Info($"Retrieved daily engagement counts for {result.Count} days");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting daily engagement counts: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets source distribution of engagements for a specific period
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Dictionary mapping sources to engagement counts</returns>
        public async Task<Dictionary<string, int>> GetEngagementSourceDistributionAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                Logger.Info($"Getting engagement source distribution from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
                
                var result = new Dictionary<string, int>();
                
                // Use DAL.Base GetList API instead of direct SQL
                string where = "Timestamp BETWEEN @StartDate AND @EndDate";
                var parameters = new SqlParameter[]
                {
                    new SqlParameter("@StartDate", startDate),
                    new SqlParameter("@EndDate", endDate)
                };
                
                // Get all engagement records for the period
                var records = await Task.Run(() => _engagementRecordsBLL.GetList(where, parameters));
                
                // Group by source and count
                var groupedRecords = records
                    .GroupBy(r => r.Source)
                    .ToDictionary(g => g.Key, g => g.Count());
                
                // Add grouped records to result
                foreach (var group in groupedRecords)
                {
                    result[group.Key] = group.Value;
                }
                
                Logger.Info($"Retrieved engagement source distribution: {string.Join(", ", result.Select(kv => $"{kv.Key}={kv.Value}"))}");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting engagement source distribution: {ex.Message}", ex);
                throw;
            }
        }
    }
}