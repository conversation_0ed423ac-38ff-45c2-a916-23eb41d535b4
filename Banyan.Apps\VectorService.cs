using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Data.SqlClient;
using Banyan.Code;
using System.Text;
using System.Configuration;
using Banyan.Apps.Configs;

namespace Banyan.Apps
{
    /// <summary>
    /// 向量服务类
    /// 负责向量相似度计算和向量数据库操作
    /// 
    /// 职责分工：
    /// - SQL Server: 向量字符串存储、结构化数据管理
    /// - VectorService: 距离计算、向量预处理、业务逻辑
    /// </summary>
    public class VectorService
    {
        private readonly string _connectionString;
        private readonly ICache _cache;
        // 配置常量已迁移到 VectorServiceConfig 类中

        public VectorService()
        {
            try
            {
                var connectionStringSettings = ConfigurationManager.ConnectionStrings["QLWL"];
                _connectionString = connectionStringSettings?.ConnectionString ?? "Data Source=localhost;Initial Catalog=TestDB;Integrated Security=true";
                _cache = CacheFactory.Cache();
            }
            catch (Exception)
            {
                // Fallback for test environment
                _connectionString = "Data Source=localhost;Initial Catalog=TestDB;Integrated Security=true";
                _cache = CacheFactory.Cache();
            }
        }

        #region 本地距离计算

        /// <summary>
        /// 计算余弦相似度
        /// 本地计算，用于向量比较
        /// </summary>
        /// <param name="vector1">向量1</param>
        /// <param name="vector2">向量2</param>
        /// <returns>余弦相似度分数</returns>
        public double CalculateCosineSimilarity(double[] vector1, double[] vector2)
        {
            if (!CheckVectorDimensions(vector1, vector2))
            {
                throw new ArgumentException("向量维度不一致");
            }

            double dotProduct = 0.0;
            double norm1 = 0.0;
            double norm2 = 0.0;

            for (int i = 0; i < vector1.Length; i++)
            {
                dotProduct += vector1[i] * vector2[i];
                norm1 += vector1[i] * vector1[i];
                norm2 += vector2[i] * vector2[i];
            }

            norm1 = Math.Sqrt(norm1);
            norm2 = Math.Sqrt(norm2);

            if (norm1 == 0 || norm2 == 0)
                return 0.0;

            return dotProduct / (norm1 * norm2);
        }

        /// <summary>
        /// 计算点积相似度
        /// 本地计算，适用于已归一化的向量
        /// </summary>
        /// <param name="vector1">向量1</param>
        /// <param name="vector2">向量2</param>
        /// <returns>点积相似度分数</returns>
        public double CalculateDotProductSimilarity(double[] vector1, double[] vector2)
        {
            if (!CheckVectorDimensions(vector1, vector2))
            {
                throw new ArgumentException("向量维度不一致");
            }

            double dotProduct = 0.0;
            for (int i = 0; i < vector1.Length; i++)
            {
                dotProduct += vector1[i] * vector2[i];
            }

            return dotProduct;
        }

        /// <summary>
        /// 计算欧几里得距离
        /// 本地计算，用于距离验证
        /// </summary>
        /// <param name="vector1">向量1</param>
        /// <param name="vector2">向量2</param>
        /// <returns>欧几里得距离</returns>
        public double CalculateEuclideanDistance(double[] vector1, double[] vector2)
        {
            if (!CheckVectorDimensions(vector1, vector2))
            {
                throw new ArgumentException("向量维度不一致");
            }

            double sum = 0.0;
            for (int i = 0; i < vector1.Length; i++)
            {
                double diff = vector1[i] - vector2[i];
                sum += diff * diff;
            }

            return Math.Sqrt(sum);
        }

        /// <summary>
        /// 计算曼哈顿距离
        /// 本地计算，用于特殊场景
        /// </summary>
        /// <param name="vector1">向量1</param>
        /// <param name="vector2">向量2</param>
        /// <returns>曼哈顿距离</returns>
        public double CalculateManhattanDistance(double[] vector1, double[] vector2)
        {
            if (!CheckVectorDimensions(vector1, vector2))
            {
                throw new ArgumentException("向量维度不一致");
            }

            double sum = 0.0;
            for (int i = 0; i < vector1.Length; i++)
            {
                sum += Math.Abs(vector1[i] - vector2[i]);
            }

            return sum;
        }

        #endregion

        #region SQL Server向量数据库操作（存储和检索）

        /// <summary>
        /// 存储用户向量到SQL Server
        /// SQL Server负责：向量字符串存储、结构化数据管理
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="vector">用户向量</param>
        /// <param name="tags">标签信息</param>
        /// <returns>操作结果</returns>
        public async Task<bool> StoreUserVectorAsync(int userId, double[] vector, string tags = null)
        {
            try
            {
                // 向量归一化处理
                var normalizedVector = NormalizeVector(vector);
                var vectorString = VectorToString(normalizedVector);
                
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    var sql = @"
                        IF EXISTS (SELECT 1 FROM UserVector WHERE UserId = @UserId)
                            UPDATE UserVector 
                            SET VectorString = @VectorString, Tags = @Tags, UpdateTime = GETDATE()
                            WHERE UserId = @UserId
                        ELSE
                            INSERT INTO UserVector (UserId, VectorString, VectorDimension, Tags, CreateTime, UpdateTime)
                            VALUES (@UserId, @VectorString, @VectorDimension, @Tags, GETDATE(), GETDATE())";
                    
                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@UserId", userId);
                        command.Parameters.AddWithValue("@VectorString", vectorString);
                        command.Parameters.AddWithValue("@VectorDimension", VectorServiceConfig.VECTOR_DIMENSION);
                        command.Parameters.AddWithValue("@Tags", (object)tags ?? DBNull.Value);
                        
                        await command.ExecuteNonQueryAsync();
                    }
                }
                
                Logger.Info($"用户向量存储成功: UserId={userId}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"存储用户向量失败: UserId={userId}", ex);
                return false;
            }
        }

        /// <summary>
        /// 存储新闻向量到SQL Server
        /// SQL Server负责：向量字符串存储、结构化数据管理
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <param name="vector">新闻向量</param>
        /// <param name="tags">标签信息</param>
        /// <returns>操作结果</returns>
        public async Task<bool> StoreNewsVectorAsync(int newsId, double[] vector, string tags = null)
        {
            try
            {
                // 向量归一化处理
                var normalizedVector = NormalizeVector(vector);
                var vectorString = VectorToString(normalizedVector);

                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var sql = @"
                        UPDATE News
                        SET NewsVector = @VectorString,
                            TagAnalysis = @Tags,
                            VectorUpdateTime = GETDATE(),
                            VectorStatus = 1
                        WHERE Id = @NewsId";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@NewsId", newsId);
                        command.Parameters.AddWithValue("@VectorString", vectorString);
                        command.Parameters.AddWithValue("@VectorDimension", VectorServiceConfig.VECTOR_DIMENSION);
                        command.Parameters.AddWithValue("@Tags", (object)tags ?? DBNull.Value);

                        await command.ExecuteNonQueryAsync();
                    }
                }

                Logger.Info($"新闻向量存储成功: NewsId={newsId}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"存储新闻向量失败: NewsId={newsId}", ex);
                return false;
            }
        }

        /// <summary>
        /// 从SQL Server获取用户向量
        /// SQL Server负责：高效检索、数据查询
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户向量</returns>
        public async Task<double[]> GetUserVectorAsync(int userId)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    var sql = "SELECT VectorString FROM UserVector WHERE UserId = @UserId";
                    
                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@UserId", userId);
                        
                        var result = await command.ExecuteScalarAsync();
                        if (result != null && result != DBNull.Value)
                        {
                            var vectorString = result.ToString();
                            return StringToVector(vectorString);
                        }
                    }
                }
                
                return null;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取用户向量失败: UserId={userId}", ex);
                return null;
            }
        }

        /// <summary>
        /// 从SQL Server获取新闻向量
        /// SQL Server负责：高效检索、数据查询
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <returns>新闻向量</returns>
        public async Task<double[]> GetNewsVectorAsync(int newsId)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var sql = "SELECT VectorString FROM NewsVector WHERE NewsId = @NewsId";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@NewsId", newsId);

                        var result = await command.ExecuteScalarAsync();
                        if (result != null && result != DBNull.Value)
                        {
                            var vectorString = result.ToString();
                            return StringToVector(vectorString);
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取新闻向量失败: NewsId={newsId}", ex);
                return null;
            }
        }

        /// <summary>
        /// 使用SQL Server进行向量相似度搜索
        /// SQL Server负责：数据查询
        /// VectorService负责：向量计算和结果处理
        /// </summary>
        /// <param name="queryVector">查询向量</param>
        /// <param name="topK">返回结果数量</param>
        /// <param name="minSimilarity">最小相似度阈值</param>
        /// <returns>相似度搜索结果</returns>
        public async Task<List<VectorSearchResult>> SearchSimilarNewsAsync(double[] queryVector, int topK = 10, double minSimilarity = 0.5)
        {
            try
            {
                // 向量归一化处理
                var normalizedQueryVector = NormalizeVector(queryVector);
                
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    var sql = "SELECT Id, NewsVector, TagAnalysis FROM News WHERE NewsVector IS NOT NULL AND NewsVector != ''";
                    
                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        var searchResults = new List<VectorSearchResult>();
                        
                        while (await reader.ReadAsync())
                        {
                            var newsId = reader.GetInt32(0);  // NewsId column (first column)
                            var vectorString = reader.GetString(1);  // VectorString column (second column)
                            var tags = reader.IsDBNull(2) ? null : reader.GetString(2);  // Tags column (third column)

                            // VectorService负责：本地向量计算
                            var newsVector = StringToVector(vectorString);
                            var similarity = CalculateCosineSimilarity(normalizedQueryVector, newsVector);

                            if (similarity >= minSimilarity)
                            {
                                searchResults.Add(new VectorSearchResult
                                {
                                    Id = newsId.ToString(),
                                    Score = similarity,
                                    Vector = newsVector,
                                    Metadata = new Dictionary<string, object>
                                    {
                                        ["Tags"] = tags,
                                        ["NewsId"] = newsId
                                    }
                                });
                            }
                        }

                        return searchResults.OrderByDescending(r => r.Score).Take(topK).ToList();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"向量相似度搜索失败", ex);
                return new List<VectorSearchResult>();
            }
        }

        /// <summary>
        /// 批量向量相似度搜索
        /// SQL Server负责：批量数据查询
        /// VectorService负责：批量向量计算
        /// </summary>
        /// <param name="queryVectors">查询向量列表</param>
        /// <param name="topK">每个查询返回结果数量</param>
        /// <param name="minSimilarity">最小相似度阈值</param>
        /// <returns>批量相似度搜索结果</returns>
        public async Task<List<List<VectorSearchResult>>> BatchSearchSimilarNewsAsync(List<double[]> queryVectors, int topK = 10, double minSimilarity = 0.5)
        {
            var batchResults = new List<List<VectorSearchResult>>();
            
            foreach (var queryVector in queryVectors)
            {
                var results = await SearchSimilarNewsAsync(queryVector, topK, minSimilarity);
                batchResults.Add(results);
            }
            
            return batchResults;
        }

        #endregion

        #region 向量预处理（本地处理）

        /// <summary>
        /// 向量归一化
        /// 本地处理，为存储做准备
        /// </summary>
        /// <param name="vector">原始向量</param>
        /// <returns>归一化后的向量</returns>
        public double[] NormalizeVector(double[] vector)
        {
            if (vector == null || vector.Length == 0)
                return vector;

            double norm = 0.0;
            for (int i = 0; i < vector.Length; i++)
            {
                norm += vector[i] * vector[i];
            }
            norm = Math.Sqrt(norm);

            if (norm == 0)
                return vector;

            var normalized = new double[vector.Length];
            for (int i = 0; i < vector.Length; i++)
            {
                normalized[i] = vector[i] / norm;
            }

            return normalized;
        }

        /// <summary>
        /// 批量向量归一化
        /// 本地处理，为批量存储做准备
        /// </summary>
        /// <param name="vectors">向量列表</param>
        /// <returns>归一化后的向量列表</returns>
        public List<double[]> NormalizeVectors(List<double[]> vectors)
        {
            var normalizedVectors = new List<double[]>();
            
            foreach (var vector in vectors)
            {
                var normalized = NormalizeVector(vector);
                normalizedVectors.Add(normalized);
            }
            
            return normalizedVectors;
        }

        /// <summary>
        /// 向量维度检查
        /// 本地验证，确保向量格式正确
        /// </summary>
        /// <param name="vector1">向量1</param>
        /// <param name="vector2">向量2</param>
        /// <returns>维度是否一致</returns>
        public bool CheckVectorDimensions(double[] vector1, double[] vector2)
        {
            return vector1 != null && vector2 != null && vector1.Length == vector2.Length;
        }

        /// <summary>
        /// 将向量转换为字符串
        /// 本地处理，为SQL Server存储做准备
        /// </summary>
        /// <param name="vector">向量数组</param>
        /// <returns>向量字符串</returns>
        public string VectorToString(double[] vector)
        {
            if (vector == null || vector.Length == 0)
                return string.Empty;
                
            return string.Join(",", vector.Select(v => v.ToString("F6")));
        }

        /// <summary>
        /// 从字符串解析向量
        /// 本地处理，解析SQL Server返回的向量数据
        /// </summary>
        /// <param name="vectorString">向量字符串</param>
        /// <returns>向量数组</returns>
        public double[] StringToVector(string vectorString)
        {
            if (string.IsNullOrEmpty(vectorString))
                return new double[0];
                
            try
            {
                var vectorStrings = vectorString.Split(',');
                var vector = new double[vectorStrings.Length];
                
                for (int i = 0; i < vectorStrings.Length; i++)
                {
                    if (double.TryParse(vectorStrings[i], out double value))
                    {
                        vector[i] = value;
                    }
                }
                
                return vector;
            }
            catch (Exception ex)
            {
                Logger.Error($"解析向量失败: VectorString={vectorString}", ex);
                return new double[VectorServiceConfig.VECTOR_DIMENSION];
            }
        }

        #endregion

        #region SQL Server数据库管理

        /// <summary>
        /// 创建向量表
        /// SQL Server负责：表结构创建、索引配置
        /// </summary>
        /// <returns>操作结果</returns>
        public async Task<bool> CreateVectorTablesAsync()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    // 创建新闻向量表
                    var createNewsVectorTable = @"
                        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='NewsVector' AND xtype='U')
                        CREATE TABLE NewsVector (
                            Id INT IDENTITY(1,1) PRIMARY KEY,
                            NewsId INT NOT NULL,
                            VectorString NVARCHAR(MAX) NOT NULL,
                            VectorDimension INT NOT NULL DEFAULT(1536),
                            Tags NVARCHAR(500),
                            CreateTime DATETIME DEFAULT(GETDATE()),
                            UpdateTime DATETIME DEFAULT(GETDATE())
                        )";

                    // 创建用户向量表
                    var createUserVectorTable = @"
                        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserVector' AND xtype='U')
                        CREATE TABLE UserVector (
                            Id INT IDENTITY(1,1) PRIMARY KEY,
                            UserId INT NOT NULL,
                            VectorString NVARCHAR(MAX) NOT NULL,
                            VectorDimension INT NOT NULL DEFAULT(1536),
                            Tags NVARCHAR(500),
                            CreateTime DATETIME DEFAULT(GETDATE()),
                            UpdateTime DATETIME DEFAULT(GETDATE())
                        )";

                    using (var command = new SqlCommand(createNewsVectorTable, connection))
                    {
                        await command.ExecuteNonQueryAsync();
                    }

                    using (var command = new SqlCommand(createUserVectorTable, connection))
                    {
                        await command.ExecuteNonQueryAsync();
                    }

                    // 创建索引
                    var createIndexes = @"
                        IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_NewsVector_NewsId')
                            CREATE NONCLUSTERED INDEX IX_NewsVector_NewsId ON NewsVector(NewsId);

                        IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_UserVector_UserId')
                            CREATE NONCLUSTERED INDEX IX_UserVector_UserId ON UserVector(UserId);

                        IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_NewsVector_Tags')
                            CREATE NONCLUSTERED INDEX IX_NewsVector_Tags ON NewsVector(Tags);";

                    using (var command = new SqlCommand(createIndexes, connection))
                    {
                        await command.ExecuteNonQueryAsync();
                    }
                }

                Logger.Info("向量表创建成功");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error("创建向量表失败", ex);
                return false;
            }
        }

        ///// <summary>
        ///// 获取向量统计信息
        ///// SQL Server负责：统计信息查询
        ///// </summary>
        ///// <returns>统计信息</returns>
        //public async Task<VectorStats> GetVectorStatsAsync()
        //{
        //    try
        //    {
        //        using (var connection = new SqlConnection(_connectionString))
        //        {
        //            await connection.OpenAsync();
                    
        //            var sql = @"
        //                SELECT 
        //                    (SELECT COUNT(*) FROM NewsVector) AS NewsVectorCount,
        //                    (SELECT COUNT(*) FROM UserVector) AS UserVectorCount,
        //                    (SELECT MAX(CreateTime) FROM NewsVector) AS LastNewsVectorTime,
        //                    (SELECT MAX(CreateTime) FROM UserVector) AS LastUserVectorTime";
                    
        //            using (var command = new SqlCommand(sql, connection))
        //            using (var reader = await command.ExecuteReaderAsync())
        //            {
        //                if (await reader.ReadAsync())
        //                {
        //                    return new VectorStats
        //                    {
        //                        NewsVectorCount = reader.GetInt32("NewsVectorCount"),
        //                        UserVectorCount = reader.GetInt32("UserVectorCount"),
        //                        LastNewsVectorTime = reader.IsDBNull("LastNewsVectorTime") ? null : (DateTime?)reader.GetDateTime("LastNewsVectorTime"),
        //                        LastUserVectorTime = reader.IsDBNull("LastUserVectorTime") ? null : (DateTime?)reader.GetDateTime("LastUserVectorTime")
        //                    };
        //                }
        //            }
        //        }
                
        //        return new VectorStats();
        //    }
        //    catch (Exception ex)
        //    {
        //        Logger.Error("获取向量统计信息失败", ex);
        //        return new VectorStats();
        //    }
        //}

        #endregion

        #region 性能优化

        /// <summary>
        /// 批量插入向量到SQL Server
        /// SQL Server负责：批量存储优化
        /// VectorService负责：批量预处理
        /// </summary>
        /// <param name="vectors">向量数据列表</param>
        /// <returns>操作结果</returns>
        public async Task<bool> BatchInsertVectorsAsync(List<VectorData> vectors)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    foreach (var vectorData in vectors)
                    {
                        var normalizedVector = NormalizeVector(vectorData.Vector);
                        var vectorString = VectorToString(normalizedVector);

                        var sql = @"
                            IF EXISTS (SELECT 1 FROM NewsVector WHERE NewsId = @NewsId)
                                UPDATE NewsVector
                                SET VectorString = @VectorString, Tags = @Tags, UpdateTime = GETDATE()
                                WHERE NewsId = @NewsId
                            ELSE
                                INSERT INTO NewsVector (NewsId, VectorString, VectorDimension, Tags, CreateTime, UpdateTime)
                                VALUES (@NewsId, @VectorString, @VectorDimension, @Tags, GETDATE(), GETDATE())";

                        using (var command = new SqlCommand(sql, connection))
                        {
                            command.Parameters.AddWithValue("@NewsId", vectorData.Id);
                            command.Parameters.AddWithValue("@VectorString", vectorString);
                            command.Parameters.AddWithValue("@VectorDimension", VectorServiceConfig.VECTOR_DIMENSION);
                            command.Parameters.AddWithValue("@Tags", (object)vectorData.Tags ?? DBNull.Value);

                            await command.ExecuteNonQueryAsync();
                        }
                    }
                }

                Logger.Info($"批量插入向量成功: Count={vectors.Count}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error("批量插入向量失败", ex);
                return false;
            }
        }

        #endregion
        
        #region 文本向量化

        /// <summary>
        /// 获取文本的向量表示
        /// </summary>
        /// <param name="text">输入文本</param>
        /// <returns>文本向量</returns>
        public async Task<double[]> GetTextEmbeddingAsync(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                Logger.Warn("获取文本向量失败：输入文本为空");
                return null;
            }

            try
            {
                // 构建缓存键
                string cacheKey = $"text_embedding:{text.GetHashCode()}:{VectorServiceConfig.EMBEDDING_MODEL_NAME}";

                // 尝试从缓存获取
                var cachedVector = _cache.GetCache<double[]>(cacheKey);
                if (cachedVector != null)
                {
                    Logger.Info("从缓存获取文本向量成功");
                    return cachedVector;
                }

                Logger.Info($"开始获取文本向量，文本长度: {text.Length}，模型: {VectorServiceConfig.EMBEDDING_MODEL_NAME}");

                double[] vector;

                if (VectorServiceConfig.ENABLE_REAL_EMBEDDING)
                {
                    // 调用真实的Embedding服务API
                    vector = await CallRealEmbeddingServiceAsync(text);
                }
                else
                {
                    // 使用模拟实现（开发/测试环境）
                    vector = GenerateMockEmbedding(text);
                }

                // 缓存向量
                if (vector != null)
                {
                    _cache.WriteCache(vector, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.EMBEDDING_CACHE_DAYS));
                    Logger.Info("文本向量已缓存");
                }

                return vector;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取文本向量失败: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 批量获取文本的向量表示
        /// </summary>
        /// <param name="texts">输入文本列表</param>
        /// <returns>文本向量列表</returns>
        public async Task<List<double[]>> GetTextEmbeddingsBatchAsync(List<string> texts)
        {
            if (texts == null || texts.Count == 0)
            {
                Logger.Warn("批量获取文本向量失败：输入文本列表为空");
                return new List<double[]>();
            }

            try
            {
                Logger.Info($"开始批量获取文本向量，文本数量: {texts.Count}");

                var results = new List<double[]>();
                var cacheMisses = new List<int>();
                var cacheMissTexts = new List<string>();

                // 首先尝试从缓存获取所有向量
                for (int i = 0; i < texts.Count; i++)
                {
                    string text = texts[i];
                    string cacheKey = $"text_embedding:{text.GetHashCode()}:{VectorServiceConfig.EMBEDDING_MODEL_NAME}";

                    var cachedVector = _cache.GetCache<double[]>(cacheKey);
                    if (cachedVector != null)
                    {
                        results.Add(cachedVector);
                    }
                    else
                    {
                        // 占位，保持索引一致
                        results.Add(null);
                        cacheMisses.Add(i);
                        cacheMissTexts.Add(text);
                    }
                }

                Logger.Info($"从缓存获取文本向量，命中: {texts.Count - cacheMisses.Count}，未命中: {cacheMisses.Count}");

                // 对于缓存未命中的，批量获取
                if (cacheMisses.Count > 0)
                {
                    List<double[]> batchVectors;

                    if (VectorServiceConfig.ENABLE_REAL_EMBEDDING)
                    {
                        // 批量调用真实的Embedding服务API
                        batchVectors = await CallRealEmbeddingServiceBatchAsync(cacheMissTexts);
                    }
                    else
                    {
                        // 使用模拟实现（开发/测试环境）
                        batchVectors = cacheMissTexts.Select(GenerateMockEmbedding).ToList();
                    }

                    // 更新结果和缓存
                    for (int i = 0; i < cacheMissTexts.Count && i < batchVectors.Count; i++)
                    {
                        string text = cacheMissTexts[i];
                        int originalIndex = cacheMisses[i];
                        var vector = batchVectors[i];

                        // 更新结果
                        results[originalIndex] = vector;

                        // 缓存向量
                        if (vector != null)
                        {
                            string cacheKey = $"text_embedding:{text.GetHashCode()}:{VectorServiceConfig.EMBEDDING_MODEL_NAME}";
                            _cache.WriteCache(vector, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.EMBEDDING_CACHE_DAYS));
                        }
                    }
                }

                return results;
            }
            catch (Exception ex)
            {
                Logger.Error("批量获取文本向量失败", ex);
                return new List<double[]>();
            }
        }

        /// <summary>
        /// 生成模拟的文本嵌入向量（仅用于开发和测试）
        /// </summary>
        /// <param name="text">输入文本</param>
        /// <returns>模拟的向量</returns>
        private double[] GenerateMockEmbedding(string text)
        {
            // 注意：这是一个改进的模拟实现，基于文本内容生成更有意义的向量
            // 在生产环境中，应该替换为实际的嵌入服务调用

            if (string.IsNullOrEmpty(text))
            {
                return new double[VectorServiceConfig.VECTOR_DIMENSION];
            }

            // 使用文本的哈希码作为随机种子，确保相同文本生成相同的向量
            var random = new Random(text.GetHashCode());

            // 创建向量
            var vector = new double[VectorServiceConfig.VECTOR_DIMENSION];

            // 基于文本特征生成更有意义的向量
            var textLower = text.ToLower();
            var words = textLower.Split(new char[] { ' ', ',', '.', '!', '?', ';', ':', '\n', '\r', '\t' },
                                       StringSplitOptions.RemoveEmptyEntries);

            // 为常见的投资和技术关键词分配特定的向量模式
            var keywordPatterns = new Dictionary<string, double[]>
            {
                ["投资"] = GenerateKeywordPattern(0.8, 0.2, random),
                ["技术"] = GenerateKeywordPattern(0.7, 0.3, random),
                ["ai"] = GenerateKeywordPattern(0.9, 0.1, random),
                ["人工智能"] = GenerateKeywordPattern(0.9, 0.1, random),
                ["区块链"] = GenerateKeywordPattern(0.6, 0.4, random),
                ["大数据"] = GenerateKeywordPattern(0.7, 0.3, random),
                ["云计算"] = GenerateKeywordPattern(0.6, 0.4, random),
                ["金融"] = GenerateKeywordPattern(0.8, 0.2, random),
                ["医疗"] = GenerateKeywordPattern(0.5, 0.5, random),
                ["教育"] = GenerateKeywordPattern(0.4, 0.6, random),
                ["新能源"] = GenerateKeywordPattern(0.7, 0.3, random),
                ["汽车"] = GenerateKeywordPattern(0.6, 0.4, random)
            };

            // 基础随机向量
            for (int i = 0; i < VectorServiceConfig.VECTOR_DIMENSION; i++)
            {
                vector[i] = (random.NextDouble() * 0.4) - 0.2; // 较小的基础值 [-0.2, 0.2]
            }

            // 根据关键词调整向量
            foreach (var word in words)
            {
                foreach (var pattern in keywordPatterns)
                {
                    if (word.Contains(pattern.Key))
                    {
                        for (int i = 0; i < Math.Min(pattern.Value.Length, vector.Length); i++)
                        {
                            vector[i] += pattern.Value[i] * 0.3; // 关键词权重
                        }
                    }
                }
            }

            // 基于文本长度调整向量强度
            var lengthFactor = Math.Min(1.0, text.Length / 1000.0);
            for (int i = 0; i < vector.Length; i++)
            {
                vector[i] *= (0.5 + lengthFactor * 0.5);
            }
            
            // 归一化向量
            return NormalizeVector(vector);
        }

        /// <summary>
        /// 调用真实的Embedding服务API
        /// </summary>
        /// <param name="text">输入文本</param>
        /// <returns>向量</returns>
        private async Task<double[]> CallRealEmbeddingServiceAsync(string text)
        {
            try
            {
                Logger.Info($"调用真实Embedding服务，文本长度: {text.Length}");

                // 构建请求数据
                var requestData = new
                {
                    model = VectorServiceConfig.EMBEDDING_MODEL_NAME,
                    input = text,
                    encoding_format = "float"
                };

                // 使用Task.Run包装同步的HttpEmbeddingPost调用
                var response = await Task.Run(() =>
                    HttpMethods.HttpEmbeddingPost(requestData));

                if (string.IsNullOrEmpty(response))
                {
                    Logger.Error("Embedding服务返回空响应");
                    return null;
                }

                // 解析响应
                var embeddingResponse = Newtonsoft.Json.JsonConvert.DeserializeObject<EmbeddingResponse>(response);

                if (embeddingResponse?.Data != null && embeddingResponse.Data.Count > 0)
                {
                    var embedding = embeddingResponse.Data[0].Embedding;

                    // 验证向量维度
                    if (embedding.Length != VectorServiceConfig.VECTOR_DIMENSION)
                    {
                        Logger.Warn($"向量维度不匹配，期望: {VectorServiceConfig.VECTOR_DIMENSION}，实际: {embedding.Length}");

                        // 调整向量维度
                        embedding = AdjustVectorDimension(embedding, VectorServiceConfig.VECTOR_DIMENSION);
                    }

                    Logger.Info($"成功获取Embedding向量，维度: {embedding.Length}");
                    return embedding;
                }
                else
                {
                    Logger.Error("Embedding响应格式错误或为空");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"调用Embedding服务失败: {ex.Message}", ex);

                // 失败时回退到模拟实现
                Logger.Info("回退到模拟向量生成");
                return GenerateMockEmbedding(text);
            }
        }

        /// <summary>
        /// 批量调用真实的Embedding服务API
        /// </summary>
        /// <param name="texts">输入文本列表</param>
        /// <returns>向量列表</returns>
        private async Task<List<double[]>> CallRealEmbeddingServiceBatchAsync(List<string> texts)
        {
            try
            {
                Logger.Info($"批量调用真实Embedding服务，文本数量: {texts.Count}");

                var results = new List<double[]>();

                // 分批处理，避免单次请求过大
                for (int i = 0; i < texts.Count; i += VectorServiceConfig.EMBEDDING_BATCH_SIZE)
                {
                    var batch = texts.Skip(i).Take(VectorServiceConfig.EMBEDDING_BATCH_SIZE).ToList();

                    // 构建批量请求数据
                    var requestData = new
                    {
                        model = VectorServiceConfig.EMBEDDING_MODEL_NAME,
                        input = batch,
                        encoding_format = "float"
                    };

                    // 使用Task.Run包装同步的HttpEmbeddingPost调用
                    var response = await Task.Run(() =>
                        HttpMethods.HttpEmbeddingPost(requestData));

                    if (string.IsNullOrEmpty(response))
                    {
                        Logger.Error($"批量Embedding服务返回空响应，批次: {i / VectorServiceConfig.EMBEDDING_BATCH_SIZE + 1}");

                        // 为这个批次生成模拟向量
                        foreach (var text in batch)
                        {
                            results.Add(GenerateMockEmbedding(text));
                        }
                        continue;
                    }

                    // 解析响应
                    var embeddingResponse = Newtonsoft.Json.JsonConvert.DeserializeObject<EmbeddingResponse>(response);

                    if (embeddingResponse?.Data != null)
                    {
                        foreach (var data in embeddingResponse.Data.OrderBy(d => d.Index))
                        {
                            var embedding = data.Embedding;

                            // 验证和调整向量维度
                            if (embedding.Length != VectorServiceConfig.VECTOR_DIMENSION)
                            {
                                embedding = AdjustVectorDimension(embedding, VectorServiceConfig.VECTOR_DIMENSION);
                            }

                            results.Add(embedding);
                        }
                    }
                    else
                    {
                        Logger.Error($"批量Embedding响应格式错误，批次: {i / VectorServiceConfig.EMBEDDING_BATCH_SIZE + 1}");

                        // 为这个批次生成模拟向量
                        foreach (var text in batch)
                        {
                            results.Add(GenerateMockEmbedding(text));
                        }
                    }

                    // 批次间延迟，避免API限流
                    if (i + VectorServiceConfig.EMBEDDING_BATCH_SIZE < texts.Count)
                    {
                        await Task.Delay(100);
                    }
                }

                Logger.Info($"批量Embedding完成，成功获取: {results.Count}/{texts.Count}");
                return results;
            }
            catch (Exception ex)
            {
                Logger.Error($"批量调用Embedding服务失败: {ex.Message}", ex);

                // 失败时回退到模拟实现
                Logger.Info("回退到模拟向量生成");
                return texts.Select(GenerateMockEmbedding).ToList();
            }
        }

        /// <summary>
        /// 调整向量维度
        /// </summary>
        /// <param name="vector">原始向量</param>
        /// <param name="targetDimension">目标维度</param>
        /// <returns>调整后的向量</returns>
        private double[] AdjustVectorDimension(double[] vector, int targetDimension)
        {
            if (vector.Length == targetDimension)
            {
                return vector;
            }

            var adjustedVector = new double[targetDimension];

            if (vector.Length > targetDimension)
            {
                // 截断向量
                Array.Copy(vector, adjustedVector, targetDimension);
            }
            else
            {
                // 填充向量
                Array.Copy(vector, adjustedVector, vector.Length);
                // 剩余部分填充0或使用插值
                for (int i = vector.Length; i < targetDimension; i++)
                {
                    adjustedVector[i] = 0.0;
                }
            }

            return adjustedVector;
        }

        /// <summary>
        /// 为关键词生成特定的向量模式
        /// </summary>
        /// <param name="strength">强度</param>
        /// <param name="variance">方差</param>
        /// <param name="random">随机数生成器</param>
        /// <returns>关键词向量模式</returns>
        private double[] GenerateKeywordPattern(double strength, double variance, Random random)
        {
            var pattern = new double[Math.Min(100, VectorServiceConfig.VECTOR_DIMENSION)]; // 只生成前100维的模式

            for (int i = 0; i < pattern.Length; i++)
            {
                // 生成具有特定强度和方差的模式
                var baseValue = strength * Math.Sin(i * 0.1) * Math.Cos(i * 0.05);
                var noise = (random.NextDouble() * 2 - 1) * variance;
                pattern[i] = baseValue + noise;
            }

            return pattern;
        }

        #endregion
    }

    #region 数据模型

    /// <summary>
    /// 向量搜索结果
    /// </summary>
    public class VectorSearchResult
    {
        public string Id { get; set; }
        public double Score { get; set; }
        public double[] Vector { get; set; }
        public Dictionary<string, object> Metadata { get; set; }
    }

    /// <summary>
    /// Embedding API响应模型
    /// </summary>
    public class EmbeddingResponse
    {
        [Newtonsoft.Json.JsonProperty("object")]
        public string Object { get; set; }

        [Newtonsoft.Json.JsonProperty("data")]
        public List<EmbeddingData> Data { get; set; }

        [Newtonsoft.Json.JsonProperty("model")]
        public string Model { get; set; }

        [Newtonsoft.Json.JsonProperty("usage")]
        public EmbeddingUsage Usage { get; set; }
    }

    /// <summary>
    /// Embedding数据
    /// </summary>
    public class EmbeddingData
    {
        [Newtonsoft.Json.JsonProperty("object")]
        public string Object { get; set; }

        [Newtonsoft.Json.JsonProperty("embedding")]
        public double[] Embedding { get; set; }

        [Newtonsoft.Json.JsonProperty("index")]
        public int Index { get; set; }
    }

    /// <summary>
    /// Embedding使用统计
    /// </summary>
    public class EmbeddingUsage
    {
        [Newtonsoft.Json.JsonProperty("prompt_tokens")]
        public int PromptTokens { get; set; }

        [Newtonsoft.Json.JsonProperty("total_tokens")]
        public int TotalTokens { get; set; }
    }

    /// <summary>
    /// 向量统计信息
    /// </summary>
    public class VectorStats
    {
        public int NewsVectorCount { get; set; }
        public int UserVectorCount { get; set; }
        public DateTime? LastNewsVectorTime { get; set; }
        public DateTime? LastUserVectorTime { get; set; }
    }

    /// <summary>
    /// 向量数据
    /// </summary>
    public class VectorData
    {
        public int Id { get; set; }
        public double[] Vector { get; set; }
        public string Tags { get; set; }
    }

    #endregion
} 