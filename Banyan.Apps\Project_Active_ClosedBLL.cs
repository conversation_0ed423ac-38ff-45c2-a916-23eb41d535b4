﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;

namespace Banyan.Apps
{
    public class Project_Active_ClosedBLL : BaseDAL<Project_Active_Closed>
    {
        private readonly AjaxResult ajaxResult = null;

        public Project_Active_ClosedBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }
        public AjaxResult GetProjectHistoryByProject(NameValueCollection paramValues)
        {
            string name = WebHelper.GetValue("name", "", paramValues);
            string portfolioID = WebHelper.GetValue("portfolioID", "", paramValues);
            var res = GetList($"abbName='{name}' or portfolio = '{portfolioID}'  ", int.MaxValue, 1, "*", "modifiedDate asc, postmoney asc");
            ajaxResult.data = res.Select(ProjectModel => {
                var r = "";
                if (ProjectModel.postMoney != 0 && ProjectModel.investedAmount != 0)
                {
                    double tmpRatio = (double)ProjectModel.investedAmount * 100.0 / (double)ProjectModel.postMoney;
                    r = tmpRatio.ToString("f2") + "%";
                }

                return new
                {
                    ProjectModel.ProjectID,
                    ProjectModel.abbName,
                    ProjectModel.fullName,
                    ProjectModel.projectType,
                    ProjectModel.investFund,
                    ProjectModel.firstInvest,
                    ProjectModel.ownership,
                    ProjectModel.shares,
                    ProjectModel.otherInvestor,
                    remark = ProjectModel.remark,
                    discussType = ProjectModel.discussType,
                    ProjectModel.closed,
                    ProjectModel.BoardSeatType,
                    projectManager = string.IsNullOrEmpty(ProjectModel.projectManager2) ? ProjectModel.projectManager : ProjectModel.projectManager + "，" + ProjectModel.projectManager2,
                    ProjectModel.currency,
                    roundRatio = r,
                    investedAmount = ProjectModel.investedAmount == 0 ? "" : string.Format("{0:N0}", ProjectModel.investedAmount),
                    preMoney = ProjectModel.preMoney == 0 ? "" : string.Format("{0:N0}", ProjectModel.preMoney),
                    postMoney = ProjectModel.postMoney == 0 ? "" : string.Format("{0:N0}", ProjectModel.postMoney),
                    totalInvestAmount = ProjectModel.totalInvestAmount == 0 ? "" : string.Format("{0:N0}", ProjectModel.totalInvestAmount),
                    ProjectModel.InhouseCounsel,
                    modifiedDate = ProjectModel.modifiedDate.ToString("yyyy-MM-dd"),
                };
            }).ToList();
            ajaxResult.code = (int)ResultCode.success;

            return ajaxResult;
        }

        public AjaxResult GetProjectHistory(NameValueCollection paramValues)
        {
            string ProjectId = WebHelper.GetValue("aid", "", paramValues);
            var p = new Project_ActiveBLL().GetModelByCache(ProjectId);
            var res = GetList($"abbName='{p.abbName}' AND ProjectId <> '{ProjectId}' AND modifiedDate <= '{p.modifiedDate.ToString("yyyy-MM-dd")}' AND ( ({p.postMoney}<>0 AND postMoney <={p.postMoney}) OR {p.postMoney} = 0)  ", int.MaxValue, 1, "*", "modifiedDate asc, postmoney asc");
            //AND (investedAmount <> 0 or discussType <> 'Closed' ) 

            if (res.Count > 0)
            {
                res.Add(new Project_Active_Closed()
                {
                    ProjectID = p.ProjectID,
                    abbName = p.abbName,
                    fullName = p.fullName,
                    projectType = p.projectType,
                    investFund = p.investFund,
                    firstInvest = p.firstInvest,
                    ownership = p.ownership,
                    otherInvestor = p.otherInvestor,
                    BoardSeatType = p.BoardSeatType,
                    oneLineDesc = p.oneLineDesc,
                    currency = p.currency,
                    projectManager = p.projectManager,
                    projectManager2 = p.projectManager,
                    remark = p.remark,
                    InhouseCounsel = p.InhouseCounsel,
                    investedAmount = p.investedAmount,
                    shares = p.shares,
                    totalInvestAmount = p.totalInvestAmount,
                    preMoney = p.preMoney,
                    postMoney = p.postMoney,
                    closed = p.closed,
                    modifiedDate = p.modifiedDate,
                    createdDate = p.createdDate
                });
            }
            ajaxResult.data = res.Select(ProjectModel => {
            var r =   "";
            if (ProjectModel.postMoney != 0 && ProjectModel.investedAmount != 0)
            {
               double tmpRatio = (double)ProjectModel.investedAmount * 100.0 / (double)ProjectModel.postMoney;
               r = tmpRatio.ToString("f2") + "%";
            }
                   
            return new
                {
                    ProjectModel.ProjectID,
                    ProjectModel.abbName,
                    ProjectModel.fullName,
                    ProjectModel.projectType,
                    ProjectModel.investFund,
                    ProjectModel.firstInvest,
                    ProjectModel.ownership,
                    ProjectModel.shares,
                    ProjectModel.otherInvestor,
                    remark = ProjectModel.remark,
                    discussType = ProjectModel.discussType,
                    ProjectModel.closed,
                    ProjectModel.BoardSeatType,
                    projectManager = string.IsNullOrEmpty(ProjectModel.projectManager2) ? ProjectModel.projectManager : ProjectModel.projectManager + "，" + ProjectModel.projectManager2,
                    ProjectModel.currency,
                    roundRatio = r,
                    investedAmount = ProjectModel.investedAmount == 0 ? "" : string.Format("{0:N0}", ProjectModel.investedAmount),
                    preMoney = ProjectModel.preMoney == 0 ? "" : string.Format("{0:N0}", ProjectModel.preMoney),
                    postMoney = ProjectModel.postMoney == 0 ? "" : string.Format("{0:N0}", ProjectModel.postMoney),
                    totalInvestAmount = ProjectModel.totalInvestAmount == 0 ? "" : string.Format("{0:N0}", ProjectModel.totalInvestAmount),
                    ProjectModel.InhouseCounsel,
                    modifiedDate = ProjectModel.modifiedDate.ToString("yyyy-MM-dd"),
                };
    }).ToList();
            ajaxResult.code = (int)ResultCode.success;
           
            return ajaxResult;
        }
             

        /// <summary>
        /// 操作日志更新
        /// </summary>
        /// <param name="page"></param>
        /// <param name="action"></param>
        /// <param name="description"></param>
        /// <param name="userName"></param>
        public void updateLog(string page, string action, string description, Member user)
        {
            SysLog log = new SysLog
            {
                Page = page,
                Action = action,
                Description = description,
                CreatedBy = user == null ? "" : user.RealName,
                Ip = Utility.WebHelper.GetIP(),
                CreatorId = user == null ? 0 : user.Id,
            };
            SysLogBLL logBLL = new SysLogBLL();
            logBLL.Add(log);
        }
    }
}
