﻿using Banyan.Apps;
using Banyan.Code;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Banyan.Domain
{
    public class ProjectService : BaseService
    {
        public static readonly ProjectBLL projectBll = new ProjectBLL();

        public object ProjectSameNameCheck(string pname)
        {
            pname = pname.Replace("'", "''").Trim();
            if(string.IsNullOrEmpty(pname) )
            {
                return JOk(0);
            }
            Project result = projectBll.GetModel($"Name='{pname}' AND Status<>{(int)ProjectStatus.delete}");
            
            return JOk(result == null ? 0 : result.Id);
        }
        
        public object FounderSimilarCheck(string name)
        {
            Member user = new MemberBLL().GetLogOnUser();
            name = name.Replace("'", "''").Replace("创始人", "").Replace("联创","").Replace("联合创始人", "").Replace("，"," ").Trim();
           
            var resultList = new List<Project>();
            if (string.IsNullOrEmpty(name) || name.Length == 1)
            {
                return JOk(resultList);
            }
            var names = name.Split(' ');
            foreach (var pname in names)
            {
                // 如果没有找到模糊匹配的项目，尝试获取所有模糊匹配且数量不超过5的项目
                var list = projectBll.GetList($"founder LIKE '%{pname}%' AND Status<>{(int)ProjectStatus.delete} AND isPrivate = 0 ");
                if (list != null && list.Count <= 5)
                {
                    // 如果找到的模糊匹配项目数量不超过5，将它们添加到结果列表
                    resultList.AddRange(list);
                }
                else
                {
                    var partialMatchResult = projectBll.GetModel($"Name<> '' AND '{pname}' LIKE CONCAT('%', Name, '%') AND Status<>{(int)ProjectStatus.delete} AND isPrivate = 0 ");
                    if (partialMatchResult != null)
                    {
                        // 如果找到模糊匹配的项目，添加到结果列表
                        resultList.Add(partialMatchResult);
                    }
                }
            }
           
            if (resultList.Count == 0)
            {
                Logger.Info("匹配同名项目失败，创始人：" + name, user.RealName);
            }
            else
            {
                if (resultList.Count > 1)
                {
                    resultList = resultList.Distinct(new ProjectComparer()).ToList();
                }
                foreach (var project in resultList)
                {
                    Logger.syslog("匹配同名项目成功，创始人：" + name + " 返回创始人:" + project.Founder + " 项目名：" + project.Name, user.RealName, project.Name);
                }
            }
           
            return JOk(getProjectSimple(resultList, user));
            //Project result = projectBll.GetModelBySql($"select * from project where FREETEXT(Name, {pname}) AND Status<>{{(int)ProjectStatus.delete}}");
        }

        public object ProjectSimilarNameCheck(string pname)
        {
            Member user = new MemberBLL().GetLogOnUser();
            pname = pname.Replace("'", "''").Trim();
            var resultList = new List<Project>();
            if (string.IsNullOrEmpty(pname) || pname.Length == 1)
            {
                return JOk(resultList);
            }
            //// 1、完全匹配
            //Project exactMatchResult = projectBll.GetModel($"Name='{pname}' AND Status<>{(int)ProjectStatus.delete}");
            //if (exactMatchResult != null)
            //{
            //    // 如果找到精确匹配的项目，直接添加到结果列表并结束搜索
            //    resultList.Add(exactMatchResult);
            //} else
            //{
            var partialMatchResult = projectBll.GetModel($"Name<> '' AND '{pname}' LIKE CONCAT('%', Name, '%') AND Status<>{(int)ProjectStatus.delete} AND isPrivate = 0 ");
            if (partialMatchResult != null)
            {
                // 如果找到模糊匹配的项目，添加到结果列表
                resultList.Add(partialMatchResult);
            }


            // 如果没有找到模糊匹配的项目，尝试获取所有模糊匹配且数量不超过5的项目
            var list = projectBll.GetList($"Name LIKE '%{pname}%' AND Status<>{(int)ProjectStatus.delete} AND isPrivate = 0 ");
            if (list != null && list.Count <= 5)
            {
                // 如果找到的模糊匹配项目数量不超过5，将它们添加到结果列表
                resultList.AddRange(list);
            }

            var res = new List<Project>();
            if (resultList.Count == 0)
            {
                Logger.Info("匹配同名项目失败，关键词：" + pname, user.RealName);
            } 
            else
            {
                var idSet = new HashSet<int>();
                foreach (var project in resultList)
                {
                    if (!idSet.Contains(project.Id))
                    {
                        idSet.Add(project.Id);
                        res.Add(project);
                    Logger.syslog("匹配同名项目成功，关键词：" + pname + " 返回项目：" + project.Name, user.RealName, project.Name);
                    }
                }
            }
            return JOk(getProjectSimple(res, user));
            //Project result = projectBll.GetModelBySql($"select * from project where FREETEXT(Name, {pname}) AND Status<>{{(int)ProjectStatus.delete}}");
        }
        private List<object> getProjectSimple(List<Project> projectList, Member user, bool showId = true)
        {
            var res = new List<object>();
            foreach (var projectmodel in projectList)
            {
                res.Add(new
                {
                    projectmodel.Id,
                    projectmodel.Name,
                    projectmodel.ProjectManager
                });
            }
            return res;
        }
    }
}
