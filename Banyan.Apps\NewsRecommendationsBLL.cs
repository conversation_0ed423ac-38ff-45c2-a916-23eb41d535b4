using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Banyan.Code;
using DAL.Base;
using Banyan.Domain;

namespace Banyan.Apps
{
    /// <summary>
    /// Statistics about user engagement with recommendations
    /// </summary>
    public class EngagementStatistics
    {
        /// <summary>
        /// Start date for the statistics period
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// End date for the statistics period
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// Total number of engagements in the period
        /// </summary>
        public int TotalEngagements { get; set; }

        /// <summary>
        /// Number of unique users who engaged with content
        /// </summary>
        public int UniqueUsers { get; set; }

        /// <summary>
        /// Number of unique news items that were engaged with
        /// </summary>
        public int UniqueNewsItems { get; set; }

        /// <summary>
        /// Number of engagements from the web interface
        /// </summary>
        public int WebEngagements { get; set; }

        /// <summary>
        /// Number of engagements from email digests
        /// </summary>
        public int EmailEngagements { get; set; }

        /// <summary>
        /// List of top engaged news items
        /// </summary>
        public List<TopEngagedNewsItem> TopEngagedNews { get; set; } = new List<TopEngagedNewsItem>();

        /// <summary>
        /// List of top engaged users
        /// </summary>
        public List<TopEngagedUser> TopEngagedUsers { get; set; } = new List<TopEngagedUser>();
    }

    /// <summary>
    /// Represents a news item with high engagement
    /// </summary>
    public class TopEngagedNewsItem
    {
        /// <summary>
        /// News ID
        /// </summary>
        public int NewsId { get; set; }

        /// <summary>
        /// News title
        /// </summary>
        public string NewsTitle { get; set; }

        /// <summary>
        /// Number of engagements with this news item
        /// </summary>
        public int EngagementCount { get; set; }
    }

    /// <summary>
    /// Represents a user with high engagement
    /// </summary>
    public class TopEngagedUser
    {
        /// <summary>
        /// User name
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// Number of engagements by this user
        /// </summary>
        public int EngagementCount { get; set; }
    }
  
    /// <summary>
    /// Business Loggeric layer for recommendation data access
    /// </summary>
    public class NewsRecommendationsBLL : BaseDAL<NewsRecommendations>
    {

        /// <summary>
        /// Constructor
        /// </summary>
        public NewsRecommendationsBLL()
        {
        }

        #region NewsRecommendations Table Operations

        /// <summary>
        /// Adds a new recommendation to the database
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="newsId">News ID</param>
        /// <param name="relevanceScore">Relevance score</param>
        /// <returns>ID of the newly created recommendation</returns>
        public async Task<int> AddRecommendationAsync(int userId, int newsId, double relevanceScore)
        {
            try
            {
                Logger.Info($"Adding recommendation for user {userId}, news {newsId}, score {relevanceScore}");

                int id = (int)await Task.Run(() => Add(new NewsRecommendations
                {
                    NewsId = newsId,
                    UserId = userId,
                    RelevanceScore = relevanceScore,
                    GeneratedTime = DateTime.Now,
                    IsRead = false,
                    IsClicked = false
                } ));
                Logger.Info($"Successfully added recommendation with ID {id}");
                return id;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error adding recommendation for user {userId}, news {newsId}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Adds multiple recommendations in a batch operation
        /// </summary>
        /// <param name="recommendations">List of recommendation objects</param>
        /// <returns>Number of recommendations added</returns>
        public async Task<int> AddRecommendationBatchAsync(List<NewsRecommendations> recommendations)
        {
            if (recommendations == null || recommendations.Count == 0)
            {
                Logger.Warn("Empty recommendations list provided for batch insert");
                return 0;
            }

            try
            {
                Logger.Info($"Adding batch of {recommendations.Count} recommendations");

                int successCount = 0;
                using (SqlConnection connection = new SqlConnection(Banyan.Code.DBConnection.connectionString))
                {
                    await connection.OpenAsync();

                    // Create a DataTable for bulk insert
                    DataTable table = new DataTable();
                    table.Columns.Add("UserId", typeof(int));
                    table.Columns.Add("NewsId", typeof(int));
                    table.Columns.Add("RelevanceScore", typeof(double));
                    table.Columns.Add("GeneratedTime", typeof(DateTime));
                    table.Columns.Add("IsRead", typeof(bool));
                    table.Columns.Add("IsClicked", typeof(bool));

                    // Add rows to the DataTable
                    foreach (var recommendation in recommendations)
                    {
                        table.Rows.Add(
                            recommendation.UserId,
                            recommendation.NewsId,
                            recommendation.RelevanceScore,
                            recommendation.GeneratedTime,
                            recommendation.IsRead,
                            recommendation.IsClicked
                        );
                    }

                    // Perform bulk insert
                    using (SqlBulkCopy bulkCopy = new SqlBulkCopy(connection))
                    {
                        bulkCopy.DestinationTableName = "NewsRecommendations";
                        bulkCopy.BatchSize = 100;

                        bulkCopy.ColumnMappings.Add("UserId", "UserId");
                        bulkCopy.ColumnMappings.Add("NewsId", "NewsId");
                        bulkCopy.ColumnMappings.Add("RelevanceScore", "RelevanceScore");
                        bulkCopy.ColumnMappings.Add("GeneratedTime", "GeneratedTime");
                        bulkCopy.ColumnMappings.Add("IsRead", "IsRead");
                        bulkCopy.ColumnMappings.Add("IsClicked", "IsClicked");

                        await bulkCopy.WriteToServerAsync(table);
                        successCount = recommendations.Count;
                    }
                }

                Logger.Info($"Successfully added {successCount} recommendations in batch");
                return successCount;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error adding batch recommendations: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets recommendations for a specific user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="limit">Maximum number of recommendations to return</param>
        /// <param name="includeRead">Whether to include already read recommendations</param>
        /// <returns>List of recommendations</returns>
        public async Task<List<NewsRecommendations>> GetUserRecommendationsAsync(int userId, int limit = 10, bool includeRead = false)
        {
            try
            {
                Logger.Info($"Getting recommendations for user {userId}, limit {limit}, includeRead {includeRead}");

                string where = $"UserId = {userId}";
                if (!includeRead)
                {
                    where += " AND IsRead = 0";
                }

                string orderBy = "RelevanceScore DESC, GeneratedTime DESC";
                
                var recommendations = await Task.Run(() => GetList(where, limit, 1, "*", orderBy));

                Logger.Info($"Retrieved {recommendations.Count} recommendations for user {userId}");
                return recommendations;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting recommendations for user {userId}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Updates the read status of a recommendation
        /// </summary>
        /// <param name="id">Recommendation ID</param>
        /// <param name="isRead">New read status</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> UpdateReadStatusAsync(int id, bool isRead)
        {
            try
            {
                Logger.Info($"Updating read status for recommendation {id} to {isRead}");

                // Create parameter for update
                var newsRecommendation = new NewsRecommendations
                {
                    Id = id,
                    IsRead = isRead
                };

                // Use the BaseDAL Update method with a where clause
                string where = $"Id = {id}";
                int result = await Task.Run(() => Update(newsRecommendation, "IsRead") ? 1 : 0);

                bool success = result > 0;
                Logger.Info($"Update read status for recommendation {id} {(success ? "succeeded" : "failed")}");
                return success;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error updating read status for recommendation {id}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Updates the clicked status of a recommendation
        /// </summary>
        /// <param name="id">Recommendation ID</param>
        /// <param name="isClicked">New clicked status</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> UpdateClickedStatusAsync(int id, bool isClicked)
        {
            try
            {
                Logger.Info($"Updating clicked status for recommendation {id} to {isClicked}");

                // Create model for update
                var newsRecommendation = new NewsRecommendations
                {
                    Id = id,
                    IsClicked = isClicked
                };

                // Use the BaseDAL Update method with a where clause
                string where = $"Id = {id}";
                int result = await Task.Run(() => Update(newsRecommendation, "IsClicked") ? 1 : 0);

                bool success = result > 0;
                Logger.Info($"Update clicked status for recommendation {id} {(success ? "succeeded" : "failed")}");
                return success;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error updating clicked status for recommendation {id}: {ex.Message}", ex);
                throw;
            }
        }
        
        /// <summary>
        /// Gets engagement records for a specific user
        /// </summary>
        /// <param name="userName">User name</param>
        /// <param name="limit">Maximum number of records to return</param>
        /// <param name="startDate">Optional start date filter</param>
        /// <param name="endDate">Optional end date filter</param>
        /// <returns>List of engagement records</returns>
        public async Task<List<EngagementRecords>> GetUserEngagementHistoryAsync(string userName, int limit = 100, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                Logger.Info($"Getting engagement history for user {userName}, limit {limit}");

                // Delegate to the EngagementRecordsBLL class
                var engagementRecordsBLL = new EngagementRecordsBLL();
                return await engagementRecordsBLL.GetUserEngagementHistoryAsync(userName, limit, startDate, endDate);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting engagement history for user {userName}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Deletes old recommendations for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="daysToKeep">Number of days of recommendations to keep</param>
        /// <returns>Number of recommendations deleted</returns>
        public async Task<int> DeleteOldRecommendationsAsync(int userId, int daysToKeep = 30)
        {
            try
            {
                Logger.Info($"Deleting old recommendations for user {userId}, keeping {daysToKeep} days");

                DateTime cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                string where = $"UserId = {userId} AND GeneratedTime < '{cutoffDate:yyyy-MM-dd}'";

                int result = await Task.Run(() => DeleteByWhere(where));
                Logger.Info($"Deleted {result} old recommendations for user {userId}");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error deleting old recommendations for user {userId}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Converts a DataTable to a list of Recommendation objects
        /// </summary>
        /// <param name="dataTable">DataTable containing recommendation data</param>
        /// <returns>List of Recommendation objects</returns>
        private List<NewsRecommendations> ConvertToRecommendationList(DataTable dataTable)
        {
            var recommendations = new List<NewsRecommendations>();

            if (dataTable == null || dataTable.Rows.Count == 0)
            {
                return recommendations;
            }

            foreach (DataRow row in dataTable.Rows)
            {
                var recommendation = new NewsRecommendations
                {
                    Id = Convert.ToInt32(row["Id"]),
                    UserId = Convert.ToInt32(row["UserId"]),
                    NewsId = Convert.ToInt32(row["NewsId"]),
                    RelevanceScore = Convert.ToDouble(row["RelevanceScore"]),
                    GeneratedTime = Convert.ToDateTime(row["GeneratedTime"]),
                    IsRead = Convert.ToBoolean(row["IsRead"]),
                    IsClicked = Convert.ToBoolean(row["IsClicked"])
                };

                recommendations.Add(recommendation);
            }

            return recommendations;
        }

        #endregion

        #region EngagementRecords Table Operations


      

        /// <summary>
        /// Gets engagement statistics for a specific period
        /// </summary>
        /// <param name="startDate">Start date for statistics</param>
        /// <param name="endDate">End date for statistics</param>
        /// <returns>Engagement statistics</returns>
        public async Task<EngagementStatistics> GetEngagementStatisticsAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                Logger.Info($"Getting engagement statistics from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");

                var statistics = new EngagementStatistics
                {
                    StartDate = startDate,
                    EndDate = endDate
                };

                // Build the where clause for date range
                string whereClause = $"Timestamp BETWEEN '{startDate:yyyy-MM-dd}' AND '{endDate:yyyy-MM-dd 23:59:59}'";

                // Use direct SQL connection for complex queries
                using (SqlConnection connection = new SqlConnection(Banyan.Code.DBConnection.connectionString))
                {
                    await connection.OpenAsync();

                    // Total engagements, unique users, and unique news items
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT COUNT(*) AS TotalCount, 
                               COUNT(DISTINCT UserName) AS UniqueUsers,
                               COUNT(DISTINCT NewsId) AS UniqueNews
                        FROM EngagementRecords 
                        WHERE Timestamp BETWEEN @StartDate AND @EndDate", connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                statistics.TotalEngagements = reader.GetInt32(0);
                                statistics.UniqueUsers = reader.GetInt32(1);
                                statistics.UniqueNewsItems = reader.GetInt32(2);
                            }
                        }
                    }

                    // Engagements by source
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT Source, COUNT(*) AS Count
                        FROM EngagementRecords 
                        WHERE Timestamp BETWEEN @StartDate AND @EndDate
                        GROUP BY Source", connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                string source = reader.GetString(0);
                                int count = reader.GetInt32(1);

                                if (source.Equals("web", StringComparison.OrdinalIgnoreCase))
                                {
                                    statistics.WebEngagements = count;
                                }
                                else if (source.Equals("email", StringComparison.OrdinalIgnoreCase))
                                {
                                    statistics.EmailEngagements = count;
                                }
                            }
                        }
                    }

                    // Top engaged news
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT TOP 10 NewsId, NewsTitle, COUNT(*) AS EngagementCount
                        FROM EngagementRecords 
                        WHERE Timestamp BETWEEN @StartDate AND @EndDate
                        GROUP BY NewsId, NewsTitle
                        ORDER BY COUNT(*) DESC", connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                statistics.TopEngagedNews.Add(new TopEngagedNewsItem
                                {
                                    NewsId = reader.GetInt32(0),
                                    NewsTitle = reader.GetString(1),
                                    EngagementCount = reader.GetInt32(2)
                                });
                            }
                        }
                    }

                    // Top engaged users
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT TOP 10 UserName, COUNT(*) AS EngagementCount
                        FROM EngagementRecords 
                        WHERE Timestamp BETWEEN @StartDate AND @EndDate
                        GROUP BY UserName
                        ORDER BY COUNT(*) DESC", connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                statistics.TopEngagedUsers.Add(new TopEngagedUser
                                {
                                    UserName = reader.GetString(0),
                                    EngagementCount = reader.GetInt32(1)
                                });
                            }
                        }
                    }
                }

                Logger.Info($"Retrieved engagement statistics: {statistics.TotalEngagements} total engagements");
                return statistics;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting engagement statistics: {ex.Message}", ex);
                throw;
            }
        }

      

        #endregion

    }

    #region Helper Methods
    // Helper methods for data conversion and manipulation
    #endregion
}