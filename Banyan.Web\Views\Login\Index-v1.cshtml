﻿
@{
    Layout = null;
}

<!DOCTYPE html>
<!--[if IE 9]>
    <html class="ie9 no-focus" lang="en"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus" lang="en">
<!--<![endif]-->
<head>
    <meta charset="utf-8">
    <title>高榕资本-GAORONG VENTURES</title>
    <meta name="description" content="">
    <meta name="author" content="">
    <meta name="robots" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

    <link rel="shortcut icon" href="/content/img/favicons/favicon.ico">
    <link rel="icon" type="image/png" href="/content/img/favicons/favicon.ico" sizes="16x16">

    <link href="/content/css/bootstrap.min.css" rel="stylesheet" />
    <link href="/content/css/oneui.min.css" rel="stylesheet" />
    <link href="/content/css/login2.css" rel="stylesheet" />

    <style>
        .msg-box {
            display: block !important;
            height: 18px;
        }

        .n-right .msg-wrap {
            margin-left: 0px !important;
        }
    </style>
</head>
<body>
    <div class="bg-white pulldown">
        <div class="content content-boxed overflow-hidden block-opt-refresh">
            <div class="row">
                <div class="col-sm-8 col-sm-offset-2 col-md-6 col-md-offset-3 col-lg-4 col-lg-offset-4">
                    <div class="push-30-t push-50 animated fadeIn">
                        <div class="text-center">
                            <img class="brand-img" src="/content/img/logo.jpg" />
                            <p class="text-muted push-15-t"></p>
                        </div>
                        <form class="form-horizontal push-30-t" id="log-form" method="post">
                            @*<div class="form-group">
                                <div class="col-xs-12">
                                    <div class="form-material form-material-primary floating">
                                        <input class="form-control" type="text" id="userName" name="userName" title="用户名" data-target="#msg-wrapper">
                                        <label for="login-username">用户名</label>
                                    </div>
                                </div>
                            </div>*@
                            <div class="form-group">
                                <div class="col-xs-12">
                                    <div class="form-material form-material-primary floating">
                                        <input class="form-control" type="number" id="phone" name="phone" title="手机号码" data-target="#msg-wrapper">
                                        <label for="login-password">手机号码</label>  
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-xs-12 col-md-7">
                                    <div class="form-material form-material-primary floating">
                                        <input class="form-control verify-code" type="text" id="verifyCode" name="verifyCode" title="验证码" data-target="#msg-wrapper">
                                        <label for="login-password">验证码</label>
                                    </div>
                                </div>
                                <div class="col-xs-12 col-md-5">
                                    <div class="form-material form-material-primary floating">
                                        <img class="brand-img" id="refresh-code" src="@(Url.Action("getvalidcode", "login"))?t=@(DateTime.Now.Ticks)" title="看不清，点击换一张" />
                                    </div>
                                </div>
                            </div>
                            <div id="msg-wrapper"></div>
                            <div class="form-group push-30-t">
                                <div class="col-xs-12 col-sm-6 col-sm-offset-3 col-md-4 col-md-offset-4">
                                    <button class="btn btn-block btn-primary" type="submit">登 录</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="pulldown push-30-t text-center animated fadeInUp">
        <small class="text-muted"><span class="js-year-copy">2018</span> &copy; GAORONG VENTURES</small>
    </div>
    <script src="/content/js/oneui.min.js"></script>

    <link href="/content/js/plugins/nice-validator/jquery.validator.css" rel="stylesheet" />
    <script src="/content/js/plugins/nice-validator/jquery.validator.min.js"></script>
    <script src="/content/js/plugins/nice-validator/local/zh-CN.js"></script>

    <script src="/content/js/plugins/layui/layui.js"></script>

    <script type="text/javascript">
        var layer;
        layui.use(['layer'], function () {
            var layer = layui.layer;
        });

        $(function () {
        if (window != top) {
                top.location.href = location.href;
            }
            $('#log-form').validator({
                theme: 'yellow_top_effect',
                timely: 0,
                stopOnError: true,
                fields: {
                    userName: "required;",
                    passWd: "required;",
                    verifyCode: "required;",
                },
                display: function (input) {
                    return $(input).attr('title');
                },
                valid: function (form) {
                    var load = layer.load(2);
                    $.ajax({
                        type: 'POST',
                        url: '@(Url.Action("login", "login"))',
                        data: { un: $('#userName').val(), pw: $('#passWd').val(), vc: $('#verifyCode').val(), rm: $('#remember').val()},
                        success: function (data) {
                            layer.close(load);
                            if (data.code == 0) {
                                layer.msg('登录成功！');
                                setTimeout(function () {
                                    window.location.href = '@(Url.Action("projects", "project"))';
                                }, 1500);
                            } else {
                                @*if (data.code == 3) {
                                    $('#refresh-code').attr('src', "@Url.Action("getvalidcode", "login")" + "?t=" + (new Date()).getTime());
                                }*@
                                layer.msg(data.msg);
                            }
                        },
                        error: function () {
                            layer.msg("很抱歉，请求异常！");
                        }
                    });
                }
            });

            $("#refresh-code").click(function () {
                this.src="@Url.Action("getvalidcode", "adminapi")";
                return;
            });
        });
    </script>
</body>
</html>