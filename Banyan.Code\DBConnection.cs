﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.SqlClient;
using System.Configuration;


/// <summary>
/// Summary description for DBConnection
/// </summary>
/// 

namespace DBConnection
{

    public class DBConnection_Basic
    {
        //protected EnDecrypt _decrypter;
        protected string _servername;
        protected string _db;
        protected string _id;
        protected string _pwd;

        //protected SqlConnection _Connection;
        protected SqlConnection _Connection;
        public SqlConnection Connection
        {
            get { return _Connection; }
        }

        public DBConnection_Basic()
        {
            _Connection = new SqlConnection();
            ReadAttributes();
            ConstructSQLConnectionstring();
        }


        //protected void Finalize()
        //{
        //    //_decrypter = null;
        //    if (_Connection != null)
        //    {
        //        _Connection.Dispose();
        //    }

        //    //base.Finalize();
        //}

        //protected abstract void ReadAttributes();

        protected void ReadAttributes()
        {
            //add by  on 21-12-2010, add the encryption to the connection string
            //sysEncryption dataAs = new sysEncryption();
            //ConnectionStringSettingsCollection con = ConfigurationManager.ConnectionStrings;
            //_servername = dataAs.decryption(con["MSSQL_ServerName"].ToString());
            //_id = dataAs.decryption(con["MSSQL_UserName"].ToString());
            //_pwd = dataAs.decryption(con["MSSQL_Password"].ToString());
            //_db = dataAs.decryption(con["MSSQL_Database"].ToString());
        }

        protected void ConstructSQLConnectionstring()
        {
            //System.Data.SqlClient.SqlConnectionStringBuilder builder = new System.Data.SqlClient.SqlConnectionStringBuilder();
            //builder["Data Source"] = _servername;
            //builder["Initial Catalog"] = _db;
            //builder["Password"] = _pwd;
            //builder["User Id"] = _id;
            //builder["integrated Security"] = false;
            ////edit by  on 13-april-2012
            //builder["Connection Timeout"] = 5;    //0 is unlimit
            //builder["Pooling"] = "true";

            //_Connection.ConnectionString = builder.ToString();

            //_Connection.ConnectionString = @"Data Source=" + _servername + ";" +
            //                               "Initial Catalog=" + _db + ";" +
            //                               "User Id=" + _id + ";" +
            //                               "Password=" + _pwd + ";" +
            //                               "Persist Security Info=False";
            //"Pooling='true';" +
            //"MinPoolSize=0 ;" +
            //"MaxPoolSize = 20 ;" +
            //"Connection Timeout = 15 ;";

            //use only for trusted connection string.

            ConnectionStringSettingsCollection con = ConfigurationManager.ConnectionStrings;
            _Connection.ConnectionString = con["BusinessCenterPortal"].ToString();
        }

        public SqlConnection GetNewMYSqlConnection()
        {
            SqlConnection aNewMySQLConnection = new SqlConnection();
            aNewMySQLConnection.ConnectionString = _Connection.ConnectionString;
            return aNewMySQLConnection;
        }
    }
}