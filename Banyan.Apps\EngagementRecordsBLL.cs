using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Banyan.Code;
using DAL.Base;
using Banyan.Domain;

namespace Banyan.Apps
{
    public class EngagementRecordsBLL : DAL.Base.BaseDAL<EngagementRecords>
    {
        /// <summary>
        /// Converts a DataTable to a list of EngagementRecord objects
        /// </summary>
        /// <param name="dataTable">DataTable containing engagement data</param>
        /// <returns>List of EngagementRecord objects</returns>
        private List<EngagementRecords> ConvertToEngagementList(DataTable dataTable)
        {
            var engagements = new List<EngagementRecords>();

            if (dataTable == null || dataTable.Rows.Count == 0)
            {
                return engagements;
            }

            foreach (DataRow row in dataTable.Rows)
            {
                var engagement = new EngagementRecords
                {
                    Id = Convert.ToInt32(row["Id"]),
                    UserName = row["UserName"].ToString(),
                    NewsId = Convert.ToInt32(row["NewsId"]),
                    NewsTitle = row["NewsTitle"].ToString(),
                    Timestamp = Convert.ToDateTime(row["Timestamp"]),
                    Source = row["Source"].ToString()
                };

                engagements.Add(engagement);
            }

            return engagements;
        }
        /// <summary>
        /// Gets engagement records for a specific user
        /// </summary>
        /// <param name="userName">User name</param>
        /// <param name="limit">Maximum number of records to return</param>
        /// <param name="startDate">Optional start date filter</param>
        /// <param name="endDate">Optional end date filter</param>
        /// <returns>List of engagement records</returns>
        public async Task<List<EngagementRecords>> GetUserEngagementHistoryAsync(string userName, int limit = 100, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                Logger.Info($"Getting engagement history for user {userName}, limit {limit}");

                string where = $"UserName = '{userName}'";

                if (startDate.HasValue)
                {
                    where += $" AND Timestamp >= '{startDate.Value:yyyy-MM-dd}'";
                }

                if (endDate.HasValue)
                {
                    where += $" AND Timestamp <= '{endDate.Value:yyyy-MM-dd 23:59:59}'";
                }

                string orderBy = "Timestamp DESC";

                var engagements = await Task.Run(() => GetList(where, limit, 1, "*", orderBy));

                Logger.Info($"Retrieved {engagements.Count} engagement records for user {userName}");
                return engagements;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting engagement history for user {userName}: {ex.Message}", ex);
                throw;
            }
        }
        /// <summary>
        /// Records a user engagement event
        /// </summary>
        /// <param name="userName">User name</param>
        /// <param name="newsId">News ID</param>
        /// <param name="newsTitle">News title</param>
        /// <param name="source">Engagement source (web or email)</param>
        /// <returns>ID of the newly created engagement record</returns>
        public async Task<int> RecordEngagementAsync(string userName, int newsId, string newsTitle, string source)
        {
            try
            {
                Logger.Info($"Recording engagement for user {userName}, news {newsId}, source {source}");


                var parameters = new List<SqlParameter>
                {
                    new SqlParameter("@UserName", userName),
                    new SqlParameter("@NewsId", newsId),
                    new SqlParameter("@NewsTitle", newsTitle),
                    new SqlParameter("@Timestamp", DateTime.Now),
                    new SqlParameter("@Source", source)
                };

                int id = (int)await Task.Run(() => Add(new EngagementRecords
                {
                    UserName = userName,
                    NewsId = newsId,
                    NewsTitle = newsTitle,
                    Timestamp = DateTime.Now,
                    Source = source
                }));
                Logger.Info($"Successfully recorded engagement with ID {id}");
                return id;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error recording engagement for user {userName}, news {newsId}: {ex.Message}", ex);
                throw;
            }
        }
    }
}