﻿using Entity.Base;
using System;
using System.Collections.Generic;
using Utility;

namespace Banyan.Domain
{

    [Serializable]
    [SqlTable(dbEnum.BusinessCenterPortal)]
    public class investForIRR
    {
        [SqlField]
        public String abbName { get; set; } = String.Empty;
        [SqlField]
        public string portfolioID { get; set; }

        [SqlField]
        public DateTime closeDate { get; set; }
        [SqlField]
        public String currency { get; set; } = String.Empty;
        [SqlField]
        public decimal cost { get; set; }

        [SqlField]
        public String fundFamillyName { get; set; } = String.Empty;
        [SqlField]
        public String PortfolioIntroducer { get; set; } = String.Empty;
        [SqlField]
        public String PortfolioManager { get; set; } = String.Empty;
        [SqlField]
        public String GroupMember { get; set; } = String.Empty;

    }
}
