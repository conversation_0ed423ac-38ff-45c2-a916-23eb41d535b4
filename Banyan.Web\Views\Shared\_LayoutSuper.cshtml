﻿@{
    Layout = "/Views/Shared/_Layout.cshtml";
    var logUser = ViewData["manager"] as Banyan.Domain.Member;
}


@if (logUser.Levels != (byte)Banyan.Domain.MemberLevels.Administrator && logUser.Levels != (byte)Banyan.Domain.MemberLevels.SuperUser)
{
    <div style="text-align:center"> 无权访问 </div>
}
else
{
    @RenderBody()
}
<div style="width:1px;height:1px;overflow:hidden;position:relative">
    @RenderSection("modal", false)
</div>
@section scripts
{
    @RenderSection("scripts", false)
}