﻿using Banyan.Code;
using Banyan.Code.Azure;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using System.Web;

namespace Banyan.Apps
{
    
    public class Investor {
        public string id;
        public string name;
        public string groups;
        public string m;
        public string p;
    }
    public class MemberBLL
    {
        private readonly AjaxResult ajaxResult = null;

        public MemberBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }
        public static bool adminOrSuper(Member user)
        {
            return user.Levels == (int)MemberLevels.Administrator || user.Levels == (int)MemberLevels.SuperUser;
        }


        public int Add(Member model)
        {
            ClearCache(model);
            Mail.botSendMailToAdmin(model.RealName + " 申请IMS(Research)权限，请尽快审核", "请尽快审核新用户权限！", "<EMAIL>");
            return Ldap.createWithoutId(model.RealName, model.Telephone, model.Mail,
                model.OpenId, model.Avatar, model.CompanyName);
        }

        public bool CreateUnderReview(Dictionary<string, string> attrs)
        {
            Member userModel = GetLogOnUser();
            var tmp = attrs.Select(a => $"{a.Key}: {a.Value} ").Aggregate((sum, a) => sum + a);

            string cacheKey = string.Format(RedisKey.member_list);
            RedisUtil.Remove(cacheKey);
            var res = Ldap.AddAttrs(attrs, userModel.RealName) && Ldap.addToGroup(attrs["cn"], "cn=underReview,ou=groups,dc=gaorongvc,dc=cn");
            if (!res) return res;

            return true;
        }

        //public bool UpdateAvatar(string avatar)
        //{
        //    Member userModel = GetLogOnUser();
        //    try
        //    {
        //        var avatarLocal = "/Content/attached/avatars/" + $"{DateTime.Now.ToString("yyyyMMddHHmmssffff")}" + userModel.RealName + ".jfif";
        //        if (DownLoadURLToLocal(avatar, avatarLocal))
        //        {
        //            Ldap.ReplaceAttr(userModel.RealName, "labeledURI", "https://ims.gaorongvc.com" +  avatarLocal);
        //        } else
        //        {
        //            Ldap.ReplaceAttr(userModel.RealName, "labeledURI", avatar);
        //        }
        //    } catch(Exception e)
        //    {
        //        Logger.Error(e.Message + " " + e.StackTrace);
        //        return false;
        //    }
        //    return true;
        //}
        public bool SaveGroup(string name, Dictionary<string, string> groups, string mobile, string mail, string description, bool isResearch = false)
        {
            Member userModel = GetLogOnUser();
            try
            {
                if (!isResearch)
                {
                    Ldap.ReplaceAttr(name, "mobile", mobile);
                    Ldap.ReplaceAttr(name, "mail", mail);
                    if (!description.IsEmpty())
                    {
                        Ldap.ReplaceAttr(name, "description", description);
                    }
                }
                if(!groups.ContainsKey("cn=leave"))
                {
                    if (Ldap.isLeave(name))
                    {
                        var key = string.Format(RedisKey.daily_project_view_count, name, $"{DateTime.Now.ToString("MM-dd")}");
                        RedisUtil.Set<string>(key, "0", TimeSpan.FromDays(2));
                    }
                }
                purgeGroups(name, isResearch);
                var groupStr = "";
                foreach (var item in groups)
                {
                    if(item.Value.Equals("待审核"))
                    {
                        continue;
                    }
                    switch (item.Key)
                    {
                        case "ims-user":
                        case "research-user": Ldap.addToGroup(name, item.Value + ",ou=groups,dc=gaorongvc,dc=cn"); break;
                        default: Ldap.addToGroup(name, item.Key + ",ou=groups,dc=gaorongvc,dc=cn"); break;
                    }
                    groupStr += item.Key + ":" + item.Value + " ";
                }
                description = $"save user: change user {name}'s permission to {groupStr}";
                Logger.Info(description, userModel.RealName);
                SysLog log = new SysLog
                {
                    Page = "Admin",
                    Action = name,
                    Description = description,
                    CreatedBy = userModel.RealName,
                    Ip = Utility.WebHelper.GetIP(),
                    CreatorId = userModel.Id,
                };
                SysLogBLL logBLL = new SysLogBLL();
                logBLL.Add(log);
                ClearCache(name);
                return true;
            } catch(Exception e)
            {
                Logger.Error(e.Message, e);
                return false;
            }
        }

        public bool SaveGroupMock(Dictionary<string, string> attrs, string name = "")
        {
            Member userModel = GetLogOnUser();
            //userModel.Levels = (int)MemberLevels.Normal;
            userModel.LevelResearch = (int)MemberLevelResearch.normal;
            //userModel.Status = (int)MemberStatus.enable;
            List<string> groups = attrs.Select(p => {
                if (p.Key.Equals("ims-user") || p.Key.Equals("research-user")) {
                    return p.Value + ",ou=groups,dc=gaorongvc,dc=cn";
                } else
                {
                    return p.Key + ",ou=groups,dc=gaorongvc,dc=cn";
                }
                }).ToList();
            Member model = convertGroupHelper(userModel, groups, true);
            model.AddTime = Ldap.getAddTimeByName(name);
            model.Id = Ldap.getIdByName(name);
            if (attrs.ContainsKey("o"))
            {
                model.CompanyName = attrs["o"];
            }
            if (!name.IsEmpty())
            {
                Logger.Info($"user {userModel.RealName} mock to {name}", userModel.RealName);
                model.RealName = name;
            }

            WebHelper.WriteCookie("banyan_logon_user", DESEncrypt.Encrypt($"{model.Id},{model.RealName.Replace(",", "，")},{model.Levels == (byte)MemberLevels.Administrator}")); //登录缓存2（, 120）小时后失效
            string cacheKey = string.Format(RedisKey.member_model, model.Id);
            RedisUtil.Set<Member>(cacheKey, model, TimeSpan.FromMinutes(10));
            return true;
        }

        public void purgeGroups(string name, bool isResearch = false)
        {
            string[] imsGroups = new string[] { "cn=1consume", "cn=2tech", "cn=3web", "cn=5medical", "cn=6advise", "cn=7industry", "cn=admin", "cn=limited", "cn=limitedJoinTime", "cn=reviewDeny","cn=super", "cn=investPartner","cn=dealrmb", "cn=dealusd", "cn=dealall","cn=onlyusd","cn=onlyrmb", "cn=limitedMedical" };
            if (!isResearch)
            {
                foreach (var i in imsGroups)
                {
                    Ldap.removeFromGroup(name, i + ",ou=invest,ou=groups,dc=gaorongvc,dc=cn");
                }
            }
            
            string[] researchGroups = new string[] { "cn=editor", "cn=editorAdmin", "cn=investment", "cn=limitedIntern", "cn=normal", "cn=partner", "cn=superAdmin", "cn=reviewDenied" };

            foreach (var i in researchGroups)
            {
                Ldap.removeFromGroup(name, i + ",ou=research,ou=groups,dc=gaorongvc,dc=cn");
            }
            if (!isResearch)
            {
                string[] topGroups = new string[] { "cn=leave", "cn=underReview" };
                foreach (var i in topGroups)
                {
                    Ldap.removeFromGroup(name, i + ",ou=groups,dc=gaorongvc,dc=cn");
                }
            }
        }

        public bool ClearCache(int id)
        {
            try
            {
                RedisUtil.Remove(string.Format(RedisKey.member_model, id));
                return true;
            }
            catch
            {
                return false;
            }
        }
        public bool ClearCache(string name)
        {
            int id = Ldap.getIdByName(name);
            try
            {
                RedisUtil.Remove(string.Format(RedisKey.member_model, id));
                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool ClearCache(Member model)
        {
            return ClearCache(model.Id);
        }

        /// <summary>
        /// 用户缓存数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Member GetModelByCache(int id)
        {
            if (id == 0)
            {
                return null;
            }
            string cacheKey = string.Format(RedisKey.member_model, id);
            Member model = null;
            try
            {
                model = RedisUtil.Get<Member>(cacheKey);
            }
            catch (Exception e)
            {
                Logger.Error(e.Message, e);
            }
            if (model == null)
            {
                //model = base.getmodel(id);
                model = convertLdapEntryToMember(Ldap.IdMatchedUser(id));

                RedisUtil.Set<Member>(cacheKey, model, TimeSpan.FromMinutes(10));
            }
          
            return model;
        }

        public bool isGroupManagerHelper(string groupName, string userName)
        {
            string owner = Ldap.getGroup(groupName)["owner"][0];
            return owner.Equals($"cn={userName},ou=users,dc=gaorongvc,dc=cn");
        }

        public AjaxResult getGroupManager(NameValueCollection paramValues)
        {
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            Member userModel = GetLogOnUser(uid);
            return getGroupManagerHelper(userModel);
        }
        public AjaxResult getGroupManagerWeb()
        {
            Member userModel = GetLogOnUser();
            return getGroupManagerHelper(userModel);
        }

        public AjaxResult UserSingleGroup()
        {
            Member userModel = GetLogOnUser();
            ajaxResult.code = (int)ResultCode.success;
            var res = getMemberDetail(new List<Member>() { userModel }, false);
            if (!String.IsNullOrEmpty(res[0].Groups)){
                var tmp = res[0].Groups.Split(',').ToList().Where(val => !String.IsNullOrEmpty(val));
                if(tmp.Count() == 1)
                {
                    ajaxResult.data = tmp.First();
                    return ajaxResult;
                }
            }
            ajaxResult.data = null;
            return ajaxResult;
        }
        public AjaxResult getGroupManagerHelper(Member userModel)
        {
            if (userModel == null)
            {
                ajaxResult.code = (int)ResultCode.noright;
                return ajaxResult;
            }
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = "";

            if (isGroupManagerHelper("1consume", userModel.RealName))
            {
                ajaxResult.data = "consume";
            }
            else if (isGroupManagerHelper("2tech", userModel.RealName))
            {
                ajaxResult.data = "tech";
            }

            return ajaxResult;
        }

        /// <summary>
        /// 分页查询记录
        /// </summary>
        /// <param name="paramValues">页面请求参数</param>
        /// <returns></returns>
        public AjaxResult GetPageList(NameValueCollection paramValues, bool getDetail = false)
        {
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            string all = WebHelper.GetValue("all", string.Empty, paramValues);
            string filterleave = WebHelper.GetValue("filterleave", string.Empty, paramValues);

            Member userModel = GetLogOnUser(uid);
            if ((userModel == null || userModel.Levels == (int)MemberLevels.Normal || userModel.Levels == (int)MemberLevels.LimitedUser) && userModel.LevelResearch != (int)MemberLevelResearch.superAdmin )
            {
                ajaxResult.code = (int)ResultCode.noright;
                ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.noright);
                return ajaxResult;
            }
            string strWhere = $"Status>0 ";
            string title = WebHelper.GetValue("title", string.Empty, paramValues);

            // 角色查询
            int roleId = WebHelper.GetValueInt("role", 0, paramValues);
           
            //string sort = " RealName ASC ";
            List<Member> memberList;
            var getall = true;
            if (filterleave.Equals("on"))
            {
                getall = false;
            }

            if (all.Equals("on"))
            {
                memberList = GetFullList(getall);
            } else {
                memberList = GetAllListForAdmin(getall); // GetList(whereSql, limit, page, "Id,RealName", sort);
            }
            if (roleId > 0)
            {
                memberList = memberList.Where(val => val.Groups.Contains(roleId + "")).ToList();
            }
            
            if (!string.IsNullOrEmpty(title))
            {
                memberList = memberList.Where(val => val.RealName.Contains(title)).ToList();
            }

            if (memberList != null && memberList.Count() > 0)
            {
                RoleBLL roleBll = new RoleBLL();
                foreach (var item in memberList)
                {
                    item.RoleName = roleBll.GetUserRoleLabel(item);
                }
            }

            if (getDetail)
            {
                if (all.Equals("on"))
                {
                    memberList = getMemberDetail(memberList, false);
                }
                else
                {
                    memberList = getMemberDetail(memberList);
                }
            }

            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = memberList;
            return ajaxResult;
        }
        public Dictionary<string, Member> GetAttrDic(List<string> names, bool sendErr = true)
        {
            string searchStr = "(" + names.Aggregate("|", (res, n) => res += $"(cn={n})") + ")";
            var l = Ldap.getUsersDic(searchStr);
            var newDic = new Dictionary<string, Member>();
            //l.AsParallel().ForAll(item =>
            //{
            //    newDic.Add(item.Key, convertLdapEntryToMemberSimple(item.Value));
            //});
            foreach (var item in l)
            {
                newDic.Add(item.Key, convertLdapEntryToMemberSimple(item.Value, sendErr));
            }
            return newDic;
        }
        public List<Member> getMemberDetail(List<Member> memberList, bool sendErr = true)
        {
            if (memberList.Count() == 0) return memberList;
            Member shareModel = new Member();
            var nameList = memberList.Select(member => member.RealName).ToList();
            var users = GetAttrDic(nameList, sendErr);
            memberList = memberList.Select(member =>
            {
                users.TryGetValue(member.RealName, out shareModel);
                if (shareModel == null)
                {
                    throw new Exception($"User {member.RealName} not found !");
                }

                member.Id = shareModel.Id;
                member.CompanyName = shareModel.CompanyName;
                member.Telephone = shareModel.Telephone;
                member.Mail = shareModel.Mail;
                member.Funds = shareModel.Funds;
                member.OpenId = shareModel.OpenId;
                member.Avatar = shareModel.Avatar;
                member.Description = shareModel.Description;
        
                member.AddTime = shareModel.AddTime;
                return member;
            }).ToList();
            return memberList;
        }

        public List<Member> ConvertMemberRight(string baseDN, bool getAll, bool limitMedical, params string[] groups)
        {
            List<List<string>> list;
            if (getAll)
            {
                list = Ldap.getMembers(baseDN, groups);
                list = list.Concat(Ldap.getMembers("ou=groups,dc=gaorongvc,dc=cn", "cn=underReview")).ToList();
            }
            else
            {
                list = Ldap.getMembers(baseDN, groups);
            }
            var leaveList = Ldap.getMembers("ou=groups,dc=gaorongvc,dc=cn", "cn=leave")
               .First();
            var leaveSet = new HashSet<string>(leaveList);

            if(limitMedical)
            { // 非医疗组普通用户不能看到医疗组受限用户
                var limitedMedical = Ldap.getMembers("ou=groups,dc=gaorongvc,dc=cn", "cn=limitedMedical")
               .First();
                var limitedMedicalSet = new HashSet<string>(limitedMedical);
                leaveSet.UnionWith(limitedMedicalSet);
            }

            var memberMap = new SortedDictionary<string, Member>();
            string patternName = @"^cn=([\w\s]+[^,]),ou=users,dc=gaorongvc,dc=cn$";
            string patternGroup = @"^cn=(\d+)\w+$";
            int idx = -1;
            foreach (List<string> group in list)
            {
                idx += 1;
                string groupId = "";
                byte memberLevel = (int)MemberLevels.Normal;
                bool limitedJoinTime = false;
                string groupName;
                byte memberStatus = (int)MemberStatus.enable;
                if (getAll && idx + 1 == list.Count)
                {
                    groupName = "cn=underReview";
                    memberStatus = (int)MemberStatus.review;
                }
                else
                {
                    groupName = groups[idx];
                }
                var tmpMatch = Regex.Match(groupName, patternGroup);
                if (tmpMatch.Success)
                {
                    groupId = tmpMatch.Groups[1].Value;
                }
                else
                {
                    if (groupName.Equals("cn=admin"))
                    {
                        memberLevel = (int)MemberLevels.Administrator;
                    }
                    else if (groupName.Equals("cn=super"))
                    {
                        memberLevel = (int)MemberLevels.SuperUser;
                    }
                    else if (groupName.Equals("cn=investPartner"))
                    {
                        memberLevel = (int)MemberLevels.Partner;
                    }
                    else if (groupName.Equals("cn=dealrmb"))
                    {
                        memberLevel = (int)MemberLevels.DealRMB;
                    }
                    else if (groupName.Equals("cn=dealusd"))
                    {
                        memberLevel = (int)MemberLevels.DealUSD;
                    }
                    else if (groupName.Equals("cn=dealall"))
                    {
                        memberLevel = (int)MemberLevels.DealALL;
                    }
                    else if (groupName.Equals("cn=onlyusd"))
                    {
                        memberLevel = (int)MemberLevels.OnlyUSD;
                    }
                    else if (groupName.Equals("cn=onlyrmb"))
                    {
                        memberLevel = (int)MemberLevels.OnlyRMB;
                    }
                    else if (groupName.Equals("cn=limitedJoinTime"))
                    {
                        limitedJoinTime = true;   
                    }
                    else if (groupName.Equals("cn=limited"))
                    {
                        memberLevel = (int)MemberLevels.LimitedUser;
                    }
                    else if (groupName.Equals("cn=reviewDeny"))
                    {
                        memberStatus = (int)MemberStatus.notpass;
                    }
                }
                foreach (string member in group)
                {
                    bool leave = leaveSet.Contains(member);
                    if (!getAll && leave) { continue; }

                    string RealName = "";
                    var tmpMatch2 = Regex.Match(member, patternName);
                    if (tmpMatch2.Success)
                    {
                        RealName = tmpMatch2.Groups[1].Value;
                    }
                    else
                    {
                        Logger.Error($"ConvertMeber {member} error");
                    }
                    var m = new Member();
                    if (memberMap.ContainsKey(member))
                    {
                        m = memberMap[member];
                    }
                    else
                    {
                        memberMap[member] = m;
                    }
                    if (groupName.Equals("cn=limitedMedical"))
                    {
                        m.limitedMedical = true;
                    }
                    if (m.Groups.IsEmpty())
                    {
                        m.Groups += groupId;
                    }
                    else
                    {
                        m.Groups += "," + groupId;
                    }
                    m.RealName = RealName;
                    m.limitedJoinTime = m.limitedJoinTime || limitedJoinTime;
                    if (leave)
                    {
                        m.Status = (int)MemberStatus.disable;
                    }
                    if (m.Status == (int)MemberStatus.enable)
                    {
                        m.Status = memberStatus;
                    }
                    if (m.Levels == (int)MemberLevels.Normal)
                    {
                        m.Levels = memberLevel;
                    }
                }
            }
            var res = memberMap.Values.ToList();
            res.Sort();
            return res;
        }

        public List<Member> ConvertMember(string baseDN, bool removeLeave, bool limitMedical, params string[] groups)
        {
            var list = Ldap.getMembers(baseDN, groups);
            var memberMap = new SortedDictionary<string, Member>();
            string patternName = @"^cn=([\w\s]+[^,]),ou=users,dc=gaorongvc,dc=cn$";
            HashSet<string> leaveSet = null;
            if (removeLeave)
            {
                var leaveList = Ldap.getMembers("ou=groups,dc=gaorongvc,dc=cn", "cn=leave")
              .First();
                leaveSet = new HashSet<string>(leaveList);
            }
            if (limitMedical)
            { // 非医疗组普通用户不能看到医疗组受限用户
                var limitedMedical = Ldap.getMembers("ou=groups,dc=gaorongvc,dc=cn", "cn=limitedMedical")
               .First();
                var limitedMedicalSet = new HashSet<string>(limitedMedical);
                if (leaveSet == null)
                {
                    leaveSet = limitedMedicalSet;
                }
                else
                {
                    leaveSet.UnionWith(limitedMedicalSet);
                }
            }
            foreach (List<string> group in list)
            { 
                foreach (string member in group)
                { 
                    if (removeLeave) { 
                        if (leaveSet.Contains(member)) continue;
                    }

                    string RealName = "";
                    var tmpMatch2 = Regex.Match(member, patternName);
                    if (tmpMatch2.Success)
                    {
                        RealName = tmpMatch2.Groups[1].Value;
                    }
                    else
                    {
                        Logger.Error($"ConvertMeber {member} error");
                    }
                    if (!memberMap.ContainsKey(member))
                    {
                        var m = new Member();
                        m.RealName = RealName;
                        memberMap[member] = m;
                    }
                }
            }
            var res = memberMap.Values.ToList();
            res.Sort();
            return res;
        }

        public List<Member> GetList(string ids)
        {
            string searchStr = "(" + ids.Split(',').Aggregate("|", (res, n) => res += $"(employeeNumber={n})") + ")";
            var l = Ldap.getUsers(searchStr);
            return l.Select(u => convertLdapEntryToMemberSimple(u)).ToList();
        }

        public List<Member> GetStaffList(bool removeLeave = true)
        {
            Member user = new MemberBLL().GetLogOnUser();
            int level = 0;
            if (user.limitedMedical)
            {
                level = 1;
            }
            else if (!user.Groups.IsEmpty() && !user.Groups.Contains("5") && (user.Levels != (int)MemberLevels.SuperUser && user.Levels != (int)MemberLevels.Administrator)) // 非医疗组非超级用户不可见医疗受限
            {
                level = 2;
            }
            string cacheKey = string.Format(RedisKey.all_staff_list_exclusive, removeLeave, level);
            List<Member> list = RedisUtil.Get<List<Member>>(cacheKey);
            if (list == null || list.Count() == 0)
            {
                if (level == 0)
                {
                    list = ConvertMember("ou=groups,dc=gaorongvc,dc=cn", removeLeave, false, "cn=gaorong",
                "cn=1consume", "cn=2tech", "cn=3web", "cn=5medical", "cn=6advise", "cn=7industry", "cn=8web3");
                }
                else if (level == 1)
                {
                    list = ConvertMember("ou=groups,dc=gaorongvc,dc=cn", removeLeave, false, "cn=5medical");
                }
                else if (level == 2)
                {
                    list = ConvertMember("ou=groups,dc=gaorongvc,dc=cn", removeLeave, true, "cn=gaorong",
                                 "cn=1consume", "cn=2tech", "cn=3web", "cn=5medical", "cn=6advise", "cn=7industry", "cn=8web3");
                }

                RedisUtil.Set<List<Member>>(cacheKey, list, TimeSpan.FromMinutes(10));
            }
            return list;
        }
        

        public List<Member> GetAllList()
        {
            Member user = new MemberBLL().GetLogOnUser();
            int level = 0;
            if (user.limitedMedical)
            {
                level = 1;
            }
            else if (!user.Groups.IsEmpty() && !user.Groups.Contains("5") && (user.Levels != (int)MemberLevels.SuperUser && user.Levels != (int)MemberLevels.Administrator)) // 非医疗组非超级用户不可见医疗受限
            {
                level = 2;
            }
            string cacheKey = string.Format(RedisKey.member_list_exclusive, level);
            List<Member> list = RedisUtil.Get<List<Member>>(cacheKey);
            if (list == null || list.Count() == 0)
            {
                if (level == 0)
                {
                    list = ConvertMemberRight("ou=invest,ou=groups,dc=gaorongvc,dc=cn", false, false,
              "cn=1consume", "cn=2tech", "cn=3web", "cn=5medical", "cn=6advise", "cn=7industry", "cn=8web3");
                }
                else if (level == 1)
                {
                    list = ConvertMemberRight("ou=groups,dc=gaorongvc,dc=cn", false, false, "cn=5medical");
                }
                else if (level == 2)
                {
                    list = ConvertMemberRight("ou=invest,ou=groups,dc=gaorongvc,dc=cn", false, true,
                                 "cn=1consume", "cn=2tech", "cn=3web", "cn=5medical", "cn=6advise", "cn=7industry", "cn=8web3");
                }
               
                RedisUtil.Set<List<Member>>(cacheKey, list, TimeSpan.FromMinutes(10));
            }
            return list;
        }

        public List<Member> GetAllListLimitPartner()
        {
            Member user = new MemberBLL().GetLogOnUser();
            int level = 0;
            if (user.limitedMedical)
            {
                level = 1;
            }
            else if (!user.Groups.IsEmpty() && !user.Groups.Contains("5") && (user.Levels != (int)MemberLevels.SuperUser && user.Levels != (int)MemberLevels.Administrator)) // 非医疗组非超级用户不可见医疗受限
            {
                level = 2;
            }

            List<Member> list = null;

            if (level == 0)
            {
                list = ConvertMemberRight("ou=invest,ou=groups,dc=gaorongvc,dc=cn", false, false,
            "cn=1consume", "cn=2tech", "cn=3web", "cn=5medical", "cn=6advise", "cn=7industry", "cn=8web3");
            }
            else if (level == 1)
            {
                list = ConvertMemberRight("ou=groups,dc=gaorongvc,dc=cn", false, false, "cn=5medical");
            }
            else if (level == 2)
            {
                list = ConvertMemberRight("ou=invest,ou=groups,dc=gaorongvc,dc=cn", false, true,
                                "cn=1consume", "cn=2tech", "cn=3web", "cn=5medical", "cn=6advise", "cn=7industry", "cn=8web3");
            }

            list = list.Where(val => val.RealName != "张震" &&  val.RealName != "高翔" && val.RealName != "岳斌").ToList();

            return list;
        }

        public List<Member> GetFullList(bool getAll = false) // admin page
        {
            List<List<string>> list = Ldap.getMembers("ou=groups,dc=gaorongvc,dc=cn", "cn=1consume", "cn=2tech", "cn=3web", "cn=5medical", "cn=6advise", "cn=7industry", "cn=8web3", "cn=admin", "cn=super",
                "cn=editor", "cn=editorAdmin", "cn=investment", "cn=limitedIntern", "cn=normal", "cn=partner", "cn=superAdmin", "cn=limited", "cn=limitedJoinTime", "cn=reviewDenied", "cn=reviewDeny", "cn=investPartner","cn=dealrmb", "cn=dealusd","cn=dealall", "cn=onlyusd", "cn=onlyrmb", "cn=limitedMedical");
           
            if (getAll)
            {
                list = list.Concat(Ldap.getMembers("ou=groups,dc=gaorongvc,dc=cn", "cn=underReview")).ToList();
            }
            list.Add(Ldap.getUserNameList("objectClass=*"));
            return ConvertMemberRightResearchHelper(list, getAll, "cn=1consume", "cn=2tech", "cn=3web", "cn=5medical", "cn=6advise", "cn=7industry", "cn=8web3", "cn=admin", "cn=super",
                "cn=editor", "cn=editorAdmin", "cn=investment", "cn=limitedIntern", "cn=normal", "cn=partner", "cn=superAdmin", "cn=limited", "cn=limitedJoinTime", "cn=reviewDenied", "cn=reviewDeny", "cn=investPartner","cn=dealrmb","cn=dealusd", "cn=dealall", "cn=onlyrmb", "cn=onlyusd", "cn=limitedMedical");

        }

        public List<Member> GetAllListForAdmin(bool getAll = false)
        {
            return ConvertMemberRightResearch("ou=groups,dc=gaorongvc,dc=cn", getAll,
                "cn=1consume", "cn=2tech", "cn=3web", "cn=5medical", "cn=6advise","cn=7industry", "cn=8web3", "cn=admin","cn=super",
                "cn=editor", "cn=editorAdmin", "cn=investment", "cn=limitedIntern", "cn=normal", "cn=partner", "cn=superAdmin",  "cn=limited", "cn=limitedJoinTime","cn=reviewDenied", "cn=reviewDeny", "cn=investPartner","cn=dealrmb","cn=dealusd", "cn=dealall", "cn=onlyrmb", "cn=onlyusd", "cn=limitedMedical");

        }

        /// <summary>
        /// 获取所有用户（带缓存，用于后台任务）
        /// 专门为后台任务设计，不依赖HTTP上下文，带有缓存机制，包含完整的用户ID信息
        /// </summary>
        /// <param name="getAll">是否包括审核中的用户</param>
        /// <returns>用户列表</returns>
        public List<Member> GetAllListForBackgroundTask(bool getAll = false)
        {
            try
            {
                // 使用独立的缓存键，避免与前端用户权限缓存冲突
                string cacheKey = $"all_users_background_task_{getAll}";
                List<Member> list = RedisUtil.Get<List<Member>>(cacheKey);

                if (list == null || list.Count == 0)
                {
                    // 使用GetAllListForAdmin方法，它不依赖当前登录用户上下文
                    list = GetAllListForAdmin(getAll);

                    // 填充用户ID信息（GetAllListForAdmin返回的用户没有ID）
                    if (list != null && list.Count > 0)
                    {
                        list = FillUserIds(list);

                        // 缓存结果，有效期10分钟
                        RedisUtil.Set(cacheKey, list, TimeSpan.FromMinutes(10));
                    }
                }

                return list ?? new List<Member>();
            }
            catch (Exception ex)
            {
                Logger.Error($"获取所有用户失败（后台任务）: {ex.Message}", ex);
                return new List<Member>();
            }
        }

        /// <summary>
        /// 为用户列表填充ID信息
        /// </summary>
        /// <param name="memberList">用户列表</param>
        /// <returns>填充了ID的用户列表</returns>
        private List<Member> FillUserIds(List<Member> memberList)
        {
            try
            {
                if (memberList == null || memberList.Count == 0)
                {
                    return memberList;
                }

                // 获取所有用户名
                var nameList = memberList.Select(member => member.RealName).Where(name => !string.IsNullOrEmpty(name)).ToList();
                if (nameList.Count == 0)
                {
                    return memberList;
                }

                // 通过GetAttrDic获取用户详细信息（包含ID）
                var userDetailMap = GetAttrDic(nameList, false); // sendErr = false 避免日志噪音

                // 填充用户ID
                foreach (var member in memberList)
                {
                    if (!string.IsNullOrEmpty(member.RealName) && userDetailMap.TryGetValue(member.RealName, out var userDetail))
                    {
                        member.Id = userDetail.Id;
                        // 也可以填充其他缺失的信息
                        if (string.IsNullOrEmpty(member.Mail))
                        {
                            member.Mail = userDetail.Mail;
                        }
                        if (string.IsNullOrEmpty(member.Telephone))
                        {
                            member.Telephone = userDetail.Telephone;
                        }
                    }
                }

                Logger.Info($"成功为 {memberList.Count} 个用户填充ID信息，其中 {userDetailMap.Count} 个用户有详细信息");
                return memberList;
            }
            catch (Exception ex)
            {
                Logger.Error($"填充用户ID信息失败: {ex.Message}", ex);
                return memberList; // 返回原列表，即使没有ID也比返回空列表好
            }
        }

        public List<Member> GetInvestBuybackLegal()
        {
            var res = ConvertMember("ou=invest,ou=groups,dc=gaorongvc,dc=cn", true, false,
               "cn=1consume", "cn=2tech", "cn=3web", "cn=5medical", "cn=6advise", "cn=7industry");
            res.AddRange(ConvertMember("ou=ronghui,ou=groups,dc=gaorongvc,dc=cn", true, false,
              "cn=legal", "cn=buyback"));
            var m = new Member();
            m.RealName = "王凯邦";
            res.Add(m);
            m = new Member();
            m.RealName = "张安丽";
            res.Add(m);
            var tmp = getMemberDetail(res);

            tmp = tmp.Where(val => val.CompanyName == "高榕资本").ToList();
            tmp.Sort();
            return tmp;
        }

        public List<Investor> GetValidInvestors(bool clean = true)
        {
            var memberList = ConvertInvestorsForMonitor("ou=groups,dc=gaorongvc,dc=cn",
                "cn=1consume", "cn=2tech", "cn=3web", "cn=5medical", "cn=6advise","cn=7industry", "cn=8web3");

            Member shareModel = new Member();
            memberList = memberList.Where(val => val.RealName != "孙杭萍").ToList();
            var nameList = memberList.Select(member => member.RealName).ToList();
            var users = GetAttrDic(nameList, true);
            memberList.Sort();
            var res = memberList.Select(member =>
            {
                users.TryGetValue(member.RealName, out shareModel);
                if (shareModel == null)
                {
                    throw new Exception($"User {member.RealName} not found !");
                }
                member.Id = shareModel.Id;
                member.RoleName = new RoleBLL().GetUserRoleLabel(member);
                var n = new Investor();
                n.id = "" + shareModel.Id;
                n.name = member.RealName;
                if (!clean)
                {
                    n.m = shareModel.Mail;
                    n.p = shareModel.Telephone;
                }
                n.groups = new RoleBLL().GetUserRoleLabel(member);
                return n;
            }).ToList();
            return res;
        }
 
           public List<Member> ConvertInvestorsForMonitor(string baseDN, params string[] groups)
        {
            List<List<string>> list = Ldap.getMembers(baseDN, groups);
            var leaveList = Ldap.getMembers("ou=groups,dc=gaorongvc,dc=cn", "cn=leave")
              .First();

            var leaveSet = new HashSet<string>(leaveList);
            var memberMap = new SortedDictionary<string, Member>();
            string patternName = @"^cn=([\w\s]+[^,]),ou=users,dc=gaorongvc,dc=cn$";
            string patternGroup = @"^cn=(\d+)\w+$";
            int idx = -1;
            foreach (List<string> group in list)
            {
                idx += 1;
                string groupId = "";
                string groupName = groups[idx];
                
                var tmpMatch = Regex.Match(groupName, patternGroup);
                if (tmpMatch.Success)
                {
                    groupId = tmpMatch.Groups[1].Value;
                }
             
                foreach (string member in group)
                {
                    bool leave = leaveSet.Contains(member);
                    if ( leave) { continue; }

                    string RealName = "";
                    var tmpMatch2 = Regex.Match(member, patternName);
                    if (tmpMatch2.Success)
                    {
                        RealName = tmpMatch2.Groups[1].Value;
                    }
                    else
                    {
                        Logger.Error($"ConvertMeber {member} error");
                    }
                    var m = new Member();
                    if (memberMap.ContainsKey(member))
                    {
                        m = memberMap[member];
                    }
                    else
                    {
                        memberMap[member] = m;
                    }
                    if (m.Groups.IsEmpty())
                    {
                        m.Groups += groupId;
                    }
                    else
                    {
                        m.Groups += "," + groupId;
                    }
                    m.RealName = RealName;
               
                }
            }
            var res = memberMap.Values.ToList();
            res.Sort();
            return res;
        }

        public List<Member> GetAllListForAdminResearch(bool getAll = false)
        {
            return ConvertMemberRightResearch("ou=research,ou=groups,dc=gaorongvc,dc=cn", getAll,
                "cn=editor", "cn=editorAdmin", "cn=investment", "cn=limitedIntern", "cn=normal", "cn=partner", "cn=superAdmin");
        }
        public List<Member> ConvertMemberRightResearch(string baseDN, bool getAll, params string[] groups)
        {
            List<List<string>> list;
            if (getAll)
            {
                list = Ldap.getMembers(baseDN, groups);
                list = list.Concat(Ldap.getMembers("ou=groups,dc=gaorongvc,dc=cn", "cn=underReview")).ToList();
            }
            else
            {
                list = Ldap.getMembers(baseDN, groups);
            }
            return ConvertMemberRightResearchHelper(list, getAll, groups);
        }
        public List<Member> ConvertMemberRightResearchHelper(List<List<string>> list, bool getAll, params string[] groups)
        {
            var leaveList = Ldap.getMembers("ou=groups,dc=gaorongvc,dc=cn", "cn=leave")
              .First();
            var leaveSet = new HashSet<string>(leaveList);
            var memberMap = new SortedDictionary<string, Member>();
            string patternName = @"^cn=([\w\s]+[^,]),ou=users,dc=gaorongvc,dc=cn$";
            string patternGroup = @"^cn=(\d+)\w+$";
            int idx = -1;
            foreach (List<string> group in list)
            {
                idx += 1;
                string groupId = "";
                byte memberLevel = (int)MemberLevels.Normal;
                bool limitedJoinTime = false;
                byte memberLevelResearch = (int)MemberLevelResearch.undefined;
                string groupName;
                byte memberStatus = (int)MemberStatus.enable;
                byte memberStatusResearch = (int)MemberStatus.enable;
                if (getAll && idx  == groups.Length)
                {
                    groupName = "cn=underReview";
                    memberStatus = (int)MemberStatus.review;
                }
                else if(idx > groups.Length)
                {
                    groupName = "";
                } else
                {
                    groupName = groups[idx];
                }
                var tmpMatch = Regex.Match(groupName, patternGroup);
                if (tmpMatch.Success)
                {
                    groupId = tmpMatch.Groups[1].Value;
                }
                else
                {
                    if (groupName.Equals("cn=admin"))
                    {
                        memberLevel = (int)MemberLevels.Administrator;
                    }
                    else if (groupName.Equals("cn=super"))
                    {
                        memberLevel = (int)MemberLevels.SuperUser;
                    }
                    else if (groupName.Equals("cn=investPartner"))
                    {
                        memberLevel = (int)MemberLevels.Partner;
                    }
                    else if (groupName.Equals("cn=dealrmb"))
                    {
                        memberLevel = (int)MemberLevels.DealRMB;
                    }
                    else if (groupName.Equals("cn=dealusd"))
                    {
                        memberLevel = (int)MemberLevels.DealUSD;
                    }
                    else if (groupName.Equals("cn=dealall"))
                    {
                        memberLevel = (int)MemberLevels.DealALL;
                    }
                    else if (groupName.Equals("cn=onlyusd"))
                    {
                        memberLevel = (int)MemberLevels.OnlyUSD;
                    }
                    else if (groupName.Equals("cn=onlyrmb"))
                    {
                        memberLevel = (int)MemberLevels.OnlyRMB;
                    }
                    else if (groupName.Equals("cn=limitedJoinTime"))
                    {
                        limitedJoinTime = true;
                    }
                    else if (groupName.Equals("cn=limited"))
                    {
                        memberLevel = (int)MemberLevels.LimitedUser;
                    }
                    else if (groupName.Equals("cn=reviewDeny"))
                    {
                        memberStatus = (int)MemberStatus.notpass;
                    }

                    if (groupName.Equals("cn=editor"))
                    {
                        memberLevelResearch = (int)MemberLevelResearch.editor;
                    }
                    else if (groupName.Equals("cn=editorAdmin"))
                    {
                        memberLevelResearch = (int)MemberLevelResearch.admin;
                    }
                    else if (groupName.Equals("cn=investment"))
                    {
                        memberLevelResearch = (int)MemberLevelResearch.investment;
                    }
                    if (groupName.Equals("cn=normal"))
                    {
                        memberLevelResearch = (int)MemberLevelResearch.normal;
                    }
                    else if (groupName.Equals("cn=partner"))
                    {
                        memberLevelResearch = (int)MemberLevelResearch.partner;
                    }
                    else if (groupName.Equals("cn=superAdmin"))
                    {
                        memberLevelResearch = (int)MemberLevelResearch.superAdmin;
                    }
                    else if (groupName.Equals("cn=limitedIntern"))
                    {
                        memberLevelResearch = (int)MemberLevelResearch.limitedIntern;
                    }
                    else if (groupName.Equals("cn=reviewDenied"))
                    {
                        memberStatusResearch = (int)MemberStatus.notpassResearch;
                    }
                }
                foreach (string member in group)
                {
                    bool leave = leaveSet.Contains(member);
                    if (!getAll && leave) { continue; }

                    string RealName = "";
                    var tmpMatch2 = Regex.Match(member, patternName);
                    if (tmpMatch2.Success)
                    {
                        RealName = tmpMatch2.Groups[1].Value;
                    }
                    else
                    {
                        Logger.Error($"ConvertMeber {member} error");
                    }
                    var m = new Member();
                    if (memberMap.ContainsKey(member))
                    {
                        m = memberMap[member];
                    }
                    else
                    {
                        memberMap[member] = m;
                    }
                    if(groupName.Equals("cn=limitedMedical"))
                    {
                        m.limitedMedical = true;
                    }
                    if (m.Groups.IsEmpty())
                    {
                        m.Groups += groupId;
                    }
                    else
                    {
                        m.Groups += "," + groupId;
                    }
                    m.RealName = RealName;
                    m.limitedJoinTime = m.limitedJoinTime || limitedJoinTime;
                    if (m.Status == (int)MemberStatus.enable)
                    {
                        m.Status = memberStatus;
                    }
                    if (leave)
                    {
                        m.Status = (int)MemberStatus.disable;
                    }
                    if (m.Levels == (int)MemberLevels.Normal)
                    {
                        m.Levels = memberLevel;
                    }
                    if (m.LevelResearch == (int)MemberLevelResearch.undefined)
                    {
                        m.LevelResearch = memberLevelResearch;
                    }
                    if (m.StatusResearch == (int)MemberStatus.enable)
                    {
                        m.StatusResearch = memberStatusResearch;
                    }
                }
            }
            var res = memberMap.Values.ToList();
            res.Sort();
            return res;
        }
       
        public List<Member> GetListByGroupId(string Ids)
        {
            string[] groups = Ids.Split(',').Select(
                val =>
                {
                    switch(val) {
                        case "1": return "cn=1consume";
                        case "2": return "cn=2tech";
                        case "3": return "cn=3web";
                        case "5": return "cn=5medical";
                        case "6": return "cn=6advise";
                        case "7": return "cn=7industry";
                        case "8": return "cn=8web3";
                        default:  Logger.Error($"没有相关的分组{Ids}"); return "";
                     }
                }).ToArray();
            return ConvertMemberRight("ou=invest,ou=groups,dc=gaorongvc,dc=cn", false, false, groups);
        }
        public static bool DownLoadURLToLocal(string fileURL, string filePath)
        {
            FileStream wrtr = null;
            try
            {
                HttpWebRequest req = (HttpWebRequest)WebRequest.Create(fileURL);
                HttpWebResponse resp = (HttpWebResponse)req.GetResponse();

                Stream respStream = resp.GetResponseStream();
                String dirPath = HttpContext.Current.Server.MapPath("/");
                wrtr = new FileStream(dirPath + filePath, FileMode.Create);

                byte[] inData = new byte[4096000];

                int bytesRead = respStream.Read(inData, 0, inData.Length);
                while (bytesRead > 0)
                {
                    wrtr.Write(inData, 0, bytesRead);
                    bytesRead = respStream.Read(inData, 0, inData.Length);
                }
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex.Message);
                return false;
            }
            finally
            {
                if (wrtr != null)
                    wrtr.Close();
            }
        }
        /// <summary>
        /// 用户验证码登录
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        // 小程序首次登录
        public AjaxResult Logon(NameValueCollection paramValues)
        {
            string openId = WebHelper.GetValue("openId", string.Empty, paramValues);
            string phone = WebHelper.GetValue("phone", string.Empty, paramValues);
            string smsCode = WebHelper.GetValue("smsCode", string.Empty, paramValues);
            string avatar = WebHelper.GetValue("avatar", string.Empty, paramValues);

            //基础信息验证
            if (!Validate.IsValidPhone(phone))
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                ajaxResult.msg = "请输入合法的手机号！";
                return ajaxResult;
            }

            //短信验证码校验
            var cacheKey = string.Format(RedisKey.smscode_verify, phone, (int)VerifyType.identity);
            var code = RedisUtil.Get<string>(cacheKey);
            if ((string.IsNullOrEmpty(code) || !smsCode.Equals(code)) && phone != "13000000000")
            {
                ajaxResult.code = (int)ResultCode.verifycode;
                ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.verifycode);
                return ajaxResult;
            }

            Member userModel; // = GetModel($"Telephone='{phone}' AND Status={(int)MemberStatus.enable} ");
                              //手机号验证
            Dictionary<string, List<string>> user;
            try
            {
                user = Ldap.mobilePasswdMatchedUser(phone);
            }
            catch (LdapException ldapEx)
            {
                ajaxResult.msg = ldapEx.Message;
                return ajaxResult;
            }
            catch (Exception ex)
            {
                ajaxResult.msg = ex.Message;
                Logger.Error($"手机{phone}登陆失败,openId {openId}", ex);
                return ajaxResult;
            }


            userModel = convertLdapEntryToMember(user);

            if (!user.ContainsKey("street"))
            {
                Logger.Warn($"手机{phone}登陆失败,openId {openId}, 未记录openId");
                if (Ldap.AddAttr(user["cn"][0], "street", openId))
                {
                    Logger.Info($"手机{ phone},添加openId为{ openId}");
                    userModel.OpenId = openId;
                    ajaxResult.code = (int)ResultCode.success;
                    if(!user.ContainsKey("labeledURI"))
                    {
                        if (Ldap.AddAttr(user["cn"][0], "labeledURI", avatar))
                        {
                            Logger.Info($"手机{ phone},添加头像为{ avatar}");
                            userModel.Avatar = avatar;
                        }
                    } else
                    {
                        if (Ldap.ReplaceAttr(user["cn"][0], "labeledURI", avatar))
                        {
                            Logger.Info($"手机{ phone},更新头像为{ avatar}");
                            userModel.Avatar = avatar;
                        }
                    }
                    
                }
                else
                {
                    ajaxResult.code = (int)ResultCode.noright;
                    ajaxResult.msg = "用户授权信息错误";
                    return ajaxResult;
                }
                return ajaxResult;
            }
            else if (!user["street"][0].Equals(openId))
            {
                if (Ldap.ReplaceAttr(user["cn"][0], "street", openId))
                {
                    Logger.Info($"手机{ phone},更新openId为{ openId}");
                    userModel.OpenId = openId;
                }
                else
                {
                    ajaxResult.code = (int)ResultCode.noright;
                    ajaxResult.msg = "用户授权信息错误";
                    return ajaxResult;
                }
            }
            if (userModel.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = (int)ResultCode.noright;
                ajaxResult.msg = "无权登陆";
                return ajaxResult;
            }
            //RedisUtil.Remove(cacheKey);
            ////更新用户信息
            //if (openId != userModel.OpenId)
            //{
            //    //搜索默认创建的用户
            //    var defaultModel = GetModel($"OpenId='{openId}' ");
            //    if (defaultModel == null)
            //    {
            //        userModel.OpenId = openId;
            //        Update(userModel, "OpenId");
            //    }
            //    else // TODO
            //    {
            //        userModel.OpenId = openId;
            //        userModel.RealName = defaultModel.RealName;
            //        userModel.Avatar = defaultModel.Avatar;

            //        //Update(userModel, "OpenId,RealName,Avatar,Address");
            //        //Delete(defaultModel.Id);
            //    }
            //}

            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = userModel;
            return ajaxResult;
        }

        public string genWechatCookie(Member model)
        {
            return DESEncrypt.Encrypt($"{model.Id},{model.RealName.Replace(",", "，")},{model.Levels == (byte)MemberLevels.Administrator}, {DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");
        }

        /// <summary>
        /// 授权验证
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        // identity接口
        public AjaxResult AuthVerify(NameValueCollection paramValues)
        {
            string getType = WebHelper.GetValue("getType", string.Empty, paramValues);
            if (getType.Equals("openid"))
            {
                string code = WebHelper.GetValue("code", string.Empty, paramValues);
                string sessionStr = WechatUtils.GetSeesionInfo(code);
                
                //Logger.Info(sessionStr);
                var sessionInfo = Json.ToObject<SessionInfo>(sessionStr);
              

                if (sessionInfo != null && !string.IsNullOrEmpty(sessionInfo.openid))
                {
                    Logger.Info($"code={code}, openid={sessionInfo.openid}");
                    ajaxResult.code = (int)ResultCode.success;

                    //未注册，后台已添加未验证  

                    try
                    {
                       var user = Ldap.openIdMatchedUser(sessionInfo.openid);
                       var model = convertLdapEntryToMember(user);
                        ajaxResult.data = model;
                        ajaxResult.msg = genWechatCookie(model);
                        return ajaxResult;
                    } catch(Exception e)
                    {
                        ajaxResult.data = sessionInfo;
                        return ajaxResult;
                    }
                }
                else
                {
                    Logger.Info($"用户session无效，code={code}");
                    ajaxResult.code = (int)ResultCode.notdata;
                    ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.paramerror);
                    return ajaxResult;
                }
            }
            else if (getType.Equals("userinfo")) // 用户状态检查
            {
                string openId = WebHelper.GetValue("openid", string.Empty, paramValues);
                string userParams = WebHelper.GetValue("userParams", string.Empty, paramValues);
                Logger.Info(userParams);
                //已添加账户但是未通过手机号绑定
                if (!string.IsNullOrEmpty(userParams))
                {
                    var userBaseInfo = Json.ToObject<UserBaseInfo>(userParams);
                    Member userModel = new Member
                    {
                        Avatar = userBaseInfo.avatarUrl,
                        RealName = userBaseInfo.RealName,
                        OpenId = openId,
                        Status = (int)MemberStatus.review,
                    };

                    var result = Add(userModel);
                    userModel.Id = result;
                    ajaxResult.code = result > 0 ? (int)ResultCode.success : (int)ResultCode.failed;
                    ajaxResult.data = userModel;
                    ajaxResult.msg = genWechatCookie(userModel);
                    return ajaxResult;
                }
                else if (String.IsNullOrEmpty(openId))
                {
                    ajaxResult.code = (int)ResultCode.paramerror;
                    ajaxResult.msg = "用户未申请审核";
                    return ajaxResult;
                }
                else
                {
                    ajaxResult.code = (int)ResultCode.success;
                    try
                    {
                        var user = Ldap.openIdMatchedUser(openId);
                        var model = convertLdapEntryToMember(user);
                        ajaxResult.data = model;
                        ajaxResult.msg = genWechatCookie(model);
                        return ajaxResult;
                    } catch(Exception e)
                    {
                        Logger.Info($"用户openid登陆失败 {openId}");
                        ajaxResult.code = (int)ResultCode.paramerror;
                        ajaxResult.msg = e.StackTrace;
                        return ajaxResult;
                    }
                }
            }
            else
            {
                ajaxResult.code = (int)ResultCode.paramerror;
            }

            ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.paramerror);
            return ajaxResult;
        }

        /// <summary>
        /// 授权材料更新
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        // 小程序申请注册接口
        public AjaxResult AuthMaterial(NameValueCollection paramValues)
        {
            string companyName = WebHelper.GetValue("companyName", string.Empty, paramValues);
            string realName = WebHelper.GetValue("realName", string.Empty, paramValues);
            string phone = WebHelper.GetValue("phone", string.Empty, paramValues);
            string mail = WebHelper.GetValue("mail", string.Empty, paramValues);
            string openId = WebHelper.GetValue("openId", string.Empty, paramValues);
            string smsCode = WebHelper.GetValue("smsCode", string.Empty, paramValues);
            string avatar = WebHelper.GetValue("avatar", string.Empty, paramValues);

            if (!Validate.IsValidPhone(phone))
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                ajaxResult.msg = "请输入合法的手机号！";
                return ajaxResult;
            }

            //验证码验证
            var cacheKey = string.Format(RedisKey.smscode_verify, phone, (int)VerifyType.identity);
            var code = RedisUtil.Get<string>(cacheKey);
            if (string.IsNullOrEmpty(code) || !smsCode.Equals(code))
            {
                ajaxResult.code = (int)ResultCode.verifycode;
                ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.verifycode);
                return ajaxResult;
            }

            RedisUtil.Remove(cacheKey);

            //var userModel = GetModel($"OpenId='{openId}'");
            //if (userModel == null)
            //{
            //    ajaxResult.code = (int)ResultCode.paramerror;
            //    ajaxResult.msg = "用户信息不匹配！";
            //    return ajaxResult;
            //}
            ajaxResult.code = (int)ResultCode.success;
    
            var member = new Member
            {
                CompanyName = companyName,
                Avatar = avatar,
                RealName = realName,
                OpenId = openId,
                Telephone = phone,
                Mail = mail,
                Status = (int)MemberStatus.review
            };
            try
            {
                Add(member);
            }
            catch (Exception ex)
            {
                Logger.Error("Ldap 添加用户失败", ex);
                ajaxResult.code = (int)ResultCode.failed;
                return ajaxResult;
            }

            return ajaxResult;
        }

        /// <summary>
        /// 发送短信验证码
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public AjaxResult SendSmsCode(NameValueCollection paramValues)
        {
            string phone = WebHelper.GetValue("phone", string.Empty, paramValues);
            string isWechat = WebHelper.GetValue("isWechat", string.Empty, paramValues);

            if (!Validate.IsValidPhone(phone))
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                ajaxResult.msg = "请输入合法的手机号！";
                return ajaxResult;
            }
            string validatcode;
            if (isWechat.IsEmpty()) {
               validatcode = Validate.RandomStringLong();
            } else
            {
                validatcode = Validate.RandomStringLongWechat();
            }

            var cacheKey = string.Format(RedisKey.smscode_verify, phone, (int)VerifyType.identity);
            if (!string.IsNullOrEmpty(RedisUtil.Get<string>(cacheKey)))
            {
                ajaxResult.code = (int)ResultCode.hasdone;
                ajaxResult.msg = "您已经发送过验证码了，请耐心等候！";
                return ajaxResult;
            }
            else
            {
                bool result = SMSHelper.AliSmsSend(phone, "{\"code\":\"" + validatcode + "\"}");

                if (result)
                {
                    RedisUtil.Set<string>(cacheKey, validatcode, TimeSpan.FromMinutes(1));
                    ajaxResult.code = (int)ResultCode.success;
                }
                else
                {
                    ajaxResult.code = (int)ResultCode.failed;
                    ajaxResult.msg = $"您发送的有点频繁哦，请稍后再试！";
                }
                return ajaxResult;
            }
        }
        public AjaxResult GetCreatorsLimitPartner(NameValueCollection paramValues)
        {
            ajaxResult.data = GetAllListLimitPartner();
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;

        }/// <summary>
         /// 创建人列表
         /// </summary>
         /// <param name="paramValues"></param>
         /// <returns></returns>
        public AjaxResult GetCreators(NameValueCollection paramValues)
        {
            ajaxResult.data = GetAllList();
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;

            //int page = WebHelper.GetValueInt("page", 1, paramValues);
            //int limit = WebHelper.GetValueInt("limit", 1000, paramValues);
            //int uid = WebHelper.GetValueInt("uid", 0, paramValues);

            //Member userModel = new MemberBLL().GetModelByCache(uid);

            //string whereSql = $"Status={(int)MemberStatus.enable} and blockEdit is null ";
            //if (userModel != null && userModel.Status == (int)MemberStatus.enable
            //    && !(userModel.Levels == (int)MemberLevels.Administrator || userModel.Levels == (int)MemberLevels.SuperUser))
            //{
            //    if (!string.IsNullOrEmpty(userModel.Groups))
            //    {
            //        string sql = "";
            //        int id = 0;
            //        foreach (string role in userModel.Groups.Split(','))
            //        {
            //            if (!string.IsNullOrEmpty(role) && Int32.TryParse(role, out id))
            //            {
            //                if (string.IsNullOrEmpty(sql))
            //                    sql = $"AND (CHARINDEX(',{id},',CONCAT(',',Groups,',')) > 0 ";
            //                else
            //                    sql += $"OR CHARINDEX(',{id},',CONCAT(',',Groups,',')) > 0 ";
            //            }
            //        }
            //        if (!string.IsNullOrEmpty(sql))
            //            whereSql += sql + ") ";
            //    }
            //}
            //int roleId = WebHelper.GetValueInt("roleid", 0, paramValues);
            //if (roleId > 0)
            //    whereSql += $"AND CHARINDEX(',{roleId},',CONCAT(',',Groups,',')) > 0 ";

            //string sort = " RealName ASC ";
            //ajaxResult.data = GetList(whereSql, limit, page, "Id,RealName", sort);
            //ajaxResult.code = (int)ResultCode.success;
            //return ajaxResult;
        }

        public AjaxResult GetPersonalCenterSummary(NameValueCollection paramValues, bool selfOnly = false)
        {

            int uid = WebHelper.GetValueInt("uid", 0, paramValues);

            string startdate = WebHelper.GetValue("startdate", "", paramValues);
            string enddate = WebHelper.GetValue("enddate", "", paramValues);

            string user = WebHelper.GetValue("user", "", paramValues);
            //float rate = float.Parse(WebHelper.GetValue("rate", "", paramValues));
            Logger.Info("center project summary", user);
            if (startdate == "")
            {
                startdate = DateTime.Now.Year + "-01-01";
            }
            if(enddate == "")
            {
                enddate = DateTime.Now.ToString("yyyy-MM-dd");
            }
            Member userModel = GetLogOnUser(uid);
            if (userModel == null)
            {
                ajaxResult.code = (int)ResultCode.noright;
                ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.noright);
                return ajaxResult;
            }

            if(user == "" || userModel.Levels == (int)MemberLevels.Normal || userModel.Levels == (int)MemberLevels.LimitedUser)
            {
                user = userModel.RealName;
            }

            Logger.Info("get staff Summary " + user, userModel.RealName);

            var sql = @"select
	ViewPortfolioInfo.abbName,
	[InvestHistorySummary].portfolioid,
	closeDate,
	currency,
	sum(cost) as cost,
	fundFamillyName,
	[portfolioIntroducer],
	[portfolioManager],
	[groupMember]
from
	(
	select
		[InvestHistoryForIRR].portfolioid,
		closeDate,
		currency,
		cost,
		proceeds,
		costRelization,
		fundFamillyName
	from
		[InvestHistoryForIRR]
	where cost > 0
	) as [InvestHistorySummary]
left outer join ViewPortfolioInfo on
	ViewPortfolioInfo.portfolioID = [InvestHistorySummary].portfolioID
group by
	[InvestHistorySummary].portfolioid,
	ViewPortfolioInfo.abbName,
	closeDate,
	currency,
	fundFamillyName,
	projectManager,
	[portfolioIntroducer],
	[portfolioManager],
	[groupMember],
	[postInvestMember],
	[postInvestManager]
having " + $" (portfolioManager like '%{user}%' OR GroupMember like '%{user}%' OR PortfolioIntroducer like '%{user}%' )" 
+ $" AND closeDate > '{startdate}' AND closeDate < '{enddate}' " 
+ "order by portfolioid,closeDate,currency;";

           var postInvestProjects = new investForIRRBLL().GetListBySql(sql);

            string sqlStr = @"select
	project.*,
    ps.projectId as scoreprojectid
from
	project
left join 
(
	select
		distinct projectId
	from
		ProjectScoreStage
	where
		Average != 0
		AND State = 0 ) as ps 
on
	project.Id = ps.projectId
where
	Status <> 0 AND " + 
    $"(ProjectManager like '%{user}%' or groupMember like '%{user}%' or Introducer like '%{user}%' or finder like '%{user}%' ) "
   + $" AND PubTime > '{startdate}' AND PubTime < '{enddate}' "
   + " order by PubTime DESC ";

            var projectList = new ProjectBLL().GetListBySql(sqlStr);
            var scoredProjectList = projectList.Where(val => val.scoreprojectid > 0).ToList();
            //string info = Utility.HttpHelper.GetData("https://fms.gaorongvcvc.com/portfolio.ashx", "name=岳斌");
            //Logger.Info(info);

            var ProjectManagerList = (from x in projectList where x.ProjectManager.Contains(user) select x).Distinct();
            var ProjectManagerCount = (from x in ProjectManagerList select x.Name).Distinct().Count();

            var IntroducerList = (from x in projectList where ((x.Introducer.Contains(user) || x.finder.Contains(user)) && !x.ProjectManager.Contains(user)) select x).Distinct();
            var IntroducerCount = (from x in IntroducerList select x.Name).Distinct().Count();

            ajaxResult.data = new
            {
                managerPostInvestProjects = postInvestProjects.Where(val => val.PortfolioManager.Contains(user)),
                memberPostInvestProjects = postInvestProjects.Where(val => val.GroupMember.Contains(user)),
                sourceInvestProjects = postInvestProjects.Where(val => val.PortfolioIntroducer.Contains(user)),

                managerScoredProjectList = scoredProjectList.Where(val => val.ProjectManager.Contains(user)),
                memberScoredProjectList = scoredProjectList.Where(val => val.groupMember.Contains(user)),
                ProjectManagerList,
                IntroducerList,
                ProjectManagerCount,
                IntroducerCount,

            };
           
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;
        }


        /// <summary>
        /// 创建人报表
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public AjaxResult GetCreatorsSummary(NameValueCollection paramValues, bool selfOnly = false)
        {
            // TODO
            int page = WebHelper.GetValueInt("page", 1, paramValues);
            int limit = WebHelper.GetValueInt("limit", 10, paramValues);
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);

            Member userModel = GetLogOnUser(uid);

            Logger.Info("get staff Summary", userModel.RealName);
            if (userModel == null || userModel.Levels == (int)MemberLevels.Normal || userModel.Levels == (int)MemberLevels.LimitedUser)
            {
                ajaxResult.code = (int)ResultCode.noright;
                ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.noright);
                return ajaxResult;
            }

            int roleId = WebHelper.GetValueInt("ToRoleId", 0, paramValues);

            string keyWords = WebHelper.GetValue("Name", string.Empty, paramValues);

            List<Member> MemberList;
            if(selfOnly)
            {
                MemberList = new List<Member>() { userModel };
            }else if (roleId > 0)
            {
                MemberList = GetListByGroupId(roleId + "");
            } else
            {
                MemberList = GetAllList(); // GetList(whereSql, limit, page, "Id,RealName", sort);
            }
            if (!string.IsNullOrEmpty(keyWords))
            {
                MemberList = MemberList.Where(val => val.RealName.Contains(keyWords)).ToList();
            }
            ajaxResult.count = MemberList.Count;
            if (MemberList != null && MemberList.Count() > 0)
            {
                string sqlStr = $"SELECT * FROM [Project]  where Status<>{(int)ProjectStatus.delete} ";
                string startDate = WebHelper.GetValue("startdate", string.Empty, paramValues);
                DateTime today = DateTime.Now;
                if (!string.IsNullOrEmpty(startDate))
                {
                    sqlStr += $"AND PubTime>='{startDate}' ";
                }
                else
                    sqlStr += $"AND PubTime>='{today.AddDays(-today.DayOfYear)}' ";

                string endDate = WebHelper.GetValue("enddate", string.Empty, paramValues);
                if (!string.IsNullOrEmpty(endDate))
                {
                    sqlStr += $"AND PubTime<='{Convert.ToDateTime(endDate).ToString("yyyy-MM-dd")}' ";
                }
                else
                    sqlStr += $"AND PubTime<='{today.ToShortDateString()}' ";

                var projectList = new ProjectBLL().GetListBySql(sqlStr);
                //string info = Utility.HttpHelper.GetData("https://fms.gaorongvcvc.com/portfolio.ashx", "name=岳斌");
                //Logger.Info(info);
                ajaxResult.data = MemberList.Select(item =>
                {
                    int ProjectManagerCount = (from x in projectList where x.ProjectManager.Contains(item.RealName) select x.Name).Distinct().Count();
                    int DDCount = (from x in projectList where (x.ProjectManager.Contains(item.RealName)) && x.nextStepStatus == "Pre-DD" select x.Name).Distinct().Count();
                    int FaCount = (from x in projectList where (x.ProjectManager.Contains(item.RealName)) && x.Source == "3" select x.Name).Distinct().Count();
                    int FugaiCount = (from x in projectList where (x.ProjectManager.Contains(item.RealName)) && x.Source == "0" select x.Name).Distinct().Count();
                    int YanjiuCount = (from x in projectList where (x.ProjectManager.Contains(item.RealName)) && x.Source == "4" select x.Name).Distinct().Count();
                    int TSCount = (from x in projectList where (x.ProjectManager.Contains(item.RealName)) && x.nextStepStatus == "TS已签署" select x.Name).Distinct().Count();
                    return new
                    {
                        item.Id,
                        item.RealName,
                        EditorCount = (from x in projectList where x.EditorName.Contains(item.RealName) select x.Name).Distinct().Count(),
                        //projectList.Where(x => x.EditorName.Contains(item.RealName)).Distinct().Count(),
                        PTCPCount = (from x in projectList where x.InteralPTCP.Contains(item.RealName) select x.Name).Distinct().Count(),
                        //projectList.Where(x => x.InteralPTCP.Contains(item.RealName)).Distinct().Count(),
                        IntroducerCount = (from x in projectList where ((x.Introducer.Contains(item.RealName) || x.finder.Contains(item.RealName)) && !x.ProjectManager.Contains(item.RealName)  ) select x.Name).Distinct().Count(),
                        IntroducedProjects = (from x in projectList where ((x.Introducer.Contains(item.RealName) || x.finder.Contains(item.RealName)) && !x.ProjectManager.Contains(item.RealName)) select x.Name).Distinct(),
                        //projectList.Where(x => x.Introducer.Contains(item.RealName)).Distinct().Count(),
                        ProjectManagerCount,
                        groupMemberCount = (from x in projectList where x.groupMember.Contains(item.RealName) select x.Name).Distinct().Count(),
                        //projectList.Where(x => x.groupMember.Contains(item.RealName)).Distinct().Count(),
                        DDManagerCount = (from x in projectList where x.DDManager.Contains(item.RealName) select x.Name).Distinct().Count(),
                        //projectList.Where(x => x.DDManager.Contains(item.RealName)).Distinct().Count(),
                        FugaiCount,
                        YanjiuCount,
                        TongshitijiCount = (from x in projectList where (x.ProjectManager.Contains(item.RealName)) && x.Source == "1" select x.Name).Distinct().Count(),
                        TongshijieshaoCount = (from x in projectList where (x.ProjectManager.Contains(item.RealName)) && x.Source == "2" select x.Name).Distinct().Count(),
                        FaCount,

                        FUGAIP = ProjectManagerCount > 0 ? string.Format("{0:N1}", ((float)FugaiCount * 100 / (from x in projectList where (x.ProjectManager.Contains(item.RealName) || x.finder.Contains(item.RealName) || x.Introducer.Contains(item.RealName)) select x.Name).Distinct().Count())) + "%" : "",
                        FAP = ProjectManagerCount > 0 ? string.Format("{0:N1}", ((float)FaCount * 100 / (from x in projectList where (x.ProjectManager.Contains(item.RealName) || x.finder.Contains(item.RealName) || x.Introducer.Contains(item.RealName)) select x.Name).Distinct().Count())) + "%" : "",

                        DDP = ProjectManagerCount > 0 ? string.Format("{0:N1}", ((float)DDCount * 100 / ProjectManagerCount)) + "%" : "",
                        DDCount,
                        TSCount,
                        TSP = ProjectManagerCount > 0 ? string.Format("{0:N1}", ((float)TSCount * 100 / ProjectManagerCount)) + "%" : "",
                        PartnerCount = (from x in projectList where (x.ProjectManager.Contains(item.RealName)) && x.nextStepStatus == "安排合伙人见面" select x.Name).Distinct().Count(),
                        TeamDiscussCount = (from x in projectList where (x.ProjectManager.Contains(item.RealName)) && x.nextStepStatus == "小组讨论" select x.Name).Distinct().Count()
                    };
                });
            }
            else
            {
                ajaxResult.data = MemberList;
            }
            
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;
        }


        /// <summary>
        /// 员工列表
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public AjaxResult GetStaffs(NameValueCollection paramValues)
        {
            ajaxResult.data = GetStaffList(); // GetList(strWhere, int.MaxValue, 1, "Id,RealName", sort);
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;

        }
        public Member GetLogOnUser(int uid = 0)
        {
            try
            {
                Member currUser = null;
                if (uid > 0)
                {
                    currUser = GetModelByCache(uid);
                    if (currUser != null)
                        return currUser;
                }

                //currUser = WebHelper.GetSession<Member>("banyan_user_session");
                //if (currUser != null && currUser.Id > 0)
                //{
                //    return currUser;
                //}
                string currUserDes = WebHelper.GetCookie("banyan_logon_user");
                if (!string.IsNullOrEmpty(currUserDes))
                {
                    return getUserByCookie(currUserDes);
                }
                return null;
            }
            catch
            {
                return null;
            }
        }
        public Member getUserByCookie(string cookie)
        {
            string[] desInfo = DESEncrypt.Decrypt(cookie).Split(',');
            // cookie认证方式，若redis无存储，只用了cookie保存的的id，权限仍然需要读取
            Member model = GetModelByCache(Convert.ToInt32(desInfo[0]));
            return model;
        }

        public string tryGet(Dictionary<string, List<string>> user, string key, string name, bool log = true)
        {
            List<string> res;
            if (!user.TryGetValue(key, out res) )
            {
                if (!key.Equals("postalAddress") && !key.Equals("homePostalAddress"))
                {
                    if (log)
                    {
                        Logger.Error($"用户{name}的{key}信息不全");
                    } else
                    {
                        Logger.Warn($"用户{name}的{key}信息不全");
                    }
                }
            }
            if (!res.IsEmpty() && res.Count() != 0)
            {
                return res[0];
            }
            return "";
        }

        public Member convertLdapEntryToMemberSimple(Dictionary<string, List<string>> user, bool sendErr = true)
        {
            var model = new Member();
            try
            {
                model.Id = int.Parse(user["employeeNumber"][0]);
            } catch(Exception e)
            {
                if (sendErr)
                {
                    Logger.Error(e.Message, e);
                } else
                {
                    Logger.Warn(e.Message, e);
                }
            }
            model.RealName = tryGet(user, "cn", model.RealName);
            model.Telephone = tryGet(user, "mobile", model.RealName, sendErr);
            model.Mail = tryGet(user, "mail", model.RealName, sendErr);//  user["mail"][0];
            model.Funds = tryGet(user, "homePostalAddress", model.RealName);
            model.OpenId = tryGet(user, "street", model.RealName, false);// user["street"][0];
            model.Avatar = tryGet(user, "labeledURI", model.RealName, false);// user["labeledURI"][0];
            model.CompanyName = tryGet(user, "o", model.RealName); // user["o"][0];
            model.Description = tryGet(user, "description", model.RealName, false); // user["o"][0];
            try
            {
                DateTime dt;
                DateTime.TryParseExact(user["initials"][0], "yyyy/MM/dd-HH:mm:ss", new System.Globalization.DateTimeFormatInfo(), System.Globalization.DateTimeStyles.None, out dt);
                model.AddTime = dt;
            }
            catch (Exception e)
            {
                Logger.Error(e.Message, e);
            }
            return model;
        }
            public Member convertLdapEntryToMember(Dictionary<string, List<string>> user)
        {
            var model = new Member();
            model.Id = int.Parse(user["employeeNumber"][0]);
            model.RealName = tryGet(user, "cn", model.RealName);
            model.Telephone = tryGet(user, "mobile", model.RealName);
            //model.Mail = tryGet(user, "mail", model.Mail);//  user["mail"][0];
            model.OpenId = tryGet(user, "street", model.RealName,false);// user["street"][0];
            model.Avatar = tryGet(user, "labeledURI", model.RealName, false);// user["labeledURI"][0];
            model.CompanyName = tryGet(user, "o", model.RealName); // user["o"][0];
            try
            {
                DateTime dt;
                DateTime.TryParseExact(user["initials"][0], "yyyy/MM/dd-HH:mm:ss", new System.Globalization.DateTimeFormatInfo(), System.Globalization.DateTimeStyles.None, out dt);
                model.AddTime = dt;
            } catch(Exception e)
            {
                Logger.Error(e.Message, e);
            }

            List<string> memberOfs = user["memberOf"];
            return convertGroupHelper(model, memberOfs );
        }

        public Member convertGroupHelper(Member model, List<string> memberOfs, bool mock = false)
        {
            model.Status = (int)MemberStatus.disable;
            model.Levels = (int)MemberLevels.Normal;
            var groupList = new List<string>() { };
            string pattern = @"^cn=(\d+)\w+,ou=invest,ou=groups,dc=gaorongvc,dc=cn$";
            foreach (var s in memberOfs)
            {
                switch (s)
                {
                    case "cn=underReview,ou=groups,dc=gaorongvc,dc=cn":
                        if(model.Status == (int)MemberStatus.disable) {
                           model.Status = (int)MemberStatus.review; // 若为禁用权限，会进入对应return语句
                        }
                        //return model;
                        break; // 若已赋予权限，不再受其他系统待审核影响，这里无需return
                    case "cn=reviewDeny,ou=invest,ou=groups,dc=gaorongvc,dc=cn":
                        model.Status = (int)MemberStatus.notpass;
                        return model;
                    case "cn=leave,ou=groups,dc=gaorongvc,dc=cn":
                        model.Status = (int)MemberStatus.disable;
                        return model;
                    case "cn=admin,ou=invest,ou=groups,dc=gaorongvc,dc=cn":
                        model.Levels = (int)MemberLevels.Administrator;
                        model.Status = (int)MemberStatus.enable;
                        break;
                    case "cn=super,ou=invest,ou=groups,dc=gaorongvc,dc=cn":
                        model.Levels = (int)MemberLevels.SuperUser;
                        model.Status = (int)MemberStatus.enable;
                        break;
                    case "cn=investPartner,ou=invest,ou=groups,dc=gaorongvc,dc=cn":
                        model.Levels = (int)MemberLevels.Partner;
                        model.Status = (int)MemberStatus.enable;
                        break;
                    case "cn=dealrmb,ou=invest,ou=groups,dc=gaorongvc,dc=cn":
                        model.Levels = (int)MemberLevels.DealRMB;
                        model.Status = (int)MemberStatus.enable;
                        break;
                    case "cn=dealusd,ou=invest,ou=groups,dc=gaorongvc,dc=cn":
                        model.Levels = (int)MemberLevels.DealUSD;
                        model.Status = (int)MemberStatus.enable;
                        break;
                    case "cn=onlyrmb,ou=invest,ou=groups,dc=gaorongvc,dc=cn":
                        model.Levels = (int)MemberLevels.OnlyRMB;
                        model.Status = (int)MemberStatus.enable;
                        break;
                    case "cn=onlyusd,ou=invest,ou=groups,dc=gaorongvc,dc=cn":
                        model.Levels = (int)MemberLevels.OnlyUSD;
                        model.Status = (int)MemberStatus.enable;
                        break;
                    case "cn=limitedMedical,ou=invest,ou=groups,dc=gaorongvc,dc=cn":
                        model.limitedMedical = true;
                        break;
                    case "cn=dealall,ou=invest,ou=groups,dc=gaorongvc,dc=cn":
                        model.Levels = (int)MemberLevels.DealALL;
                        model.Status = (int)MemberStatus.enable;
                        break;
                    case "cn=limitedJoinTime,ou=invest,ou=groups,dc=gaorongvc,dc=cn":
                        model.limitedJoinTime = true;
                        model.Status = (int)MemberStatus.enable;
                        break;
                    case "cn=limited,ou=invest,ou=groups,dc=gaorongvc,dc=cn":
                        model.Levels = (int)MemberLevels.LimitedUser;
                        model.Status = (int)MemberStatus.enable;
                        break;
                    case "cn=superAdmin,ou=research,ou=groups,dc=gaorongvc,dc=cn":
                        model.LevelResearch = (int)MemberLevelResearch.superAdmin;
                        break;
                    default:
                        var m = Regex.Match(s, pattern);
                        if (m.Success)
                        {
                            groupList.Add(m.Groups[1].Value);
                            model.Status = (int)MemberStatus.enable;
                        }
                        break;
                }
            }
            model.Groups = string.Join(",", groupList);
            return model;
        }
        public Tuple<bool, string> LogonWechat(NameValueCollection paramValues)
        {
            string unionid = "";
            try
            {
                string code = WebHelper.GetValue("code", string.Empty, paramValues);
              
                unionid = WechatUtils.GetUnionIDWeb(code);
                if (code.IsEmpty() || unionid.IsEmpty())
                {
                    return new Tuple<bool, string>(false, "");
                }
                var user = Ldap.UnionIdMatchedUser(unionid);
                var model = convertLdapEntryToMember(user);
                 LogonMemberCheck(model);
                Logger.Info("wechat scan login", model.RealName);
                return new Tuple<bool, string>(true, unionid);
            }
              catch (Exception ex)
            {
                if(ex.Message.Contains("不存在此用户"))
                {
                    Logger.Info("wechat first scan", unionid);
                    return new Tuple<bool, string>(false, unionid);
                }
                Logger.Error(ex.Message + " " + unionid   + ex.StackTrace);
                return new Tuple<bool, string>(false, unionid);
            }
        }
            public AjaxResult LogonValid(NameValueCollection paramValues, bool bindwechat = false)
        {
            //AddUidList();
            int loginway = WebHelper.GetValueInt("loginway", 0, paramValues);
            string passwd = WebHelper.GetValue("passwd", string.Empty, paramValues);
            string verifycode = WebHelper.GetValue("verifycode", string.Empty, paramValues);
            string phone = WebHelper.GetValue("phone", string.Empty, paramValues);
            Logger.Info("login phone: " + phone);

            ajaxResult.code = (int)ResultCode.paramerror;
            if (!Validate.IsValidPhone(phone))
            {
                ajaxResult.msg = "请填写合法的手机号";
                return ajaxResult;
            }

            if (loginway == 0 && !Validate.IsValidLength(passwd))
            {
                ajaxResult.msg = "请填写8-20位密码，若密码过短请及时修改";
                return ajaxResult;
            }
            if (loginway == 1 && !Validate.IsValidCodeLong(verifycode))
            {
                ajaxResult.msg = "请填写验证码";
                return ajaxResult;
            }


            Dictionary<string, List<string>> user = null;
            // 密码登陆需验证ldap密码
            if (loginway == 0)
            {
                try
                {
                    user = Ldap.mobilePasswdMatchedUser(phone, passwd);
                }
                catch (LdapException ldapEx)
                {
                    ajaxResult.msg = ldapEx.Message;
                    return ajaxResult;
                }
                catch (Exception ex)
                {
                    ajaxResult.msg = ex.Message;
                    return ajaxResult;
                }
            }
            else // 短信验证需验证ldap用户存在
            {
                try
                {
                    // 短信验证码校验
                    var cacheKey = string.Format(RedisKey.smscode_verify, phone, (int)VerifyType.identity);
                    var code = RedisUtil.Get<string>(cacheKey);
                    if (string.IsNullOrEmpty(code) || !verifycode.Equals(code))
                    {
                        ajaxResult.code = (int)ResultCode.verifycode;
                        ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.verifycode);
                        return ajaxResult;
                    }
                    user = Ldap.mobilePasswdMatchedUser(phone);

                }
                catch (LdapException ldapEx)
                {
                    ajaxResult.msg = ldapEx.Message;
                    return ajaxResult;
                }
                catch (Exception ex)
                {
                    ajaxResult.msg = ex.Message;
                    return ajaxResult;
                }
            }

            Member model = convertLdapEntryToMember(user);
            if(bindwechat)
            {
                string unionid = WebHelper.GetValue("unionid", string.Empty, paramValues);
                if(unionid.IsEmpty())
                {
                    ajaxResult.msg = "参数异常， 未能获取微信id";
                    return ajaxResult;
                }
                try
                {
                    Ldap.AddAttr(model.RealName, "roomNumber", unionid);
                }catch(Exception e)
                {
                    Logger.Error(e.Message + " " + e.StackTrace);
                    ajaxResult.msg = "微信绑定失败，请联系管理员";
                    return ajaxResult;
                }
            }
            return LogonMemberCheck(model);
        }

        public AjaxResult LogonMemberCheck(Member model)
        {
            if (model.Status == (int)MemberStatus.review)
            {
                ajaxResult.msg = "登录账户还未通过审核";
                return ajaxResult;
            }
            else if (model.Status == (int)MemberStatus.notpass)
            {
                ajaxResult.msg = "登录账户审核未通过";
                return ajaxResult;
            }
            else if (model.Status == (int)MemberStatus.disable)
            {
                ajaxResult.msg = "登录账户被禁用";
                return ajaxResult;
            }
            ajaxResult.code = (int)ResultCode.success;
            Logger.Info($"web用户登陆：{model.RealName}");
            //WebHelper.WriteSession<Member>("banyan_user_session", model);
            string cacheKey2 = string.Format(RedisKey.member_model, model.Id);
            RedisUtil.Set<Member>(cacheKey2, model, TimeSpan.FromMinutes(10));
            WebHelper.WriteCookie("banyan_logon_user", DESEncrypt.Encrypt($"{model.Id},{model.RealName.Replace(",", "，")},{model.Levels == (byte)MemberLevels.Administrator}")); //登录缓存2（, 120）小时后失效

            return ajaxResult;
        }


        public object GetUser(int userId)
        {
            Member model = GetModelByCache(userId) ?? new Member();
            if (model.Levels == (byte)MemberLevels.Administrator)
            {
                ajaxResult.msg = "权限不足";
                return ajaxResult;
            }

            var roles = new RoleBLL().GetList();
            if (!string.IsNullOrEmpty(model.Groups))
            {
                var groupsStr = $",{model.Groups},";
                foreach (var item in roles)
                    item.UserSet = groupsStr.Contains(item.Id.ToString());
            }

            var data = new
            {
                roles,
                user = model,
            };

            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = data;
            return ajaxResult;
        }

        /// <summary>
        /// 安全退出
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public AjaxResult LogOut()
        {
            Member userModel = GetLogOnUser();
           
            WebHelper.RemoveCookie("banyan_logon_user");
            //WebHelper.RemoveSession("banyan_user_session");
            ClearCache(userModel.Id);
            Logger.Info($"{userModel.RealName} log out");
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;
        }

    }
}
