﻿@model Banyan.Domain.Article
@{
    Layout = null;
    var roleList = (List<Banyan.Domain.Role>)ViewData["roleList"];
}

<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>高榕创投IMS-GAORONG VENTURES</title>

    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" href="~/content/img/favicons/favicon.ico">
    <link rel="icon" type="image/png" href="~/content/img/favicons/favicon.ico" sizes="16x16">
    <title>设置阅读权限</title>
    <link type="text/css" href="/content/js/plugins/layui/css/layui.css" rel="stylesheet" />
    <style>
        .layui-form-label {
            padding: 9px 0;
        }

        .layui-input-block {
            margin-left: 90px;
        }
    </style>
</head>
<body>
    <div style="padding:50px 20px 20px">
        <form class="layui-form" id="madd-form">
            <input type="hidden" id="roleids" value="@(Model.ToRoleIds)" />
            <div class="layui-form-item">
                <label class="layui-form-label">用户分类：</label>
                <div class="layui-input-block">
                    @foreach (var rli in roleList)
                    {
                        <input type="checkbox" name="role" lay-filter="role" value="@(rli.Id)" lay-skin="primary" title="@(rli.RoleName)" checked="@(Model.ToRoleIds.Contains(rli.Id.ToString()))" />
                    }
                </div>
                <div id="role-tip"></div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn">保 存</button>
                </div>
            </div>
        </form>
    </div>

    <script type="text/javascript" src="~/Content/js/core/jquery.min.js"></script>
    <script type="text/javascript" src="~/content/js/plugins/layui/layui.js"></script>

    <link href="~/Content/js/plugins/nice-validator/jquery.validator.css" rel="stylesheet" />
    <script src="~/Content/js/plugins/nice-validator/jquery.validator.min.js"></script>
    <script src="~/Content/js/plugins/nice-validator/local/zh-CN.js"></script>

    <script type="text/javascript">
        var layer, roleArr = $('#roleids').val() == '' ? [] : $('#roleids').val().split(',');
        
        layui.use(['layer', 'form'], function () {
            layer = layui.layer
            var form = layui.form;

            form.on('checkbox(role)', function (data) {
                if (data.elem.checked) {
                    roleArr.push(data.value);
                } else {
                    var index = roleArr.indexOf(data.value);
                    if (index > -1) {
                        roleArr.splice(index, 1);
                    }
                }
            });
        });

        $(function () {
            $('#madd-form').validator({
                theme: 'yellow_top_effect',
                timely: 2,
                stopOnError: true,
                fields: {
                    role: "required;",
                },
                valid: function (form) {
                    $.ajax({
                        type: 'POST',
                        url: '@(Url.Action("articleset", "adminapi"))',
                        data: { id: @(Model.Id), field: 'role', values: roleArr.join(',') },
                        success: function (data) {
                            if (data.code == 0) {
                                parent.layer.closeAll();
                                parent.openurl('@(Url.Action("articles", "article"))');
                            } else {
                                layer.msg(data.msg);
                            }
                        },
                        error: function () {
                            layer.msg("很抱歉，请求异常！");
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>
