﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;

namespace Banyan.Apps
{
    public class PortfolioExitExtBLL : BaseDAL<PortfolioExitExt>
    { }
    public class PortfolioExitBLL : BaseDAL<PortfolioExit>
    {
        private readonly AjaxResult ajaxResult = null;
        private SysLogBLL logBLL = new SysLogBLL();
        private PortfolioBLL portfolioBll = new PortfolioBLL();
        private PortfolioExitExtBLL portfolioExitExtBll = new PortfolioExitExtBLL();
        private string searchStr = @"select
	pe.* ,
	pas.discussType,
	pas.dicussDate,
	pas.discussContent,
	pbi.portfolioManager,
	pbi.postInvestMember,
	pbi.exitManager
from
	portfolioExit pe
join PortfolioBasicInfo pbi on
	pe.portfolioID = pbi.portfolioID collate Chinese_PRC_CI_AS
OUTER APPLY (
    SELECT TOP 1 
        discussType,
        discussContent,
        dicussDate
    FROM Project_ActiveStatus
    WHERE projectID = pe.dealID collate Chinese_PRC_CI_AS
    ORDER BY dicussDate DESC
) pas ";
        public PortfolioExitBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        public AjaxResult Save(PortfolioExitExt model, string attach = "")
        {
            AjaxResult ajaxResult = new AjaxResult();
            var user = new MemberBLL().GetLogOnUser();
            var portfolio = portfolioBll.GetModel($"Name='{model.Name}'");

            if (model.Id > 0)
            {
                try
                {
                    var original = GetModel(model.Id);
                    model.portfolioID = portfolio.portfolioID;
                    model.modifier = user.RealName;
                    model.modifiedDate = DateTime.Now;
                    ajaxResult.data = base.Update(model, @"Name,portfolioID,dealID,ToRoleId,exitFund,exitType,exitMember,exitContributor,exitContribution,exitAmount,exitCost,exitRatio,exitPlan,remark,modifiedDate,modifier,exitStatus,exitStatusExplain,legalOpinion,investOpinion,viewer,editor,isprivate");//MeetingDate，LastTime
                }
                catch (Exception e)
                {
                    ajaxResult.code = (int)ResultCode.exception;
                    ajaxResult.msg = e.Message;
                    return ajaxResult;
                }
                ajaxResult.code = (bool)ajaxResult.data ? (int)ResultCode.success : (int)ResultCode.exception;
                updateLog("Update Portfolio Exit", "Update", model.Name, user);
            }
            else
            {
                try
                {
                    if (user != null)
                    {
                        model.creator = user.RealName;
                    }

                    model.createdDate = DateTime.Now;
                    model.modifiedDate = DateTime.Now;
                    model.portfolioID = portfolio.portfolioID;
                    model.Id = Convert.ToInt32(Add(model));
                }
                catch (Exception e)
                {
                    Logger.Error(e.Message + " " + e.StackTrace, user.RealName);
                    ajaxResult.code = (int)ResultCode.exception;
                    ajaxResult.msg = e.Message;
                    return ajaxResult;
                }
                if (model.Id > 0)
                {
                    AddAttach(attach, model.Id); // 附件添加
                }
                ajaxResult.code = model.Id > 0 ? (int)ResultCode.success : (int)ResultCode.exception;
                updateLog("New Portfolio Exit", "New", model.Name, user);
            }
            if (model.exitManager != portfolio.ExitManager)
            {
                portfolio.ExitManager = model.exitManager;
                portfolioBll.Update(portfolio, "exitManager");
            }
            ajaxResult.data = model.Id;
            return ajaxResult;
        }

        public void addFinancialViewer(int exitID)
        {
            var model = GetModel(exitID);
            if (model == null)
            {
                return;
            }
            if (model.exitFund.Contains("RMB"))
            {
                if (!model.viewer.Contains("张安丽"))
                {
                    if(model.viewer.IsEmpty())
                    {
                        model.viewer = "张安丽";
                    }
                    else
                    {
                        model.viewer += ",张安丽";
                    }
                }
            } else if (model.exitFund.Contains("USD"))
            {
                if (!model.viewer.Contains("王凯邦"))
                {
                    if (model.viewer.IsEmpty())
                    {
                        model.viewer = "王凯邦";
                    }
                    else
                    {
                        model.viewer += ",王凯邦";
                    }
                }
            } 
            Update(model, "viewer");
            var user = new MemberBLL().GetLogOnUser();
            updateLog("Web, Portfolio Exit", "close sync viewer to " + model.viewer, "ID:" + exitID, user, model.Name);
        }

        public bool AddAttach(string attach, int sourceId)
        {
            Member user = new MemberBLL().GetLogOnUser();
            if (string.IsNullOrEmpty(attach))
                return true;

            List<AttachmentFMS> list = Utils.DeserializeObjectByJson<List<AttachmentFMS>>(attach);
            if (list == null || list.Count <= 0)
            {
                return true;
            }
            AttachmentFMSBLL attachBll = new AttachmentFMSBLL();
            //TransactionModel tranModel = new TransactionModel();
            //foreach(var item in list)
            //{
            //    tranModel.Add(attachBll.BuildAddSql(item));
            //}
            foreach (var item in list)
            {
                try
                {
                    item.SourceId = sourceId;
                    item.AtName = item.AtName.Replace("+", "加").Replace(" ", "_");
                    var tmpPathArr = item.AtUrl.Split('/');
                    item.Path = $"/imsfiles/{tmpPathArr[2]}/{tmpPathArr[3]}/" + item.AtName;
                    item.Creator = user.RealName;
                    try
                    {
                        if (!item.Content.IsEmpty() && !item.Content.Equals("-1"))
                        {
                            item.Content = logBLL.GetModel(Convert.ToInt32(item.Content)).Description;
                        }
                    }
                    catch (Exception e)
                    {
                        Logger.Error(e.Message, e);
                    }
                    attachBll.Add(item);
                }
                catch (Exception e)
                {
                    Logger.Error(e.Message, user.RealName);
                }
            }
            return true;
        }

        public AjaxResult Delete(int id)
        {
            ajaxResult.code = (int)ResultCode.exception;
            Member user = new MemberBLL().GetLogOnUser();
            if (id <= 0)
            {
                ajaxResult.msg = $"参数不合法";
                return ajaxResult;
            }
            var m = GetModel(id);
            m.Status = (int)PortfolioExitStatus.delete;
            base.Update(m, "status");
            ajaxResult.code = (int)ResultCode.success;
            updateLog("Web, Portfolio Exit", "delete", "ID:" + id, user, "");
            return ajaxResult;
        }

        public AjaxResult GetDetail(int id, string from ="Web")
        {
            Member user = new MemberBLL().GetLogOnUser();

            var m = new PortfolioExitExtBLL().GetModelBySql(searchStr + $" where pe.id = {id}");
            if (m == null)
            {
                ajaxResult.code = (int)ResultCode.notdata;
                return ajaxResult;
            }

            updateLog(from + ", Portfolio Exit", "view", "id: " + id + ",Name:" + m.Name, user);

            //返回数据
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = m;

            return ajaxResult;
        }
        public AjaxResult GetDetail(NameValueCollection paramValues)
        {
            var id = WebHelper.GetValueInt("id", 0, paramValues);
            int userId = WebHelper.GetValueInt("uid", 0, paramValues);
            //string tag = WebHelper.GetValue("tag", string.Empty, paramValues);

            if (id == 0 || userId <= 0)
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                ajaxResult.msg = "参数不合法！";
                return ajaxResult;
            }

            var userModel = new MemberBLL().GetModelByCache(userId);
            if (userModel == null || userModel.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = userModel == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = userModel == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }

            return GetDetail(id, "miniapp");
        }

        public void updateLog(string page, string action, string description, Member user, string project = "")
        {
            SysLog log = new SysLog
            {
                Page = page,
                Action = action,
                Project = project,
                Description = description,
                CreatedBy = user == null ? "" : user.RealName,
                CreatorId = user == null ? 0 : user.Id,
            };
            SysLogBLL logBLL = new SysLogBLL();
            logBLL.Add(log);
        }

        private List<PortfolioExitExt> searchCommon(NameValueCollection paramValues, Member user,
      int pageIndex, int pageSize, out int count, bool searchNameOnly = false)
        {
            string sort;
            string strWhere = getStrWhere(paramValues, user, out sort, searchNameOnly);

            updateLog("MiniApp, Get Portfolio Exit List", "view", strWhere, user);

            string Fund = WebHelper.GetValue("Fund", string.Empty, paramValues);
            List<PortfolioExitExt> PortfolioList;

            List<string> fundList = new List<string>();
            //if (!MemberBLL.adminOrSuper(user) && MemberBLL.gaorongFinOrOpNoAdmin(user) && Fund.IsEmpty())
            //{
            //    Fund = user.Funds;
            //    fundList = new List<string>(user.Funds.Split(','));
            //}
            var dic = new Fund2PortfolioSummaryBLL().portfolioIDFundDic();
            if (Fund.IsEmpty())
            {
                PortfolioList = portfolioExitExtBll.GetListBySql($"{searchStr} where {strWhere} order by {sort}  offset {pageSize * (pageIndex - 1)} row fetch next {pageSize} row only");

                count = GetListBySql($"{searchStr} where {strWhere} ").Count;
            }
            else
            {
                PortfolioList = portfolioExitExtBll.GetList(strWhere, 10000, 0, "*", sort);
              
                PortfolioList = PortfolioList.Where(a => {
                    if (dic.ContainsKey(a.portfolioID))
                    {
                        if (fundList.Count == 0)
                        {
                            return dic[a.portfolioID].Contains(Fund);
                        }
                        else
                        {
                            return fundList.Select((val) => dic[a.portfolioID].Contains(val))
                            .Aggregate(false, (res, val) => res |= val);
                        }
                    }
                    else
                    {
                        Logger.Info(a.Name + " not in fund");
                        return false;
                    }
                }
                ).ToList();
                count = PortfolioList.Count();
                int start = (pageIndex - 1) * pageSize;
                if (start > count) start = 0;
                PortfolioList = PortfolioList.GetRange(start, start + pageSize > count ? count - start : pageSize);
            }
            if (PortfolioList != null && PortfolioList.Count() > 0 )
            {
                var roleList = new RoleBLL().GetList(false);
                if (roleList != null && roleList.Count() > 0)
                {
                    foreach (var item in PortfolioList)
                    {
                        item.RoleName = roleList.Where(x => x.Id == item.ToRoleId).Select(x => x.RoleName).FirstOrDefault()?.ToString();
                        item.discussDateStr = item.dicussDate.ToString("yyyy-MM-dd");
                        item.IsOperate = user.Levels == (byte)MemberLevels.Administrator || user.Levels == (byte)MemberLevels.DealALL || item.creator == user.RealName || user.Levels == (byte)MemberLevels.SuperUser ||
                           item.postInvestManager.Contains(user.RealName) || item.editor.Contains(user.RealName)
                            || item.exitManager.Contains(user.RealName) || item.portfolioManager.Contains(user.RealName);
                        item.remarkBottom = item.createdDate.ToString("yyyy-MM-dd") +  (String.IsNullOrEmpty(item.remark) ? "" :  " | " + item.remark);
                    }
                }
            }
            return PortfolioList;
        }
        private string getStrWhere(NameValueCollection paramValues, Member user, out string sort, bool searchNameOnly = false)
        {
            string strWhere = $" Status <> {(int)PortfolioExitStatus.delete} ";

            if (!user.CompanyName.Equals("高榕资本"))
            {
                strWhere += $" AND pe.Name='no companies should be seen' ";
            }

            if (user.Levels != (int)MemberLevels.Administrator && user.Levels != (int)MemberLevels.SuperUser )
            {
                strWhere += $" AND (Creator='{user.RealName}' OR pbi.portfolioManager like '%{user.RealName}%' OR pbi.exitManager like '%{user.RealName}%' OR pe.exitMember like '%{user.RealName}%' OR pe.viewer like '%{user.RealName}%' OR pe.editor like '%{user.RealName}%') ";
            } else if (user.RealName == "孙杭萍")
            { // 管理员权限私密项目也不可见，除非由本人创建（打分改由创建人发起和关闭）
                strWhere += $" AND (isPrivate = 0 or isPrivate is NULL  or Creator='{user.RealName}' )";
            }

            int ToRoleId = WebHelper.GetValueInt("ToRoleId", 0, paramValues);
            if (ToRoleId != 0)
            {
                strWhere += $" AND toroleid={ToRoleId} ";
            }
            int exitStatus = WebHelper.GetValueInt("exitStatus", -1, paramValues);
            if (exitStatus != -1)
            {
                strWhere += $" AND exitStatus={exitStatus} ";
            }
            string exitFund = WebHelper.GetValue("exitFund", string.Empty, paramValues);
            if (exitFund != string.Empty)
            { // TODO check
                strWhere += $" AND exitFund+',' like '%{exitFund},%' ";
            }

            string portfolioManager = WebHelper.GetValue("Creator", string.Empty, paramValues);
            if (portfolioManager != string.Empty)
            { 
                strWhere += $" AND pbi.portfolioManager like '%{portfolioManager}%' ";
            }

            string startDate = WebHelper.GetValue("startdate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(startDate))
            {
                strWhere += $"AND pe.createdDate>='{startDate}' ";
            }
         
            string endDate = WebHelper.GetValue("enddate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(endDate))
            {
                strWhere += $"AND pe.createdDate<'{Convert.ToDateTime(endDate).AddDays(1).ToString("yyyy-MM-dd")}' ";
            }

            string Name = WebHelper.GetValue("Name", string.Empty, paramValues);
            Name = Name.Replace("\'", string.Empty);     

            if (!string.IsNullOrWhiteSpace(Name))
            {
                if (searchNameOnly)
                {
                    strWhere += $@" AND pe.Name like '%{Name}%'  ";
                }
                else
                {
                    strWhere += $@" AND (pe.Name like '%{Name}%' OR exitManager like '%{Name}%' OR exitMember like '%{Name}%' OR exitContributor like '%{Name}%' OR exitFund+',' like '%{Name}%,' OR pbi.portfolioManager like '%{Name}%' OR pbi.postInvestManager like '%{Name}%' OR pe.creator like '%{Name}%'  OR pe.remark like '%{Name}%' ) ";
                }
            }

            sort = "Id Desc";
            return strWhere;
        }

        public AjaxResult GetPageList(NameValueCollection paramValues)
        {
            Member user = new MemberBLL().GetLogOnUser();
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10000, paramValues);
            int count;
            var PortfolioList = searchCommon(paramValues, user, pageIndex, pageSize, out count);


            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = PortfolioList;
            ajaxResult.count = count;
            return ajaxResult;
        }

        public bool isEditor(int id)
        {
            if (id == 0)
            {
                return false;
            }
            var ProjectModel = GetModel(id);
            Member user = new MemberBLL().GetLogOnUser();
            return ProjectModel.creator == user.RealName || MemberBLL.adminOrSuper(user);
        }
    }
}