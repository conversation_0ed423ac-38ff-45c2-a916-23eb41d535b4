﻿using Banyan.Apps;
using Banyan.Code;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Banyan.Domain
{
    public class ScoreService : BaseService
    {
        public static readonly ScoreBLL scoreBll = new ScoreBLL();
        private ExitScoreBLL exitScoreBll = new ExitScoreBLL();
        private ProjectScoreStageBLL stageBll = new ProjectScoreStageBLL();
        private ExitScoreStageBLL exitStageBll = new ExitScoreStageBLL();
        private ProjectScoreStage closeModel = new ProjectScoreStage(0);
        private ExitScoreStage exitCloseModel = new ExitScoreStage(0);
        /// <summary>
        /// 分页列表
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public object GetPageList(Member user)
        {
            int pageIndex = Utils.GetRequestInt("page", 1);
            int pageSize = Utils.GetRequestInt("limit", 10);
            int projectId = Utils.GetRequestInt("pjid", 0);

            string whereSql = string.Empty;
            if (projectId > 0)
            {
                whereSql += $"AND ProjectId={projectId} ";
            }

            var list = scoreBll.GetList(whereSql, pageSize, pageIndex, "*", "Id DESC");
            //if (list != null && list.Count() > 0)
            //{
            //    var userList = new MemberBLL().GetList($"Id in({string.Join(",", list.Select(x => x.UserId).Distinct().ToArray())})");
            //    foreach (var item in list)
            //    {
            //        item.UserName = userList.Where(x => x.Id == item.UserId)?.FirstOrDefault()?.RealName;
            //    }
            //}
            return JOk(list, "ok", scoreBll.GetCount(whereSql));
        }

        /// <summary>
        /// 获取用户评分记录
        /// </summary>
        /// <param name="user"></param>
        /// <param name="pid"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        public object GetUserToScore(Member user, int projectId)
        {
            if (user == null)
                return JFail("请先登录", (int)ResultCode.unlogin);

            if (user.Id <= 0 && projectId <= 0)
            {
                return JFail("参数错误", (int)ResultCode.paramerror);
            }

            string whereSql = $"SELECT Id,Score FROM ProjectScore WHERE UserId={user.Id} AND ProjectId={projectId} ";
            ProjectScore model = scoreBll.GetModelBySql(whereSql);
            return (model == null || model.Id == 0) ? -1 : model.Score;
        }
        public object ProlongScore(Member user, int stageid)
        {
            var scoreStageBll = new ProjectScoreStageBLL();
            var stage = scoreStageBll.GetModel(stageid);
            if(stage.State == 1)
            {
                return JFail("正在评分，无需延长！");
            } else
            {
                stage.State = 1;
                stage.AddTime = DateTime.Now;
                stage.EndTime = stage.EndTime.AddDays(2);
                scoreStageBll.Update(stage, "state,addTime,endTime");
                return JOk(stageid, "评分延长成功！");
            }
        }
        /// <summary>
        /// 添加评分
        /// </summary>
        /// <param name="user"></param>
        /// <param name="pid"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        public object ScoreSet(Member user, int projectId, int score)
        {
            int stageId = Utils.GetRequestInt("stageid", 0);
            if (user == null)
                return JFail("请先登录", (int)ResultCode.unlogin);

            if (user.Id <= 0 && projectId <= 0)
                return JFail("参数错误", (int)ResultCode.paramerror);

            ProjectScore model = new ProjectScore
            {
                ProjectId = projectId,
                UserId = user.Id,
                UserName = user.RealName,
                Score = score,
                StageId = stageId,
            };

            ProjectScoreStage stage = null;

            int nId = 0;
            string whereSql = $" UserId={user.Id} AND ProjectId={projectId}";
            if (stageId > 0)
            {
                whereSql += $" AND stageId={stageId}";
                stage = stageBll.GetModel(stageId);
                if (stage == null)
                {
                    return JFail("评分活动不存在");
                }
                else if (stage.State == 0)
                {
                    return JFail("评分已锁定，不能添加或修改");
                }
            }
            ProjectScore pscore = scoreBll.GetModel(whereSql);
            if (pscore != null && pscore.Id > 0)
            {
                model.Id = pscore.Id;
                nId = scoreBll.Update(model) ? model.Id : 0;
            }
            else
            {
                nId = Convert.ToInt32(scoreBll.Add(model));
            }

            if (nId > 0)
                new ProjectBLL().UpdateScoreAvg(projectId); // not used
            if (nId > 0 && stageId > 0)
                stageBll.UpdateScoreStage(projectId, stageId, user.RealName, score);

            return nId > 0 ? JOk(nId, "ok") : JFail("fail");
        }

        public object ExitScoreSet(Member user, int exitId, int score)
        {
            int stageId = Utils.GetRequestInt("stageid", 0);
            if (user == null)
                return JFail("请先登录", (int)ResultCode.unlogin);

            if (user.Id <= 0 && exitId <= 0)
                return JFail("参数错误", (int)ResultCode.paramerror);

            ExitScore model = new ExitScore
            {
                exitID = exitId,
                UserId = user.Id,
                UserName = user.RealName,
                Score = score,
                StageId = stageId,
            };

            ExitScoreStage stage = null;

            int nId = 0;
            string whereSql = $" UserId={user.Id} AND exitId={exitId}";
            if (stageId > 0)
            {
                whereSql += $" AND stageId={stageId}";
                stage = exitStageBll.GetModel(stageId);
                if (stage == null)
                {
                    return JFail("评分活动不存在");
                }
                else if (stage.State == 0)
                {
                    return JFail("评分已锁定，不能添加或修改");
                }
            }
            ExitScore pscore = exitScoreBll.GetModel(whereSql);
            if (pscore != null && pscore.Id > 0)
            {
                model.Id = pscore.Id;
                nId = exitScoreBll.Update(model) ? model.Id : 0;
            }
            else
            {
                nId = Convert.ToInt32(exitScoreBll.Add(model));
            }

            if (nId > 0 && stageId > 0)
                exitStageBll.UpdateScoreStage(exitId, stageId, user.RealName, score);

            return nId > 0 ? JOk(nId, "ok") : JFail("fail");
        }

        /// <summary>
        /// 获取评分
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public object GetScores()
        {
            int projectId = Utils.GetRequestInt("pjid", 0);
            int pageIndex = Utils.GetRequestInt("page", 1);
            int pageSize = Utils.GetRequestInt("limit", int.MaxValue);
            int stageId = Utils.GetRequestInt("stageId", 0);
            int uid = Utils.GetRequestInt("uid", 0);

            if (projectId < 0 || stageId < 0)
                return JFail("参数错误");
            string search = $" ProjectId={projectId} ";
            if (stageId > 0)
                search += $" and stageId={stageId} ";
            List<ProjectScore> list = scoreBll.GetList(search, pageSize, pageIndex, "*", "AddTime DESC");
            object data = null;
            if (list != null && list.Count() > 0)
            {
                List<Member> userAvatars = new MemberBLL().GetList($"{string.Join(",", list.Select(x => x.UserId).ToArray())}"); //查询用户头像信息
                if (uid == 86 || uid == 87 || uid == 88)
                { // 合伙人，自己未打分不能查看其他合伙人分数

                    var recentScore = scoreBll.GetListBySql($"select* from ProjectScore ps2 join (select  max(StageId) as sid from ProjectScore ps where ProjectId = {projectId}) as p on StageId = sid");
                    var recentScoreBySelf = recentScore.Where(val => val.UserId == uid).ToList();
                    if (recentScoreBySelf.Count() == 0 && recentScore.Count() > 0)
                    {
                        var recentStageId = recentScore[0].StageId;
                        if (uid == 86)
                        {
                            list = list.Where(val => (val.UserId != 87 && val.UserId != 88) || val.StageId != recentStageId).ToList();
                        }
                        else if (uid == 87)
                        {
                            list = list.Where(val => (val.UserId != 86 && val.UserId != 88) || val.StageId != recentStageId).ToList();
                        }
                        else if (uid == 88)
                        {
                            list = list.Where(val => (val.UserId != 86 && val.UserId != 87) || val.StageId != recentStageId).ToList();
                        }
                        //else if (uid == 319)
                        //{
                        //    list = list.Where(val => (val.UserId != 86 && val.UserId != 88 && val.UserId != 87 ) ||  val.StageId != recentStageId).ToList();
                        //}
                    }
                }

                data = list.Select(item =>
                {
                    var user = userAvatars.Where(x => x.Id == item.UserId)?.FirstOrDefault();
                    return new
                    {
                        item.Id,
                        item.Score,
                        item.ProjectId,
                        item.UserId,
                        item.StageId,
                        item.UserName,
                        //UserName = user?.RealName,
                        UserAvatar = user?.Avatar,
                        AddTime = item.AddTime.ToString("yyyy-MM-dd HH:mm"),
                    };
                });
            }
            return JOk(data);
        }
        public object GetExitScores()
        {
            int exitId = Utils.GetRequestInt("pjid", 0);
            int pageIndex = Utils.GetRequestInt("page", 1);
            int pageSize = Utils.GetRequestInt("limit", int.MaxValue);
            int stageId = Utils.GetRequestInt("stageId", 0);
            int uid = Utils.GetRequestInt("uid", 0);

            if (exitId < 0 || stageId < 0)
                return JFail("参数错误");
            string search = $" exitId={exitId} ";
            if (stageId > 0)
                search += $" and stageId={stageId} ";
            List<ExitScore> list = exitScoreBll.GetList(search, pageSize, pageIndex, "*", "AddTime DESC");
            object data = null;
            if (list != null && list.Count() > 0)
            {
                List<Member> userAvatars = new MemberBLL().GetList($"{string.Join(",", list.Select(x => x.UserId).ToArray())}"); //查询用户头像信息
                if (uid == 86 || uid == 87 || uid == 88)
                { // 合伙人，自己未打分不能查看其他合伙人分数

                    var recentScore = exitScoreBll.GetListBySql($"select * from ExitScore ps2 join (select  max(StageId) as sid from ExitScore ps where ExitId = {exitId}) as p on StageId = sid");
                    var recentScoreBySelf = recentScore.Where(val => val.UserId == uid).ToList();
                    if (recentScoreBySelf.Count() == 0 && recentScore.Count() > 0)
                    {
                        var recentStageId = recentScore[0].StageId;
                        if (uid == 86)
                        {
                            list = list.Where(val => (val.UserId != 87 && val.UserId != 88) || val.StageId != recentStageId).ToList();
                        }
                        else if (uid == 87)
                        {
                            list = list.Where(val => (val.UserId != 86 && val.UserId != 88) || val.StageId != recentStageId).ToList();
                        }
                        else if (uid == 88)
                        {
                            list = list.Where(val => (val.UserId != 86 && val.UserId != 87) || val.StageId != recentStageId).ToList();
                        }
                        //else if (uid == 319)
                        //{
                        //    list = list.Where(val => (val.UserId != 86 && val.UserId != 88 && val.UserId != 87 ) ||  val.StageId != recentStageId).ToList();
                        //}
                    }
                }

                data = list.Select(item =>
                {
                    var user = userAvatars.Where(x => x.Id == item.UserId)?.FirstOrDefault();
                    return new
                    {
                        item.Id,
                        item.Score,
                        item.exitID,
                        item.UserId,
                        item.StageId,
                        item.UserName,
                        //UserName = user?.RealName,
                        UserAvatar = user?.Avatar,
                        AddTime = item.AddTime.ToString("yyyy-MM-dd HH:mm"),
                    };
                });
            }
            return JOk(data);
        }

        /// <summary>
        /// 正在进行的评分活动
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        public object GetInScoreStage(int projectId)
        {
            ProjectScoreStage model = stageBll.GetModel($"projectId={projectId} AND State=1");
            if (model == null)
                return model;

            return new
            {
                model.Id,
                model.ActName,
                model.ProjectManagerScore,
                model.Average,
            };
        }

        public object StageScores(int stageId)
        {
            List<ProjectScore> list = new ProjectScoreBLL().GetList($"StageId={stageId}");
            MemberBLL memberBll = new MemberBLL();
            object data = list.Select(item =>
            {
                Member member = memberBll.GetModelByCache(item.UserId);
                return new
                {
                    item.Id,
                    item.Score,
                    member.RealName,
                    AddTime = item.AddTime.ToString("yyyy-MM-dd HH:mm:ss"),
                };
            });
            return JOk(data);
        }
        public object ExitStageScores(int stageId)
        {
            List<ExitScore> list = new ExitScoreBLL().GetList($"StageId={stageId}");
            MemberBLL memberBll = new MemberBLL();
            object data = list.Select(item =>
            {
                Member member = memberBll.GetModelByCache(item.UserId);
                return new
                {
                    item.Id,
                    item.Score,
                    member.RealName,
                    AddTime = item.AddTime.ToString("yyyy-MM-dd HH:mm:ss"),
                };
            });
            return JOk(data);
        }
        public void checkStageClose()
        {
            var time = DateTime.Now.AddDays(-5).ToString("yyyy-MM-dd HH:mm:ss");
            
            stageBll.Update(closeModel, "State", $" AddTime<'{time}' AND State=1 ");
            exitStageBll.Update(exitCloseModel, "State", $" AddTime<'{time}' AND State=1 ");
        }

        public object SetScoreStage(Member user, int projectId, int state, string description, bool syncRecentScore)
        {
            Project project = new ProjectBLL().GetModelByCache(projectId);
            if (project == null)
                return JFail("项目信息不存在");

            ProjectScoreStage model = new ProjectScoreStage
            {
                ActName = DateTime.Now.ToString("yyyyMMdd"),
                ProjectId = projectId,
                State = 1,
                Description = description,
            };

            ProjectScoreStage stagein = stageBll.GetModel($"state=1 and projectid={projectId}");
            if (stagein != null && state == 0)
            {
                stagein.State = 0;
                stagein.EndTime = DateTime.Now;
                return stageBll.Update(stagein, "State, EndTime") ? JOk(stagein,"锁定成功") : JFail("锁定失败");
            }
            int modelId = 0;
            if (syncRecentScore && state != 0) //开启评分, 同步最近评分
            {
                try
                {
                    var list = stageBll.GetList($" projectid={projectId}", int.MaxValue, 1, "*", "AddTime Desc");
                    modelId = Convert.ToInt32(stageBll.Add(model));
                    model.Id = modelId;

                    if (list.Count != 0)
                    {
                        var lastStage = list[0];
                        var stageId = lastStage.Id;
                        var scores = scoreBll.GetList($"stageId={stageId}");
                        foreach (var score in scores)
                        {
                            score.AddTime = DateTime.Now;
                            score.StageId = modelId;
                            scoreBll.Add(score);
                        }
                        model.Average = lastStage.Average;
                        model.ProjectManagerScore = lastStage.ProjectManagerScore;
                        model.People = lastStage.People;
                        stageBll.Update(model, "people,average,projectmanagerscore");
                    }
                }
                catch (Exception e)
                {
                    Logger.Error(e.Message);
                }
            } else
            {
                modelId = Convert.ToInt32(stageBll.Add(model));
            }
           
            return modelId > 0 ? JOk(model, "创建成功") : JFail("创建失败");
        }

        public object SetExitScoreStage(Member user, int exitId, int state)
        {
            PortfolioExit portfolioExit = new PortfolioExitBLL().GetModel(exitId);
            if (portfolioExit == null)
                return JFail("项目信息不存在");

            ExitScoreStage model = new ExitScoreStage
            {
                ActName = DateTime.Now.ToString("yyyyMMdd"),
                exitID = exitId,
                State = 1,
            };

            ExitScoreStage stagein = exitStageBll.GetModel($"state=1 and exitID={exitId}");
            if (stagein != null && state == 0)
            {
                stagein.State = 0;
                stagein.EndTime = DateTime.Now;
                var res = exitStageBll.Update(stagein, "State, EndTime");
                if(res)
                {
                    if (stagein.Average >= 6) {
                        new PortfolioExitBLL().addFinancialViewer(model.exitID);
                    }
                    return JOk(stagein, "锁定成功");
                } else
                {
                    return JFail("锁定失败");
                }
            }
            int modelId = Convert.ToInt32(exitStageBll.Add(model));

            return modelId > 0 ? JOk(model, "创建成功") : JFail("创建失败");
        }

        public object GetRecentlyStage(Member user, int projectId)
        {
            string strSql = $"select * from ProjectScoreStage where id=(select MAX(id) from ProjectScoreStage where projectId={projectId})";
            ProjectScoreStage stage = new ProjectScoreStageBLL().GetModelBySql(strSql);
            if (stage == null || stage.Id == 0)
            {
                return JOk(null);
            }

            int roleId = new ProjectBLL().GetProjectRoleId(projectId);
            ProjectScore score = new ProjectScoreBLL().GetModel($"UserId={user.Id} AND ProjectId={projectId} and StageId={stage.Id}");
            return JOk(new
            {
                scoreStage = new
                {
                    stage.Id,
                    stage.ActName,
                    stage.Description,
                    stage.AddTime,
                    stage.EndTime,
                    stage.Average,
                    stage.State,
                    stage.ProjectId,
                    stage.People,
                    stage.ProjectManagerScore,
                    // 合伙人可能变动，所以不直接保存
                    PartnerScoreAvg = CalPartnerScoreAvg(stage.ProjectId, stage.Id, roleId),
                    MedicalScoreAvg = CalMedicalScoreAvg(stage.ProjectId, stage.Id, roleId),
                    DDScoreAvg = CalDDScoreAvg(stage.ProjectId, stage.Id, roleId),
                    InvestScoreAvg = CalInvestScoreAvg(stage.ProjectId, stage.Id, roleId),
                },
                score = score == null ? -1 : score.Score,
            });
        }

        public object GetRecentlyExitStage(Member user, int exitId)
        {
            string strSql = $"select * from ExitScoreStage where id=(select MAX(id) from ExitScoreStage where exitId={exitId})";
            ExitScoreStage stage = new ExitScoreStageBLL().GetModelBySql(strSql);
            if (stage == null || stage.Id == 0)
            {
                return JOk(null);
            }

            var score = new ExitScoreBLL().GetModel($"UserId={user.Id} AND exitId={exitId} and StageId={stage.Id}");
            return JOk(new
            {
                scoreStage = new
                {
                    stage.Id,
                    stage.ActName,
                    stage.Description,
                    stage.AddTime,
                    stage.EndTime,
                    stage.Average,
                    stage.State,
                    stage.exitID,
                    stage.People,
                    // 合伙人可能变动，所以不直接保存
                    PartnerScoreAvg = CalPartnerExitScoreAvg(stage.exitID, stage.Id)
                },
                score = score == null ? -1 : score.Score,
            });
        }
        public object GetStageList(Member user, int projectId)
        {
            try
            {
                string strSql = $"select * from ProjectScoreStage where projectId={projectId} order by AddTime desc";
                var ProjectScoreStageList = new List<ProjectScoreStage>();
                ProjectScoreStageList = stageBll.GetListBySql(strSql);
                if (ProjectScoreStageList == null || ProjectScoreStageList.Count == 0)
                {
                    return JOk(null);
                }
                int roleId = new ProjectBLL().GetProjectRoleId(projectId);
                return JOk(new
                {
                    scoreStageList = ProjectScoreStageList.Select(item =>
                    {
                        return new { item.Id, item.ActName, item.Description, item.AddTime, EndTime = item.EndTime.ToString("yyyy-MM-dd"), item.Average, item.State, item.ProjectId, item.People, item.ProjectManagerScore, PartnerScoreAvg = CalPartnerScoreAvg(item.ProjectId, item.Id, roleId), MedicalScoreAvg = CalMedicalScoreAvg(item.ProjectId, item.Id, roleId), DDScoreAvg = CalDDScoreAvg(item.ProjectId, item.Id, roleId), InvestScoreAvg = CalInvestScoreAvg(item.ProjectId, item.Id, roleId) };
                    }),
                });
            }
            catch (Exception ex)
            {
                Logger.Error(ex.Message, ex);
                return JOk(null);
            }
        }

        public object GetExitStageList(Member user, int exitId)
        {
            try
            {
                string strSql = $"select * from ExitScoreStage where exitId={exitId} order by AddTime desc";
                var ExitScoreStageList = new List<ExitScoreStage>();
                ExitScoreStageList = exitStageBll.GetListBySql(strSql);
                if (ExitScoreStageList == null || ExitScoreStageList.Count == 0)
                {
                    return JOk(null);
                }
                return JOk(new
                {
                    exitScoreStageList = ExitScoreStageList.Select(item =>
                    {
                        return new { item.Id, item.ActName, item.Description, item.AddTime, EndTime = item.EndTime.ToString("yyyy-MM-dd"), item.Average, item.State, item.exitID, item.People,PartnerScoreAvg = CalPartnerExitScoreAvg(item.exitID, item.Id) };
                    }),
                });
            }
            catch (Exception ex)
            {
                Logger.Error(ex.Message, ex);
                return JOk(null);
            }
        }
        /// <summary>
        /// 计算合伙人平均分
        /// </summary>
        /// <returns></returns>
        public decimal CalPartnerScoreAvg(int projectId, int stageId, int roleId = -1)
        {
            string users = "UserId=86 OR UserId=87 OR UserId=88";
            string search = $"SELECT COUNT(*) AS UserId, round(AVG(score), 1) AS Score FROM ProjectScore WHERE ({users}) and projectId = {projectId} and StageId={stageId}";
            if (roleId == 1 || roleId == 3)
            {
                search = $"SELECT COUNT(*) AS UserId, round(AVG(score), 1) AS Score FROM ProjectScore WHERE (({users}) or  (UserId = 53 and AddTime >= '06/21/2020') ) and projectId = {projectId} and StageId={stageId}";
            }
         
            ProjectScore model = scoreBll.GetModelBySql(search);
            if (model == null)
                return 0;

            return model.Score;
        }

        public decimal CalPartnerExitScoreAvg(int exitId, int stageId)
        {
            string users = "UserId=86 OR UserId=87 OR UserId=88";
            string search = $"SELECT COUNT(*) AS UserId, round(AVG(score), 1) AS Score FROM ExitScore WHERE ({users}) and exitId = {exitId} and StageId={stageId}";

            ExitScore model = exitScoreBll.GetModelBySql(search);
            if (model == null)
                return 0;

            return model.Score;
        }

        public decimal CalMedicalScoreAvg(int projectId, int stageId, int roleId = -1)
        {
            string users = "UserId=86 OR UserId=87 OR UserId=88";
            string search;
            if (roleId == 5)
            {// 杨昆、张总、外部两人
                search = $"SELECT COUNT(*) AS UserId, round(AVG(score), 1) AS Score FROM ProjectScore WHERE (( ({users}) and AddTime < '05/30/2022') or  ((userId=86 or UserId=3126 or UserId=3767 or UserId=3766)  and AddTime >= '05/30/2022') ) and projectId = {projectId} and StageId={stageId}";
            } else
            {
                return 0;
            }

            ProjectScore model = scoreBll.GetModelBySql(search);
            if (model == null)
                return 0;

            return model.Score;
        }

        public decimal CalDDScoreAvg(int projectId, int stageId, int roleId = -1)
        {
            if(DateTime.Now < Convert.ToDateTime("2024-01-15"))
            {
               return 0;
            }
            string users = "UserId=339 OR UserId=3724 OR UserId=3450 OR UserId=4042";
            string search = $"SELECT COUNT(*) AS UserId, round(AVG(score), 1) AS Score FROM ProjectScore WHERE  ({users}) and projectId = {projectId} and StageId={stageId}";
            

            ProjectScore model = scoreBll.GetModelBySql(search);
            if (model == null)
                return 0;

            return model.Score;
        }

        public decimal CalInvestScoreAvg(int projectId, int stageId, int roleId = -1)
        {
            if (DateTime.Now < Convert.ToDateTime("2024-01-15"))
            {
               return 0;
            }
            string users = "UserId!=339 AND UserId!=3724 AND UserId!=3450 AND UserId!=4042";
            string search = $"SELECT COUNT(*) AS UserId, round(AVG(score), 1) AS Score FROM ProjectScore WHERE  ({users}) and projectId = {projectId} and StageId={stageId}";


            ProjectScore model = scoreBll.GetModelBySql(search);
            if (model == null)
                return 0;

            return model.Score;
        }
    }
}
