<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute">
            <summary>
            テスト配置ごとに配置項目 (ファイルまたはディレクトリ) を指定するために使用されます。
            テスト クラスまたはテスト メソッドで指定できます。
            属性に複数のインスタンスを指定して、2 つ以上の項目を指定することができます。
            項目のパスには絶対パスまたは相対パスを指定できます。相対パスの場合は、RunConfig.RelativePathRoot からの相対パスです。
            </summary>
            <example>
            [DeploymentItem("file1.xml")]
            [DeploymentItem("file2.xml", "DataFiles")]
            [DeploymentItem("bin\Debug")]
            </example>
            <remarks>
            DeploymentItemAttribute is currently not supported in .Net Core. This is just a placehodler for support in the future.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/> クラスの新しいインスタンスを初期化します。
            </summary>
            <param name="path">配置するファイルまたはディレクトリ。パスはビルドの出力ディレクトリの相対パスです。項目は配置されたテスト アセンブリと同じディレクトリにコピーされます。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String,System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/> クラスの新しいインスタンスを初期化する
            </summary>
            <param name="path">配置するファイルまたはディレクトリへの相対パスまたは絶対パス。パスはビルドの出力ディレクトリの相対パスです。項目は配置されたテスト アセンブリと同じディレクトリにコピーされます。</param>
            <param name="outputDirectory">アイテムのコピー先のディレクトリのパス。配置ディレクトリへの絶対パスまたは相対パスのいずれかを指定できます。次で識別されるすべてのファイルとディレクトリは <paramref name="path"/> このディレクトリにコピーされます。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.Path">
            <summary>
            コピーするソース ファイルまたはフォルダーのパスを取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.OutputDirectory">
            <summary>
            項目のコピー先のディレクトリのパスを取得します。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext">
            <summary>
            TestContext クラス。このクラスは完全に抽象的でなければならず、かつメンバー
            を含んでいてはなりません。アダプターはメンバーを実装します。フレームワーク内のユーザーは
            正しく定義されたインターフェイスを介してのみこのクラスにアクセスする必要があります。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.Properties">
            <summary>
            テストのテスト プロパティを取得します。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.FullyQualifiedTestClassName">
            <summary>
            現在実行中のテスト メソッドを含むクラスの完全修飾名を取得する
            </summary>
            <remarks>
            This property can be useful in attributes derived from ExpectedExceptionBaseAttribute.
            Those attributes have access to the test context, and provide messages that are included
            in the test results. Users can benefit from messages that include the fully-qualified
            class name in addition to the name of the test method currently being executed.
            </remarks>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestName">
            <summary>
            現在実行中のテスト メソッドの名前を取得する
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.CurrentTestOutcome">
            <summary>
            現在のテスト成果を取得します。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String)">
            <summary>
            Used to write trace messages while the test is running
            </summary>
            <param name="message">formatted message string</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String,System.Object[])">
            <summary>
            Used to write trace messages while the test is running
            </summary>
            <param name="format">format string</param>
            <param name="args">the arguments</param>
        </member>
    </members>
</doc>
