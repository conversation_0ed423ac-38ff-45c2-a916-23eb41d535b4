using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Banyan.Code;
using Banyan.Domain;

namespace Banyan.Apps
{
    /// <summary>
    /// Service for generating and sending weekly email digests with personalized news recommendations
    /// </summary>
    public class EmailDigestService
    {
        private readonly NewsRecommendationEngine _recommendationEngine;
        private readonly EmailDigestRecordsBLL _emailDigestRecordsBLL;
        private readonly MemberBLL _memberBLL;
        private readonly NewsBLL _newsBLL;
        private readonly ICache _cache;
        private readonly int _defaultNewsCount = 10;
        private readonly string _emailSender = "<EMAIL>";
        private readonly string _emailSenderName = "高榕创投新闻推荐";

        /// <summary>
        /// Constructor
        /// </summary>
        public EmailDigestService()
        {
            _recommendationEngine = new NewsRecommendationEngine();
            _emailDigestRecordsBLL = new EmailDigestRecordsBLL();
            _memberBLL = new MemberBLL();
            _newsBLL = new NewsBLL();
            _cache = CacheFactory.Cache();
        }

        /// <summary>
        /// Generates and sends weekly digest emails to all users
        /// </summary>
        /// <returns>Number of emails sent successfully</returns>
        public async Task<int> GenerateAndSendWeeklyDigestAsync()
        {
            try
            {
                Logger.Info("Starting weekly email digest generation for all users");
                
                // Get all active users - 使用带缓存的后台任务专用方法
                var users = _memberBLL.GetAllListForBackgroundTask(false).Where(u => u.Status == (int)MemberStatus.enable).ToList();
                Logger.Info($"Found {users.Count} active users for email digest");
                
                int successCount = 0;
                
                // Process each user
                foreach (var user in users)
                {
                    try
                    {
                        // Skip users without email
                        if (string.IsNullOrEmpty(user.Mail))
                        {
                            Logger.Warn($"Skipping user {user.RealName} (ID: {user.Id}) - no email address");
                            continue;
                        }
                        
                        // Generate and send digest for this user
                        bool success = await GenerateAndSendDigestForUserAsync(user.Id, user.RealName, user.Mail);
                        
                        if (success)
                        {
                            successCount++;
                            Logger.Info($"Successfully sent digest to user {user.RealName} ({user.Mail})");
                        }
                        else
                        {
                            Logger.Warn($"Failed to send digest to user {user.RealName} ({user.Mail})");
                        }
                        
                        // Add a small delay to avoid overwhelming the email server
                        await Task.Delay(100);
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"Error processing digest for user {user.RealName} (ID: {user.Id}): {ex.Message}", ex);
                        // Continue with next user
                    }
                }
                
                Logger.Info($"Completed weekly email digest generation. Sent {successCount} of {users.Count} emails successfully");
                return successCount;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error generating weekly email digests: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Generates and sends a digest email for a specific user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="userName">User name</param>
        /// <param name="userEmail">User email address</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> GenerateAndSendDigestForUserAsync(int userId, string userName, string userEmail)
        {
            try
            {
                Logger.Info($"Generating email digest for user {userName} (ID: {userId})");
                
                // Get personalized recommendations for this user
                var recommendations = await GetRecommendationsForDigestAsync(userId);
                
                if (recommendations == null || recommendations.Count == 0)
                {
                    Logger.Warn($"No recommendations available for user {userName} (ID: {userId})");
                    return false;
                }
                
                // Generate email content
                string emailSubject = $"您的每周新闻推荐 - {DateTime.Now:yyyy-MM-dd}";
                string emailContent = await GenerateDigestEmailContentAsync(userId, userName, recommendations);
                
                // Send the email
                bool success = SendDigestEmail(userEmail, userName, emailSubject, emailContent);
                
                if (success)
                {
                    // Record the sent digest
                    string newsIds = string.Join(",", recommendations.Select(n => n.Id));
                    await _emailDigestRecordsBLL.RecordEmailDigestAsync(userId, recommendations.Count, newsIds);
                    
                    Logger.Info($"Successfully sent and recorded digest for user {userName} (ID: {userId})");
                }
                
                return success;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error generating digest for user {userId}: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// Gets recommendations for a user's digest
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of recommended news</returns>
        private async Task<List<News>> GetRecommendationsForDigestAsync(int userId)
        {
            try
            {
                // Get recommendations from the past week
                var startDate = DateTime.Now.AddDays(-7);
                var endDate = DateTime.Now;
                
                // Create filters for the past week
                var filters = new NewsSearchFilters
                {
                    StartDate = startDate,
                    EndDate = endDate
                };
                
                // Try to get personalized recommendations first
                var recommendations = await _recommendationEngine.GetPersonalizedRecommendationsAsync(
                    userId, _defaultNewsCount, 0.3, filters);
                
                // If we don't have enough personalized recommendations, add some popular news
                if (recommendations.Count < _defaultNewsCount)
                {
                    int remainingCount = _defaultNewsCount - recommendations.Count;
                    
                    // Exclude news IDs we already have
                    var excludeIds = recommendations.Select(n => n.Id).ToList();
                    filters.ExcludeNewsIds = excludeIds;
                    
                    // Get popular news
                    var popularNews = await _recommendationEngine.GetPopularNewsAsync(remainingCount, filters);
                    
                    // Add to our recommendations
                    recommendations.AddRange(popularNews);
                }
                
                return recommendations;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting recommendations for digest (user {userId}): {ex.Message}", ex);
                
                // Fallback to popular news
                return await _recommendationEngine.GetPopularNewsAsync(_defaultNewsCount);
            }
        }

        /// <summary>
        /// Generates the HTML content for a digest email
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="userName">User name</param>
        /// <param name="recommendations">List of recommended news</param>
        /// <returns>HTML email content</returns>
        private async Task<string> GenerateDigestEmailContentAsync(int userId, string userName, List<News> recommendations)
        {
            try
            {
                // Read the email template
                string templatePath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "EmailTemplates", "WeeklyDigestTemplate.html");
                string newsItemTemplatePath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "EmailTemplates", "NewsItemTemplate.html");
                
                string emailTemplate;
                string newsItemTemplate;
                
                // Read templates from files if they exist, otherwise use embedded templates
                if (System.IO.File.Exists(templatePath))
                {
                    using (var reader = new StreamReader(templatePath))
                    {
                        emailTemplate = await Task.Run(() => reader.ReadToEnd());
                    }
                }
                else
                {
                    emailTemplate = GetEmbeddedEmailTemplate();
                }
                
                if (System.IO.File.Exists(newsItemTemplatePath))
                {
                    using (var reader = new StreamReader(newsItemTemplatePath))
                    {
                        newsItemTemplate = await Task.Run(() => reader.ReadToEnd());
                    }
                }
                else
                {
                    newsItemTemplate = GetEmbeddedNewsItemTemplate();
                }
                
                // Replace user name and current year
                emailTemplate = emailTemplate.Replace("{UserName}", userName);
                emailTemplate = emailTemplate.Replace("{CurrentYear}", DateTime.Now.Year.ToString());
                
                // Generate news items HTML
                var newsItemsHtml = new StringBuilder();
                
                foreach (var news in recommendations)
                {
                    // Generate tracking URL
                    string trackingUrl = GenerateTrackingUrl(userId, userName, news.Id, "email");
                    
                    // Format date
                    string formattedDate = news.PubTime.ToString("yyyy-MM-dd");
                    
                    // Create summary (truncate content if needed)
                    string summary = news.Content;
                    if (summary != null && summary.Length > 150)
                    {
                        summary = summary.Substring(0, 147) + "...";
                    }
                    else if (summary == null)
                    {
                        summary = "无内容摘要";
                    }
                    
                    // Format tags if available
                    string tagsHtml = "";
                    if (!string.IsNullOrEmpty(news.Tag))
                    {
                        var tags = news.Tag.Split(new[] { ',', '，', ';', '；', ' ' }, StringSplitOptions.RemoveEmptyEntries);
                        if (tags.Length > 0)
                        {
                            var tagBuilder = new StringBuilder("<div class=\"news-tags\">");
                            foreach (var tag in tags.Take(3)) // Limit to 3 tags
                            {
                                tagBuilder.Append($"<span class=\"news-tag\">{tag}</span>");
                            }
                            tagBuilder.Append("</div>");
                            tagsHtml = tagBuilder.ToString();
                        }
                    }
                    
                    // Format similarity score if available
                    string similarityScoreHtml = "";
                    if (news.MatchScore > 0)
                    {
                        int scorePercentage = (int)(news.MatchScore * 100);
                        similarityScoreHtml = $"<span class=\"similarity-score\">匹配度: {scorePercentage}%</span>";
                    }
                    
                    // Replace placeholders in news item template
                    string newsItemHtml = newsItemTemplate
                        .Replace("{Title}", HttpUtility.HtmlEncode(news.Title))
                        .Replace("{Source}", HttpUtility.HtmlEncode(news.Source ?? "未知来源"))
                        .Replace("{PublishDate}", formattedDate)
                        .Replace("{Summary}", HttpUtility.HtmlEncode(summary))
                        .Replace("{TrackingUrl}", trackingUrl)
                        .Replace("{Tags}", tagsHtml)
                        .Replace("{SimilarityScore}", similarityScoreHtml);
                    
                    newsItemsHtml.AppendLine(newsItemHtml);
                }
                
                // Replace news items placeholder in the main template
                emailTemplate = emailTemplate.Replace("<!-- NEWS_ITEMS_PLACEHOLDER -->", newsItemsHtml.ToString());
                
                return emailTemplate;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error generating email content for user {userId}: {ex.Message}", ex);
                throw;
            }
        }
        
        /// <summary>
        /// Gets the embedded email template as a fallback if the file doesn't exist
        /// </summary>
        /// <returns>Email template HTML</returns>
        private string GetEmbeddedEmailTemplate()
        {
            return @"<!DOCTYPE html>
<html>
<head>
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>每周新闻推荐</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        h2 {
            color: #3498db;
            margin-top: 20px;
        }
        .news-item {
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .news-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .news-meta {
            font-size: 12px;
            color: #7f8c8d;
            margin-bottom: 10px;
        }
        .news-summary {
            font-size: 14px;
            margin-bottom: 10px;
        }
        .news-link {
            display: inline-block;
            color: #3498db;
            text-decoration: none;
            font-weight: bold;
        }
        .news-link:hover {
            text-decoration: underline;
        }
        .news-tag {
            display: inline-block;
            background-color: #f1f1f1;
            padding: 2px 6px;
            margin-right: 5px;
            border-radius: 3px;
            font-size: 12px;
            color: #555;
        }
        .footer {
            margin-top: 30px;
            font-size: 12px;
            color: #7f8c8d;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
        .similarity-score {
            display: inline-block;
            background-color: #2ecc71;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            margin-left: 5px;
        }
    </style>
</head>
<body>
    <h1>您好，{UserName}！</h1>
    <p>以下是本周为您精选的新闻内容，希望对您有所帮助。</p>
    
    <div class=""recommendations"">
        <!-- NEWS_ITEMS_PLACEHOLDER -->
    </div>
    
    <div class=""footer"">
        <p>此邮件由系统自动发送，请勿直接回复。</p>
        <p>如需取消订阅或调整邮件接收频率，请联系管理员。</p>
        <p>&copy; {CurrentYear} 高榕创投. 保留所有权利.</p>
    </div>
</body>
</html>";
        }
        
        /// <summary>
        /// Gets the embedded news item template as a fallback if the file doesn't exist
        /// </summary>
        /// <returns>News item template HTML</returns>
        private string GetEmbeddedNewsItemTemplate()
        {
            return @"<div class=""news-item"">
    <div class=""news-title"">{Title}</div>
    <div class=""news-meta"">
        来源: {Source} | 发布日期: {PublishDate}
        {SimilarityScore}
    </div>
    
    {Tags}
    
    <div class=""news-summary"">{Summary}</div>
    
    <a href=""{TrackingUrl}"" class=""news-link"">阅读全文 &raquo;</a>
</div>";
        }

        /// <summary>
        /// Generates a tracking URL for a news item
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="userName">User name</param>
        /// <param name="newsId">News ID</param>
        /// <param name="source">Source of the click (email)</param>
        /// <returns>Tracking URL</returns>
        private string GenerateTrackingUrl(int userId, string userName, int newsId, string source)
        {
            // Use the recommendation controller to properly track email clicks
            string baseUrl = "https://ims.gaorongvc.com/Recommendation/ViewNews";

            // Add tracking parameters
            string trackingUrl = $"{baseUrl}/{newsId}?source={source}&utm_medium=email&utm_campaign=weekly_digest";
            trackingUrl += $"&uid={userId}&uname={HttpUtility.UrlEncode(userName)}";

            return trackingUrl;
        }

        /// <summary>
        /// Sends an email digest to a user using the Banyan.Code.Mail utility
        /// </summary>
        /// <param name="toEmail">Recipient email address</param>
        /// <param name="toName">Recipient name</param>
        /// <param name="subject">Email subject</param>
        /// <param name="htmlContent">HTML email content</param>
        /// <returns>True if successful, false otherwise</returns>
        private bool SendDigestEmail(string toEmail, string toName, string subject, string htmlContent)
        {
            const int maxRetries = 3;
            int retryCount = 0;
            TimeSpan retryDelay = TimeSpan.FromSeconds(2);
            
            while (retryCount < maxRetries)
            {
                try
                {
                    Logger.Info($"Sending email digest to {toName} ({toEmail}), attempt {retryCount + 1} of {maxRetries}");
                    
                    // Convert HTML to plain text for email clients that don't support HTML
                    string plainTextContent = ConvertHtmlToPlainText(htmlContent);
                    
                    // Use Mail.botSendMailToAdmin to send the email
                    // Note: This method only supports plain text, so we'll lose HTML formatting
                    bool success = Mail.botSendMailToAdmin(subject, plainTextContent, toEmail);
                    
                    if (success)
                    {
                        Logger.Info($"Successfully sent email digest to {toName} ({toEmail})");
                        return true;
                    }
                    else
                    {
                        throw new Exception("Mail.botSendMailToAdmin returned false");
                    }
                }
                catch (Exception ex)
                {
                    retryCount++;
                    
                    if (retryCount >= maxRetries)
                    {
                        Logger.Error($"Failed to send email digest to {toName} ({toEmail}) after {maxRetries} attempts: {ex.Message}", ex);
                        return false;
                    }
                    
                    Logger.Warn($"Error sending email digest to {toName} ({toEmail}), attempt {retryCount} of {maxRetries}: {ex.Message}. Retrying in {retryDelay.TotalSeconds} seconds...");
                    
                    // Wait before retrying
                    System.Threading.Thread.Sleep(retryDelay);
                    
                    // Increase delay for next retry (exponential backoff)
                    retryDelay = TimeSpan.FromMilliseconds(retryDelay.TotalMilliseconds * 2);
                }
            }
            
            // This should never be reached due to the return statements above, but added for completeness
            return false;
        }
        
        /// <summary>
        /// Converts HTML content to plain text for email clients that don't support HTML
        /// </summary>
        /// <param name="html">HTML content</param>
        /// <returns>Plain text version</returns>
        private string ConvertHtmlToPlainText(string html)
        {
            if (string.IsNullOrEmpty(html))
                return string.Empty;
            
            // This is a simple conversion - in a production environment, 
            // you might want to use a more sophisticated HTML-to-text converter
            
            // Remove HTML tags
            var text = System.Text.RegularExpressions.Regex.Replace(html, "<[^>]*>", string.Empty);
            
            // Decode HTML entities
            text = HttpUtility.HtmlDecode(text);
            
            // Replace multiple spaces with a single space
            text = System.Text.RegularExpressions.Regex.Replace(text, @"\s+", " ");
            
            // Replace multiple newlines with a single newline
            text = System.Text.RegularExpressions.Regex.Replace(text, @"\n+", "\n");
            
            return text.Trim();
        }

        /// <summary>
        /// Schedules the weekly digest to run
        /// </summary>
        /// <param name="dayOfWeek">Day of week to send digests (default: Monday)</param>
        /// <param name="hour">Hour of day to send digests (default: 9)</param>
        /// <returns>True if scheduling was successful</returns>
        public bool ScheduleWeeklyDigest(DayOfWeek dayOfWeek = DayOfWeek.Monday, int hour = 9)
        {
            try
            {
                Logger.Info($"Scheduling weekly digest for {dayOfWeek} at {hour}:00");
                
                // Note: This is a placeholder for actual scheduling logic
                // In a real implementation, you would use a task scheduler or Windows service
                
                // For now, we'll just log that scheduling was requested
                Logger.Info($"Weekly digest scheduled for {dayOfWeek} at {hour}:00");
                
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error scheduling weekly digest: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// Gets the history of email digests sent to a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="limit">Maximum number of records to return</param>
        /// <returns>List of email digest records</returns>
        public async Task<List<EmailDigestRecords>> GetUserDigestHistoryAsync(int userId, int limit = 10)
        {
            try
            {
                Logger.Info($"Getting email digest history for user {userId}");
                return await _emailDigestRecordsBLL.GetUserEmailDigestHistoryAsync(userId, limit);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting email digest history for user {userId}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets the last email digest sent to a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>The last email digest record, or null if none exists</returns>
        public async Task<EmailDigestRecords> GetLastDigestAsync(int userId)
        {
            try
            {
                Logger.Info($"Getting last email digest for user {userId}");
                return await _emailDigestRecordsBLL.GetLastEmailDigestAsync(userId);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error getting last email digest for user {userId}: {ex.Message}", ex);
                throw;
            }
        }
    }
}