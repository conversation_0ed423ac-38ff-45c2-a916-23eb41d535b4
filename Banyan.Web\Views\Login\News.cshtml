﻿
@{
    Layout = null;
    var model = ViewData["model"] as Banyan.Domain.News;
    var name = ViewData["name"] as String;
    var highlight = ViewData["highlight"] as String;
    var imgWidth = ViewData["imgWidth"] as String;
    if (String.IsNullOrEmpty(highlight))
    {
        highlight = "";
    }
}
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@(model.Title)</title>
    <style>
        body {
            font-size: 1.1em;
            background-color: #f4f4f4;
            -webkit-text-size-adjust: 100% !important;
        }
        h1 {
            font-size: 1.3em;
        }
        img {
            max-width: @imgWidth;
            height: auto !important;
        }
        .water-mark {
            font-size: 14px;
            color: #c2c2c2; /* 颜色会动态重写，根据配置 */
            position: fixed;
            padding: 0 15px;
            transform: translate(-50%, -50%);
            transform: rotate(-30deg);
            -ms-transform: rotate(-30deg); /* IE 9 */
            -moz-transform: rotate(-30deg); /* Firefox */
            -webkit-transform: rotate(-30deg); /* Safari 和 Chrome */
            -o-transform: rotate(-30deg);
            opacity: 0.3;
            overflow: hidden;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            pointer-events: none;
        }
    </style>
</head>
<body oncontextmenu="return false" oncopy="return false" onselectstart="return false">
    <div>
        @if (!string.IsNullOrEmpty(highlight))
        {
            @Html.Raw(model.Html.Replace(highlight, "<span style='color:#008af7;font-weight: bold'>" + highlight + "</span>"))
        }
        else
        {
            @Html.Raw(model.Html)
        }
    </div>
    <div style="margin: 10px;">
        @Html.Raw(model.PubTime)
        @if (model.Source == "海外" && !model.Translate)
        {
            <span style="opacity: .5"> 译自本地LLM</span>
        }
    </div>

    <script>
        window.onload = function () {
            watermark({ watermark_txt: "@name" });
        };
        function watermark(settings) {
            //debugger;
            //默认设置
            var defaultSettings = {
                watermark_txt: "text",
                watermark_x: 100,//水印起始位置x轴坐标
                watermark_y: 100,//水印起始位置Y轴坐标
                watermark_rows: 0,//水印行数
                watermark_cols: 0,//水印列数
                watermark_x_space: 60,//水印x轴间隔
                watermark_y_space: 60,//水印y轴间隔
                watermark_color: '#aaa',//水印字体颜色
                watermark_alpha: 0.4,//水印透明度
                watermark_fontsize: '15px',//水印字体大小
                watermark_font: '微软雅黑',//水印字体
                watermark_width: 100,//水印宽度
                watermark_height: 100,//水印长度
                watermark_angle: 30//水印倾斜度数
            };
            //采用配置项替换默认值，作用类似jquery.extend
            if (arguments.length === 1 && typeof arguments[0] === "object") {
                var src = arguments[0] || {};
                for (key in src) {
                    if (src[key] && defaultSettings[key] && src[key] === defaultSettings[key])
                        continue;
                    else if (src[key])
                        defaultSettings[key] = src[key];
                }
            }

            var oTemp = document.createDocumentFragment();

            //获取页面最大宽度
            var page_width = Math.max(document.body.scrollWidth, document.body.clientWidth);
            var cutWidth = page_width * 0.0150;
            var page_width = page_width - cutWidth;
            //获取页面最大高度
            var page_height = Math.max(document.body.scrollHeight, document.body.clientHeight);
            // var page_height = document.body.scrollHeight+document.body.scrollTop;
            //如果将水印列数设置为0，或水印列数设置过大，超过页面最大宽度，则重新计算水印列数和水印x轴间隔
            if (defaultSettings.watermark_cols == 0 || (parseInt(defaultSettings.watermark_x + defaultSettings.watermark_width * defaultSettings.watermark_cols + defaultSettings.watermark_x_space * (defaultSettings.watermark_cols - 1)) > page_width)) {
                defaultSettings.watermark_cols = parseInt((page_width - defaultSettings.watermark_x + defaultSettings.watermark_x_space) / (defaultSettings.watermark_width + defaultSettings.watermark_x_space));
                defaultSettings.watermark_x_space = parseInt((page_width - defaultSettings.watermark_x - defaultSettings.watermark_width * defaultSettings.watermark_cols) / (defaultSettings.watermark_cols - 1));
            }
            //如果将水印行数设置为0，或水印行数设置过大，超过页面最大长度，则重新计算水印行数和水印y轴间隔
            if (defaultSettings.watermark_rows == 0 || (parseInt(defaultSettings.watermark_y + defaultSettings.watermark_height * defaultSettings.watermark_rows + defaultSettings.watermark_y_space * (defaultSettings.watermark_rows - 1)) > page_height)) {
                defaultSettings.watermark_rows = parseInt((defaultSettings.watermark_y_space + page_height - defaultSettings.watermark_y) / (defaultSettings.watermark_height + defaultSettings.watermark_y_space));
                defaultSettings.watermark_y_space = parseInt(((page_height - defaultSettings.watermark_y) - defaultSettings.watermark_height * defaultSettings.watermark_rows) / (defaultSettings.watermark_rows - 1));
            }
            var x;
            var y;
            for (var i = 0; i < defaultSettings.watermark_rows; i++) {
                y = defaultSettings.watermark_y + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i;
                for (var j = 0; j < defaultSettings.watermark_cols; j++) {
                    x = defaultSettings.watermark_x + (defaultSettings.watermark_width + defaultSettings.watermark_x_space) * j;

                    var mask_div = document.createElement('div');
                    mask_div.id = 'mask_div' + i + j;
                    mask_div.className = 'water-mark';
                    mask_div.appendChild(document.createTextNode(defaultSettings.watermark_txt));
                    //设置水印div倾斜显示

                    mask_div.style.visibility = "";
                    mask_div.style.left = x + 'px';
                    mask_div.style.top = y + 'px';
                    mask_div.style.zIndex = "9999"; // 弹窗为19891016左右
                    //mask_div.style.border="solid #eee 1px";
                    mask_div.style.textAlign = "center";
                    mask_div.style.width = defaultSettings.watermark_width + 'px';
                    mask_div.style.height = defaultSettings.watermark_height + 'px';
                    mask_div.style.display = "block";
                    oTemp.appendChild(mask_div);
                };
            };
            document.body.appendChild(oTemp);

            if (!!window.ActiveXObject || "ActiveXObject" in window)
                document.getElementById("ie-warn").style.display = "block";

        }
    </script>
</body>
</html>
