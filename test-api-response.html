<!DOCTYPE html>
<html>
<head>
    <title>Test API Response</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .response { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Test GetNewsRecommendations API Response</h1>

    <div>
        <button onclick="testAPI()">Test API</button>
        <button onclick="testGetNews()">Test GetNews API</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>
    
    <script>
        function testAPI() {
            $('#results').html('<div class="response">Loading...</div>');
            
            $.ajax({
                url: '/AdminApi/GetNewsRecommendations',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    page: 1,
                    pageSize: 3,
                    threshold: 0.4
                }),
                success: function(response) {
                    console.log('API Response:', response);
                    
                    var html = '<div class="response success">';
                    html += '<h3>API Response (Success)</h3>';
                    html += '<pre>' + JSON.stringify(response, null, 2) + '</pre>';
                    html += '</div>';
                    
                    if (response.code === 0 && response.data && response.data.data) {
                        html += '<div class="response">';
                        html += '<h3>Subject Field Analysis</h3>';
                        
                        response.data.data.forEach(function(item, index) {
                            html += '<div style="border: 1px solid #ddd; margin: 10px 0; padding: 10px;">';
                            html += '<h4>News Item ' + (index + 1) + '</h4>';
                            html += '<p><strong>ID:</strong> ' + (item.id || 'N/A') + '</p>';
                            html += '<p><strong>Title:</strong> ' + (item.title || 'N/A') + '</p>';
                            html += '<p><strong>Subject:</strong> ' + (item.subject || 'N/A') + '</p>';
                            html += '<p><strong>Category:</strong> ' + (item.category || 'N/A') + '</p>';
                            html += '<p><strong>Source:</strong> ' + (item.source || 'N/A') + '</p>';
                            html += '<p><strong>Publish Time:</strong> ' + (item.publishTime || 'N/A') + '</p>';
                            html += '<p><strong>Has Subject Field:</strong> ' + (item.hasOwnProperty('subject') ? 'YES' : 'NO') + '</p>';
                            html += '<p><strong>Subject Length:</strong> ' + (item.subject ? item.subject.length : 0) + '</p>';
                            html += '<p><strong>Subject Empty:</strong> ' + (item.subject === '' ? 'YES (Empty String)' : 'NO') + '</p>';
                            html += '<p><strong>Subject Content Preview:</strong> ' + (item.subject ? item.subject.substring(0, 100) + (item.subject.length > 100 ? '...' : '') : 'N/A') + '</p>';
                            html += '</div>';
                        });
                        
                        html += '</div>';
                    }
                    
                    $('#results').html(html);
                },
                error: function(xhr, status, error) {
                    console.error('API Error:', error);
                    
                    var html = '<div class="response error">';
                    html += '<h3>API Response (Error)</h3>';
                    html += '<p><strong>Status:</strong> ' + status + '</p>';
                    html += '<p><strong>Error:</strong> ' + error + '</p>';
                    html += '<p><strong>Status Code:</strong> ' + xhr.status + '</p>';
                    html += '<pre>' + xhr.responseText + '</pre>';
                    html += '</div>';
                    
                    $('#results').html(html);
                }
            });
        }
        
        function testGetNews() {
            $('#results').html('<div class="response">Loading GetNews API...</div>');

            $.ajax({
                url: '/Api/GetNews',
                type: 'POST',
                data: {
                    uid: 1, // 假设用户ID为1，实际使用时需要获取当前用户ID
                    page: 1,
                    limit: 3,
                    keywords: ''
                },
                success: function(response) {
                    console.log('GetNews API Response:', response);

                    var html = '<div class="response success">';
                    html += '<h3>GetNews API Response (Success)</h3>';
                    html += '<pre>' + JSON.stringify(response, null, 2) + '</pre>';
                    html += '</div>';

                    if (response.code === 0 && response.data) {
                        html += '<div class="response">';
                        html += '<h3>GetNews Subject Field Analysis</h3>';

                        response.data.forEach(function(item, index) {
                            html += '<div style="border: 1px solid #ddd; margin: 10px 0; padding: 10px;">';
                            html += '<h4>News Item ' + (index + 1) + '</h4>';
                            html += '<p><strong>ID:</strong> ' + (item.Id || 'N/A') + '</p>';
                            html += '<p><strong>Title:</strong> ' + (item.Title || 'N/A') + '</p>';
                            html += '<p><strong>Subject:</strong> ' + (item.Subject || 'N/A') + '</p>';
                            html += '<p><strong>Content Preview:</strong> ' + (item.Content ? item.Content.substring(0, 100) + '...' : 'N/A') + '</p>';
                            html += '<p><strong>Has Subject Field:</strong> ' + (item.hasOwnProperty('Subject') ? 'YES' : 'NO') + '</p>';
                            html += '<p><strong>Subject Length:</strong> ' + (item.Subject ? item.Subject.length : 0) + '</p>';
                            html += '<p><strong>Subject Empty:</strong> ' + (item.Subject === '' ? 'YES (Empty String)' : 'NO') + '</p>';
                            html += '</div>';
                        });

                        html += '</div>';
                    }

                    $('#results').html(html);
                },
                error: function(xhr, status, error) {
                    console.error('GetNews API Error:', error);

                    var html = '<div class="response error">';
                    html += '<h3>GetNews API Response (Error)</h3>';
                    html += '<p><strong>Status:</strong> ' + status + '</p>';
                    html += '<p><strong>Error:</strong> ' + error + '</p>';
                    html += '<p><strong>Status Code:</strong> ' + xhr.status + '</p>';
                    html += '<pre>' + xhr.responseText + '</pre>';
                    html += '</div>';

                    $('#results').html(html);
                }
            });
        }

        function clearResults() {
            $('#results').empty();
        }
    </script>
</body>
</html>
