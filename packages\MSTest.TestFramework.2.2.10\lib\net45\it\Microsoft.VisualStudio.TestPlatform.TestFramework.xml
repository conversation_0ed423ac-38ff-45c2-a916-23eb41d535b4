<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod">
            <summary>
            Metodo di test per l'esecuzione.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestMethodName">
            <summary>
            Ottiene il nome del metodo di test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestClassName">
            <summary>
            Ottiene il nome della classe di test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ReturnType">
            <summary>
            Ottiene il tipo restituito del metodo di test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ParameterTypes">
            <summary>
            Ottiene i parametri del metodo di test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.MethodInfo">
            <summary>
            Ottiene l'oggetto methodInfo per il metodo di test.
            </summary>
            <remarks>
            This is just to retrieve additional information about the method.
            Do not directly invoke the method using MethodInfo. Use ITestMethod.Invoke instead.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.Invoke(System.Object[])">
            <summary>
            Richiama il metodo di test.
            </summary>
            <param name="arguments">
            Argomenti da passare al metodo di test, ad esempio per test basati sui dati
            </param>
            <returns>
            Risultato della chiamata del metodo di test.
            </returns>
            <remarks>
            This call handles asynchronous test methods as well.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAllAttributes(System.Boolean)">
            <summary>
            Ottiene tutti gli attributi del metodo di test.
            </summary>
            <param name="inherit">
            Indica se l'attributo definito nella classe padre è valido.
            </param>
            <returns>
            Tutti gli attributi.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAttributes``1(System.Boolean)">
            <summary>
            Ottiene l'attributo di tipo specifico.
            </summary>
            <typeparam name="AttributeType"> System.Attribute type. </typeparam>
            <param name="inherit">
            Indica se l'attributo definito nella classe padre è valido.
            </param>
            <returns>
            Attributi del tipo specificato.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Helper">
            <summary>
            Helper.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNull(System.Object,System.String,System.String)">
            <summary>
            Parametro check non Null.
            </summary>
            <param name="param">
            Parametro.
            </param>
            <param name="parameterName">
            Nome del parametro.
            </param>
            <param name="message">
            Messaggio.
            </param>
            <exception cref="T:System.ArgumentNullException"> Throws argument null exception when parameter is null. </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNullOrEmpty(System.String,System.String,System.String)">
            <summary>
            Parametro check non Null o vuoto.
            </summary>
            <param name="param">
            Parametro.
            </param>
            <param name="parameterName">
            Nome del parametro.
            </param>
            <param name="message">
            Messaggio.
            </param>
            <exception cref="T:System.ArgumentException"> Throws ArgumentException when parameter is null. </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod">
            <summary>
            Enumerazione relativa alla modalità di accesso alle righe di dati nei test basati sui dati.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Sequential">
            <summary>
            Le righe vengono restituite in ordine sequenziale.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random">
            <summary>
            Le righe vengono restituite in ordine casuale.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute">
            <summary>
            Attributo per definire i dati inline per un metodo di test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/>.
            </summary>
            <param name="data1"> Oggetto dati. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object,System.Object[])">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> che accetta una matrice di argomenti.
            </summary>
            <param name="data1"> Oggetto dati. </param>
            <param name="moreData"> Altri dati. </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.Data">
            <summary>
            Ottiene i dati per chiamare il metodo di test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.DisplayName">
            <summary>
            Ottiene o imposta il nome visualizzato nei risultati del test per la personalizzazione.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            <summary>
            Eccezione senza risultati dell'asserzione.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String,System.Exception)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
            <param name="msg"> Messaggio. </param>
            <param name="ex"> Eccezione. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
            <param name="msg"> Messaggio. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException">
            <summary>
            Classe InternalTestFailureException. Usata per indicare un errore interno per un test case
            </summary>
            <remarks>
            This class is only added to preserve source compatibility with the V1 framework.
            For all practical purposes either use AssertFailedException/AssertInconclusiveException.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String,System.Exception)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
            <param name="msg"> Messaggio dell'eccezione. </param>
            <param name="ex"> Eccezione. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
            <param name="msg"> Messaggio dell'eccezione. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute">
            <summary>
            Attributo che specifica di presupporre un'eccezione del tipo specificato
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> con il tipo previsto
            </summary>
            <param name="exceptionType">Tipo dell'eccezione prevista</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type,System.String)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> con
            il tipo previsto e il messaggio da includere quando il test non genera alcuna eccezione.
            </summary>
            <param name="exceptionType">Tipo dell'eccezione prevista</param>
            <param name="noExceptionMessage">
            Messaggio da includere nel risultato del test se il test non riesce perché non viene generata un'eccezione
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.ExceptionType">
            <summary>
            Ottiene un valore che indica il tipo dell'eccezione prevista
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.AllowDerivedTypes">
            <summary>
            Ottiene o imposta un valore che indica se consentire a tipi derivati dal tipo dell'eccezione prevista
            di qualificarsi come previsto
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.NoExceptionMessage">
            <summary>
            Ottiene il messaggio da includere nel risultato del test se il test non riesce perché non viene generata un'eccezione
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.Verify(System.Exception)">
            <summary>
            Verifica che il tipo dell'eccezione generata dallo unit test sia prevista
            </summary>
            <param name="exception">Eccezione generata dallo unit test</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute">
            <summary>
            Classe di base per attributi che specificano se prevedere che uno unit test restituisca un'eccezione
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> con un messaggio per indicare nessuna eccezione
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor(System.String)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> con un messaggio che indica nessuna eccezione
            </summary>
            <param name="noExceptionMessage">
            Messaggio da includere nel risultato del test se il test non riesce perché non
            viene generata un'eccezione
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.NoExceptionMessage">
            <summary>
            Ottiene il messaggio da includere nel risultato del test se il test non riesce perché non viene generata un'eccezione
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.SpecifiedNoExceptionMessage">
            <summary>
            Ottiene il messaggio da includere nel risultato del test se il test non riesce perché non viene generata un'eccezione
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.GetDefaultNoExceptionMessage(System.String)">
            <summary>
            Ottiene il messaggio predefinito per indicare nessuna eccezione
            </summary>
            <param name="expectedExceptionAttributeTypeName">Nome del tipo di attributo di ExpectedException</param>
            <returns>Messaggio predefinito per indicare nessuna eccezione</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.Verify(System.Exception)">
            <summary>
            Determina se l'eccezione è prevista. Se il metodo viene completato, si
            presuppone che l'eccezione era prevista. Se il metodo genera un'eccezione, si
            presuppone che l'eccezione non era prevista e il messaggio dell'eccezione generata
            viene incluso nel risultato del test. Si può usare la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert"/> per
            comodità. Se si usa <see cref="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive"/> e l'asserzione non riesce,
            il risultato del test viene impostato su Senza risultati.
            </summary>
            <param name="exception">Eccezione generata dallo unit test</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.RethrowIfAssertException(System.Exception)">
            <summary>
            Genera di nuovo l'eccezione se si tratta di un'eccezione AssertFailedException o AssertInconclusiveException
            </summary>
            <param name="exception">Eccezione da generare di nuovo se si tratta di un'eccezione di asserzione</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper">
            <summary>
            Questa classe consente all'utente di eseguire testing unità per tipi che usano tipi generici.
            GenericParameterHelper soddisfa alcuni dei vincoli di tipo generici più comuni,
            ad esempio:
            1. costruttore predefinito pubblico
            2. implementa l'interfaccia comune: IComparable, IEnumerable
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> che
            soddisfa il vincolo 'newable' nei generics C#.
            </summary>
            <remarks>
            This constructor initializes the Data property to a random value.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor(System.Int32)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> che
            inizializza la proprietà Data con un valore fornito dall'utente.
            </summary>
            <param name="data">Qualsiasi valore Integer</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Data">
            <summary>
            Ottiene o imposta i dati
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Equals(System.Object)">
            <summary>
            Esegue il confronto dei valori di due oggetti GenericParameterHelper
            </summary>
            <param name="obj">oggetto con cui eseguire il confronto</param>
            <returns>true se il valore di obj è uguale a quello dell'oggetto GenericParameterHelper 'this';
            in caso contrario, false.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetHashCode">
            <summary>
            Restituisce un codice hash per questo oggetto.
            </summary>
            <returns>Codice hash.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.CompareTo(System.Object)">
            <summary>
            Confronta i dati dei due oggetti <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </summary>
            <param name="obj">Oggetto con cui eseguire il confronto.</param>
            <returns>
            Numero con segno che indica i valori relativi di questa istanza e di questo valore.
            </returns>
            <exception cref="T:System.NotSupportedException">
            Thrown when the object passed in is not an instance of <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetEnumerator">
            <summary>
            Restituisce un oggetto IEnumerator la cui lunghezza viene derivata dalla
            proprietà Data.
            </summary>
            <returns>L'oggetto IEnumerator</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Clone">
            <summary>
            Restituisce un oggetto GenericParameterHelper uguale a
            quello corrente.
            </summary>
            <returns>Oggetto clonato.</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger">
            <summary>
            Consente agli utenti di registrare/scrivere tracce degli unit test per la diagnostica.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessageHandler">
            <summary>
            Gestore per LogMessage.
            </summary>
            <param name="message">Messaggio da registrare.</param>
        </member>
        <member name="E:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.OnLogMessage">
            <summary>
            Evento di cui rimanere in ascolto. Generato quando il writer di unit test scrive alcuni messaggi.
            Utilizzato principalmente dall'adattatore.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessage(System.String,System.Object[])">
            <summary>
            API del writer di test da chiamare per registrare i messaggi.
            </summary>
            <param name="format">Formato stringa con segnaposto.</param>
            <param name="args">Parametri per segnaposto.</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute">
            <summary>
            Attributo TestCategory; usato per specificare la categoria di uno unit test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.#ctor(System.String)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute"/> e applica la categoria al test.
            </summary>
            <param name="testCategory">
            Categoria di test.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.TestCategories">
            <summary>
            Ottiene le categorie di test applicate al test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute">
            <summary>
            Classe di base per l'attributo "Category"
            </summary>
            <remarks>
            The reason for this attribute is to let the users create their own implementation of test categories.
            - test framework (discovery, etc) deals with TestCategoryBaseAttribute.
            - The reason that TestCategories property is a collection rather than a string,
              is to give more flexibility to the user. For instance the implementation may be based on enums for which the values can be OR'ed
              in which case it makes sense to have single attribute rather than multiple ones on the same test.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.#ctor">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute"/>.
            Applica la categoria al test. Le stringhe restituite da TestCategories
            vengono usate con il comando /category per filtrare i test
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.TestCategories">
            <summary>
            Ottiene la categoria di test applicata al test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            <summary>
            Classe AssertFailedException. Usata per indicare un errore per un test case
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
            <param name="msg"> Messaggio. </param>
            <param name="ex"> Eccezione. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
            <param name="msg"> Messaggio. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert">
            <summary>
            Raccolta di classi helper per testare diverse condizioni
            negli unit test. Se la condizione da testare non viene soddisfatta,
            viene generata un'eccezione.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.That">
            <summary>
            Ottiene l'istanza singleton della funzionalità Assert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void IsOfType&lt;T&gt;(this Assert assert, object obj)"
            Users could then use a syntax similar to the default assertions which in this case is "Assert.That.IsOfType&lt;Dog&gt;(animal);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean)">
            <summary>
            Verifica se la condizione specificata è true e genera un'eccezione
            se è false.
            </summary>
            <param name="condition">
            Condizione che il test presuppone sia true.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String)">
            <summary>
            Verifica se la condizione specificata è true e genera un'eccezione
            se è false.
            </summary>
            <param name="condition">
            Condizione che il test presuppone sia true.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="condition"/>
            è false. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String,System.Object[])">
            <summary>
            Verifica se la condizione specificata è true e genera un'eccezione
            se è false.
            </summary>
            <param name="condition">
            Condizione che il test presuppone sia true.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="condition"/>
            è false. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean)">
            <summary>
            Verifica se la condizione specificata è false e genera un'eccezione
            se è true.
            </summary>
            <param name="condition">
            Condizione che il test presuppone sia false.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String)">
            <summary>
            Verifica se la condizione specificata è false e genera un'eccezione
            se è true.
            </summary>
            <param name="condition">
            Condizione che il test presuppone sia false.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="condition"/>
            è true. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String,System.Object[])">
            <summary>
            Verifica se la condizione specificata è false e genera un'eccezione
            se è true.
            </summary>
            <param name="condition">
            Condizione che il test presuppone sia false.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="condition"/>
            è true. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object)">
            <summary>
            Verifica se l'oggetto specificato è Null e genera un'eccezione
            se non lo è.
            </summary>
            <param name="value">
            Oggetto che il test presuppone sia Null.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String)">
            <summary>
            Verifica se l'oggetto specificato è Null e genera un'eccezione
            se non lo è.
            </summary>
            <param name="value">
            Oggetto che il test presuppone sia Null.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="value"/>
            non è Null. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String,System.Object[])">
            <summary>
            Verifica se l'oggetto specificato è Null e genera un'eccezione
            se non lo è.
            </summary>
            <param name="value">
            Oggetto che il test presuppone sia Null.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="value"/>
            non è Null. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object)">
            <summary>
            Verifica se l'oggetto specificato non è Null e genera un'eccezione
            se non lo è.
            </summary>
            <param name="value">
            Oggetto che il test presuppone non sia Null.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String)">
            <summary>
            Verifica se l'oggetto specificato non è Null e genera un'eccezione
            se non lo è.
            </summary>
            <param name="value">
            Oggetto che il test presuppone non sia Null.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="value"/>
            è Null. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String,System.Object[])">
            <summary>
            Verifica se l'oggetto specificato non è Null e genera un'eccezione
            se non lo è.
            </summary>
            <param name="value">
            Oggetto che il test presuppone non sia Null.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="value"/>
            è Null. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object)">
            <summary>
            Verifica se gli oggetti specificati si riferiscono entrambi allo stesso oggetto e
            genera un'eccezione se i due input non si riferiscono allo stesso oggetto.
            </summary>
            <param name="expected">
            Primo oggetto da confrontare. Questo è il valore previsto dal test.
            </param>
            <param name="actual">
            Secondo oggetto da confrontare. Si tratta del valore prodotto dal codice sottoposto a test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String)">
            <summary>
            Verifica se gli oggetti specificati si riferiscono entrambi allo stesso oggetto e
            genera un'eccezione se i due input non si riferiscono allo stesso oggetto.
            </summary>
            <param name="expected">
            Primo oggetto da confrontare. Questo è il valore previsto dal test.
            </param>
            <param name="actual">
            Secondo oggetto da confrontare. Si tratta del valore prodotto dal codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è diverso da <paramref name="expected"/>. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Verifica se gli oggetti specificati si riferiscono entrambi allo stesso oggetto e
            genera un'eccezione se i due input non si riferiscono allo stesso oggetto.
            </summary>
            <param name="expected">
            Primo oggetto da confrontare. Questo è il valore previsto dal test.
            </param>
            <param name="actual">
            Secondo oggetto da confrontare. Si tratta del valore prodotto dal codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è diverso da <paramref name="expected"/>. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object)">
            <summary>
            Verifica se gli oggetti specificati si riferiscono a oggetti diversi e
            genera un'eccezione se i due input si riferiscono allo stesso oggetto.
            </summary>
            <param name="notExpected">
            Primo oggetto da confrontare. Questo è il valore che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Secondo oggetto da confrontare. Si tratta del valore prodotto dal codice sottoposto a test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String)">
            <summary>
            Verifica se gli oggetti specificati si riferiscono a oggetti diversi e
            genera un'eccezione se i due input si riferiscono allo stesso oggetto.
            </summary>
            <param name="notExpected">
            Primo oggetto da confrontare. Questo è il valore che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Secondo oggetto da confrontare. Si tratta del valore prodotto dal codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Verifica se gli oggetti specificati si riferiscono a oggetti diversi e
            genera un'eccezione se i due input si riferiscono allo stesso oggetto.
            </summary>
            <param name="notExpected">
            Primo oggetto da confrontare. Questo è il valore che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Secondo oggetto da confrontare. Si tratta del valore prodotto dal codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0)">
            <summary>
            Verifica se i valori specificati sono uguali e genera un'eccezione
            se sono diversi. I tipi numerici diversi vengono considerati
            diversi anche se i valori logici sono uguali. 42L è diverso da 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Primo valore da confrontare. Questo è il valore previsto dai test.
            </param>
            <param name="actual">
            Secondo valore da confrontare. Si tratta del valore prodotto dal codice sottoposto a test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String)">
            <summary>
            Verifica se i valori specificati sono uguali e genera un'eccezione
            se sono diversi. I tipi numerici diversi vengono considerati
            diversi anche se i valori logici sono uguali. 42L è diverso da 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Primo valore da confrontare. Questo è il valore previsto dai test.
            </param>
            <param name="actual">
            Secondo valore da confrontare. Si tratta del valore prodotto dal codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è diverso da <paramref name="expected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Verifica se i valori specificati sono uguali e genera un'eccezione
            se sono diversi. I tipi numerici diversi vengono considerati
            diversi anche se i valori logici sono uguali. 42L è diverso da 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Primo valore da confrontare. Questo è il valore previsto dai test.
            </param>
            <param name="actual">
            Secondo valore da confrontare. Si tratta del valore prodotto dal codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è diverso da <paramref name="expected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0)">
            <summary>
            Verifica se i valori specificati sono diversi e genera un'eccezione
            se sono uguali. I tipi numerici diversi vengono considerati
            diversi anche se i valori logici sono uguali. 42L è diverso da 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Primo valore da confrontare. Questo è il valore che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Secondo valore da confrontare. Si tratta del valore prodotto dal codice sottoposto a test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String)">
            <summary>
            Verifica se i valori specificati sono diversi e genera un'eccezione
            se sono uguali. I tipi numerici diversi vengono considerati
            diversi anche se i valori logici sono uguali. 42L è diverso da 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Primo valore da confrontare. Questo è il valore che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Secondo valore da confrontare. Si tratta del valore prodotto dal codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Verifica se i valori specificati sono diversi e genera un'eccezione
            se sono uguali. I tipi numerici diversi vengono considerati
            diversi anche se i valori logici sono uguali. 42L è diverso da 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Primo valore da confrontare. Questo è il valore che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Secondo valore da confrontare. Si tratta del valore prodotto dal codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object)">
            <summary>
            Verifica se gli oggetti specificati sono uguali e genera un'eccezione
            se sono diversi. I tipi numerici diversi vengono considerati
            diversi anche se i valori logici sono uguali. 42L è diverso da 42.
            </summary>
            <param name="expected">
            Primo oggetto da confrontare. Questo è l'oggetto previsto dai test.
            </param>
            <param name="actual">
            Secondo oggetto da confrontare. Si tratta dell'oggetto prodotto dal codice sottoposto a test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String)">
            <summary>
            Verifica se gli oggetti specificati sono uguali e genera un'eccezione
            se sono diversi. I tipi numerici diversi vengono considerati
            diversi anche se i valori logici sono uguali. 42L è diverso da 42.
            </summary>
            <param name="expected">
            Primo oggetto da confrontare. Questo è l'oggetto previsto dai test.
            </param>
            <param name="actual">
            Secondo oggetto da confrontare. Si tratta dell'oggetto prodotto dal codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è diverso da <paramref name="expected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Verifica se gli oggetti specificati sono uguali e genera un'eccezione
            se sono diversi. I tipi numerici diversi vengono considerati
            diversi anche se i valori logici sono uguali. 42L è diverso da 42.
            </summary>
            <param name="expected">
            Primo oggetto da confrontare. Questo è l'oggetto previsto dai test.
            </param>
            <param name="actual">
            Secondo oggetto da confrontare. Si tratta dell'oggetto prodotto dal codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è diverso da <paramref name="expected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object)">
            <summary>
            Verifica se gli oggetti specificati sono diversi e genera un'eccezione
            se sono uguali. I tipi numerici diversi vengono considerati
            diversi anche se i valori logici sono uguali. 42L è diverso da 42.
            </summary>
            <param name="notExpected">
            Primo oggetto da confrontare. Questo è il valore che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Secondo oggetto da confrontare. Si tratta dell'oggetto prodotto dal codice sottoposto a test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String)">
            <summary>
            Verifica se gli oggetti specificati sono diversi e genera un'eccezione
            se sono uguali. I tipi numerici diversi vengono considerati
            diversi anche se i valori logici sono uguali. 42L è diverso da 42.
            </summary>
            <param name="notExpected">
            Primo oggetto da confrontare. Questo è il valore che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Secondo oggetto da confrontare. Si tratta dell'oggetto prodotto dal codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Verifica se gli oggetti specificati sono diversi e genera un'eccezione
            se sono uguali. I tipi numerici diversi vengono considerati
            diversi anche se i valori logici sono uguali. 42L è diverso da 42.
            </summary>
            <param name="notExpected">
            Primo oggetto da confrontare. Questo è il valore che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Secondo oggetto da confrontare. Si tratta dell'oggetto prodotto dal codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single)">
            <summary>
            Verifica se i valori float specificati sono uguali e genera un'eccezione
            se sono diversi.
            </summary>
            <param name="expected">
            Primo valore float da confrontare. Questo è il valore float previsto dai test.
            </param>
            <param name="actual">
            Secondo valore float da confrontare. Si tratta del valore float prodotto dal codice sottoposto a test.
            </param>
            <param name="delta">
            Accuratezza richiesta. Verrà generata un'eccezione solo se
            <paramref name="actual"/> differisce da <paramref name="expected"/>
            di più di <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Verifica se i valori float specificati sono uguali e genera un'eccezione
            se sono diversi.
            </summary>
            <param name="expected">
            Primo valore float da confrontare. Questo è il valore float previsto dai test.
            </param>
            <param name="actual">
            Secondo valore float da confrontare. Si tratta del valore float prodotto dal codice sottoposto a test.
            </param>
            <param name="delta">
            Accuratezza richiesta. Verrà generata un'eccezione solo se
            <paramref name="actual"/> differisce da <paramref name="expected"/>
            di più di <paramref name="delta"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            differisce da <paramref name="expected"/> di più di
            <paramref name="delta"/>. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Verifica se i valori float specificati sono uguali e genera un'eccezione
            se sono diversi.
            </summary>
            <param name="expected">
            Primo valore float da confrontare. Questo è il valore float previsto dai test.
            </param>
            <param name="actual">
            Secondo valore float da confrontare. Si tratta del valore float prodotto dal codice sottoposto a test.
            </param>
            <param name="delta">
            Accuratezza richiesta. Verrà generata un'eccezione solo se
            <paramref name="actual"/> differisce da <paramref name="expected"/>
            di più di <paramref name="delta"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            differisce da <paramref name="expected"/> di più di
            <paramref name="delta"/>. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single)">
            <summary>
            Verifica se i valori float specificati sono diversi e genera un'eccezione
            se sono uguali.
            </summary>
            <param name="notExpected">
            Primo valore float da confrontare. Questo è il valore float che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Secondo valore float da confrontare. Si tratta del valore float prodotto dal codice sottoposto a test.
            </param>
            <param name="delta">
            Accuratezza richiesta. Verrà generata un'eccezione solo se
            <paramref name="actual"/> differisce da <paramref name="notExpected"/>
            al massimo di <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Verifica se i valori float specificati sono diversi e genera un'eccezione
            se sono uguali.
            </summary>
            <param name="notExpected">
            Primo valore float da confrontare. Questo è il valore float che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Secondo valore float da confrontare. Si tratta del valore float prodotto dal codice sottoposto a test.
            </param>
            <param name="delta">
            Accuratezza richiesta. Verrà generata un'eccezione solo se
            <paramref name="actual"/> differisce da <paramref name="notExpected"/>
            al massimo di <paramref name="delta"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/> o differisce di meno di
            <paramref name="delta"/>. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Verifica se i valori float specificati sono diversi e genera un'eccezione
            se sono uguali.
            </summary>
            <param name="notExpected">
            Primo valore float da confrontare. Questo è il valore float che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Secondo valore float da confrontare. Si tratta del valore float prodotto dal codice sottoposto a test.
            </param>
            <param name="delta">
            Accuratezza richiesta. Verrà generata un'eccezione solo se
            <paramref name="actual"/> differisce da <paramref name="notExpected"/>
            al massimo di <paramref name="delta"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/> o differisce di meno di
            <paramref name="delta"/>. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double)">
            <summary>
            Verifica se i valori double specificati sono uguali e genera un'eccezione
            se sono diversi.
            </summary>
            <param name="expected">
            Primo valore double da confrontare. Questo è il valore double previsto dai test.
            </param>
            <param name="actual">
            Secondo valore double da confrontare. Si tratta del valore double prodotto dal codice sottoposto a test.
            </param>
            <param name="delta">
            Accuratezza richiesta. Verrà generata un'eccezione solo se
            <paramref name="actual"/> differisce da <paramref name="expected"/>
            di più di <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Verifica se i valori double specificati sono uguali e genera un'eccezione
            se sono diversi.
            </summary>
            <param name="expected">
            Primo valore double da confrontare. Questo è il valore double previsto dai test.
            </param>
            <param name="actual">
            Secondo valore double da confrontare. Si tratta del valore double prodotto dal codice sottoposto a test.
            </param>
            <param name="delta">
            Accuratezza richiesta. Verrà generata un'eccezione solo se
            <paramref name="actual"/> differisce da <paramref name="expected"/>
            di più di <paramref name="delta"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            differisce da <paramref name="expected"/> di più di
            <paramref name="delta"/>. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Verifica se i valori double specificati sono uguali e genera un'eccezione
            se sono diversi.
            </summary>
            <param name="expected">
            Primo valore double da confrontare. Questo è il valore double previsto dai test.
            </param>
            <param name="actual">
            Secondo valore double da confrontare. Si tratta del valore double prodotto dal codice sottoposto a test.
            </param>
            <param name="delta">
            Accuratezza richiesta. Verrà generata un'eccezione solo se
            <paramref name="actual"/> differisce da <paramref name="expected"/>
            di più di <paramref name="delta"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            differisce da <paramref name="expected"/> di più di
            <paramref name="delta"/>. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double)">
            <summary>
            Verifica se i valori double specificati sono diversi e genera un'eccezione
            se sono uguali.
            </summary>
            <param name="notExpected">
            Primo valore double da confrontare. Questo è il valore double che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Secondo valore double da confrontare. Si tratta del valore double prodotto dal codice sottoposto a test.
            </param>
            <param name="delta">
            Accuratezza richiesta. Verrà generata un'eccezione solo se
            <paramref name="actual"/> differisce da <paramref name="notExpected"/>
            al massimo di <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Verifica se i valori double specificati sono diversi e genera un'eccezione
            se sono uguali.
            </summary>
            <param name="notExpected">
            Primo valore double da confrontare. Questo è il valore double che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Secondo valore double da confrontare. Si tratta del valore double prodotto dal codice sottoposto a test.
            </param>
            <param name="delta">
            Accuratezza richiesta. Verrà generata un'eccezione solo se
            <paramref name="actual"/> differisce da <paramref name="notExpected"/>
            al massimo di <paramref name="delta"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/> o differisce di meno di
            <paramref name="delta"/>. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Verifica se i valori double specificati sono diversi e genera un'eccezione
            se sono uguali.
            </summary>
            <param name="notExpected">
            Primo valore double da confrontare. Questo è il valore double che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Secondo valore double da confrontare. Si tratta del valore double prodotto dal codice sottoposto a test.
            </param>
            <param name="delta">
            Accuratezza richiesta. Verrà generata un'eccezione solo se
            <paramref name="actual"/> differisce da <paramref name="notExpected"/>
            al massimo di <paramref name="delta"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/> o differisce di meno di
            <paramref name="delta"/>. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean)">
            <summary>
            Verifica se le stringhe specificate sono uguali e genera un'eccezione
            se sono diverse. Per il confronto vengono usate le impostazioni cultura inglese non dipendenti da paese/area geografica.
            </summary>
            <param name="expected">
            Prima stringa da confrontare. Questa è la stringa prevista dai test.
            </param>
            <param name="actual">
            Seconda stringa da confrontare. Si tratta della stringa prodotta dal codice sottoposto a test.
            </param>
            <param name="ignoreCase">
            Valore booleano che indica un confronto con o senza distinzione tra maiuscole e minuscole. True
            indica un confronto senza distinzione tra maiuscole e minuscole.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Verifica se le stringhe specificate sono uguali e genera un'eccezione
            se sono diverse. Per il confronto vengono usate le impostazioni cultura inglese non dipendenti da paese/area geografica.
            </summary>
            <param name="expected">
            Prima stringa da confrontare. Questa è la stringa prevista dai test.
            </param>
            <param name="actual">
            Seconda stringa da confrontare. Si tratta della stringa prodotta dal codice sottoposto a test.
            </param>
            <param name="ignoreCase">
            Valore booleano che indica un confronto con o senza distinzione tra maiuscole e minuscole. True
            indica un confronto senza distinzione tra maiuscole e minuscole.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è diverso da <paramref name="expected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Verifica se le stringhe specificate sono uguali e genera un'eccezione
            se sono diverse. Per il confronto vengono usate le impostazioni cultura inglese non dipendenti da paese/area geografica.
            </summary>
            <param name="expected">
            Prima stringa da confrontare. Questa è la stringa prevista dai test.
            </param>
            <param name="actual">
            Seconda stringa da confrontare. Si tratta della stringa prodotta dal codice sottoposto a test.
            </param>
            <param name="ignoreCase">
            Valore booleano che indica un confronto con o senza distinzione tra maiuscole e minuscole. True
            indica un confronto senza distinzione tra maiuscole e minuscole.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è diverso da <paramref name="expected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Verifica se le stringhe specificate sono uguali e genera un'eccezione
            se sono diverse.
            </summary>
            <param name="expected">
            Prima stringa da confrontare. Questa è la stringa prevista dai test.
            </param>
            <param name="actual">
            Seconda stringa da confrontare. Si tratta della stringa prodotta dal codice sottoposto a test.
            </param>
            <param name="ignoreCase">
            Valore booleano che indica un confronto con o senza distinzione tra maiuscole e minuscole. True
            indica un confronto senza distinzione tra maiuscole e minuscole.
            </param>
            <param name="culture">
            Oggetto CultureInfo che fornisce informazioni sul confronto specifiche delle impostazioni cultura.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Verifica se le stringhe specificate sono uguali e genera un'eccezione
            se sono diverse.
            </summary>
            <param name="expected">
            Prima stringa da confrontare. Questa è la stringa prevista dai test.
            </param>
            <param name="actual">
            Seconda stringa da confrontare. Si tratta della stringa prodotta dal codice sottoposto a test.
            </param>
            <param name="ignoreCase">
            Valore booleano che indica un confronto con o senza distinzione tra maiuscole e minuscole. True
            indica un confronto senza distinzione tra maiuscole e minuscole.
            </param>
            <param name="culture">
            Oggetto CultureInfo che fornisce informazioni sul confronto specifiche delle impostazioni cultura.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è diverso da <paramref name="expected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Verifica se le stringhe specificate sono uguali e genera un'eccezione
            se sono diverse.
            </summary>
            <param name="expected">
            Prima stringa da confrontare. Questa è la stringa prevista dai test.
            </param>
            <param name="actual">
            Seconda stringa da confrontare. Si tratta della stringa prodotta dal codice sottoposto a test.
            </param>
            <param name="ignoreCase">
            Valore booleano che indica un confronto con o senza distinzione tra maiuscole e minuscole. True
            indica un confronto senza distinzione tra maiuscole e minuscole.
            </param>
            <param name="culture">
            Oggetto CultureInfo che fornisce informazioni sul confronto specifiche delle impostazioni cultura.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è diverso da <paramref name="expected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean)">
            <summary>
            Verifica se le stringhe specificate sono diverse e genera un'eccezione
            se sono uguali. Per il confronto vengono usate le impostazioni cultura inglese non dipendenti da paese/area geografica.
            </summary>
            <param name="notExpected">
            Prima stringa da confrontare. Questa è la stringa che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconda stringa da confrontare. Si tratta della stringa prodotta dal codice sottoposto a test.
            </param>
            <param name="ignoreCase">
            Valore booleano che indica un confronto con o senza distinzione tra maiuscole e minuscole. True
            indica un confronto senza distinzione tra maiuscole e minuscole.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Verifica se le stringhe specificate sono diverse e genera un'eccezione
            se sono uguali. Per il confronto vengono usate le impostazioni cultura inglese non dipendenti da paese/area geografica.
            </summary>
            <param name="notExpected">
            Prima stringa da confrontare. Questa è la stringa che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconda stringa da confrontare. Si tratta della stringa prodotta dal codice sottoposto a test.
            </param>
            <param name="ignoreCase">
            Valore booleano che indica un confronto con o senza distinzione tra maiuscole e minuscole. True
            indica un confronto senza distinzione tra maiuscole e minuscole.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Verifica se le stringhe specificate sono diverse e genera un'eccezione
            se sono uguali. Per il confronto vengono usate le impostazioni cultura inglese non dipendenti da paese/area geografica.
            </summary>
            <param name="notExpected">
            Prima stringa da confrontare. Questa è la stringa che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconda stringa da confrontare. Si tratta della stringa prodotta dal codice sottoposto a test.
            </param>
            <param name="ignoreCase">
            Valore booleano che indica un confronto con o senza distinzione tra maiuscole e minuscole. True
            indica un confronto senza distinzione tra maiuscole e minuscole.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Verifica se le stringhe specificate sono diverse e genera un'eccezione
            se sono uguali.
            </summary>
            <param name="notExpected">
            Prima stringa da confrontare. Questa è la stringa che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconda stringa da confrontare. Si tratta della stringa prodotta dal codice sottoposto a test.
            </param>
            <param name="ignoreCase">
            Valore booleano che indica un confronto con o senza distinzione tra maiuscole e minuscole. True
            indica un confronto senza distinzione tra maiuscole e minuscole.
            </param>
            <param name="culture">
            Oggetto CultureInfo che fornisce informazioni sul confronto specifiche delle impostazioni cultura.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Verifica se le stringhe specificate sono diverse e genera un'eccezione
            se sono uguali.
            </summary>
            <param name="notExpected">
            Prima stringa da confrontare. Questa è la stringa che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconda stringa da confrontare. Si tratta della stringa prodotta dal codice sottoposto a test.
            </param>
            <param name="ignoreCase">
            Valore booleano che indica un confronto con o senza distinzione tra maiuscole e minuscole. True
            indica un confronto senza distinzione tra maiuscole e minuscole.
            </param>
            <param name="culture">
            Oggetto CultureInfo che fornisce informazioni sul confronto specifiche delle impostazioni cultura.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Verifica se le stringhe specificate sono diverse e genera un'eccezione
            se sono uguali.
            </summary>
            <param name="notExpected">
            Prima stringa da confrontare. Questa è la stringa che il test presuppone
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconda stringa da confrontare. Si tratta della stringa prodotta dal codice sottoposto a test.
            </param>
            <param name="ignoreCase">
            Valore booleano che indica un confronto con o senza distinzione tra maiuscole e minuscole. True
            indica un confronto senza distinzione tra maiuscole e minuscole.
            </param>
            <param name="culture">
            Oggetto CultureInfo che fornisce informazioni sul confronto specifiche delle impostazioni cultura.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type)">
            <summary>
            Verifica se l'oggetto specificato è un'istanza del tipo previsto
            e genera un'eccezione se il tipo previsto non è incluso nella
            gerarchia di ereditarietà dell'oggetto.
            </summary>
            <param name="value">
            Oggetto che il test presuppone sia del tipo specificato.
            </param>
            <param name="expectedType">
            Tipo previsto di <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Verifica se l'oggetto specificato è un'istanza del tipo previsto
            e genera un'eccezione se il tipo previsto non è incluso nella
            gerarchia di ereditarietà dell'oggetto.
            </summary>
            <param name="value">
            Oggetto che il test presuppone sia del tipo specificato.
            </param>
            <param name="expectedType">
            Tipo previsto di <paramref name="value"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="value"/>
            non è un'istanza di <paramref name="expectedType"/>. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Verifica se l'oggetto specificato è un'istanza del tipo previsto
            e genera un'eccezione se il tipo previsto non è incluso nella
            gerarchia di ereditarietà dell'oggetto.
            </summary>
            <param name="value">
            Oggetto che il test presuppone sia del tipo specificato.
            </param>
            <param name="expectedType">
            Tipo previsto di <paramref name="value"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="value"/>
            non è un'istanza di <paramref name="expectedType"/>. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type)">
            <summary>
            Verifica se l'oggetto specificato non è un'istanza del tipo errato
            e genera un'eccezione se il tipo specificato è incluso nella
            gerarchia di ereditarietà dell'oggetto.
            </summary>
            <param name="value">
            Oggetto che il test presuppone non sia del tipo specificato.
            </param>
            <param name="wrongType">
            Tipo che <paramref name="value"/> non dovrebbe essere.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Verifica se l'oggetto specificato non è un'istanza del tipo errato
            e genera un'eccezione se il tipo specificato è incluso nella
            gerarchia di ereditarietà dell'oggetto.
            </summary>
            <param name="value">
            Oggetto che il test presuppone non sia del tipo specificato.
            </param>
            <param name="wrongType">
            Tipo che <paramref name="value"/> non dovrebbe essere.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="value"/>
            è un'istanza di <paramref name="wrongType"/>. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Verifica se l'oggetto specificato non è un'istanza del tipo errato
            e genera un'eccezione se il tipo specificato è incluso nella
            gerarchia di ereditarietà dell'oggetto.
            </summary>
            <param name="value">
            Oggetto che il test presuppone non sia del tipo specificato.
            </param>
            <param name="wrongType">
            Tipo che <paramref name="value"/> non dovrebbe essere.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="value"/>
            è un'istanza di <paramref name="wrongType"/>. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail">
            <summary>
            Genera un'eccezione AssertFailedException.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String)">
            <summary>
            Genera un'eccezione AssertFailedException.
            </summary>
            <param name="message">
            Messaggio da includere nell'eccezione. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String,System.Object[])">
            <summary>
            Genera un'eccezione AssertFailedException.
            </summary>
            <param name="message">
            Messaggio da includere nell'eccezione. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive">
            <summary>
            Genera un'eccezione AssertInconclusiveException.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String)">
            <summary>
            Genera un'eccezione AssertInconclusiveException.
            </summary>
            <param name="message">
            Messaggio da includere nell'eccezione. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String,System.Object[])">
            <summary>
            Genera un'eccezione AssertInconclusiveException.
            </summary>
            <param name="message">
            Messaggio da includere nell'eccezione. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Equals(System.Object,System.Object)">
            <summary>
            Gli overload di uguaglianza statici vengono usati per confrontare istanze di due tipi e stabilire se
            i riferimenti sono uguali. Questo metodo <b>non</b> deve essere usato per il confronto di uguaglianza tra due
            istanze. Questo oggetto verrà <b>sempre</b> generato con Assert.Fail. Usare
            Assert.AreEqual e gli overload associati negli unit test.
            </summary>
            <param name="objA"> Oggetto A </param>
            <param name="objB"> Oggetto B </param>
            <returns> Sempre false. </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action)">
            <summary>
            Verifica se il codice specificato dal delegato <paramref name="action"/> genera l'esatta eccezione specificata di tipo <typeparamref name="T"/> (e non di tipo derivato)
            e genera l'eccezione
            <code>
            AssertFailedException
            </code>
             se il codice non genera l'eccezione oppure genera un'eccezione di tipo diverso da <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegato per il codice da testare e che dovrebbe generare l'eccezione.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Tipo di eccezione che dovrebbe essere generata.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String)">
            <summary>
            Verifica se il codice specificato dal delegato <paramref name="action"/> genera l'esatta eccezione specificata di tipo <typeparamref name="T"/> (e non di tipo derivato)
            e genera l'eccezione
            <code>
            AssertFailedException
            </code>
             se il codice non genera l'eccezione oppure genera un'eccezione di tipo diverso da <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegato per il codice da testare e che dovrebbe generare l'eccezione.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="action"/>
            non genera l'eccezione di tipo <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Tipo di eccezione che dovrebbe essere generata.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object})">
            <summary>
            Verifica se il codice specificato dal delegato <paramref name="action"/> genera l'esatta eccezione specificata di tipo <typeparamref name="T"/> (e non di tipo derivato)
            e genera l'eccezione
            <code>
            AssertFailedException
            </code>
             se il codice non genera l'eccezione oppure genera un'eccezione di tipo diverso da <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegato per il codice da testare e che dovrebbe generare l'eccezione.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Tipo di eccezione che dovrebbe essere generata.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String)">
            <summary>
            Verifica se il codice specificato dal delegato <paramref name="action"/> genera l'esatta eccezione specificata di tipo <typeparamref name="T"/> (e non di tipo derivato)
            e genera l'eccezione
            <code>
            AssertFailedException
            </code>
             se il codice non genera l'eccezione oppure genera un'eccezione di tipo diverso da <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegato per il codice da testare e che dovrebbe generare l'eccezione.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="action"/>
            non genera l'eccezione di tipo <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Tipo di eccezione che dovrebbe essere generata.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String,System.Object[])">
            <summary>
            Verifica se il codice specificato dal delegato <paramref name="action"/> genera l'esatta eccezione specificata di tipo <typeparamref name="T"/> (e non di tipo derivato)
            e genera l'eccezione
            <code>
            AssertFailedException
            </code>
             se il codice non genera l'eccezione oppure genera un'eccezione di tipo diverso da <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegato per il codice da testare e che dovrebbe generare l'eccezione.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="action"/>
            non genera l'eccezione di tipo <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throw exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Tipo di eccezione che dovrebbe essere generata.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String,System.Object[])">
            <summary>
            Verifica se il codice specificato dal delegato <paramref name="action"/> genera l'esatta eccezione specificata di tipo <typeparamref name="T"/> (e non di tipo derivato)
            e genera l'eccezione
            <code>
            AssertFailedException
            </code>
             se il codice non genera l'eccezione oppure genera un'eccezione di tipo diverso da <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegato per il codice da testare e che dovrebbe generare l'eccezione.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="action"/>
            non genera l'eccezione di tipo <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Tipo di eccezione che dovrebbe essere generata.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Verifica se il codice specificato dal delegato <paramref name="action"/> genera l'esatta eccezione specificata di tipo <typeparamref name="T"/> (e non di tipo derivato)
            e genera l'eccezione
            <code>
            AssertFailedException
            </code>
             se il codice non genera l'eccezione oppure genera un'eccezione di tipo diverso da <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Delegato per il codice da testare e che dovrebbe generare l'eccezione.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
             <see cref="T:System.Threading.Tasks.Task"/> che esegue il delegato.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            Verifica se il codice specificato dal delegato <paramref name="action"/> genera l'esatta eccezione specificata di tipo <typeparamref name="T"/> (e non di tipo derivato)
            e genera <code> l'eccezione AssertFailedException</code> se il codice non genera l'eccezione oppure genera un'eccezione di tipo diverso da <typeparamref name="T"/>.
            </summary>
            <param name="action">Delegato per il codice da testare e che dovrebbe generare l'eccezione.</param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="action"/>
            non genera l'eccezione di tipo <typeparamref name="T"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
             <see cref="T:System.Threading.Tasks.Task"/> che esegue il delegato.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String,System.Object[])">
            <summary>
            Verifica se il codice specificato dal delegato <paramref name="action"/> genera l'esatta eccezione specificata di tipo <typeparamref name="T"/> (e non di tipo derivato)
            e genera <code> l'eccezione AssertFailedException</code> se il codice non genera l'eccezione oppure genera un'eccezione di tipo diverso da <typeparamref name="T"/>.
            </summary>
            <param name="action">Delegato per il codice da testare e che dovrebbe generare l'eccezione.</param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="action"/>
            non genera l'eccezione di tipo <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
             <see cref="T:System.Threading.Tasks.Task"/> che esegue il delegato.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNullChars(System.String)">
            <summary>
            Sostituisce caratteri Null ('\0') con "\\0".
            </summary>
            <param name="input">
            Stringa da cercare.
            </param>
            <returns>
            Stringa convertita con caratteri Null sostituiti da "\\0".
            </returns>
            <remarks>
            This is only public and still present to preserve compatibility with the V1 framework.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.HandleFail(System.String,System.String,System.Object[])">
            <summary>
            Funzione helper che crea e genera un'eccezione AssertionFailedException
            </summary>
            <param name="assertionName">
            nome dell'asserzione che genera un'eccezione
            </param>
            <param name="message">
            messaggio che descrive le condizioni per l'errore di asserzione
            </param>
            <param name="parameters">
            Parametri.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.CheckParameterNotNull(System.Object,System.String,System.String,System.String,System.Object[])">
            <summary>
            Verifica la validità delle condizioni nel parametro
            </summary>
            <param name="param">
            Parametro.
            </param>
            <param name="assertionName">
            Nome dell'asserzione.
            </param>
            <param name="parameterName">
            nome del parametro
            </param>
            <param name="message">
            messaggio per l'eccezione di parametro non valido
            </param>
            <param name="parameters">
            Parametri.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNulls(System.Object)">
            <summary>
            Converte in modo sicuro un oggetto in una stringa, gestendo valori e caratteri Null.
            I valori Null vengono convertiti in "(null)". I caratteri Null vengono convertiti in "\\0".
            </summary>
            <param name="input">
            Oggetto da convertire in una stringa.
            </param>
            <returns>
            Stringa convertita.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert">
            <summary>
            Asserzione della stringa.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.That">
            <summary>
            Ottiene l'istanza singleton della funzionalità CollectionAssert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void ContainsWords(this StringAssert cusomtAssert, string value, ICollection substrings)"
            Users could then use a syntax similar to the default assertions which in this case is "StringAssert.That.ContainsWords(value, substrings);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String)">
            <summary>
            Verifica se la stringa specificata contiene la sottostringa specificata
            e genera un'eccezione se la sottostringa non è presente nella
            stringa di test.
            </summary>
            <param name="value">
            Stringa che dovrebbe contenere <paramref name="substring"/>.
            </param>
            <param name="substring">
            Stringa che dovrebbe essere presente in <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String)">
            <summary>
            Verifica se la stringa specificata contiene la sottostringa specificata
            e genera un'eccezione se la sottostringa non è presente nella
            stringa di test.
            </summary>
            <param name="value">
            Stringa che dovrebbe contenere <paramref name="substring"/>.
            </param>
            <param name="substring">
            Stringa che dovrebbe essere presente in <paramref name="value"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="substring"/>
            non è contenuto in <paramref name="value"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String,System.Object[])">
            <summary>
            Verifica se la stringa specificata contiene la sottostringa specificata
            e genera un'eccezione se la sottostringa non è presente nella
            stringa di test.
            </summary>
            <param name="value">
            Stringa che dovrebbe contenere <paramref name="substring"/>.
            </param>
            <param name="substring">
            Stringa che dovrebbe essere presente in <paramref name="value"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="substring"/>
            non è contenuto in <paramref name="value"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String)">
            <summary>
            Verifica se la stringa specificata inizia con la sottostringa specificata
            e genera un'eccezione se la stringa di test non inizia con
            la sottostringa.
            </summary>
            <param name="value">
            Stringa che dovrebbe iniziare con <paramref name="substring"/>.
            </param>
            <param name="substring">
            Stringa che dovrebbe essere un prefisso di <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String)">
            <summary>
            Verifica se la stringa specificata inizia con la sottostringa specificata
            e genera un'eccezione se la stringa di test non inizia con
            la sottostringa.
            </summary>
            <param name="value">
            Stringa che dovrebbe iniziare con <paramref name="substring"/>.
            </param>
            <param name="substring">
            Stringa che dovrebbe essere un prefisso di <paramref name="value"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="value"/>
            non inizia con <paramref name="substring"/>. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Verifica se la stringa specificata inizia con la sottostringa specificata
            e genera un'eccezione se la stringa di test non inizia con
            la sottostringa.
            </summary>
            <param name="value">
            Stringa che dovrebbe iniziare con <paramref name="substring"/>.
            </param>
            <param name="substring">
            Stringa che dovrebbe essere un prefisso di <paramref name="value"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="value"/>
            non inizia con <paramref name="substring"/>. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String)">
            <summary>
            Verifica se la stringa specificata termina con la sottostringa specificata
            e genera un'eccezione se la stringa di test non termina con
            la sottostringa.
            </summary>
            <param name="value">
            Stringa che dovrebbe terminare con <paramref name="substring"/>.
            </param>
            <param name="substring">
            Stringa che dovrebbe essere un suffisso di <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String)">
            <summary>
            Verifica se la stringa specificata termina con la sottostringa specificata
            e genera un'eccezione se la stringa di test non termina con
            la sottostringa.
            </summary>
            <param name="value">
            Stringa che dovrebbe terminare con <paramref name="substring"/>.
            </param>
            <param name="substring">
            Stringa che dovrebbe essere un suffisso di <paramref name="value"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="value"/>
            non termina con <paramref name="substring"/>. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Verifica se la stringa specificata termina con la sottostringa specificata
            e genera un'eccezione se la stringa di test non termina con
            la sottostringa.
            </summary>
            <param name="value">
            Stringa che dovrebbe terminare con <paramref name="substring"/>.
            </param>
            <param name="substring">
            Stringa che dovrebbe essere un suffisso di <paramref name="value"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="value"/>
            non termina con <paramref name="substring"/>. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Verifica se la stringa specificata corrisponde a un'espressione regolare e
            genera un'eccezione se non corrisponde.
            </summary>
            <param name="value">
            Stringa che dovrebbe corrispondere a <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Espressione regolare a cui <paramref name="value"/> dovrebbe
            corrispondere.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Verifica se la stringa specificata corrisponde a un'espressione regolare e
            genera un'eccezione se non corrisponde.
            </summary>
            <param name="value">
            Stringa che dovrebbe corrispondere a <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Espressione regolare a cui <paramref name="value"/> dovrebbe
            corrispondere.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="value"/>
            non corrisponde a <paramref name="pattern"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Verifica se la stringa specificata corrisponde a un'espressione regolare e
            genera un'eccezione se non corrisponde.
            </summary>
            <param name="value">
            Stringa che dovrebbe corrispondere a <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Espressione regolare a cui <paramref name="value"/> dovrebbe
            corrispondere.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="value"/>
            non corrisponde a <paramref name="pattern"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Verifica se la stringa specificata non corrisponde a un'espressione regolare e
            genera un'eccezione se corrisponde.
            </summary>
            <param name="value">
            Stringa che non dovrebbe corrispondere a <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Espressione regolare a cui <paramref name="value"/> non
            dovrebbe corrispondere.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Verifica se la stringa specificata non corrisponde a un'espressione regolare e
            genera un'eccezione se corrisponde.
            </summary>
            <param name="value">
            Stringa che non dovrebbe corrispondere a <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Espressione regolare a cui <paramref name="value"/> non
            dovrebbe corrispondere.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="value"/>
            corrisponde a <paramref name="pattern"/>. Il messaggio viene visualizzato nei risultati
            del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Verifica se la stringa specificata non corrisponde a un'espressione regolare e
            genera un'eccezione se corrisponde.
            </summary>
            <param name="value">
            Stringa che non dovrebbe corrispondere a <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Espressione regolare a cui <paramref name="value"/> non
            dovrebbe corrispondere.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="value"/>
            corrisponde a <paramref name="pattern"/>. Il messaggio viene visualizzato nei risultati
            del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert">
            <summary>
            Raccolta di classi helper per testare diverse condizioni associate
            alle raccolte negli unit test. Se la condizione da testare non viene
            soddisfatta, viene generata un'eccezione.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.That">
            <summary>
            Ottiene l'istanza singleton della funzionalità CollectionAssert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void AreEqualUnordered(this CollectionAssert cusomtAssert, ICollection expected, ICollection actual)"
            Users could then use a syntax similar to the default assertions which in this case is "CollectionAssert.That.AreEqualUnordered(list1, list2);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object)">
            <summary>
            Verifica se la raccolta specificata contiene l'elemento specificato
            e genera un'eccezione se l'elemento non è presente nella raccolta.
            </summary>
            <param name="collection">
            Raccolta in cui cercare l'elemento.
            </param>
            <param name="element">
            Elemento che dovrebbe essere presente nella raccolta.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Verifica se la raccolta specificata contiene l'elemento specificato
            e genera un'eccezione se l'elemento non è presente nella raccolta.
            </summary>
            <param name="collection">
            Raccolta in cui cercare l'elemento.
            </param>
            <param name="element">
            Elemento che dovrebbe essere presente nella raccolta.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="element"/>
            non è contenuto in <paramref name="collection"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Verifica se la raccolta specificata contiene l'elemento specificato
            e genera un'eccezione se l'elemento non è presente nella raccolta.
            </summary>
            <param name="collection">
            Raccolta in cui cercare l'elemento.
            </param>
            <param name="element">
            Elemento che dovrebbe essere presente nella raccolta.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="element"/>
            non è contenuto in <paramref name="collection"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object)">
            <summary>
            Verifica se la raccolta specificata non contiene l'elemento
            specificato e genera un'eccezione se l'elemento è presente nella raccolta.
            </summary>
            <param name="collection">
            Raccolta in cui cercare l'elemento.
            </param>
            <param name="element">
            Elemento che non dovrebbe essere presente nella raccolta.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Verifica se la raccolta specificata non contiene l'elemento
            specificato e genera un'eccezione se l'elemento è presente nella raccolta.
            </summary>
            <param name="collection">
            Raccolta in cui cercare l'elemento.
            </param>
            <param name="element">
            Elemento che non dovrebbe essere presente nella raccolta.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="element"/>
            è presente in <paramref name="collection"/>. Il messaggio viene visualizzato nei risultati
            del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Verifica se la raccolta specificata non contiene l'elemento
            specificato e genera un'eccezione se l'elemento è presente nella raccolta.
            </summary>
            <param name="collection">
            Raccolta in cui cercare l'elemento.
            </param>
            <param name="element">
            Elemento che non dovrebbe essere presente nella raccolta.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="element"/>
            è presente in <paramref name="collection"/>. Il messaggio viene visualizzato nei risultati
            del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection)">
            <summary>
            Verifica se tutti gli elementi della raccolta specificata sono non Null e genera
            un'eccezione se un qualsiasi elemento è Null.
            </summary>
            <param name="collection">
            Raccolta in cui cercare gli elementi Null.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String)">
            <summary>
            Verifica se tutti gli elementi della raccolta specificata sono non Null e genera
            un'eccezione se un qualsiasi elemento è Null.
            </summary>
            <param name="collection">
            Raccolta in cui cercare gli elementi Null.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="collection"/>
            contiene un elemento Null. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Verifica se tutti gli elementi della raccolta specificata sono non Null e genera
            un'eccezione se un qualsiasi elemento è Null.
            </summary>
            <param name="collection">
            Raccolta in cui cercare gli elementi Null.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="collection"/>
            contiene un elemento Null. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection)">
            <summary>
            Verifica se tutti gli elementi della raccolta specificata sono univoci o meno
            e genera un'eccezione se due elementi qualsiasi della raccolta sono uguali.
            </summary>
            <param name="collection">
            Raccolta in cui cercare gli elementi duplicati.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String)">
            <summary>
            Verifica se tutti gli elementi della raccolta specificata sono univoci o meno
            e genera un'eccezione se due elementi qualsiasi della raccolta sono uguali.
            </summary>
            <param name="collection">
            Raccolta in cui cercare gli elementi duplicati.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="collection"/>
            contiene almeno un elemento duplicato. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Verifica se tutti gli elementi della raccolta specificata sono univoci o meno
            e genera un'eccezione se due elementi qualsiasi della raccolta sono uguali.
            </summary>
            <param name="collection">
            Raccolta in cui cercare gli elementi duplicati.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="collection"/>
            contiene almeno un elemento duplicato. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Verifica se una raccolta è un subset di un'altra raccolta e
            genera un'eccezione se un qualsiasi elemento nel subset non è presente anche
            nel superset.
            </summary>
            <param name="subset">
            Raccolta che dovrebbe essere un subset di <paramref name="superset"/>.
            </param>
            <param name="superset">
            Raccolta che dovrebbe essere un superset di <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Verifica se una raccolta è un subset di un'altra raccolta e
            genera un'eccezione se un qualsiasi elemento nel subset non è presente anche
            nel superset.
            </summary>
            <param name="subset">
            Raccolta che dovrebbe essere un subset di <paramref name="superset"/>.
            </param>
            <param name="superset">
            Raccolta che dovrebbe essere un superset di <paramref name="subset"/>
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando un elemento in
            <paramref name="subset"/> non è presente in <paramref name="superset"/>.
            Il messaggio viene visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Verifica se una raccolta è un subset di un'altra raccolta e
            genera un'eccezione se un qualsiasi elemento nel subset non è presente anche
            nel superset.
            </summary>
            <param name="subset">
            Raccolta che dovrebbe essere un subset di <paramref name="superset"/>.
            </param>
            <param name="superset">
            Raccolta che dovrebbe essere un superset di <paramref name="subset"/>
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando un elemento in
            <paramref name="subset"/> non è presente in <paramref name="superset"/>.
            Il messaggio viene visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Verifica se una raccolta non è un subset di un'altra raccolta e
            genera un'eccezione se tutti gli elementi nel subset sono presenti anche
            nel superset.
            </summary>
            <param name="subset">
            Raccolta che non dovrebbe essere un subset di <paramref name="superset"/>.
            </param>
            <param name="superset">
            Raccolta che non dovrebbe essere un superset di <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Verifica se una raccolta non è un subset di un'altra raccolta e
            genera un'eccezione se tutti gli elementi nel subset sono presenti anche
            nel superset.
            </summary>
            <param name="subset">
            Raccolta che non dovrebbe essere un subset di <paramref name="superset"/>.
            </param>
            <param name="superset">
            Raccolta che non dovrebbe essere un superset di <paramref name="subset"/>
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando ogni elemento in
            <paramref name="subset"/> è presente anche in <paramref name="superset"/>.
            Il messaggio viene visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Verifica se una raccolta non è un subset di un'altra raccolta e
            genera un'eccezione se tutti gli elementi nel subset sono presenti anche
            nel superset.
            </summary>
            <param name="subset">
            Raccolta che non dovrebbe essere un subset di <paramref name="superset"/>.
            </param>
            <param name="superset">
            Raccolta che non dovrebbe essere un superset di <paramref name="subset"/>
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando ogni elemento in
            <paramref name="subset"/> è presente anche in <paramref name="superset"/>.
            Il messaggio viene visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Verifica se due raccolte contengono gli stessi elementi e genera
            un'eccezione se una delle raccolte contiene un elemento non presente
            nell'altra.
            </summary>
            <param name="expected">
            Prima raccolta da confrontare. Contiene gli elementi previsti dal
            test.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Verifica se due raccolte contengono gli stessi elementi e genera
            un'eccezione se una delle raccolte contiene un elemento non presente
            nell'altra.
            </summary>
            <param name="expected">
            Prima raccolta da confrontare. Contiene gli elementi previsti dal
            test.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando un elemento viene trovato
            in una delle raccolte ma non nell'altra. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Verifica se due raccolte contengono gli stessi elementi e genera
            un'eccezione se una delle raccolte contiene un elemento non presente
            nell'altra.
            </summary>
            <param name="expected">
            Prima raccolta da confrontare. Contiene gli elementi previsti dal
            test.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando un elemento viene trovato
            in una delle raccolte ma non nell'altra. Il messaggio viene
            visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Verifica se due raccolte contengono elementi diversi e genera
            un'eccezione se le raccolte contengono gli stessi elementi senza
            considerare l'ordine.
            </summary>
            <param name="expected">
            Prima raccolta da confrontare. Contiene gli elementi che il test
            prevede siano diversi rispetto alla raccolta effettiva.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Verifica se due raccolte contengono elementi diversi e genera
            un'eccezione se le raccolte contengono gli stessi elementi senza
            considerare l'ordine.
            </summary>
            <param name="expected">
            Prima raccolta da confrontare. Contiene gli elementi che il test
            prevede siano diversi rispetto alla raccolta effettiva.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            contiene gli stessi elementi di <paramref name="expected"/>. Il messaggio
            viene visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Verifica se due raccolte contengono elementi diversi e genera
            un'eccezione se le raccolte contengono gli stessi elementi senza
            considerare l'ordine.
            </summary>
            <param name="expected">
            Prima raccolta da confrontare. Contiene gli elementi che il test
            prevede siano diversi rispetto alla raccolta effettiva.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            contiene gli stessi elementi di <paramref name="expected"/>. Il messaggio
            viene visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type)">
            <summary>
            Verifica se tutti gli elementi della raccolta specificata sono istanze
            del tipo previsto e genera un'eccezione se il tipo previsto non
            è presente nella gerarchia di ereditarietà di uno o più elementi.
            </summary>
            <param name="collection">
            Raccolta contenente elementi che il test presuppone siano del
            tipo specificato.
            </param>
            <param name="expectedType">
            Tipo previsto di ogni elemento di <paramref name="collection"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String)">
            <summary>
            Verifica se tutti gli elementi della raccolta specificata sono istanze
            del tipo previsto e genera un'eccezione se il tipo previsto non
            è presente nella gerarchia di ereditarietà di uno o più elementi.
            </summary>
            <param name="collection">
            Raccolta contenente elementi che il test presuppone siano del
            tipo specificato.
            </param>
            <param name="expectedType">
            Tipo previsto di ogni elemento di <paramref name="collection"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando un elemento in
            <paramref name="collection"/> non è un'istanza di
            <paramref name="expectedType"/>. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String,System.Object[])">
            <summary>
            Verifica se tutti gli elementi della raccolta specificata sono istanze
            del tipo previsto e genera un'eccezione se il tipo previsto non
            è presente nella gerarchia di ereditarietà di uno o più elementi.
            </summary>
            <param name="collection">
            Raccolta contenente elementi che il test presuppone siano del
            tipo specificato.
            </param>
            <param name="expectedType">
            Tipo previsto di ogni elemento di <paramref name="collection"/>.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando un elemento in
            <paramref name="collection"/> non è un'istanza di
            <paramref name="expectedType"/>. Il messaggio viene visualizzato nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Verifica se le due raccolte specificate sono uguali e genera un'eccezione
            se sono diverse. Per uguaglianza si intende che le raccolte
            contengono gli stessi elementi nello stesso ordine e nella stessa quantità. 
            Riferimenti diversi allo stesso valore vengono considerati uguali.
            </summary>
            <param name="expected">
            Prima raccolta da confrontare. Questa è la raccolta prevista dai test.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Verifica se le due raccolte specificate sono uguali e genera un'eccezione
            se sono diverse. Per uguaglianza si intende che le raccolte
            contengono gli stessi elementi nello stesso ordine e nella stessa quantità. 
            Riferimenti diversi allo stesso valore vengono considerati uguali.
            </summary>
            <param name="expected">
            Prima raccolta da confrontare. Questa è la raccolta prevista dai test.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è diverso da <paramref name="expected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Verifica se le due raccolte specificate sono uguali e genera un'eccezione
            se sono diverse. Per uguaglianza si intende che le raccolte
            contengono gli stessi elementi nello stesso ordine e nella stessa quantità. 
            Riferimenti diversi allo stesso valore vengono considerati uguali.
            </summary>
            <param name="expected">
            Prima raccolta da confrontare. Questa è la raccolta prevista dai test.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è diverso da <paramref name="expected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Verifica se le due raccolte specificate sono diverse e genera un'eccezione
            se sono uguali. Per uguaglianza si intende che le raccolte
            contengono gli stessi elementi nello stesso ordine e nella stessa quantità. 
            Riferimenti diversi allo stesso valore vengono considerati uguali.
            </summary>
            <param name="notExpected">
            Prima raccolta da confrontare. Questa è la raccolta che i test presuppongono
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Verifica se le due raccolte specificate sono diverse e genera un'eccezione
            se sono uguali. Per uguaglianza si intende che le raccolte
            contengono gli stessi elementi nello stesso ordine e nella stessa quantità. 
            Riferimenti diversi allo stesso valore vengono considerati uguali.
            </summary>
            <param name="notExpected">
            Prima raccolta da confrontare. Questa è la raccolta che i test presuppongono
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Verifica se le due raccolte specificate sono diverse e genera un'eccezione
            se sono uguali. Per uguaglianza si intende che le raccolte
            contengono gli stessi elementi nello stesso ordine e nella stessa quantità. 
            Riferimenti diversi allo stesso valore vengono considerati uguali.
            </summary>
            <param name="notExpected">
            Prima raccolta da confrontare. Questa è la raccolta che i test presuppongono
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Verifica se le due raccolte specificate sono uguali e genera un'eccezione
            se sono diverse. Per uguaglianza si intende che le raccolte
            contengono gli stessi elementi nello stesso ordine e nella stessa quantità. 
            Riferimenti diversi allo stesso valore vengono considerati uguali.
            </summary>
            <param name="expected">
            Prima raccolta da confrontare. Questa è la raccolta prevista dai test.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <param name="comparer">
            Implementazione di compare da usare quando si confrontano elementi della raccolta.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Verifica se le due raccolte specificate sono uguali e genera un'eccezione
            se sono diverse. Per uguaglianza si intende che le raccolte
            contengono gli stessi elementi nello stesso ordine e nella stessa quantità. 
            Riferimenti diversi allo stesso valore vengono considerati uguali.
            </summary>
            <param name="expected">
            Prima raccolta da confrontare. Questa è la raccolta prevista dai test.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <param name="comparer">
            Implementazione di compare da usare quando si confrontano elementi della raccolta.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è diverso da <paramref name="expected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Verifica se le due raccolte specificate sono uguali e genera un'eccezione
            se sono diverse. Per uguaglianza si intende che le raccolte
            contengono gli stessi elementi nello stesso ordine e nella stessa quantità. 
            Riferimenti diversi allo stesso valore vengono considerati uguali.
            </summary>
            <param name="expected">
            Prima raccolta da confrontare. Questa è la raccolta prevista dai test.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <param name="comparer">
            Implementazione di compare da usare quando si confrontano elementi della raccolta.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è diverso da <paramref name="expected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Verifica se le due raccolte specificate sono diverse e genera un'eccezione
            se sono uguali. Per uguaglianza si intende che le raccolte
            contengono gli stessi elementi nello stesso ordine e nella stessa quantità. 
            Riferimenti diversi allo stesso valore vengono considerati uguali.
            </summary>
            <param name="notExpected">
            Prima raccolta da confrontare. Questa è la raccolta che i test presuppongono
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <param name="comparer">
            Implementazione di compare da usare quando si confrontano elementi della raccolta.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Verifica se le due raccolte specificate sono diverse e genera un'eccezione
            se sono uguali. Per uguaglianza si intende che le raccolte
            contengono gli stessi elementi nello stesso ordine e nella stessa quantità. 
            Riferimenti diversi allo stesso valore vengono considerati uguali.
            </summary>
            <param name="notExpected">
            Prima raccolta da confrontare. Questa è la raccolta che i test presuppongono
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <param name="comparer">
            Implementazione di compare da usare quando si confrontano elementi della raccolta.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Verifica se le due raccolte specificate sono diverse e genera un'eccezione
            se sono uguali. Per uguaglianza si intende che le raccolte
            contengono gli stessi elementi nello stesso ordine e nella stessa quantità. 
            Riferimenti diversi allo stesso valore vengono considerati uguali.
            </summary>
            <param name="notExpected">
            Prima raccolta da confrontare. Questa è la raccolta che i test presuppongono
            non corrisponda a <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare. Si tratta della raccolta prodotta dal
            codice sottoposto a test.
            </param>
            <param name="comparer">
            Implementazione di compare da usare quando si confrontano elementi della raccolta.
            </param>
            <param name="message">
            Messaggio da includere nell'eccezione quando <paramref name="actual"/>
            è uguale a <paramref name="notExpected"/>. Il messaggio viene visualizzato
            nei risultati del test.
            </param>
            <param name="parameters">
            Matrice di parametri da usare quando si formatta <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOfHelper(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Determina se la prima raccolta è un subset della seconda raccolta.
            Se entrambi i set contengono elementi duplicati, il numero delle
            occorrenze dell'elemento nel subset deve essere minore o uguale
            a quello delle occorrenze nel superset.
            </summary>
            <param name="subset">
            Raccolta che il test presuppone debba essere contenuta in <paramref name="superset"/>.
            </param>
            <param name="superset">
            Raccolta che il test presuppone debba contenere <paramref name="subset"/>.
            </param>
            <returns>
            True se <paramref name="subset"/> è un subset di
            <paramref name="superset"/>; in caso contrario, false.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.GetElementCounts(System.Collections.ICollection,System.Int32@)">
            <summary>
            Costruisce un dizionario contenente il numero di occorrenze di ogni
            elemento nella raccolta specificata.
            </summary>
            <param name="collection">
            Raccolta da elaborare.
            </param>
            <param name="nullCount">
            Numero di elementi Null presenti nella raccolta.
            </param>
            <returns>
            Dizionario contenente il numero di occorrenze di ogni elemento
            nella raccolta specificata.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.FindMismatchedElement(System.Collections.ICollection,System.Collections.ICollection,System.Int32@,System.Int32@,System.Object@)">
            <summary>
            Trova un elemento senza corrispondenza tra le due raccolte. Per elemento
            senza corrispondenza si intende un elemento che appare nella raccolta prevista
            un numero di volte diverso rispetto alla raccolta effettiva. Si presuppone
            che le raccolte siano riferimenti non Null diversi con lo stesso
            numero di elementi. Il chiamante è responsabile di questo livello di
            verifica. Se non ci sono elementi senza corrispondenza, la funzione
            restituisce false e i parametri out non devono essere usati.
            </summary>
            <param name="expected">
            Prima raccolta da confrontare.
            </param>
            <param name="actual">
            Seconda raccolta da confrontare.
            </param>
            <param name="expectedCount">
            Numero previsto di occorrenze di
            <paramref name="mismatchedElement"/> o 0 se non ci sono elementi senza
            corrispondenza.
            </param>
            <param name="actualCount">
            Numero effettivo di occorrenze di
            <paramref name="mismatchedElement"/> o 0 se non ci sono elementi senza
            corrispondenza.
            </param>
            <param name="mismatchedElement">
            Elemento senza corrispondenza (può essere Null) o Null se non ci sono elementi
            senza corrispondenza.
            </param>
            <returns>
            true se è stato trovato un elemento senza corrispondenza; in caso contrario, false.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.ObjectComparer">
            <summary>
            confronta gli oggetti usando object.Equals
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException">
            <summary>
            Classe di base per le eccezioni del framework.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String,System.Exception)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
            <param name="msg"> Messaggio. </param>
            <param name="ex"> Eccezione. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
            <param name="msg"> Messaggio. </param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages">
            <summary>
              Classe di risorse fortemente tipizzata per la ricerca di stringhe localizzate e così via.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ResourceManager">
            <summary>
              Restituisce l'istanza di ResourceManager nella cache usata da questa classe.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Culture">
            <summary>
              Esegue l'override della proprietà CurrentUICulture del thread corrente per tutte
              le ricerche di risorse eseguite usando questa classe di risorse fortemente tipizzata.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AccessStringInvalidSyntax">
            <summary>
              Cerca una stringa localizzata simile a La sintassi della stringa di accesso non è valida.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ActualHasMismatchedElements">
            <summary>
              Cerca una stringa localizzata simile a La raccolta prevista contiene {1} occorrenza/e di &lt;{2}&gt;, mentre quella effettiva ne contiene {3}. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AllItemsAreUniqueFailMsg">
            <summary>
              Cerca una stringa localizzata simile a È stato trovato un elemento duplicato:&lt;{1}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualCaseFailMsg">
            <summary>
              Cerca una stringa localizzata simile a Il valore previsto è &lt;{1}&gt;, ma la combinazione di maiuscole/minuscole è diversa per il valore effettivo &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDeltaFailMsg">
            <summary>
              Cerca una stringa localizzata simile a È prevista una differenza minore di &lt;{3}&gt; tra il valore previsto &lt;{1}&gt; e il valore effettivo &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDifferentTypesFailMsg">
            <summary>
              Cerca una stringa localizzata simile a Valore previsto: &lt;{1} ({2})&gt;. Valore effettivo: &lt;{3} ({4})&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualFailMsg">
            <summary>
              Cerca una stringa localizzata simile a Valore previsto: &lt;{1}&gt;. Valore effettivo: &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualDeltaFailMsg">
            <summary>
              Cerca una stringa localizzata simile a È prevista una differenza maggiore di &lt;{3}&gt; tra il valore previsto &lt;{1}&gt; e il valore effettivo &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualFailMsg">
            <summary>
              Cerca una stringa localizzata simile a È previsto un valore qualsiasi eccetto &lt;{1}&gt;. Valore effettivo: &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreSameGivenValues">
            <summary>
              Cerca una stringa localizzata simile a Non passare tipi valore a AreSame(). I valori convertiti in Object non saranno mai uguali. Usare AreEqual(). {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AssertionFailed">
            <summary>
              Cerca una stringa localizzata simile a {0} non riuscita. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AsyncUITestMethodNotSupported">
            <summary>
              Cerca una stringa localizzata simile ad async TestMethod con UITestMethodAttribute non supportata. Rimuovere async o usare TestMethodAttribute.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsEmpty">
            <summary>
              Cerca una stringa localizzata simile a Le raccolte sono entrambe vuote. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameElements">
            <summary>
              Cerca una stringa localizzata simile a Le raccolte contengono entrambe gli stessi elementi.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameReference">
            <summary>
              Cerca una stringa localizzata simile a I riferimenti a raccolte puntano entrambi allo stesso oggetto Collection. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothSameElements">
            <summary>
              Cerca una stringa localizzata simile a Le raccolte contengono entrambe gli stessi elementi. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.CollectionEqualReason">
            <summary>
              Cerca una stringa localizzata simile a {0}({1}).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_NullInMessages">
            <summary>
              Cerca una stringa localizzata simile a (Null).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_ObjectString">
            <summary>
              Cerca una stringa localizzata simile a (oggetto).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ContainsFail">
            <summary>
              Cerca una stringa localizzata simile a La stringa '{0}' non contiene la stringa '{1}'. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DataDrivenResultDisplayName">
            <summary>
              Cerca una stringa localizzata simile a {0} ({1}).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DoNotUseAssertEquals">
            <summary>
              Cerca una stringa localizzata simile a Per le asserzioni non usare Assert.Equals, ma preferire Assert.AreEqual e gli overload.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementNumbersDontMatch">
            <summary>
              Cerca una stringa localizzata simile a Il numero di elementi nelle raccolte non corrisponde. Valore previsto: &lt;{1}&gt;. Valore effettivo: &lt;{2}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementsAtIndexDontMatch">
            <summary>
              Cerca una stringa localizzata simile a L'elemento alla posizione di indice {0} non corrisponde.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch">
            <summary>
              Cerca una stringa localizzata simile a L'elemento alla posizione di indice {1} non è del tipo previsto. Tipo previsto: &lt;{2}&gt;. Tipo effettivo: &lt;{3}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch2">
            <summary>
              Cerca una stringa localizzata simile a L'elemento alla posizione di indice {1} è (Null). Tipo previsto: &lt;{2}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EndsWithFail">
            <summary>
              Cerca una stringa localizzata simile a La stringa '{0}' non termina con la stringa '{1}'. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EqualsTesterInvalidArgs">
            <summary>
              Cerca una stringa localizzata simile a Argomento non valido: EqualsTester non può usare valori Null.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ErrorInvalidCast">
            <summary>
              Cerca una stringa localizzata simile a Non è possibile convertire un oggetto di tipo {0} in {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InternalObjectNotValid">
            <summary>
              Cerca una stringa localizzata simile a L'oggetto interno a cui si fa riferimento non è più valido.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidParameterToAssert">
            <summary>
              Cerca una stringa localizzata simile a Il parametro '{0}' non è valido. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidPropertyType">
            <summary>
              Cerca una stringa localizzata simile a Il tipo della proprietà {0} è {1}, ma quello previsto è {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsInstanceOfFailMsg">
            <summary>
              Cerca una stringa localizzata simile a Tipo previsto di {0}: &lt;{1}&gt;. Tipo effettivo: &lt;{2}&gt;.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsMatchFail">
            <summary>
              Cerca una stringa localizzata simile a La stringa '{0}' non corrisponde al criterio '{1}'. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotInstanceOfFailMsg">
            <summary>
              Cerca una stringa localizzata simile a Tipo errato: &lt;{1}&gt;. Tipo effettivo: &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotMatchFail">
            <summary>
              Cerca una stringa localizzata simile a La stringa '{0}' corrisponde al criterio '{1}'. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoDataRow">
            <summary>
              Cerca una stringa localizzata simile a Non è stato specificato alcun elemento DataRowAttribute. Con DataTestMethodAttribute è necessario almeno un elemento DataRowAttribute.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoExceptionThrown">
            <summary>
              Cerca una stringa localizzata simile a Non è stata generata alcuna eccezione. Era prevista un'eccezione {1}. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NullParameterToAssert">
            <summary>
              Cerca una stringa localizzata simile a Il parametro '{0}' non è valido. Il valore non può essere Null. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NumberOfElementsDiff">
            <summary>
              Cerca una stringa localizzata simile a Il numero di elementi è diverso.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorConstructorNotFound">
            <summary>
              Cerca una stringa localizzata simile a 
                 Il costruttore con la firma specificata non è stato trovato. Potrebbe essere necessario rigenerare la funzione di accesso privata
      oppure il membro potrebbe essere privato e definito per una classe di base. In quest'ultimo caso, è necessario passare il tipo
      che definisce il membro nel costruttore di PrivateObject.
               .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorMemberNotFound">
            <summary>
              Cerca una stringa localizzata simile a 
                 Il membro specificato ({0}) non è stato trovato. Potrebbe essere necessario rigenerare la funzione di accesso privata
      oppure il membro potrebbe essere privato e definito per una classe di base. In quest'ultimo caso, è necessario passare il tipo
      che definisce il membro nel costruttore di PrivateObject.
               .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.StartsWithFail">
            <summary>
              Cerca una stringa localizzata simile a La stringa '{0}' non inizia con la stringa '{1}'. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_ExpectedExceptionTypeMustDeriveFromException">
            <summary>
              Cerca una stringa localizzata simile a Il tipo di eccezione previsto deve essere System.Exception o un tipo derivato da System.Exception.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_FailedToGetExceptionMessage">
            <summary>
              Cerca una stringa localizzata simile a Non è stato possibile ottenere il messaggio per un'eccezione di tipo {0} a causa di un'eccezione.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoException">
            <summary>
              Cerca una stringa localizzata simile a Il metodo di test non ha generato l'eccezione prevista {0}. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoExceptionDefault">
            <summary>
              Cerca una stringa localizzata simile a Il metodo di test non ha generato un'eccezione. È prevista un'eccezione dall'attributo {0} definito nel metodo di test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongException">
            <summary>
              Cerca una stringa localizzata simile a Il metodo di test ha generato l'eccezione {0}, ma era prevista l'eccezione {1}. Messaggio dell'eccezione: {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongExceptionDerivedAllowed">
            <summary>
              Cerca una stringa localizzata simile a Il metodo di test ha generato l'eccezione {0}, ma era prevista l'eccezione {1} o un tipo derivato da essa. Messaggio dell'eccezione: {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.WrongExceptionThrown">
             <summary>
               Cerca una stringa localizzata simile a È stata generata l'eccezione {2}, ma era prevista un'eccezione {1}. {0}
            Messaggio dell'eccezione: {3}
            Analisi dello stack: {4}.
             </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome">
            <summary>
            risultati degli unit test
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Failed">
            <summary>
            Il test è stato eseguito, ma si sono verificati errori.
            Gli errori possono implicare eccezioni o asserzioni non riuscite.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Inconclusive">
            <summary>
            Il test è stato completato, ma non è possibile determinare se è stato o meno superato.
            Può essere usato per test interrotti.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Passed">
            <summary>
            Il test è stato eseguito senza problemi.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.InProgress">
            <summary>
            Il test è attualmente in corso.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Error">
            <summary>
            Si è verificato un errore di sistema durante il tentativo di eseguire un test.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Timeout">
            <summary>
            Timeout del test.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Aborted">
            <summary>
            Il test è stato interrotto dall'utente.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Unknown">
            <summary>
            Il test si trova in uno stato sconosciuto
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper">
            <summary>
            Fornisce la funzionalità di helper per il framework degli unit test
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper.GetExceptionMsg(System.Exception)">
            <summary>
            Ottiene i messaggi di eccezione in modo ricorsivo, inclusi quelli relativi a 
            tutte le eccezioni interne
            </summary>
            <param name="ex">Eccezione per cui ottenere i messaggi</param>
            <returns>stringa con le informazioni sul messaggio di errore</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout">
            <summary>
            Enumerazione per i timeout, che può essere usata con la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/>.
            Il tipo dell'enumerazione deve corrispondere
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout.Infinite">
            <summary>
            Valore infinito.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute">
            <summary>
            Attributo della classe di test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute.GetTestMethodAttribute(Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute)">
            <summary>
            Ottiene un attributo di metodo di test che consente di eseguire questo test.
            </summary>
            <param name="testMethodAttribute">Istanza di attributo del metodo di test definita in questo metodo.</param>
            <returns>Oggetto <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/> da usare per eseguire questo test.</returns>
            <remarks>Extensions can override this method to customize how all methods in a class are run.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute">
            <summary>
            Attributo del metodo di test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Esegue un metodo di test.
            </summary>
            <param name="testMethod">Metodo di test da eseguire.</param>
            <returns>Matrice di oggetti TestResult che rappresentano il risultato o i risultati del test.</returns>
            <remarks>Extensions can override this method to customize running a TestMethod.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestInitializeAttribute">
            <summary>
            Attributo di inizializzazione test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCleanupAttribute">
            <summary>
            Attributo di pulizia dei test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute">
            <summary>
            Attributo ignore.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute">
            <summary>
            Attributo della proprietà di test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.#ctor(System.String,System.String)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute"/>.
            </summary>
            <param name="name">
            Nome.
            </param>
            <param name="value">
            Valore.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Name">
            <summary>
            Ottiene il nome.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Value">
            <summary>
            Ottiene il valore.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute">
            <summary>
            Attributo di inizializzazione classi.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute">
            <summary>
            Attributo di pulizia delle classi.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyInitializeAttribute">
            <summary>
            Attributo di inizializzazione assembly.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyCleanupAttribute">
            <summary>
            Attributo di pulizia degli assembly.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute">
            <summary>
            Proprietario del test
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.#ctor(System.String)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute"/>.
            </summary>
            <param name="owner">
            Proprietario.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.Owner">
            <summary>
            Ottiene il proprietario.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute">
            <summary>
            Attributo Priority; usato per specificare la priorità di uno unit test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.#ctor(System.Int32)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute"/>.
            </summary>
            <param name="priority">
            Priorità.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.Priority">
            <summary>
            Ottiene la priorità.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute">
            <summary>
            Descrizione del test
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.#ctor(System.String)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute"/> per descrivere un test.
            </summary>
            <param name="description">Descrizione.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.Description">
            <summary>
            Ottiene la descrizione di un test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute">
            <summary>
            URI della struttura di progetto CSS
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.#ctor(System.String)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute"/> per l'URI della struttura di progetto CSS.
            </summary>
            <param name="cssProjectStructure">URI della struttura di progetto CSS.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.CssProjectStructure">
            <summary>
            Ottiene l'URI della struttura di progetto CSS.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute">
            <summary>
            URI dell'iterazione CSS
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.#ctor(System.String)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute"/> per l'URI dell'iterazione CSS.
            </summary>
            <param name="cssIteration">URI dell'iterazione CSS.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.CssIteration">
            <summary>
            Ottiene l'URI dell'iterazione CSS.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute">
            <summary>
            Attributo WorkItem; usato per specificare un elemento di lavoro associato a questo test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.#ctor(System.Int32)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute"/> per l'attributo WorkItem.
            </summary>
            <param name="id">ID di un elemento di lavoro.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.Id">
            <summary>
            Ottiene l'ID di un elemento di lavoro associato.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute">
            <summary>
            Attributo Timeout; usato per specificare il timeout di uno unit test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(System.Int32)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/>.
            </summary>
            <param name="timeout">
            Timeout.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> con un timeout preimpostato
            </summary>
            <param name="timeout">
            Timeout
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.Timeout">
            <summary>
            Ottiene il timeout.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult">
            <summary>
            Oggetto TestResult da restituire all'adattatore.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.#ctor">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DisplayName">
            <summary>
            Ottiene o imposta il nome visualizzato del risultato. Utile quando vengono restituiti più risultati.
            Se è Null, come nome visualizzato viene usato il nome del metodo.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Outcome">
            <summary>
            Ottiene o imposta il risultato dell'esecuzione dei test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestFailureException">
            <summary>
            Ottiene o imposta l'eccezione generata quando il test non viene superato.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogOutput">
            <summary>
            Ottiene o imposta l'output del messaggio registrato dal codice del test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogError">
            <summary>
            Ottiene o imposta l'output del messaggio registrato dal codice del test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DebugTrace">
            <summary>
            Ottiene o imposta le tracce di debug in base al codice del test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestContextMessages">
            <summary>
            Gets or sets the debug traces by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Duration">
            <summary>
            Ottiene o imposta la durata dell'esecuzione dei test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DatarowIndex">
            <summary>
            Ottiene o imposta l'indice della riga di dati nell'origine dati. Impostare solo per risultati di singole
            esecuzioni della riga di dati di un test basato sui dati.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ReturnValue">
            <summary>
            Ottiene o imposta il valore restituito del metodo di test. Attualmente è sempre Null.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ResultFiles">
            <summary>
            Ottiene o imposta i file di risultati allegati dal test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute">
            <summary>
            Specifica la stringa di connessione, il nome tabella e il metodo di accesso alle righe per test basati sui dati.
            </summary>
            <example>
            [DataSource("Provider=SQLOLEDB.1;Data Source=source;Integrated Security=SSPI;Initial Catalog=EqtCoverage;Persist Security Info=False", "MyTable")]
            [DataSource("dataSourceNameFromConfigFile")]
            </example>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultProviderName">
            <summary>
            Nome del provider predefinito per DataSource.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultDataAccessMethod">
            <summary>
            Metodo predefinito di accesso ai dati.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String,System.String,Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>. Questa istanza verrà inizializzata con un provider di dati, la stringa di connessione, la tabella dati e il metodo di accesso ai dati per accedere all'origine dati.
            </summary>
            <param name="providerInvariantName">Nome del provider di dati non dipendente da paese/area geografica, ad esempio System.Data.SqlClient</param>
            <param name="connectionString">
            Stringa di connessione specifica del provider di dati.
            AVVISO: la stringa di connessione può contenere dati sensibili, ad esempio una password.
            La stringa di connessione è archiviata in formato testo normale nel codice sorgente e nell'assembly compilato. 
            Limitare l'accesso al codice sorgente e all'assembly per proteggere questi dati sensibili.
            </param>
            <param name="tableName">Nome della tabella dati.</param>
            <param name="dataAccessMethod">Specifica l'ordine per l'accesso ai dati.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>. Questa istanza verrà inizializzata con una stringa di connessione e un nome tabella.
            Specificare la stringa di connessione e la tabella dati per accedere all'origine dati OLEDB.
            </summary>
            <param name="connectionString">
            Stringa di connessione specifica del provider di dati.
            AVVISO: la stringa di connessione può contenere dati sensibili, ad esempio una password.
            La stringa di connessione è archiviata in formato testo normale nel codice sorgente e nell'assembly compilato. 
            Limitare l'accesso al codice sorgente e all'assembly per proteggere questi dati sensibili.
            </param>
            <param name="tableName">Nome della tabella dati.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String)">
            <summary>
            Inizializza una nuova istanza della classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>. Questa istanza verrà inizializzata con un provider di dati e la stringa di connessione associata al nome dell'impostazione.
            </summary>
            <param name="dataSourceSettingName">Nome di un'origine dati trovata nella sezione &lt;microsoft.visualstudio.qualitytools&gt; del file app.config.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ProviderInvariantName">
            <summary>
            Ottiene un valore che rappresenta il provider di dati dell'origine dati.
            </summary>
            <returns>
            Nome del provider di dati. Se non è stato designato un provider di dati durante l'inizializzazione dell'oggetto, verrà restituito il provider predefinito di System.Data.OleDb.
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ConnectionString">
            <summary>
            Ottiene un valore che rappresenta la stringa di connessione per l'origine dati.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.TableName">
            <summary>
            Ottiene un valore che indica il nome della tabella che fornisce i dati.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataAccessMethod">
             <summary>
             Ottiene il metodo usato per accedere all'origine dati.
             </summary>
            
             <returns>
             Uno dei valori di <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod"/> . Se <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> non è inizializzato, restituirà il valore predefinito <see cref="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random"/>.
             </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataSourceSettingName">
            <summary>
            Ottiene il nome di un'origine dati trovata nella sezione &lt;microsoft.visualstudio.qualitytools&gt; del file app.config.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute">
            <summary>
            Attributo per il test basato sui dati in cui è possibile specificare i dati inline.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Trova tutte le righe di dati e le esegue.
            </summary>
            <param name="testMethod">
            Metodo di test.
            </param>
            <returns>
            Matrice di istanze di <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.RunDataDrivenTest(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod,Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute[])">
            <summary>
            Esegue il metodo di test basato sui dati.
            </summary>
            <param name="testMethod"> Metodo di test da eseguire. </param>
            <param name="dataRows"> Riga di dati. </param>
            <returns> Risultati dell'esecuzione. </returns>
        </member>
    </members>
</doc>
