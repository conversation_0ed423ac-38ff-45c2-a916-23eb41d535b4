﻿using Banyan.Apps;
using System;
using Banyan.Code;
using Banyan.Domain;
using System.Web.Mvc;

namespace Banyan.Web.Controllers
{
    public class FBaseController : Controller
    {
        public static readonly MemberBLL memberBll = new MemberBLL();
        public static readonly ArticleBLL articleBll = new ArticleBLL();
        public static readonly ProjectBLL projectBll = new ProjectBLL();
        public static readonly MeetBLL meetBll = new MeetBLL();

        //protected Member loginUser
        //{
        //    get
        //    {
        //        int userId = Utils.GetRequestInt("uid", 0);
        //        return userId <= 0 ? null : memberBll.GetModelByCache(userId);
        //    }
        //}
        protected Member loginUser
        {
            get
            {
                try
                {
                    string currUserDes = WebHelper.GetCookie("banyan_logon_user");
                    if (!string.IsNullOrEmpty(currUserDes))
                    {
                        string[] desInfo = DESEncrypt.Decrypt(currUserDes).Split(',');
                        int userId = Convert.ToInt32(desInfo[0]);

                        return userId <= 0 ? null : memberBll.GetModelByCache(userId);
                    }
                    int uid = Utils.GetRequestInt("uid", 0);
                    return uid <= 0 ? null : memberBll.GetModelByCache(uid);
                }
                catch (Exception ex)
                {
                    Logger.Info(ex.Message + " " + ex.StackTrace);
                    return null;
                }
            }
        }

    }
}