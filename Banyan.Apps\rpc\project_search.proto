﻿
syntax = "proto3";
 
package Milvus;
 
service milvus {
	rpc similarSearch (SearchRequest) returns (SearchReply);
	rpc add (AddUpdateRequest) returns (AjaxReply);
	rpc update (AddUpdateRequest) returns (AjaxReply);
	rpc delete (DeleteRequest) returns (AjaxReply);
}
 
message AddUpdateRequest {
	Project project = 1;
}

message DeleteRequest {
	string Id = 1;
}
message AjaxReply {
	string code = 1;
	string data = 2;
}
message SearchRequest {
	string content = 1;
}
 
message SearchReply {
	repeated Project project = 1;
}

message Project {
    string Summary = 1;
	int32 Id = 2;
	string Name = 3;
	int32 ToRoleId = 4;
	string EditorName = 5;
	string DDManager = 6;
	string ProjectManager = 7;
	string GroupMember = 8;
	string InteralPTCP = 9;
	string Currency = 10;
	bool IsPrivate = 11;
	string PubTime = 12; 
	bool HasRight = 13;
}