# MCP时间条件功能测试示例

## 测试目的
验证MCP智能工具调用功能中新增的时间条件解析和过滤能力。

## 测试环境
- 确保MCP功能已开启（点击"智能工具"按钮变为绿色）
- 系统中有不同时间创建的测试项目数据

## 测试用例

### 1. 基础时间条件测试

#### 测试用例1.1：本周项目查询
**输入**: "本周有哪些新的投资项目？"
**期望结果**: 
- 🔍 智能检索到 X 个相关项目（时间范围：本周）
- 只返回本周一到今天创建的项目

#### 测试用例1.2：最近时间范围查询
**输入**: "最近三个月的AI项目有哪些亮点？"
**期望结果**: 
- 🔍 智能检索到 X 个相关项目（时间范围：最近三个月）
- 只返回近三个月内的AI相关项目

#### 测试用例1.3：具体年份查询
**输入**: "2024年的新能源项目表现如何？"
**期望结果**: 
- 🔍 智能检索到 X 个相关项目（时间范围：2024年）
- 只返回2024年的新能源相关项目

### 2. 复合条件测试

#### 测试用例2.1：关键词+时间条件
**输入**: "上个月的区块链项目风险分析"
**期望结果**: 
- 同时应用关键词"区块链"和时间条件"上个月"
- 返回上个月的区块链相关项目

#### 测试用例2.2：多重时间表达
**输入**: "今年最近三个月的医疗健康项目统计"
**期望结果**: 
- 正确解析较精确的时间条件（最近三个月）
- 返回最近三个月的医疗健康项目

### 3. 边界条件测试

#### 测试用例3.1：模糊时间表达
**输入**: "最近的项目情况怎么样？"
**期望结果**: 
- 🔍 智能检索到 X 个相关项目（时间范围：最近一个月）
- 默认使用"最近一个月"作为时间范围

#### 测试用例3.2：无时间条件
**输入**: "AI项目的技术栈都有哪些？"
**期望结果**: 
- 🔍 智能检索到 X 个相关项目
- 没有时间范围限制，使用常规搜索

#### 测试用例3.3：无法识别的时间表达
**输入**: "很久以前的项目还有价值吗？"
**期望结果**: 
- 🔍 未找到相关项目信息 或 使用常规搜索
- 无法解析"很久以前"，退化为关键词搜索

### 4. 具体数字时间测试

#### 测试用例4.1：具体天数
**输入**: "近30天创建的项目有哪些？"
**期望结果**: 
- 🔍 智能检索到 X 个相关项目（时间范围：近30天）
- 返回最近30天内的项目

#### 测试用例4.2：具体月份数
**输入**: "近6个月的投资决策分析"
**期望结果**: 
- 🔍 智能检索到 X 个相关项目（时间范围：最近半年）
- 返回最近6个月的项目

## 验证要点

### 1. 功能验证
- [ ] MCP工具能正确识别时间相关问题
- [ ] 时间条件能正确解析为日期范围
- [ ] 搜索结果确实按时间条件过滤
- [ ] 状态信息正确显示时间范围

### 2. 性能验证
- [ ] 时间条件查询响应速度合理
- [ ] 不会返回过多结果影响性能
- [ ] 错误处理机制工作正常

### 3. 用户体验验证
- [ ] 状态提示信息清晰易懂
- [ ] 时间范围描述准确
- [ ] 与现有功能无缝集成

## 常见问题排查

### 问题1：时间条件没有生效
**可能原因**: 
- MCP功能未开启
- 时间表达无法识别
- 数据库中PubTime字段问题

**解决方法**: 
1. 确认"智能工具"按钮为绿色状态
2. 尝试使用标准时间表达（如"本周"、"最近三个月"）
3. 检查后端日志中的时间解析信息

### 问题2：返回结果为空
**可能原因**: 
- 指定时间范围内确实没有匹配项目
- 用户权限限制
- 关键词过于严格

**解决方法**: 
1. 尝试扩大时间范围
2. 使用更通用的关键词
3. 检查用户权限设置

### 问题3：时间解析不准确
**可能原因**: 
- 自然语言表达歧义
- 时间解析逻辑需要完善

**解决方法**: 
1. 使用更明确的时间表达
2. 反馈给开发团队完善解析逻辑

---

*此测试文档应与MCP功能更新同步维护，确保测试用例覆盖所有主要功能点。* 