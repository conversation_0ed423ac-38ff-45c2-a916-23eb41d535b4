﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Blob;
using Microsoft.WindowsAzure.Storage.File;

namespace Banyan.Code.Azure
{
    public class AzureBlob
    {
        #region 私有变量
        //类的成员，用于创建Blob服务客户端
        CloudBlobClient blobClient;
        CloudBlobClient commonBlobClient;
        CloudFileClient fileClient;
        //容器和Blob其实就相当于文件夹、文件名

        /// <summary>
        /// 连接字符串
        /// </summary>
        private string storageConnectionString = "DefaultEndpointsProtocol=https;AccountName=imsfiles;AccountKey=****************************************************************************************;EndpointSuffix=core.chinacloudapi.cn";

        private string commonStorageConnectionString = "DefaultEndpointsProtocol=https;AccountName=common;AccountKey=****************************************************************************************;EndpointSuffix=core.chinacloudapi.cn";
        //CloudConfigurationManager.GetSetting("StorageConnectionString");//代码存储替换web.config
        #endregion

        #region 构造函数创建Blob服务客户端
        public AzureBlob()
        {
            //解析配置中的连接字符串
            CloudStorageAccount storageAccount = CloudStorageAccount.Parse(storageConnectionString);
            //创建Blob服务客户端
            blobClient = storageAccount.CreateCloudBlobClient();
            // CloudFileClient 类是 Windows Azure File Service 客户端的逻辑表示，我们需要使用它来配置和执行对 File Storage 的操作。
            fileClient = storageAccount.CreateCloudFileClient();

        }

        public AzureBlob(bool useCommon)
        {
            //解析配置中的连接字符串
            CloudStorageAccount storageAccount = CloudStorageAccount.Parse(commonStorageConnectionString);
            //创建Blob服务客户端

            CloudStorageAccount commonStorageAccount = CloudStorageAccount.Parse(commonStorageConnectionString);
            //创建Blob服务客户端
            commonBlobClient = commonStorageAccount.CreateCloudBlobClient();
        }

        #endregion

        #region 获取块Blob引用
        /// <summary>
        /// 获取块Blob引用
        /// </summary>
        /// <param name="mycontainer">容器名</param>
        /// <param name="fileName">文件名</param>
        /// <returns></returns>
        public CloudBlockBlob GetContainer(string mycontainer, string fileName)
        {
            //获取容器的引用
            CloudBlobContainer container = blobClient.GetContainerReference(mycontainer);
            //获取块 Blob 引用
            CloudBlockBlob blob = container.GetBlockBlobReference(fileName);
            return blob;
        }
        #endregion

        #region 二进制形式上传文件
        /// <summary>
        /// 二进制形式上传文件
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="mycontainer">容器名</param>
        /// <param name="bytes">二进制形式的文件</param>
        /// <returns>异步信息</returns>
        public Task UploadToBlob(string fileName, string mycontainer, byte[] bytes)
        {
            //获取容器的引用
            CloudBlobContainer container = blobClient.GetContainerReference(mycontainer);
            //创建一个容器（如果该容器不存在）
            container.CreateIfNotExists();
            //设置该容器为公共容器，也就是说网络上能访问容器中的文件，但不能修改、删除
            container.SetPermissions(new BlobContainerPermissions { PublicAccess = BlobContainerPublicAccessType.Blob });
            //将Blob（文件）上载到容器中，如果已存在同名Blob，则覆盖它
            CloudBlockBlob blockBlob = container.GetBlockBlobReference(fileName);//获取块 Blob 引用
            Task result = blockBlob.UploadFromByteArrayAsync(bytes, 0, bytes.Length);//将二进制文件上传
            return result;
        }
        #endregion

        #region Blob文件路径上传
        /// <summary>
        /// Blob文件路径上传
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="mycontainer">容器名</param>
        /// <param name="filePath">文件路径</param>
        /// <returns></returns>
        public string UploadToBlob(string fileName, string mycontainer, string filePath)
        {
            ////获取容器的引用
            CloudBlobContainer container = blobClient.GetContainerReference(mycontainer);
            //创建一个容器（如果该容器不存在）
            container.CreateIfNotExists();
            //设置该容器为公共容器，也就是说网络上能访问容器中的文件，但不能修改、删除
            container.SetPermissions(new BlobContainerPermissions { PublicAccess = BlobContainerPublicAccessType.Blob });
            //将Blob（文件）上载到容器中，如果已存在同名Blob，则覆盖它
            CloudBlockBlob blockBlob = container.GetBlockBlobReference(fileName);//获取块 Blob 引用文件路径
            using (var fileStream = System.IO.File.OpenRead(filePath))
            {
                blockBlob.UploadFromStream(fileStream);
            }
            return blockBlob.Uri.ToString();
        }

        public string UploadToBlobCommon(string fileName, string mycontainer, string filePath)
        {
            ////获取容器的引用
            CloudBlobContainer container = commonBlobClient.GetContainerReference(mycontainer);
            if (!container.Exists())
            {
                return "";
            }
            //创建一个容器（如果该容器不存在）
            //container.CreateIfNotExists();
            ////设置该容器为公共容器，也就是说网络上能访问容器中的文件，但不能修改、删除
            //container.SetPermissions(new BlobContainerPermissions { PublicAccess = BlobContainerPublicAccessType.Blob });
            //将Blob（文件）上载到容器中，如果已存在同名Blob，则覆盖它
            CloudBlockBlob blockBlob = container.GetBlockBlobReference(fileName);//获取块 Blob 引用文件路径
            using (var fileStream = System.IO.File.OpenRead(filePath))
            {
                blockBlob.UploadFromStream(fileStream);
            }
            return blockBlob.Uri.ToString();
        }

        public string UploadToBlobCommon(string fileDir, string mycontainer, HttpPostedFile File)
        {
            ////获取容器的引用
            CloudBlobContainer container = commonBlobClient.GetContainerReference(mycontainer);
            //创建一个容器（如果该容器不存在）
            if (!container.Exists())
            {
                return "";
            }
            //将Blob（文件）上载到容器中，如果已存在同名Blob，则覆盖它
            CloudBlockBlob blockBlob = container.GetBlockBlobReference(fileDir);//获取块 Blob 引用文件路径

            blockBlob.UploadFromStream(File.InputStream);

            return blockBlob.Uri.ToString();
        }
        /// <summary>
        /// Blob文件路径上传
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="mycontainer">容器名</param>
        /// <param name="filePath">文件路径</param>
        /// <returns></returns>
        public string UploadToBlob(string fileDir, string mycontainer, HttpPostedFile File)
        {
            ////获取容器的引用
            CloudBlobContainer container = blobClient.GetContainerReference(mycontainer);
            //创建一个容器（如果该容器不存在）
            container.CreateIfNotExists();
            //设置该容器为公共容器，也就是说网络上能访问容器中的文件，但不能修改、删除
            container.SetPermissions(new BlobContainerPermissions { PublicAccess = BlobContainerPublicAccessType.Blob });
            //将Blob（文件）上载到容器中，如果已存在同名Blob，则覆盖它
            CloudBlockBlob blockBlob = container.GetBlockBlobReference(fileDir);//获取块 Blob 引用文件路径

            blockBlob.UploadFromStream(File.InputStream);

            return blockBlob.Uri.ToString();
        }
        public string UploadToBlob(string fileDir, string mycontainer, HttpPostedFileBase File)
        {
            ////获取容器的引用
            CloudBlobContainer container = blobClient.GetContainerReference(mycontainer);
            //创建一个容器（如果该容器不存在）
            container.CreateIfNotExists();
            //设置该容器为公共容器，也就是说网络上能访问容器中的文件，但不能修改、删除
            container.SetPermissions(new BlobContainerPermissions { PublicAccess = BlobContainerPublicAccessType.Blob });
            //将Blob（文件）上载到容器中，如果已存在同名Blob，则覆盖它
            CloudBlockBlob blockBlob = container.GetBlockBlobReference(fileDir);//获取块 Blob 引用文件路径

            blockBlob.UploadFromStream(File.InputStream);

            return blockBlob.Uri.ToString();
        }
        #endregion
        #region share文件路径上传
        /// <summary>
        /// share文件路径上传
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="shareName">share名</param>
        /// <param name="fileDir">文件夹</param>
        /// <returns></returns>
        public bool UploadToShareFile(string shareName, string fileDir, string fileName, string localFile)
        {
            try
            {
                // CloudFileShare 表示一个 File Share 对象
                CloudFileShare share = fileClient.GetShareReference(shareName);
                // 如果不存在就创建 File Share
                if (share != null && share.CreateIfNotExists())
                {
                    Logger.Info("shareName:" + shareName + ",fileDir:" + fileDir + ",FileName:" + fileName + ",File:" + localFile);

                    // 获得根目录的引用
                    CloudFileDirectory rootDir = share.GetRootDirectoryReference();
                    // 创建子目录 的引用
                    CloudFileDirectory webDir = rootDir.GetDirectoryReference(fileDir);
                    // 创建子目录
                    if (webDir.CreateIfNotExists())
                    {
                        // 创建文件fileName的引用。
                        CloudFile cloudFile = webDir.GetFileReference(fileName);

                        using (var fileStream = System.IO.File.OpenRead(localFile))
                        {
                            //上传文件
                            cloudFile.UploadFromStream(fileStream);
                        }

                        return true;
                    }
                    else
                    {
                        Logger.Info("子目录不存在！");
                        return false;
                    }
                }
                else
                {
                    Logger.Info("File Share 对象不存在！");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Info(ex.Message);
                return false;
            }
        }
        #endregion
        #region share文件路径上传
        /// <summary>
        /// share文件路径上传
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="mycontainer">容器名</param>
        /// <param name="fileDir">文件夹</param>
        /// <returns></returns>
        public bool UploadToShareFile(string shareName, string fileDir, HttpPostedFile File)
        {
            try
            {
                // CloudFileShare 表示一个 File Share 对象
                CloudFileShare share = fileClient.GetShareReference(shareName);
                // 如果不存在就创建 File Share
                share.CreateIfNotExists();

                // 获得根目录的引用
                CloudFileDirectory rootDir = share.GetRootDirectoryReference();
                // 创建子目录 的引用
                CloudFileDirectory webDir = rootDir.GetDirectoryReference(fileDir);
                // 创建子目录
                webDir.CreateIfNotExists();
                // 创建文件fileName的引用
                CloudFile cloudFile = webDir.GetFileReference(File.FileName);

                //上传文件
                cloudFile.UploadFromStream(File.InputStream);

                return true;
            }
            catch (Exception ex)
            {
                Logger.Info(ex.Message);
                return false;
            }
        }
        #endregion
        #region 根据文件名和容器名取到Blob地址
        /// <summary>
        /// 根据文件名和容器名取到Blob地址
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="mycontainer">容器名</param>
        /// <returns></returns>
        public string GetBlobURI(string fileName, string mycontainer)
        {
            CloudBlockBlob blob = GetContainer(mycontainer, fileName);
            return blob.Uri.ToString();
        }
        #endregion

        #region 下载Blob
        /// <summary>
        /// 下载Blob
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="mycontainer">容器名</param>
        /// <param name="fliePath">文件路径</param>
        public void DownloadToFile(string fileName, string mycontainer, string fliePath)
        {
            CloudBlockBlob blob = GetContainer(mycontainer, fileName);
            using (var fileStream = File.OpenWrite(fliePath))
            {
                blob.DownloadToStream(fileStream); //将blob保存在指定路径
            }
        }
        #endregion

        #region 下载Blob
        /// <summary>
        /// 下载Blob
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="mycontainer">容器名</param>
        /// <param name="fliePath">文件路径</param>
        public string DownloadToStream(string fileName, string mycontainer)
        {
            CloudBlockBlob blob = GetContainer(mycontainer, fileName);
            string text;
            using (var memoryStream = new MemoryStream())
            {
                blob.DownloadToStream(memoryStream);
                text = System.Text.Encoding.UTF8.GetString(memoryStream.ToArray());
            }
            return text;
        }
        #endregion

        #region 下载Blob
        /// <summary>
        /// 下载Blob
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="mycontainer">容器名</param>
        /// <param name="fliePath">文件路径</param>
        public void DownloadToFileStream(string fileName, string mycontainer, string fliePath)
        {
            CloudBlockBlob blob = GetContainer(mycontainer, fileName);
            using (var fileStream = new FileStream(fliePath, FileMode.OpenOrCreate))
            {
                blob.DownloadToStream(fileStream); //将blob保存在指定路径
            };
        }
        #endregion

        #region 删除Blob
        /// <summary>
        /// 删除Blob
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="mycontainer">容器名</param>
        public void DeleteBlob(string fileName, string mycontainer)
        {
            CloudBlockBlob blob = GetContainer(mycontainer, fileName);
            blob.Delete();
        }
        #endregion
    }
}
