﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;

namespace Banyan.Apps
{
    public class RestrictedTradingListBLL : BaseDAL<RestrictedTradingList>
    {
        private readonly AjaxResult ajaxResult = null;

        public RestrictedTradingListBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }
    
        public AjaxResult GetPageList(NameValueCollection paramValues)
        {
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var userModel = new MemberBLL().GetModelByCache(uid);
            if (userModel == null || userModel.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = userModel == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = userModel == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }
            if (!(userModel.Levels == (int)MemberLevels.Administrator || userModel.Levels == (int)MemberLevels.SuperUser))
            {
                ajaxResult.code = (int)ResultCode.success;
                ajaxResult.data = null;
                return ajaxResult;
            }
            string strWhere = $" id <> -1 ";
            string Name = WebHelper.GetValue("keywords", string.Empty, paramValues);
            if (!string.IsNullOrWhiteSpace(Name))
            {
                strWhere += $@"AND ( companyName like '%{Name}%' OR code like '%{Name}%' OR ipoAddress like '%{Name}%' ) ";
            }

            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 12, paramValues);
            string sort = " companyName DESC ";

            var DealList = GetList(strWhere, pageSize, pageIndex, "*", sort);

            updateLog("miniapp, Get Black List", "view", "getpagelist", userModel);

            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = DealList.Select(item =>
            {
                return new
                {
                    abbName = item.code,
                    investFund = item.ipoAddress,
                    remark = item.companyName
                };
            });
            return ajaxResult;
        }
    
        public void updateLog(string page, string action, string description, Member user)
        {
            SysLog log = new SysLog
            {
                Page = page,
                Action = action,
                Description = description,
                CreatedBy = user == null ? "" : user.RealName,
                Ip = Utility.WebHelper.GetIP(),
                CreatorId = user == null ? 0 : user.Id,
            };
            SysLogBLL logBLL = new SysLogBLL();
            logBLL.Add(log);
        }
    }
}
