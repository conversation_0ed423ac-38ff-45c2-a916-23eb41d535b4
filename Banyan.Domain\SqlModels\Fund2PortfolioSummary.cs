﻿using Entity.Base;
using System;
using Utility;
using System.Collections.Generic;

namespace Banyan.Domain
{
    public class Fund2PortfolioSummaryComparer : IEqualityComparer<Fund2PortfolioSummary>
    {
        public bool Equals(Fund2PortfolioSummary x, Fund2PortfolioSummary y)
        {
            if (x == null)
                return y == null;
            return x.fundFamillyName.Equals(y.fundFamillyName);
        }

        public int GetHashCode(Fund2PortfolioSummary obj)
        {
            return 1;
        }
    }


    [Serializable]
    [SqlTable(dbEnum.BusinessCenterPortal)]
    public class Fund2PortfolioSummary
    {
        [SqlField]
        public string fundID { get; set; } = string.Empty;
        [SqlField]
        public string fundName { get; set; } = string.Empty;
        [SqlField]
        public string fundFamillyName { get; set; } = string.Empty;
        [SqlField]
        public string Name { get; set; } = string.Empty;
        [SqlField]
        public string fundFullNameEng { get; set; } = string.Empty;
        [SqlField]
        public string portfolioID { get; set; } = string.Empty;
    }
}

