﻿using System.Configuration;

namespace Banyan.Code
{
    public class ResultStatus
    {
        public string errcode { get; set; }

        public string errmsg { get; set; }
    }

    public class ResultToken : ResultStatus
    {
        /// <summary>
        /// token
        /// </summary>
        public string access_token { get; set; }

        /// <summary>
        /// 失效时间（秒）
        /// </summary>
        public string expires_in { get; set; }

        public string openid { get; set; }

        public string unionid { get; set; }
    }

    public class WechatUtils
    {
        /// <summary>
        /// 获取小程序配置
        /// </summary>
        /// <returns></returns>
        public static string GetAppKeys(string key="appKey", string secret = "appSecret")
        {
            string appKey = ConfigurationManager.AppSettings[key]?.ToString();
            string appSecret = ConfigurationManager.AppSettings[secret]?.ToString();
            if (string.IsNullOrEmpty(appKey) || string.IsNullOrEmpty(appSecret))
            {
                return string.Empty;
            }
            return $"{appKey},{appSecret}";
        }

        /// <summary>
        /// 获取access_token
        /// </summary>
        /// <param name="appId">公众号/小程序AppId</param>
        /// <param name="appSecret"></param>
        /// <returns></returns>
        public static string GetAccessToken()
        {
            string appKeys = GetAppKeys();
            if (string.IsNullOrEmpty(appKeys))
            {
                return string.Empty;
            }
            string appId = appKeys.Split(',')[0].ToString();
            string appSecret = appKeys.Split(',')[1].ToString();
            string resultBack = HttpMethods.HttpGet(string.Format(ApiUrls.AccessToken, appId, appSecret));
            if (resultBack.IndexOf("access_token") > -1)
            {
                ResultToken resultToken = Json.ToObject<ResultToken>(resultBack);
                return resultToken.access_token;
            }
            return string.Empty;
        }

        public static string GetUnionIDWeb(string code, string key = "appKeyWeb", string secret = "appSecretWeb")
        {
            string appKeys = GetAppKeys(key, secret);
            if (string.IsNullOrEmpty(appKeys))
            {
                return string.Empty;
            }
            string appId = appKeys.Split(',')[0].ToString();
            string appSecret = appKeys.Split(',')[1].ToString();
            string resultBack = HttpMethods.HttpGet(string.Format("https://api.weixin.qq.com/sns/oauth2/access_token?appid={0}&secret={1}&code={2}&grant_type=authorization_code", appId, appSecret, code));
            if (resultBack.IndexOf("access_token") > -1)
            {
                ResultToken resultToken = Json.ToObject<ResultToken>(resultBack);
                return resultToken.unionid; // GetUserfInfo(resultToken.openid, resultToken.access_token);
            }
            return string.Empty;
        }

        public static string GetUserfInfo(string openid, string access_token)
        {
            string apiUrl = $"https://api.weixin.qq.com/sns/userinfo?access_token={access_token}&openid={openid}";
            var result = WebHelper.HttpWebRequest(apiUrl);
            return result;
        }

        /// <summary>
        /// 获取会话信息（openId，sessionId）
        /// </summary>
        /// <param name="code"></param>
        /// <param name="appId"></param>
        /// <param name="appSecret"></param>
        /// <returns></returns>
        public static string GetSeesionInfo(string code)
        {
            try
            {
                string appKeys = GetAppKeys();
                if (string.IsNullOrEmpty(appKeys))
                {
                    return string.Empty;
                }
                string appId = appKeys.Split(',')[0].ToString();
                string appSecret = appKeys.Split(',')[1].ToString();

                string apiUrl = $"https://api.weixin.qq.com/sns/jscode2session?appid={appId}&secret={appSecret}&js_code={code}&grant_type=authorization_code";
                var result = WebHelper.HttpWebRequest(apiUrl);
                return result;
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取会话信息（openId，sessionId）
        /// </summary>
        /// <param name="code"></param>
        /// <param name="appId"></param>
        /// <param name="appSecret"></param>
        /// <returns></returns>
        public static string GetRonghuiSeesionInfo(string code)
        {
            try
            {
                string appId = "wxb4132b692b595ed2";
                string appSecret = "94eb4c58b9b40000f4c7eeac2072c6bc";

                string apiUrl = $"https://api.weixin.qq.com/sns/jscode2session?appid={appId}&secret={appSecret}&js_code={code}&grant_type=authorization_code";
                var result = WebHelper.HttpWebRequest(apiUrl);
                return result;
            }
            catch
            {
                return string.Empty;
            }
        }
    }

    public class SessionInfo
    {
        public string openid { get; set; } = string.Empty;

        public string session_key { get; set; } = string.Empty;

        public string unionid { get; set; } = string.Empty;
    }

    public class UserBaseInfo
    {
        /// <summary>
        /// openid
        /// </summary>
        public string openid { get; set; }
        /// <summary>
        /// 头像
        /// </summary>
        public string avatarUrl { get; set; }
        /// <summary>
        /// 市/区
        /// </summary>
        public string city { get; set; }
        /// <summary>
        /// 县/街道
        /// </summary>
        public string country { get; set; }

        /// <summary>
        /// 语言
        /// </summary>
        public string language { get; set; }
        /// <summary>
        /// 昵称
        /// </summary>
        public string RealName { get; set; }
        /// <summary>
        /// 省
        /// </summary>
        public string province { get; set; }
    }

    public class ApiUrls
    {
        /// <summary>
        /// Token
        /// </summary>
        public static string AccessToken = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={0}&secret={1}";

        /// <summary>
        /// 添加模板
        /// </summary>
        public static string TemplateAdd = "https://api.weixin.qq.com/cgi-bin/wxopen/template/add?access_token={0}";

        /// <summary>
        /// 获取模板消息详细
        /// </summary>
        public static string TemplateGet = "https://api.weixin.qq.com/cgi-bin/wxopen/template/library/get?access_token={0}";

        /// <summary>
        /// 当前账户下模板列表
        /// </summary>
        public static string TemplateList = "https://api.weixin.qq.com/cgi-bin/wxopen/template/list?access_token={0}";

        /// <summary>
        /// 删除模板
        /// </summary>
        public static string TemplateDel = "https://api.weixin.qq.com/cgi-bin/wxopen/template/del?access_token={0}";

        /// <summary>
        /// 发送模板
        /// </summary>
        public static string TemplateSend = "https://api.weixin.qq.com/cgi-bin/message/wxopen/template/send?access_token={0}";

    }
}
