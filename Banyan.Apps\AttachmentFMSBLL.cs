﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Collections.Specialized;

namespace Banyan.Apps
{
    public class AttachmentFMSBLL : BaseDAL<AttachmentFMS>
    {
        private readonly AjaxResult ajaxResult = null;

        private class SearchAttachmentFMS
        {
            public int Id;
            public string Name;
            public int SourceId;
            public string AtSuffix;
            public string AddTime;
            public string Path;
        }
        public AttachmentFMSBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }
        public AjaxResult searchByName(NameValueCollection paramValues)
        {
            string Name = WebHelper.GetValue("Name", string.Empty, paramValues);
            var ProjectList = GetList($" AtName like '%{Name}%' ", int.MaxValue, 1, "*", "AtName DESC");
            var result = ProjectList.Select(val =>
            {
                var tmp = new SearchAttachmentFMS();
                tmp.Id = val.Id;
                tmp.Name = val.AtName;
                tmp.AtSuffix = val.AtSuffix;
                tmp.SourceId = val.SourceId;
                tmp.AddTime = val.AddTime.ToString("yyyy-MM-dd");
                tmp.Path = val.Path;
                return tmp;
            }).ToList();
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = result;
            return ajaxResult;
        }
        private string getStrWhere(NameValueCollection paramValues, Member user, out string sort, bool searchNameOnly = false, bool mobile = false)
        {

            string strWhere = $" id > -1 ";

            string suffix = WebHelper.GetValue("suffix", string.Empty, paramValues);
            if (!string.IsNullOrWhiteSpace(suffix))
            {
                strWhere += $@" AND (AtSuffix = '{suffix}') ";

            }

            string Name = WebHelper.GetValue("Name", string.Empty, paramValues);
            Name = Name.Replace("'", "''");

            if (!string.IsNullOrWhiteSpace(Name))
            {
               strWhere += $@" AND (AtName like '%{Name}%') ";

            }

            string startDate = WebHelper.GetValue("startdate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(startDate))
            {
                strWhere += $"AND AddTime>='{startDate}' ";
            }
            if (user.limitedJoinTime)
            {
                strWhere += $"AND AddTime>='{user.AddTime.ToString("yyyy-MM-dd")}' ";
            }

            string endDate = WebHelper.GetValue("enddate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(endDate))
            {
                strWhere += $"AND AddTime<'{Convert.ToDateTime(endDate).AddDays(1).ToString("yyyy-MM-dd")}' ";
            }

            sort = " AddTime DESC ";

            return strWhere;
        }

        public List<AttachmentFMS> searchCommon(NameValueCollection paramValues, Member user,
           int pageIndex, int pageSize, out int count, bool searchNameOnly = false, bool searchMonthOnly = false)
        {
            string sort;
            string strWhere = getStrWhere(paramValues, user, out sort, searchNameOnly);

            Logger.Info("AttachmentFMSsPage search str: " + strWhere, user.RealName);

            var ProjectList = GetList(strWhere, pageSize, pageIndex, "*", sort);

            count = GetCount(strWhere);
            return ProjectList;
        }

        public AjaxResult GetPageList(NameValueCollection paramValues)
        {
            Member user = new MemberBLL().GetLogOnUser();
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10000, paramValues);
            int count;
            var ProjectList = searchCommon(paramValues, user, pageIndex, pageSize, out count);

            Logger.Info("View AttachmentFMSs page", user.RealName);

            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = ProjectList;
            ajaxResult.count = count;
            return ajaxResult;
        }

        public List<AttachmentFMS> searchList(string searchStr, string Name)
        {
            var list = GetList($"{searchStr} AND (AtName like '%{Name}%'  OR Contains(Content, '\"{Name}\"') ) ");
            return list;
        }

        public AjaxResult GetPortfolioExitDocs(int id, bool fullPath = false)
        {
            ajaxResult.code = (int)ResultCode.success;
            List<AttachmentFMS> list = GetList($"SourceId={id} AND SourceType={(Byte)SourceTypeEnumFMS.Exit}"); 
            if (fullPath && list.Count > 0)
            {
                String hostUrl = System.Configuration.ConfigurationManager.AppSettings["FileDomain"].ToString() ?? "";
                foreach (var item in list)
                {
                    if (item.AtSuffix == "EXIT")
                    {
                        var t = item.AtUrl.Split(',');
                        item.AtUrl = hostUrl + string.Join("," + hostUrl, t);
                    }
                    else
                    {
                        item.AtUrl = hostUrl + item.AtUrl;
                    }
                }
            }
            var newList = new List<AttachmentFMS>();
            var sortList = new List<AttachmentFMS>();
            foreach (var i in list)
            {
                if (i.AtSuffix == "Image")
                {
                    newList.Add(i);
                }
                else
                {
                    sortList.Add(i);
                }
            }
            sortList.AddRange(newList);
            ajaxResult.data = sortList;
            return ajaxResult;
        }
        public AjaxResult SaveDoc(AttachmentFMS model)
        {
            Member user = new MemberBLL().GetLogOnUser();
            ajaxResult.code = (int)ResultCode.exception;
            if (model.SourceId <= 0)
            {
                ajaxResult.msg = $"关联记录不存在";
                return ajaxResult;
            }
            try
            {
                model.AtName = model.AtName.Replace("+", "加").Replace(" ", "_");
                if (!model.AtSuffix.Equals(".xlsx"))
                {
                    var tmpPathArr = model.AtUrl.Split('/');
                    model.Path = $"/imsfiles/{tmpPathArr[2]}/{tmpPathArr[3]}/" + model.AtName;
                }
                model.Creator = user.RealName;
                int docId = Convert.ToInt32(Add(model));
                if (docId <= 0)
                {
                    Logger.Error($"add doc {model.AtName} err", user.RealName);
                }
                Logger.Info($"add doc {model.AtName}", user.RealName);
                ajaxResult.data = docId;
                ajaxResult.code = docId > 0 ? (int)ResultCode.success : (int)ResultCode.exception;
                
            }
            catch (Exception e)
            {
                Logger.Error(e.Message, e, user.RealName);
            }

            return ajaxResult;
        }

        public AjaxResult DelDoc(int id)
        {
            Member user = new MemberBLL().GetLogOnUser();
            ajaxResult.code = (int)ResultCode.exception;
            if (id <= 0)
            {
                ajaxResult.msg = $"参数不合法";
                return ajaxResult;
            }
            try
            {
                //var model = new AttachmentFMS() { Id = id, Deleted = 1 };

                //ajaxResult.code = Update(model, "Deleted") ? (int)ResultCode.success : (int)ResultCode.exception;
                Logger.Info($"delete doc {id}", user.RealName);
                var res = Delete(id);
                if (res <= 0)
                {
                    Logger.Error($"delete doc {id} error", user.RealName);
                }
                ajaxResult.code = res > 0 ? (int)ResultCode.success : (int)ResultCode.exception;

            }catch(Exception e)
            {
                Logger.Error(e.Message, e, user.RealName);
            }
            return ajaxResult;
        }

        public void ClearCache(AttachmentFMS model)
        {
            RedisUtil.Remove(string.Format(RedisKey.attachment_comb_fms, model.Id));
        }
        public override bool Update(AttachmentFMS model)
        {
            ClearCache(model);
            return base.Update(model);
        }
        public override bool Update(AttachmentFMS model, string fldList)
        {
            ClearCache(model);
            return base.Update(model, fldList);
        }
        public AttachmentFMS GetCache(int articleId)
        {
            string cacheKey = string.Format(RedisKey.attachment_comb_fms, articleId);
            AttachmentFMS model = RedisUtil.Get<AttachmentFMS>(cacheKey);
            if (model == null)
            {
                model = base.GetModel(articleId);
                RedisUtil.Set<AttachmentFMS>(cacheKey, model, TimeSpan.FromMinutes(10));
            }
            return model;
        }

        public object JOk(object data, string msg = "", int code = 0, int count = 0)
        {
            return new { code, data, msg, count };
        }

        public object JFail(string msg = "", int code = 1)
        {
            return new { code, msg };
        }
        public static string GetDomain()
        {
            return System.Configuration.ConfigurationManager.AppSettings["FileDomain"];
        }
        public AttachmentFMS GetAttachmentFMS(int articleId)
        {
            AttachmentFMS model = GetCache(articleId);
            if (model != null && !string.IsNullOrEmpty(model.AtUrl))
            {
                List<string> fullPathList = new List<string>();
                string filePath = GetDomain();
                foreach (var item in model.AtUrl.Split(','))
                {
                    fullPathList.Add($"{filePath}{item}");
                }
                model.AtUrl = string.Join(",", fullPathList);
                new SysLogBLL().LogFileView(model.AtName, model.SourceId +"", model.AtUrl);
            }
            return model;
        }

        public bool DelDoc(int sourceid, String type)
        {
            return DeleteByWhere($"SourceId='{sourceid}' AND AtSuffix='{type}'") > 0 ? true : false;
        }
    }
}
