﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;


namespace Banyan.Apps
{
    public class BuybackBLL : BaseDAL<Buyback>
    {
        private readonly AjaxResult ajaxResult = null;
        private MemberBLL memberBll = new MemberBLL();
        private Project_ActiveBLL projectActiveBll = new Project_ActiveBLL();
        private PortfolioBLL portfolioBLL = new PortfolioBLL();
        public BuybackBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        public AjaxResult GetDetail(NameValueCollection paramValues)
        {
            Member user = new MemberBLL().GetLogOnUser();
            int id = WebHelper.GetValueInt("id", 1, paramValues);
            ajaxResult.code = (int)ResultCode.success;
            var group = base.GetModel(id);
            group.IsOperate = group.Creator == user.RealName || group.Manager == user.RealName || MemberBLL.adminOrSuper(user);
            ajaxResult.data = group;
            return ajaxResult;
        }

        public Buyback GetByName(String name)
        {
            return GetModel($"Title='{name}'");
        }

        /// <summary>
        /// 分页查询记录
        /// </summary>
        /// <param name="paramValues">页面请求参数</param>
        /// <returns></returns>
        public AjaxResult GetPageList(NameValueCollection paramValues)
        {
            Member user = new MemberBLL().GetLogOnUser();
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10000, paramValues);
            int count;
            var BuybackList = searchCommon(paramValues, user, pageIndex, pageSize, out count);
            updateLog("Web, Get Buyback List", "view", "getpagelist", user);
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = BuybackList;
            ajaxResult.count = count;
            return ajaxResult;
        }

        public override int Delete(int id)
        {
            var user = new MemberBLL().GetLogOnUser();
            var model = base.GetModel(id);
            var m = projectActiveBll.GetModel($"ID={model.DealID}");
            m.buyback = false;
            projectActiveBll.Update(m, "buyback");
            updateLog("New Buyback", "Delete", model.ToJson(), user, model.abbName);
            return base.Delete(id);
        }
        public AjaxResult Save(Buyback model)
        {
            AjaxResult ajaxResult = new AjaxResult();
            var user = new MemberBLL().GetLogOnUser();

            if (model.Status == (int)BuybackStatus.normal && model.DueDate < DateTime.Now)
            {
                model.Status = (int)BuybackStatus.expired;
            }
            if ((model.Type == 0 || model.Type == 1) && model.DueDate == DateTime.MaxValue)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = "当前回购触发类型需要选择截止日期!";
                return ajaxResult;
            }
            if (model.Id > 0)
            {
                model.ModifiedDate = DateTime.Now;
                model.Modifier = user.RealName;
                ajaxResult.data = Update(model, "DealID,Manager,DueDate,Status,Type,Description,Progress,Result,Otherinfo,ModifiedDate,Modifier");
                ajaxResult.code = (bool)ajaxResult.data ? (int)ResultCode.success : (int)ResultCode.exception;
                updateLog("Update Buyback", "Update", model.ToJson(), user, model.abbName);
            }
            else
            {
                model.Creator = user.RealName;
                if (model.Type == 2)
                {
                    model.Status = (int)BuybackStatus.other;
                }
                model.Id = Convert.ToInt32(Add(model));
                ajaxResult.code = model.Id > 0 ? (int)ResultCode.success : (int)ResultCode.exception;
                updateLog("New Buyback", "Save", model.ToJson(), user, model.abbName);
                var m = projectActiveBll.GetModel($"ID={model.DealID}");
                m.buyback = true;
                projectActiveBll.Update(m, "buyback");
            }
            ajaxResult.data = model.Id;
            return ajaxResult;
        }

        private List<Buyback> searchCommon(NameValueCollection paramValues, Member user,
            int pageIndex, int pageSize, out int count, bool searchNameOnly = false)
        {
            string sort;
            string strWhere = getStrWhere(paramValues, user, out sort, searchNameOnly);

            var searchStr = @"select
	                            pbi.portfolioManager + ' ' + pbi.PostInvestManager+ ' ' + pbi.PostInvestMember as manager,
	                            b.*,
                                pbi.buyBackManager,
	                            pa.abbName,
	                            pa.investFund as fund,
	                            pa.firstInvest as round
                            from
	                            Buyback b
                            join Project_Active pa on
	                            b.dealID = pa.ID
                            join PortfolioBasicInfo pbi on
	                            pa.portfolio = pbi.portfolioID ";

            var BuybackList = GetListBySql($"{searchStr} where {strWhere} order by {sort}  offset {pageSize * (pageIndex - 1)} row fetch next {pageSize} row only");

            count = GetListBySql($"{searchStr} where {strWhere} ").Count;

            return BuybackList;
        }
        private string getStrWhere(NameValueCollection paramValues, Member user, out string sort, bool searchNameOnly = false, bool mobile = false)
        {
            string strWhere = $"b.Status<> -1 ";

            string Name = WebHelper.GetValue("Name", string.Empty, paramValues);

            Name = Name.Replace("\'", string.Empty);
            if (!string.IsNullOrWhiteSpace(Name))
            {
                if (searchNameOnly)
                {
                    strWhere += $@"AND pa.abbName like '%{Name}%' ";
                }
                else
                {
                    strWhere += $@"AND (pa.abbName like '%{Name}%' OR b.Creator like '%{Name}%'  OR b.Description like '%{Name}%' OR b.otherinfo like '%{Name}%' OR b.progress like '%{Name}%' )";
                }
            }

            string Fund = WebHelper.GetValue("Fund", string.Empty, paramValues);

            if (!string.IsNullOrWhiteSpace(Fund))
            {
                strWhere += $@"AND pa.investFund = '{Fund}' ";
            }

            if(!MemberBLL.adminOrSuper(user))
            {
                strWhere += $@"AND pbi.portfolioManager + ' ' +pbi.PostInvestManager+ ' ' + pbi.PostInvestMember like  '%{user.RealName}%' OR b.Creator = '{user.RealName}' OR pbi.buyBackManager like '%{user.RealName}%' ";
                // if (user.RealName == "钟秋月")
                // {
                //     strWhere += $@"OR pa.investFund like '%RMB Growth%' ";
                // }
            }

            sort = " status ASC, AddDate DESC, Creator DESC ";
            return strWhere;
        }

        public void updateLog(string page, string action, string description, Member user, string Buyback = "")
        {
            SysLog log = new SysLog
            {
                Page = page,
                Action = action,
                Name = Buyback,
                Description = description,
                CreatedBy = user == null ? "" : user.RealName,
                CreatorId = user == null ? 0 : user.Id,
            };
            SysLogBLL logBLL = new SysLogBLL();
            logBLL.Add(log);
        }
     
    }
}