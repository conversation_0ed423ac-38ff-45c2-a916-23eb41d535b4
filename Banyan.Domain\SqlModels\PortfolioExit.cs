﻿using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.BusinessCenterPortal)]
    public class PortfolioExit
    {
        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        [SqlField]
        public string Name { get; set; } = string.Empty;

        [SqlField]
        public String portfolioID { get; set; } = String.Empty;

        [SqlField]
        public String dealID { get; set; } = String.Empty;

        [SqlField]
        public int ToRoleId { get; set; }
        // case "消费组": p.ToRoleId = 1; break;
        // case "技术组": p.ToRoleId = 2; break;
        // case "互联网组": p.ToRoleId = 3; break;
        // case "医疗组": p.ToRoleId = 5; break;
        // case "参谋部": p.ToRoleId = 6; break;
        // case "产业组": p.ToRoleId = 7; break;

        [SqlField]
        public string creator { get; set; } = string.Empty;

        [SqlField]
        public string exitMember { get; set; } = string.Empty;

        [SqlField] // 退出贡献人
        public string exitContributor { get; set; } = string.Empty;
        [SqlField] // 具体贡献内容
        public string exitContribution { get; set; } = string.Empty;

        [SqlField] //退出方案
        public string exitPlan { get; set; } = string.Empty;

        [SqlField]
        public string exitFund { get; set; } = string.Empty;

        [SqlField]
        public string exitAmount { get; set; } = string.Empty;

        [SqlField]
        public string exitCost { get; set; } = string.Empty;
        [SqlField] //退出部分倍数
        public string exitRatio { get; set; } = string.Empty;

        [SqlField]
        public string remark { get; set; } = string.Empty;

        [SqlField]
        public string viewer { get; set; } = string.Empty;

        [SqlField]
        public string editor { get; set; } = string.Empty;

        [SqlField]
        public string exitType { get; set; } = string.Empty;

       [SqlField]
        public string modifier { get; set; } = string.Empty;
        [SqlField]
        public string exitStatusExplain { get; set; } = string.Empty;

        [SqlField]
        public string legalOpinion { get; set; } = string.Empty;

        [SqlField]
        public string investOpinion { get; set; } = string.Empty;

        [SqlField]
        public DateTime modifiedDate { get; set; } = DateTime.Now;
        /// <summary>
        /// 创建时间
        /// </summary>
        [SqlField]
        public DateTime createdDate { get; set; } = DateTime.Now;

        [SqlField]
        public byte Status { get; set; } = (int)PortfolioExitStatus.normal;
        [SqlField]
        public byte exitStatus { get; set; }
        [SqlField]
        public bool IsPrivate { get; set; } = false;

        // for miniapp
        public string remarkBottom { get; set; } = string.Empty;

        public string RoleName { get; set; } = string.Empty;
        public bool IsOperate { get; set; } = false;
    }
    public class PortfolioExitExt: PortfolioExit
    {
        [SqlField]
        public string portfolioManager { get; set; } = string.Empty;
        [SqlField]
        public string postInvestManager { get; set; } = string.Empty;
        [SqlField]
        public string exitManager { get; set; } = string.Empty;
        [SqlField]
        public string discussType { get; set; } = string.Empty;
        [SqlField]
        public DateTime dicussDate { get; set; } = DateTime.Now;
        public string discussDateStr { get; set; } = string.Empty;
        [SqlField]
        public string discussContent { get; set; } = string.Empty;
    }
    public enum PortfolioExitStatus
    {
        delete = 0,
        normal = 1
    }
}
