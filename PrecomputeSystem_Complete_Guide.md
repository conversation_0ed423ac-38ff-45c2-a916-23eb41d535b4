# Banyan 预计算系统完整指南

## 概述

Banyan 预计算系统是一个智能的新闻推荐预计算解决方案，通过提前计算用户个性化推荐和热门新闻相似度，显著提升推荐系统的响应速度。

## 系统架构

### 核心组件

1. **NewsPrecomputeService** - 预计算核心服务
2. **PrecomputeDiagnosticService** - 诊断和监控服务
3. **PrecomputeTestService** - 测试验证服务
4. **RecommendationScheduler** - 定时任务调度器

### 数据流程

```
用户兴趣向量 → 预计算推荐 → Redis缓存 → 推荐引擎 → 用户
新闻向量 → 相似度计算 → Redis缓存 → 相似新闻推荐
```

## 主要功能

### 1. 用户推荐预计算

为所有有兴趣向量的用户预计算个性化推荐结果：

```csharp
var precomputeService = NewsPrecomputeService.Instance;
var result = await precomputeService.PrecomputeUserRecommendationsAsync(
    recommendationCount: 20,  // 每个用户的推荐数量
    threshold: 0.4           // 相似度阈值
);
```

### 2. 热门新闻相似度预计算

为热门新闻预计算相似新闻：

```csharp
var result = await precomputeService.PrecomputeHotNewsRecommendationsAsync(
    topN: 50,           // 热门新闻数量
    similarCount: 10,   // 每篇新闻的相似新闻数量
    threshold: 0.5      // 相似度阈值
);
```

### 3. 综合预计算

同时执行用户推荐和热门新闻预计算：

```csharp
var result = await precomputeService.PrecomputeAllAsync(
    hotNewsCount: 50,
    similarCount: 10,
    newsThreshold: 0.5,
    userRecommendationCount: 20,
    userThreshold: 0.4
);
```

## 缓存策略

### Redis 缓存键格式

- 用户推荐: `precomputed_user_recommendations:{userId}`
- 新闻相似度: `precomputed_similar_news:{newsId}`

### 缓存过期时间

- 用户推荐: 12小时
- 新闻相似度: 24小时

## 推荐引擎集成

### NewsRecommendationEngine 集成

推荐引擎现在会优先检查预计算结果：

```csharp
public async Task<List<News>> GetPersonalizedRecommendationsAsync(int userId, ...)
{
    // 1. 首先检查预计算缓存
    var precomputedRecommendations = precomputeService.GetPrecomputedUserRecommendations(userId);
    if (precomputedRecommendations != null && precomputedRecommendations.Count > 0)
    {
        return ConvertToNews(precomputedRecommendations);
    }
    
    // 2. 回退到实时计算
    // ...
}
```

### NewsVectorSearch 集成

向量搜索服务也会优先使用预计算结果：

```csharp
public async Task<List<NewsVectorSimilarity>> GetRecommendedNewsByInterest(int userId, ...)
{
    // 1. 检查预计算缓存
    var precomputedRecommendations = _precomputeService.GetPrecomputedUserRecommendations(userId);
    if (precomputedRecommendations != null && precomputedRecommendations.Count > 0)
    {
        return ApplyFilters(precomputedRecommendations);
    }
    
    // 2. 回退到向量搜索
    // ...
}
```

## 定时任务配置

### RecommendationScheduler 配置

系统会自动在应用启动时启动定时任务：

```csharp
// Global.asax.cs
private void InitializeRecommendationScheduler()
{
    var scheduler = RecommendationScheduler.Instance;
    bool started = scheduler.Start(6); // 每6小时执行一次
}
```

### 定时任务执行内容

1. 执行综合预计算（用户推荐 + 热门新闻）
2. 更新用户推荐缓存
3. 预热热门用户缓存

## 监控和诊断

### 访问诊断页面

访问 `/PrecomputeDiagnostic/Index` 查看系统状态和执行操作。

### 主要监控指标

1. **用户推荐覆盖率** - 有预计算推荐的用户比例
2. **热门新闻覆盖率** - 有预计算相似新闻的热门新闻比例
3. **Redis连接状态** - 缓存系统健康状态
4. **缓存命中率** - 预计算结果的使用效率

### 诊断功能

```csharp
var diagnosticService = PrecomputeDiagnosticService.Instance;
var report = await diagnosticService.RunFullDiagnosticAsync();

// 检查整体状态
if (report.OverallStatus == DiagnosticStatus.Healthy)
{
    // 系统正常
}
```

## 性能优化

### 预计算带来的性能提升

- **响应时间**: 从数秒降低到毫秒级
- **系统负载**: 减少实时向量计算压力
- **用户体验**: 推荐结果即时返回

### 性能测试结果

使用 `PrecomputeTestService` 可以测试性能提升：

```csharp
var testService = PrecomputeTestService.Instance;
var testResult = await testService.RunCompleteTestAsync();

Console.WriteLine($"平均性能提升: {testResult.PerformanceTest.AveragePerformanceImprovement:F1}%");
```

## 故障排除

### 常见问题

1. **Redis连接失败**
   - 检查Redis服务状态
   - 验证连接配置

2. **预计算结果为空**
   - 检查用户是否有兴趣向量
   - 验证新闻向量化状态

3. **缓存未命中**
   - 检查缓存键格式
   - 验证缓存过期时间

### 诊断命令

```csharp
// 检查Redis连接
var isConnected = precomputeService.CheckRedisConnection();

// 获取统计信息
var stats = await precomputeService.GetPrecomputeStatisticsAsync();

// 强制刷新用户推荐
await precomputeService.ForceRefreshUserRecommendationsAsync(userId);

// 清除所有缓存
await precomputeService.ClearAllPrecomputedCacheAsync();
```

## API 接口

### 诊断控制器接口

- `GET /PrecomputeDiagnostic/GetStatistics` - 获取统计信息
- `GET /PrecomputeDiagnostic/RunFullDiagnostic` - 运行完整诊断
- `POST /PrecomputeDiagnostic/TriggerUserPrecompute` - 触发用户预计算
- `POST /PrecomputeDiagnostic/TriggerHotNewsPrecompute` - 触发热门新闻预计算
- `POST /PrecomputeDiagnostic/TriggerComprehensivePrecompute` - 触发综合预计算
- `POST /PrecomputeDiagnostic/ForceRefreshUserRecommendations` - 强制刷新用户推荐
- `POST /PrecomputeDiagnostic/ClearAllCache` - 清除所有缓存
- `POST /PrecomputeDiagnostic/ClearUserCache` - 清除用户缓存
- `GET /PrecomputeDiagnostic/CheckRedisConnection` - 检查Redis连接
- `GET /PrecomputeDiagnostic/RunCompleteTest` - 运行完整测试

## 最佳实践

### 1. 定时任务配置

- 建议在低峰期（如凌晨2-4点）执行预计算
- 根据用户活跃度调整执行频率
- 监控执行时间，避免影响正常服务

### 2. 缓存管理

- 定期清理过期缓存
- 监控Redis内存使用情况
- 为重要用户设置更长的缓存时间

### 3. 性能监控

- 定期运行诊断检查系统健康状态
- 监控缓存命中率，目标 > 80%
- 跟踪推荐响应时间，目标 < 100ms

### 4. 故障恢复

- 实现优雅降级，预计算失败时回退到实时计算
- 设置告警机制，及时发现系统异常
- 保持日志记录，便于问题排查

## 总结

Banyan 预计算系统通过智能的预计算策略和完善的监控机制，为新闻推荐系统提供了高性能、高可用的解决方案。系统具有以下特点：

- **高性能**: 毫秒级推荐响应
- **高可用**: 优雅降级机制
- **易监控**: 完整的诊断工具
- **易维护**: 丰富的管理接口

通过合理配置和监控，该系统能够显著提升用户体验和系统性能。
