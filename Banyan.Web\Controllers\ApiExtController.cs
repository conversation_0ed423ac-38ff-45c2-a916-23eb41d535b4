﻿using Banyan.Apps;
using Banyan.Code;
using Banyan.Domain;
using log4net;
using System;
using System.Web.Mvc;
using Banyan.Web.Filters;
using System.Collections.Generic;
using System.Linq;

namespace Banyan.Web.Controllers
{
    [ExternalAuthFilter]
    public class ApiExtController:Controller
    {
        private static readonly ILog log = LogManager.GetLogger("apiexcontroller");
        private ResearchBLL reserachBll = new ResearchBLL();

        [HttpPost]
        public ActionResult InvestorList() {
           var res = new MemberBLL().GetValidInvestors(false);
           return Json(res);
        }

        [HttpPost]
        public ActionResult InvestorListClean()
        {
            var res = new MemberBLL().GetValidInvestors();
            return Json(res);
        }

        [HttpPost]
        public ActionResult MatchNameList(string names)
        { 
            if(names == null)
            {
                return null;
            }
            names = names.Replace("'", "''");
            Logger.Info($"research match names {names}", "张斌");
            var nametmp = names.Split(',').ToList();
            List<string> name = new List<string>(nametmp);
            foreach(var i in nametmp)
            {
                name.Add(i + " (revisit)");
                name.Add(i + " (revisit) (revisit)");
            }
            var namelist = "(" + name.Aggregate("", (res, str) => res + ",'" + str + "'").Substring(1) + ")";
            var resNames = "";
            object ajaxRes = new ProjectBLL().GetList($"name in {namelist} AND status<>0").Select(val =>
            {
                bool isCanMouBu = val.InteralPTCP.Contains("参谋部");
                resNames += val.Name + ", ";
                return new
                { 
                    Id = val.Id,
                    Name = val.Name,
                    ProjectManager = val.ProjectManager,
                    OtherIntroducer = val.ContributionDetail,
                    Status = val.nextStepStatus,
                    PubTime = val.PubTime.ToString("yyyy-MM-dd"),
                    InMeet = val.InteralPTCP,

                    City = isCanMouBu ? val.city :"",
                    Summary = isCanMouBu ? val.Summary : "",
                    Background = isCanMouBu ? val.Background : "",
                    BusinessData = isCanMouBu ? val.BusinessData : "",
                    FinancialData = isCanMouBu ? val.FinancialData : "",
                    CompareProduct = isCanMouBu ? val.CompareProduct : "",
                    HighLight = isCanMouBu ? val.HighLight : "",
                    Risk = isCanMouBu ? val.Risk : "",
                    NextStep = isCanMouBu ? val.NextStep : "",
                    UpdatedNews = isCanMouBu ? val.UpdatedNews : "",
                };
            }).ToList();
           

            if (resNames.Length > 0)
            {
                Logger.Info($"research match names success: {resNames}", "张斌");
            }
            return Json(ajaxRes);
        }

        [HttpPost]
        public ActionResult reportList(bool text = false, string keyword = "", string startDate = "", string endDate = "")
        {
            var res = reserachBll.GetReportList(text, keyword, startDate, endDate);
            Logger.Info($"research get articles for database, text: {text}, keyword: {keyword} startDate: {startDate} endDate: {endDate}", "张斌");
            return Json(res);
        }

    }
}