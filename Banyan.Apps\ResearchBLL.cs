﻿using Banyan.Code;
using Banyan.MeetDomain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;

namespace Banyan.Apps
{
    public class ResearchBLL : BaseDAL<Article>
    {
        private readonly AjaxResult ajaxResult = null;

        public ResearchBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        public override object Add(Article model)
        {
            ClearCache(model);
            return base.Add(model);
        }

        public override bool Update(Article model)
        {
            ClearCache(model);
            return base.Update(model);
        }

        public override bool Update(Article model, string fldList)
        {
            ClearCache(model);
            return base.Update(model, fldList);
        }

        public bool ClearCache(Article model)
        {
            try
            {
                RedisUtil.Remove(string.Format(RedisKey.Research_model, model.Id));
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取文章缓存
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Article GetModelByCache(int id)
        {
            string cacheKey = string.Format(RedisKey.Research_model, id);
            Article model = RedisUtil.Get<Article>(cacheKey);
            if (model == null)
            {
                model = base.GetModel(id);
                RedisUtil.Set<Article>(cacheKey, model, TimeSpan.FromMinutes(10));
            }
            return model;
        }

        public AjaxResult GetDetail(int id, bool backAll = false)
        {
            Article ProjectModel = null;
            if (id <= 0)
            {
                ProjectModel = new Article();
            }
            else
                ProjectModel = GetModelByCache(id);

            if (backAll)
            {
                ajaxResult.code = (int)ResultCode.success;
                ProjectModel = ProjectModel ?? new Article();
                ProjectModel.PubTimeStr = ProjectModel.PubTime.ToString("yyyy-MM-dd");
                ajaxResult.data = ProjectModel;
                return ajaxResult;
            }
            if (ProjectModel == null || ProjectModel.Status != (int)ResearchStatus.normal)
            {
                ajaxResult.code = (int)ResultCode.notdata;
                return ajaxResult;
            }

            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = new
            {
                ProjectModel.Id,
                ProjectModel.EditorName,
                ProjectModel.Summary,
                ProjectModel.Title,
                ProjectModel.CreatorName,
                PubTimeStr = ProjectModel.PubTime.ToString("yyyy-MM-dd"),
                ProjectModel.CoverUrl,
                ProjectModel.Content
            };

            return ajaxResult;
        }
        public object JOk(object data, string msg = "", int code = 0)
        {
            return new { code, data, msg };
        }

        public object JFail(string msg = "", int code = 1)
        {
            return new { code, msg };
        }
        public object GetAttach(int articleId, Byte types = (Byte)AttachmentEnum.Document)
        {
            if (articleId <= 0)
                return JFail("参数错误");

            Attachment model = new ResearchAttachBLL().GetCache(articleId, types);
            if (model != null && !string.IsNullOrEmpty(model.KeyValue))
            {
                List<string> fullPathList = new List<string>();
                string filePath = "https://research.gaorongvc.com";
                foreach (var item in model.KeyValue.Split(','))
                {
                    fullPathList.Add($"{filePath}{item}");
                }
                model.KeyValue = string.Join(",", fullPathList);
            }
            return JOk(model);
        }

        /// <summary>
        /// 分类角色权限
        /// </summary>
        /// <param name="userModel"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public bool ClassifyToRoleRight(Banyan.Domain.Member userModel, Article model)
        {
            if (userModel.Levels == (byte)Banyan.Domain.MemberLevels.Administrator || userModel.Levels == (byte)Banyan.Domain.MemberLevels.SuperUser)
            {
                return true;
            }
            else if (model.ColumnId == (int)ColumnId.interalReport)
            {
                //        1 消费组 2 技术组 3互联网组 5医疗组 6其他
                //        Tech = 47,
                //Con = 48,
                //Med = 49
                string type;
                if (model.ClassId == 47)
                {
                    type = "2";
                }
                else if (model.ClassId == 48)
                {
                    type = "1";
                }
                else
                {
                    type = "5";
                }
                return model.EditorName == userModel.RealName || userModel.Groups.Contains(type);
            }
            else
            {
                return true;
            }
        }

        public AjaxResult searchByNameHelper(NameValueCollection paramValues, Banyan.Domain.Member userModel, string nametype)
        {

            var articleList = new List<Article>();
            string sqlStr = $"Status={(int)ResearchStatus.normal}";
            // AND ((ToRoleIds is null OR ToRoleIds='') OR CHARINDEX(',{userModel.RoleId},',CONCAT(',',ToRoleIds,',')) > 0) "; //获取用户可以观看的文章

            string orderBy = "Sort DESC,PubTime DESC, ViewCount DESC, CommentCount DESC, CollectCount DESC ";


            string keyWords = Utils.GetRequest(nametype, string.Empty);
            if (!string.IsNullOrEmpty(keyWords))
                sqlStr += $"AND Title like '%{keyWords}%' ";

            Logger.Info(userModel.RealName + " GetArticles:" + sqlStr);
            //默认按排序值、发布时间、点赞数、评论数、浏览数排序
            articleList = GetList(sqlStr, int.MaxValue, 1, "*", orderBy);

            //针对推荐的文章进行权限筛选
            if (articleList != null && articleList.Count() > 0)
            {
                var niceIds = new List<int>();
                foreach (var item in articleList)
                {
                    if (ClassifyToRoleRight(userModel, item))
                        niceIds.Add(item.Id);
                }
                articleList = (from x in articleList
                               where niceIds.Contains(x.Id)
                               select x).ToList();
            }

            //文章内容格式处理
            if (articleList != null && articleList.Count() > 0)
            {
                ajaxResult.data = articleList.Select(item =>
                {
                    return new
                    {
                        item.Id,
                        Name = item.Title,
                        item.Summary,
                        item.CoverUrl,
                        AddTime = item.PubTime.ToString("yyyy-MM-dd HH:mm"),
                        ReportTime = item.ReportTime.ToString("yyyy-MM-dd"),
                        item.CreatorName,
                        item.EditorName
                    };
                });
            }
            else
            {
                ajaxResult.data = articleList;
            }

            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;
        }

        /// <summary>
        /// 文章列表
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public AjaxResult searchByName(NameValueCollection paramValues)
        {
            Banyan.Domain.Member userModel = new MemberBLL().GetLogOnUser();
            //if (userModel == null || userModel.Status != (int)MemberStatus.enable)
            //{
            //    ajaxResult.code = userModel == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
            //    ajaxResult.msg = userModel == null ? "无匹配的身份信息！" : "账号信息异常！";
            //    return ajaxResult;
            //}
            return searchByNameHelper(paramValues, userModel, "Name");
        }

        public AjaxResult searchByNameMobile(NameValueCollection paramValues)
        {
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var userModel = new MemberBLL().GetModelByCache(uid);
            if (userModel == null || userModel.Status != (int)Banyan.Domain.MemberStatus.enable)
            {
                ajaxResult.code = userModel == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = userModel == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }

            return searchByNameHelper(paramValues, userModel, "keywords");
        }
        public AjaxResult GetReportList(bool text = false, string keyword = "", string startDate = "", string endDate = "")
        {
            var dateStr = " and PubTime > '2022-5-20' ";
            var searchStr = "";
            var getText = "";
            if(startDate != "")
            {
                dateStr += $" and PubTime > '{startDate}' ";
            }
            if(endDate != "")
            {
                dateStr += $" and PubTime < '{endDate}'";
            }
            if(keyword != "")
            {
                searchStr = $" and (a2.content like '%{keyword}%' or title like '%{keyword}%' or editorName like '%{keyword}%' or summary like '%{keyword}%' or labels like '%{keyword}%' )";
            }
            if (text)
            {
                getText = "a2.Content, ";
            }

            var list = GetListBySql($"select a2.KeyValue as className,a2.AzureUrl as toRoleIds, {getText}" + @"a.Title, a.EditorName,a.Summary,a.CoverUrl,a.PubTime,a.labels from Article a 
join Attachment a2 
on a.Id = a2.KeyId 
where Status = 1
and ClassId <> 51 and ClassId <> 49 and ClassId <> 48 and ClassId  <> 47 and ClassId <> 14 and ClassId <> 61 and ClassId <> 58 and ClassId <> 57" + $" {dateStr} {searchStr} order by a.Id desc");

            ajaxResult.data = list;
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;
        }

        ///// <summary>
        ///// 分类角色权限
        ///// </summary>
        ///// <param name="usermodel"></param>
        ///// <param name="model"></param>
        ///// <returns></returns>
        //public bool classifytoroleright(member usermodel, research model)
        //{
        //    if (model.classid <= 0)
        //    {
        //        return true;
        //    }
        //    var classifymodel = new classifybll().getmodelbycache(model.classid);
        //    if (classifymodel != null && (string.isnullorempty(classifymodel.toroleids) || $",{classifymodel.toroleids},".contains($",{usermodel.roleid},")))
        //    {
        //        return true;
        //    }
        //    return false;
        //}

        ///// <summary>
        ///// 获取文章数据
        ///// </summary>
        ///// <returns></returns>
        //public ajaxresult getresearch(member usermodel)
        //{
        //    int researchid = utils.getrequestint("aid", 0);
        //    if (researchid <= 0)
        //    {
        //        ajaxresult.code = (int)resultcode.paramerror;
        //        ajaxresult.msg = "参数不合法！";
        //        return ajaxresult;
        //    }
        //    if (usermodel == null || usermodel.status != (int)memberstatus.enable)
        //    {
        //        ajaxresult.code = usermodel == null ? (int)resultcode.nomatchidentity : (int)resultcode.accountdisable;
        //        ajaxresult.msg = usermodel == null ? "无匹配的身份信息！" : "账号信息异常！";
        //        return ajaxresult;
        //    }

        //    var researchmodel = new researchbll().getmodel($"id={researchid} and ((toroleids is null or toroleids='') or charindex(',{usermodel.roleid},',concat(',',toroleids,',')) > 0) ");
        //    if (researchmodel == null)
        //    {
        //        ajaxresult.code = (int)resultcode.noright;
        //        return ajaxresult;
        //    }
        //    else if (researchmodel.status != (int)researchstatus.normal)
        //    {
        //        ajaxresult.code = (int)resultcode.noright;
        //        return ajaxresult;
        //    }

        //    //验证文章分类权限（文章分类是否选择角色）
        //    if (!classifytoroleright(usermodel, researchmodel))
        //    {
        //        ajaxresult.code = (int)resultcode.noright;
        //        return ajaxresult;
        //    }

        //    //浏览量更新
        //    var cahcekey = string.format(rediskey.research_views, researchmodel.id);
        //    var viewscache = redisutil.get<string>(cahcekey);
        //    if (!string.isnullorempty(viewscache))
        //    {
        //        var savetime = convert.todatetime(viewscache.split(',')[0]);
        //        var viewcount = convert.toint32(viewscache.split(',')[1]);
        //        var timespan = datetime.now - savetime;

        //        var upinterval = convert.toint32(configurationmanager.appsettings["viewcountupdateinterval"] ?? "30");
        //        if (timespan.seconds > upinterval)
        //        {
        //            researchmodel.viewcount += viewcount + 1;
        //            var result = update(researchmodel, "viewcount") && redisutil.remove(cahcekey);
        //        }
        //        else
        //        {
        //            redisutil.set<string>(cahcekey, $"{viewscache.split(',')[0]},{viewcount + 1}");
        //        }
        //    }
        //    else
        //    {
        //        redisutil.set<string>(cahcekey, $"{datetime.now.tostring("yyyy-mm-dd hh:mm:ss")},1");
        //    }

        //    if (researchmodel == null)
        //    {
        //        ajaxresult.code = (int)resultcode.notdata;
        //    }
        //    else
        //    {
        //        ajaxresult.code = (int)resultcode.success;
        //        ajaxresult.data = new
        //        {
        //            researchmodel.id,
        //            researchmodel.title,
        //            researchmodel.summary,
        //            researchmodel.pdfurl,
        //            researchmodel.coverurl,
        //            researchmodel.content,
        //            researchmodel.editorname,
        //            researchmodel.collectcount,
        //            researchmodel.commentcount,
        //            researchmodel.prisecount,
        //            addtime = researchmodel.pubtime.tostring("yyyy-mm-dd hh:mm"),
        //            classnames = new classifybll().gettagnames(researchmodel),
        //            iscollect = new collectdetailbll().iscollect(usermodel.id, researchid),
        //            reporttime = researchmodel.reporttime.tostring("yyyy-mm-dd"),
        //            researchmodel.creatorname,
        //        };
        //    }
        //    return ajaxresult;
        //}


        ///// <summary>
        ///// 显示tag
        ///// </summary>
        ///// <param name="list"></param>
        ///// <param name="model"></param>
        ///// <returns></returns>
        //public list<string> gettagnames(list<classify> list, research model)
        //{
        //    list<string> tagnames = new list<string>();
        //    string colname = string.empty;
        //    if (model.columnid == (int)columnid.cases)
        //    {
        //        colname = "案例";
        //    }
        //    else if (model.columnid == (int)columnid.weekly)
        //    {
        //        colname = "周报";
        //    }
        //    else if (model.columnid == (int)columnid.subject)
        //    {
        //        colname = "专题";
        //    }
        //    else if (model.columnid == (int)columnid.interalreport)
        //    {
        //        colname = "内部报告";
        //    }
        //    tagnames.add(colname);

        //    var classname = list.where(x => x.id == model.classid)?.firstordefault()?.name;
        //    if (!string.isnullorempty(classname))
        //        tagnames.add(classname);
        //    return tagnames;
        //}
    }
}

///// <summary>
///// 分页查询记录
///// </summary>
///// <param name="paramValues">页面请求参数</param>
///// <returns></returns>
//public AjaxResult GetPageList()
//{
//    var user = MemberBLL.GetLogPcUser();
//    if (user == null)
//    {
//        ajaxResult.code = (int)ResultCode.noright;
//        ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.noright);
//        return ajaxResult;
//    }
//    int pageIndex = Utils.GetRequestInt("page", 1);
//    int pageSize = Utils.GetRequestInt("limit", 10);
//    //后端限制权限
//    string strWhere = $"Status<>{(int)ResearchStatus.delete} ";
//    if (user.Levels != 2)
//    {
//        strWhere += $"AND (EditorName like '%{user.RealName}%' OR CreatorName like '%{user.RealName}%') ";
//    }
//    string title = Utils.GetRequest("title", string.Empty);
//    if (!string.IsNullOrWhiteSpace(title))
//    {
//        strWhere += $"AND (Title like '%{title}%' OR EditorName like '%{title}%' OR Summary like '%{title}%') ";
//    }

//    int classId = Utils.GetRequestInt("classid", 0);
//    if (classId > 0)
//    {
//        strWhere += $"AND ClassId={classId} ";
//    }
//    Logger.Info(user.RealName + ":" + strWhere);
//    var ResearchList = GetList(strWhere, pageSize, pageIndex, "*", "Sort DESC, PubTime DESC, PriseCount DESC, CommentCount DESC, ViewCount DESC");
//    if (ResearchList != null && ResearchList.Count() > 0)
//    {
//        var classList = new ClassifyBLL().GetList($"Status={(int)ClassifyStatus.enable}", int.MaxValue, 1, "Id,Name,ParentId");
//        if (classList != null && classList.Count() > 0)
//        {
//            foreach (var item in ResearchList)
//            {
//                item.ClassName = classList.Where(x => x.Id == item.ClassId).Select(x => x.Name).FirstOrDefault()?.ToString();
//            }
//        }
//    }

//    ajaxResult.code = (int)ResultCode.success;
//    ajaxResult.data = ResearchList;
//    ajaxResult.count = GetCount(strWhere);
//    return ajaxResult;
//}


///// <summary>
///// 判断分类角色判断
///// </summary>
///// <param name="classId"></param>
///// <param name="roleId"></param>
///// <returns></returns>
//public AjaxResult ClassRoleJudge(int classId, int roleId)
//{
//    var classBll = new ClassifyBLL();
//    if (classId <= 0)
//    {
//        return null;
//    }
//    var classModel = classBll.GetModelByCache(classId);
//    if (classModel == null)
//    {
//        ajaxResult.code = (int)ResultCode.success;
//        ajaxResult.data = null;
//        return ajaxResult;
//    }
//    Logger.Info("classId:" + classId.ToString() + ",classModel.ToRoleIds:" + classModel.ToRoleIds + ",roleId:" + roleId.ToString());
//    if (string.IsNullOrEmpty(classModel.ToRoleIds) || classModel.ToRoleIds.Split(',').Contains(roleId.ToString()))
//    {
//        return null;
//    }
//    ajaxResult.code = (int)ResultCode.noright;
//    ajaxResult.data = null;
//    return ajaxResult;
//}
