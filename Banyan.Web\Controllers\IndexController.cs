﻿using Banyan.Apps;
using Banyan.Code;
using Banyan.Domain;
using Banyan.Web.Filters;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace Banyan.Web.Controllers
{
    public class IndexController : BaseController
    {
        public ActionResult Index()
        {
            var rolelist = new RoleBLL().GetList();
            ViewData["rolelist"] = rolelist;
            ViewData["creatorlist"] = new MemberBLL().GetAllList();
            return View();
        }

        /// <summary>
        /// Chat页面 - 支持被其他网站嵌入使用
        /// </summary>
        /// <param name="id">项目ID</param>
        /// <param name="embedded">是否为嵌入模式</param>
        /// <returns></returns>
        public ActionResult Chat(bool embedded = false)
        {
            ViewData["pingLLM"] = HttpMethods.HttpLLMPing();
            ViewData["embedded"] = embedded; // 标记是否为嵌入模式

            // 设置响应头支持跨域访问和iframe嵌入
            Response.Headers.Add("Access-Control-Allow-Origin", "*");
            Response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type, Authorization");

            // 移除X-Frame-Options限制，允许iframe嵌入
            Response.Headers.Remove("X-Frame-Options");
            // 设置Content Security Policy，允许iframe嵌入
            Response.Headers.Add("Content-Security-Policy", "frame-ancestors *;");

            return View();
        }

        public ActionResult PortfolioExit()
        {
            var rolelist = new RoleBLL().GetList();
            ViewData["rolelist"] = rolelist;
            ViewData["creatorlist"] = new MemberBLL().GetAllList();
            return View();
        }
        /// <summary>
        /// 项目列表
        /// </summary>
        /// <returns></returns>
        public ActionResult Projects()
        {
            var rolelist = new RoleBLL().GetList();
            ViewData["rolelist"] = rolelist;
            return View();
        }
        public ActionResult ProjectContribution()
        {
            ViewData["creatorList"] = new MemberBLL().GetAllList();
            ViewData["portfolioList"] = new PortfolioBLL().GetList("");
            return View();
        }
        public ActionResult Score(int id)
        {
            ViewData["id"] = id;
            return View();
        }
        public ActionResult ExitScore(int id)
        {
            ViewData["id"] = id;
            return View();
        }
        public ActionResult ExplainVectorSearch()
        {
            Member user = new MemberBLL().GetLogOnUser();
            Logger.Info("view explain vectorsearch", user.RealName);
            return View();
        }

        public ActionResult ScoreSet(int id)
        {
            ViewData["id"] = id;
            return View();
        }
        public ActionResult ExitScoreSet(int id)
        {
            ViewData["id"] = id;
            return View();
        }
        public ActionResult Attachments()
        {
            return View();
        }
        /// <summary>
        /// My项目列表
        /// </summary>
        /// <returns></returns>
        public ActionResult MyProjects()
        {
            var rolelist = new RoleBLL().GetList();
            ViewData["rolelist"] = rolelist;
            return View();
        }
        /// <summary>
        /// 添加/编辑项目
        /// </summary>
        /// <returns></returns>
        public ActionResult ProjectSet(int id = 0)
        {
            ViewData["projectId"] = id;
            ViewData["rolelist"] = new RoleBLL().GetList();
            ViewData["creatorList"] = new MemberBLL().GetAllList();
            ViewData["staffList"] = new MemberBLL().GetStaffList();
            ViewData["isProjectManager"] = new ProjectBLL().isProjectManager(id);
            ViewData["pingLLM"] = HttpMethods.HttpLLMPing();
            return View();
        }
        public ActionResult PortfolioExitSet(int id = 0, int preview = 0)
        {
            ViewData["id"] = id;
            ViewData["rolelist"] = new RoleBLL().GetList();
            ViewData["CompanyList"] = new PortfolioBLL().GetNameListByRight();
            ViewData["creatorList"] = new MemberBLL().GetAllList();
            var exitManagerList = memberBll.GetInvestBuybackLegal();
            ViewData["exitManagerList"] = exitManagerList;
            ViewData["fundList"] = new Fund2PortfolioSummaryBLL().GetListStr();
            ViewData["isEditor"] = new PortfolioExitBLL().isEditor(id);
            ViewData["preview"] = preview == 1;
            return View();
        }
        /// <summary>
        /// 评论列表
        /// </summary>
        /// <returns></returns>
        public ActionResult Comments()
        {
            return View();
        }

        /// <summary>
        /// 设置阅读角色
        /// </summary>
        /// <returns></returns>
        public ActionResult RoleSet(int id = 0)
        {
            var artBll = new ProjectBLL();
            Project model = null;
            if (id > 0)
            {
                model = artBll.GetModel(id);
            }
            if (model == null)
            {
                model = new Project();
            }

            var rolelist = new RoleBLL().GetList();
            ViewData["rolelist"] = rolelist;
            return View(model);
        }

        /// <summary>
        /// 预览
        /// </summary>
        /// <returns></returns>
        public ActionResult Preview(int id = 0)
        {
            ViewBag.id = id;
            return View();
        }

        /// <summary>
        /// 预览
        /// </summary>
        /// <returns></returns>
        public ActionResult PicPreview(int id = 0)
        {
            ViewData["model"] = new AttachmentBLL().GetAttachment(id);
            return View();
        }

        /// <summary>
        /// 新闻向量化浏览页面
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页数量</param>
        /// <param name="keywords">搜索关键词</param>
        /// <param name="category">新闻分类</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>新闻向量化浏览页面</returns>
        public ActionResult NewsVector(int page = 1, int pageSize = 10, string keywords = "", string category = "", string startDate = "", string endDate = "")
        {
            var user = new MemberBLL().GetLogOnUser();
            if (user == null)
            {
                return RedirectToAction("Index", "Login");
            }

            ViewData["manager"] = user;
            return View();
        }

        /// <summary>
        /// 获取新闻向量化列表数据
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页数量</param>
        /// <param name="keywords">搜索关键词</param>
        /// <param name="category">新闻分类</param>
        /// <param name="startdate">开始日期</param>
        /// <param name="enddate">结束日期</param>
        /// <param name="sort">排序方式</param>
        /// <param name="vectorStatus">向量化状态（0：未向量化，1：已向量化，2：向量化失败）</param>
        /// <returns>新闻列表数据</returns>
        public JsonResult GetNewsVector(int page = 1, int pageSize = 10, string keywords = "", string category = "", string startdate = "", string enddate = "", string sort = "date", string vectorStatus = "")
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" });
                }

                // 创建参数集合
                var paramValues = new System.Collections.Specialized.NameValueCollection();
                paramValues.Add("page", page.ToString());
                paramValues.Add("limit", pageSize.ToString());
                paramValues.Add("keywords", keywords);
                paramValues.Add("type", category);
                paramValues.Add("startdate", startdate);
                paramValues.Add("enddate", enddate);
                paramValues.Add("uid", user.Id.ToString());

                // 添加向量状态过滤
                if (!string.IsNullOrEmpty(vectorStatus))
                {
                    paramValues.Add("vectorStatus", vectorStatus);
                }

                // 获取新闻数据
                var newsBLL = new NewsBLL();
                var result = newsBLL.GetNews(paramValues);

                // 如果有关键词，尝试使用向量相似度搜索增强结果
                if (!string.IsNullOrEmpty(keywords) && sort == "relevance")
                {
                    try
                    {
                        // 使用NewsRecommendationEngine进行向量相似度搜索
                        var recommendationEngine = new NewsRecommendationEngine();
                        var vectorResults = recommendationEngine.SearchSimilarNewsByText(keywords, pageSize * 2);

                        // 如果有向量搜索结果，与关键词搜索结果合并
                        if (vectorResults != null && vectorResults.Count > 0)
                        {
                            // 合并结果并按相关度排序
                            MergeNewsResults(result, vectorResults);

                            // 记录日志
                            Logger.Info($"向量相似度搜索成功，找到 {vectorResults.Count} 条相关新闻");
                        }
                    }
                    catch (Exception ex)
                    {
                        // 向量搜索失败，记录日志但继续使用关键词搜索结果
                        Logger.Error($"向量相似度搜索失败: {ex.Message}", ex);
                    }
                }

                // 根据排序方式处理结果
                if (result.data != null && result.data is List<News> newsList)
                {
                    switch (sort)
                    {
                        case "date":
                            // 按日期排序（默认已经是按日期排序）
                            break;
                        case "popularity":
                            // 按热度（浏览量）排序
                            newsList = newsList.OrderByDescending(n => n.ViewCount ?? 0).ToList();
                            result.data = newsList;
                            break;
                        case "relevance":
                            // 相关度排序（默认已经处理）
                            break;
                    }
                }

                return Json(result);
            }
            catch (Exception ex)
            {
                Logger.Error($"获取新闻向量化列表失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "获取新闻列表失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取与指定新闻相关的新闻
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <param name="limit">返回数量</param>
        /// <returns>相关新闻列表</returns>
        public JsonResult GetRelatedNews(int newsId, int limit = 5)
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" });
                }

                var newsBLL = new NewsBLL();
                var relatedNews = GetRelatedNewsByVector(newsId, limit);

                return Json(new { code = 0, data = relatedNews });
            }
            catch (Exception ex)
            {
                Logger.Error($"获取相关新闻失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "获取相关新闻失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 新闻详情页面
        /// </summary>
        /// <param name="id">新闻ID</param>
        /// <param name="highlight">高亮关键词</param>
        /// <param name="recommend">是否来自推荐新闻</param>
        /// <returns>新闻详情页面</returns>
        public ActionResult News(int id = 0, string highlight = "", bool recommend = false)
        {
            if (id == 0)
            {
                return RedirectToAction("NewsVector");
            }

            var user = new MemberBLL().GetLogOnUser();
            if (user == null)
            {
                return RedirectToAction("Index", "Login");
            }

            var newsBLL = new NewsBLL();
            var news = newsBLL.GetModel(user, id, recommend);

            ViewData["model"] = news;
            ViewData["name"] = user.RealName;
            ViewData["imgWidth"] = "100%";
            ViewData["highlight"] = highlight?.Replace("(revisit)", "").Trim();

            // 记录用户查看新闻的行为，用于更新用户兴趣画像
            try
            {
                RecordNewsView(user.Id, id, recommend);
            }
            catch (Exception ex)
            {
                Logger.Error($"记录新闻查看行为失败: {ex.Message}", ex);
            }

            return View("~/Views/Login/News.cshtml");
        }

        #region 辅助方法

        /// <summary>
        /// 使用向量相似度搜索新闻
        /// </summary>
        /// <param name="query">搜索关键词</param>
        /// <param name="userId">用户ID</param>
        /// <param name="limit">返回数量</param>
        /// <returns>相似新闻列表</returns>
        private List<News> GetNewsWithVectorSimilarity(string query, int userId, int limit = 10)
        {
            try
            {
                // 这里应该调用NewsRecommendationEngine的方法进行向量相似度搜索
                // 由于我们还没有实现该方法，这里先返回空列表
                return new List<News>();
            }
            catch (Exception ex)
            {
                Logger.Error($"向量相似度搜索失败: {ex.Message}", ex);
                return new List<News>();
            }
        }

        /// <summary>
        /// 获取与指定新闻相关的新闻
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <param name="limit">返回数量</param>
        /// <returns>相关新闻列表</returns>
        private List<News> GetRelatedNewsByVector(int newsId, int limit = 5)
        {
            try
            {
                // 使用NewsRecommendationEngine获取相关新闻
                var recommendationEngine = new NewsRecommendationEngine();
                var relatedNews = recommendationEngine.GetSimilarNews(newsId, limit);

                // 如果向量搜索没有结果，回退到基于标题的搜索
                if (relatedNews == null || relatedNews.Count() == 0)
                {
                    Logger.Info($"向量相似度搜索未找到相关新闻，回退到基于标题的搜索，新闻ID: {newsId}");
                    var newsBLL = new NewsBLL();
                    var news = newsBLL.GetModel(newsId);
                    if (news == null)
                    {
                        return new List<News>();
                    }

                    // 使用现有的方法获取相关新闻
                    return newsBLL.GetRelatedProjects(news.Title).Take(limit).ToList();
                }

                Logger.Info($"向量相似度搜索成功，找到 {relatedNews.Count} 条相关新闻，新闻ID: {newsId}");
                return relatedNews;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取相关新闻失败: {ex.Message}", ex);

                // 出错时回退到基于标题的搜索
                try
                {
                    var newsBLL = new NewsBLL();
                    var news = newsBLL.GetModel(newsId);
                    if (news != null)
                    {
                        return newsBLL.GetRelatedProjects(news.Title).Take(limit).ToList();
                    }
                }
                catch (Exception innerEx)
                {
                    Logger.Error($"回退到基于标题的搜索也失败: {innerEx.Message}", innerEx);
                }

                return new List<News>();
            }
        }

        /// <summary>
        /// 合并新闻结果
        /// </summary>
        /// <param name="result">原始结果</param>
        /// <param name="vectorResults">向量搜索结果</param>
        private void MergeNewsResults(AjaxResult result, List<News> vectorResults)
        {
            if (result.data == null || !(result.data is List<News> originalList))
            {
                return;
            }

            // 获取原始列表中的新闻ID
            var originalIds = new HashSet<int>(originalList.Select(n => n.Id));

            // 添加向量搜索结果中不在原始列表中的新闻
            foreach (var news in vectorResults)
            {
                if (!originalIds.Contains(news.Id))
                {
                    originalList.Add(news);
                }
            }

            // 更新结果
            result.data = originalList;
        }

        /// <summary>
        /// 记录用户查看新闻行为
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="newsId">新闻ID</param>
        /// <param name="recommend">是否来自推荐新闻</param>
        private void RecordNewsView(int userId, int newsId, bool recommend = false)
        {
            try
            {
                // 使用 NewsVectorSearch 记录用户阅读行为
                var newsVectorSearch = new NewsVectorSearch();
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await newsVectorSearch.RecordUserReadNewsAsync(userId, newsId);
                        Logger.Info($"成功记录用户 {userId} 阅读新闻 {newsId}");
                    }
                    catch (Exception asyncEx)
                    {
                        Logger.Error($"异步记录用户阅读行为失败: {asyncEx.Message}", asyncEx);
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Error($"记录用户查看新闻行为失败: {ex.Message}", ex);
            }
        }

        #endregion
        /// <summary>
        /// 手动触发新闻向量化处理
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <returns>处理结果</returns>
        public JsonResult VectorizeNews(int newsId)
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" });
                }

                // 检查管理员权限
                if (user.Levels != (byte)MemberLevels.Administrator && user.Levels != (byte)MemberLevels.SuperUser)
                {
                    return Json(new { code = 1, msg = "权限不足，只有管理员才能执行向量化操作" });
                }

                // 检查新闻是否存在
                var newsBLL = new NewsBLL();
                var news = newsBLL.GetModel(newsId);
                if (news == null)
                {
                    return Json(new { code = 1, msg = "新闻不存在" });
                }

                // 创建向量化服务实例
                var vectorizationService = new NewsVectorizationService();

                // 异步处理向量化
                Task.Run(async () =>
                {
                    try
                    {
                        // 执行向量化处理
                        bool success = await vectorizationService.VectorizeSingleNewsAsync(news, newsBLL);

                        if (success)
                        {
                            Logger.Info($"新闻手动向量化成功，新闻ID: {newsId}，操作用户: {user.RealName}");
                        }
                        else
                        {
                            Logger.Error($"新闻手动向量化失败，新闻ID: {newsId}，操作用户: {user.RealName}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"新闻手动向量化异常，新闻ID: {newsId}，操作用户: {user.RealName}，错误: {ex.Message}", ex);
                    }
                });

                return Json(new { code = 0, msg = "向量化处理已触发，请稍后刷新查看结果" });
            }
            catch (Exception ex)
            {
                Logger.Error($"触发新闻向量化失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "触发向量化处理失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 新闻向量化详情页面
        /// </summary>
        /// <param name="id">新闻ID</param>
        /// <returns>新闻向量化详情页面</returns>
        public ActionResult NewsVectorDetails(int id)
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return RedirectToAction("Index", "Login");
                }

                // 检查管理员权限
                if (user.Levels != (byte)MemberLevels.Administrator && user.Levels != (byte)MemberLevels.SuperUser)
                {
                    return RedirectToAction("NewsVector");
                }

                // 传递用户信息到视图
                ViewData["manager"] = user;

                // 获取新闻详情
                var newsBLL = new NewsBLL();
                var news = newsBLL.GetModel(user, id);
                if (news == null)
                {
                    return RedirectToAction("NewsVector");
                }

                ViewData["news"] = news;

                // 如果新闻已向量化，解析标签分析结果
                if (news.VectorStatus == 1 && !string.IsNullOrEmpty(news.TagAnalysis))
                {
                    try
                    {
                        var tagAnalysis = JsonConvert.DeserializeObject<NewsTagAnalysis>(news.TagAnalysis);
                        ViewData["tagAnalysis"] = tagAnalysis;
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"解析标签分析结果失败，新闻ID: {id}，错误: {ex.Message}", ex);
                    }
                }

                return View();
            }
            catch (Exception ex)
            {
                Logger.Error($"显示新闻向量化详情失败，新闻ID: {id}，错误: {ex.Message}", ex);
                return RedirectToAction("NewsVector");
            }
        }

        /// <summary>
        /// 批量触发新闻向量化处理
        /// </summary>
        /// <param name="vectorStatus">向量化状态过滤（默认为0，即未向量化）</param>
        /// <param name="batchSize">批量大小</param>
        /// <returns>处理结果</returns>
        public JsonResult BatchVectorizeNews(string vectorStatus = "0", int batchSize = 50)
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" });
                }

                // 检查管理员权限
                if (user.Levels != (byte)MemberLevels.Administrator && user.Levels != (byte)MemberLevels.SuperUser)
                {
                    return Json(new { code = 1, msg = "权限不足，只有管理员才能执行批量向量化操作" });
                }

                // 创建向量化调度器实例
                var vectorizationScheduler = new NewsVectorizationScheduler();

                // 异步处理批量向量化
                Task.Run(async () =>
                {
                    try
                    {
                        // 执行批量向量化处理
                        var result = await vectorizationScheduler.TriggerVectorizationAsync(batchSize);

                        Logger.Info($"批量向量化处理完成，总计: {result.TotalProcessed}，成功: {result.SuccessCount}，失败: {result.FailedCount}，操作用户: {user.RealName}");
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"批量向量化处理异常，错误: {ex.Message}，操作用户: {user.RealName}", ex);
                    }
                });

                // 获取符合条件的新闻数量
                var newsBLL = new NewsBLL();
                string whereClause = string.IsNullOrEmpty(vectorStatus) ? "" : $"VectorStatus = {vectorStatus}";
                int count = newsBLL.GetCount(whereClause);

                return Json(new { code = 0, msg = "批量向量化处理已触发", data = new { count } });
            }
            catch (Exception ex)
            {
                Logger.Error($"触发批量向量化失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "触发批量向量化处理失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 测试新闻向量化字段
        /// </summary>
        /// <returns>测试页面</returns>
        public ActionResult TestNewsVector()
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return RedirectToAction("Index", "Login");
                }

                return View();
            }
            catch (Exception ex)
            {
                Logger.Error($"测试新闻向量化字段失败，错误: {ex.Message}", ex);
                return RedirectToAction("NewsVector");
            }
        }
        
        /// <summary>
        /// 获取新闻向量化调度器状态
        /// </summary>
        /// <param name="includeDetailedStats">是否包含详细统计信息</param>
        /// <returns>调度器状态</returns>
        public JsonResult GetNewsVectorizationSchedulerStatus(bool includeDetailedStats = false)
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" });
                }

                // 检查管理员权限
                if (user.Levels != (byte)MemberLevels.Administrator && user.Levels != (byte)MemberLevels.SuperUser)
                {
                    return Json(new { code = 1, msg = "权限不足，只有管理员才能查看调度器状态" });
                }

                var status = NewsVectorizationSchedulerManager.GetStatus(includeDetailedStats);
                return Json(new { code = 0, data = status });
            }
            catch (Exception ex)
            {
                Logger.Error($"获取新闻向量化调度器状态失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "获取调度器状态失败: " + ex.Message });
            }
        }
        
        /// <summary>
        /// 控制新闻向量化调度器（启动/停止/重启）
        /// </summary>
        /// <param name="action">操作类型（start/stop/restart）</param>
        /// <returns>操作结果</returns>
        public JsonResult ControlNewsVectorizationScheduler(string action)
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" });
                }

                // 检查管理员权限
                if (user.Levels != (byte)MemberLevels.Administrator && user.Levels != (byte)MemberLevels.SuperUser)
                {
                    return Json(new { code = 1, msg = "权限不足，只有管理员才能控制向量化调度器" });
                }
                
                switch (action?.ToLower())
                {
                    case "start":
                        NewsVectorizationSchedulerManager.Initialize();
                        Logger.Info($"用户 {user.RealName} 启动了新闻向量化调度器");
                        return Json(new { code = 0, msg = "新闻向量化调度器已启动" });
                        
                    case "stop":
                        NewsVectorizationSchedulerManager.Shutdown();
                        Logger.Info($"用户 {user.RealName} 停止了新闻向量化调度器");
                        return Json(new { code = 0, msg = "新闻向量化调度器已停止" });
                        
                    case "restart":
                        NewsVectorizationSchedulerManager.Restart();
                        Logger.Info($"用户 {user.RealName} 重启了新闻向量化调度器");
                        return Json(new { code = 0, msg = "新闻向量化调度器已重启" });
                        
                    default:
                        return Json(new { code = 1, msg = "无效的操作类型，有效值为：start, stop, restart" });
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"控制新闻向量化调度器失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "控制调度器失败: " + ex.Message });
            }
        }
        
        /// <summary>
        /// 获取新闻标签分析数据
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <returns>标签分析数据</returns>
        public JsonResult GetNewsTagAnalysis(int newsId)
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" }, JsonRequestBehavior.AllowGet);
                }

                // 获取新闻详情
                var newsBLL = new NewsBLL();
                var news = newsBLL.GetModel(newsId);
                if (news == null)
                {
                    return Json(new { code = 1, msg = "新闻不存在" }, JsonRequestBehavior.AllowGet);
                }

                // 创建默认的空数据结构
                var emptyData = new NewsTagAnalysis
                {
                    MainTags = new List<NewsTag>(),
                    SecondaryTags = new List<NewsTag>(),
                    SemanticKeywords = new List<NewsTag>()
                };

                // 检查新闻是否已向量化
                if (news.VectorStatus != 1 || string.IsNullOrEmpty(news.TagAnalysis))
                {
                    Logger.Info($"新闻ID: {newsId} 未向量化或标签分析为空，VectorStatus: {news.VectorStatus}，TagAnalysis为空: {string.IsNullOrEmpty(news.TagAnalysis)}");
                    return Json(new { code = 0, data = emptyData }, JsonRequestBehavior.AllowGet);
                }

                try
                {
                    // 记录原始TagAnalysis内容用于调试
                    Logger.Info($"新闻ID: {newsId} TagAnalysis内容长度: {news.TagAnalysis.Length}，前100字符: {news.TagAnalysis.Substring(0, Math.Min(100, news.TagAnalysis.Length))}");

                    // 验证JSON格式
                    if (!IsValidJson(news.TagAnalysis))
                    {
                        Logger.Warn($"新闻ID: {newsId} TagAnalysis不是有效的JSON格式");
                        return Json(new { code = 0, data = emptyData }, JsonRequestBehavior.AllowGet);
                    }

                    // 解析标签分析结果
                    var tagAnalysis = JsonConvert.DeserializeObject<NewsTagAnalysis>(news.TagAnalysis);

                    // 验证解析结果并修复空值
                    if (tagAnalysis == null)
                    {
                        Logger.Warn($"新闻ID: {newsId} 标签分析解析结果为null");
                        tagAnalysis = emptyData;
                    }
                    else
                    {
                        // 确保所有列表都不为null
                        if (tagAnalysis.MainTags == null) tagAnalysis.MainTags = new List<NewsTag>();
                        if (tagAnalysis.SecondaryTags == null) tagAnalysis.SecondaryTags = new List<NewsTag>();
                        if (tagAnalysis.SemanticKeywords == null) tagAnalysis.SemanticKeywords = new List<NewsTag>();
                    }

                    // 记录用户查看标签分析的行为
                    Logger.Info($"用户 {user.RealName} 查看了新闻ID: {newsId} 的标签分析，主要标签: {tagAnalysis.MainTags.Count}，次要标签: {tagAnalysis.SecondaryTags.Count}，语义关键词: {tagAnalysis.SemanticKeywords.Count}");

                    return Json(new { code = 0, data = tagAnalysis }, JsonRequestBehavior.AllowGet);
                }
                catch (JsonException jsonEx)
                {
                    Logger.Error($"JSON解析失败，新闻ID: {newsId}，错误: {jsonEx.Message}，TagAnalysis内容: {news.TagAnalysis}", jsonEx);
                    return Json(new { code = 0, data = emptyData }, JsonRequestBehavior.AllowGet);
                }
                catch (Exception ex)
                {
                    Logger.Error($"解析标签分析结果失败，新闻ID: {newsId}，错误: {ex.Message}", ex);
                    return Json(new { code = 0, data = emptyData }, JsonRequestBehavior.AllowGet);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"获取新闻标签分析失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "获取新闻标签分析失败: " + ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 验证字符串是否为有效的JSON格式
        /// </summary>
        /// <param name="jsonString">要验证的JSON字符串</param>
        /// <returns>是否为有效JSON</returns>
        private bool IsValidJson(string jsonString)
        {
            if (string.IsNullOrWhiteSpace(jsonString))
                return false;

            try
            {
                JToken.Parse(jsonString);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 修复无效的TagAnalysis数据
        /// </summary>
        /// <returns>修复结果</returns>
        public JsonResult FixInvalidTagAnalysisData()
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" }, JsonRequestBehavior.AllowGet);
                }

                // 检查用户权限（可以根据需要添加管理员权限检查）
                Logger.Info($"用户 {user.RealName} 开始修复TagAnalysis数据");

                var newsBLL = new NewsBLL();
                var fixedCount = 0;
                var totalCount = 0;
                var errorCount = 0;

                // 创建空的标签分析JSON
                var emptyTagAnalysis = new NewsTagAnalysis
                {
                    MainTags = new List<NewsTag>(),
                    SecondaryTags = new List<NewsTag>(),
                    SemanticKeywords = new List<NewsTag>()
                };
                var emptyTagAnalysisJson = JsonConvert.SerializeObject(emptyTagAnalysis);

                // 获取所有新闻记录（分批处理）
                var pageSize = 100;
                var pageIndex = 1;
                List<News> newsList;

                do
                {
                    newsList = newsBLL.GetList("1=1", pageSize, pageIndex, "Id, TagAnalysis, VectorStatus", "Id");
                    totalCount += newsList.Count;

                    foreach (var news in newsList)
                    {
                        var needsUpdate = false;
                        var originalTagAnalysis = news.TagAnalysis;

                        // 检查是否需要修复
                        if (string.IsNullOrEmpty(news.TagAnalysis))
                        {
                            news.TagAnalysis = emptyTagAnalysisJson;
                            needsUpdate = true;
                        }
                        else if (!IsValidJson(news.TagAnalysis))
                        {
                            news.TagAnalysis = emptyTagAnalysisJson;
                            needsUpdate = true;
                        }
                        else
                        {
                            // 尝试解析JSON，检查结构是否完整
                            try
                            {
                                var tagAnalysis = JsonConvert.DeserializeObject<NewsTagAnalysis>(news.TagAnalysis);
                                if (tagAnalysis == null ||
                                    tagAnalysis.MainTags == null ||
                                    tagAnalysis.SecondaryTags == null ||
                                    tagAnalysis.SemanticKeywords == null)
                                {
                                    news.TagAnalysis = emptyTagAnalysisJson;
                                    needsUpdate = true;
                                }
                            }
                            catch
                            {
                                news.TagAnalysis = emptyTagAnalysisJson;
                                needsUpdate = true;
                            }
                        }

                        // 更新记录
                        if (needsUpdate)
                        {
                            try
                            {
                                var success = newsBLL.Update(news, "TagAnalysis");
                                if (success)
                                {
                                    fixedCount++;
                                    Logger.Info($"修复新闻ID: {news.Id} TagAnalysis数据，原始长度: {originalTagAnalysis?.Length ?? 0}");
                                }
                                else
                                {
                                    errorCount++;
                                    Logger.Error($"修复新闻ID: {news.Id} TagAnalysis数据失败");
                                }
                            }
                            catch (Exception ex)
                            {
                                errorCount++;
                                Logger.Error($"修复新闻ID: {news.Id} TagAnalysis数据异常: {ex.Message}", ex);
                            }
                        }
                    }

                    pageIndex++;
                } while (newsList.Count == pageSize);

                Logger.Info($"TagAnalysis数据修复完成，总记录: {totalCount}，修复: {fixedCount}，错误: {errorCount}");

                return Json(new
                {
                    code = 0,
                    msg = "修复完成",
                    data = new
                    {
                        totalCount = totalCount,
                        fixedCount = fixedCount,
                        errorCount = errorCount
                    }
                }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error($"修复TagAnalysis数据失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "修复失败: " + ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }
    }
}