# Design Document

## Overview

This design implements pagination and infinite scroll functionality for the news recommendations page. The solution provides a seamless user experience by loading content in batches and automatically fetching more results as users scroll. The implementation focuses on performance optimization, error handling, and mobile compatibility.

## Architecture

### Frontend Components

1. **Pagination Manager**: Handles page state and loading indicators
2. **Content Renderer**: Dynamically renders news cards and manages DOM updates
3. **Scroll Handler**: Simple scroll-to-bottom detection for loading more content

### Backend Modifications

1. **Enhanced API Endpoints**: Add pagination parameters to existing recommendation endpoints
2. **Performance Optimization**: Implement efficient database queries with OFFSET/LIMIT
3. **Caching Strategy**: Cache paginated results to improve response times

## Components and Interfaces

### Frontend JavaScript Components

#### PaginationManager Class
```javascript
class PaginationManager {
    constructor(options) {
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalCount = 0;
        this.isLoading = false;
        this.hasMoreData = true;
        this.filters = {};
    }
    
    async loadNextPage();
    resetPagination();
    updateFilters(newFilters);
    canLoadMore();
}
```

#### Simple Scroll Handler
```javascript
// Simple scroll event handler
$(window).scroll(function() {
    if ($(window).scrollTop() + $(window).height() >= $(document).height() - 200) {
        if (!isLoading && hasMoreData) {
            loadNextPage();
        }
    }
});
```

#### ContentRenderer Class
```javascript
class ContentRenderer {
    constructor(containerSelector) {
        this.container = $(containerSelector);
        this.viewMode = 'card'; // 'card' or 'list'
    }
    
    appendNewsCards(newsData);
    clearContent();
    showLoadingIndicator();
    hideLoadingIndicator();
    showEndMessage();
}
```

### Backend API Enhancements

#### Enhanced GetRecommendations Endpoint
```csharp
public class RecommendationRequest
{
    public string Category { get; set; }
    public string Source { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public double Threshold { get; set; } = 0.4;
}

public class PaginatedResponse<T>
{
    public List<T> Data { get; set; }
    public int TotalCount { get; set; }
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
    public bool HasNextPage { get; set; }
    public int TotalPages { get; set; }
}
```

#### NewsVectorSearch Service Updates
```csharp
public async Task<PaginatedResponse<NewsVectorSimilarity>> GetRecommendationsPaginated(
    int userId,
    RecommendationRequest request)
{
    // Calculate offset
    int offset = (request.Page - 1) * request.PageSize;
    
    // Get total count for pagination info
    int totalCount = await GetRecommendationsCount(userId, request);
    
    // Get paginated results
    var results = await GetRecommendedNewsByInterest(
        userId, 
        request.PageSize, 
        request.Threshold, 
        CreateFilters(request),
        offset);
    
    return new PaginatedResponse<NewsVectorSimilarity>
    {
        Data = results,
        TotalCount = totalCount,
        CurrentPage = request.Page,
        PageSize = request.PageSize,
        HasNextPage = (request.Page * request.PageSize) < totalCount,
        TotalPages = (int)Math.Ceiling((double)totalCount / request.PageSize)
    };
}
```

## Data Models

### Frontend State Management
```javascript
const paginationState = {
    currentPage: 1,
    pageSize: 20,
    totalCount: 0,
    isLoading: false,
    hasMoreData: true,
    loadedNewsIds: new Set(), // Prevent duplicates
    filters: {
        category: '',
        source: '',
        startDate: '',
        endDate: ''
    }
};
```

### API Response Structure
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "data": [...], // News items
        "totalCount": 150,
        "currentPage": 2,
        "pageSize": 20,
        "hasNextPage": true,
        "totalPages": 8
    }
}
```

## Error Handling

### Frontend Error Scenarios
1. **Network Failures**: Show retry button with exponential backoff
2. **Server Errors**: Display user-friendly error messages
3. **Empty Results**: Show appropriate "no more content" message
4. **Duplicate Requests**: Prevent multiple simultaneous requests

### Backend Error Handling
1. **Database Timeouts**: Implement query optimization and fallback strategies
2. **Memory Issues**: Limit maximum page size and implement result streaming
3. **Cache Failures**: Graceful degradation to direct database queries

### Error Recovery Strategies
```javascript
class ErrorHandler {
    constructor() {
        this.retryAttempts = 0;
        this.maxRetries = 3;
        this.retryDelay = 1000; // Start with 1 second
    }
    
    async handleError(error, retryCallback) {
        if (this.retryAttempts < this.maxRetries) {
            this.retryAttempts++;
            await this.delay(this.retryDelay * this.retryAttempts);
            return await retryCallback();
        }
        
        this.showErrorMessage(error);
        return null;
    }
}
```

## Testing Strategy

### Unit Tests
1. **PaginationManager**: Test page calculations, state management
2. **ScrollMonitor**: Test scroll detection accuracy
3. **ContentRenderer**: Test DOM manipulation and view switching
4. **API Client**: Test request handling and error scenarios

### Integration Tests
1. **End-to-End Pagination**: Test complete pagination flow
2. **Filter Integration**: Test pagination with various filter combinations
3. **Performance Tests**: Test with large datasets and slow networks
4. **Mobile Compatibility**: Test touch scrolling and responsive behavior

### Test Scenarios
```javascript
describe('Pagination Functionality', () => {
    test('should load initial page correctly');
    test('should load next page on scroll');
    test('should handle network errors gracefully');
    test('should reset pagination when filters change');
    test('should prevent duplicate requests');
    test('should show end message when no more data');
});
```

## Performance Optimizations

### Frontend Optimizations
1. **Throttled Scroll Events**: Prevent excessive scroll event handling
2. **Duplicate Request Prevention**: Ensure only one request at a time
3. **Simple DOM Append**: Efficiently append new content to existing container

### Backend Optimizations
1. **Database Indexing**: Ensure proper indexes for pagination queries
2. **Query Optimization**: Use OFFSET/LIMIT efficiently
3. **Result Caching**: Cache paginated results with appropriate TTL
4. **Connection Pooling**: Optimize database connection usage

### Caching Strategy
```csharp
public class PaginationCacheManager
{
    private const int CACHE_DURATION_MINUTES = 15;
    
    public string BuildCacheKey(int userId, RecommendationRequest request)
    {
        return $"recommendations:paginated:{userId}:{request.GetHashCode()}:{request.Page}";
    }
    
    public async Task<PaginatedResponse<T>> GetOrSetAsync<T>(
        string cacheKey, 
        Func<Task<PaginatedResponse<T>>> factory)
    {
        var cached = await _cache.GetAsync<PaginatedResponse<T>>(cacheKey);
        if (cached != null) return cached;
        
        var result = await factory();
        await _cache.SetAsync(cacheKey, result, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));
        return result;
    }
}
```

## Scroll Detection

### Simple Scroll-to-Bottom Detection
```javascript
// Basic scroll detection - trigger when user is 200px from bottom
function setupScrollHandler() {
    $(window).scroll(function() {
        if ($(window).scrollTop() + $(window).height() >= $(document).height() - 200) {
            if (!paginationState.isLoading && paginationState.hasMoreData) {
                loadNextPage();
            }
        }
    });
}
```

### Loading States
1. **Initial Load**: Show loading spinner for first page
2. **Infinite Scroll**: Show bottom loading indicator for additional pages
3. **End State**: Display "no more results" message when all data is loaded
4. **Error State**: Show retry button on failed requests