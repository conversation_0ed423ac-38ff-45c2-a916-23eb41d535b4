﻿using Banyan.Code;
using Banyan.Domain;
using System;
using System.Collections.Specialized;
using System.Linq;

namespace Banyan.Apps
{
    public class ProjectMemoBLL : DAL.Base.BaseDAL<ProjectMemo>
    {

        private readonly AjaxResult ajaxResult = null;

        public ProjectMemoBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        /// <summary>
        /// 分页查询记录
        /// </summary>
        /// <param name="paramValues">页面请求参数</param>
        /// <returns></returns>
        public AjaxResult GetPageList(NameValueCollection paramValues)
        {
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            int pid = WebHelper.GetValueInt("pid", 0, paramValues);

            string strWhere = "";

            if (uid > 0)
            {
                strWhere += $"AND UserId={uid} ";
            }
            if (pid > 0)
            {
                strWhere += $"AND ProjectId={pid} ";
            }

            var memoList = GetList(strWhere, pageSize, pageIndex, "*", "PubTime DESC");
            if (memoList != null && memoList.Count() > 0)
            {
                var users = new MemberBLL().GetList($"Id in({string.Join(",", memoList.Select(x => x.UserId).Distinct().ToArray())})");
                foreach (var item in memoList)
                {
                    item.UserName = users.Where(x => x.Id == item.UserId)?.FirstOrDefault()?.RealName;
                }
            }

            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = memoList;
            ajaxResult.count = GetCount(strWhere);
            return ajaxResult;
        }

        /// <summary>
        /// 属性更新
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public AjaxResult memoSet(NameValueCollection paramValues, Member user)
        {
            if (user == null)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = "请先登录";
                return ajaxResult;
            }
            if (user.Levels != (int)MemberLevels.Administrator && user.Levels != (int)MemberLevels.SuperUser)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = "没有权限，非法操作";
                return ajaxResult;
            }

            int pid = WebHelper.GetValueInt("pid", 0, paramValues);
            string memo = WebHelper.GetValue("memo", "", paramValues);
            string mtype = WebHelper.GetValue("mtype", "", paramValues);
            string mdate = WebHelper.GetValue("mdate", "", paramValues);
            var model = new ProjectMemo
            {
                ProjectId = pid,
                UserId = user.Id,
                Memo = memo, // use nvarchar
                PubTime = Convert.ToDateTime(mdate),
                Type = mtype,
            };
            Logger.Info($"{user.RealName} save memo");
            ajaxResult.data = Convert.ToInt32(Add(model)) > 0;
            ajaxResult.code = (bool)ajaxResult.data ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }

        /// <summary>
        ///  保存会议纪要信息
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public AjaxResult memoSave(ProjectMemo memo, Member user)
        {
            if (user == null)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = "请先登录";
                return ajaxResult;
            }
            if (user.Levels != (int)MemberLevels.Administrator && user.Levels != (int)MemberLevels.SuperUser)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = "没有权限，非法操作";
                return ajaxResult;
            }
            if (memo.Id <= 0)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = "参数错误";
                return ajaxResult;
            }

            ProjectMemo model = GetModel(memo.Id);
            if (model == null)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = "会议记录不存在";
                return ajaxResult;
            }
            model.Type = memo.Type;
            model.PubTime = memo.PubTime;
            model.Memo = memo.Memo;
            model.AddTime = memo.AddTime;

            ajaxResult.data = Update(model, "Type,PubTime,Memo,AddTime");
            Logger.Info($"{user.RealName} save memo");
            ajaxResult.code = (bool)ajaxResult.data ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }



        /// <summary>
        ///  保存会议纪要信息
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public AjaxResult memoDelete(ProjectMemo memo, Member user)
        {
            if (user == null)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = "请先登录";
                return ajaxResult;
            }
            if (user.Levels != (int)MemberLevels.Administrator && user.Levels != (int)MemberLevels.SuperUser)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = "没有权限，非法操作";
                return ajaxResult;
            }
            if (memo.Id <= 0)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = "参数错误";
                return ajaxResult;
            }

            ajaxResult.data = Delete(memo.Id);
            Logger.Info($"{user.RealName} delete memo {memo.Id}");
            ajaxResult.code = (int)ajaxResult.data != 0 ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }


        /// <summary>
        /// 添加memo
        /// </summary>
        /// <param name="user"></param>
        /// <param name="pid"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        public AjaxResult Save(ProjectMemo memo)
        {
            AjaxResult ajaxResult = new AjaxResult();
            var user = new MemberBLL().GetLogOnUser(memo.UserId);
            if (user == null)
            {
                ajaxResult.code = (int)ResultCode.unlogin;
                ajaxResult.msg = "请先登录";
                return ajaxResult;
            }

            if (user.Id > 0 && memo.ProjectId > 0)
            {
                string strWhere = $" UserId={user.Id} AND ProjectId={memo.ProjectId} ";
                var memoList = GetModelBySql(strWhere);
                if (memoList.Id > 0)
                {
                    memo.Id = memoList.Id;
                    ajaxResult.data = Update(memo);
                }
                else
                {
                    ajaxResult.data = Convert.ToInt32(Add(memo)) > 0;
                }
            }
            else
            {
                ajaxResult.data = Convert.ToInt32(Add(memo)) > 0;
            }

            ajaxResult.code = memo.Id > 0 ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }

        /// <summary>
        /// 获取评分
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public AjaxResult Getmemos(NameValueCollection paramValues)
        {
            int ProjectId = WebHelper.GetValueInt("pid", 0, paramValues);
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 100, paramValues);

            if (ProjectId < 0)
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.paramerror);
                return ajaxResult;
            }

            ajaxResult.code = (int)ResultCode.success;

            var memoList = GetList($" ProjectId={ProjectId} ", pageSize, pageIndex, "*", "PubTime DESC");
            if (memoList != null && memoList.Count() > 0)
            {
                var userAvatars = new MemberBLL().GetList($"{string.Join(",", memoList.Select(x => x.UserId).ToArray())}"); //查询用户头像信息
                ajaxResult.data = memoList.Select(item =>
                {
                    var user = userAvatars.Where(x => x.Id == item.UserId)?.FirstOrDefault();
                    return new
                    {
                        item.Id,
                        item.Memo,
                        item.ProjectId,
                        item.UserId,
                        item.Type,
                        UserName = user?.RealName,
                        UserAvatar = user?.Avatar,
                        PubTime = item.PubTime.ToString("yyyy-MM-dd"),
                        AddTime = item.AddTime.ToString("yyyy-MM-dd HH:mm"),
                    };
                });
            }
            return ajaxResult;
        }

    }
}
