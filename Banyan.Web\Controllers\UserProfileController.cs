using Banyan.Apps;
using Banyan.Code;
using Banyan.Domain;
using Banyan.Web.Filters;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace Banyan.Web.Controllers
{
    [AuthFilter]
    public class UserProfileController : BaseController
    {
        private readonly UserProfileBLL _userProfileBLL;
        private readonly UserInterestTagBLL _userInterestTagBLL;
        private readonly UserTagRelationBLL _userTagRelationBLL;
        private readonly Log _logger;

        public UserProfileController()
        {
            _userProfileBLL = new UserProfileBLL();
            _userInterestTagBLL = new UserInterestTagBLL();
            _userTagRelationBLL = new UserTagRelationBLL();
            _logger = LogFactory.GetLogger("UserProfileController");
        }

        /// <summary>
        /// 批量更新用户画像
        /// </summary>
        /// <param name="userIds">用户ID列表（JSON字符串）</param>
        /// <param name="userNames">用户名称列表（JSON字符串）</param>
        /// <returns>更新结果</returns>
        [HttpPost]
        public async Task<JsonResult> BatchUpdateProfiles(string userIds, string userNames)
        {
            try
            {
                _logger.Info($"开始批量更新用户画像，参数：userIds={userIds}, userNames={userNames}");
                
                List<int> ids = null;
                List<string> names = null;
                
                try
                {
                    if (!string.IsNullOrEmpty(userIds))
                    {
                        ids = Newtonsoft.Json.JsonConvert.DeserializeObject<List<int>>(userIds);
                    }
                    
                    if (!string.IsNullOrEmpty(userNames))
                    {
                        names = Newtonsoft.Json.JsonConvert.DeserializeObject<List<string>>(userNames);
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"解析用户ID或名称失败: {ex.Message}");
                }
                
                var result = await _userProfileBLL.BatchUpdateProfiles(ids, names);
                return Json(result);
            }
            catch (Exception ex)
            {
                _logger.Error($"批量更新用户画像失败: {ex.Message}");
                return Json(new AjaxResult
                {
                    code = (int)ResultCode.exception,
                    msg = "批量更新用户画像失败: " + ex.Message
                });
            }
        }

        /// <summary>
        /// 获取用户兴趣标签
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户兴趣标签列表</returns>
        [HttpGet]
        public async Task<JsonResult> GetUserInterestTags(int userId)
        {
            try
            {
                _logger.Info($"获取用户兴趣标签，用户ID: {userId}");
                
                if (userId <= 0)
                {
                    return Json(new AjaxResult
                    {
                        code = (int)ResultCode.failed,
                        msg = "无效的用户ID"
                    }, JsonRequestBehavior.AllowGet);
                }
                
                // 获取用户标签关联
                var tagRelations = await _userTagRelationBLL.GetUserTagRelationsAsync(userId);
                
                if (tagRelations == null || !tagRelations.Any())
                {
                    return Json(new AjaxResult
                    {
                        code = (int)ResultCode.notdata,
                        msg = "用户没有兴趣标签"
                    }, JsonRequestBehavior.AllowGet);
                }
                
                // 按权重排序
                tagRelations = tagRelations.OrderByDescending(t => t.Weight).ToList();
                
                // 获取标签详情
                var tagDetails = new List<object>();
                foreach (var relation in tagRelations)
                {
                    var tag = _userInterestTagBLL.GetTagById(relation.TagId);
                    if (tag != null)
                    {
                        tagDetails.Add(new
                        {
                            Id = tag.Id,
                            Name = tag.Name,
                            Category = tag.Category,
                            Weight = relation.Weight,
                            ClickCount = relation.ClickCount
                        });
                    }
                }
                
                return Json(new AjaxResult
                {
                    code = (int)ResultCode.success,
                    data = tagDetails
                }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                _logger.Error($"获取用户兴趣标签失败: {ex.Message}");
                return Json(new AjaxResult
                {
                    code = (int)ResultCode.exception,
                    msg = "获取用户兴趣标签失败: " + ex.Message
                }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 获取多个用户的兴趣标签
        /// </summary>
        /// <param name="userIds">用户ID列表（JSON字符串）</param>
        /// <returns>多个用户的兴趣标签</returns>
        [HttpPost]
        public async Task<JsonResult> GetMultipleUserInterestTags(string userIds)
        {
            try
            {
                _logger.Info($"获取多个用户的兴趣标签，参数：userIds={userIds}");
                
                List<int> ids = null;
                
                try
                {
                    if (!string.IsNullOrEmpty(userIds))
                    {
                        ids = Newtonsoft.Json.JsonConvert.DeserializeObject<List<int>>(userIds);
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"解析用户ID失败: {ex.Message}");
                    return Json(new AjaxResult
                    {
                        code = (int)ResultCode.failed,
                        msg = "解析用户ID失败"
                    });
                }
                
                if (ids == null || !ids.Any())
                {
                    return Json(new AjaxResult
                    {
                        code = (int)ResultCode.failed,
                        msg = "未提供有效的用户ID"
                    });
                }
                
                // 获取所有用户的标签
                var userTagsList = new List<object>();

                foreach (var userId in ids)
                {
                    try
                    {
                        // 获取用户标签关联
                        var tagRelations = await _userTagRelationBLL.GetUserTagRelationsAsync(userId);

                        var tagDetails = new List<object>();
                        if (tagRelations != null && tagRelations.Any())
                        {
                            // 按权重排序
                            tagRelations = tagRelations.OrderByDescending(t => t.Weight).ToList();

                            // 获取标签详情
                            foreach (var relation in tagRelations)
                            {
                                var tag = _userInterestTagBLL.GetTagById(relation.TagId);
                                if (tag != null)
                                {
                                    tagDetails.Add(new
                                    {
                                        Id = tag.Id,
                                        Name = tag.Name,
                                        Category = tag.Category,
                                        Weight = relation.Weight,
                                        ClickCount = relation.ClickCount
                                    });
                                }
                            }
                        }

                        // 添加用户及其标签到结果列表
                        userTagsList.Add(new
                        {
                            UserId = userId,
                            Tags = tagDetails
                        });
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"获取用户 {userId} 的兴趣标签失败: {ex.Message}");
                        // 出错时也添加空数组
                        userTagsList.Add(new
                        {
                            UserId = userId,
                            Tags = new List<object>()
                        });
                    }
                }

                // 转换为字典格式以便前端使用
                var resultDict = userTagsList.ToDictionary(
                    item => ((dynamic)item).UserId.ToString(),
                    item => ((dynamic)item).Tags
                );

                return Json(new AjaxResult
                {
                    code = (int)ResultCode.success,
                    data = resultDict
                });
            }
            catch (Exception ex)
            {
                _logger.Error($"获取多个用户的兴趣标签失败: {ex.Message}");
                return Json(new AjaxResult
                {
                    code = (int)ResultCode.exception,
                    msg = "获取多个用户的兴趣标签失败: " + ex.Message
                });
            }
        }

        ///// <summary>
        ///// 重写Json方法以支持更大的JSON长度
        ///// </summary>
        //protected override JsonResult Json(object data, string contentType, Encoding contentEncoding, JsonRequestBehavior behavior)
        //{
        //    return new JsonResult()
        //    {
        //        Data = data,
        //        ContentType = contentType,
        //        ContentEncoding = contentEncoding,
        //        JsonRequestBehavior = behavior,
        //        MaxJsonLength = int.MaxValue
        //    };
        //}
    }
}