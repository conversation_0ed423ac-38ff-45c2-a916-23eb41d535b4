﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.Linq;


namespace Banyan.Apps
{
    public class FundBasicInfoComparer : IEqualityComparer<FundBasicInfo>
    {
        public bool Equals(FundBasicInfo x, FundBasicInfo y)
        {
            if (x == null)
                return y == null;
            return x.fundFamillyName.Equals(y.fundFamillyName);
        }

        public int GetHashCode(FundBasicInfo obj)
        {
            return 1;
        }
    }
    public class FundBasicInfoBLL : BaseDAL<FundBasicInfo>
    {
        private readonly AjaxResult ajaxResult = null;

        public FundBasicInfoBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }
        //public List<FundBasicInfo> GetList()
        //{
        //    var fundList = base.GetList("fundFamillyName Not like '%Test%'");
        //    fundList = fundList.Where(a => !a.fundFamillyName.Equals("Rongqu")).Distinct(new FundBasicInfoComparer()).OrderBy(a => a.fundFamillyName).ToList();
        //    //var fundList = base.GetList().Select(val => val.fundName).Where(val => !val.Contains("Test")).ToList();
        //    return fundList;
        //}
        public List<string> GetList()
        {
            var fundList = base.GetList("fundFamillyName Not like '%Test%'");
            var res = fundList.Where(a => !a.fundFamillyName.Equals("Rongqu")).Distinct(new FundBasicInfoComparer()).OrderBy(a => a.fundFamillyName).Select(val => val.fundFamillyName).ToList();
            //var fundList = base.GetList().Select(val => val.fundName).Where(val => !val.Contains("Test")).ToList();
            return res;
        }
        public List<FundBasicInfo> GetFundDetailList()
        {
            var fundList = base.GetList("fundFamillyName Not like '%Test%' AND fundFamillyName Not like '%榕和%'");
            var res = fundList.OrderBy(a => a.fundFamillyName).ToList();
            //var fundList = base.GetList().Select(val => val.fundName).Where(val => !val.Contains("Test")).ToList();
            return res;
        }
        public Dictionary<string, string> getFundNameDic()
        {
            var list = base.GetList("fundID is NOT NULL");
            return list.ToDictionary(val => val.fundID, val => val.fundName);
        }
        public string getIDByFamilyName(string fundName)
        {
            var fund = GetModel($"fundFamillyName='{fundName}'");
            if (fund == null)
            {
                return null;
            }
            return fund.fundID;
        }
    }
}
