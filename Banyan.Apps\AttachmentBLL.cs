﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Collections.Specialized;

namespace Banyan.Apps
{
    public class AttachmentBLL : BaseDAL<Attachment>
    {
        private readonly AjaxResult ajaxResult = null;

        private class SearchAttachment
        {
            public int Id;
            public string Name;
            public int SourceId;
            public string AtSuffix;
            public string AddTime;
            public string Path;
        }
        public AttachmentBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }
        public AjaxResult searchByName(NameValueCollection paramValues)
        {
            string Name = WebHelper.GetValue("Name", string.Empty, paramValues);
            var ProjectList = GetList($" AtName like '%{Name}%' ", int.MaxValue, 1, "*", "AtName DESC");
            var result = ProjectList.Select(val =>
            {
                var tmp = new SearchAttachment();
                tmp.Id = val.Id;
                tmp.Name = val.AtName;
                tmp.AtSuffix = val.AtSuffix;
                tmp.SourceId = val.SourceId;
                tmp.AddTime = val.AddTime.ToString("yyyy-MM-dd");
                tmp.Path = val.Path;
                return tmp;
            }).ToList();
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = result;
            return ajaxResult;
        }
        private string getStrWhere(NameValueCollection paramValues, Member user, out string sort, bool searchNameOnly = false, bool mobile = false)
        {

            string strWhere = $" id > -1 ";

            //if (!(user.Levels == (int)MemberLevels.Administrator || user.Levels == (int)MemberLevels.SuperUser))
            //{
            //    strWhere += $"AND (EditorName='{user.RealName}' OR DDManager like '%{user.RealName}%' OR ProjectManager like '%{user.RealName}%' OR groupMember like '%{user.RealName}%' OR InteralPTCP like '%{user.RealName}%' ";

            //        if (user.Levels != (int)MemberLevels.LimitedUser)
            //        {
            //            string roleIds = string.IsNullOrEmpty(user.Groups) ? "0" : $"{user.Groups}";
            //            roleIds += ",6";//其他组均可见
            //            if (!string.IsNullOrEmpty(roleIds))
            //                strWhere += $" OR (ToRoleId in({roleIds}) AND IsPrivate = 0) ";
            //        }


            //    strWhere += ") ";
            //}
            string suffix = WebHelper.GetValue("suffix", string.Empty, paramValues);
            if (!string.IsNullOrWhiteSpace(suffix))
            {
                strWhere += $@" AND (AtSuffix = '{suffix}') ";

            }

            string Name = WebHelper.GetValue("Name", string.Empty, paramValues);
            Name = Name.Replace("'", "''");

            if (!string.IsNullOrWhiteSpace(Name))
            {
               strWhere += $@" AND (AtName like '%{Name}%') ";

            }

            string startDate = WebHelper.GetValue("startdate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(startDate))
            {
                strWhere += $"AND AddTime>='{startDate}' ";
            }
            if (user.limitedJoinTime)
            {
                strWhere += $"AND AddTime>='{user.AddTime.ToString("yyyy-MM-dd")}' ";
            }

            string endDate = WebHelper.GetValue("enddate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(endDate))
            {
                strWhere += $"AND AddTime<'{Convert.ToDateTime(endDate).AddDays(1).ToString("yyyy-MM-dd")}' ";
            }

            sort = " AddTime DESC ";

            return strWhere;
        }

        public List<Attachment> searchCommon(NameValueCollection paramValues, Member user,
           int pageIndex, int pageSize, out int count, bool searchNameOnly = false, bool searchMonthOnly = false)
        {
            string sort;
            string strWhere = getStrWhere(paramValues, user, out sort, searchNameOnly);

            Logger.Info("attachmentsPage search str: " + strWhere, user.RealName);

            var ProjectList = GetList(strWhere, pageSize, pageIndex, "*", sort);

            count = GetCount(strWhere);
            return ProjectList;
        }

        public AjaxResult GetPageList(NameValueCollection paramValues)
        {
            Member user = new MemberBLL().GetLogOnUser();
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10000, paramValues);
            int count;
            var ProjectList = searchCommon(paramValues, user, pageIndex, pageSize, out count);

            Logger.Info("View attachments page", user.RealName);

            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = ProjectList;
            ajaxResult.count = count;
            return ajaxResult;
        }

        public List<Attachment> searchList(string searchStr, string Name)
        {
            var list = GetList($"{searchStr} AND (AtName like '%{Name}%'  OR Contains(Content, '\"{Name}\"') ) ");
            return list;
        }

        public List<Attachment> GetDocList(int sourceId, SourceTypeEnum typeEnum)
        {
            return GetList($"SourceId={sourceId} AND SourceType={(Byte)typeEnum}");
        }

        public AjaxResult SaveDoc(Attachment model)
        {
            Member user = new MemberBLL().GetLogOnUser();
            ajaxResult.code = (int)ResultCode.exception;
            if (model.SourceId <= 0)
            {
                ajaxResult.msg = $"关联记录不存在";
                return ajaxResult;
            }
            try
            {
                model.AtName = model.AtName.Replace("+", "加").Replace(" ", "_");
                if (!model.AtSuffix.Equals(".xlsx"))
                {
                    var tmpPathArr = model.AtUrl.Split('/');
                    model.Path = $"/imsfiles/{tmpPathArr[2]}/{tmpPathArr[3]}/" + model.AtName;
                }
                model.Creator = user.RealName;
                int docId = Convert.ToInt32(Add(model));
                if (docId <= 0)
                {
                    Logger.Error($"add doc {model.AtName} err", user.RealName);
                }
                Logger.Info($"add doc {model.AtName}", user.RealName);
                ajaxResult.data = docId;
                ajaxResult.code = docId > 0 ? (int)ResultCode.success : (int)ResultCode.exception;
                
            }
            catch (Exception e)
            {
                Logger.Error(e.Message, e, user.RealName);
            }

            return ajaxResult;
        }

        public AjaxResult DelDoc(int id)
        {
            Member user = new MemberBLL().GetLogOnUser();
            ajaxResult.code = (int)ResultCode.exception;
            if (id <= 0)
            {
                ajaxResult.msg = $"参数不合法";
                return ajaxResult;
            }
            try
            {
                //var model = new Attachment() { Id = id, Deleted = 1 };

                //ajaxResult.code = Update(model, "Deleted") ? (int)ResultCode.success : (int)ResultCode.exception;
                Logger.Info($"delete doc {id}", user.RealName);
                var res = Delete(id);
                if (res <= 0)
                {
                    Logger.Error($"delete doc {id} error", user.RealName);
                }
                ajaxResult.code = res > 0 ? (int)ResultCode.success : (int)ResultCode.exception;

            }catch(Exception e)
            {
                Logger.Error(e.Message, e, user.RealName);
            }
            return ajaxResult;
        }

        public void ClearCache(Attachment model)
        {
            RedisUtil.Remove(string.Format(RedisKey.attachment_comb, model.Id));
        }
        public override bool Update(Attachment model)
        {
            ClearCache(model);
            return base.Update(model);
        }
        public override bool Update(Attachment model, string fldList)
        {
            ClearCache(model);
            return base.Update(model, fldList);
        }
        public Attachment GetCache(int articleId)
        {
            string cacheKey = string.Format(RedisKey.attachment_comb, articleId);
            Attachment model = RedisUtil.Get<Attachment>(cacheKey);
            if (model == null)
            {
                model = base.GetModel(articleId);
                RedisUtil.Set<Attachment>(cacheKey, model, TimeSpan.FromMinutes(10));
            }
            return model;
        }

        public object JOk(object data, string msg = "", int code = 0, int count = 0)
        {
            return new { code, data, msg, count };
        }

        public object JFail(string msg = "", int code = 1)
        {
            return new { code, msg };
        }
        public static string GetDomain()
        {
            return System.Configuration.ConfigurationManager.AppSettings["FileDomain"];
        }
        public Attachment GetAttachment(int articleId)
        {
            Attachment model = GetCache(articleId);
            if (model != null && !string.IsNullOrEmpty(model.AtUrl))
            {
                List<string> fullPathList = new List<string>();
                string filePath = GetDomain();
                foreach (var item in model.AtUrl.Split(','))
                {
                    fullPathList.Add($"{filePath}{item}");
                }
                model.AtUrl = string.Join(",", fullPathList);
                new SysLogBLL().LogFileView(model.AtName, model.SourceId +"", model.AtUrl);
            }
            return model;
        }

        public bool DelDoc(int sourceid, String type)
        {
            return DeleteByWhere($"SourceId='{sourceid}' AND AtSuffix='{type}'") > 0 ? true : false;
        }
    }
}
