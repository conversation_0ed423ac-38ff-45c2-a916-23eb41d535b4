using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public partial class ProjectScoreStage
    {
        public ProjectScoreStage() { }
        public ProjectScoreStage(int state) {
            this.State = state;
        }

        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        /// <summary>
        /// 项目ID
        /// </summary>
        [SqlField]
        public int ProjectId { get; set; }

        /// <summary>
        /// 参与人次
        /// </summary>
        [SqlField]
        public int People { get; set; } = 0;

        /// <summary>
        /// 活动名称
        /// </summary>
        [SqlField]
        public string ActName { get; set; } = string.Empty;
        [SqlField]
        public string Description { get; set; } = string.Empty;
        /// <summary>
        /// 场次平均分
        /// </summary>
        [SqlField]
        public decimal Average { get; set; } = 0;
        [SqlField]
        public int ProjectManagerScore { get; set; } = 0;

        /// <summary>
        /// 添加时间
        /// </summary>
        [SqlField]
        public DateTime AddTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 添加时间
        /// </summary>
        [SqlField]
        public DateTime EndTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 状态值
        /// </summary>
        [SqlField]
        public int State { get; set; }
    }
}
