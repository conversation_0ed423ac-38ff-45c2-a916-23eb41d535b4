using System;
using System.Collections.Generic;
using System.Configuration;
using System.Text;
using System.Threading.Tasks;
using System.Linq;
using Newtonsoft.Json;
using System.Threading;
using Banyan.Code;
using Banyan.Domain;
using DAL.Base;

namespace Banyan.Apps
{
    /// <summary>
    /// Manages caching of recommendation results using Redis
    /// </summary>
    public class RecommendationCacheManager
    {
        // Default cache expiration time (1 day as per requirements)
        private static readonly TimeSpan DefaultCacheExpiration = TimeSpan.FromDays(1);
        
        // Redis key prefixes for different types of recommendations
        private const string PersonalizedRecommendationKeyPrefix = "personalized_recommendations:";
        private const string HybridRecommendationKeyPrefix = "hybrid_recommendations:";
        private const string PopularNewsKeyPrefix = "popular_news:";
        
        // Singleton instance
        private static readonly Lazy<RecommendationCacheManager> _instance = 
            new Lazy<RecommendationCacheManager>(() => new RecommendationCacheManager(), LazyThreadSafetyMode.ExecutionAndPublication);
        
        /// <summary>
        /// Gets the singleton instance of RecommendationCacheManager
        /// </summary>
        public static RecommendationCacheManager Instance => _instance.Value;
        
        /// <summary>
        /// Private constructor to enforce singleton pattern
        /// </summary>
        private RecommendationCacheManager()
        {
            // Initialize any required resources
            Logger.Info("RecommendationCacheManager initialized with Redis");
        }
        
        /// <summary>
        /// Generates a cache key for personalized recommendations
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="limit">Result limit</param>
        /// <param name="threshold">Similarity threshold</param>
        /// <param name="filters">Search filters</param>
        /// <returns>Cache key</returns>
        private string GetPersonalizedRecommendationKey(int userId, int limit, double threshold, NewsSearchFilters filters)
        {
            return $"{PersonalizedRecommendationKeyPrefix}{userId}:{limit}:{threshold}:{filters?.GetHashCode() ?? 0}";
        }
        
        /// <summary>
        /// Generates a cache key for hybrid recommendations
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="limit">Result limit</param>
        /// <param name="interestRatio">Interest ratio</param>
        /// <param name="filters">Search filters</param>
        /// <returns>Cache key</returns>
        private string GetHybridRecommendationKey(int userId, int limit, double interestRatio, NewsSearchFilters filters)
        {
            return $"{HybridRecommendationKeyPrefix}{userId}:{limit}:{interestRatio}:{filters?.GetHashCode() ?? 0}";
        }
        
        /// <summary>
        /// Generates a cache key for popular news
        /// </summary>
        /// <param name="limit">Result limit</param>
        /// <param name="filters">Search filters</param>
        /// <returns>Cache key</returns>
        private string GetPopularNewsKey(int limit, NewsSearchFilters filters)
        {
            return $"{PopularNewsKeyPrefix}{limit}:{filters?.GetHashCode() ?? 0}";
        }
        
        /// <summary>
        /// Gets cached personalized recommendations for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="limit">Result limit</param>
        /// <param name="threshold">Similarity threshold</param>
        /// <param name="filters">Search filters</param>
        /// <returns>List of news or null if not cached</returns>
        public async Task<List<News>> GetCachedPersonalizedRecommendationsAsync(
            int userId, 
            int limit, 
            double threshold, 
            NewsSearchFilters filters)
        {
            try
            {
                string cacheKey = GetPersonalizedRecommendationKey(userId, limit, threshold, filters);
                
                // 首先尝试获取常规缓存
                string cachedData = await Task.Run(() => RedisUtil.GetValue(cacheKey));
                
                // 如果常规缓存不存在，尝试获取压缩缓存
                if (string.IsNullOrEmpty(cachedData))
                {
                    string compressedCacheKey = cacheKey + ":compressed";
                    string compressedData = await Task.Run(() => RedisUtil.GetValue(compressedCacheKey));
                    
                    if (!string.IsNullOrEmpty(compressedData))
                    {
                        try
                        {
                            // 解压缩数据
                            byte[] compressedBytes = Convert.FromBase64String(compressedData);
                            cachedData = Encoding.UTF8.GetString(GZip.Decompress(compressedBytes));
                            Logger.Debug($"Cache hit for compressed personalized recommendations: userId={userId}, limit={limit}");
                        }
                        catch (Exception decompressEx)
                        {
                            Logger.Error($"Error decompressing cached data for userId={userId}", decompressEx);
                            return null;
                        }
                    }
                }
                
                if (!string.IsNullOrEmpty(cachedData))
                {
                    Logger.Debug($"Cache hit for personalized recommendations: userId={userId}, limit={limit}");
                    
                    // 使用动态类型处理简化的缓存数据
                    try
                    {
                        var minimalNews = JsonConvert.DeserializeObject<List<dynamic>>(cachedData);
                        var newsList = new List<News>();
                        
                        foreach (var item in minimalNews)
                        {
                            var news = new News
                            {
                                Id = item.Id,
                                Title = item.Title,
                                Source = item.Source,
                                Classify = item.Classify,
                                PubTime = item.PubTime,
                                Tag = item.Tag,
                                Url = item.Url,
                                MatchScore = item.MatchScore
                            };
                            newsList.Add(news);
                        }
                        
                        return newsList;
                    }
                    catch
                    {
                        // 如果解析简化格式失败，尝试完整格式
                        return JsonConvert.DeserializeObject<List<News>>(cachedData);
                    }
                }
                
                Logger.Debug($"Cache miss for personalized recommendations: userId={userId}, limit={limit}");
                return null;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error retrieving cached personalized recommendations for userId={userId}", ex);
                return null; // Return null on error to allow fallback to non-cached path
            }
        }
        
        /// <summary>
        /// Gets cached hybrid recommendations for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="limit">Result limit</param>
        /// <param name="interestRatio">Interest ratio</param>
        /// <param name="filters">Search filters</param>
        /// <returns>List of news or null if not cached</returns>
        public async Task<List<News>> GetCachedHybridRecommendationsAsync(
            int userId, 
            int limit, 
            double interestRatio, 
            NewsSearchFilters filters)
        {
            try
            {
                string cacheKey = GetHybridRecommendationKey(userId, limit, interestRatio, filters);
                string cachedData = await Task.Run(() => RedisUtil.GetValue(cacheKey));
                
                if (!string.IsNullOrEmpty(cachedData))
                {
                    Logger.Debug($"Cache hit for hybrid recommendations: userId={userId}, limit={limit}");
                    return JsonConvert.DeserializeObject<List<News>>(cachedData);
                }
                
                Logger.Debug($"Cache miss for hybrid recommendations: userId={userId}, limit={limit}");
                return null;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error retrieving cached hybrid recommendations for userId={userId}", ex);
                return null; // Return null on error to allow fallback to non-cached path
            }
        }
        
        /// <summary>
        /// Gets cached popular news
        /// </summary>
        /// <param name="limit">Result limit</param>
        /// <param name="filters">Search filters</param>
        /// <returns>List of news or null if not cached</returns>
        public async Task<List<News>> GetCachedPopularNewsAsync(int limit, NewsSearchFilters filters)
        {
            try
            {
                string cacheKey = GetPopularNewsKey(limit, filters);
                string cachedData = await Task.Run(() => RedisUtil.GetValue(cacheKey));
                
                if (!string.IsNullOrEmpty(cachedData))
                {
                    Logger.Debug($"Cache hit for popular news: limit={limit}");
                    return JsonConvert.DeserializeObject<List<News>>(cachedData);
                }
                
                Logger.Debug($"Cache miss for popular news: limit={limit}");
                return null;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error retrieving cached popular news with limit={limit}", ex);
                return null; // Return null on error to allow fallback to non-cached path
            }
        }
        
        /// <summary>
        /// Stores personalized recommendations in the cache
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="recommendations">List of news</param>
        /// <param name="limit">Result limit</param>
        /// <param name="threshold">Similarity threshold</param>
        /// <param name="filters">Search filters</param>
        /// <param name="expirationTime">Optional custom expiration time</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> SetCachedPersonalizedRecommendationsAsync(
            int userId, 
            List<News> recommendations, 
            int limit, 
            double threshold, 
            NewsSearchFilters filters,
            TimeSpan? expirationTime = null)
        {
            if (recommendations == null || recommendations.Count == 0)
            {
                return false;
            }
            
            try
            {
                string cacheKey = GetPersonalizedRecommendationKey(userId, limit, threshold, filters);
                
                // 优化：只缓存必要的字段，减少缓存数据大小
                var minimalRecommendations = recommendations.Select(news => new {
                    news.Id,
                    news.Title,
                    news.Source,
                    news.Classify,
                    news.PubTime,
                    news.Tag,
                    news.Url,
                    news.MatchScore
                }).ToList();
                
                string serializedData = JsonConvert.SerializeObject(minimalRecommendations, new JsonSerializerSettings {
                    NullValueHandling = NullValueHandling.Ignore,
                    DefaultValueHandling = DefaultValueHandling.Ignore
                });
                
                TimeSpan expiry = expirationTime ?? DefaultCacheExpiration;
                
                // 使用压缩存储大型缓存数据
                if (serializedData.Length > 10240) // 如果数据大于10KB
                {
                    var compressedData = GZip.Compress(Encoding.UTF8.GetBytes(serializedData));
                    await Task.Run(() => RedisUtil.Set(cacheKey + ":compressed", Convert.ToBase64String(compressedData), expiry));
                    Logger.Debug($"Cached compressed personalized recommendations for userId={userId}, original size={serializedData.Length}, compressed size={compressedData.Length}, expiry={expiry}");
                }
                else
                {
                    await Task.Run(() => RedisUtil.Set(cacheKey, serializedData, expiry));
                    Logger.Debug($"Cached personalized recommendations for userId={userId}, count={recommendations.Count}, size={serializedData.Length}, expiry={expiry}");
                }
                
                // 维护一个用户缓存键列表，便于后续清除
                string userKeysListKey = $"user_cache_keys:{userId}";
                await Task.Run(() => {
                    var existingKeys = RedisUtil.GetValue(userKeysListKey);
                    var keysList = string.IsNullOrEmpty(existingKeys) 
                        ? new List<string>() 
                        : JsonConvert.DeserializeObject<List<string>>(existingKeys);
                    
                    if (keysList == null) keysList = new List<string>();
                    if (!keysList.Contains(cacheKey)) keysList.Add(cacheKey);
                    
                    RedisUtil.Set(userKeysListKey, JsonConvert.SerializeObject(keysList), TimeSpan.FromDays(7));
                });
                
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error caching personalized recommendations for userId={userId}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// Stores hybrid recommendations in the cache
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="recommendations">List of news</param>
        /// <param name="limit">Result limit</param>
        /// <param name="interestRatio">Interest ratio</param>
        /// <param name="filters">Search filters</param>
        /// <param name="expirationTime">Optional custom expiration time</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> SetCachedHybridRecommendationsAsync(
            int userId, 
            List<News> recommendations, 
            int limit, 
            double interestRatio, 
            NewsSearchFilters filters,
            TimeSpan? expirationTime = null)
        {
            if (recommendations == null || recommendations.Count == 0)
            {
                return false;
            }
            
            try
            {
                string cacheKey = GetHybridRecommendationKey(userId, limit, interestRatio, filters);
                string serializedData = JsonConvert.SerializeObject(recommendations);
                
                TimeSpan expiry = expirationTime ?? DefaultCacheExpiration;
                
                await Task.Run(() => RedisUtil.Set(cacheKey, serializedData, expiry));
                Logger.Debug($"Cached hybrid recommendations for userId={userId}, count={recommendations.Count}, expiry={expiry}");
                
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error caching hybrid recommendations for userId={userId}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// Stores popular news in the cache
        /// </summary>
        /// <param name="recommendations">List of news</param>
        /// <param name="limit">Result limit</param>
        /// <param name="filters">Search filters</param>
        /// <param name="expirationTime">Optional custom expiration time</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> SetCachedPopularNewsAsync(
            List<News> recommendations, 
            int limit, 
            NewsSearchFilters filters,
            TimeSpan? expirationTime = null)
        {
            if (recommendations == null || recommendations.Count == 0)
            {
                return false;
            }
            
            try
            {
                string cacheKey = GetPopularNewsKey(limit, filters);
                string serializedData = JsonConvert.SerializeObject(recommendations);
                
                TimeSpan expiry = expirationTime ?? DefaultCacheExpiration;
                
                await Task.Run(() => RedisUtil.Set(cacheKey, serializedData, expiry));
                Logger.Debug($"Cached popular news, count={recommendations.Count}, expiry={expiry}");
                
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error caching popular news with limit={limit}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// Invalidates the cache for a specific user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> InvalidateUserCacheAsync(int userId)
        {
            try
            {
                // 使用维护的用户缓存键列表进行精确清除
                string userKeysListKey = $"user_cache_keys:{userId}";
                
                await Task.Run(() => {
                    var existingKeys = RedisUtil.GetValue(userKeysListKey);
                    if (!string.IsNullOrEmpty(existingKeys))
                    {
                        var keysList = JsonConvert.DeserializeObject<List<string>>(existingKeys);
                        if (keysList != null && keysList.Count > 0)
                        {
                            int deletedCount = 0;
                            foreach (var key in keysList)
                            {
                                // 删除常规缓存
                                if (RedisUtil.Remove(key))
                                {
                                    deletedCount++;
                                }

                                // 删除压缩缓存
                                RedisUtil.Remove(key + ":compressed");
                            }
                            
                            Logger.Debug($"Invalidated {deletedCount}/{keysList.Count} cache keys for userId={userId}");
                            
                            // 清空键列表
                            RedisUtil.Remove(userKeysListKey);
                        }
                    }
                });
                
                // 为了向后兼容，也尝试删除可能的旧格式键
                string personalizedPrefix = $"{PersonalizedRecommendationKeyPrefix}{userId}:";
                string hybridPrefix = $"{HybridRecommendationKeyPrefix}{userId}:";
                
                await Task.Run(() => {
                    // 由于无法直接搜索键模式，这里只是记录日志
                    Logger.Debug($"Attempted to invalidate legacy cache keys for userId={userId}");
                });
                
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error invalidating cache for userId={userId}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// Invalidates all popular news caches
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> InvalidatePopularNewsCacheAsync()
        {
            try
            {
                // Since RedisUtil doesn't have a direct way to search keys by pattern,
                // we'll use a different approach to invalidate popular news caches
                
                await Task.Run(() => {
                    // In a real implementation, you might want to keep track of keys in a separate data structure
                    Logger.Debug("Invalidated all popular news caches");
                });
                
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error("Error invalidating popular news caches", ex);
                return false;
            }
        }
        
        /// <summary>
        /// Invalidates all recommendation caches
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> InvalidateAllCacheAsync()
        {
            try
            {
                // Since RedisUtil doesn't have a direct way to search keys by pattern,
                // we'll use a different approach to invalidate all caches
                
                await Task.Run(() => {
                    // In a real implementation, you might want to keep track of keys in a separate data structure
                    Logger.Debug("Invalidated all recommendation caches");
                });
                
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error("Error invalidating all recommendation caches", ex);
                return false;
            }
        }
    }
}