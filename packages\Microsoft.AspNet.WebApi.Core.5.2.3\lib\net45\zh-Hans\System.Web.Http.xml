﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.Http</name>
  </assembly>
  <members>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.Http.InvalidByteRangeException)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,System.Exception)">
      <summary>创建用于表示异常的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>此请求必须与 <see cref="T:System.Web.Http.HttpConfiguration" /> 实例相关联。<see cref="T:System.Net.Http.HttpResponseMessage" />，其内容是 <see cref="T:System.Web.Http.HttpError" /> 实例的序列化表示形式。</returns>
      <param name="request">HTTP 请求。</param>
      <param name="statusCode">响应的状态代码。</param>
      <param name="exception">异常。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,System.String)">
      <summary>创建用于表示错误消息的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>此请求必须与 <see cref="T:System.Web.Http.HttpConfiguration" /> 实例相关联。<see cref="T:System.Net.Http.HttpResponseMessage" />，其内容是 <see cref="T:System.Web.Http.HttpError" /> 实例的序列化表示形式。</returns>
      <param name="request">HTTP 请求。</param>
      <param name="statusCode">响应的状态代码。</param>
      <param name="message">错误消息。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,System.String,System.Exception)">
      <summary>创建使用错误消息表示异常的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>此请求必须与 <see cref="T:System.Web.Http.HttpConfiguration" /> 实例相关联。<see cref="T:System.Net.Http.HttpResponseMessage" />，其内容是 <see cref="T:System.Web.Http.HttpError" /> 实例的序列化表示形式。</returns>
      <param name="request">HTTP 请求。</param>
      <param name="statusCode">响应的状态代码。</param>
      <param name="message">错误消息。</param>
      <param name="exception">异常。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,System.Web.Http.HttpError)">
      <summary>创建用于表示错误的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>此请求必须与 <see cref="T:System.Web.Http.HttpConfiguration" /> 实例相关联。<see cref="T:System.Net.Http.HttpResponseMessage" />，其内容是 <see cref="T:System.Web.Http.HttpError" /> 实例的序列化表示形式。</returns>
      <param name="request">HTTP 请求。</param>
      <param name="statusCode">响应的状态代码。</param>
      <param name="error">HTTP 错误。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,System.Web.Http.ModelBinding.ModelStateDictionary)">
      <summary>创建用于表示模型状态中的错误的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>此请求必须与 <see cref="T:System.Web.Http.HttpConfiguration" /> 实例相关联。<see cref="T:System.Net.Http.HttpResponseMessage" />，其内容是 <see cref="T:System.Web.Http.HttpError" /> 实例的序列化表示形式。</returns>
      <param name="request">HTTP 请求。</param>
      <param name="statusCode">响应的状态代码。</param>
      <param name="modelState">模型状态。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0)">
      <summary>创建与关联的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 连接的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>与关联的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 连接的已初始化 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
      <param name="request">导致此响应消息的 HTTP 请求消息。</param>
      <param name="statusCode">HTTP 响应状态代码。</param>
      <param name="value">HTTP 响应消息的内容。</param>
      <typeparam name="T">HTTP 响应消息的类型。</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.Net.Http.Formatting.MediaTypeFormatter)">
      <summary>创建与关联的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 连接的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>与关联的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 连接的已初始化 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
      <param name="request">导致此响应消息的 HTTP 请求消息。</param>
      <param name="statusCode">HTTP 响应状态代码。</param>
      <param name="value">HTTP 响应消息的内容。</param>
      <param name="formatter">媒体类型格式化程序。</param>
      <typeparam name="T">HTTP 响应消息的类型。</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary>创建与关联的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 连接的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>与关联的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 连接的已初始化 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
      <param name="request">导致此响应消息的 HTTP 请求消息。</param>
      <param name="statusCode">HTTP 响应状态代码。</param>
      <param name="value">HTTP 响应消息的内容。</param>
      <param name="formatter">媒体类型格式化程序。</param>
      <param name="mediaType">媒体类型标头值。</param>
      <typeparam name="T">HTTP 响应消息的类型。</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String)">
      <summary>创建与关联的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 连接的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>与关联的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 连接的已初始化 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
      <param name="request">导致此响应消息的 HTTP 请求消息。</param>
      <param name="statusCode">HTTP 响应状态代码。</param>
      <param name="value">HTTP 响应消息的内容。</param>
      <param name="formatter">媒体类型格式化程序。</param>
      <param name="mediaType">媒体类型。</param>
      <typeparam name="T">HTTP 响应消息的类型。</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary>创建与关联的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 连接的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>与关联的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 连接的已初始化 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
      <param name="request">导致此响应消息的 HTTP 请求消息。</param>
      <param name="statusCode">HTTP 响应状态代码。</param>
      <param name="value">HTTP 响应消息的内容。</param>
      <param name="mediaType">媒体类型标头值。</param>
      <typeparam name="T">HTTP 响应消息的类型。</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.String)">
      <summary>创建与关联的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 连接的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>与关联的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 连接的已初始化 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
      <param name="request">导致此响应消息的 HTTP 请求消息。</param>
      <param name="statusCode">HTTP 响应状态代码。</param>
      <param name="value">HTTP 响应消息的内容。</param>
      <param name="mediaType">媒体类型。</param>
      <typeparam name="T">HTTP 响应消息的类型。</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.Web.Http.HttpConfiguration)">
      <summary>创建与关联的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 连接的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>与关联的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 连接的已初始化 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
      <param name="request">导致此响应消息的 HTTP 请求消息。</param>
      <param name="statusCode">HTTP 响应状态代码。</param>
      <param name="value">HTTP 响应消息的内容。</param>
      <param name="configuration">包含用于解析服务的依赖关系解析程序的 HTTP 配置。</param>
      <typeparam name="T">HTTP 响应消息的类型。</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,``0)">
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.DisposeRequestResources(System.Net.Http.HttpRequestMessage)">
      <summary>释放所有通过 <see cref="M:System.Net.Http.HttpRequestMessageExtensions.RegisterForDispose(System.Net.Http.HttpRequestMessage,System.IDisposable)" /> 方法添加的与 <paramref name="request" /> 关联的受跟踪资源。</summary>
      <param name="request">HTTP 请求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetActionDescriptor(System.Net.Http.HttpRequestMessage)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetClientCertificate(System.Net.Http.HttpRequestMessage)">
      <summary>从给定 HTTP 请求获取当前 X.509 证书。</summary>
      <returns>当前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 或 null（如果证书不可用）。</returns>
      <param name="request">HTTP 请求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetConfiguration(System.Net.Http.HttpRequestMessage)">
      <summary>检索给定请求的 <see cref="T:System.Web.Http.HttpConfiguration" />。</summary>
      <returns>给定请求的 <see cref="T:System.Web.Http.HttpConfiguration" />。</returns>
      <param name="request">HTTP 请求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetCorrelationId(System.Net.Http.HttpRequestMessage)">
      <summary>检索已指定为与给定 <paramref name="request" /> 关联的相关 ID 的 <see cref="T:System.Guid" />。将在第一次调用此方法时创建并设置该值。</summary>
      <returns>表示与请求关联的相关 ID 的 <see cref="T:System.Guid" /> 对象。</returns>
      <param name="request">HTTP 请求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetDependencyScope(System.Net.Http.HttpRequestMessage)">
      <summary>检索给定请求的 <see cref="T:System.Web.Http.Dependencies.IDependencyScope" />；如果不可用，则为 null。</summary>
      <returns>给定请求的 <see cref="T:System.Web.Http.Dependencies.IDependencyScope" />；如果不可用，则为 null。</returns>
      <param name="request">HTTP 请求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetQueryNameValuePairs(System.Net.Http.HttpRequestMessage)">
      <summary>获取已分析的查询字符串作为键/值对的集合。</summary>
      <returns>作为键/值对集合的查询字符串。</returns>
      <param name="request">HTTP 请求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetRequestContext(System.Net.Http.HttpRequestMessage)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetResourcesForDisposal(System.Net.Http.HttpRequestMessage)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetRouteData(System.Net.Http.HttpRequestMessage)">
      <summary>检索给定请求的 <see cref="T:System.Web.Http.Routing.IHttpRouteData" />；如果不可用，则为 null。</summary>
      <returns>给定请求的 <see cref="T:System.Web.Http.Routing.IHttpRouteData" />；如果不可用，则为 null。</returns>
      <param name="request">HTTP 请求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetSynchronizationContext(System.Net.Http.HttpRequestMessage)">
      <summary>检索给定请求的 <see cref="T:System.Threading.SynchronizationContext" />；如果不可用，则为 null。</summary>
      <returns>给定请求的 <see cref="T:System.Threading.SynchronizationContext" />；如果不可用，则为 null。</returns>
      <param name="request">HTTP 请求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetUrlHelper(System.Net.Http.HttpRequestMessage)">
      <summary>为 HTTP 请求获取 <see cref="T:System.Web.Http.Routing.UrlHelper" /> 实例。</summary>
      <returns>一个已为指定的 HTTP 请求初始化的 <see cref="T:System.Web.Http.Routing.UrlHelper" /> 实例。</returns>
      <param name="request">HTTP 请求。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.IsBatchRequest(System.Net.Http.HttpRequestMessage)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.IsLocal(System.Net.Http.HttpRequestMessage)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.RegisterForDispose(System.Net.Http.HttpRequestMessage,System.Collections.Generic.IEnumerable{System.IDisposable})"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.RegisterForDispose(System.Net.Http.HttpRequestMessage,System.IDisposable)">
      <summary>将给定的 <paramref name="resource" /> 添加到资源列表，这些资源将在处理 <paramref name="request" /> 后由主机释放。</summary>
      <param name="request">控制 <paramref name="resource" /> 的生命周期的 HTTP 请求。</param>
      <param name="resource">处理 <paramref name="request" /> 后要释放的资源。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.SetConfiguration(System.Net.Http.HttpRequestMessage,System.Web.Http.HttpConfiguration)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.SetRequestContext(System.Net.Http.HttpRequestMessage,System.Web.Http.Controllers.HttpRequestContext)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.SetRouteData(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRouteData)"></member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.ShouldIncludeErrorDetail(System.Net.Http.HttpRequestMessage)"></member>
    <member name="T:System.Net.Http.HttpResponseMessageExtensions">
      <summary>表示 ASP.NET 操作中 HTTP 响应的消息扩展。</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessageExtensions.TryGetContentValue``1(System.Net.Http.HttpResponseMessage,``0@)">
      <summary>尝试检索 <see cref="T:System.Net.Http.HttpResponseMessageExtensions" /> 的内容的值。</summary>
      <returns>内容值的检索结果。</returns>
      <param name="response">操作的响应。</param>
      <param name="value">内容的值。</param>
      <typeparam name="T">要检索的值的类型。</typeparam>
    </member>
    <member name="T:System.Net.Http.Formatting.MediaTypeFormatterExtensions">
      <summary>表示用于将 <see cref="T:System.Net.Http.Formatting.MediaTypeMapping" /> 项添加到 <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" /> 的扩展。</summary>
    </member>
    <member name="M:System.Net.Http.Formatting.MediaTypeFormatterExtensions.AddUriPathExtensionMapping(System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Net.Http.Headers.MediaTypeHeaderValue)"></member>
    <member name="M:System.Net.Http.Formatting.MediaTypeFormatterExtensions.AddUriPathExtensionMapping(System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.String)"></member>
    <member name="T:System.Net.Http.Formatting.UriPathExtensionMapping">
      <summary>从 <see cref="T:System.Uri" /> 中出现的路径扩展提供 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />。</summary>
    </member>
    <member name="M:System.Net.Http.Formatting.UriPathExtensionMapping.#ctor(System.String,System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary> 初始化 <see cref="T:System.Net.Http.Formatting.UriPathExtensionMapping" /> 类的新实例。</summary>
      <param name="uriPathExtension">与 mediaType 对应的扩展。此值应不包括点或通配符。</param>
      <param name="mediaType">匹配 uriPathExtension 时将返回的 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />。</param>
    </member>
    <member name="M:System.Net.Http.Formatting.UriPathExtensionMapping.#ctor(System.String,System.String)">
      <summary> 初始化 <see cref="T:System.Net.Http.Formatting.UriPathExtensionMapping" /> 类的新实例。</summary>
      <param name="uriPathExtension">与 mediaType 对应的扩展。此值应不包括点或通配符。</param>
      <param name="mediaType">匹配 uriPathExtension 时将返回的媒体类型。</param>
    </member>
    <member name="M:System.Net.Http.Formatting.UriPathExtensionMapping.TryMatchMediaType(System.Net.Http.HttpRequestMessage)">
      <summary> 返回一个值，该值指示此 <see cref="T:System.Net.Http.Formatting.UriPathExtensionMapping" /> 实例是否可以为 request 的 <see cref="T:System.Uri" /> 提供 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />。</summary>
      <returns>如果此实例可以匹配 request 中的文件扩展名，则返回 1.0；否则返回 0.0。</returns>
      <param name="request">要检查的 <see cref="T:System.Net.Http.HttpRequestMessage" />。</param>
    </member>
    <member name="P:System.Net.Http.Formatting.UriPathExtensionMapping.UriPathExtension">
      <summary> 获取 <see cref="T:System.Uri" /> 路径扩展。</summary>
      <returns>
        <see cref="T:System.Uri" /> 路径扩展。</returns>
    </member>
    <member name="F:System.Net.Http.Formatting.UriPathExtensionMapping.UriPathExtensionKey">
      <summary>
        <see cref="T:System.Uri" /> 路径扩展键。</summary>
    </member>
    <member name="T:System.Web.Http.AcceptVerbsAttribute">
      <summary>表示一个特性，该特性指定操作方法将响应的 HTTP 方法。</summary>
    </member>
    <member name="M:System.Web.Http.AcceptVerbsAttribute.#ctor(System.String)">
      <summary>使用 <see cref="T:System.Web.Http.AcceptVerbsAttribute" /> 类将响应的操作方法来初始化该类的新实例。</summary>
      <param name="method">操作方法将响应的 HTTP 方法。</param>
    </member>
    <member name="M:System.Web.Http.AcceptVerbsAttribute.#ctor(System.String[])">
      <summary>使用操作方法将响应的 HTTP 方法的列表来初始化 <see cref="T:System.Web.Http.AcceptVerbsAttribute" /> 类的新实例。</summary>
      <param name="methods">操作方法将响应的 HTTP 方法。</param>
    </member>
    <member name="P:System.Web.Http.AcceptVerbsAttribute.HttpMethods">
      <summary>获取或设置操作方法将响应的 HTTP 方法的列表。</summary>
      <returns>获取或设置操作方法将响应的 HTTP 方法的列表。</returns>
    </member>
    <member name="T:System.Web.Http.ActionNameAttribute">
      <summary>表示一个用于操作的名称的特性。</summary>
    </member>
    <member name="M:System.Web.Http.ActionNameAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.ActionNameAttribute" /> 类的新实例。</summary>
      <param name="name">操作的名称。</param>
    </member>
    <member name="P:System.Web.Http.ActionNameAttribute.Name">
      <summary>获取或设置操作的名称。</summary>
      <returns>操作的名称。</returns>
    </member>
    <member name="T:System.Web.Http.AllowAnonymousAttribute">
      <summary>指定在授权期间 <see cref="T:System.Web.Http.AuthorizeAttribute" /> 将跳过操作和控制器。</summary>
    </member>
    <member name="M:System.Web.Http.AllowAnonymousAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.AllowAnonymousAttribute" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Web.Http.ApiController">
      <summary>为 API 控制器定义属性和方法。</summary>
    </member>
    <member name="M:System.Web.Http.ApiController.#ctor"></member>
    <member name="P:System.Web.Http.ApiController.ActionContext">
      <summary>获取操作上下文。</summary>
      <returns>操作上下文。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.BadRequest">
      <summary>创建一个 <see cref="T:System.Web.Http.Results.BadRequestResult" />。</summary>
      <returns>
        <see cref="T:System.Web.Http.Results.BadRequestResult" />。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.BadRequest(System.String)">
      <summary>创建包含指定错误消息的 <see cref="T:System.Web.Http.Results.ErrorMessageResult" />（400 错误的请求）。</summary>
      <returns>具有指定模型状态的 <see cref="T:System.Web.Http.Results.InvalidModelStateResult" />。</returns>
      <param name="message">用户可见的错误消息。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.BadRequest(System.Web.Http.ModelBinding.ModelStateDictionary)">
      <summary>创建具有指定模型状态的 <see cref="T:System.Web.Http.Results.InvalidModelStateResult" />。</summary>
      <returns>具有指定模型状态的 <see cref="T:System.Web.Http.Results.InvalidModelStateResult" />。</returns>
      <param name="modelState">要在错误中包括的模型状态。</param>
    </member>
    <member name="P:System.Web.Http.ApiController.Configuration">
      <summary>获取当前 <see cref="T:System.Web.Http.ApiController" /> 的 <see cref="T:System.Web.Http.HttpConfiguration" />。</summary>
      <returns>当前 <see cref="T:System.Web.Http.ApiController" /> 的 <see cref="T:System.Web.Http.HttpConfiguration" />。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.Conflict">
      <summary>创建 <see cref="T:System.Web.Http.Results.ConflictResult" />（409 冲突）。</summary>
      <returns>
        <see cref="T:System.Web.Http.Results.ConflictResult" />。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.Content``1(System.Net.HttpStatusCode,``0)">
      <summary>创建包含指定值的 &lt;see cref="T:System.Web.Http.NegotiatedContentResult`1" /&gt;。</summary>
      <returns>包含指定值的 &lt;see cref="T:System.Web.Http.NegotiatedContentResult`1" /&gt;。</returns>
      <param name="statusCode">响应消息的 HTTP 状态代码。</param>
      <param name="value">要在实体正文中协商和设置格式的内容值。</param>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Content``1(System.Net.HttpStatusCode,``0,System.Net.Http.Formatting.MediaTypeFormatter)">
      <summary>创建包含指定值的 &lt;see cref="T:System.Web.Http.FormattedContentResult`1" /&gt;。</summary>
      <returns>包含指定值的 &lt;see cref="T:System.Web.Http.FormattedContentResult`1" /。</returns>
      <param name="statusCode">响应消息的 HTTP 状态代码。</param>
      <param name="value">要在实体正文中设置格式的内容值。</param>
      <param name="formatter">用于设置内容格式的格式化程序。</param>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Content``1(System.Net.HttpStatusCode,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary>创建包含指定值的 &lt;see cref="T:System.Web.Http.FormattedContentResult`1" /&gt;。</summary>
      <returns>包含指定值的 &lt;see cref="T:System.Web.Http.FormattedContentResult`1" /。</returns>
      <param name="statusCode">响应消息的 HTTP 状态代码。</param>
      <param name="value">要在实体正文中设置格式的内容值。</param>
      <param name="formatter">用于设置内容格式的格式化程序。</param>
      <param name="mediaType"> Content-Type 标头的值，或者 &lt;see langword="null" /&gt;（使格式化程序选择默认值）。</param>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Content``1(System.Net.HttpStatusCode,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String)">
      <summary>创建包含指定值的 &lt;see cref="T:System.Web.Http.FormattedContentResult`1" /&gt;。</summary>
      <returns>包含指定值的 &lt;see cref="T:System.Web.Http.FormattedContentResult`1" /。</returns>
      <param name="statusCode">响应消息的 HTTP 状态代码。</param>
      <param name="value">要在实体正文中设置格式的内容值。</param>
      <param name="formatter">用于设置内容格式的格式化程序。</param>
      <param name="mediaType">Content-Type 标头的值。</param>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="P:System.Web.Http.ApiController.ControllerContext">
      <summary>获取当前 <see cref="T:System.Web.Http.ApiController" /> 的 <see cref="T:System.Web.Http.HttpConfiguration" />。</summary>
      <returns>当前 <see cref="T:System.Web.Http.ApiController" /> 的 <see cref="T:System.Web.Http.HttpConfiguration" />。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.Created``1(System.String,``0)">
      <summary>创建包含指定值的 <see cref="T:System.Web.Http.Results.CreatedNegotiatedContentResult`1" />（201 已创建）。</summary>
      <returns>包含指定值的 <see cref="T:System.Web.Http.Results.CreatedNegotiatedContentResult`1" />。</returns>
      <param name="location">在其中创建内容的位置。</param>
      <param name="content">要在实体正文中协商和设置格式的内容值。</param>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Created``1(System.Uri,``0)">
      <summary>创建包含指定值的 <see cref="T:System.Web.Http.Results.CreatedNegotiatedContentResult`1" />（201 已创建）。</summary>
      <returns>包含指定值的 <see cref="T:System.Web.Http.Results.CreatedNegotiatedContentResult`1" />。</returns>
      <param name="location">在其中创建内容的位置。</param>
      <param name="content">要在实体正文中协商和设置格式的内容值。</param>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.CreatedAtRoute``1(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},``0)">
      <summary>创建包含指定值的 <see cref="T:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1" />（201 已创建）。</summary>
      <returns>包含指定值的 <see cref="T:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1" />。</returns>
      <param name="routeName">用于生成 URL 的路由的名称。</param>
      <param name="routeValues">用于生成 URL 的路由数据。</param>
      <param name="content">要在实体正文中协商和设置格式的内容值。</param>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.CreatedAtRoute``1(System.String,System.Object,``0)">
      <summary>创建包含指定值的 <see cref="T:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1" />（201 已创建）。</summary>
      <returns>包含指定值的 <see cref="T:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1" />。</returns>
      <param name="routeName">用于生成 URL 的路由的名称。</param>
      <param name="routeValues">用于生成 URL 的路由数据。</param>
      <param name="content">要在实体正文中协商和设置格式的内容值。</param>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Dispose">
      <summary>执行与释放或重置非托管资源关联的应用程序定义任务。</summary>
    </member>
    <member name="M:System.Web.Http.ApiController.Dispose(System.Boolean)">
      <summary>释放对象使用的非托管资源，并有选择性地释放托管资源。</summary>
      <param name="disposing">若为 true，则同时释放托管资源和非托管资源；若为 false，则仅释放非托管资源。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.ExecuteAsync(System.Web.Http.Controllers.HttpControllerContext,System.Threading.CancellationToken)">
      <summary>异步执行单个 HTTP 操作。</summary>
      <returns>新启动的任务。</returns>
      <param name="controllerContext">单个 HTTP 操作的控制器上下文。</param>
      <param name="cancellationToken">为 HTTP 操作分配的取消标记。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.Initialize(System.Web.Http.Controllers.HttpControllerContext)">
      <summary>使用指定的 controllerContext 初始化 <see cref="T:System.Web.Http.ApiController" /> 实例。</summary>
      <param name="controllerContext">用于初始化的 <see cref="T:System.Web.Http.Controllers.HttpControllerContext" /> 对象。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.InternalServerError">
      <summary>创建 <see cref="T:System.Web.Http.Results.InternalServerErrorResult" />（500 内部服务器错误）。</summary>
      <returns>
        <see cref="T:System.Web.Http.Results.InternalServerErrorResult" />。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.InternalServerError(System.Exception)">
      <summary>创建包含指定异常的 <see cref="T:System.Web.Http.Results.ExceptionResult" />（500 内部服务器错误）。</summary>
      <returns>包含指定异常的 <see cref="T:System.Web.Http.Results.ExceptionResult" />。</returns>
      <param name="exception">要在错误中包含的异常。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.Json``1(``0)">
      <summary>创建包含指定值的 <see cref="T:System.Web.Http.Results.JsonResult`1" />（200 正常）。</summary>
      <returns>包含指定值的 <see cref="T:System.Web.Http.Results.JsonResult`1" />。</returns>
      <param name="content">要在实体正文中序列化的内容值。</param>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Json``1(``0,Newtonsoft.Json.JsonSerializerSettings)">
      <summary>创建包含指定值的 <see cref="T:System.Web.Http.Results.JsonResult`1" />（200 正常）。</summary>
      <returns>包含指定值的 <see cref="T:System.Web.Http.Results.JsonResult`1" />。</returns>
      <param name="content">要在实体正文中序列化的内容值。</param>
      <param name="serializerSettings">序列化程序设置。</param>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Json``1(``0,Newtonsoft.Json.JsonSerializerSettings,System.Text.Encoding)">
      <summary>创建包含指定值的 <see cref="T:System.Web.Http.Results.JsonResult`1" />（200 正常）。</summary>
      <returns>包含指定值的 <see cref="T:System.Web.Http.Results.JsonResult`1" />。</returns>
      <param name="content">要在实体正文中序列化的内容值。</param>
      <param name="serializerSettings">序列化程序设置。</param>
      <param name="encoding">内容编码。</param>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="P:System.Web.Http.ApiController.ModelState">
      <summary>在模型绑定过程之后获取模型状态。</summary>
      <returns>模型绑定过程之后的模型状态。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.NotFound">
      <summary>创建一个 <see cref="T:System.Web.Http.Results.NotFoundResult" />。</summary>
      <returns>
        <see cref="T:System.Web.Http.Results.NotFoundResult" />。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.Ok">
      <summary>创建 <see cref="T:System.Web.Http.Results.OkResult" />（200 正常）。</summary>
      <returns>一个 <see cref="T:System.Web.Http.Results.OkResult" />。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.Ok``1(``0)">
      <summary>创建包含指定值的 <see cref="T:System.Web.Http.Results.OkNegotiatedContentResult`1" />。</summary>
      <returns>包含指定值的 <see cref="T:System.Web.Http.Results.OkNegotiatedContentResult`1" />。</returns>
      <param name="content">要在实体正文中协商和设置格式的内容值。</param>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Redirect(System.String)">
      <summary>根据指定值创建重定向结果（302 已找到）。</summary>
      <returns>包含指定值的重定向结果（302 已找到）。</returns>
      <param name="location">要重定向到的位置。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.Redirect(System.Uri)">
      <summary>根据指定值创建重定向结果（302 已找到）。</summary>
      <returns>包含指定值的重定向结果（302 已找到）。</returns>
      <param name="location">要重定向到的位置。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.RedirectToRoute(System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>根据指定值创建到路由结果的重定向（302 已找到）。</summary>
      <returns>到包含指定值的路由结果的重定向（302 已找到）。</returns>
      <param name="routeName">用于生成 URL 的路由的名称。</param>
      <param name="routeValues">用于生成 URL 的路由数据。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.RedirectToRoute(System.String,System.Object)">
      <summary>根据指定值创建到路由结果的重定向（302 已找到）。</summary>
      <returns>到包含指定值的路由结果的重定向（302 已找到）。</returns>
      <param name="routeName">用于生成 URL 的路由的名称。</param>
      <param name="routeValues">用于生成 URL 的路由数据。</param>
    </member>
    <member name="P:System.Web.Http.ApiController.Request">
      <summary>获取或设置当前 <see cref="T:System.Web.Http.ApiController" /> 的 HttpRequestMessage。</summary>
      <returns>当前 <see cref="T:System.Web.Http.ApiController" /> 的 HttpRequestMessage。</returns>
    </member>
    <member name="P:System.Web.Http.ApiController.RequestContext">
      <summary>获取请求上下文。</summary>
      <returns>请求上下文。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.ResponseMessage(System.Net.Http.HttpResponseMessage)">
      <summary>创建包含指定响应的 <see cref="T:System.Web.Http.Results.ResponseMessageResult" />。</summary>
      <returns>指定响应的 <see cref="T:System.Web.Http.Results.ResponseMessageResult" />。</returns>
      <param name="response">HTTP 响应消息。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.StatusCode(System.Net.HttpStatusCode)">
      <summary>创建具有指定状态代码的 <see cref="T:System.Web.Http.StatusCodeResult" />。</summary>
      <returns>具有指定状态代码的 <see cref="T:System.Web.Http.StatusCodeResult" />。</returns>
      <param name="status">响应消息的 HTTP 状态代码</param>
    </member>
    <member name="M:System.Web.Http.ApiController.Unauthorized(System.Collections.Generic.IEnumerable{System.Net.Http.Headers.AuthenticationHeaderValue})">
      <summary>创建包含指定值的 <see cref="T:System.Web.Http.Results.UnauthorizedResult" />（401 未授权）。</summary>
      <returns>包含指定值的 <see cref="T:System.Web.Http.Results.UnauthorizedResult" />。</returns>
      <param name="challenges">WWW-Authenticate 质询。</param>
    </member>
    <member name="M:System.Web.Http.ApiController.Unauthorized(System.Net.Http.Headers.AuthenticationHeaderValue[])">
      <summary>创建包含指定值的 <see cref="T:System.Web.Http.Results.UnauthorizedResult" />（401 未授权）。</summary>
      <returns>包含指定值的 <see cref="T:System.Web.Http.Results.UnauthorizedResult" />。</returns>
      <param name="challenges">WWW-Authenticate 质询。</param>
    </member>
    <member name="P:System.Web.Http.ApiController.Url">
      <summary>获取 <see cref="T:System.Web.Http.Routing.UrlHelper" /> 的实例，该实例用于生成指向其他 API 的 URL。</summary>
      <returns>
        <see cref="T:System.Web.Http.Routing.UrlHelper" />，用于生成指向其他 API 的 URL。</returns>
    </member>
    <member name="P:System.Web.Http.ApiController.User">
      <summary>返回与此请求关联的当前主体。</summary>
      <returns>与此请求关联的当前主体。</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.Validate``1(``0)">
      <summary>验证给定实体并使用空前缀将验证错误添加到模型状态（如果有）。</summary>
      <param name="entity">所验证的实体。</param>
      <typeparam name="TEntity">要验证的实体的类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ApiController.Validate``1(``0,System.String)">
      <summary>验证给定实体并将验证错误添加到模型状态（如果有）。</summary>
      <param name="entity">所验证的实体。</param>
      <param name="keyPrefix">将模型状态错误添加到模型状态中时使用的键前缀。</param>
      <typeparam name="TEntity">要验证的实体的类型。</typeparam>
    </member>
    <member name="T:System.Web.Http.AuthorizeAttribute">
      <summary>指定用于验证请求的 <see cref="T:System.Security.Principal.IPrincipal" /> 的授权筛选器。</summary>
    </member>
    <member name="M:System.Web.Http.AuthorizeAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.AuthorizeAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.AuthorizeAttribute.HandleUnauthorizedRequest(System.Web.Http.Controllers.HttpActionContext)">
      <summary>处理授权失败的请求。</summary>
      <param name="actionContext">上下文。</param>
    </member>
    <member name="M:System.Web.Http.AuthorizeAttribute.IsAuthorized(System.Web.Http.Controllers.HttpActionContext)">
      <summary>指示指定的控件是否已获得授权。</summary>
      <returns>如果控件已获得授权，则为 true；否则为 false。</returns>
      <param name="actionContext">上下文。</param>
    </member>
    <member name="M:System.Web.Http.AuthorizeAttribute.OnAuthorization(System.Web.Http.Controllers.HttpActionContext)">
      <summary>为操作授权时调用。</summary>
      <param name="actionContext">上下文。</param>
      <exception cref="T:System.ArgumentNullException">上下文参数为 null。</exception>
    </member>
    <member name="P:System.Web.Http.AuthorizeAttribute.Roles">
      <summary>获取或设置授权角色。</summary>
      <returns>角色字符串。</returns>
    </member>
    <member name="P:System.Web.Http.AuthorizeAttribute.TypeId">
      <summary>获取此特性的唯一标识符。</summary>
      <returns>此特性的唯一标识符。</returns>
    </member>
    <member name="P:System.Web.Http.AuthorizeAttribute.Users">
      <summary>获取或设置授权用户。</summary>
      <returns>用户字符串。</returns>
    </member>
    <member name="T:System.Web.Http.FromBodyAttribute">
      <summary> 一个特性，该特性指定操作参数仅来自传入 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的实体正文。</summary>
    </member>
    <member name="M:System.Web.Http.FromBodyAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.FromBodyAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.FromBodyAttribute.GetBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>获取参数绑定。</summary>
      <returns>参数绑定。</returns>
      <param name="parameter">参数说明。</param>
    </member>
    <member name="T:System.Web.Http.FromUriAttribute">
      <summary>一个特性，该特性指定操作参数来自传入 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的 URI。</summary>
    </member>
    <member name="M:System.Web.Http.FromUriAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.FromUriAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.FromUriAttribute.GetValueProviderFactories(System.Web.Http.HttpConfiguration)">
      <summary>获取模型联编程序的值提供程序工厂。</summary>
      <returns>
        <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> 对象的集合。</returns>
      <param name="configuration">配置。</param>
    </member>
    <member name="T:System.Web.Http.HttpBindNeverAttribute">
      <summary>表示用于指定 HTTP 绑定应排除某属性的特性。</summary>
    </member>
    <member name="M:System.Web.Http.HttpBindNeverAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpBindNeverAttribute" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Web.Http.HttpBindRequiredAttribute">
      <summary>表示用于 http 绑定的必需特性。</summary>
    </member>
    <member name="M:System.Web.Http.HttpBindRequiredAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpBindRequiredAttribute" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Web.Http.HttpConfiguration">
      <summary>表示 <see cref="T:System.Web.Http.HttpServer" /> 实例的配置。</summary>
    </member>
    <member name="M:System.Web.Http.HttpConfiguration.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpConfiguration" /> 类的一个新实例。</summary>
    </member>
    <member name="M:System.Web.Http.HttpConfiguration.#ctor(System.Web.Http.HttpRouteCollection)">
      <summary>使用 HTTP 路由集合初始化 <see cref="T:System.Web.Http.HttpConfiguration" /> 类的新实例。</summary>
      <param name="routes">要与此实例关联的 HTTP 路由集合。</param>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.DependencyResolver">
      <summary>获取或设置与此实例关联的依赖关系解析程序。</summary>
      <returns>依赖关系解析程序。</returns>
    </member>
    <member name="M:System.Web.Http.HttpConfiguration.Dispose">
      <summary>执行应用程序定义的、与释放或重置非托管资源相关的任务。</summary>
    </member>
    <member name="M:System.Web.Http.HttpConfiguration.Dispose(System.Boolean)">
      <summary>释放由对象使用的非托管资源，并选择性释放托管资源。</summary>
      <param name="disposing">如果为 true，则托管及非托管资源都释放；如果为 false，则仅释放非托管资源。</param>
    </member>
    <member name="M:System.Web.Http.HttpConfiguration.EnsureInitialized">
      <summary>用于调用初始化程序挂钩。从此之后，将此项视为不变项。可以多次调用此方法。</summary>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Filters">
      <summary>获取筛选器列表，这些筛选器适用于使用此 <see cref="T:System.Web.Http.HttpConfiguration" /> 实例处理的所有请求。</summary>
      <returns>筛选器列表。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Formatters">
      <summary>获取此实例的媒体类型格式化程序。</summary>
      <returns>
        <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" /> 对象的集合。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.IncludeErrorDetailPolicy">
      <summary>获取或设置指示错误消息中是否应包含错误详细信息的值。</summary>
      <returns>
        <see cref="T:System.Web.Http.IncludeErrorDetailPolicy" /> 值，指示该错误详细信息策略。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Initializer">
      <summary> 获取或设置在使用 <see cref="T:System.Web.Http.HttpConfiguration" /> 实例处理请求前，需要对该实例执行的最终初始化操作。</summary>
      <returns>需要对 <see cref="T:System.Web.Http.HttpConfiguration" /> 实例执行的最终初始化操作。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.MessageHandlers">
      <summary>当 <see cref="T:System.Net.Http.HttpRequestMessage" /> 在堆栈中向上遍历，使得 <see cref="T:System.Net.Http.HttpResponseMessage" /> 在堆栈中向下遍历时，获取要调用的 <see cref="T:System.Net.Http.DelegatingHandler" /> 实例的有序列表。</summary>
      <returns>消息处理程序集合。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.ParameterBindingRules">
      <summary>获取参数绑定规则集。</summary>
      <returns>可为给定参数生成参数绑定的函数集。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Properties">
      <summary>获取与此实例关联的属性。</summary>
      <returns>包含属性的 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Routes">
      <summary>获取与此 <see cref="T:System.Web.Http.HttpConfiguration" /> 实例关联的 <see cref="T:System.Web.Http.HttpRouteCollection" />。</summary>
      <returns>
        <see cref="T:System.Web.Http.HttpRouteCollection" />。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Services">
      <summary>获取与此实例关联的默认服务的容器。</summary>
      <returns>
        <see cref="T:System.Web.Http.Controllers.ServicesContainer" />，包含此实例的默认服务。</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.VirtualPathRoot">
      <summary>获取根虚拟路径。</summary>
      <returns>根虚拟路径。</returns>
    </member>
    <member name="T:System.Web.Http.HttpConfigurationExtensions">
      <summary>包含 <see cref="T:System.Web.Http.HttpConfiguration" /> 类的扩展方法。</summary>
    </member>
    <member name="M:System.Web.Http.HttpConfigurationExtensions.BindParameter(System.Web.Http.HttpConfiguration,System.Type,System.Web.Http.ModelBinding.IModelBinder)"></member>
    <member name="M:System.Web.Http.HttpConfigurationExtensions.MapHttpAttributeRoutes(System.Web.Http.HttpConfiguration)"></member>
    <member name="M:System.Web.Http.HttpConfigurationExtensions.MapHttpAttributeRoutes(System.Web.Http.HttpConfiguration,System.Web.Http.Routing.IDirectRouteProvider)">
      <summary>映射应用程序的特性定义路由。</summary>
      <param name="configuration">服务器配置。</param>
      <param name="directRouteProvider">要用于发现和生成路由的 <see cref="T:System.Web.Http.Routing.IDirectRouteProvider" />。</param>
    </member>
    <member name="M:System.Web.Http.HttpConfigurationExtensions.MapHttpAttributeRoutes(System.Web.Http.HttpConfiguration,System.Web.Http.Routing.IInlineConstraintResolver)">
      <summary>映射应用程序的特性定义路由。</summary>
      <param name="configuration">服务器配置。</param>
      <param name="constraintResolver">约束解析程序。</param>
    </member>
    <member name="M:System.Web.Http.HttpConfigurationExtensions.MapHttpAttributeRoutes(System.Web.Http.HttpConfiguration,System.Web.Http.Routing.IInlineConstraintResolver,System.Web.Http.Routing.IDirectRouteProvider)">
      <summary>映射应用程序的特性定义路由。</summary>
      <param name="configuration">服务器配置。</param>
      <param name="constraintResolver">用于解析内联约束的 <see cref="T:System.Web.Http.Routing.IInlineConstraintResolver" />。</param>
      <param name="directRouteProvider">要用于发现和生成路由的 <see cref="T:System.Web.Http.Routing.IDirectRouteProvider" />。</param>
    </member>
    <member name="M:System.Web.Http.HttpConfigurationExtensions.SuppressHostPrincipal(System.Web.Http.HttpConfiguration)"></member>
    <member name="T:System.Web.Http.HttpDeleteAttribute">
      <summary>指定某个操作支持 DELETE HTTP 方法。</summary>
    </member>
    <member name="M:System.Web.Http.HttpDeleteAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpDeleteAttribute" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.HttpDeleteAttribute.HttpMethods">
      <summary>获取与此特性对应的 http 方法。</summary>
      <returns>与此特性对应的 http 方法。</returns>
    </member>
    <member name="T:System.Web.Http.HttpError">
      <summary>定义可序列化容器以存储错误信息。此信息存储为键/值对。用于查找标准错误信息的字典键在 <see cref="T:System.Web.Http.HttpErrorKeys" /> 类型中可用。</summary>
    </member>
    <member name="M:System.Web.Http.HttpError.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpError" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.HttpError.#ctor(System.Exception,System.Boolean)">
      <summary>初始化 <paramref name="exception" /> 的 <see cref="T:System.Web.Http.HttpError" /> 类的新实例。</summary>
      <param name="exception">要用于错误信息的异常。</param>
      <param name="includeErrorDetail">若要在错误中包含异常信息，则为 true；否则为 false</param>
    </member>
    <member name="M:System.Web.Http.HttpError.#ctor(System.String)">
      <summary>初始化包含错误消息 <paramref name="message" /> 的 <see cref="T:System.Web.Http.HttpError" /> 类的新实例。</summary>
      <param name="message">要与此实例关联的错误消息。</param>
    </member>
    <member name="M:System.Web.Http.HttpError.#ctor(System.Web.Http.ModelBinding.ModelStateDictionary,System.Boolean)">
      <summary>初始化 <paramref name="modelState" /> 的 <see cref="T:System.Web.Http.HttpError" /> 类的新实例。</summary>
      <param name="modelState">要用于错误信息的无效模型状态。</param>
      <param name="includeErrorDetail">若要在错误中包含异常消息，则为 true；否则为 false</param>
    </member>
    <member name="P:System.Web.Http.HttpError.ExceptionMessage">
      <summary>获取或设置 <see cref="T:System.Exception" /> 的消息（如果可用）。</summary>
      <returns>
        <see cref="T:System.Exception" /> 的消息（如果可用）。</returns>
    </member>
    <member name="P:System.Web.Http.HttpError.ExceptionType">
      <summary>获取或设置 <see cref="T:System.Exception" /> 的类型（如果可用）。</summary>
      <returns>
        <see cref="T:System.Exception" /> 的类型（如果可用）。</returns>
    </member>
    <member name="M:System.Web.Http.HttpError.GetPropertyValue``1(System.String)">
      <summary>从此错误实例中获取特定的属性值。</summary>
      <returns>来自此错误实例的特定属性值。</returns>
      <param name="key">错误属性的名称。</param>
      <typeparam name="TValue">属性的类型。</typeparam>
    </member>
    <member name="P:System.Web.Http.HttpError.InnerException">
      <summary>获取与此实例关联的内部 <see cref="T:System.Exception" />（如果可用）。</summary>
      <returns>与此实例关联的内部 <see cref="T:System.Exception" />（如果可用）。</returns>
    </member>
    <member name="P:System.Web.Http.HttpError.Message">
      <summary>获取或设置说明错误原因的概括性用户可见消息。在此字段中的信息应被视为公共信息，因为无论 <see cref="T:System.Web.Http.IncludeErrorDetailPolicy" /> 为何值都将传输这些信息。因此，应小心不要泄露有关服务器或应用程序的敏感信息。</summary>
      <returns>说明错误原因的概括性用户可见消息。在此字段中的信息应被视为公共信息，因为无论 <see cref="T:System.Web.Http.IncludeErrorDetailPolicy" /> 为何值都将传输这些信息。因此，应小心不要泄露有关服务器或应用程序的敏感信息。</returns>
    </member>
    <member name="P:System.Web.Http.HttpError.MessageDetail">
      <summary>获取或设置对错误的详细描述，旨在帮助开发人员确切了解问题之所在。</summary>
      <returns>对错误的详细描述，旨在帮助开发人员确切了解问题之所在。</returns>
    </member>
    <member name="P:System.Web.Http.HttpError.ModelState">
      <summary>获取 <see cref="P:System.Web.Http.HttpError.ModelState" />，其中包含模型绑定期间所发生错误的信息。</summary>
      <returns>
        <see cref="P:System.Web.Http.HttpError.ModelState" />，包含模型绑定期间所发生错误的信息。</returns>
    </member>
    <member name="P:System.Web.Http.HttpError.StackTrace">
      <summary>获取或设置与此实例关联的堆栈跟踪信息（如果可用）。</summary>
      <returns>与此实例关联的堆栈跟踪信息（如果可用）。</returns>
    </member>
    <member name="M:System.Web.Http.HttpError.System#Xml#Serialization#IXmlSerializable#GetSchema">
      <summary>此方法为保留方法，不应使用。</summary>
      <returns>始终返回 null。</returns>
    </member>
    <member name="M:System.Web.Http.HttpError.System#Xml#Serialization#IXmlSerializable#ReadXml(System.Xml.XmlReader)">
      <summary>从 <see cref="T:System.Web.Http.HttpError" /> 的 XML 表示形式生成其实例。</summary>
      <param name="reader">从中反序列化对象的 XmlReader 流。</param>
    </member>
    <member name="M:System.Web.Http.HttpError.System#Xml#Serialization#IXmlSerializable#WriteXml(System.Xml.XmlWriter)">
      <summary>将 <see cref="T:System.Web.Http.HttpError" /> 实例转换为它的 XML 表示形式。</summary>
      <param name="writer">将对象序列化到的 XmlWriter 流。</param>
    </member>
    <member name="T:System.Web.Http.HttpErrorKeys">
      <summary> 提供键用于查找 <see cref="T:System.Web.Http.HttpError" /> 字典中存储的错误信息。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.ErrorCodeKey">
      <summary> 为 ErrorCode 提供一个键。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.ExceptionMessageKey">
      <summary> 为 ExceptionMessage 提供一个键。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.ExceptionTypeKey">
      <summary> 为 ExceptionType 提供一个键。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.InnerExceptionKey">
      <summary> 为 InnerException 提供一个键。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.MessageDetailKey">
      <summary> 为 MessageDetail 提供一个键。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.MessageKey">
      <summary> 为 Message 提供一个键。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.MessageLanguageKey">
      <summary> 为 MessageLanguage 提供一个键。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.ModelStateKey">
      <summary> 为 ModelState 提供一个键。</summary>
    </member>
    <member name="F:System.Web.Http.HttpErrorKeys.StackTraceKey">
      <summary> 为 StackTrace 提供一个键。 </summary>
    </member>
    <member name="T:System.Web.Http.HttpGetAttribute">
      <summary>指定某个操作支持 GET HTTP 方法。</summary>
    </member>
    <member name="M:System.Web.Http.HttpGetAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpGetAttribute" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.HttpGetAttribute.HttpMethods">
      <summary>获取与此特性对应的 http 方法。</summary>
      <returns>与此特性对应的 http 方法。</returns>
    </member>
    <member name="T:System.Web.Http.HttpHeadAttribute">
      <summary> 指定某个操作支持 HEAD HTTP 方法。</summary>
    </member>
    <member name="M:System.Web.Http.HttpHeadAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpHeadAttribute" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.HttpHeadAttribute.HttpMethods">
      <summary>获取与此特性对应的 HTTP 方法。</summary>
      <returns>与此特性对应的 HTTP 方法。</returns>
    </member>
    <member name="T:System.Web.Http.HttpOptionsAttribute">
      <summary>表示一个特性，该特性用于限制某个 HTTP 方法，使得该方法仅处理 HTTP OPTIONS 请求。</summary>
    </member>
    <member name="M:System.Web.Http.HttpOptionsAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpOptionsAttribute" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.HttpOptionsAttribute.HttpMethods">
      <summary>获取与此特性对应的 HTTP 方法。</summary>
      <returns>与此特性对应的 HTTP 方法。</returns>
    </member>
    <member name="T:System.Web.Http.HttpPatchAttribute">
      <summary> 指定某个操作支持 PATCH HTTP 方法。</summary>
    </member>
    <member name="M:System.Web.Http.HttpPatchAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpPatchAttribute" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.HttpPatchAttribute.HttpMethods">
      <summary>获取与此特性对应的 HTTP 方法。</summary>
      <returns>与此特性对应的 HTTP 方法。</returns>
    </member>
    <member name="T:System.Web.Http.HttpPostAttribute">
      <summary>指定某个操作支持 POST HTTP 方法。</summary>
    </member>
    <member name="M:System.Web.Http.HttpPostAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpPostAttribute" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.HttpPostAttribute.HttpMethods">
      <summary>获取与此特性对应的 HTTP 方法。</summary>
      <returns>与此特性对应的 HTTP 方法。</returns>
    </member>
    <member name="T:System.Web.Http.HttpPutAttribute">
      <summary>表示一个特性，该特性用于限制某个 HTTP 方法，以便该方法仅处理 HTTP PUT 请求。</summary>
    </member>
    <member name="M:System.Web.Http.HttpPutAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpPutAttribute" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.HttpPutAttribute.HttpMethods">
      <summary>获取与此特性对应的 HTTP 方法。</summary>
      <returns>与此特性对应的 HTTP 方法。</returns>
    </member>
    <member name="T:System.Web.Http.HttpResponseException">
      <summary> 允许向客户端返回给定 <see cref="T:System.Net.Http.HttpResponseMessage" /> 的异常。</summary>
    </member>
    <member name="M:System.Web.Http.HttpResponseException.#ctor(System.Net.Http.HttpResponseMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.HttpResponseException" /> 类的新实例。</summary>
      <param name="response">要返回到客户端的 HTTP 响应。</param>
    </member>
    <member name="M:System.Web.Http.HttpResponseException.#ctor(System.Net.HttpStatusCode)">
      <summary> 初始化 <see cref="T:System.Web.Http.HttpResponseException" /> 类的新实例。</summary>
      <param name="statusCode">响应的状态代码。</param>
    </member>
    <member name="P:System.Web.Http.HttpResponseException.Response">
      <summary>获取要返回到客户端的 HTTP 响应。</summary>
      <returns>用于表示 HTTP 响应的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
    </member>
    <member name="T:System.Web.Http.HttpRouteCollection">
      <summary>
        <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 实例的集合。</summary>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.HttpRouteCollection" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.HttpRouteCollection" /> 类的新实例。</summary>
      <param name="virtualPathRoot">虚拟路径根。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Add(System.String,System.Web.Http.Routing.IHttpRoute)">
      <summary>向集合中添加 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 实例。</summary>
      <param name="name">路由的名称。</param>
      <param name="route">要添加到集合中的 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 实例。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Clear">
      <summary>从集合中移除所有项。</summary>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Contains(System.Web.Http.Routing.IHttpRoute)">
      <summary>确定集合是否包含特定 <see cref="T:System.Web.Http.Routing.IHttpRoute" />。</summary>
      <returns>如果在集合中找到 <see cref="T:System.Web.Http.Routing.IHttpRoute" />，则为 true；否则为 false。</returns>
      <param name="item">要在集合中查找的对象。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.ContainsKey(System.String)">
      <summary>确定集合是否包含具有指定键的元素。</summary>
      <returns>如果集合包含具有该键的元素，则为 true；否则为 false。</returns>
      <param name="name">要在集合中查找的键。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Web.Http.Routing.IHttpRoute}[],System.Int32)">
      <summary>从特定的数组索引位置开始，将集合的 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 实例复制到一个数组中。</summary>
      <param name="array">作为集合中元素的复制目标位置的数组。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.CopyTo(System.Web.Http.Routing.IHttpRoute[],System.Int32)">
      <summary>从特定的数组索引位置开始，将路由名称和集合的 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 实例复制到一个数组中。</summary>
      <param name="array">作为集合中元素的复制目标位置的数组。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
    </member>
    <member name="P:System.Web.Http.HttpRouteCollection.Count">
      <summary>获取集合中的项数。</summary>
      <returns>集合中的项数。</returns>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.CreateRoute(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>创建 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 实例。</summary>
      <returns>新的 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 实例。</returns>
      <param name="routeTemplate">路由模板。</param>
      <param name="defaults">一个包含默认路由参数的对象。</param>
      <param name="constraints">一个包含路由约束的对象。</param>
      <param name="dataTokens">路由数据标记。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.CreateRoute(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.IDictionary{System.String,System.Object},System.Net.Http.HttpMessageHandler)">
      <summary>创建 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 实例。</summary>
      <returns>新的 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 实例。</returns>
      <param name="routeTemplate">路由模板。</param>
      <param name="defaults">一个包含默认路由参数的对象。</param>
      <param name="constraints">一个包含路由约束的对象。</param>
      <param name="dataTokens">路由数据标记。</param>
      <param name="handler">路由的消息处理程序。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.CreateRoute(System.String,System.Object,System.Object)">
      <summary>创建 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 实例。</summary>
      <returns>新的 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 实例。</returns>
      <param name="routeTemplate">路由模板。</param>
      <param name="defaults">一个包含默认路由参数的对象。</param>
      <param name="constraints">一个包含路由约束的对象。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Dispose">
      <summary>执行与释放或重置非托管资源关联的应用程序定义任务。</summary>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Dispose(System.Boolean)">
      <summary>释放对象使用的非托管资源，并有选择性地释放托管资源。</summary>
      <param name="disposing">若为 true，则同时释放托管资源和非托管资源；若为 false，则仅释放非托管资源。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>一个可用于循环访问集合的 <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.GetRouteData(System.Net.Http.HttpRequestMessage)">
      <summary>获取指定的 HTTP 请求的路由数据。</summary>
      <returns>一个表示路由数据的 <see cref="T:System.Web.Http.Routing.IHttpRouteData" /> 实例。</returns>
      <param name="request">HTTP 请求。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.GetVirtualPath(System.Net.Http.HttpRequestMessage,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>获取虚拟路径。</summary>
      <returns>一个表示虚拟路径的 <see cref="T:System.Web.Http.Routing.IHttpVirtualPathData" /> 实例。</returns>
      <param name="request">HTTP 请求。</param>
      <param name="name">路由名称。</param>
      <param name="values">路由值。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Insert(System.Int32,System.String,System.Web.Http.Routing.IHttpRoute)">
      <summary>将 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 实例插入到集合中。</summary>
      <param name="index">从零开始的索引，应在此索引处插入 <paramref name="value" />。</param>
      <param name="name">路由名称。</param>
      <param name="value">要插入的 <see cref="T:System.Web.Http.Routing.IHttpRoute" />。值不能为 null。</param>
    </member>
    <member name="P:System.Web.Http.HttpRouteCollection.IsReadOnly">
      <summary>获取一个值，该值指示集合是否为只读集合。</summary>
      <returns>如果该集合是只读的，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Http.HttpRouteCollection.Item(System.Int32)">
      <summary>获取或设置指定索引处的元素。</summary>
      <returns>指定索引处的 <see cref="T:System.Web.Http.Routing.IHttpRoute" />。</returns>
      <param name="index">索引。</param>
    </member>
    <member name="P:System.Web.Http.HttpRouteCollection.Item(System.String)">
      <summary>获取或设置具有指定路由名称的元素。</summary>
      <returns>指定索引处的 <see cref="T:System.Web.Http.Routing.IHttpRoute" />。</returns>
      <param name="name">路由名称。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.OnGetEnumerator">
      <summary>在内部调用以获取集合的枚举器。</summary>
      <returns>一个可用于循环访问集合的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Remove(System.String)">
      <summary>从集合中移除一个 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 实例。</summary>
      <returns>如果成功移除该元素，则为 true；否则为 false。如果在集合中找不到 <paramref name="name" />，则此方法也会返回 false。</returns>
      <param name="name">要移除的路由的名称。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.System#Collections#Generic#ICollection{T}#Add(System.Web.Http.Routing.IHttpRoute)">
      <summary>向集合中添加一项。</summary>
      <param name="route">要添加到集合中的对象。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.System#Collections#Generic#ICollection{T}#Remove(System.Web.Http.Routing.IHttpRoute)">
      <summary>从集合中移除特定对象的第一个匹配项。</summary>
      <returns>如果从集合中成功移除了 <paramref name="route" />，则为 true；否则为 false。如果在原始集合中找不到 <paramref name="route" />，则此方法也会返回 false。</returns>
      <param name="route">要从集合中移除的对象。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>一个可用于循环访问集合的 <see cref="T:System.Collections.IEnumerator" /> 对象。</returns>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.TryGetValue(System.String,System.Web.Http.Routing.IHttpRoute@)">
      <summary>获取具有指定路由名称的 <see cref="T:System.Web.Http.Routing.IHttpRoute" />。</summary>
      <returns>如果集合包含具有指定名称的元素，则为 true；否则为 false。</returns>
      <param name="name">路由名称。</param>
      <param name="route">如果找到该路由名称，此方法返回时将包含 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 实例；否则为 null。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.ValidateConstraint(System.String,System.String,System.Object)">
      <summary>验证约束对于通过调用 <see cref="M:System.Web.Http.HttpRouteCollection.CreateRoute(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.IDictionary{System.String,System.Object},System.Net.Http.HttpMessageHandler)" /> 方法创建的 <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 是否有效。</summary>
      <param name="routeTemplate">路由模板。</param>
      <param name="name">约束名称。</param>
      <param name="constraint">约束对象。</param>
    </member>
    <member name="P:System.Web.Http.HttpRouteCollection.VirtualPathRoot">
      <summary>获取虚拟路径根。</summary>
      <returns>虚拟路径根。</returns>
    </member>
    <member name="T:System.Web.Http.HttpRouteCollectionExtensions">
      <summary>
        <see cref="T:System.Web.Http.HttpRouteCollection" /> 的扩展方法</summary>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.IgnoreRoute(System.Web.Http.HttpRouteCollection,System.String,System.String)">
      <summary>忽略指定的路由。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Routing.IHttpRoute" />。</returns>
      <param name="routes">应用程序的路由的集合。</param>
      <param name="routeName">要忽略的路由的名称。</param>
      <param name="routeTemplate">路由的路由模板。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.IgnoreRoute(System.Web.Http.HttpRouteCollection,System.String,System.String,System.Object)">
      <summary>忽略指定的路由。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Routing.IHttpRoute" />。</returns>
      <param name="routes">应用程序的路由的集合。</param>
      <param name="routeName">要忽略的路由的名称。</param>
      <param name="routeTemplate">路由的路由模板。</param>
      <param name="constraints">一组表达式，用于指定路由模板的值。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.MapHttpBatchRoute(System.Web.Http.HttpRouteCollection,System.String,System.String,System.Web.Http.Batch.HttpBatchHandler)">
      <summary> 映射指定的路由以处理 HTTP 批请求。</summary>
      <param name="routes">应用程序的路由的集合。</param>
      <param name="routeName">要映射的路由的名称。</param>
      <param name="routeTemplate">路由的路由模板。</param>
      <param name="batchHandler">用于处理批请求的 <see cref="T:System.Web.Http.Batch.HttpBatchHandler" />。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.MapHttpRoute(System.Web.Http.HttpRouteCollection,System.String,System.String)">
      <summary>映射指定的路由模板。</summary>
      <returns>对映射路由的引用。</returns>
      <param name="routes">应用程序的路由的集合。</param>
      <param name="name">要映射的路由的名称。</param>
      <param name="routeTemplate">路由的路由模板。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.MapHttpRoute(System.Web.Http.HttpRouteCollection,System.String,System.String,System.Object)">
      <summary>映射指定的路由模板并设置默认路由值。</summary>
      <returns>对映射路由的引用。</returns>
      <param name="routes">应用程序的路由的集合。</param>
      <param name="name">要映射的路由的名称。</param>
      <param name="routeTemplate">路由的路由模板。</param>
      <param name="defaults">一个包含默认路由值的对象。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.MapHttpRoute(System.Web.Http.HttpRouteCollection,System.String,System.String,System.Object,System.Object)">
      <summary>映射指定的路由模板并设置默认路由值和约束。</summary>
      <returns>对映射路由的引用。</returns>
      <param name="routes">应用程序的路由的集合。</param>
      <param name="name">要映射的路由的名称。</param>
      <param name="routeTemplate">路由的路由模板。</param>
      <param name="defaults">一个包含默认路由值的对象。</param>
      <param name="constraints">一组表达式，用于指定 <paramref name="routeTemplate" /> 的值。</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.MapHttpRoute(System.Web.Http.HttpRouteCollection,System.String,System.String,System.Object,System.Object,System.Net.Http.HttpMessageHandler)">
      <summary>映射指定的路由模板并设置默认的路由值、约束和终结点消息处理程序。</summary>
      <returns>对映射路由的引用。</returns>
      <param name="routes">应用程序的路由的集合。</param>
      <param name="name">要映射的路由的名称。</param>
      <param name="routeTemplate">路由的路由模板。</param>
      <param name="defaults">一个包含默认路由值的对象。</param>
      <param name="constraints">一组表达式，用于指定 <paramref name="routeTemplate" /> 的值。</param>
      <param name="handler">请求将被调度到的处理程序。</param>
    </member>
    <member name="T:System.Web.Http.HttpServer">
      <summary> 定义用于调度传入的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 并创建 <see cref="T:System.Net.Http.HttpResponseMessage" /> 作为结果的 <see cref="T:System.Net.Http.HttpMessageHandler" /> 实现。</summary>
    </member>
    <member name="M:System.Web.Http.HttpServer.#ctor">
      <summary>使用默认配置和调度程序初始化 <see cref="T:System.Web.Http.HttpServer" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.HttpServer.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary> 使用指定调度程序初始化 <see cref="T:System.Web.Http.HttpServer" /> 类的新实例。</summary>
      <param name="dispatcher">将处理传入请求的 HTTP 调度程序。</param>
    </member>
    <member name="M:System.Web.Http.HttpServer.#ctor(System.Web.Http.HttpConfiguration)">
      <summary>使用指定配置初始化 <see cref="T:System.Web.Http.HttpServer" /> 类的新实例。</summary>
      <param name="configuration">用于配置此实例的 <see cref="T:System.Web.Http.HttpConfiguration" />。</param>
    </member>
    <member name="M:System.Web.Http.HttpServer.#ctor(System.Web.Http.HttpConfiguration,System.Net.Http.HttpMessageHandler)">
      <summary>使用指定配置和调度程序初始化 <see cref="T:System.Web.Http.HttpServer" /> 类的新实例。</summary>
      <param name="configuration">用于配置此实例的 <see cref="T:System.Web.Http.HttpConfiguration" />。</param>
      <param name="dispatcher">将处理传入请求的 HTTP 调度程序。</param>
    </member>
    <member name="P:System.Web.Http.HttpServer.Configuration">
      <summary>获取用于配置此实例的 <see cref="T:System.Web.Http.HttpConfiguration" />。</summary>
      <returns>用于配置此实例的 <see cref="T:System.Web.Http.HttpConfiguration" />。</returns>
    </member>
    <member name="P:System.Web.Http.HttpServer.Dispatcher">
      <summary>获取用于处理传入请求的 HTTP 调度程序。</summary>
      <returns>用于处理传入请求的 HTTP 调度程序。</returns>
    </member>
    <member name="M:System.Web.Http.HttpServer.Dispose(System.Boolean)">
      <summary>释放对象使用的非托管资源，并有选择性地释放托管资源。</summary>
      <param name="disposing">若为 true，则同时释放托管资源和非托管资源；若为 false，则仅释放非托管资源。</param>
    </member>
    <member name="M:System.Web.Http.HttpServer.Initialize">
      <summary>为操作准备服务器。</summary>
    </member>
    <member name="M:System.Web.Http.HttpServer.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>调度传入的 <see cref="T:System.Net.Http.HttpRequestMessage" />。</summary>
      <returns> 表示异步操作的任务。</returns>
      <param name="request">要调度的请求。</param>
      <param name="cancellationToken">要监视的取消请求标记。</param>
    </member>
    <member name="T:System.Web.Http.IHttpActionResult">
      <summary>定义一个用于以异步方式创建 <see cref="T:System.Net.Http.HttpResponseMessage" /> 的命令。</summary>
    </member>
    <member name="M:System.Web.Http.IHttpActionResult.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>以异步方式创建 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>在完成时包含 <see cref="T:System.Net.Http.HttpResponseMessage" /> 的任务。</returns>
      <param name="cancellationToken">要监视的取消请求标记。</param>
    </member>
    <member name="T:System.Web.Http.IncludeErrorDetailPolicy">
      <summary>指定是否应在错误消息中包含错误详细信息，例如异常消息和堆栈跟踪。</summary>
    </member>
    <member name="F:System.Web.Http.IncludeErrorDetailPolicy.Always">
      <summary>始终包含错误详细信息。</summary>
    </member>
    <member name="F:System.Web.Http.IncludeErrorDetailPolicy.Default">
      <summary>对主机环境使用默认行为。对于 ASP.NET 托管，使用 Web.config 文件的 customErrors 元素中的值。对于自承载，使用值 <see cref="F:System.Web.Http.IncludeErrorDetailPolicy.LocalOnly" />。</summary>
    </member>
    <member name="F:System.Web.Http.IncludeErrorDetailPolicy.LocalOnly">
      <summary>仅在响应本地请求时包含错误详细信息。</summary>
    </member>
    <member name="F:System.Web.Http.IncludeErrorDetailPolicy.Never">
      <summary>从不包含错误详细信息。 </summary>
    </member>
    <member name="T:System.Web.Http.NonActionAttribute">
      <summary>表示一个特性，该特性用于指示控制器方法不是操作方法。</summary>
    </member>
    <member name="M:System.Web.Http.NonActionAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.NonActionAttribute" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Web.Http.OverrideActionFiltersAttribute">
      <summary>表示一个筛选器特性，该特性可重写在更高级别定义的操作筛选器。</summary>
    </member>
    <member name="M:System.Web.Http.OverrideActionFiltersAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.OverrideActionFiltersAttribute" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.OverrideActionFiltersAttribute.AllowMultiple">
      <summary>获取一个值，用于指示操作筛选器是否允许 multiple 特性。</summary>
      <returns>如果操作筛选器允许 multiple 特性，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Http.OverrideActionFiltersAttribute.FiltersToOverride">
      <summary>获取要重写的筛选器的类型。</summary>
      <returns>要重写的筛选器的类型。</returns>
    </member>
    <member name="T:System.Web.Http.OverrideAuthenticationAttribute">
      <summary>表示一个筛选器特性，该特性可重写在更高级别定义的身份验证筛选器。</summary>
    </member>
    <member name="M:System.Web.Http.OverrideAuthenticationAttribute.#ctor"></member>
    <member name="P:System.Web.Http.OverrideAuthenticationAttribute.AllowMultiple"></member>
    <member name="P:System.Web.Http.OverrideAuthenticationAttribute.FiltersToOverride"></member>
    <member name="T:System.Web.Http.OverrideAuthorizationAttribute">
      <summary>表示一个筛选器特性，该特性可重写在更高级别定义的授权筛选器。</summary>
    </member>
    <member name="M:System.Web.Http.OverrideAuthorizationAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.OverrideAuthorizationAttribute" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.OverrideAuthorizationAttribute.AllowMultiple">
      <summary>获取或设置一个布尔值，该值指示是否可以为单个程序元素指定多个已指示特性的实例。</summary>
      <returns>如果可以指定多个实例，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Http.OverrideAuthorizationAttribute.FiltersToOverride">
      <summary>获取筛选器重写特性的类型。</summary>
      <returns>筛选器重写特性的类型。</returns>
    </member>
    <member name="T:System.Web.Http.OverrideExceptionFiltersAttribute">
      <summary>表示一个筛选器特性，该特性可重写在更高级别定义的异常筛选器。</summary>
    </member>
    <member name="M:System.Web.Http.OverrideExceptionFiltersAttribute.#ctor"></member>
    <member name="P:System.Web.Http.OverrideExceptionFiltersAttribute.AllowMultiple"></member>
    <member name="P:System.Web.Http.OverrideExceptionFiltersAttribute.FiltersToOverride"></member>
    <member name="T:System.Web.Http.ParameterBindingAttribute">
      <summary>用于生成 <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" /> 的参数或类型的特性。如果该特性是关于类型声明的，则表示该特性将存在于该类型的所有操作参数上。</summary>
    </member>
    <member name="M:System.Web.Http.ParameterBindingAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ParameterBindingAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ParameterBindingAttribute.GetBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>获取参数绑定。</summary>
      <returns>参数绑定。</returns>
      <param name="parameter">参数说明。</param>
    </member>
    <member name="T:System.Web.Http.RouteAttribute">
      <summary>放置在某个操作上，以通过路由直接将它公开。</summary>
    </member>
    <member name="M:System.Web.Http.RouteAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.RouteAttribute" /> 类的新实例。 </summary>
    </member>
    <member name="M:System.Web.Http.RouteAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.RouteAttribute" /> 类的新实例。</summary>
      <param name="template">描述要匹配的 URI 模式的路由模板。</param>
    </member>
    <member name="P:System.Web.Http.RouteAttribute.Name">
      <returns>返回 <see cref="T:System.String" />。</returns>
    </member>
    <member name="P:System.Web.Http.RouteAttribute.Order">
      <returns>返回 <see cref="T:System.Int32" />。</returns>
    </member>
    <member name="M:System.Web.Http.RouteAttribute.System#Web#Http#Routing#IDirectRouteFactory#CreateRoute(System.Web.Http.Routing.DirectRouteFactoryContext)"></member>
    <member name="P:System.Web.Http.RouteAttribute.Template">
      <returns>返回 <see cref="T:System.String" />。</returns>
    </member>
    <member name="T:System.Web.Http.RouteParameter">
      <summary>
        <see cref="T:System.Web.Http.RouteParameter" /> 类可用于指示有关某个路由参数的属性（位于某个 <see cref="M:IHttpRoute.RouteTemplate" /> 的段中的文本和占位符）。例如，它可用于指示某个路由参数是可选的。</summary>
    </member>
    <member name="F:System.Web.Http.RouteParameter.Optional">
      <summary>一个可选参数。</summary>
    </member>
    <member name="M:System.Web.Http.RouteParameter.ToString">
      <summary>返回一个表示此实例的 <see cref="T:System.String" />。</summary>
      <returns>一个表示此实例的 <see cref="T:System.String" />。</returns>
    </member>
    <member name="T:System.Web.Http.RoutePrefixAttribute">
      <summary> 使用应用到某个控制器中所有操作的路由前缀来批注该控制器。</summary>
    </member>
    <member name="M:System.Web.Http.RoutePrefixAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.RoutePrefixAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.RoutePrefixAttribute.#ctor(System.String)">
      <summary> 初始化 <see cref="T:System.Web.Http.RoutePrefixAttribute" /> 类的新实例。</summary>
      <param name="prefix">控制器的路由前缀。</param>
    </member>
    <member name="P:System.Web.Http.RoutePrefixAttribute.Prefix">
      <summary> 获取路由前缀。</summary>
    </member>
    <member name="T:System.Web.Http.ServicesExtensions">
      <summary>为从 <see cref="T:System.Web.Http.Controllers.ServicesContainer" /> 对象获得的服务提供类型安全的访问器。</summary>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetActionInvoker(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.Controllers.IHttpActionInvoker" /> 服务。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Controllers.IHttpActionInvoker" /> 实例。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetActionSelector(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.Controllers.IHttpActionSelector" /> 服务。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Controllers.IHttpActionSelector" /> 实例。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetActionValueBinder(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.Controllers.IActionValueBinder" /> 服务。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Controllers.IActionValueBinder" /> 实例。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetApiExplorer(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.Description.IApiExplorer" /> 服务。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Description.IApiExplorer" /> 实例。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetAssembliesResolver(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.Dispatcher.IAssembliesResolver" /> 服务。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Dispatcher.IAssembliesResolver" /> 实例。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetBodyModelValidator(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.Validation.IBodyModelValidator" /> 服务。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Validation.IBodyModelValidator" /> 实例。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetContentNegotiator(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Net.Http.Formatting.IContentNegotiator" /> 服务。</summary>
      <returns>返回 <see cref="T:System.Net.Http.Formatting.IContentNegotiator" /> 实例。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetDocumentationProvider(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.Description.IDocumentationProvider" /> 服务。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Description.IDocumentationProvider" /> 实例。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetExceptionHandler(System.Web.Http.Controllers.ServicesContainer)">
      <summary>返回已注册但未经处理的异常处理程序（如果有）。</summary>
      <returns>已注册但未经处理的异常处理程序（如果有）；否则为 null。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetExceptionLoggers(System.Web.Http.Controllers.ServicesContainer)">
      <summary>返回已注册但未经处理的异常记录器的集合。</summary>
      <returns>已注册但未经处理的异常记录器的集合。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetFilterProviders(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.Filters.IFilterProvider" /> 集合。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Filters.IFilterProvider" /> 对象的集合。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetHostBufferPolicySelector(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.Hosting.IHostBufferPolicySelector" /> 服务。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Hosting.IHostBufferPolicySelector" /> 实例。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetHttpControllerActivator(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerActivator" /> 服务。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerActivator" /> 实例；如果未注册任何实例，则返回 null。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetHttpControllerSelector(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerSelector" /> 服务。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerSelector" /> 实例。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetHttpControllerTypeResolver(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerTypeResolver" /> 服务。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerTypeResolver" /> 实例。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetModelBinderProviders(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> 集合。</summary>
      <returns>返回 <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> 对象的集合。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetModelMetadataProvider(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" /> 服务。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" /> 实例。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetModelValidatorProviders(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> 集合。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> 对象的集合。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetTraceManager(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.Tracing.ITraceManager" /> 服务。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Tracing.ITraceManager" /> 实例。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetTraceWriter(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.Tracing.ITraceWriter" /> 服务。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Tracing.ITraceWriter" /> 实例。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetValueProviderFactories(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取 <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> 集合。</summary>
      <returns>返回 <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> 对象的集合。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="T:System.Web.Http.SingleResult">
      <summary>表示包含零个或一个实体的 <see cref="T:System.Linq.IQueryable" />。在 System.Web.Http.OData 或 System.Web.OData 命名空间中与 [EnableQuery] 一起使用。</summary>
    </member>
    <member name="M:System.Web.Http.SingleResult.#ctor(System.Linq.IQueryable)">
      <summary>初始化 <see cref="T:System.Web.Http.SingleResult" /> 类的新实例。</summary>
      <param name="queryable">包含零个或一个实体的 <see cref="T:System.Linq.IQueryable" />。</param>
    </member>
    <member name="M:System.Web.Http.SingleResult.Create``1(System.Linq.IQueryable{``0})">
      <summary>从 <see cref="T:System.Linq.IQueryable`1" /> 创建 <see cref="T:System.Web.Http.SingleResult`1" />。无需显式指定类型 <paramref name="T" /> 即可实例化 <see cref="T:System.Web.Http.SingleResult`1" /> 对象的帮助器方法。</summary>
      <returns>创建的 <see cref="T:System.Web.Http.SingleResult`1" />。</returns>
      <param name="queryable">包含零个或一个实体的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="T">数据源中数据的类型。</typeparam>
    </member>
    <member name="P:System.Web.Http.SingleResult.Queryable">
      <summary>包含零个或一个实体的 <see cref="T:System.Linq.IQueryable" />。</summary>
    </member>
    <member name="T:System.Web.Http.SingleResult`1">
      <summary>表示包含零个或一个实体的 <see cref="T:System.Linq.IQueryable`1" />。在 System.Web.Http.OData 或 System.Web.OData 命名空间中与 [EnableQuery] 一起使用。</summary>
      <typeparam name="T">数据源中数据的类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.SingleResult`1.#ctor(System.Linq.IQueryable{`0})">
      <summary>初始化 <see cref="T:System.Web.Http.SingleResult`1" /> 类的新实例。</summary>
      <param name="queryable">包含零个或一个实体的 <see cref="T:System.Linq.IQueryable`1" />。</param>
    </member>
    <member name="P:System.Web.Http.SingleResult`1.Queryable">
      <summary>包含零个或一个实体的 <see cref="T:System.Linq.IQueryable`1" />。</summary>
    </member>
    <member name="T:System.Web.Http.Batch.BatchExecutionOrder">
      <summary> 定义批请求的执行顺序。</summary>
    </member>
    <member name="F:System.Web.Http.Batch.BatchExecutionOrder.NonSequential">
      <summary> 断续执行批请求。 </summary>
    </member>
    <member name="F:System.Web.Http.Batch.BatchExecutionOrder.Sequential">
      <summary> 连续执行批请求。</summary>
    </member>
    <member name="T:System.Web.Http.Batch.BatchHttpRequestMessageExtensions">
      <summary> 为 <see cref="T:System.Net.Http.HttpRequestMessage" /> 类提供扩展方法。</summary>
    </member>
    <member name="M:System.Web.Http.Batch.BatchHttpRequestMessageExtensions.CopyBatchRequestProperties(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpRequestMessage)">
      <summary> 从其他 <see cref="T:System.Net.Http.HttpRequestMessage" /> 中复制属性。</summary>
      <param name="subRequest">子请求。</param>
      <param name="batchRequest">包含要复制的属性的批请求。</param>
    </member>
    <member name="T:System.Web.Http.Batch.DefaultHttpBatchHandler">
      <summary>表示将 HTTP 请求/响应消息编码为 MIME 多部分的 <see cref="T:System.Web.Http.Batch.HttpBatchHandler" /> 的默认实现。</summary>
    </member>
    <member name="M:System.Web.Http.Batch.DefaultHttpBatchHandler.#ctor(System.Web.Http.HttpServer)">
      <summary>初始化 <see cref="T:System.Web.Http.Batch.DefaultHttpBatchHandler" /> 类的新实例。</summary>
      <param name="httpServer">用于处理单个批请求的 <see cref="T:System.Web.Http.HttpServer" />。</param>
    </member>
    <member name="M:System.Web.Http.Batch.DefaultHttpBatchHandler.CreateResponseMessageAsync(System.Collections.Generic.IList{System.Net.Http.HttpResponseMessage},System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>创建批响应消息。</summary>
      <returns>批响应消息。</returns>
      <param name="responses">批请求的响应。</param>
      <param name="request">包含所有批请求的原始请求。</param>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="M:System.Web.Http.Batch.DefaultHttpBatchHandler.ExecuteRequestMessagesAsync(System.Collections.Generic.IEnumerable{System.Net.Http.HttpRequestMessage},System.Threading.CancellationToken)">
      <summary>执行批请求消息。</summary>
      <returns>批请求的 <see cref="T:System.Net.Http.HttpResponseMessage" /> 集合。</returns>
      <param name="requests">批请求消息的集合。</param>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="P:System.Web.Http.Batch.DefaultHttpBatchHandler.ExecutionOrder">
      <summary>获取或设置批请求的执行顺序。默认执行顺序为连续。</summary>
      <returns>批请求的执行顺序。默认执行顺序为连续。</returns>
    </member>
    <member name="M:System.Web.Http.Batch.DefaultHttpBatchHandler.ParseBatchRequestsAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>将传入的批请求转换为请求消息的集合。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpRequestMessage" /> 的集合。</returns>
      <param name="request">包含批请求消息的请求。</param>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="M:System.Web.Http.Batch.DefaultHttpBatchHandler.ProcessBatchAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>处理批请求。</summary>
      <returns>操作结果。</returns>
      <param name="request">批请求。</param>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="P:System.Web.Http.Batch.DefaultHttpBatchHandler.SupportedContentTypes">
      <summary>获取批请求的受支持内容类型。</summary>
      <returns>批请求的受支持内容类型。</returns>
    </member>
    <member name="M:System.Web.Http.Batch.DefaultHttpBatchHandler.ValidateRequest(System.Net.Http.HttpRequestMessage)">
      <summary>验证传入请求是否包含批请求消息。</summary>
      <param name="request">包含批请求消息的请求。</param>
    </member>
    <member name="T:System.Web.Http.Batch.HttpBatchHandler">
      <summary>定义抽象，以便处理 HTTP 批请求。</summary>
    </member>
    <member name="M:System.Web.Http.Batch.HttpBatchHandler.#ctor(System.Web.Http.HttpServer)">
      <summary>初始化 <see cref="T:System.Web.Http.Batch.HttpBatchHandler" /> 类的新实例。</summary>
      <param name="httpServer">用于处理各个批请求的 <see cref="T:System.Web.Http.HttpServer" />。</param>
    </member>
    <member name="P:System.Web.Http.Batch.HttpBatchHandler.Invoker">
      <summary>获取调用程序，以将批请求发送到 <see cref="T:System.Web.Http.HttpServer" />。</summary>
      <returns>将批请求发送到 <see cref="T:System.Web.Http.HttpServer" /> 的调用程序。</returns>
    </member>
    <member name="M:System.Web.Http.Batch.HttpBatchHandler.ProcessBatchAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>将传入批请求作为单个 <see cref="T:System.Net.Http.HttpRequestMessage" /> 进行处理。</summary>
      <returns>批响应。</returns>
      <param name="request">批请求。</param>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="M:System.Web.Http.Batch.HttpBatchHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>异步发送批处理程序。</summary>
      <returns>操作结果。</returns>
      <param name="request">发送请求。</param>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ApiControllerActionInvoker">
      <summary>调用控制器的操作方法。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ApiControllerActionInvoker.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ApiControllerActionInvoker" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ApiControllerActionInvoker.InvokeActionAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>使用指定的控制器上下文来异步调用指定操作。</summary>
      <returns>已调用的操作。</returns>
      <param name="actionContext">控制器上下文。</param>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ApiControllerActionSelector">
      <summary>表示基于反射的操作选择器。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ApiControllerActionSelector.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ApiControllerActionSelector" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ApiControllerActionSelector.GetActionMapping(System.Web.Http.Controllers.HttpControllerDescriptor)">
      <summary>获取 <see cref="T:System.Web.Http.Controllers.ApiControllerActionSelector" /> 的操作映射。</summary>
      <returns>操作映射。</returns>
      <param name="controllerDescriptor">描述控制器的信息。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ApiControllerActionSelector.SelectAction(System.Web.Http.Controllers.HttpControllerContext)">
      <summary>为 <see cref="T:System.Web.Http.Controllers.ApiControllerActionSelector" /> 选择操作。</summary>
      <returns>选定的操作。</returns>
      <param name="controllerContext">控制器上下文。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ControllerServices">
      <summary> 表示可以特定于控制器的服务的容器。这将覆盖其父 <see cref="T:System.Web.Http.Controllers.ServicesContainer" /> 中的服务。控制器可以在此处设置服务，也可以贯穿到更全局的服务集。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.#ctor(System.Web.Http.Controllers.ServicesContainer)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ControllerServices" /> 类的新实例。</summary>
      <param name="parent">父服务容器。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.ClearSingle(System.Type)">
      <summary>从默认服务中删除单实例服务。</summary>
      <param name="serviceType">服务的类型。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.GetService(System.Type)">
      <summary>获取指定类型的服务。</summary>
      <returns>服务的第一个实例；如果找不到该服务，则为 null。</returns>
      <param name="serviceType">服务的类型。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.GetServiceInstances(System.Type)">
      <summary>获取给定服务类型的服务对象的列表，并验证该服务类型。</summary>
      <returns>指定类型的服务对象的列表。</returns>
      <param name="serviceType">服务类型。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.GetServices(System.Type)">
      <summary>获取给定服务类型的服务对象的列表。</summary>
      <returns>指定类型的服务对象的列表；如果未找到该服务，则为空列表。</returns>
      <param name="serviceType">服务的类型。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.IsSingleService(System.Type)">
      <summary>查询服务类型是否为单实例。</summary>
      <returns>如果该服务类型最多只有一个实例，则为 true；如果该服务类型支持多个实例，则为 false。</returns>
      <param name="serviceType">服务类型。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.ReplaceSingle(System.Type,System.Object)">
      <summary>替换单实例服务对象。</summary>
      <param name="serviceType">服务类型。</param>
      <param name="service">服务对象，用于替换以前的实例。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpActionBinding">
      <summary>描述绑定将“如何”发生但不实际进行绑定。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionBinding.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpActionBinding" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionBinding.#ctor(System.Web.Http.Controllers.HttpActionDescriptor,System.Web.Http.Controllers.HttpParameterBinding[])">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpActionBinding" /> 类的新实例。</summary>
      <param name="actionDescriptor">此绑定所用于的操作的后向指针。</param>
      <param name="bindings">每个参数的同步绑定。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionBinding.ActionDescriptor">
      <summary>获取或设置此绑定所用于的操作的后向指针。</summary>
      <returns>此绑定所用于的操作的后向指针。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionBinding.ExecuteBindingAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>异步执行给定请求上下文的绑定。</summary>
      <returns>绑定完成时将通知的任务。 </returns>
      <param name="actionContext">绑定的操作上下文。这包含将进行填充的参数字典。</param>
      <param name="cancellationToken">用于取消绑定操作的取消标记。联编程序也可以将参数绑定到此项。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionBinding.ParameterBindings">
      <summary>获取或设置每个参数的同步绑定。</summary>
      <returns>每个参数的同步绑定。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpActionContext">
      <summary>包含正在执行的操作的信息。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionContext.#ctor">
      <summary> 初始化 <see cref="T:System.Web.Http.Controllers.HttpActionContext" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionContext.#ctor(System.Web.Http.Controllers.HttpControllerContext,System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpActionContext" /> 类的新实例。</summary>
      <param name="controllerContext">控制器上下文。</param>
      <param name="actionDescriptor">操作描述符。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.ActionArguments">
      <summary>获取操作参数的列表。</summary>
      <returns>操作参数的列表。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.ActionDescriptor">
      <summary>获取或设置操作上下文的操作描述符。</summary>
      <returns>操作描述符。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.ControllerContext">
      <summary>获取或设置控制器上下文。</summary>
      <returns>控制器上下文。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.ModelState">
      <summary>获取上下文的模型状态字典。</summary>
      <returns>模型状态字典。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.Request">
      <summary>获取操作上下文的请求消息。</summary>
      <returns>操作上下文的请求消息。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.RequestContext">
      <summary>获取当前请求上下文。</summary>
      <returns>当前请求上下文。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.Response">
      <summary>获取或设置操作上下文的响应消息。</summary>
      <returns>操作上下文的响应消息。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpActionContextExtensions">
      <summary>包含 <see cref="T:System.Web.Http.Controllers.HttpActionContext" /> 的扩展方法。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.Bind(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)"></member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.Bind(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.Collections.Generic.IEnumerable{System.Web.Http.ModelBinding.IModelBinder})"></member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.GetMetadataProvider(System.Web.Http.Controllers.HttpActionContext)"></member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.GetValidatorProviders(System.Web.Http.Controllers.HttpActionContext)"></member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.GetValidators(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.Metadata.ModelMetadata)"></member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.TryBindStrongModel``1(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.String,System.Web.Http.Metadata.ModelMetadataProvider,``0@)">
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpActionDescriptor">
      <summary>提供有关操作方法的信息。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.#ctor(System.Web.Http.Controllers.HttpControllerDescriptor)">
      <summary>使用用于描述操作控制器的指定信息初始化 <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> 类的新实例。</summary>
      <param name="controllerDescriptor">用于描述操作控制器的信息。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.ActionBinding">
      <summary>获取或设置用于描述操作的绑定。</summary>
      <returns>用于描述操作的绑定。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.ActionName">
      <summary>获取操作的名称。</summary>
      <returns>操作的名称。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.Configuration">
      <summary>获取或设置操作配置。</summary>
      <returns>操作配置。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.ControllerDescriptor">
      <summary>获取用于描述操作控制器的信息。</summary>
      <returns>用于描述操作控制器的信息。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.ExecuteAsync(System.Web.Http.Controllers.HttpControllerContext,System.Collections.Generic.IDictionary{System.String,System.Object},System.Threading.CancellationToken)">
      <summary>执行所描述的操作并返回 <see cref="T:System.Threading.Tasks.Task`1" />，后者在完成后将包含该操作的返回值。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />，完成后将包含操作的返回值。</returns>
      <param name="controllerContext">控制器上下文。</param>
      <param name="arguments">参数列表。</param>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.GetCustomAttributes``1">
      <summary>返回与操作描述符关联的自定义特性。</summary>
      <returns>与操作描述符关联的自定义特性。</returns>
      <typeparam name="T">操作描述符。</typeparam>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.GetCustomAttributes``1(System.Boolean)">
      <summary>获取操作的自定义特性。</summary>
      <returns>应用到此操作的自定义特性的集合。</returns>
      <param name="inherit">要搜索此操作的继承链以查找特性，则为 true；否则为 false。</param>
      <typeparam name="T">要搜索的特性的类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.GetFilterPipeline">
      <summary>检索给定配置和操作的筛选器。</summary>
      <returns>给定配置和操作的筛选器。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.GetFilters">
      <summary>检索操作描述符的筛选器。</summary>
      <returns>操作描述符的筛选器。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.GetParameters">
      <summary>检索操作描述符的参数。</summary>
      <returns>操作描述符的参数。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.Properties">
      <summary>获取与此实例关联的属性。</summary>
      <returns>与此实例关联的属性。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.ResultConverter">
      <summary>获取用于将调用 ExecuteAsync(HttpControllerContext, IDictionaryString, Object)" 的结果正确转换为 <see cref="T:System.Net.Http.HttpResponseMessage" /> 实例的转换器。</summary>
      <returns>操作结果转换器。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.ReturnType">
      <summary>获取描述符的返回类型。</summary>
      <returns>描述符的返回类型。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.SupportedHttpMethods">
      <summary>获取描述符的受支持 HTTP 方法的集合。</summary>
      <returns>描述符的受支持 HTTP 方法的集合。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpControllerContext">
      <summary>包含单个 HTTP 操作的信息。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerContext.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpControllerContext" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerContext.#ctor(System.Web.Http.Controllers.HttpRequestContext,System.Net.Http.HttpRequestMessage,System.Web.Http.Controllers.HttpControllerDescriptor,System.Web.Http.Controllers.IHttpController)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpControllerContext" /> 类的新实例。</summary>
      <param name="requestContext">请求上下文。</param>
      <param name="request">HTTP 请求。</param>
      <param name="controllerDescriptor">控制器描述符。</param>
      <param name="controller">控制器。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerContext.#ctor(System.Web.Http.HttpConfiguration,System.Web.Http.Routing.IHttpRouteData,System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpControllerContext" /> 类的新实例。</summary>
      <param name="configuration">配置。</param>
      <param name="routeData">路由数据。</param>
      <param name="request">请求。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.Configuration">
      <summary>获取或设置配置。</summary>
      <returns>配置。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.Controller">
      <summary>获取或设置 HTTP 控制器。</summary>
      <returns>HTTP 控制器。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.ControllerDescriptor">
      <summary>获取或设置控制器描述符。</summary>
      <returns>控制器描述符。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.Request">
      <summary>获取或设置请求。</summary>
      <returns>请求。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.RequestContext">
      <summary>获取或设置请求上下文。</summary>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.RouteData">
      <summary>获取或设置路由数据。</summary>
      <returns>路由数据。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpControllerDescriptor">
      <summary>表示描述 HTTP 控制器的信息。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.#ctor(System.Web.Http.HttpConfiguration,System.String,System.Type)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 类的新实例。</summary>
      <param name="configuration">配置。</param>
      <param name="controllerName">控制器名称。</param>
      <param name="controllerType">控制器类型。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerDescriptor.Configuration">
      <summary>获取或设置与控制器关联的配置。</summary>
      <returns>与控制器关联的配置。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerDescriptor.ControllerName">
      <summary>获取或设置控制器的名称。</summary>
      <returns>控制器的名称。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerDescriptor.ControllerType">
      <summary>获取或设置控制器的类型。</summary>
      <returns>控制器的类型。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.CreateController(System.Net.Http.HttpRequestMessage)">
      <summary>为给定 <see cref="T:System.Net.Http.HttpRequestMessage" /> 创建一个控制器实例。</summary>
      <returns>创建的控制器实例。</returns>
      <param name="request">请求消息。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.GetCustomAttributes``1">
      <summary>检索控制器的自定义特性的集合。</summary>
      <returns>自定义特性的集合。</returns>
      <typeparam name="T">对象的类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.GetCustomAttributes``1(System.Boolean)">
      <summary> 返回可分配到此描述符控制器的 &lt;typeparamref name="T" /&gt; 的特性集合。</summary>
      <returns>与此控制器关联的特性的集合。</returns>
      <param name="inherit">要搜索此控制器的继承链以查找特性，则为 true；否则为 false。</param>
      <typeparam name="T">用于筛选特性的集合。使用 <see cref="T:System.Object" /> 的值来检索所有特性。</typeparam>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.GetFilters">
      <summary>返回与控制器关联的筛选器的集合。</summary>
      <returns>与控制器关联的筛选器的集合。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerDescriptor.Properties">
      <summary>获取与此实例关联的属性。</summary>
      <returns>与此实例关联的属性。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpControllerSettings">
      <summary>包含 HTTP 控制器的设置。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerSettings.#ctor(System.Web.Http.HttpConfiguration)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpControllerSettings" /> 类的新实例。</summary>
      <param name="configuration">用于初始化实例的配置对象。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerSettings.Formatters">
      <summary>获取控制器的 <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" /> 实例的集合。</summary>
      <returns>
        <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" /> 实例的集合。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerSettings.ParameterBindingRules">
      <summary>获取控制器的参数绑定函数的集合。</summary>
      <returns>参数绑定函数的集合。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerSettings.Services">
      <summary>获取控制器的服务实例的集合。</summary>
      <returns>服务实例的集合。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpParameterBinding">
      <summary> 描述如何绑定参数。绑定应为静态绑定（纯粹基于描述符），并可以跨请求共享。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" /> 类的新实例。</summary>
      <param name="descriptor">用于描述参数的 <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" />。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterBinding.Descriptor">
      <summary>获取用于初始化此实例的 <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" />。</summary>
      <returns>
        <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> 实例。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterBinding.ErrorMessage">
      <summary>如果绑定无效，则获取描述绑定错误的错误消息。</summary>
      <returns>错误消息。如果绑定成功，则此值为 null。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>以异步方式执行给定请求的绑定。</summary>
      <returns>一个表示异步操作的任务对象。</returns>
      <param name="metadataProvider">要用于验证的元数据提供程序。</param>
      <param name="actionContext">绑定的操作上下文。操作上下文包含将使用参数填充的参数字典。</param>
      <param name="cancellationToken">用于取消绑定操作的取消标记。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterBinding.GetValue(System.Web.Http.Controllers.HttpActionContext)">
      <summary> 从操作上下文的参数字典中获取参数值。</summary>
      <returns>此参数在给定操作上下文中的值；如果尚未设置此参数，则为 null。</returns>
      <param name="actionContext">操作上下文。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterBinding.IsValid">
      <summary>获取一个值，该值指示绑定是否成功。</summary>
      <returns>如果绑定成功，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterBinding.SetValue(System.Web.Http.Controllers.HttpActionContext,System.Object)">
      <summary>在操作上下文的参数字典中设置此参数绑定的结果。</summary>
      <param name="actionContext">操作上下文。</param>
      <param name="value">参数值。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterBinding.WillReadBody">
      <summary>返回一个值，该值指示此 <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" /> 实例是否将读取 HTTP 消息的实体正文。</summary>
      <returns>如果此 <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" /> 将读取实体正文，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpParameterDescriptor">
      <summary>表示 HTTP 参数描述符。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterDescriptor.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterDescriptor.#ctor(System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> 类的新实例。</summary>
      <param name="actionDescriptor">操作描述符。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.ActionDescriptor">
      <summary>获取或设置操作描述符。</summary>
      <returns>操作描述符。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.Configuration">
      <summary>获取或设置 <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> 的 <see cref="T:System.Web.Http.HttpConfiguration" />。</summary>
      <returns>
        <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> 的 <see cref="T:System.Web.Http.HttpConfiguration" />。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.DefaultValue">
      <summary>获取参数的默认值。</summary>
      <returns>参数的默认值。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterDescriptor.GetCustomAttributes``1">
      <summary>从参数中检索自定义特性的集合。</summary>
      <returns>参数中的自定义特性的集合。</returns>
      <typeparam name="T">自定义特性的类型。</typeparam>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.IsOptional">
      <summary>获取一个值，该值指示参数是否为可选。</summary>
      <returns>如果参数为可选，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.ParameterBinderAttribute">
      <summary>获取或设置参数绑定特性。</summary>
      <returns>参数绑定特性。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.ParameterName">
      <summary>获取参数名。</summary>
      <returns>参数名。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.ParameterType">
      <summary>获取参数的类型。</summary>
      <returns>参数的类型。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.Prefix">
      <summary>获取此参数的前缀。</summary>
      <returns>此参数的前缀。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.Properties">
      <summary>获取此参数的属性。</summary>
      <returns>此参数的属性。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpRequestContext">
      <summary>表示与请求关联的上下文。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpRequestContext.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.HttpRequestContext" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpRequestContext.ClientCertificate">
      <summary>获取或设置客户端证书。</summary>
      <returns>返回 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpRequestContext.Configuration">
      <summary>获取或设置配置。</summary>
      <returns>返回 <see cref="T:System.Web.Http.HttpConfiguration" />。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpRequestContext.IncludeErrorDetail">
      <summary>获取或设置一个值，用于指示是否应在此请求的响应中包含错误详细信息，例如异常消息和堆栈跟踪。</summary>
      <returns>返回 <see cref="T:System.Boolean" />。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpRequestContext.IsLocal">
      <summary>获取或设置一个值，用于指示请求是否源自本地地址。</summary>
      <returns>返回 <see cref="T:System.Boolean" />。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpRequestContext.Principal">
      <summary>获取或设置主体。</summary>
      <returns>返回 <see cref="T:System.Security.Principal.IPrincipal" />。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpRequestContext.RouteData">
      <summary>获取或设置路由数据。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Routing.IHttpRouteData" />。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpRequestContext.Url">
      <summary>获取或设置工厂，用于生成指向其他 API 的 URL。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Routing.UrlHelper" />。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpRequestContext.VirtualPathRoot">
      <summary>获取或设置虚拟路径根。</summary>
      <returns>返回 <see cref="T:System.String" />。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.IActionHttpMethodProvider"></member>
    <member name="P:System.Web.Http.Controllers.IActionHttpMethodProvider.HttpMethods"></member>
    <member name="T:System.Web.Http.Controllers.IActionResultConverter">
      <summary> 一个转换例程的协定，该转换例程可以使用从 &lt;see cref="M:System.Web.Http.Controllers.HttpActionDescriptor.ExecuteAsync(System.Web.Http.Controllers.HttpControllerContext,System.Collections.Generic.IDictionary{System.String,System.Object})" /&gt; 返回的操作结果，并将该结果转换为 <see cref="T:System.Net.Http.HttpResponseMessage" /> 的实例。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IActionResultConverter.Convert(System.Web.Http.Controllers.HttpControllerContext,System.Object)">
      <summary>将指定的 <see cref="T:System.Web.Http.Controllers.IActionResultConverter" /> 对象转换为另一个对象。</summary>
      <returns>转换后的对象。</returns>
      <param name="controllerContext">控制器上下文。</param>
      <param name="actionResult">操作结果。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.IActionValueBinder">
      <summary>定义与参数值相关的检索操作绑定的方法。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IActionValueBinder.GetBinding(System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>获取 <see cref="T:System.Web.Http.Controllers.HttpActionBinding" />。</summary>
      <returns>
        <see cref="T:System.Web.Http.Controllers.HttpActionBinding" /> 对象。</returns>
      <param name="actionDescriptor">操作描述符。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.IControllerConfiguration">
      <summary> 如果某个控制器是使用此接口的特性修饰的，则将调用此接口来初始化该控制器设置。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IControllerConfiguration.Initialize(System.Web.Http.Controllers.HttpControllerSettings,System.Web.Http.Controllers.HttpControllerDescriptor)">
      <summary> 为设置此 controllerDescriptor 的按控制器重写而调用的回调。</summary>
      <param name="controllerSettings">要初始化的控制器设置。</param>
      <param name="controllerDescriptor">控制器描述符。请注意，<see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 可以与派生的控制器类型相关联（假定 <see cref="T:System.Web.Http.Controllers.IControllerConfiguration" /> 是继承的）。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.IHttpActionInvoker">
      <summary>包含用于调用 HTTP 操作的方法。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IHttpActionInvoker.InvokeActionAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>异步执行 HTTP 操作。</summary>
      <returns>新启动的任务。</returns>
      <param name="actionContext">执行上下文。</param>
      <param name="cancellationToken">为 HTTP 操作分配的取消标记。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.IHttpActionSelector">
      <summary>包含用于选择操作方法的逻辑。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IHttpActionSelector.GetActionMapping(System.Web.Http.Controllers.HttpControllerDescriptor)">
      <summary>返回选择器可以选择的所有 <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> 的映射，该映射使用操作字符串作为键。此方法主要由 <see cref="T:System.Web.Http.Description.IApiExplorer" /> 调用，以发现控制器中的所有可能操作。</summary>
      <returns>选择器可以选择的 <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> 的映射或 null（如果选择器没有 <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> 的已经过良好定义的映射）。</returns>
      <param name="controllerDescriptor">控制器描述符。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.IHttpActionSelector.SelectAction(System.Web.Http.Controllers.HttpControllerContext)">
      <summary>选择控制器的操作。</summary>
      <returns>控制器的操作。</returns>
      <param name="controllerContext">控制器的上下文。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.IHttpController">
      <summary>表示 HTTP 控制器。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IHttpController.ExecuteAsync(System.Web.Http.Controllers.HttpControllerContext,System.Threading.CancellationToken)">
      <summary>执行用于同步的控制器。</summary>
      <returns>控制器。</returns>
      <param name="controllerContext">测试控制器的当前上下文。</param>
      <param name="cancellationToken">取消操作的通知。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ParameterBindingExtensions">
      <summary>定义 <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" /> 的扩展方法。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindAsError(System.Web.Http.Controllers.HttpParameterDescriptor,System.String)">
      <summary>绑定导致错误的参数。</summary>
      <returns>HTTP 参数绑定对象。</returns>
      <param name="parameter">用于描述要绑定的参数的参数描述符。</param>
      <param name="message">用于描述绑定失败原因的错误消息。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithAttribute(System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.ParameterBindingAttribute)">
      <summary>绑定参数，就像该参数具有声明中的给定特性一样。</summary>
      <returns>HTTP 参数绑定对象。</returns>
      <param name="parameter">要为其提供绑定的参数。</param>
      <param name="attribute">用于描述绑定的特性。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithFormatter(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>通过分析 HTTP 正文内容绑定参数。</summary>
      <returns>HTTP 参数绑定对象。</returns>
      <param name="parameter">用于描述要绑定的参数的参数描述符。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithFormatter(System.Web.Http.Controllers.HttpParameterDescriptor,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>通过分析 HTTP 正文内容绑定参数。</summary>
      <returns>HTTP 参数绑定对象。</returns>
      <param name="parameter">用于描述要绑定的参数的参数描述符。</param>
      <param name="formatters">格式化程序的列表，可从中选择用于将参数序列化为对象的适当格式化程序。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithFormatter(System.Web.Http.Controllers.HttpParameterDescriptor,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Web.Http.Validation.IBodyModelValidator)">
      <summary>通过分析 HTTP 正文内容绑定参数。</summary>
      <returns>HTTP 参数绑定对象。</returns>
      <param name="parameter">用于描述要绑定的参数的参数描述符。</param>
      <param name="formatters">格式化程序的列表，可从中选择用于将参数序列化为对象的适当格式化程序。</param>
      <param name="bodyModelValidator">用于验证参数的正文模型验证程序。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithFormatter(System.Web.Http.Controllers.HttpParameterDescriptor,System.Net.Http.Formatting.MediaTypeFormatter[])">
      <summary>通过分析 HTTP 正文内容绑定参数。</summary>
      <returns>HTTP 参数绑定对象。</returns>
      <param name="parameter">用于描述要绑定的参数的参数描述符。</param>
      <param name="formatters">格式化程序的列表，可从中选择用于将参数序列化为对象的适当格式化程序。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithModelBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>通过分析查询字符串绑定参数。</summary>
      <returns>HTTP 参数绑定对象。</returns>
      <param name="parameter">用于描述要绑定的参数的参数描述符。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithModelBinding(System.Web.Http.Controllers.HttpParameterDescriptor,System.Collections.Generic.IEnumerable{System.Web.Http.ValueProviders.ValueProviderFactory})">
      <summary>通过分析查询字符串绑定参数。</summary>
      <returns>HTTP 参数绑定对象。</returns>
      <param name="parameter">用于描述要绑定的参数的参数描述符。</param>
      <param name="valueProviderFactories">用于提供查询字符串参数数据的值提供程序工厂。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithModelBinding(System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.ModelBinding.IModelBinder)">
      <summary>通过分析查询字符串绑定参数。</summary>
      <returns>HTTP 参数绑定对象。</returns>
      <param name="parameter">用于描述要绑定的参数的参数描述符。</param>
      <param name="binder">用于将参数组装为对象的模型联编程序。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithModelBinding(System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.ModelBinding.IModelBinder,System.Collections.Generic.IEnumerable{System.Web.Http.ValueProviders.ValueProviderFactory})">
      <summary>通过分析查询字符串绑定参数。</summary>
      <returns>HTTP 参数绑定对象。</returns>
      <param name="parameter">用于描述要绑定的参数的参数描述符。</param>
      <param name="binder">用于将参数组装为对象的模型联编程序。</param>
      <param name="valueProviderFactories">用于提供查询字符串参数数据的值提供程序工厂。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithModelBinding(System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.ValueProviders.ValueProviderFactory[])">
      <summary>通过分析查询字符串绑定参数。</summary>
      <returns>HTTP 参数绑定对象。</returns>
      <param name="parameter">用于描述要绑定的参数的参数描述符。</param>
      <param name="valueProviderFactories">用于提供查询字符串参数数据的值提供程序工厂。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ReflectedHttpActionDescriptor">
      <summary>表示反射的同步或异步操作方法。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ReflectedHttpActionDescriptor" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.#ctor(System.Web.Http.Controllers.HttpControllerDescriptor,System.Reflection.MethodInfo)">
      <summary>使用指定描述符和方法详细信息初始化 <see cref="T:System.Web.Http.Controllers.ReflectedHttpActionDescriptor" /> 类的新实例。</summary>
      <param name="controllerDescriptor">控制器描述符。</param>
      <param name="methodInfo">操作方法信息。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.ActionName">
      <summary>获取操作的名称。</summary>
      <returns>操作的名称。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.Equals(System.Object)"></member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.ExecuteAsync(System.Web.Http.Controllers.HttpControllerContext,System.Collections.Generic.IDictionary{System.String,System.Object},System.Threading.CancellationToken)">
      <summary>执行所描述的操作并返回 <see cref="T:System.Threading.Tasks.Task`1" />，后者在完成后将包含该操作的返回值。</summary>
      <returns>[T:System.Threading.Tasks.Task`1"]，完成后将包含操作的返回值。</returns>
      <param name="controllerContext">上下文。</param>
      <param name="arguments">参数。</param>
      <param name="cancellationToken">用于取消操作的取消标记。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.GetCustomAttributes``1(System.Boolean)">
      <summary>返回为此成员定义的自定义特性的数组（按类型标识）。</summary>
      <returns>自定义特性的数组，如果没有自定义特性，则为空数组。</returns>
      <param name="inherit">要搜索此操作的继承链以查找特性，则为 true；否则为 false。</param>
      <typeparam name="T">自定义特性的类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.GetFilters">
      <summary>检索有关操作筛选器的信息。</summary>
      <returns>筛选器信息。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.GetHashCode"></member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.GetParameters">
      <summary>检索操作方法的参数。</summary>
      <returns>操作方法的参数。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.MethodInfo">
      <summary>获取或设置操作方法信息。</summary>
      <returns>操作方法信息。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.ReturnType">
      <summary>获取此方法的返回类型。</summary>
      <returns>此方法的返回类型。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.SupportedHttpMethods">
      <summary>获取或设置支持的 http 方法。</summary>
      <returns>支持的 http 方法。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor">
      <summary>表示反射的 HTTP 参数描述符。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.#ctor(System.Web.Http.Controllers.HttpActionDescriptor,System.Reflection.ParameterInfo)">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor" /> 类的新实例。</summary>
      <param name="actionDescriptor">操作描述符。</param>
      <param name="parameterInfo">参数信息。</param>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.DefaultValue">
      <summary>获取参数的默认值。</summary>
      <returns>参数的默认值。</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.GetCustomAttributes``1">
      <summary>从参数中检索自定义特性的集合。</summary>
      <returns>参数中的自定义特性的集合。</returns>
      <typeparam name="TAttribute">自定义特性的类型。</typeparam>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.IsOptional">
      <summary>获取一个值，该值指示参数是否为可选。</summary>
      <returns>如果参数为可选，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.ParameterInfo">
      <summary>获取或设置参数信息。</summary>
      <returns>参数信息。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.ParameterName">
      <summary>获取参数名。</summary>
      <returns>参数名。</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.ParameterType">
      <summary>获取参数的类型。</summary>
      <returns>参数的类型。</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.ResponseMessageResultConverter">
      <summary>表示返回类型为 <see cref="T:System.Net.Http.HttpResponseMessage" /> 的操作的转换器。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ResponseMessageResultConverter.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ResponseMessageResultConverter" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ResponseMessageResultConverter.Convert(System.Web.Http.Controllers.HttpControllerContext,System.Object)">
      <summary>将 <see cref="T:System.Web.Http.Controllers.ResponseMessageResultConverter" /> 对象转换为其他对象。</summary>
      <returns>转换后的对象。</returns>
      <param name="controllerContext">控制器上下文。</param>
      <param name="actionResult">操作结果。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ServicesContainer">
      <summary>一种抽象类，用于为 ASP.NET Web API 所使用的服务提供容器。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ServicesContainer" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Add(System.Type,System.Object)">
      <summary> 将服务添加到给定服务类型的服务列表的末尾。</summary>
      <param name="serviceType">服务类型。</param>
      <param name="service">服务实例。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.AddRange(System.Type,System.Collections.Generic.IEnumerable{System.Object})">
      <summary> 将指定集合的服务添加到给定服务类型的服务列表的末尾。</summary>
      <param name="serviceType">服务类型。</param>
      <param name="services">要添加的服务。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Clear(System.Type)">
      <summary> 删除给定服务类型的所有服务实例。</summary>
      <param name="serviceType">要从服务列表中清除的服务类型。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ClearMultiple(System.Type)">
      <summary>删除多实例服务类型的所有实例。</summary>
      <param name="serviceType">要删除的服务类型。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ClearSingle(System.Type)">
      <summary>删除单实例服务类型。</summary>
      <param name="serviceType">要删除的服务类型。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Dispose">
      <summary>执行与释放或重置非托管资源关联的应用程序定义任务。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.FindIndex(System.Type,System.Predicate{System.Object})">
      <summary> 搜索与指定的谓词所定义的条件匹配的服务，并返回第一个匹配项的从零开始的索引。</summary>
      <returns>如果找到，则返回第一个匹配项的从零开始的索引；否则为 -1。</returns>
      <param name="serviceType">服务类型。</param>
      <param name="match">一个委托，该委托定义了要搜索的元素的条件。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.GetService(System.Type)">
      <summary>获取指定类型的服务实例。</summary>
      <param name="serviceType">服务类型。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.GetServiceInstances(System.Type)">
      <summary>获取指定类型的服务实例的可变列表。</summary>
      <returns>服务实例的可变列表。</returns>
      <param name="serviceType">服务类型。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.GetServices(System.Type)">
      <summary>获取指定类型的服务实例的集合。</summary>
      <returns>服务实例的集合。</returns>
      <param name="serviceType">服务类型。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Insert(System.Type,System.Int32,System.Object)">
      <summary> 在集合中的指定索引处插入一个服务。</summary>
      <param name="serviceType">服务类型。</param>
      <param name="index">从零开始的索引，应在此索引处插入服务。如果传递了 <see cref="F:System.Int32.MaxValue" />，请确保将该元素添加到末尾。</param>
      <param name="service">要插入的服务。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.InsertRange(System.Type,System.Int32,System.Collections.Generic.IEnumerable{System.Object})">
      <summary> 将集合中的元素插入到服务列表的指定索引处。</summary>
      <param name="serviceType">服务类型。</param>
      <param name="index">从零开始的索引，应在此索引处插入新元素。如果传递了 <see cref="F:System.Int32.MaxValue" />，请确保将这些元素添加到末尾。</param>
      <param name="services">要插入的服务的集合。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.IsSingleService(System.Type)">
      <summary> 确定服务类型应使用 GetService 还是 GetServices 进行提取。</summary>
      <returns>如果服务为单数，则为 true。</returns>
      <param name="serviceType">要查询的服务类型</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Remove(System.Type,System.Object)">
      <summary> 从给定服务类型的服务列表中删除给定服务的第一个匹配项。</summary>
      <returns>如果已成功删除该项，则为 true；否则为 false。</returns>
      <param name="serviceType">服务类型。</param>
      <param name="service">要删除的服务实例。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.RemoveAll(System.Type,System.Predicate{System.Object})">
      <summary> 删除与指定谓词所定义的条件匹配的所有元素。</summary>
      <returns>从列表中删除的元素的数目。</returns>
      <param name="serviceType">服务类型。</param>
      <param name="match">一个委托，该委托定义了要删除的元素的条件。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.RemoveAt(System.Type,System.Int32)">
      <summary> 删除指定索引处的服务。</summary>
      <param name="serviceType">服务类型。</param>
      <param name="index">待删除服务的从零开始的索引。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Replace(System.Type,System.Object)">
      <summary>将给定服务类型的所有现有服务替换为给定服务实例。这对于单数和复数服务均适用。</summary>
      <param name="serviceType">服务类型。</param>
      <param name="service">服务实例。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ReplaceMultiple(System.Type,System.Object)">
      <summary>将多实例服务的所有实例替换为一个新实例。</summary>
      <param name="serviceType">服务的类型。</param>
      <param name="service">服务实例，将替换此类型的当前服务。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ReplaceRange(System.Type,System.Collections.Generic.IEnumerable{System.Object})">
      <summary> 将给定服务类型的所有现有服务替换为给定服务实例。</summary>
      <param name="serviceType">服务类型。</param>
      <param name="services">服务实例。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ReplaceSingle(System.Type,System.Object)">
      <summary>替换指定类型的单实例服务。</summary>
      <param name="serviceType">服务类型。</param>
      <param name="service">服务实例。</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ResetCache(System.Type)">
      <summary>删除单个服务类型的缓存值。</summary>
      <param name="serviceType">服务类型。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ValueResultConverter`1">
      <summary> 一个转换器，用于根据返回任意 <paramref name="T" /> 值的操作创建响应。</summary>
      <typeparam name="T">操作的已声明返回类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Controllers.ValueResultConverter`1.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.ValueResultConverter`1" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ValueResultConverter`1.Convert(System.Web.Http.Controllers.HttpControllerContext,System.Object)">
      <summary>将具有任意返回类型 <paramref name="T" /> 的操作结果转换为 <see cref="T:System.Net.Http.HttpResponseMessage" /> 的实例。</summary>
      <returns>新建的 <see cref="T:System.Net.Http.HttpResponseMessage" /> 对象。</returns>
      <param name="controllerContext">操作控制器上下文。</param>
      <param name="actionResult">执行结果。</param>
    </member>
    <member name="T:System.Web.Http.Controllers.VoidResultConverter">
      <summary>表示一个用于根据不返回值的操作来创建响应的转换器。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.VoidResultConverter.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Controllers.VoidResultConverter" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.VoidResultConverter.Convert(System.Web.Http.Controllers.HttpControllerContext,System.Object)">
      <summary>转换根据不返回值的操作创建的响应。</summary>
      <returns>已转换的响应。</returns>
      <param name="controllerContext">控制器的上下文。</param>
      <param name="actionResult">操作的结果。</param>
    </member>
    <member name="T:System.Web.Http.Dependencies.IDependencyResolver">
      <summary>表示依赖关系注入容器。</summary>
    </member>
    <member name="M:System.Web.Http.Dependencies.IDependencyResolver.BeginScope">
      <summary> 开始解析范围。</summary>
      <returns>依赖范围。</returns>
    </member>
    <member name="T:System.Web.Http.Dependencies.IDependencyScope">
      <summary>表示依赖项范围的接口。</summary>
    </member>
    <member name="M:System.Web.Http.Dependencies.IDependencyScope.GetService(System.Type)">
      <summary>从范围中检索服务。</summary>
      <returns>检索到的服务。</returns>
      <param name="serviceType">要检索的服务。</param>
    </member>
    <member name="M:System.Web.Http.Dependencies.IDependencyScope.GetServices(System.Type)">
      <summary>从范围中检索服务集合。</summary>
      <returns>检索到的服务集合。</returns>
      <param name="serviceType">要检索的服务集合。</param>
    </member>
    <member name="T:System.Web.Http.Description.ApiDescription">
      <summary> 描述由相对 URI 路径和 HTTP 方法定义的 API。</summary>
    </member>
    <member name="M:System.Web.Http.Description.ApiDescription.#ctor">
      <summary> 初始化 <see cref="T:System.Web.Http.Description.ApiDescription" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.ActionDescriptor">
      <summary> 获取或设置将处理 API 的操作描述符。</summary>
      <returns> 操作描述符。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.Documentation">
      <summary> 获取或设置 API 文档。</summary>
      <returns> 文档。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.HttpMethod">
      <summary> 获取或设置 HTTP 方法。</summary>
      <returns> HTTP 方法。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.ID">
      <summary>获取 ID。该 ID 在 <see cref="T:System.Web.Http.HttpServer" /> 中是唯一的。</summary>
      <returns>ID。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.ParameterDescriptions">
      <summary> 获取参数说明。</summary>
      <returns>参数说明。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.RelativePath">
      <summary> 获取或设置相对路径。</summary>
      <returns> 相对路径。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.ResponseDescription">
      <summary>获取或设置响应说明。</summary>
      <returns>响应说明。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.Route">
      <summary> 获取或设置 API 的已注册路由。</summary>
      <returns> 路由。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.SupportedRequestBodyFormatters">
      <summary> 获取受支持的请求正文格式化程序。</summary>
      <returns>受支持的请求正文格式化程序。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.SupportedResponseFormatters">
      <summary> 获取受支持的响应格式化程序。</summary>
      <returns>受支持的响应格式化程序。</returns>
    </member>
    <member name="T:System.Web.Http.Description.ApiExplorer">
      <summary> 基于系统中可用的路由、控制器和操作，浏览服务的 URI 空间。</summary>
    </member>
    <member name="M:System.Web.Http.Description.ApiExplorer.#ctor(System.Web.Http.HttpConfiguration)">
      <summary> 初始化 <see cref="T:System.Web.Http.Description.ApiExplorer" /> 类的新实例。</summary>
      <param name="configuration">配置。</param>
    </member>
    <member name="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions">
      <summary> 获取 API 说明。这些说明将在首次访问时进行初始化。</summary>
    </member>
    <member name="P:System.Web.Http.Description.ApiExplorer.DocumentationProvider">
      <summary> 获取或设置文档提供程序。该提供程序将负责记录 API。</summary>
      <returns> 文档提供程序。</returns>
    </member>
    <member name="M:System.Web.Http.Description.ApiExplorer.GetHttpMethodsSupportedByAction(System.Web.Http.Routing.IHttpRoute,System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary> 获取该操作支持的 HttpMethods 的集合。初始化 <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" /> 时调用。</summary>
      <returns>该操作支持的 HttpMethods 的集合。</returns>
      <param name="route">路由。</param>
      <param name="actionDescriptor">操作描述符。</param>
    </member>
    <member name="M:System.Web.Http.Description.ApiExplorer.ShouldExploreAction(System.String,System.Web.Http.Controllers.HttpActionDescriptor,System.Web.Http.Routing.IHttpRoute)">
      <summary> 确定是否应考虑将此操作用于生成 <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" />。初始化 <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" /> 时调用。</summary>
      <returns>如果应考虑将此操作用于生成 <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" />，则为 true，否则为 false。</returns>
      <param name="actionVariableValue">来自路由的操作变量值。</param>
      <param name="actionDescriptor">操作描述符。</param>
      <param name="route">路由。</param>
    </member>
    <member name="M:System.Web.Http.Description.ApiExplorer.ShouldExploreController(System.String,System.Web.Http.Controllers.HttpControllerDescriptor,System.Web.Http.Routing.IHttpRoute)">
      <summary> 确定是否应考虑将此控制器用于生成 <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" />。初始化 <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" /> 时调用。</summary>
      <returns>如果应考虑将此控制器用于生成 <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" />，则为 true，否则为 false。</returns>
      <param name="controllerVariableValue">来自路由的控制器变量值。</param>
      <param name="controllerDescriptor">控制器描述符。</param>
      <param name="route">路由。</param>
    </member>
    <member name="T:System.Web.Http.Description.ApiExplorerSettingsAttribute">
      <summary> 此特性可用于控制器和操作以影响 <see cref="T:System.Web.Http.Description.ApiExplorer" /> 的行为。</summary>
    </member>
    <member name="M:System.Web.Http.Description.ApiExplorerSettingsAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Description.ApiExplorerSettingsAttribute" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.Description.ApiExplorerSettingsAttribute.IgnoreApi">
      <summary> 获取或设置一个值，该值指示是否从由 <see cref="T:System.Web.Http.Description.ApiExplorer" /> 生成的 <see cref="T:System.Web.Http.Description.ApiDescription" /> 实例中排除控制器或操作。</summary>
      <returns>如果应忽略控制器或操作，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Web.Http.Description.ApiParameterDescription">
      <summary> 描述由相对 URI 路径和 HTTP 方法定义的 API 的参数。</summary>
    </member>
    <member name="M:System.Web.Http.Description.ApiParameterDescription.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Description.ApiParameterDescription" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.Description.ApiParameterDescription.Documentation">
      <summary> 获取或设置文档。</summary>
      <returns> 文档。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiParameterDescription.Name">
      <summary> 获取或设置名称。</summary>
      <returns> 名称。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiParameterDescription.ParameterDescriptor">
      <summary> 获取或设置参数描述符。</summary>
      <returns> 参数描述符。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiParameterDescription.Source">
      <summary> 获取或设置参数的源。它可能来自请求 URI、请求正文或其他地方。</summary>
      <returns> 源。 </returns>
    </member>
    <member name="T:System.Web.Http.Description.ApiParameterSource">
      <summary> 描述参数的来源。</summary>
    </member>
    <member name="F:System.Web.Http.Description.ApiParameterSource.FromBody">
      <summary>参数来自正文。</summary>
    </member>
    <member name="F:System.Web.Http.Description.ApiParameterSource.FromUri">
      <summary>参数来自 Uri。</summary>
    </member>
    <member name="F:System.Web.Http.Description.ApiParameterSource.Unknown">
      <summary>位置未知。</summary>
    </member>
    <member name="T:System.Web.Http.Description.IApiExplorer">
      <summary> 定义用于获取 <see cref="T:System.Web.Http.Description.ApiDescription" /> 的集合的接口。</summary>
    </member>
    <member name="P:System.Web.Http.Description.IApiExplorer.ApiDescriptions">
      <summary> 获取 API 说明。 </summary>
    </member>
    <member name="T:System.Web.Http.Description.IDocumentationProvider">
      <summary> 定义负责记录服务的提供程序。</summary>
    </member>
    <member name="M:System.Web.Http.Description.IDocumentationProvider.GetDocumentation(System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary> 基于 <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> 获取文档。</summary>
      <returns>控制器的文档。</returns>
      <param name="actionDescriptor">操作描述符。</param>
    </member>
    <member name="M:System.Web.Http.Description.IDocumentationProvider.GetDocumentation(System.Web.Http.Controllers.HttpControllerDescriptor)"></member>
    <member name="M:System.Web.Http.Description.IDocumentationProvider.GetDocumentation(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary> 基于 <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> 获取文档。</summary>
      <returns>控制器的文档。</returns>
      <param name="parameterDescriptor">参数描述符。</param>
    </member>
    <member name="M:System.Web.Http.Description.IDocumentationProvider.GetResponseDocumentation(System.Web.Http.Controllers.HttpActionDescriptor)"></member>
    <member name="T:System.Web.Http.Description.ResponseDescription">
      <summary>描述 API 响应。</summary>
    </member>
    <member name="M:System.Web.Http.Description.ResponseDescription.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Description.ResponseDescription" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.Description.ResponseDescription.DeclaredType">
      <summary>获取或设置声明的响应类型。</summary>
      <returns>声明的响应类型。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ResponseDescription.Documentation">
      <summary>获取或设置响应文档。</summary>
      <returns>响应文档。</returns>
    </member>
    <member name="P:System.Web.Http.Description.ResponseDescription.ResponseType">
      <summary>获取或设置实际响应类型。</summary>
      <returns>实际响应类型。</returns>
    </member>
    <member name="T:System.Web.Http.Description.ResponseTypeAttribute">
      <summary>用于指定当声明的返回类型为 <see cref="T:System.Net.Http.HttpResponseMessage" /> 或 <see cref="T:System.Web.Http.IHttpActionResult" /> 时操作返回的实体类型。<see cref="T:System.Web.Http.Description.ApiExplorer" /> 在生成 <see cref="T:System.Web.Http.Description.ApiDescription" /> 时将读取 <see cref="P:System.Web.Http.Description.ResponseTypeAttribute.ResponseType" />。</summary>
    </member>
    <member name="M:System.Web.Http.Description.ResponseTypeAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Web.Http.Description.ResponseTypeAttribute" /> 类的新实例。</summary>
      <param name="responseType">响应类型。</param>
    </member>
    <member name="P:System.Web.Http.Description.ResponseTypeAttribute.ResponseType">
      <summary>获取响应类型。</summary>
    </member>
    <member name="T:System.Web.Http.Dispatcher.DefaultAssembliesResolver">
      <summary> 提供 <see cref="T:System.Web.Http.Dispatcher.IAssembliesResolver" /> 的实现，而无需外部依赖项。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultAssembliesResolver.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Dispatcher.DefaultAssembliesResolver" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultAssembliesResolver.GetAssemblies">
      <summary> 返回可用于应用程序的程序集的列表。</summary>
      <returns>程序集的 &lt;see cref="T:System.Collections.ObjectModel.Collection`1" /&gt;。</returns>
    </member>
    <member name="T:System.Web.Http.Dispatcher.DefaultHttpControllerActivator">
      <summary>表示 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerActivator" /> 的默认实现。可以通过 <see cref="T:System.Web.Http.Services.DependencyResolver" /> 注册不同的实现。我们已针对每个 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 实例具有一个 <see cref="T:System.Web.Http.Controllers.ApiControllerActionInvoker" /> 实例的情况进行优化，但也支持一个 <see cref="T:System.Web.Http.Controllers.ApiControllerActionInvoker" /> 具有多个 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 实例的情况。对于后一种情况，查找会略慢一些，因为查找需要遍历 <see cref="P:HttpControllerDescriptor.Properties" /> 目录。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerActivator.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Dispatcher.DefaultHttpControllerActivator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerActivator.Create(System.Net.Http.HttpRequestMessage,System.Web.Http.Controllers.HttpControllerDescriptor,System.Type)">
      <summary> 使用给定 <paramref name="request" /> 创建 <paramref name="controllerType" /> 所指定的 <see cref="T:System.Web.Http.Controllers.IHttpController" />。</summary>
      <returns>类型 <paramref name="controllerType" /> 的实例。</returns>
      <param name="request">请求消息。</param>
      <param name="controllerDescriptor">控制器描述符。</param>
      <param name="controllerType">控制器的类型。</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.DefaultHttpControllerSelector">
      <summary>表示一个默认 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerSelector" /> 实例，该实例用于根据 <see cref="T:System.Net.Http.HttpRequestMessage" /> 选择 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" />。可以通过 <see cref="P:System.Web.Http.HttpConfiguration.Services" /> 注册不同的实现。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerSelector.#ctor(System.Web.Http.HttpConfiguration)">
      <summary> 初始化 <see cref="T:System.Web.Http.Dispatcher.DefaultHttpControllerSelector" /> 类的新实例。</summary>
      <param name="configuration">配置。</param>
    </member>
    <member name="F:System.Web.Http.Dispatcher.DefaultHttpControllerSelector.ControllerSuffix">
      <summary>指定控制器名称中的后缀字符串。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerSelector.GetControllerMapping">
      <summary>返回选择器可以选择的所有 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 的映射，该映射使用控制器字符串作为键。</summary>
      <returns>选择器可以选择的所有 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 的映射或 null（如果选择器没有 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 的已经过良好定义的映射）。</returns>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerSelector.GetControllerName(System.Net.Http.HttpRequestMessage)">
      <summary>获取指定 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的控制器的名称。</summary>
      <returns>指定 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的控制器的名称。</returns>
      <param name="request">HTTP 请求消息。</param>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerSelector.SelectController(System.Net.Http.HttpRequestMessage)">
      <summary>为给定 <see cref="T:System.Net.Http.HttpRequestMessage" /> 选择 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" />。</summary>
      <returns>给定 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 实例。</returns>
      <param name="request">HTTP 请求消息。</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver">
      <summary> 提供不带有任何外部依赖项的 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerTypeResolver" /> 实现。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver.#ctor(System.Predicate{System.Type})">
      <summary>使用用于筛选控制器类型的谓词初始化新的 <see cref="T:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver" /> 实例。</summary>
      <param name="predicate">谓词。</param>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver.GetControllerTypes(System.Web.Http.Dispatcher.IAssembliesResolver)">
      <summary> 返回可用于应用程序的控制器的列表。</summary>
      <returns>控制器的 &lt;see cref="T:System.Collections.Generic.ICollection`1" /&gt;。</returns>
      <param name="assembliesResolver">程序集解析程序。</param>
    </member>
    <member name="P:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver.IsControllerTypePredicate">
      <summary>获取一个值，该值指示解析程序类型是否是一个控制器类型谓词。</summary>
      <returns>如果解析程序类型是一个控制器类型谓词，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Web.Http.Dispatcher.HttpControllerDispatcher">
      <summary>将传入的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 调度到 <see cref="T:System.Web.Http.Controllers.IHttpController" /> 实现以进行处理。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.HttpControllerDispatcher.#ctor(System.Web.Http.HttpConfiguration)">
      <summary>使用指定配置初始化 <see cref="T:System.Web.Http.Dispatcher.HttpControllerDispatcher" /> 类的新实例。</summary>
      <param name="configuration">Http 配置。</param>
    </member>
    <member name="P:System.Web.Http.Dispatcher.HttpControllerDispatcher.Configuration">
      <summary>获取 HTTP 配置。</summary>
      <returns>HTTP 配置。</returns>
    </member>
    <member name="M:System.Web.Http.Dispatcher.HttpControllerDispatcher.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>将传入的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 调度到 <see cref="T:System.Web.Http.Controllers.IHttpController" />。</summary>
      <returns>表示正在进行的操作的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="request">要调度的请求</param>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.HttpRoutingDispatcher">
      <summary> 此类是默认终结点消息处理程序，用于检查匹配的路由的 <see cref="T:System.Web.Http.Routing.IHttpRoute" />，并选择要调用哪个消息处理程序。如果 <see cref="P:System.Web.Http.Routing.IHttpRoute.Handler" /> 为 null，则将委托给 <see cref="T:System.Web.Http.Dispatcher.HttpControllerDispatcher" />。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.HttpRoutingDispatcher.#ctor(System.Web.Http.HttpConfiguration)">
      <summary> 将所提供的 <see cref="T:System.Web.Http.HttpConfiguration" /> 和 <see cref="T:System.Web.Http.Dispatcher.HttpControllerDispatcher" /> 用作默认处理程序，初始化 <see cref="T:System.Web.Http.Dispatcher.HttpRoutingDispatcher" /> 类的新实例。</summary>
      <param name="configuration">服务器配置。</param>
    </member>
    <member name="M:System.Web.Http.Dispatcher.HttpRoutingDispatcher.#ctor(System.Web.Http.HttpConfiguration,System.Net.Http.HttpMessageHandler)">
      <summary> 使用所提供的 <see cref="T:System.Web.Http.HttpConfiguration" /> 和 <see cref="T:System.Net.Http.HttpMessageHandler" /> 初始化 <see cref="T:System.Web.Http.Dispatcher.HttpRoutingDispatcher" /> 类的新实例。</summary>
      <param name="configuration">服务器配置。</param>
      <param name="defaultHandler">
        <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 没有 <see cref="P:System.Web.Http.Routing.IHttpRoute.Handler" /> 时要使用的默认处理程序。</param>
    </member>
    <member name="M:System.Web.Http.Dispatcher.HttpRoutingDispatcher.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>以异步操作的形式发送 HTTP 请求。</summary>
      <returns>表示异步操作的任务对象。</returns>
      <param name="request">要发送的 HTTP 请求消息。</param>
      <param name="cancellationToken">用于取消操作的取消标记。</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.IAssembliesResolver">
      <summary>提供抽象以管理应用程序的程序集。可以通过 <see cref="T:System.Web.Http.Services.DependencyResolver" /> 注册不同的实现。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.IAssembliesResolver.GetAssemblies">
      <summary> 返回可用于应用程序的程序集的列表。</summary>
      <returns>程序集的 &lt;see cref="T:System.Collections.Generic.ICollection`1" /&gt;。</returns>
    </member>
    <member name="T:System.Web.Http.Dispatcher.IHttpControllerActivator">
      <summary>定义 <see cref="T:System.Web.Http.Dispatcher.IHttpControllerActivator" /> 所需的方法。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.IHttpControllerActivator.Create(System.Net.Http.HttpRequestMessage,System.Web.Http.Controllers.HttpControllerDescriptor,System.Type)">
      <summary>创建一个 <see cref="T:System.Web.Http.Controllers.IHttpController" /> 对象。</summary>
      <returns>
        <see cref="T:System.Web.Http.Controllers.IHttpController" /> 对象。</returns>
      <param name="request">消息请求。</param>
      <param name="controllerDescriptor">HTTP 控制器描述符。</param>
      <param name="controllerType">控制器的类型。</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.IHttpControllerSelector">
      <summary> 定义 <see cref="T:System.Web.Http.Controllers.IHttpController" /> 工厂所需的方法。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.IHttpControllerSelector.GetControllerMapping">
      <summary>返回选择器可以选择的所有 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 的映射，该映射使用控制器字符串作为键。此方法主要由 <see cref="T:System.Web.Http.Description.IApiExplorer" /> 调用，以发现系统中的所有可能的控制器。</summary>
      <returns>选择器可以选择的所有 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 的映射或 null（如果选择器没有 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 的已经过良好定义的映射）。</returns>
    </member>
    <member name="M:System.Web.Http.Dispatcher.IHttpControllerSelector.SelectController(System.Net.Http.HttpRequestMessage)">
      <summary> 为给定 <see cref="T:System.Net.Http.HttpRequestMessage" /> 选择 <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" />。</summary>
      <returns>
        <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> 实例。</returns>
      <param name="request">请求消息。</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.IHttpControllerTypeResolver">
      <summary> 提供抽象以管理应用程序的控制器类型。可以通过 DependencyResolver 注册不同的实现。</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.IHttpControllerTypeResolver.GetControllerTypes(System.Web.Http.Dispatcher.IAssembliesResolver)">
      <summary> 返回可用于应用程序的控制器的列表。</summary>
      <returns>控制器的 &lt;see cref="T:System.Collections.Generic.ICollection`1" /&gt;。</returns>
      <param name="assembliesResolver">失败的程序集的解析程序。</param>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionCatchBlocks">
      <summary>提供在此程序集中使用的 catch 块。</summary>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpBatchHandler">
      <summary>获取 System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpBatchHandler.SendAsync 中的 catch 块。</summary>
      <returns>System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpBatchHandler.SendAsync 中的 catch 块。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpControllerDispatcher">
      <summary>获取 System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpControllerDispatcher.SendAsync 中的 catch 块。</summary>
      <returns>System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpControllerDispatcher.SendAsync 中的 catch 块。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpServer">
      <summary>获取 System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpServer.SendAsync 中的 catch 块。</summary>
      <returns>System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.HttpServer.SendAsync 中的 catch 块。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.IExceptionFilter">
      <summary>获取在使用 <see cref="P:System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.IExceptionFilter" /> 时 System.Web.Http.ApiController.ExecuteAsync 中的 catch 块。</summary>
      <returns>在使用 <see cref="P:System.Web.Http.ExceptionHandling.ExceptionCatchBlocks.IExceptionFilter" /> 时 System.Web.Http.ApiController.ExecuteAsync 中的 catch 块。</returns>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionContext">
      <summary>表示一个异常以及在捕获到该异常时与之关联的上下文数据。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionContext.#ctor(System.Exception,System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock)">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionContext" /> 类的新实例。</summary>
      <param name="exception">捕获到的异常。</param>
      <param name="catchBlock">捕获到了异常的 catch 块。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionContext.#ctor(System.Exception,System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock,System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionContext" /> 类的新实例。</summary>
      <param name="exception">捕获到的异常。</param>
      <param name="catchBlock">捕获到了异常的 catch 块。</param>
      <param name="request">在捕获到异常时处理的请求。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionContext.#ctor(System.Exception,System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock,System.Net.Http.HttpRequestMessage,System.Net.Http.HttpResponseMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionContext" /> 类的新实例。</summary>
      <param name="exception">捕获到的异常。</param>
      <param name="catchBlock">捕获到了异常的 catch 块。</param>
      <param name="request">在捕获到异常时处理的请求。</param>
      <param name="response">在捕获到异常时返回的响应。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionContext.#ctor(System.Exception,System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock,System.Web.Http.Controllers.HttpActionContext)">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionContext" /> 类的新实例。</summary>
      <param name="exception">捕获到的异常。</param>
      <param name="catchBlock">捕获到了异常的 catch 块。</param>
      <param name="actionContext">发生了异常的操作上下文。</param>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContext.ActionContext">
      <summary>获取发生了异常的操作上下文（如果可用）。</summary>
      <returns>发生了异常的操作上下文（如果可用）。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContext.CatchBlock">
      <summary>获取在其中捕获到异常的 catch 块。</summary>
      <returns>在其中捕获到异常的 catch 块。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContext.ControllerContext">
      <summary>获取发生了异常的控制器上下文（如果可用）。</summary>
      <returns>发生了异常的控制器上下文（如果可用）。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContext.Exception">
      <summary>获取捕获到的异常。</summary>
      <returns>捕获到的异常。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContext.Request">
      <summary>获取在捕获到异常时处理的请求。</summary>
      <returns>在捕获到异常时处理的请求。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContext.RequestContext">
      <summary>获取发生了异常的请求上下文。</summary>
      <returns>发生了异常的请求上下文。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContext.Response">
      <summary>获取在捕获到异常时发送的响应。</summary>
      <returns>在捕获到异常时发送的响应。</returns>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock">
      <summary>表示异常上下文的 catch 块的位置。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock.#ctor(System.String,System.Boolean,System.Boolean)">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock" /> 类的新实例。</summary>
      <param name="name">捕获到异常的 catch 块的标签。</param>
      <param name="isTopLevel">指示捕获到异常的 catch 块是否是主机之前的最后一个块的值。</param>
      <param name="callsHandler">指示 catch 块中的异常是否可以在记录之后进行处理的值。</param>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock.CallsHandler">
      <summary>获取一个指示 catch 块中的异常是否可以在记录之后进行处理的值。</summary>
      <returns>指示 catch 块中的异常是否可以在记录之后进行处理的值。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock.IsTopLevel">
      <summary>获取指示捕获到异常的 catch 块是否是主机之前的最后一个块的值。</summary>
      <returns>指示捕获到异常的 catch 块是否是主机之前的最后一个块的值。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock.Name">
      <summary>获取在其中捕获到异常的 catch 块的标签。</summary>
      <returns>在其中捕获到异常的 catch 块的标签。</returns>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionContextCatchBlock.ToString">
      <returns>返回 <see cref="T:System.String" />。</returns>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionHandler">
      <summary>表示未处理的异常处理程序。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionHandler.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionHandler" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionHandler.Handle(System.Web.Http.ExceptionHandling.ExceptionHandlerContext)">
      <summary>在派生类中重写时，将同步处理异常。</summary>
      <param name="context">异常处理程序上下文。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionHandler.HandleAsync(System.Web.Http.ExceptionHandling.ExceptionHandlerContext,System.Threading.CancellationToken)">
      <summary>在派生类中重写时，将异步处理异常。</summary>
      <returns>表示异步异常处理操作的任务。</returns>
      <param name="context">异常处理程序上下文。</param>
      <param name="cancellationToken">要监视的取消请求标记。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionHandler.ShouldHandle(System.Web.Http.ExceptionHandling.ExceptionHandlerContext)">
      <summary>确定是否应处理异常。</summary>
      <returns>如果应处理异常，则为 true；否则为 false。</returns>
      <param name="context">异常处理程序上下文。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionHandler.System#Web#Http#ExceptionHandling#IExceptionHandler#HandleAsync(System.Web.Http.ExceptionHandling.ExceptionHandlerContext,System.Threading.CancellationToken)">
      <returns>返回 <see cref="T:System.Threading.Tasks.Task" />。</returns>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionHandlerContext">
      <summary>表示出现未处理的异常处理的上下文。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionHandlerContext.#ctor(System.Web.Http.ExceptionHandling.ExceptionContext)">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionHandlerContext" /> 类的新实例。</summary>
      <param name="exceptionContext">异常上下文。</param>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionHandlerContext.CatchBlock">
      <summary>获取在其中捕获到异常的 catch 块。</summary>
      <returns>在其中捕获到异常的 catch 块。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionHandlerContext.Exception">
      <summary>获取捕获到的异常。</summary>
      <returns>捕获到的异常。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionHandlerContext.ExceptionContext">
      <summary>获取提供异常和相关数据的异常上下文。</summary>
      <returns>提供异常和相关数据的异常上下文。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionHandlerContext.Request">
      <summary>获取在捕获到异常时处理的请求。</summary>
      <returns>在捕获到异常时处理的请求。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionHandlerContext.RequestContext">
      <summary>获取发生了异常的请求上下文。</summary>
      <returns>发生了异常的请求上下文。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionHandlerContext.Result">
      <summary>获取或设置在处理异常时提供响应消息的结果。</summary>
      <returns>在处理异常时提供响应消息的结果。</returns>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionHandlerExtensions">
      <summary>提供 <see cref="T:System.Web.Http.ExceptionHandling.IExceptionHandler" /> 的扩展方法。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionHandlerExtensions.HandleAsync(System.Web.Http.ExceptionHandling.IExceptionHandler,System.Web.Http.ExceptionHandling.ExceptionContext,System.Threading.CancellationToken)">
      <summary>调用异常处理程序并确定处理该异常的响应（如果有）。</summary>
      <returns>完成时包含要在处理异常时返回的响应消息的任务；当异常仍保留未处理状态时为 null。</returns>
      <param name="handler">未处理的异常处理程序。</param>
      <param name="context">异常上下文。</param>
      <param name="cancellationToken">要监视的取消请求标记。</param>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionLogger">
      <summary>表示未处理的异常记录器。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionLogger.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionLogger" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionLogger.Log(System.Web.Http.ExceptionHandling.ExceptionLoggerContext)">
      <summary>在派生类中重写时，将同步记录异常。</summary>
      <param name="context">异常记录器上下文。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionLogger.LogAsync(System.Web.Http.ExceptionHandling.ExceptionLoggerContext,System.Threading.CancellationToken)">
      <summary>在派生类中重写时，将异步记录异常。</summary>
      <returns>表示异步异常记录操作的任务。</returns>
      <param name="context">异常记录器上下文。</param>
      <param name="cancellationToken">要监视的取消请求标记。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionLogger.ShouldLog(System.Web.Http.ExceptionHandling.ExceptionLoggerContext)">
      <summary>确定是否应记录异常。</summary>
      <returns>如果应记录异常，则为 true；否则为 false。</returns>
      <param name="context">异常记录器上下文。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionLogger.System#Web#Http#ExceptionHandling#IExceptionLogger#LogAsync(System.Web.Http.ExceptionHandling.ExceptionLoggerContext,System.Threading.CancellationToken)">
      <returns>返回 <see cref="T:System.Threading.Tasks.Task" />。</returns>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionLoggerContext">
      <summary>表示在其中记录未处理异常的上下文。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionLoggerContext.#ctor(System.Web.Http.ExceptionHandling.ExceptionContext)">
      <summary>初始化 <see cref="T:System.Web.Http.ExceptionHandling.ExceptionLoggerContext" /> 类的新实例。</summary>
      <param name="exceptionContext">异常上下文。</param>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionLoggerContext.CallsHandler">
      <summary>获取或设置一个值，该值指示是否可以随后由 <see cref="T:System.Web.Http.ExceptionHandling.IExceptionHandler" /> 来处理异常以生成新的响应消息。</summary>
      <returns>指示是否可以随后由 <see cref="T:System.Web.Http.ExceptionHandling.IExceptionHandler" /> 来处理异常以生成新响应消息的值。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionLoggerContext.CatchBlock">
      <summary>获取在其中捕获到异常的 catch 块。</summary>
      <returns>在其中捕获到异常的 catch 块。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionLoggerContext.Exception">
      <summary>获取捕获到的异常。</summary>
      <returns>捕获到的异常。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionLoggerContext.ExceptionContext">
      <summary>获取提供异常和相关数据的异常上下文。</summary>
      <returns>提供异常和相关数据的异常上下文。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionLoggerContext.Request">
      <summary>获取在捕获到异常时处理的请求。</summary>
      <returns>在捕获到异常时处理的请求。</returns>
    </member>
    <member name="P:System.Web.Http.ExceptionHandling.ExceptionLoggerContext.RequestContext">
      <summary>获取发生了异常的请求上下文。</summary>
      <returns>发生了异常的请求上下文。</returns>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionLoggerExtensions">
      <summary>提供 <see cref="T:System.Web.Http.ExceptionHandling.IExceptionLogger" /> 的扩展方法。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionLoggerExtensions.LogAsync(System.Web.Http.ExceptionHandling.IExceptionLogger,System.Web.Http.ExceptionHandling.ExceptionContext,System.Threading.CancellationToken)">
      <summary>调用异常记录器。</summary>
      <returns>表示异步异常记录操作的任务。</returns>
      <param name="logger">未处理的异常记录器。</param>
      <param name="context">异常上下文。</param>
      <param name="cancellationToken">要监视的取消请求标记。</param>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.ExceptionServices">
      <summary>创建要从 catch 块调用记录和处理的异常服务。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionServices.GetHandler(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取用于调用已注册处理程序服务的异常处理程序（如果有），并确保异常不会意外地传播到主机。</summary>
      <returns>用于调用任何已注册处理程序的异常处理程序，并确保异常不会意外地传播到主机。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionServices.GetHandler(System.Web.Http.HttpConfiguration)">
      <summary>获取用于调用已注册处理程序服务的异常处理程序（如果有），并确保异常不会意外地传播到主机。</summary>
      <returns>用于调用任何已注册处理程序的异常处理程序，并确保异常不会意外地传播到主机。</returns>
      <param name="configuration">配置。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionServices.GetLogger(System.Web.Http.Controllers.ServicesContainer)">
      <summary>获取用于调用所有已注册记录器服务的异常记录器。</summary>
      <returns>复合记录器。</returns>
      <param name="services">服务容器。</param>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.ExceptionServices.GetLogger(System.Web.Http.HttpConfiguration)">
      <summary>获取用于调用所有已注册记录器服务的异常记录器。</summary>
      <returns>复合记录器。</returns>
      <param name="configuration">配置。</param>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.IExceptionHandler">
      <summary>定义未处理的异常处理程序。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.IExceptionHandler.HandleAsync(System.Web.Http.ExceptionHandling.ExceptionHandlerContext,System.Threading.CancellationToken)">
      <summary>处理未处理的异常，并允许其传播或者通过提供要返回的响应消息对其进行处理。</summary>
      <returns>表示异步异常处理操作的任务。</returns>
      <param name="context">异常处理程序上下文。</param>
      <param name="cancellationToken">要监视的取消请求标记。</param>
    </member>
    <member name="T:System.Web.Http.ExceptionHandling.IExceptionLogger">
      <summary>定义未处理的异常记录器。</summary>
    </member>
    <member name="M:System.Web.Http.ExceptionHandling.IExceptionLogger.LogAsync(System.Web.Http.ExceptionHandling.ExceptionLoggerContext,System.Threading.CancellationToken)">
      <summary>记录未处理的异常。</summary>
      <returns>表示异步异常记录操作的任务。</returns>
      <param name="context">异常记录器上下文。</param>
      <param name="cancellationToken">要监视的取消请求标记。</param>
    </member>
    <member name="T:System.Web.Http.Filters.ActionDescriptorFilterProvider">
      <summary>提供有关操作方法的信息，如操作方法的名称、控制器、参数、特性和筛选器。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ActionDescriptorFilterProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.ActionDescriptorFilterProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ActionDescriptorFilterProvider.GetFilters(System.Web.Http.HttpConfiguration,System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>返回与此操作方法关联的筛选器。</summary>
      <returns>与此操作方法关联的筛选器。</returns>
      <param name="configuration">配置。</param>
      <param name="actionDescriptor">操作描述符。</param>
    </member>
    <member name="T:System.Web.Http.Filters.ActionFilterAttribute">
      <summary>表示所有操作筛选器特性的基类。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ActionFilterAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.ActionFilterAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ActionFilterAttribute.OnActionExecuted(System.Web.Http.Filters.HttpActionExecutedContext)">
      <summary>在调用操作方法之后发生。</summary>
      <param name="actionExecutedContext">操作执行的上下文。</param>
    </member>
    <member name="M:System.Web.Http.Filters.ActionFilterAttribute.OnActionExecutedAsync(System.Web.Http.Filters.HttpActionExecutedContext,System.Threading.CancellationToken)"></member>
    <member name="M:System.Web.Http.Filters.ActionFilterAttribute.OnActionExecuting(System.Web.Http.Controllers.HttpActionContext)">
      <summary>在调用操作方法之前发生。</summary>
      <param name="actionContext">操作上下文。</param>
    </member>
    <member name="M:System.Web.Http.Filters.ActionFilterAttribute.OnActionExecutingAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)"></member>
    <member name="M:System.Web.Http.Filters.ActionFilterAttribute.System#Web#Http#Filters#IActionFilter#ExecuteActionFilterAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken,System.Func{System.Threading.Tasks.Task{System.Net.Http.HttpResponseMessage}})">
      <summary>异步执行筛选器操作。</summary>
      <returns>为此操作新建的任务。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="cancellationToken">为此任务分配的取消标记。</param>
      <param name="continuation">在调用操作方法之后，委托函数将继续。</param>
    </member>
    <member name="T:System.Web.Http.Filters.AuthorizationFilterAttribute">
      <summary>提供授权筛选器的详细信息。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.AuthorizationFilterAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.AuthorizationFilterAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.AuthorizationFilterAttribute.OnAuthorization(System.Web.Http.Controllers.HttpActionContext)">
      <summary>在过程请求授权时调用。</summary>
      <param name="actionContext">操作上下文，它封装有关使用 <see cref="T:System.Web.Http.Filters.AuthorizationFilterAttribute" /> 的信息。</param>
    </member>
    <member name="M:System.Web.Http.Filters.AuthorizationFilterAttribute.OnAuthorizationAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)"></member>
    <member name="M:System.Web.Http.Filters.AuthorizationFilterAttribute.System#Web#Http#Filters#IAuthorizationFilter#ExecuteAuthorizationFilterAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken,System.Func{System.Threading.Tasks.Task{System.Net.Http.HttpResponseMessage}})">
      <summary>在同步过程中执行授权筛选器。</summary>
      <returns>同步过程中的授权筛选器。</returns>
      <param name="actionContext">操作上下文，它封装有关使用 <see cref="T:System.Web.Http.Filters.AuthorizationFilterAttribute" /> 的信息。</param>
      <param name="cancellationToken">用于取消操作的取消标记。</param>
      <param name="continuation">操作的继续。</param>
    </member>
    <member name="T:System.Web.Http.Filters.ConfigurationFilterProvider">
      <summary>表示配置筛选器提供程序。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ConfigurationFilterProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.ConfigurationFilterProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ConfigurationFilterProvider.GetFilters(System.Web.Http.HttpConfiguration,System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>返回与此配置方法关联的筛选器。</summary>
      <returns>与此配置方法关联的筛选器。</returns>
      <param name="configuration">配置。</param>
      <param name="actionDescriptor">操作描述符。</param>
    </member>
    <member name="T:System.Web.Http.Filters.ExceptionFilterAttribute">
      <summary>表示异常筛选器的特性。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ExceptionFilterAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.ExceptionFilterAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ExceptionFilterAttribute.OnException(System.Web.Http.Filters.HttpActionExecutedContext)">
      <summary>引发异常事件。</summary>
      <param name="actionExecutedContext">操作的上下文。</param>
    </member>
    <member name="M:System.Web.Http.Filters.ExceptionFilterAttribute.OnExceptionAsync(System.Web.Http.Filters.HttpActionExecutedContext,System.Threading.CancellationToken)"></member>
    <member name="M:System.Web.Http.Filters.ExceptionFilterAttribute.System#Web#Http#Filters#IExceptionFilter#ExecuteExceptionFilterAsync(System.Web.Http.Filters.HttpActionExecutedContext,System.Threading.CancellationToken)">
      <summary>异步执行异常筛选器。</summary>
      <returns>执行的结果。</returns>
      <param name="actionExecutedContext">操作的上下文。</param>
      <param name="cancellationToken">取消上下文。</param>
    </member>
    <member name="T:System.Web.Http.Filters.FilterAttribute">
      <summary>表示操作-筛选器特性的基类。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.FilterAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.FilterAttribute" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.Filters.FilterAttribute.AllowMultiple">
      <summary>获取用于指示是否允许多个筛选器的值。</summary>
      <returns>如果允许多个筛选器，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Web.Http.Filters.FilterInfo">
      <summary>提供有关可用的操作筛选器的信息。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.FilterInfo.#ctor(System.Web.Http.Filters.IFilter,System.Web.Http.Filters.FilterScope)">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.FilterInfo" /> 类的新实例。</summary>
      <param name="instance">此类的实例。</param>
      <param name="scope">此类的范围。</param>
    </member>
    <member name="P:System.Web.Http.Filters.FilterInfo.Instance">
      <summary>获取或设置 <see cref="T:System.Web.Http.Filters.FilterInfo" /> 的实例。</summary>
      <returns>
        <see cref="T:System.Web.Http.Filters.FilterInfo" />。</returns>
    </member>
    <member name="P:System.Web.Http.Filters.FilterInfo.Scope">
      <summary>获取或设置 <see cref="T:System.Web.Http.Filters.FilterInfo" /> 的范围。</summary>
      <returns>FilterInfo 的范围。</returns>
    </member>
    <member name="T:System.Web.Http.Filters.FilterScope">
      <summary>定义值，这些值指定筛选器在同一筛选器类型和筛选器顺序内的运行顺序。</summary>
    </member>
    <member name="F:System.Web.Http.Filters.FilterScope.Action">
      <summary>在 Controller 之后指定一个顺序。 </summary>
    </member>
    <member name="F:System.Web.Http.Filters.FilterScope.Controller">
      <summary>在 Action 之前和 Global 之后指定一个顺序。</summary>
    </member>
    <member name="F:System.Web.Http.Filters.FilterScope.Global">
      <summary>在 Controller 之前指定一个操作。</summary>
    </member>
    <member name="T:System.Web.Http.Filters.HttpActionExecutedContext">
      <summary>表示 HTTP 执行的上下文的操作。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpActionExecutedContext.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.HttpActionExecutedContext" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpActionExecutedContext.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Exception)">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.HttpActionExecutedContext" /> 类的新实例。</summary>
      <param name="actionContext">操作上下文。</param>
      <param name="exception">异常。</param>
    </member>
    <member name="P:System.Web.Http.Filters.HttpActionExecutedContext.ActionContext">
      <summary>获取或设置 HTTP 操作上下文。</summary>
      <returns>HTTP 操作上下文。</returns>
    </member>
    <member name="P:System.Web.Http.Filters.HttpActionExecutedContext.Exception">
      <summary>获取或设置在执行期间引发的异常。</summary>
      <returns>在执行期间引发的异常。</returns>
    </member>
    <member name="P:System.Web.Http.Filters.HttpActionExecutedContext.Request">
      <summary>获取上下文的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 对象。</summary>
      <returns>上下文的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 对象。</returns>
    </member>
    <member name="P:System.Web.Http.Filters.HttpActionExecutedContext.Response">
      <summary>获取或设置上下文的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</summary>
      <returns>上下文的 <see cref="T:System.Net.Http.HttpResponseMessage" />。</returns>
    </member>
    <member name="T:System.Web.Http.Filters.HttpAuthenticationChallengeContext">
      <summary>表示身份验证质询上下文，其中包含用于执行身份验证质询的信息。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpAuthenticationChallengeContext.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.IHttpActionResult)">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.HttpAuthenticationChallengeContext" /> 类的新实例。</summary>
      <param name="actionContext">操作上下文。</param>
      <param name="result">当前操作结果。</param>
    </member>
    <member name="P:System.Web.Http.Filters.HttpAuthenticationChallengeContext.ActionContext">
      <summary>获取操作上下文。</summary>
    </member>
    <member name="P:System.Web.Http.Filters.HttpAuthenticationChallengeContext.Request">
      <summary>获取请求消息。</summary>
    </member>
    <member name="P:System.Web.Http.Filters.HttpAuthenticationChallengeContext.Result">
      <summary>获取或设置要执行的操作结果。</summary>
    </member>
    <member name="T:System.Web.Http.Filters.HttpAuthenticationContext">
      <summary>表示身份验证上下文，其中包含用于执行身份验证的信息。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpAuthenticationContext.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Security.Principal.IPrincipal)">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.HttpAuthenticationContext" /> 类的新实例。</summary>
      <param name="actionContext">操作上下文。</param>
      <param name="principal">当前主体。</param>
    </member>
    <member name="P:System.Web.Http.Filters.HttpAuthenticationContext.ActionContext">
      <summary>获取操作上下文。</summary>
      <returns>操作上下文。</returns>
    </member>
    <member name="P:System.Web.Http.Filters.HttpAuthenticationContext.ErrorResult">
      <summary>获取或设置将会生成错误响应的操作结果（如果身份验证失败；否则为 null）。</summary>
      <returns>将会生成错误响应的操作结果。</returns>
    </member>
    <member name="P:System.Web.Http.Filters.HttpAuthenticationContext.Principal">
      <summary>获取或设置已进行身份验证的主体。</summary>
      <returns>已进行身份验证的主体。</returns>
    </member>
    <member name="P:System.Web.Http.Filters.HttpAuthenticationContext.Request">
      <summary>获取请求消息。</summary>
      <returns>请求消息。</returns>
    </member>
    <member name="T:System.Web.Http.Filters.HttpFilterCollection">
      <summary>表示 HTTP 筛选器的集合。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Filters.HttpFilterCollection" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.Add(System.Web.Http.Filters.IFilter)">
      <summary>在集合的末尾添加一个项。</summary>
      <param name="filter">要添加到集合中的项。</param>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.AddRange(System.Collections.Generic.IEnumerable{System.Web.Http.Filters.IFilter})"></member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.Clear">
      <summary>移除集合中的所有项。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.Contains(System.Web.Http.Filters.IFilter)">
      <summary>确定集合是否包含指定的项。</summary>
      <returns>如果集合包含指定项，则为 true；否则为 false。</returns>
      <param name="filter">要检查的项。</param>
    </member>
    <member name="P:System.Web.Http.Filters.HttpFilterCollection.Count">
      <summary>获取集合中的元素数。</summary>
      <returns>集合中的元素数。</returns>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.GetEnumerator">
      <summary>获取循环访问集合的枚举器。</summary>
      <returns>一个可用于循环访问集合的枚举器对象。</returns>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.Remove(System.Web.Http.Filters.IFilter)">
      <summary>从集合中删除指定项。</summary>
      <param name="filter">要在集合中删除的项。</param>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>获取循环访问集合的枚举器。</summary>
      <returns>一个可用于循环访问集合的枚举器对象。</returns>
    </member>
    <member name="T:System.Web.Http.Filters.IActionFilter">
      <summary>定义操作筛选器中使用的方法。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.IActionFilter.ExecuteActionFilterAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken,System.Func{System.Threading.Tasks.Task{System.Net.Http.HttpResponseMessage}})">
      <summary>异步执行筛选器操作。</summary>
      <returns>为此操作新建的任务。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="cancellationToken">为此任务分配的取消标记。</param>
      <param name="continuation">在调用操作方法之后，委托函数将继续。</param>
    </member>
    <member name="T:System.Web.Http.Filters.IAuthenticationFilter">
      <summary>定义一个用于执行身份验证的筛选器。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.IAuthenticationFilter.AuthenticateAsync(System.Web.Http.Filters.HttpAuthenticationContext,System.Threading.CancellationToken)">
      <summary>对请求进行身份验证。</summary>
      <returns>一个将执行身份验证的“任务”。</returns>
      <param name="context">身份验证上下文。</param>
      <param name="cancellationToken">要监视的取消请求标记。</param>
    </member>
    <member name="M:System.Web.Http.Filters.IAuthenticationFilter.ChallengeAsync(System.Web.Http.Filters.HttpAuthenticationChallengeContext,System.Threading.CancellationToken)"></member>
    <member name="T:System.Web.Http.Filters.IAuthorizationFilter">
      <summary>定义授权筛选器所需的方法。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.IAuthorizationFilter.ExecuteAuthorizationFilterAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken,System.Func{System.Threading.Tasks.Task{System.Net.Http.HttpResponseMessage}})">
      <summary>执行要同步的授权筛选器。</summary>
      <returns>要同步的授权筛选器。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="cancellationToken">与筛选器关联的取消标记。</param>
      <param name="continuation">继续。</param>
    </member>
    <member name="T:System.Web.Http.Filters.IExceptionFilter">
      <summary>定义异常筛选器所需的方法。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.IExceptionFilter.ExecuteExceptionFilterAsync(System.Web.Http.Filters.HttpActionExecutedContext,System.Threading.CancellationToken)">
      <summary>执行异步异常筛选器。</summary>
      <returns>异步异常筛选器。</returns>
      <param name="actionExecutedContext">操作执行的上下文。</param>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="T:System.Web.Http.Filters.IFilter">
      <summary>定义筛选器中使用的方法。</summary>
    </member>
    <member name="P:System.Web.Http.Filters.IFilter.AllowMultiple">
      <summary>获取或设置一个值，该值指示是否可以为单个程序元素指定多个已指示特性的实例。</summary>
      <returns>如果可以指定多个实例，则为 true；否则为 false。默认值为 false。</returns>
    </member>
    <member name="T:System.Web.Http.Filters.IFilterProvider">
      <summary>提供筛选器信息。</summary>
    </member>
    <member name="M:System.Web.Http.Filters.IFilterProvider.GetFilters(System.Web.Http.HttpConfiguration,System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>返回筛选器的枚举。</summary>
      <returns>筛选器的枚举。</returns>
      <param name="configuration">HTTP 配置。</param>
      <param name="actionDescriptor">操作描述符。</param>
    </member>
    <member name="T:System.Web.Http.Filters.IOverrideFilter"></member>
    <member name="P:System.Web.Http.Filters.IOverrideFilter.FiltersToOverride"></member>
    <member name="T:System.Web.Http.Hosting.HttpPropertyKeys">
      <summary> 为 <see cref="P:System.Net.Http.HttpRequestMessage.Properties" /> 中存储的属性提供公共键</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.ClientCertificateKey">
      <summary>为此请求的客户端证书提供一个键。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.DependencyScope">
      <summary>为与此请求关联的 <see cref="T:System.Web.Http.Dependencies.IDependencyScope" /> 提供一个键。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.DisposableRequestResourcesKey">
      <summary>为处理请求后应释放的资源的集合提供一个键。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.HttpActionDescriptorKey">
      <summary> 为与此请求关联的 <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> 提供一个键。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.HttpConfigurationKey">
      <summary>为与此请求关联的 <see cref="T:System.Web.Http.HttpConfiguration" /> 提供一个键。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.HttpRouteDataKey">
      <summary>为与此请求关联的 <see cref="T:System.Web.Http.Routing.IHttpRouteData" /> 提供一个键。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.IncludeErrorDetailKey">
      <summary>提供一个键，该键指示是否要将错误详细信息包含在此 HTTP 请求的响应中。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.IsBatchRequest">
      <summary> 提供一个用于指示请求是否为批请求的键。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.IsLocalKey">
      <summary>提供一个指示请求是否源自本地地址的键。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.NoRouteMatched">
      <summary> 提供一个用于指示请求是否无法匹配路由的键。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.RequestContextKey">
      <summary>为此请求的 <see cref="T:System.Web.Http.Controllers.HttpRequestContext" /> 提供一个键。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.RequestCorrelationKey">
      <summary>为 <see cref="T:System.Net.Http.Properties" /> 中存储的 <see cref="T:System.Guid" /> 提供一个键。这是该请求的相关 ID。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.RequestQueryNameValuePairsKey">
      <summary>为 <see cref="T:System.Net.Http.Properties" /> 中存储的已分析查询字符串提供一个键。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.RetrieveClientCertificateDelegateKey">
      <summary>为委托提供一个键，该委托可以检索此请求的客户端证书。</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.SynchronizationContextKey">
      <summary>为 Properties() 中存储的当前 <see cref="T:System.Threading.SynchronizationContext" /> 提供一个键。如果 Current() 为 null，则不存储上下文。</summary>
    </member>
    <member name="T:System.Web.Http.Hosting.IHostBufferPolicySelector">
      <summary> 接口，用于控制主机中缓冲请求和响应的使用。如果主机为缓冲请求和/或响应提供支持，则它可以使用此接口来确定使用缓冲的时间策略。</summary>
    </member>
    <member name="M:System.Web.Http.Hosting.IHostBufferPolicySelector.UseBufferedInputStream(System.Object)">
      <summary>确定主机是否应缓冲 <see cref="T:System.Net.Http.HttpRequestMessage" /> 实体正文。</summary>
      <returns>如果应使用缓冲，则为 true；否则，应使用已流式处理的请求。</returns>
      <param name="hostContext">主机上下文。</param>
    </member>
    <member name="M:System.Web.Http.Hosting.IHostBufferPolicySelector.UseBufferedOutputStream(System.Net.Http.HttpResponseMessage)">
      <summary>确定主机是否应缓冲 <see cref="T.System.Net.Http.HttpResponseMessage" /> 实体正文。</summary>
      <returns>如果应使用缓冲，则为 true；否则，应使用已流式处理的响应。</returns>
      <param name="response">HTTP 响应消息。</param>
    </member>
    <member name="T:System.Web.Http.Hosting.SuppressHostPrincipalMessageHandler">
      <summary>表示可取消主机身份验证结果的消息处理程序。</summary>
    </member>
    <member name="M:System.Web.Http.Hosting.SuppressHostPrincipalMessageHandler.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Hosting.SuppressHostPrincipalMessageHandler" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Hosting.SuppressHostPrincipalMessageHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>异步发送请求消息。</summary>
      <returns>用于完成异步操作的任务。</returns>
      <param name="request">要发送的请求消息。</param>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="T:System.Web.Http.Metadata.ModelMetadata">
      <summary>表示 ModelMetadata 的元数据类。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadata.#ctor(System.Web.Http.Metadata.ModelMetadataProvider,System.Type,System.Func{System.Object},System.Type,System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.ModelMetadata" /> 类的新实例。</summary>
      <param name="provider">提供程序。</param>
      <param name="containerType">容器的类型。</param>
      <param name="modelAccessor">模型访问器。</param>
      <param name="modelType">模型的类型。</param>
      <param name="propertyName">属性的名称。</param>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.AdditionalValues">
      <summary>获取包含有关模型的其他元数据的字典。</summary>
      <returns>包含有关模型的其他元数据的字典。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.ContainerType">
      <summary>获取或设置模型的容器的类型。</summary>
      <returns>模型的容器的类型。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.ConvertEmptyStringToNull">
      <summary>获取或设置一个值，该值指示在窗体中回发的空字符串是否应转换为 null。</summary>
      <returns>如果在窗体中回发的空字符串应转换为 null，则为 true；否则为 false。默认值为 true。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.Description">
      <summary>获取或设置模型的说明。</summary>
      <returns>模型的说明。默认值为 null。</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadata.GetDisplayName">
      <summary>获取模型的显示名称。</summary>
      <returns>模型的显示名称。</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadata.GetValidators(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>获取模型的验证程序的列表。</summary>
      <returns>模型的验证程序的列表。</returns>
      <param name="validatorProviders">模型的验证程序提供程序。</param>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.IsComplexType">
      <summary>获取或设置一个值，该值指示模型是否为复杂类型。</summary>
      <returns>一个值，该值指示是否将模型视为复杂模型。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.IsNullableValueType">
      <summary>获取一个值，该值指示类型是否可为 null。</summary>
      <returns>如果该类型可为 null，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.IsReadOnly">
      <summary>获取或设置一个值，该值指示模型是否为只读。</summary>
      <returns>如果该模型为只读，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.Model">
      <summary>获取模型的值。</summary>
      <returns>模型值可以为 null。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.ModelType">
      <summary>获取模型的类型。</summary>
      <returns>模型的类型。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.Properties">
      <summary>获取模型元数据对象的集合，这些对象描述模型的属性。</summary>
      <returns>用于描述模型属性的模型元数据对象的集合。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.PropertyName">
      <summary>获取属性名称。</summary>
      <returns>属性名称。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.Provider">
      <summary>获取或设置提供程序。</summary>
      <returns>提供程序。</returns>
    </member>
    <member name="T:System.Web.Http.Metadata.ModelMetadataProvider">
      <summary>为自定义元数据提供程序提供抽象基类。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadataProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadataProvider.GetMetadataForProperties(System.Object,System.Type)">
      <summary>获取模型的每个属性所对应的 ModelMetadata 对象。</summary>
      <returns>模型的每个属性所对应的 ModelMetadata 对象。</returns>
      <param name="container">容器。</param>
      <param name="containerType">容器的类型。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadataProvider.GetMetadataForProperty(System.Func{System.Object},System.Type,System.String)">
      <summary>获取指定属性的元数据。</summary>
      <returns>指定的属性的元数据模型。</returns>
      <param name="modelAccessor">模型访问器。</param>
      <param name="containerType">容器的类型。</param>
      <param name="propertyName">要获取其元数据模型的属性。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadataProvider.GetMetadataForType(System.Func{System.Object},System.Type)">
      <summary>获取指定模型访问器和模型类型的元数据。</summary>
      <returns>元数据。</returns>
      <param name="modelAccessor">模型访问器。</param>
      <param name="modelType">模式的类型。</param>
    </member>
    <member name="T:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1">
      <summary>提供用于实现元数据提供程序的抽象类。</summary>
      <typeparam name="TModelMetadata">模型元数据的类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.CreateMetadataFromPrototype(`0,System.Func{System.Object})">
      <summary>在派生类中重写时，使用指定的原型创建属性的模型元数据。</summary>
      <returns>属性的模型元数据。</returns>
      <param name="prototype">创建模型元数据时所基于的原型。</param>
      <param name="modelAccessor">模型访问器。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.CreateMetadataPrototype(System.Collections.Generic.IEnumerable{System.Attribute},System.Type,System.Type,System.String)">
      <summary>在派生类中重写时，创建属性的模型元数据。</summary>
      <returns>属性的模型元数据。</returns>
      <param name="attributes">特性集。</param>
      <param name="containerType">容器的类型。</param>
      <param name="modelType">模型的类型。</param>
      <param name="propertyName">属性的名称。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.GetMetadataForProperties(System.Object,System.Type)">
      <summary>检索模型的属性列表。</summary>
      <returns>模型的属性列表。</returns>
      <param name="container">模型容器。</param>
      <param name="containerType">容器的类型。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.GetMetadataForProperty(System.Func{System.Object},System.Type,System.String)">
      <summary>使用容器类型和属性名称检索指定属性的元数据。</summary>
      <returns>指定的属性的元数据。</returns>
      <param name="modelAccessor">模型访问器。</param>
      <param name="containerType">容器的类型。</param>
      <param name="propertyName">属性的名称。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.GetMetadataForType(System.Func{System.Object},System.Type)">
      <summary>使用模型类型返回指定属性的元数据。</summary>
      <returns>指定的属性的元数据。</returns>
      <param name="modelAccessor">模型访问器。</param>
      <param name="modelType">容器的类型。</param>
    </member>
    <member name="T:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes">
      <summary>为 <see cref="T:System.Web.Http.Metadata.Providers.CachedModelMetadata`1" /> 提供原型缓存数据。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.#ctor(System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes" /> 类的新实例。</summary>
      <param name="attributes">提供用于初始化的数据的特性。</param>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.Display">
      <summary>获取或设置元数据显示特性。</summary>
      <returns>元数据显示特性。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.DisplayFormat">
      <summary>获取或设置元数据显示格式特性。</summary>
      <returns>元数据显示格式特性。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.DisplayName"></member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.Editable">
      <summary>获取或设置元数据可编辑特性。</summary>
      <returns>元数据可编辑特性。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.ReadOnly">
      <summary>获取或设置元数据只读特性。</summary>
      <returns>元数据只读特性。</returns>
    </member>
    <member name="T:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata">
      <summary>为公共元数据、<see cref="T:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider" /> 类和数据模型提供容器。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.#ctor(System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata,System.Func{System.Object})">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata" /> 类的新实例。</summary>
      <param name="prototype">用于初始化模型元数据的原型。</param>
      <param name="modelAccessor">模型访问器。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.#ctor(System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider,System.Type,System.Type,System.String,System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata" /> 类的新实例。</summary>
      <param name="provider">元数据提供程序。</param>
      <param name="containerType">容器的类型。</param>
      <param name="modelType">模型的类型。</param>
      <param name="propertyName">属性的名称。</param>
      <param name="attributes">提供用于初始化的数据的特性。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.ComputeConvertEmptyStringToNull">
      <summary>检索一个值，该值指示是否应将在窗体中回发的空字符串转换为 null。</summary>
      <returns>如果在窗体中回发的空字符串应转换为 null，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.ComputeDescription">
      <summary>检索模型的说明。</summary>
      <returns>模型的说明。</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.ComputeIsReadOnly">
      <summary>检索一个值，该值指示模型是否为只读。</summary>
      <returns>如果该模型为只读，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.GetDisplayName"></member>
    <member name="T:System.Web.Http.Metadata.Providers.CachedModelMetadata`1">
      <summary>为 <see cref="T:System.Web.Http.Metadata.Providers.CachedModelMetadata`1" /> 提供原型缓存数据。</summary>
      <typeparam name="TPrototypeCache">原型缓存的类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.#ctor(System.Web.Http.Metadata.Providers.CachedModelMetadata{`0},System.Func{System.Object})">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.Providers.CachedModelMetadata`1" /> 类的新实例。</summary>
      <param name="prototype">原型。</param>
      <param name="modelAccessor">模型访问器。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.#ctor(System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider,System.Type,System.Type,System.String,`0)">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.Providers.CachedModelMetadata`1" /> 类的新实例。</summary>
      <param name="provider">提供程序。</param>
      <param name="containerType">容器的类型。</param>
      <param name="modelType">模型的类型。</param>
      <param name="propertyName">属性的名称。</param>
      <param name="prototypeCache">原型缓存。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.ComputeConvertEmptyStringToNull">
      <summary>指示是否应计算在窗体中回发的空字符串并将其转换为 null。</summary>
      <returns>如果应计算在窗体中回发的空字符串并将其转换为 null，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.ComputeDescription">
      <summary>指示计算值。</summary>
      <returns>计算值。</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.ComputeIsComplexType">
      <summary>获取一个值，该值指示模型是否为复杂类型。</summary>
      <returns>一个值，指示 Web API 框架是否将模型视为复杂类型。</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.ComputeIsReadOnly">
      <summary>获取一个值，该值指示要计算的模型是否为只读。</summary>
      <returns>如果要计算的模型为只读，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.ConvertEmptyStringToNull">
      <summary>获取或设置一个值，该值指示在窗体中回发的空字符串是否应转换为 null。</summary>
      <returns>如果在窗体中回发的空字符串应转换为 null，则为 true；否则为 false。默认值为 true。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.Description">
      <summary>获取或设置模型的说明。</summary>
      <returns>模型的说明。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.IsComplexType">
      <summary>获取一个值，该值指示模型是否为复杂类型。</summary>
      <returns>一个值，指示 Web API 框架是否将模型视为复杂类型。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.IsReadOnly">
      <summary>获取或设置一个值，该值指示模型是否为只读。</summary>
      <returns>如果该模型为只读，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.PrototypeCache">
      <summary>获取或设置一个值，该值指示是否正在更新原型缓存。</summary>
      <returns>如果正在更新原型缓存，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider">
      <summary>实现默认模型元数据提供程序。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider.CreateMetadataFromPrototype(System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata,System.Func{System.Object})">
      <summary>从指定属性的原型创建元数据。</summary>
      <returns>属性的元数据。</returns>
      <param name="prototype">原型。</param>
      <param name="modelAccessor">模型访问器。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider.CreateMetadataPrototype(System.Collections.Generic.IEnumerable{System.Attribute},System.Type,System.Type,System.String)">
      <summary>为指定的属性创建元数据。</summary>
      <returns>属性的元数据。</returns>
      <param name="attributes">特性。</param>
      <param name="containerType">容器的类型。</param>
      <param name="modelType">模型的类型。</param>
      <param name="propertyName">属性的名称。</param>
    </member>
    <member name="T:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider">
      <summary>表示空的模型元数据提供程序。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider.CreateMetadataFromPrototype(System.Web.Http.Metadata.ModelMetadata,System.Func{System.Object})">
      <summary>从原型创建元数据。</summary>
      <returns>元数据。</returns>
      <param name="prototype">模型元数据原型。</param>
      <param name="modelAccessor">模型访问器。</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider.CreateMetadataPrototype(System.Collections.Generic.IEnumerable{System.Attribute},System.Type,System.Type,System.String)">
      <summary>创建 <see cref="T:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider" /> 的元数据提供程序的原型。</summary>
      <returns>元数据提供程序的原型。</returns>
      <param name="attributes">特性。</param>
      <param name="containerType">容器的类型。</param>
      <param name="modelType">模型的类型。</param>
      <param name="propertyName">属性的名称。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.CancellationTokenParameterBinding">
      <summary>表示直接绑定到取消标记的绑定。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.CancellationTokenParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.CancellationTokenParameterBinding" /> 类的新实例。</summary>
      <param name="descriptor">绑定描述符。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.CancellationTokenParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>在同步过程中执行绑定。</summary>
      <returns>同步过程中的绑定。</returns>
      <param name="metadataProvider">元数据提供程序。</param>
      <param name="actionContext">操作上下文。</param>
      <param name="cancellationToken">取消操作后的通知。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.CustomModelBinderAttribute">
      <summary>表示一个调用自定义模型联编程序的特性。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.CustomModelBinderAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.CustomModelBinderAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.CustomModelBinderAttribute.GetBinder">
      <summary>检索关联的模型联编程序。</summary>
      <returns>对实现 <see cref="T:System.Web.Http.ModelBinding.IModelBinder" /> 接口的对象的引用。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.DefaultActionValueBinder">
      <summary>表示联编程序的默认操作值。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.DefaultActionValueBinder.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.DefaultActionValueBinder" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.DefaultActionValueBinder.GetBinding(System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>
        <see cref="T:System.Web.Http.Controllers.IActionValueBinder" /> 接口的默认实现。此接口是绑定操作参数的主入口点。</summary>
      <returns>与 <see cref="T:System.Web.Http.ModelBinding.DefaultActionValueBinder" /> 关联的 <see cref="T:System.Web.Http.Controllers.HttpActionBinding" />。</returns>
      <param name="actionDescriptor">操作描述符。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.DefaultActionValueBinder.GetParameterBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>获取与 <see cref="T:System.Web.Http.ModelBinding.DefaultActionValueBinder" /> 关联的 <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" />。</summary>
      <returns>与 <see cref="T:System.Web.Http.ModelBinding.DefaultActionValueBinder" /> 关联的 <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" />。</returns>
      <param name="parameter">参数描述符。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ErrorParameterBinding">
      <summary>定义绑定错误。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ErrorParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor,System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ErrorParameterBinding" /> 类的新实例。</summary>
      <param name="descriptor">错误描述符。</param>
      <param name="message">消息。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ErrorParameterBinding.ErrorMessage">
      <summary>获取错误消息。</summary>
      <returns>错误消息。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ErrorParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>在同步过程中执行绑定方法。</summary>
      <param name="metadataProvider">元数据提供程序。</param>
      <param name="actionContext">操作上下文。</param>
      <param name="cancellationToken">取消标记值。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.FormatterParameterBinding">
      <summary>表示将从正文读取并调用格式化程序的参数绑定。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormatterParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Web.Http.Validation.IBodyModelValidator)">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.FormatterParameterBinding" /> 类的新实例。</summary>
      <param name="descriptor">描述符。</param>
      <param name="formatters">格式化程序。</param>
      <param name="bodyModelValidator">正文模型验证程序。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.FormatterParameterBinding.BodyModelValidator">
      <summary>获取或设置正文模型验证程序的接口。</summary>
      <returns>正文模型验证程序的接口。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.FormatterParameterBinding.ErrorMessage">
      <summary>获取错误消息。</summary>
      <returns>错误消息。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormatterParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>异步执行 <see cref="T:System.Web.Http.ModelBinding.FormatterParameterBinding" /> 的绑定。</summary>
      <returns>操作的结果。</returns>
      <param name="metadataProvider">元数据提供程序。</param>
      <param name="actionContext">与操作关联的上下文。</param>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.FormatterParameterBinding.Formatters">
      <summary>获取或设置一个可枚举对象，该对象表示用于参数绑定的格式化程序。</summary>
      <returns>一个可枚举对象，该对象表示用于参数绑定的格式化程序。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormatterParameterBinding.ReadContentAsync(System.Net.Http.HttpRequestMessage,System.Type,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Net.Http.Formatting.IFormatterLogger)">
      <summary>异步读取 <see cref="T:System.Web.Http.ModelBinding.FormatterParameterBinding" /> 的内容。</summary>
      <returns>操作的结果。</returns>
      <param name="request">请求。</param>
      <param name="type">类型。</param>
      <param name="formatters">格式化程序。</param>
      <param name="formatterLogger">格式记录程序。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormatterParameterBinding.ReadContentAsync(System.Net.Http.HttpRequestMessage,System.Type,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Net.Http.Formatting.IFormatterLogger,System.Threading.CancellationToken)"></member>
    <member name="P:System.Web.Http.ModelBinding.FormatterParameterBinding.WillReadBody">
      <summary>获取 <see cref="T:System.Web.Http.ModelBinding.FormatterParameterBinding" /> 是否将读取正文。</summary>
      <returns>如果 <see cref="T:System.Web.Http.ModelBinding.FormatterParameterBinding" /> 将读取正文，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.FormDataCollectionExtensions">
      <summary>表示窗体数据集合的扩展。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs``1(System.Net.Http.Formatting.FormDataCollection)">
      <summary>读取具有指定类型的集合扩展。</summary>
      <returns>读取集合扩展。</returns>
      <param name="formData">窗体数据。</param>
      <typeparam name="T">泛型类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs``1(System.Net.Http.Formatting.FormDataCollection,System.String,System.Net.Http.Formatting.IRequiredMemberSelector,System.Net.Http.Formatting.IFormatterLogger)">
      <summary>读取具有指定类型的集合扩展。</summary>
      <returns>集合扩展。</returns>
      <param name="formData">窗体数据。</param>
      <param name="modelName">模型的名称。</param>
      <param name="requiredMemberSelector">所需的成员选择器。</param>
      <param name="formatterLogger">格式化程序记录程序。</param>
      <typeparam name="T">泛型类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs``1(System.Net.Http.Formatting.FormDataCollection,System.String,System.Web.Http.Controllers.HttpActionContext)">
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs(System.Net.Http.Formatting.FormDataCollection,System.Type)">
      <summary>读取具有指定类型的集合扩展。</summary>
      <returns>具有指定类型的集合扩展。</returns>
      <param name="formData">窗体数据。</param>
      <param name="type">对象的类型。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs(System.Net.Http.Formatting.FormDataCollection,System.Type,System.String,System.Net.Http.Formatting.IRequiredMemberSelector,System.Net.Http.Formatting.IFormatterLogger)">
      <summary>读取具有指定类型和模型名称的集合扩展。</summary>
      <returns>集合扩展。</returns>
      <param name="formData">窗体数据。</param>
      <param name="type">对象的类型。</param>
      <param name="modelName">模型的名称。</param>
      <param name="requiredMemberSelector">所需的成员选择器。</param>
      <param name="formatterLogger">格式化程序记录程序。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs(System.Net.Http.Formatting.FormDataCollection,System.Type,System.String,System.Net.Http.Formatting.IRequiredMemberSelector,System.Net.Http.Formatting.IFormatterLogger,System.Web.Http.HttpConfiguration)">
      <summary>使用模型绑定将窗体数据反序列化为给定类型。</summary>
      <returns>绑定对象的最佳尝试。最佳尝试可以为 null。</returns>
      <param name="formData">包含已分析的窗体 URL 数据的集合</param>
      <param name="type">要读取为的目标类型</param>
      <param name="modelName">若要将整个窗体读取为单个对象，则为  null 或为空。这对于正文数据很常见。或者是要对窗体数据执行部分绑定的模型名称。这在提取各个字段时很常见。</param>
      <param name="requiredMemberSelector">用于确定必需的成员的 <see cref="T:System.Net.Http.Formatting.IRequiredMemberSelector" />。</param>
      <param name="formatterLogger">要将事件记录到的 <see cref="T:System.Net.Http.Formatting.IFormatterLogger" />。</param>
      <param name="config">要从中选取联编程序的 <see cref="T:System.Web.Http.HttpConfiguration" /> 配置。如果尚未创建该配置，则可以为 null。在这种情况下，会创建一个新配置。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs(System.Net.Http.Formatting.FormDataCollection,System.Type,System.String,System.Web.Http.Controllers.HttpActionContext)"></member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs(System.Net.Http.Formatting.FormDataCollection,System.Type,System.Web.Http.Controllers.HttpActionContext)"></member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs``1(System.Net.Http.Formatting.FormDataCollection,System.Web.Http.Controllers.HttpActionContext)">
      <typeparam name="T"></typeparam>
    </member>
    <member name="T:System.Web.Http.ModelBinding.HttpBindingBehavior">
      <summary>枚举 HTTP 绑定的行为。</summary>
    </member>
    <member name="F:System.Web.Http.ModelBinding.HttpBindingBehavior.Never">
      <summary>绝不使用 HTTP 绑定。</summary>
    </member>
    <member name="F:System.Web.Http.ModelBinding.HttpBindingBehavior.Optional">
      <summary>可选绑定行为。</summary>
    </member>
    <member name="F:System.Web.Http.ModelBinding.HttpBindingBehavior.Required">
      <summary>HTTP 绑定是必需的。</summary>
    </member>
    <member name="T:System.Web.Http.ModelBinding.HttpBindingBehaviorAttribute">
      <summary>提供模型绑定行为特性的基类。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.HttpBindingBehaviorAttribute.#ctor(System.Web.Http.ModelBinding.HttpBindingBehavior)">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.HttpBindingBehaviorAttribute" /> 类的新实例。</summary>
      <param name="behavior">行为。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.HttpBindingBehaviorAttribute.Behavior">
      <summary>获取或设置行为类别。</summary>
      <returns>行为类别。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.HttpBindingBehaviorAttribute.TypeId">
      <summary>获取此特性的唯一标识符。</summary>
      <returns>此特性的 ID。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.HttpRequestParameterBinding">
      <summary>将参数绑定到请求。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.HttpRequestParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.HttpRequestParameterBinding" /> 类的新实例。</summary>
      <param name="descriptor">参数描述符。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.HttpRequestParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>以异步方式执行参数绑定。</summary>
      <returns>绑定的参数。</returns>
      <param name="metadataProvider">元数据提供程序。</param>
      <param name="actionContext">操作上下文。</param>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.IModelBinder">
      <summary>定义模型联编程序所需的方法。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.IModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>使用指定的控制器上下文和绑定上下文将模型绑定到一个值。</summary>
      <returns>如果模型绑定成功，则为 true；否则为 false。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="bindingContext">绑定上下文。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.IValueProviderParameterBinding">
      <summary>表示参数绑定的值提供程序。</summary>
    </member>
    <member name="P:System.Web.Http.ModelBinding.IValueProviderParameterBinding.ValueProviderFactories">
      <summary>获取此参数绑定所使用的 <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> 实例。</summary>
      <returns>此参数绑定所使用的 <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> 实例。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter">
      <summary>表示 <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" /> 类，用于处理以 HTML 窗体 URL 结尾的数据（也称为 application/x-www-form-urlencoded）。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter.#ctor(System.Web.Http.HttpConfiguration)"></member>
    <member name="M:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter.CanReadType(System.Type)">
      <summary> 确定此 <see cref="T:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter" /> 是否可以读取指定 <paramref name="type" /> 的对象。</summary>
      <returns>如果可以读取此类型的对象，则为 true；否则为 false。</returns>
      <param name="type">将要读取的对象的类型。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter.ReadFromStreamAsync(System.Type,System.IO.Stream,System.Net.Http.HttpContent,System.Net.Http.Formatting.IFormatterLogger)">
      <summary>从指定流中读取指定 <paramref name="type" /> 的对象。在反序列化期间调用此方法。</summary>
      <returns>一个 <see cref="T:System.Threading.Tasks.Task" />，其结果将是已读取的对象实例。</returns>
      <param name="type">要读取的对象的类型。</param>
      <param name="readStream">要从中读取的 <see cref="T:System.IO.Stream" />。</param>
      <param name="content">要读取的内容。</param>
      <param name="formatterLogger">要将事件记录到的 <see cref="T:System.Net.Http.Formatting.IFormatterLogger" />。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBinderAttribute">
      <summary>指定此参数使用模型联编程序。这可以有选择性地指定特定模型联编程序和驱动该模型联编程序的值提供程序。派生的特性可以为模型联编程序或值提供程序提供方便的设置。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelBinderAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelBinderAttribute" /> 类的新实例。</summary>
      <param name="binderType">模型联编程序的类型。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderAttribute.BinderType">
      <summary>获取或设置模型联编程序的类型。</summary>
      <returns>模型联编程序的类型。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.GetBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>获取参数绑定。</summary>
      <returns>包含绑定的 <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" />。</returns>
      <param name="parameter">要绑定的参数。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.GetModelBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary> 获取此类型的 IModelBinder。</summary>
      <returns> 非 null 模型联编程序。</returns>
      <param name="configuration">配置。</param>
      <param name="modelType">联编程序应绑定的模型类型。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.GetModelBinderProvider(System.Web.Http.HttpConfiguration)">
      <summary>获取模型联编程序提供程序。</summary>
      <returns>
        <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> 实例。</returns>
      <param name="configuration">
        <see cref="T:System.Web.Http.HttpConfiguration" /> 配置对象。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.GetValueProviderFactories(System.Web.Http.HttpConfiguration)">
      <summary> 获取将向模型联编程序提供的值提供程序。</summary>
      <returns>
        <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> 实例的集合。</returns>
      <param name="configuration">
        <see cref="T:System.Web.Http.HttpConfiguration" /> 配置对象。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderAttribute.Name">
      <summary>获取或设置要在模型绑定期间视为参数名称的名称。</summary>
      <returns>要考虑的参数名称。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderAttribute.SuppressPrefixCheck">
      <summary>获取或设置一个值，该值指定是否应取消前缀检查。</summary>
      <returns>如果应取消前缀检查，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBinderConfig">
      <summary>为模型联编程序配置提供一个容器。</summary>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderConfig.ResourceClassKey">
      <summary>获取或设置包含本地化字符串值的资源文件的名称（类键）。</summary>
      <returns>资源文件的名称（类键）。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderConfig.TypeConversionErrorMessageProvider">
      <summary>获取或设置类型转换错误消息的当前提供程序。</summary>
      <returns>类型转换错误消息的当前提供程序。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderConfig.ValueRequiredErrorMessageProvider">
      <summary>获取或设置必填值错误消息的当前提供程序。</summary>
      <returns>错误消息提供程序。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBinderErrorMessageProvider">
      <summary>为模型联编程序错误消息提供程序提供一个容器。</summary>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBinderParameterBinding">
      <summary> 描述通过 ModelBinding 绑定的参数。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.ModelBinding.IModelBinder,System.Collections.Generic.IEnumerable{System.Web.Http.ValueProviders.ValueProviderFactory})">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelBinderParameterBinding" /> 类的新实例。</summary>
      <param name="descriptor">参数描述符。</param>
      <param name="modelBinder">模型联编程序。</param>
      <param name="valueProviderFactories">值提供程序工厂的集合。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderParameterBinding.Binder">
      <summary>获取模型联编程序。</summary>
      <returns>模型联编程序。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>以异步方式通过模型联编程序执行参数绑定。</summary>
      <returns>绑定完成时将通知的任务。</returns>
      <param name="metadataProvider">要用于验证的元数据提供程序。</param>
      <param name="actionContext">绑定的操作上下文。</param>
      <param name="cancellationToken">为此任务分配的用于取消绑定操作的取消标记。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderParameterBinding.ValueProviderFactories">
      <summary>获取值提供程序工厂的集合。</summary>
      <returns>值提供程序工厂的集合。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBinderProvider">
      <summary>为模型联编程序提供程序提供抽象基类。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>查找给定类型的联编程序。</summary>
      <returns>一个联编程序，该程序可能尝试绑定此类型。或者如果此联编程序静态了解到它将无法绑定该类型，则为 null。</returns>
      <param name="configuration">配置对象。</param>
      <param name="modelType">要绑定的模型的类型。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBindingContext">
      <summary>提供运行模型联编程序的上下文。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBindingContext.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelBindingContext" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBindingContext.#ctor(System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelBindingContext" /> 类的新实例。</summary>
      <param name="bindingContext">绑定上下文。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.FallbackToEmptyPrefix">
      <summary>获取或设置一个值，该值指示联编程序是否应使用空前缀。</summary>
      <returns>如果联编程序应使用空前缀，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.Model">
      <summary>获取或设置模型。</summary>
      <returns>模型。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ModelMetadata">
      <summary>获取或设置模型元数据。</summary>
      <returns>模型元数据。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ModelName">
      <summary>获取或设置模型的名称。</summary>
      <returns>模型的名称。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ModelState">
      <summary>获取或设置模型的状态。</summary>
      <returns>模型的状态。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ModelType">
      <summary>获取或设置模型的类型。</summary>
      <returns>模型的类型。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.PropertyMetadata">
      <summary>获取属性元数据。</summary>
      <returns>属性元数据。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ValidationNode">
      <summary>获取或设置验证节点。</summary>
      <returns>验证节点。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ValueProvider">
      <summary>获取或设置值提供程序。</summary>
      <returns>值提供程序。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelError">
      <summary>表示在模型绑定期间发生的错误。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelError.#ctor(System.Exception)">
      <summary>使用指定的异常初始化 <see cref="T:System.Web.Http.ModelBinding.ModelError" /> 类的新实例。</summary>
      <param name="exception">异常。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelError.#ctor(System.Exception,System.String)">
      <summary>使用指定的异常和错误消息初始化 <see cref="T:System.Web.Http.ModelBinding.ModelError" /> 类的新实例。</summary>
      <param name="exception">异常。</param>
      <param name="errorMessage">错误消息</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelError.#ctor(System.String)">
      <summary>使用指定的错误消息初始化 <see cref="T:System.Web.Http.ModelBinding.ModelError" /> 类的新实例。</summary>
      <param name="errorMessage">错误消息</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelError.ErrorMessage">
      <summary>获取或设置错误消息。</summary>
      <returns>错误消息。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelError.Exception">
      <summary>获取或设置异常对象。</summary>
      <returns>异常对象。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelErrorCollection">
      <summary>表示 <see cref="T:System.Web.Http.ModelBinding.ModelError" /> 实例的集合。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelErrorCollection.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelErrorCollection" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelErrorCollection.Add(System.Exception)">
      <summary>将指定的 Exception 对象添加到模型错误集合中。</summary>
      <param name="exception">异常。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelErrorCollection.Add(System.String)">
      <summary>将指定的错误消息添加到模型错误集合中。</summary>
      <param name="errorMessage">错误消息。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelState">
      <summary>将模型绑定的状态封装到操作-方法参数的一个属性或操作方法参数本身。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelState.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelState" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelState.Errors">
      <summary>获取一个 <see cref="T:System.Web.Http.ModelBinding.ModelErrorCollection" /> 对象，该对象包含在模型绑定期间发生的任何错误。</summary>
      <returns>模型状态错误。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelState.Value">
      <summary>获取一个 <see cref="T:System.Web.Http.ValueProviders.ValueProviderResult" /> 对象，该对象用于封装在模型绑定期间绑定的值。</summary>
      <returns>模型状态值。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelStateDictionary">
      <summary>表示有关将已发送窗体绑定到操作方法（其中包括验证信息）的尝试的状态。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.#ctor(System.Web.Http.ModelBinding.ModelStateDictionary)">
      <summary>使用从指定的模型状态字典复制的值来初始化 <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" /> 类的新实例。</summary>
      <param name="dictionary">字典。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Add(System.Collections.Generic.KeyValuePair{System.String,System.Web.Http.ModelBinding.ModelState})">
      <summary>将指定的项添加到模型状态字典中。</summary>
      <param name="item">要添加到模型状态字典中的对象。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Add(System.String,System.Web.Http.ModelBinding.ModelState)">
      <summary>将具有指定的键和值的元素添加到模型状态字典中。</summary>
      <param name="key">要添加的元素的键。</param>
      <param name="value">要添加的元素的值。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.AddModelError(System.String,System.Exception)">
      <summary>将指定的模型错误添加到与指定键关联的模型状态字典的错误集合中。</summary>
      <param name="key">键。</param>
      <param name="exception">异常。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.AddModelError(System.String,System.String)">
      <summary>将指定的错误消息添加到与指定键关联的模型状态字典的错误集合中。</summary>
      <param name="key">键。</param>
      <param name="errorMessage">错误消息。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Clear">
      <summary>移除模型状态字典中的所有项。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Contains(System.Collections.Generic.KeyValuePair{System.String,System.Web.Http.ModelBinding.ModelState})">
      <summary>确定模型状态字典是否包含特定值。</summary>
      <returns>如果在模型状态字典中找到了相应项，则为 true；否则为 false。</returns>
      <param name="item">要在模型状态字典中查找的对象。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.ContainsKey(System.String)">
      <summary>确定模型状态字典是否包含指定的键。</summary>
      <returns>如果模型状态字典包含指定的键，则为 true；否则为 false。</returns>
      <param name="key">要在模型状态字典中查找的键。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Web.Http.ModelBinding.ModelState}[],System.Int32)">
      <summary>从指定的索引位置开始，将模型状态字典中的元素复制到一个数组中。</summary>
      <param name="array">数组。该数组的索引必须从零开始。</param>
      <param name="arrayIndex">数组中开始复制位置的从零开始的索引。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.Count">
      <summary>获取集合中键/值对的数目。</summary>
      <returns>集合中键/值对的数目。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.GetEnumerator">
      <summary>返回一个可用于循环访问集合的枚举器。</summary>
      <returns>一个可用于循环访问集合的枚举器。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.IsReadOnly">
      <summary>获取一个值，该值指示该集合是否为只读集合。</summary>
      <returns>如果该集合是只读的，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.IsValid">
      <summary>获取一个值，该值指示模型状态字典的此实例是否有效。</summary>
      <returns>如果该实例有效，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.IsValidField(System.String)">
      <summary>确定是否存在与指定键关联或以指定键为前缀的任何 <see cref="T:System.Web.Http.ModelBinding.ModelError" /> 对象。</summary>
      <returns>如果模型状态字典包含一个与指定键关联的值，则为 true；否则为 false。</returns>
      <param name="key">键。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.Item(System.String)">
      <summary>获取或设置与指定的键关联的值。</summary>
      <returns>模型状态项。</returns>
      <param name="key">键。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.Keys">
      <summary>获取包含字典中的键的集合。</summary>
      <returns>一个包含模型状态字典中的键的集合。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Merge(System.Web.Http.ModelBinding.ModelStateDictionary)">
      <summary>将指定的 <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" /> 对象中的值复制到此字典中，如果键相同，则覆盖现有值。</summary>
      <param name="dictionary">字典。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Remove(System.Collections.Generic.KeyValuePair{System.String,System.Web.Http.ModelBinding.ModelState})">
      <summary>从模型状态字典中移除指定对象的第一个匹配项。</summary>
      <returns>如果从模型状态字典中成功移除了相应项，则为 true；否则为 false。如果在模型状态字典中找不到相应项，则此方法也会返回 false。</returns>
      <param name="item">要从模型状态字典中移除的对象。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Remove(System.String)">
      <summary>从模型状态字典中移除具有指定键的元素。</summary>
      <returns>如果成功移除该元素，则为 true；否则为 false。如果在模型状态字典中找不到相应键，则此方法也会返回 false。</returns>
      <param name="key">要移除的元素的键。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.SetModelValue(System.String,System.Web.Http.ValueProviders.ValueProviderResult)">
      <summary>使用指定的值提供程序字典设置指定键的值。</summary>
      <param name="key">键。</param>
      <param name="value">值。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回循环访问集合的枚举器。</summary>
      <returns>一个可用于循环访问集合的 IEnumerator 对象。</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.TryGetValue(System.String,System.Web.Http.ModelBinding.ModelState@)">
      <summary>尝试获取与指定的键关联的值。</summary>
      <returns>如果对象包含具有指定键的元素，则为 true；否则为 false。</returns>
      <param name="key">要获取的值的键。</param>
      <param name="value">与指定键关联的值。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.Values">
      <summary>获取包含字典中的值的集合。</summary>
      <returns>一个包含模型状态字典中的值的集合。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ParameterBindingRulesCollection">
      <summary> 函数的集合，可用于生成给定参数的参数绑定。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ParameterBindingRulesCollection.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.ParameterBindingRulesCollection" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ParameterBindingRulesCollection.Add(System.Type,System.Func{System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.Controllers.HttpParameterBinding})">
      <summary> 将函数添加到集合的末尾。添加的函数是用于检查 parameterType 是否与 typeMatch 匹配的 funcInner 的包装。</summary>
      <param name="typeMatch">要与 HttpParameterDescriptor.ParameterType 匹配的类型。</param>
      <param name="funcInner">类型匹配成功时调用的内部函数</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ParameterBindingRulesCollection.Insert(System.Int32,System.Type,System.Func{System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.Controllers.HttpParameterBinding})">
      <summary> 在集合中的指定索引处插入一个函数。/// 添加的函数是用于检查 parameterType 是否与 typeMatch 匹配的 funcInner 的包装。</summary>
      <param name="index">要插入处的索引。</param>
      <param name="typeMatch">要与 HttpParameterDescriptor.ParameterType 匹配的类型。</param>
      <param name="funcInner">类型匹配成功时调用的内部函数</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ParameterBindingRulesCollection.LookupBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary> 按顺序执行每个绑定函数，直到其中一个函数返回非 null 绑定。</summary>
      <returns>为参数生成的第一个非 null 绑定；如果未生成绑定，则为 null。</returns>
      <param name="parameter">要绑定的参数。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ArrayModelBinder`1">
      <summary>将浏览器请求映射到数组。</summary>
      <typeparam name="TElement">数组的类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ArrayModelBinder`1.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.ArrayModelBinder`1" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ArrayModelBinder`1.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>指示是否绑定模型。</summary>
      <returns>如果已绑定指定模型，则为 true；否则为 false。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="bindingContext">绑定上下文。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ArrayModelBinder`1.CreateOrReplaceCollection(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.Collections.Generic.IList{`0})">
      <summary>将集合转换为数组。</summary>
      <returns>所有情况下均为 true。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="bindingContext">绑定上下文。</param>
      <param name="newCollection">新集合。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ArrayModelBinderProvider">
      <summary>为数组提供模型联编程序。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ArrayModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.ArrayModelBinderProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ArrayModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>返回数组的模型联编程序。</summary>
      <returns>一个模型联编程序对象；或者如果尝试获取模型联编程序失败，则为 null。</returns>
      <param name="configuration">配置。</param>
      <param name="modelType">模型的类型。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.CollectionModelBinder`1">
      <summary>将浏览器请求映射到集合。</summary>
      <typeparam name="TElement">集合的类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CollectionModelBinder`1.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.CollectionModelBinder`1" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CollectionModelBinder`1.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>使用指定的执行上下文和绑定上下文来绑定模型。</summary>
      <returns>如果模型绑定成功，则为 true；否则为 false。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="bindingContext">绑定上下文。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CollectionModelBinder`1.CreateOrReplaceCollection(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.Collections.Generic.IList{`0})">
      <summary>提供一种方法来让派生类在将集合从联编程序返回之前对集合进行操作。</summary>
      <returns>所有情况下均为 true。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="bindingContext">绑定上下文。</param>
      <param name="newCollection">新集合。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.CollectionModelBinderProvider">
      <summary>为集合提供模型联编程序。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CollectionModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.CollectionModelBinderProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CollectionModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>检索集合的模型联编程序。</summary>
      <returns>模型联编程序。</returns>
      <param name="configuration">模型的配置。</param>
      <param name="modelType">模型的类型。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto">
      <summary>表示一个复杂模型的数据传输对象 (DTO)。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDto.#ctor(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Metadata.ModelMetadata})">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 类的新实例。</summary>
      <param name="modelMetadata">模型元数据。</param>
      <param name="propertyMetadata">属性元数据的集合。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.ComplexModelDto.ModelMetadata">
      <summary>获取或设置 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 的模型元数据。</summary>
      <returns>
        <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 的模型元数据。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.ComplexModelDto.PropertyMetadata">
      <summary>获取或设置 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 的属性元数据的集合。</summary>
      <returns>
        <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 的属性元数据的集合。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.ComplexModelDto.Results">
      <summary>获取或设置 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 的结果。</summary>
      <returns>
        <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 的结果。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinder">
      <summary>表示 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 对象的模型联编程序。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinder.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinder" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>确定是否已绑定指定的模型。</summary>
      <returns>如果已绑定指定模型，则为 true；否则为 false。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="bindingContext">绑定上下文。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinderProvider">
      <summary>表示调用模型联编程序提供程序的复杂模型。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinderProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>检索关联的模型联编程序。</summary>
      <returns>模型联编程序。</returns>
      <param name="configuration">配置。</param>
      <param name="modelType">要检索的模型的类型。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult">
      <summary>表示 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> 对象的结果。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult.#ctor(System.Object,System.Web.Http.Validation.ModelValidationNode)">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult" /> 类的新实例。</summary>
      <param name="model">对象模型。</param>
      <param name="validationNode">验证节点。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult.Model">
      <summary>获取或设置此对象的模型。</summary>
      <returns>此对象的模型。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult.ValidationNode">
      <summary>获取或设置此对象的 <see cref="T:System.Web.Http.Validation.ModelValidationNode" />。</summary>
      <returns>此对象的 <see cref="T:System.Web.Http.Validation.ModelValidationNode" />。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinder">
      <summary>表示委托给 <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> 实例集合之一的 <see cref="T:System.Web.Http.ModelBinding.IModelBinder" />。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinder.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.ModelBinding.IModelBinder})">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinder" /> 类的新实例。</summary>
      <param name="binders">联编程序的枚举。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinder.#ctor(System.Web.Http.ModelBinding.IModelBinder[])">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinder" /> 类的新实例。</summary>
      <param name="binders">联编程序的数组。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>指示是否绑定指定模型。</summary>
      <returns>如果已绑定该模型，则为 true；否则为 false。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="bindingContext">绑定上下文。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider">
      <summary>表示复合模型联编程序提供程序的类。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.ModelBinding.ModelBinderProvider})">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider" /> 类的新实例。</summary>
      <param name="providers">
        <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> 的集合</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>获取模型的联编程序。</summary>
      <returns>模型的联编程序。</returns>
      <param name="configuration">联编程序配置。</param>
      <param name="modelType">模型的类型。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider.Providers">
      <summary>获取复合模型联编程序的提供程序。</summary>
      <returns>提供程序集合。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.DictionaryModelBinder`2">
      <summary>将浏览器请求映射到字典数据对象。</summary>
      <typeparam name="TKey">键的类型。</typeparam>
      <typeparam name="TValue">值的类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.DictionaryModelBinder`2.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.DictionaryModelBinder`2" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.DictionaryModelBinder`2.CreateOrReplaceCollection(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.Collections.Generic.IList{System.Collections.Generic.KeyValuePair{`0,`1}})">
      <summary>将集合转换为字典。</summary>
      <returns>所有情况下均为 true。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="bindingContext">绑定上下文。</param>
      <param name="newCollection">新集合。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.DictionaryModelBinderProvider">
      <summary>为字典提供模型联编程序。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.DictionaryModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.DictionaryModelBinderProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.DictionaryModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>检索关联的模型联编程序。</summary>
      <returns>关联的模型联编程序。</returns>
      <param name="configuration">要使用的配置。</param>
      <param name="modelType">模型的类型。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinder`2">
      <summary>将浏览器请求映射到键/值对数据对象。</summary>
      <typeparam name="TKey">键的类型。</typeparam>
      <typeparam name="TValue">值的类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinder`2.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinder`2" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinder`2.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>使用指定的执行上下文和绑定上下文来绑定模型。</summary>
      <returns>如果模型绑定成功，则为 true；否则为 false。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="bindingContext">绑定上下文。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinderProvider">
      <summary>为键/值对的集合提供模型联编程序。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinderProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>检索关联的模型联编程序。</summary>
      <returns>关联的模型联编程序。</returns>
      <param name="configuration">配置。</param>
      <param name="modelType">模型的类型。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder">
      <summary>将浏览器请求映射到可变数据对象。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>使用指定的操作上下文和绑定上下文来绑定模型。</summary>
      <returns>如果绑定成功，则为 true；否则为 false。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="bindingContext">绑定上下文。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.CanUpdateProperty(System.Web.Http.Metadata.ModelMetadata)">
      <summary>检索一个值，该值指示是否可以更新某个属性。</summary>
      <returns>如果可以更新该属性，则为 true；否则为 false。</returns>
      <param name="propertyMetadata">要对其求值的属性的元数据。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.CreateModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>创建模型的实例。</summary>
      <returns>新建的模型对象。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="bindingContext">绑定上下文。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.EnsureModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>如果绑定上下文中尚不存在实例，则创建一个模型实例。</summary>
      <param name="actionContext">操作上下文。</param>
      <param name="bindingContext">绑定上下文。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.GetMetadataForProperties(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>检索模型属性的元数据。</summary>
      <returns>模型属性的元数据。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="bindingContext">绑定上下文。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.SetProperty(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.Web.Http.Metadata.ModelMetadata,System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult,System.Web.Http.Validation.ModelValidator)">
      <summary>设置指定属性的值。</summary>
      <param name="actionContext">操作上下文。</param>
      <param name="bindingContext">绑定上下文。</param>
      <param name="propertyMetadata">要设置的属性的元数据。</param>
      <param name="dtoResult">有关属性的验证信息。</param>
      <param name="requiredValidator">模型的验证程序。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinderProvider">
      <summary>为可变对象提供模型联编程序。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinderProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>检索指定类型的模型联编程序。</summary>
      <returns>模型联编程序。</returns>
      <param name="configuration">配置。</param>
      <param name="modelType">要检索的模型的类型。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider">
      <summary>提供此模型绑定类的简单模型联编程序。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider.#ctor(System.Type,System.Func{System.Web.Http.ModelBinding.IModelBinder})">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider" /> 类的新实例。</summary>
      <param name="modelType">模型类型。</param>
      <param name="modelBinderFactory">模型联编程序工厂。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider.#ctor(System.Type,System.Web.Http.ModelBinding.IModelBinder)">
      <summary>使用指定的模型类型和模型联编程序来初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider" /> 类的新实例。</summary>
      <param name="modelType">模型类型。</param>
      <param name="modelBinder">模型联编程序。</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>使用指定的执行上下文和绑定上下文来返回模型联编程序。</summary>
      <returns>模型联编程序；如果尝试获取模型联编程序失败，则为 null。</returns>
      <param name="configuration">配置。</param>
      <param name="modelType">模型类型。</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider.ModelType">
      <summary>获取模型的类型。</summary>
      <returns>模型的类型。</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider.SuppressPrefixCheck">
      <summary>获取或设置一个值，该值指定是否应取消前缀检查。</summary>
      <returns>如果应取消前缀检查，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinder">
      <summary>将浏览器请求映射到数据对象。当模型绑定要求使用 .NET Framework 类型转换器进行转换时，将使用此类型。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinder.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinder" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>使用指定的控制器上下文和绑定上下文来绑定模型。</summary>
      <returns>如果模型绑定成功，则为 true；否则为 false。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="bindingContext">绑定上下文。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinderProvider">
      <summary>为需要类型转换的模型提供模型联编程序。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinderProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>检索需要类型转换的模型的模型联编程序。</summary>
      <returns>模型联编程序；如果无法转换类型，或者没有可转换的值，则为 Nothing。</returns>
      <param name="configuration">联编程序的配置。</param>
      <param name="modelType">模型的类型。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinder">
      <summary>将浏览器请求映射到数据对象。当模型绑定不需要类型转换时，将使用此类。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinder.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinder" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>使用指定的执行上下文和绑定上下文来绑定模型。</summary>
      <returns>如果模型绑定成功，则为 true；否则为 false。</returns>
      <param name="actionContext">操作上下文。</param>
      <param name="bindingContext">绑定上下文。</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinderProvider">
      <summary>为不需要类型转换的模型提供模型联编程序。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinderProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinderProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>检索关联的模型联编程序。</summary>
      <returns>关联的模型联编程序。</returns>
      <param name="configuration">配置。</param>
      <param name="modelType">模型的类型。</param>
    </member>
    <member name="T:System.Web.Http.Results.BadRequestErrorMessageResult">
      <summary>表示一个操作结果，该结果返回 <see cref="F:System.Net.HttpStatusCode.BadRequest" /> 响应，并在出现 <see cref="T:System.Web.Http.HttpError" /> 和 <see cref="P:System.Web.Http.HttpError.Message" /> 时执行内容协商。</summary>
    </member>
    <member name="M:System.Web.Http.Results.BadRequestErrorMessageResult.#ctor(System.String,System.Net.Http.Formatting.IContentNegotiator,System.Net.Http.HttpRequestMessage,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>初始化 <see cref="T:System.Web.Http.Results.BadRequestErrorMessageResult" /> 类的新实例。</summary>
      <param name="message">用户可见的错误消息。</param>
      <param name="contentNegotiator">用于处理内容协商的内容协商者。</param>
      <param name="request">导致此结果的请求消息。</param>
      <param name="formatters">用于协商内容及设置内容格式的格式化程序。</param>
    </member>
    <member name="M:System.Web.Http.Results.BadRequestErrorMessageResult.#ctor(System.String,System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.BadRequestErrorMessageResult" /> 类的新实例。</summary>
      <param name="message">用户可见的错误消息。</param>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.BadRequestErrorMessageResult.ContentNegotiator">
      <summary>获取用于处理内容协商的内容协商者。</summary>
      <returns>返回 <see cref="T:System.Net.Http.Formatting.IContentNegotiator" />。</returns>
    </member>
    <member name="M:System.Web.Http.Results.BadRequestErrorMessageResult.ExecuteAsync(System.Threading.CancellationToken)">
      <returns>返回 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.BadRequestErrorMessageResult.Formatters">
      <summary>获取用于协商内容及设置内容格式的格式化程序。</summary>
      <returns>返回 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.BadRequestErrorMessageResult.Message">
      <summary>获取用户可见的错误消息。</summary>
      <returns>返回 <see cref="T:System.String" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.BadRequestErrorMessageResult.Request">
      <summary>获取导致此结果的请求消息。</summary>
      <returns>返回 <see cref="T:System.Net.Http.HttpRequestMessage" />。</returns>
    </member>
    <member name="T:System.Web.Http.Results.BadRequestResult">
      <summary>表示一个返回空 <see cref="F:System.Net.HttpStatusCode.BadRequest" /> 响应的操作结果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.BadRequestResult.#ctor(System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.BadRequestResult" /> 类的新实例。</summary>
      <param name="request">导致此结果的请求消息。</param>
    </member>
    <member name="M:System.Web.Http.Results.BadRequestResult.#ctor(System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.BadRequestResult" /> 类的新实例。</summary>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.BadRequestResult.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>异步执行请求。</summary>
      <returns>用于完成执行操作的任务。</returns>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="P:System.Web.Http.Results.BadRequestResult.Request">
      <summary>获取导致此结果的请求消息。</summary>
      <returns>导致此结果的请求消息。</returns>
    </member>
    <member name="T:System.Web.Http.Results.ConflictResult">
      <summary>表示一个返回空“HttpStatusCode.Conflict”响应的操作结果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.ConflictResult.#ctor(System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.ConflictResult" /> 类的新实例。</summary>
      <param name="request">导致此结果的请求消息。</param>
    </member>
    <member name="M:System.Web.Http.Results.ConflictResult.#ctor(System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.ConflictResult" /> 类的新实例。</summary>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.ConflictResult.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>异步执行冲突结果的操作。</summary>
      <returns>异步执行指定的任务。</returns>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="P:System.Web.Http.Results.ConflictResult.Request">
      <summary>获取导致此结果的请求消息。</summary>
      <returns>导致此结果的 HTTP 请求消息。</returns>
    </member>
    <member name="T:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1">
      <summary>表示一个操作结果，该结果执行路由生成和内容协商，并在内容协商成功时返回 <see cref="F:System.Net.HttpStatusCode.Created" /> 响应。</summary>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},`0,System.Web.Http.ApiController)">
      <summary>使用提供的值初始化 <see cref="T:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1" /> 类的新实例。</summary>
      <param name="routeName">用于生成 URL 的路由的名称。</param>
      <param name="routeValues">用于生成 URL 的路由数据。</param>
      <param name="content">要在实体正文中协商和设置格式的内容值。</param>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},`0,System.Web.Http.Routing.UrlHelper,System.Net.Http.Formatting.IContentNegotiator,System.Net.Http.HttpRequestMessage,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>使用提供的值初始化 <see cref="T:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1" /> 类的新实例。</summary>
      <param name="routeName">用于生成 URL 的路由的名称。</param>
      <param name="routeValues">用于生成 URL 的路由数据。</param>
      <param name="content">要在实体正文中协商和设置格式的内容值。</param>
      <param name="urlFactory">用于生成路由 URL 的工厂。</param>
      <param name="contentNegotiator">用于处理内容协商的内容协商者。</param>
      <param name="request">导致此结果的请求消息。</param>
      <param name="formatters">用于协商内容及设置内容格式的格式化程序。</param>
    </member>
    <member name="P:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.Content">
      <summary>获取要在实体正文中协商和设置格式的内容值。</summary>
    </member>
    <member name="P:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.ContentNegotiator">
      <summary>获取用于处理内容协商的内容协商者。</summary>
    </member>
    <member name="M:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.ExecuteAsync(System.Threading.CancellationToken)"></member>
    <member name="P:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.Formatters">
      <summary>获取用于协商内容及设置内容格式的格式化程序。</summary>
    </member>
    <member name="P:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.Request">
      <summary>获取导致此结果的请求消息。</summary>
    </member>
    <member name="P:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.RouteName">
      <summary>获取用于生成 URL 的路由的名称。</summary>
    </member>
    <member name="P:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.RouteValues">
      <summary>获取用于生成 URL 的路由数据。</summary>
    </member>
    <member name="P:System.Web.Http.Results.CreatedAtRouteNegotiatedContentResult`1.UrlFactory">
      <summary>获取用于生成路由 URL 的工厂。</summary>
    </member>
    <member name="T:System.Web.Http.Results.CreatedNegotiatedContentResult`1">
      <summary>表示一个操作结果，该结果执行内容协商并在协商成功时返回 <see cref="F:System.Net.HttpStatusCode.Created" /> 响应。</summary>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Results.CreatedNegotiatedContentResult`1.#ctor(System.Uri,`0,System.Net.Http.Formatting.IContentNegotiator,System.Net.Http.HttpRequestMessage,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>使用提供的值初始化 <see cref="T:System.Web.Http.Results.OkNegotiatedContentResult`1" /> 类的新实例。</summary>
      <param name="location">要在实体正文中协商和设置格式的内容值。</param>
      <param name="content">在其中创建内容的位置。</param>
      <param name="contentNegotiator">用于处理内容协商的内容协商者。</param>
      <param name="request">导致此结果的请求消息。</param>
      <param name="formatters">用于协商内容及设置内容格式的格式化程序。</param>
    </member>
    <member name="M:System.Web.Http.Results.CreatedNegotiatedContentResult`1.#ctor(System.Uri,`0,System.Web.Http.ApiController)">
      <summary>使用提供的值初始化 <see cref="T:System.Web.Http.Results.CreatedNegotiatedContentResult`1" /> 类的新实例。</summary>
      <param name="location">在其中创建内容的位置。</param>
      <param name="content">要在实体正文中协商和设置格式的内容值。</param>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.CreatedNegotiatedContentResult`1.Content">
      <summary>获取要在实体正文中协商和设置格式的内容值。</summary>
      <returns>要在实体正文中协商和设置格式的内容值。</returns>
    </member>
    <member name="P:System.Web.Http.Results.CreatedNegotiatedContentResult`1.ContentNegotiator">
      <summary>获取用于处理内容协商的内容协商者。</summary>
      <returns>用于处理内容协商的内容协商者。</returns>
    </member>
    <member name="M:System.Web.Http.Results.CreatedNegotiatedContentResult`1.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>异步执行创建的协商内容结果的操作。</summary>
      <returns>异步执行返回值。</returns>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="P:System.Web.Http.Results.CreatedNegotiatedContentResult`1.Formatters">
      <summary>获取用于协商内容及设置内容格式的格式化程序。</summary>
      <returns>用于协商内容及设置内容格式的格式化程序。</returns>
    </member>
    <member name="P:System.Web.Http.Results.CreatedNegotiatedContentResult`1.Location">
      <summary>获取已在其中创建内容的位置。</summary>
      <returns>在其中创建内容的位置。</returns>
    </member>
    <member name="P:System.Web.Http.Results.CreatedNegotiatedContentResult`1.Request">
      <summary>获取导致此结果的请求消息。</summary>
      <returns>导致此结果的 HTTP 请求消息。</returns>
    </member>
    <member name="T:System.Web.Http.Results.ExceptionResult">
      <summary>表示一个操作结果，该结果返回 <see cref="F:System.Net.HttpStatusCode.InternalServerError" /> 响应，并基于 <see cref="P:System.Web.Http.Results.ExceptionResult.Exception" /> 在 <see cref="T:System.Web.Http.HttpError" /> 上执行内容协商。</summary>
    </member>
    <member name="M:System.Web.Http.Results.ExceptionResult.#ctor(System.Exception,System.Boolean,System.Net.Http.Formatting.IContentNegotiator,System.Net.Http.HttpRequestMessage,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>初始化 <see cref="T:System.Web.Http.Results.ExceptionResult" /> 类的新实例。</summary>
      <param name="exception">要在错误中包含的异常。</param>
      <param name="includeErrorDetail">如果错误应包括异常消息，则为 true；否则为 false。</param>
      <param name="contentNegotiator">用于处理内容协商的内容协商者。</param>
      <param name="request">导致此结果的请求消息。</param>
      <param name="formatters">用于协商内容及设置内容格式的格式化程序。</param>
    </member>
    <member name="M:System.Web.Http.Results.ExceptionResult.#ctor(System.Exception,System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.ExceptionResult" /> 类的新实例。</summary>
      <param name="exception">要在错误中包含的异常。</param>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.ExceptionResult.ContentNegotiator">
      <summary>获取用于处理内容协商的内容协商者。</summary>
      <returns>返回 <see cref="T:System.Net.Http.Formatting.IContentNegotiator" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.ExceptionResult.Exception">
      <summary>获取要在错误中包含的异常。</summary>
      <returns>返回 <see cref="T:System.Exception" />。</returns>
    </member>
    <member name="M:System.Web.Http.Results.ExceptionResult.ExecuteAsync(System.Threading.CancellationToken)">
      <returns>返回 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.ExceptionResult.Formatters">
      <summary>获取用于协商内容及设置内容格式的格式化程序。</summary>
      <returns>返回 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.ExceptionResult.IncludeErrorDetail">
      <summary>获取一个值，该值指示错误是否应包括异常消息。</summary>
      <returns>返回 <see cref="T:System.Boolean" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.ExceptionResult.Request">
      <summary>获取导致此结果的请求消息。</summary>
      <returns>返回 <see cref="T:System.Net.Http.HttpRequestMessage" />。</returns>
    </member>
    <member name="T:System.Web.Http.Results.FormattedContentResult`1">
      <summary>表示一个返回已设置格式的内容的操作结果。</summary>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Results.FormattedContentResult`1.#ctor(System.Net.HttpStatusCode,`0,System.Net.Http.Formatting.MediaTypeFormatter,System.Net.Http.Headers.MediaTypeHeaderValue,System.Net.Http.HttpRequestMessage)">
      <summary>使用提供的值初始化 <see cref="T:System.Web.Http.Results.FormattedContentResult`1" /> 类的新实例。</summary>
      <param name="statusCode">响应消息的 HTTP 状态代码。</param>
      <param name="content">要在实体正文中设置格式的内容值。</param>
      <param name="formatter">用于设置内容格式的格式化程序。</param>
      <param name="mediaType">Content-Type 标头的值，或者 <see cref="null" />（使格式化程序选择默认值）。</param>
      <param name="request">导致此结果的请求消息。</param>
    </member>
    <member name="M:System.Web.Http.Results.FormattedContentResult`1.#ctor(System.Net.HttpStatusCode,`0,System.Net.Http.Formatting.MediaTypeFormatter,System.Net.Http.Headers.MediaTypeHeaderValue,System.Web.Http.ApiController)">
      <summary>使用提供的值初始化 <see cref="T:System.Web.Http.Results.FormattedContentResult`1" /> 类的新实例。</summary>
      <param name="statusCode">响应消息的 HTTP 状态代码。</param>
      <param name="content">要在实体正文中设置格式的内容值。</param>
      <param name="formatter">用于设置内容格式的格式化程序。</param>
      <param name="mediaType">Content-Type 标头的值，或者 <see cref="null" />（使格式化程序选择默认值）。</param>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.FormattedContentResult`1.Content">
      <summary>获取要在实体正文中设置格式的内容值。</summary>
    </member>
    <member name="M:System.Web.Http.Results.FormattedContentResult`1.ExecuteAsync(System.Threading.CancellationToken)"></member>
    <member name="P:System.Web.Http.Results.FormattedContentResult`1.Formatter">
      <summary>获取用于设置内容格式的格式化程序。</summary>
    </member>
    <member name="P:System.Web.Http.Results.FormattedContentResult`1.MediaType">
      <summary>获取 Content-Type 标头的值或 <see cref="null" />（使格式化程序选择默认值）。</summary>
    </member>
    <member name="P:System.Web.Http.Results.FormattedContentResult`1.Request">
      <summary>获取导致此结果的请求消息。</summary>
    </member>
    <member name="P:System.Web.Http.Results.FormattedContentResult`1.StatusCode">
      <summary>获取响应消息的 HTTP 状态代码。</summary>
    </member>
    <member name="T:System.Web.Http.Results.InternalServerErrorResult">
      <summary>表示一个返回空 <see cref="F:System.Net.HttpStatusCode.InternalServerError" /> 响应的操作结果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.InternalServerErrorResult.#ctor(System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.InternalServerErrorResult" /> 类的新实例。</summary>
      <param name="request">导致此结果的请求消息。</param>
    </member>
    <member name="M:System.Web.Http.Results.InternalServerErrorResult.#ctor(System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.InternalServerErrorResult" /> 类的新实例。</summary>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.InternalServerErrorResult.ExecuteAsync(System.Threading.CancellationToken)">
      <returns>返回 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.InternalServerErrorResult.Request">
      <summary>获取导致此结果的请求消息。</summary>
      <returns>返回 <see cref="T:System.Net.Http.HttpRequestMessage" />。</returns>
    </member>
    <member name="T:System.Web.Http.Results.InvalidModelStateResult">
      <summary>表示一个操作结果，该结果返回 <see cref="F:System.Net.HttpStatusCode.BadRequest" /> 响应，并基于 <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" /> 在 <see cref="T:System.Web.Http.HttpError" /> 上执行内容协商。</summary>
    </member>
    <member name="M:System.Web.Http.Results.InvalidModelStateResult.#ctor(System.Web.Http.ModelBinding.ModelStateDictionary,System.Boolean,System.Net.Http.Formatting.IContentNegotiator,System.Net.Http.HttpRequestMessage,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>初始化 <see cref="T:System.Web.Http.Results.InvalidModelStateResult" /> 类的新实例。</summary>
      <param name="modelState">要在错误中包括的模型状态。</param>
      <param name="includeErrorDetail">如果错误应包括异常消息，则为 true；否则为 false。</param>
      <param name="contentNegotiator">用于处理内容协商的内容协商者。</param>
      <param name="request">导致此结果的请求消息。</param>
      <param name="formatters">用于协商内容及设置内容格式的格式化程序。</param>
    </member>
    <member name="M:System.Web.Http.Results.InvalidModelStateResult.#ctor(System.Web.Http.ModelBinding.ModelStateDictionary,System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.InvalidModelStateResult" /> 类的新实例。</summary>
      <param name="modelState">要在错误中包括的模型状态。</param>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.InvalidModelStateResult.ContentNegotiator">
      <summary>获取用于处理内容协商的内容协商者。</summary>
      <returns>用于处理内容协商的内容协商者。</returns>
    </member>
    <member name="M:System.Web.Http.Results.InvalidModelStateResult.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>异步创建响应消息。</summary>
      <returns>在完成时包含响应消息的任务。</returns>
      <param name="cancellationToken">要监视的取消请求标记。</param>
    </member>
    <member name="P:System.Web.Http.Results.InvalidModelStateResult.Formatters">
      <summary>获取用于协商内容及设置内容格式的格式化程序。</summary>
      <returns>用于协商内容及设置内容格式的格式化程序。</returns>
    </member>
    <member name="P:System.Web.Http.Results.InvalidModelStateResult.IncludeErrorDetail">
      <summary>获取一个值，该值指示错误是否应包括异常消息。</summary>
      <returns>如果错误应包括异常消息，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Http.Results.InvalidModelStateResult.ModelState">
      <summary>获取要在错误中包括的模型状态。</summary>
      <returns>要在错误中包括的模型状态。</returns>
    </member>
    <member name="P:System.Web.Http.Results.InvalidModelStateResult.Request">
      <summary>获取导致此结果的请求消息。</summary>
      <returns>导致此结果的请求消息。</returns>
    </member>
    <member name="T:System.Web.Http.Results.JsonResult`1">
      <summary>表示一个操作结果，该结果返回包含 JSON 数据的 <see cref="F:System.Net.HttpStatusCode.OK" /> 响应。</summary>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Results.JsonResult`1.#ctor(`0,Newtonsoft.Json.JsonSerializerSettings,System.Text.Encoding,System.Net.Http.HttpRequestMessage)">
      <summary>使用提供的值初始化 <see cref="T:System.Web.Http.Results.JsonResult`1" /> 类的新实例。</summary>
      <param name="content">要在实体正文中序列化的内容值。</param>
      <param name="serializerSettings">序列化程序设置。</param>
      <param name="encoding">内容编码。</param>
      <param name="request">导致此结果的请求消息。</param>
    </member>
    <member name="M:System.Web.Http.Results.JsonResult`1.#ctor(`0,Newtonsoft.Json.JsonSerializerSettings,System.Text.Encoding,System.Web.Http.ApiController)">
      <summary>使用提供的值初始化 <see cref="T:System.Web.Http.Results.JsonResult`1" /> 类的新实例。</summary>
      <param name="content">要在实体正文中序列化的内容值。</param>
      <param name="serializerSettings">序列化程序设置。</param>
      <param name="encoding">内容编码。</param>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.JsonResult`1.Content">
      <summary>获取要在实体正文中序列化的内容值。</summary>
      <returns>要在实体正文中序列化的内容值。</returns>
    </member>
    <member name="P:System.Web.Http.Results.JsonResult`1.Encoding">
      <summary>获取内容编码。</summary>
      <returns>内容编码。</returns>
    </member>
    <member name="M:System.Web.Http.Results.JsonResult`1.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>异步创建响应消息。</summary>
      <returns>在完成时包含响应消息的任务。</returns>
      <param name="cancellationToken">要监视的取消请求标记。</param>
    </member>
    <member name="P:System.Web.Http.Results.JsonResult`1.Request">
      <summary>获取导致此结果的请求消息。</summary>
      <returns>导致此结果的请求消息。</returns>
    </member>
    <member name="P:System.Web.Http.Results.JsonResult`1.SerializerSettings">
      <summary>获取序列化程序设置。</summary>
      <returns>序列化程序设置。</returns>
    </member>
    <member name="T:System.Web.Http.Results.NegotiatedContentResult`1">
      <summary>表示一个执行内容协商的操作结果。</summary>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Results.NegotiatedContentResult`1.#ctor(System.Net.HttpStatusCode,`0,System.Net.Http.Formatting.IContentNegotiator,System.Net.Http.HttpRequestMessage,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>使用提供的值初始化 <see cref="T:System.Web.Http.Results.NegotiatedContentResult`1" /> 类的新实例。</summary>
      <param name="statusCode">响应消息的 HTTP 状态代码。</param>
      <param name="content">要在实体正文中协商和设置格式的内容值。</param>
      <param name="contentNegotiator">用于处理内容协商的内容协商者。</param>
      <param name="request">导致此结果的请求消息。</param>
      <param name="formatters">用于协商内容及设置内容格式的格式化程序。</param>
    </member>
    <member name="M:System.Web.Http.Results.NegotiatedContentResult`1.#ctor(System.Net.HttpStatusCode,`0,System.Web.Http.ApiController)">
      <summary>使用提供的值初始化 <see cref="T:System.Web.Http.Results.NegotiatedContentResult`1" /> 类的新实例。</summary>
      <param name="statusCode">响应消息的 HTTP 状态代码。</param>
      <param name="content">要在实体正文中协商和设置格式的内容值。</param>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.NegotiatedContentResult`1.Content">
      <summary>获取要在实体正文中协商和设置格式的内容值。</summary>
      <returns>要在实体正文中协商和设置格式的内容值。</returns>
    </member>
    <member name="P:System.Web.Http.Results.NegotiatedContentResult`1.ContentNegotiator">
      <summary>获取用于处理内容协商的内容协商者。</summary>
      <returns>用于处理内容协商的内容协商者。</returns>
    </member>
    <member name="M:System.Web.Http.Results.NegotiatedContentResult`1.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>异步执行 HTTP 协商的内容结果。</summary>
      <returns>异步执行 HTTP 协商的内容结果。</returns>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="P:System.Web.Http.Results.NegotiatedContentResult`1.Formatters">
      <summary>获取用于协商内容及设置内容格式的格式化程序。</summary>
      <returns>用于协商内容及设置内容格式的格式化程序。</returns>
    </member>
    <member name="P:System.Web.Http.Results.NegotiatedContentResult`1.Request">
      <summary>获取导致此结果的请求消息。</summary>
      <returns>导致此结果的 HTTP 请求消息。</returns>
    </member>
    <member name="P:System.Web.Http.Results.NegotiatedContentResult`1.StatusCode">
      <summary>获取响应消息的 HTTP 状态代码。</summary>
      <returns>响应消息的 HTTP 状态代码。</returns>
    </member>
    <member name="T:System.Web.Http.Results.NotFoundResult">
      <summary>表示一个返回空 <see cref="F:System.Net.HttpStatusCode.NotFound" /> 响应的操作结果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.NotFoundResult.#ctor(System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.NotFoundResult" /> 类的新实例。</summary>
      <param name="request">导致此结果的请求消息。</param>
    </member>
    <member name="M:System.Web.Http.Results.NotFoundResult.#ctor(System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.NotFoundResult" /> 类的新实例。</summary>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.NotFoundResult.ExecuteAsync(System.Threading.CancellationToken)"></member>
    <member name="P:System.Web.Http.Results.NotFoundResult.Request">
      <summary>获取导致此结果的请求消息。</summary>
    </member>
    <member name="T:System.Web.Http.Results.OkNegotiatedContentResult`1">
      <summary>表示一个操作结果，该结果执行内容协商并在协商成功时返回“HttpStatusCode.OK”响应。</summary>
      <typeparam name="T">实体正文中的内容类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Results.OkNegotiatedContentResult`1.#ctor(`0,System.Net.Http.Formatting.IContentNegotiator,System.Net.Http.HttpRequestMessage,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>使用提供的值初始化 <see cref="T:System.Web.Http.Results.OkNegotiatedContentResult`1" /> 类的新实例。</summary>
      <param name="content">要在实体正文中协商和设置格式的内容值。</param>
      <param name="contentNegotiator">用于处理内容协商的内容协商者。</param>
      <param name="request">导致此结果的请求消息。</param>
      <param name="formatters">用于协商内容及设置内容格式的格式化程序。</param>
    </member>
    <member name="M:System.Web.Http.Results.OkNegotiatedContentResult`1.#ctor(`0,System.Web.Http.ApiController)">
      <summary>使用提供的值初始化 <see cref="T:System.Web.Http.Results.OkNegotiatedContentResult`1" /> 类的新实例。</summary>
      <param name="content">要在实体正文中协商和设置格式的内容值。</param>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.OkNegotiatedContentResult`1.Content">
      <summary>获取要在实体正文中协商和设置格式的内容值。</summary>
    </member>
    <member name="P:System.Web.Http.Results.OkNegotiatedContentResult`1.ContentNegotiator">
      <summary>获取用于处理内容协商的内容协商者。</summary>
    </member>
    <member name="M:System.Web.Http.Results.OkNegotiatedContentResult`1.ExecuteAsync(System.Threading.CancellationToken)"></member>
    <member name="P:System.Web.Http.Results.OkNegotiatedContentResult`1.Formatters">
      <summary>获取用于协商内容及设置内容格式的格式化程序。</summary>
    </member>
    <member name="P:System.Web.Http.Results.OkNegotiatedContentResult`1.Request">
      <summary>获取导致此结果的请求消息。</summary>
    </member>
    <member name="T:System.Web.Http.Results.OkResult">
      <summary>表示一个返回空“HttpStatusCode.OK”响应的操作结果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.OkResult.#ctor(System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.NotFoundResult" /> 类的新实例。</summary>
      <param name="request">导致此结果的请求消息。</param>
    </member>
    <member name="M:System.Web.Http.Results.OkResult.#ctor(System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.NotFoundResult" /> 类的新实例。</summary>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.OkResult.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>异步执行。</summary>
      <returns>返回任务。</returns>
      <param name="cancellationToken">取消标记。</param>
    </member>
    <member name="P:System.Web.Http.Results.OkResult.Request">
      <summary>获取结果的 HTTP 请求消息。</summary>
      <returns>结果的 HTTP 请求消息。</returns>
    </member>
    <member name="T:System.Web.Http.Results.RedirectResult">
      <summary>表示 &lt;see cref="F:System.Net.HttpStatusCode.Redirect"/&gt; 的操作结果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.RedirectResult.#ctor(System.Uri,System.Net.Http.HttpRequestMessage)">
      <summary>使用提供的值初始化 &lt;see cref="T:System.Web.Http.Results.RedirectResult"/&gt; 类的新实例。</summary>
      <param name="location">要重定向到的位置。</param>
      <param name="request">导致此结果的请求消息。</param>
    </member>
    <member name="M:System.Web.Http.Results.RedirectResult.#ctor(System.Uri,System.Web.Http.ApiController)">
      <summary>使用提供的值初始化 &lt;see cref="T:System.Web.Http.Results.RedirectResult"/&gt; 类的新实例。</summary>
      <param name="location">要重定向到的位置。</param>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.RedirectResult.ExecuteAsync(System.Threading.CancellationToken)">
      <returns>返回 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.RedirectResult.Location">
      <summary>获取已在其中创建内容的位置。</summary>
      <returns>返回 <see cref="T:System.Uri" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.RedirectResult.Request">
      <summary>获取导致此结果的请求消息。</summary>
      <returns>返回 <see cref="T:System.Net.Http.HttpRequestMessage" />。</returns>
    </member>
    <member name="T:System.Web.Http.Results.RedirectToRouteResult">
      <summary>表示一个执行路由生成并返回 &lt;see cref="F:System.Net.HttpStatusCode.Redirect"/&gt; 响应的操作结果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.RedirectToRouteResult.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.ApiController)">
      <summary>使用提供的值初始化 &lt;see cref="T:System.Web.Http.Results.RedirectToRouteResult"/&gt; 类的新实例。</summary>
      <param name="routeName">用于生成 URL 的路由的名称。</param>
      <param name="routeValues">用于生成 URL 的路由数据。</param>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.RedirectToRouteResult.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.UrlHelper,System.Net.Http.HttpRequestMessage)">
      <summary>使用提供的值初始化 &lt;see cref="T:System.Web.Http.Results.RedirectToRouteResult"/&gt; 类的新实例。</summary>
      <param name="routeName">用于生成 URL 的路由的名称。</param>
      <param name="routeValues">用于生成 URL 的路由数据。</param>
      <param name="urlFactory">用于生成路由 URL 的工厂。</param>
      <param name="request">导致此结果的请求消息。</param>
    </member>
    <member name="M:System.Web.Http.Results.RedirectToRouteResult.ExecuteAsync(System.Threading.CancellationToken)">
      <returns>返回 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.RedirectToRouteResult.Request">
      <summary>获取导致此结果的请求消息。</summary>
      <returns>返回 <see cref="T:System.Net.Http.HttpRequestMessage" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.RedirectToRouteResult.RouteName">
      <summary>获取用于生成 URL 的路由的名称。</summary>
      <returns>返回 <see cref="T:System.String" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.RedirectToRouteResult.RouteValues">
      <summary>获取用于生成 URL 的路由数据。</summary>
      <returns>返回 <see cref="T:System.Collections.Generic.IDictionary`2" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.RedirectToRouteResult.UrlFactory">
      <summary>获取用于生成路由 URL 的工厂。</summary>
      <returns>返回 <see cref="T:System.Web.Http.Routing.UrlHelper" />。</returns>
    </member>
    <member name="T:System.Web.Http.Results.ResponseMessageResult">
      <summary>表示一个返回指定响应消息的操作结果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.ResponseMessageResult.#ctor(System.Net.Http.HttpResponseMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.ResponseMessageResult" /> 类的新实例。</summary>
      <param name="response">响应消息。</param>
    </member>
    <member name="M:System.Web.Http.Results.ResponseMessageResult.ExecuteAsync(System.Threading.CancellationToken)"></member>
    <member name="P:System.Web.Http.Results.ResponseMessageResult.Response">
      <summary>获取响应消息。</summary>
    </member>
    <member name="T:System.Web.Http.Results.StatusCodeResult">
      <summary>表示一个返回指定 HTTP 状态代码的操作结果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.StatusCodeResult.#ctor(System.Net.HttpStatusCode,System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.StatusCodeResult" /> 类的新实例。</summary>
      <param name="statusCode">响应消息的 HTTP 状态代码。</param>
      <param name="request">导致此结果的请求消息。</param>
    </member>
    <member name="M:System.Web.Http.Results.StatusCodeResult.#ctor(System.Net.HttpStatusCode,System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.StatusCodeResult" /> 类的新实例。</summary>
      <param name="statusCode">响应消息的 HTTP 状态代码。</param>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="M:System.Web.Http.Results.StatusCodeResult.ExecuteAsync(System.Threading.CancellationToken)">
      <summary>异步创建响应消息。</summary>
      <returns>在完成时包含响应消息的任务。</returns>
      <param name="cancellationToken">要监视的取消请求标记。</param>
    </member>
    <member name="P:System.Web.Http.Results.StatusCodeResult.Request">
      <summary>获取导致此结果的请求消息。</summary>
      <returns>导致此结果的请求消息。</returns>
    </member>
    <member name="P:System.Web.Http.Results.StatusCodeResult.StatusCode">
      <summary>获取响应消息的 HTTP 状态代码。</summary>
      <returns>响应消息的 HTTP 状态代码。</returns>
    </member>
    <member name="T:System.Web.Http.Results.UnauthorizedResult">
      <summary>表示一个返回 <see cref="F:System.Net.HttpStatusCode.Unauthorized" /> 响应的操作结果。</summary>
    </member>
    <member name="M:System.Web.Http.Results.UnauthorizedResult.#ctor(System.Collections.Generic.IEnumerable{System.Net.Http.Headers.AuthenticationHeaderValue},System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.StatusCodeResult" /> 类的新实例。</summary>
      <param name="challenges">WWW-Authenticate 质询。</param>
      <param name="request">导致此结果的请求消息。</param>
    </member>
    <member name="M:System.Web.Http.Results.UnauthorizedResult.#ctor(System.Collections.Generic.IEnumerable{System.Net.Http.Headers.AuthenticationHeaderValue},System.Web.Http.ApiController)">
      <summary>初始化 <see cref="T:System.Web.Http.Results.StatusCodeResult" /> 类的新实例。</summary>
      <param name="challenges">WWW-Authenticate 质询。</param>
      <param name="controller">从中获取执行所需依赖项的控制器。</param>
    </member>
    <member name="P:System.Web.Http.Results.UnauthorizedResult.Challenges">
      <summary>获取 WWW-Authenticate 质询。</summary>
      <returns>返回 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</returns>
    </member>
    <member name="M:System.Web.Http.Results.UnauthorizedResult.ExecuteAsync(System.Threading.CancellationToken)">
      <returns>返回 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
    </member>
    <member name="P:System.Web.Http.Results.UnauthorizedResult.Request">
      <summary>获取导致此结果的请求消息。</summary>
      <returns>返回 <see cref="T:System.Net.Http.HttpRequestMessage" />。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.DefaultDirectRouteProvider">
      <summary>
        <see cref="T:System.Web.Http.Routing.IDirectRouteProvider" /> 的默认实现。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.DefaultDirectRouteProvider.#ctor"></member>
    <member name="M:System.Web.Http.Routing.DefaultDirectRouteProvider.GetActionDirectRoutes(System.Web.Http.Controllers.HttpActionDescriptor,System.Collections.Generic.IReadOnlyList{System.Web.Http.Routing.IDirectRouteFactory},System.Web.Http.Routing.IInlineConstraintResolver)">
      <summary>基于提供的工厂和操作，创建 <see cref="T:System.Web.Http.Routing.RouteEntry" /> 实例。路由项提供指向提供的操作的直接路由。</summary>
      <returns>一组路由项。</returns>
      <param name="actionDescriptor">操作描述符。</param>
      <param name="factories">直接路由工厂。</param>
      <param name="constraintResolver">约束解析程序。</param>
    </member>
    <member name="M:System.Web.Http.Routing.DefaultDirectRouteProvider.GetActionRouteFactories(System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>获取给定操作描述符的一组路由工厂。</summary>
      <returns>一组路由工厂。</returns>
      <param name="actionDescriptor">操作描述符。</param>
    </member>
    <member name="M:System.Web.Http.Routing.DefaultDirectRouteProvider.GetControllerDirectRoutes(System.Web.Http.Controllers.HttpControllerDescriptor,System.Collections.Generic.IReadOnlyList{System.Web.Http.Controllers.HttpActionDescriptor},System.Collections.Generic.IReadOnlyList{System.Web.Http.Routing.IDirectRouteFactory},System.Web.Http.Routing.IInlineConstraintResolver)">
      <summary>基于提供的工厂、控制器和操作，创建 <see cref="T:System.Web.Http.Routing.RouteEntry" /> 实例。路由项提供了指向所提供的控制器的直接路由，并可访问所提供的操作集。</summary>
      <returns>一组路由项。</returns>
      <param name="controllerDescriptor">控制器描述符。</param>
      <param name="actionDescriptors">操作描述符。</param>
      <param name="factories">直接路由工厂。</param>
      <param name="constraintResolver">约束解析程序。</param>
    </member>
    <member name="M:System.Web.Http.Routing.DefaultDirectRouteProvider.GetControllerRouteFactories(System.Web.Http.Controllers.HttpControllerDescriptor)">
      <summary>获取给定控制器描述符的路由工厂。</summary>
      <returns>一组路由工厂。</returns>
      <param name="controllerDescriptor">控制器描述符。</param>
    </member>
    <member name="M:System.Web.Http.Routing.DefaultDirectRouteProvider.GetDirectRoutes(System.Web.Http.Controllers.HttpControllerDescriptor,System.Collections.Generic.IReadOnlyList{System.Web.Http.Controllers.HttpActionDescriptor},System.Web.Http.Routing.IInlineConstraintResolver)">
      <summary>基于 <see cref="T:System.Web.Http.Routing.IDirectRouteFactory" /> 特性，获取给定控制器描述符和操作描述符的直接路由。</summary>
      <returns>一组路由项。</returns>
      <param name="controllerDescriptor">控制器描述符。</param>
      <param name="actionDescriptors">所有操作的操作描述符。</param>
      <param name="constraintResolver">约束解析程序。</param>
    </member>
    <member name="M:System.Web.Http.Routing.DefaultDirectRouteProvider.GetRoutePrefix(System.Web.Http.Controllers.HttpControllerDescriptor)">
      <summary>从提供的控制器中获取路由前缀。</summary>
      <returns>路由前缀或 null。</returns>
      <param name="controllerDescriptor">控制器描述符。</param>
    </member>
    <member name="T:System.Web.Http.Routing.DefaultInlineConstraintResolver">
      <summary>
        <see cref="T:System.Web.Http.Routing.IInlineConstraintResolver" /> 的默认实现。解析约束，方法是分析约束键和约束参数，使用映射解析约束类型，并对约束类型调用相应的构造函数。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.DefaultInlineConstraintResolver.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.DefaultInlineConstraintResolver" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.DefaultInlineConstraintResolver.ConstraintMap">
      <summary> 获取可将约束键映射到特定约束类型的可变字典。</summary>
      <returns>可将约束键映射到特定约束类型的可变字典。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.DefaultInlineConstraintResolver.ResolveConstraint(System.String)">
      <summary>解析内联约束。</summary>
      <returns>内联约束已解析成的 <see cref="T:System.Web.Http.Routing.IHttpRouteConstraint" />。</returns>
      <param name="inlineConstraint">要解析的内联约束。</param>
    </member>
    <member name="T:System.Web.Http.Routing.DirectRouteFactoryContext">
      <summary>表示支持创建直接路由的上下文。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.DirectRouteFactoryContext.#ctor(System.String,System.Collections.Generic.IReadOnlyCollection{System.Web.Http.Controllers.HttpActionDescriptor},System.Web.Http.Routing.IInlineConstraintResolver,System.Boolean)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.DirectRouteFactoryContext" /> 类的新实例。</summary>
      <param name="prefix">控制器定义的路由前缀（如果有）。</param>
      <param name="actions">要创建路由的操作描述符。</param>
      <param name="inlineConstraintResolver">内联约束解析程序。</param>
      <param name="targetIsAction">一个值，该值指示路由是在操作级别还是控制器级别配置的。</param>
    </member>
    <member name="P:System.Web.Http.Routing.DirectRouteFactoryContext.Actions">
      <summary>获取要创建路由的操作描述符。</summary>
      <returns>要创建路由的操作描述符。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.DirectRouteFactoryContext.CreateBuilder(System.String)">
      <summary>创建可构建与此上下文匹配的路由的路由生成器。</summary>
      <returns>可构建与此上下文匹配的路由的路由生成器。</returns>
      <param name="template">路由模板。</param>
    </member>
    <member name="M:System.Web.Http.Routing.DirectRouteFactoryContext.CreateBuilder(System.String,System.Web.Http.Routing.IInlineConstraintResolver)">
      <summary>创建可构建与此上下文匹配的路由的路由生成器。</summary>
      <returns>可构建与此上下文匹配的路由的路由生成器。</returns>
      <param name="template">路由模板。</param>
      <param name="constraintResolver">要使用的内联约束解析程序（如果有），否则为 null。</param>
    </member>
    <member name="P:System.Web.Http.Routing.DirectRouteFactoryContext.InlineConstraintResolver">
      <summary>获取内联约束解析程序。</summary>
      <returns>内联约束解析程序。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.DirectRouteFactoryContext.Prefix">
      <summary>获取控制器定义的路由前缀（如果有）。</summary>
      <returns>控制器定义的路由前缀（如果有）。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.DirectRouteFactoryContext.TargetIsAction">
      <summary>获取一个值，该值指示路由是在操作级别还是控制器级别配置的。</summary>
      <returns>如果路由是在操作级别配置的，则为 true；否则为 false（如果路由是在控制器级别配置的）。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.HttpMethodConstraint">
      <summary>允许您定义当 ASP.NET 路由确定 URL 是否与路由匹配时允许使用的 HTTP 谓词。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpMethodConstraint.#ctor(System.Net.Http.HttpMethod[])">
      <summary>使用允许对路由使用的 HTTP 谓词来初始化 <see cref="T:System.Web.Http.Routing.HttpMethodConstraint" /> 类的新实例。</summary>
      <param name="allowedMethods">对路由有效的 HTTP 谓词。</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpMethodConstraint.AllowedMethods">
      <summary>获取或设置允许对路由使用的 HTTP 谓词的集合。</summary>
      <returns>允许对路由使用的 HTTP 谓词的集合。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.HttpMethodConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>确定请求是否是使用某一个已允许对路由使用的 HTTP 谓词发出的。</summary>
      <returns>当 ASP.NET 路由正在处理请求时，如果该请求是使用允许的 HTTP 谓词发出的，则此值为 true；否则为 false。当 ASP.NET 路由正在构造 URL 时，如果提供的值包含与某一个允许的 HTTP 谓词匹配的 HTTP 谓词，则此值为 true；否则为 false。默认值为 true。</returns>
      <param name="request">为了确定其是否与 URL 匹配而检查的请求。</param>
      <param name="route">为了确定其是否与 URL 匹配而检查的对象。</param>
      <param name="parameterName">所检查的参数的名称。</param>
      <param name="values">一个包含路由参数的对象。</param>
      <param name="routeDirection">一个对象，用于指示当处理某个传入请求或生成某个 URL 时，是否正在执行约束检查。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpMethodConstraint.System#Web#Http#Routing#IHttpRouteConstraint#Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>确定请求是否是使用某一个已允许对路由使用的 HTTP 谓词发出的。</summary>
      <returns>当 ASP.NET 路由正在处理请求时，如果该请求是使用允许的 HTTP 谓词发出的，则此值为 true；否则为 false。当 ASP.NET 路由正在构造 URL 时，如果提供的值包含与某一个允许的 HTTP 谓词匹配的 HTTP 谓词，则此值为 true；否则为 false。默认值为 true。</returns>
      <param name="request">为了确定其是否与 URL 匹配而检查的请求。</param>
      <param name="route">为了确定其是否与 URL 匹配而检查的对象。</param>
      <param name="parameterName">所检查的参数的名称。</param>
      <param name="values">一个包含路由参数的对象。</param>
      <param name="routeDirection">一个对象，用于指示当处理某个传入请求或生成某个 URL 时，是否正在执行约束检查。</param>
    </member>
    <member name="T:System.Web.Http.Routing.HttpRoute">
      <summary>表示自承载（即在 ASP.NET 之外承载）的路由类。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRoute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRoute" /> 类的新实例。</summary>
      <param name="routeTemplate">路由模板。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor(System.String,System.Web.Http.Routing.HttpRouteValueDictionary)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRoute" /> 类的新实例。</summary>
      <param name="routeTemplate">路由模板。</param>
      <param name="defaults">路由参数的默认值。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor(System.String,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteValueDictionary)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRoute" /> 类的新实例。</summary>
      <param name="routeTemplate">路由模板。</param>
      <param name="defaults">路由参数的默认值。</param>
      <param name="constraints">路由参数的约束。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor(System.String,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteValueDictionary)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRoute" /> 类的新实例。</summary>
      <param name="routeTemplate">路由模板。</param>
      <param name="defaults">路由参数的默认值。</param>
      <param name="constraints">路由参数的约束。</param>
      <param name="dataTokens">路由参数的任何附加标记。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor(System.String,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteValueDictionary,System.Net.Http.HttpMessageHandler)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRoute" /> 类的新实例。</summary>
      <param name="routeTemplate">路由模板。</param>
      <param name="defaults">路由参数的默认值。</param>
      <param name="constraints">路由参数的约束。</param>
      <param name="dataTokens">路由参数的任何附加标记。</param>
      <param name="handler">将作为请求接收方的消息处理程序。</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRoute.Constraints">
      <summary>获取路由参数的约束。</summary>
      <returns>路由参数的约束。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRoute.DataTokens">
      <summary>获取任何不直接用于确定路由是否与传入的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 匹配的附加数据标记。</summary>
      <returns>任何不直接用于确定路由是否与传入的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 匹配的附加数据标记。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRoute.Defaults">
      <summary>获取路由参数的默认值（如果传入的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 未提供路由参数值）。</summary>
      <returns>路由参数的默认值（如果传入的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 未提供路由参数值）。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.GetRouteData(System.String,System.Net.Http.HttpRequestMessage)">
      <summary>通过查找路由的 <see cref="T:System.Web.Http.Routing.HttpRouteData" /> 来确定该路由是否是传入请求的匹配项。</summary>
      <returns>如果匹配，则为该路由的 <see cref="T:System.Web.Http.Routing.HttpRouteData" />；否则为 null。</returns>
      <param name="virtualPathRoot">虚拟路径根。</param>
      <param name="request">HTTP 请求。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.GetVirtualPath(System.Net.Http.HttpRequestMessage,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>尝试生成一个 URI，该 URI 表示基于 <see cref="T:System.Web.Http.Routing.HttpRouteData" /> 的当前值传入的值，并使用指定的 <see cref="T:System.Web.Http.Routing.HttpRoute" /> 生成新值。</summary>
      <returns>
        <see cref="T:System.Web.Http.Routing.HttpVirtualPathData" /> 实例或 null（如果无法生成 URI）。</returns>
      <param name="request">HTTP 请求消息。</param>
      <param name="values">路由值。</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRoute.Handler">
      <summary>获取或设置 http 路由处理程序。</summary>
      <returns>http 路由处理程序。</returns>
    </member>
    <member name="F:System.Web.Http.Routing.HttpRoute.HttpRouteKey">
      <summary>指定 HTTP 路由键。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.ProcessConstraint(System.Net.Http.HttpRequestMessage,System.Object,System.String,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteDirection)">
      <summary>确定此实例是否等于指定的路由。</summary>
      <returns>如果此实例等于指定的路由，则为 true；否则为 false。</returns>
      <param name="request">HTTP 请求。</param>
      <param name="constraint">路由参数的约束。</param>
      <param name="parameterName">参数名。</param>
      <param name="values">参数值的列表。</param>
      <param name="routeDirection">
        <see cref="System.Web.Http.Routing.HttpRouteDirection" /> 枚举的枚举值之一。</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRoute.RouteTemplate">
      <summary>获取描述要匹配的 URI 模式的路由模板。</summary>
      <returns>描述要匹配的 URI 模式的路由模板。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.HttpRouteData">
      <summary>封装有关 HTTP 路由的信息。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteData.#ctor(System.Web.Http.Routing.IHttpRoute)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRouteData" /> 类的新实例。</summary>
      <param name="route">一个定义路由的对象。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteData.#ctor(System.Web.Http.Routing.IHttpRoute,System.Web.Http.Routing.HttpRouteValueDictionary)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRouteData" /> 类的新实例。</summary>
      <param name="route">一个定义路由的对象。</param>
      <param name="values">值。</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRouteData.Route">
      <summary>获取表示路由的对象。</summary>
      <returns>表示路由的对象。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRouteData.Values">
      <summary>获取 URL 参数值和路由默认值的集合。</summary>
      <returns>包含从 URL 和默认值中分析得到的值的对象。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.HttpRouteDataExtensions">
      <summary>删除不具有路由数据中值的所有可选参数。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteDataExtensions.GetSubRoutes(System.Web.Http.Routing.IHttpRouteData)">
      <summary>如果某一路由实际是其他路由的联合，则返回子路由集。</summary>
      <returns>返回此路由中包含的子路由集。</returns>
      <param name="routeData">联合路由数据。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteDataExtensions.RemoveOptionalRoutingParameters(System.Web.Http.Routing.IHttpRouteData)">
      <summary>删除不具有路由数据中值的所有可选参数。</summary>
      <param name="routeData">要就地转变的路由数据。</param>
    </member>
    <member name="T:System.Web.Http.Routing.HttpRouteDirection">
      <summary>指定路由方向的枚举。</summary>
    </member>
    <member name="F:System.Web.Http.Routing.HttpRouteDirection.UriGeneration">
      <summary>UriGeneration 方向。</summary>
    </member>
    <member name="F:System.Web.Http.Routing.HttpRouteDirection.UriResolution">
      <summary>UriResolution 方向。</summary>
    </member>
    <member name="T:System.Web.Http.Routing.HttpRouteValueDictionary">
      <summary>表示指定键/值对的自承载的路由类。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteValueDictionary.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRouteValueDictionary" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteValueDictionary.#ctor(System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRouteValueDictionary" /> 类的新实例。</summary>
      <param name="dictionary">字典。</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteValueDictionary.#ctor(System.Object)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpRouteValueDictionary" /> 类的新实例。</summary>
      <param name="values">键值。</param>
    </member>
    <member name="T:System.Web.Http.Routing.HttpVirtualPathData">
      <summary>提供了有关 HTTP 虚拟路径的数据。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpVirtualPathData.#ctor(System.Web.Http.Routing.IHttpRoute,System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.HttpVirtualPathData" /> 类的新实例。</summary>
      <param name="route">虚拟路径的路由。</param>
      <param name="virtualPath">从路由定义创建的 URL。</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpVirtualPathData.Route">
      <summary>获取或设置虚拟路径的路由。</summary>
      <returns>虚拟路径的路由。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.HttpVirtualPathData.VirtualPath">
      <summary>获取或设置从路由定义创建的 URL。</summary>
      <returns>从路由定义创建的 URL。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.IDirectRouteBuilder">
      <summary>定义用于创建指向操作的直接路由（特性路由）的生成器。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.Actions">
      <summary>获取要创建路由的操作描述符。</summary>
      <returns>要创建路由的操作描述符。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.IDirectRouteBuilder.Build">
      <summary>根据当前属性值创建路由项。</summary>
      <returns>创建的路由项。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.Constraints">
      <summary>获取或设置路由约束。</summary>
      <returns>路由约束。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.DataTokens">
      <summary>获取或设置路由数据标记。</summary>
      <returns>路由数据标记。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.Defaults">
      <summary>获取或设置路由默认值。</summary>
      <returns>路由默认值。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.Name">
      <summary>获取或设置路由名称（如果有）；否则为 null。</summary>
      <returns>如果找到路由名称，则为该路由名称；否则为 null。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.Order">
      <summary>获取或设置路由顺序。</summary>
      <returns>路由顺序。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.Precedence">
      <summary>获取或设置路由优先顺序。</summary>
      <returns>路由优先顺序。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.TargetIsAction">
      <summary>获取一个值，该值指示路由是在操作级别还是控制器级别配置的。</summary>
      <returns>如果路由是在操作级别配置的，则为 true；否则为 false（如果路由是在控制器级别配置的）。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IDirectRouteBuilder.Template">
      <summary>获取或设置路由模板。</summary>
      <returns>路由模板。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.IDirectRouteFactory">
      <summary>定义用于创建直接指向一组操作描述符的路由（特性路由）的工厂。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.IDirectRouteFactory.CreateRoute(System.Web.Http.Routing.DirectRouteFactoryContext)">
      <summary>创建直接路由项。</summary>
      <returns>直接路由项。</returns>
      <param name="context">要用于创建路由的上下文。</param>
    </member>
    <member name="T:System.Web.Http.Routing.IDirectRouteProvider">
      <summary>定义直接以操作描述符为目标的路由（特性路由）的提供程序。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.IDirectRouteProvider.GetDirectRoutes(System.Web.Http.Controllers.HttpControllerDescriptor,System.Collections.Generic.IReadOnlyList{System.Web.Http.Controllers.HttpActionDescriptor},System.Web.Http.Routing.IInlineConstraintResolver)">
      <summary>获取控制器的直接路由。</summary>
      <returns>控制器的一组路由项。</returns>
      <param name="controllerDescriptor">控制器描述符。</param>
      <param name="actionDescriptors">操作描述符。</param>
      <param name="constraintResolver">内联约束解析程序。</param>
    </member>
    <member name="T:System.Web.Http.Routing.IHttpRoute">
      <summary>
        <see cref="T:System.Web.Http.Routing.IHttpRoute" /> 为一个表示如何将传入的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 映射到特定控制器和操作的路由定义了接口。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRoute.Constraints">
      <summary> 获取路由参数的约束。</summary>
      <returns>路由参数的约束。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRoute.DataTokens">
      <summary> 获取任何不直接用于确定路由是否与传入的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 匹配的附加数据标记。</summary>
      <returns>附加数据标记。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRoute.Defaults">
      <summary> 获取路由参数的默认值（如果传入的 <see cref="T:System.Net.Http.HttpRequestMessage" /> 未提供路由参数值）。</summary>
      <returns>路由参数的默认值。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.IHttpRoute.GetRouteData(System.String,System.Net.Http.HttpRequestMessage)">
      <summary> 通过查找路由的 &lt;see cref="!:IRouteData" /&gt;，确定传入请求是否匹配此路由。</summary>
      <returns>如果匹配，则为路由的 &lt;see cref="!:RouteData" /&gt;；否则为 null。</returns>
      <param name="virtualPathRoot">虚拟路径根。</param>
      <param name="request">请求。</param>
    </member>
    <member name="M:System.Web.Http.Routing.IHttpRoute.GetVirtualPath(System.Net.Http.HttpRequestMessage,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>基于所提供的路由和值获取虚拟路径数据。</summary>
      <returns>虚拟路径数据。</returns>
      <param name="request">请求消息。</param>
      <param name="values">值。</param>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRoute.Handler">
      <summary>获取将作为请求接收方的消息处理程序。</summary>
      <returns>消息处理程序。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRoute.RouteTemplate">
      <summary> 获取描述要匹配的 URI 模式的路由模板。</summary>
      <returns>路由模板。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.IHttpRouteConstraint">
      <summary>表示基类路由约束。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.IHttpRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>确定此实例是否等于指定的路由。</summary>
      <returns>如果此实例等于指定的路由，则为 True；否则为 false。</returns>
      <param name="request">请求。</param>
      <param name="route">要比较的路由。</param>
      <param name="parameterName">参数名。</param>
      <param name="values">参数值的列表。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="T:System.Web.Http.Routing.IHttpRouteData">
      <summary>提供有关路由的信息。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRouteData.Route">
      <summary>获取表示路由的对象。</summary>
      <returns>表示路由的对象。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRouteData.Values">
      <summary>获取 URL 参数值和路由默认值的集合。</summary>
      <returns>从 URL 分析出来的值以及来自默认值的值。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.IHttpRouteInfoProvider">
      <summary> 提供用于定义路由的信息。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRouteInfoProvider.Name">
      <summary> 获取要生成的路由的名称。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRouteInfoProvider.Order">
      <summary> 获取路由相对于其他路由的顺序。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRouteInfoProvider.Template">
      <summary> 获取描述要匹配的 URI 模式的路由模板。 </summary>
    </member>
    <member name="T:System.Web.Http.Routing.IHttpVirtualPathData">
      <summary>定义 HTTP 路由的属性。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpVirtualPathData.Route">
      <summary>获取 HTTP 路由。</summary>
      <returns>HTTP 路由。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpVirtualPathData.VirtualPath">
      <summary>获取表示当前 HTTP 路由的虚拟路径的 URI。</summary>
      <returns>表示当前 HTTP 路由的虚拟路径的 URI。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.IInlineConstraintResolver">
      <summary> 定义用于将内联约束解析为 <see cref="T:System.Web.Http.Routing.IHttpRouteConstraint" /> 实例的抽象。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.IInlineConstraintResolver.ResolveConstraint(System.String)">
      <summary> 解析内联约束。</summary>
      <returns>内联约束已解析成的 <see cref="T:System.Web.Http.Routing.IHttpRouteConstraint" />。</returns>
      <param name="inlineConstraint">要解析的内联约束。</param>
    </member>
    <member name="T:System.Web.Http.Routing.IRoutePrefix">
      <summary>定义路由前缀。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IRoutePrefix.Prefix">
      <summary>获取路由前缀。</summary>
      <returns>路由前缀。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.RouteEntry">
      <summary>表示指定的路由。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.RouteEntry.#ctor(System.String,System.Web.Http.Routing.IHttpRoute)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.RouteEntry" /> 类的新实例。</summary>
      <param name="name">如果找到路由名称，则为该路由名称；否则为 null。</param>
      <param name="route">路由。</param>
    </member>
    <member name="P:System.Web.Http.Routing.RouteEntry.Name">
      <summary>获取路由名称（如果有）；否则为 null。</summary>
      <returns>如果找到路由名称，则为该路由名称；否则为 null。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.RouteEntry.Route">
      <summary>获取路由。</summary>
      <returns>路由。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.RouteFactoryAttribute">
      <summary>表示可能包含自定义约束的特性路由。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.RouteFactoryAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.RouteFactoryAttribute" /> 类的新实例。</summary>
      <param name="template">路由模板。</param>
    </member>
    <member name="P:System.Web.Http.Routing.RouteFactoryAttribute.Constraints">
      <summary>获取路由约束（如果有）；否则为 null。</summary>
      <returns>如果找到路由约束，则为该路由约束；否则为 null。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.RouteFactoryAttribute.CreateRoute(System.Web.Http.Routing.DirectRouteFactoryContext)">
      <summary>创建路由项</summary>
      <returns>创建的路由项。</returns>
      <param name="context">上下文。</param>
    </member>
    <member name="P:System.Web.Http.Routing.RouteFactoryAttribute.DataTokens">
      <summary>获取路由数据标记（如果有）；否则为 null。</summary>
      <returns>如果找到路由数据标记，则为该路由数据标记；否则为 null。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.RouteFactoryAttribute.Defaults">
      <summary>获取路由默认值（如果有）；否则为 null。</summary>
      <returns>路由默认值（如果有）；否则为 null。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.RouteFactoryAttribute.Name">
      <summary>获取或设置路由名称（如果有）；否则为 null。</summary>
      <returns>如果找到路由名称，则为该路由名称；否则为 null。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.RouteFactoryAttribute.Order">
      <summary>获取或设置路由顺序。</summary>
      <returns>路由顺序。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.RouteFactoryAttribute.Template">
      <summary>获取路由模板。</summary>
      <returns>路由模板。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.StopRoutingHandler">
      <summary>表示一个处理程序，该处理程序指定路由不应处理路由模板的请求。当路由以处理程序形式提供此类时，将忽略与该路由匹配的请求。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.StopRoutingHandler.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.StopRoutingHandler" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Web.Http.Routing.UrlHelper">
      <summary>表示用于创建 URL 的工厂。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.#ctor">
      <summary> 初始化 <see cref="T:System.Web.Http.Routing.UrlHelper" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.#ctor(System.Net.Http.HttpRequestMessage)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.UrlHelper" /> 类的新实例。</summary>
      <param name="request">此实例的 HTTP 请求。</param>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.Content(System.String)">
      <summary>使用指定的路径创建绝对 URL。</summary>
      <returns>生成的 URL。</returns>
      <param name="path">URL 路径，可以是相对 URL、带根目录的 URL 或虚拟路径。</param>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.Link(System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>返回指定路由的链接。</summary>
      <returns>指定路由的链接。</returns>
      <param name="routeName">路由的名称。</param>
      <param name="routeValues">一个包含路由参数的对象。</param>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.Link(System.String,System.Object)">
      <summary>返回指定路由的链接。</summary>
      <returns>指定路由的链接。</returns>
      <param name="routeName">路由的名称。</param>
      <param name="routeValues">路由值。</param>
    </member>
    <member name="P:System.Web.Http.Routing.UrlHelper.Request">
      <summary>获取或设置当前 <see cref="T:System.Web.Http.Routing.UrlHelper" /> 实例的 <see cref="T:System.Net.Http.HttpRequestMessage" />。</summary>
      <returns>当前实例的 <see cref="T:System.Net.Http.HttpRequestMessage" />。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.Route(System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>返回 <see cref="T:System.Web.Http.Routing.UrlHelper" /> 的路由。</summary>
      <returns>
        <see cref="T:System.Web.Http.Routing.UrlHelper" /> 的路由。</returns>
      <param name="routeName">路由的名称。</param>
      <param name="routeValues">路由值列表。</param>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.Route(System.String,System.Object)">
      <summary>返回 <see cref="T:System.Web.Http.Routing.UrlHelper" /> 的路由。</summary>
      <returns>
        <see cref="T:System.Web.Http.Routing.UrlHelper" /> 的路由。</returns>
      <param name="routeName">路由的名称。</param>
      <param name="routeValues">路由值。</param>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.AlphaRouteConstraint">
      <summary>约束某个路由参数，使之仅包含小写或大写英文字母 A 到 Z。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.AlphaRouteConstraint.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.AlphaRouteConstraint" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.BoolRouteConstraint">
      <summary>约束某个路由参数，使之仅代表布尔值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.BoolRouteConstraint.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.BoolRouteConstraint" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.BoolRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>确定此实例是否等于指定的路由。</summary>
      <returns>如果此实例等于指定的路由，则为 true；否则为 false。</returns>
      <param name="request">请求。</param>
      <param name="route">要比较的路由。</param>
      <param name="parameterName">参数名。</param>
      <param name="values">参数值的列表。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.CompoundRouteConstraint">
      <summary>按多个子约束来约束路由。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.CompoundRouteConstraint.#ctor(System.Collections.Generic.IList{System.Web.Http.Routing.IHttpRouteConstraint})">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.CompoundRouteConstraint" /> 类的新实例。</summary>
      <param name="constraints">该约束匹配时也必须匹配的子约束。</param>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.CompoundRouteConstraint.Constraints">
      <summary>获取该约束匹配时也必须匹配的子约束。</summary>
      <returns>该约束匹配时也必须匹配的子约束。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.CompoundRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>确定此实例是否等于指定的路由。</summary>
      <returns>如果此实例等于指定的路由，则为 true；否则为 false。</returns>
      <param name="request">请求。</param>
      <param name="route">要比较的路由。</param>
      <param name="parameterName">参数名。</param>
      <param name="values">参数值的列表。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.DateTimeRouteConstraint">
      <summary>约束某个路由参数，使之仅代表 <see cref="T:System.DateTime" /> 值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.DateTimeRouteConstraint.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.DateTimeRouteConstraint" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.DateTimeRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>确定此实例是否等于指定的路由。</summary>
      <returns>如果此实例等于指定的路由，则为 true；否则为 false。</returns>
      <param name="request">请求。</param>
      <param name="route">要比较的路由。</param>
      <param name="parameterName">参数名。</param>
      <param name="values">参数值的列表。</param>
      <param name="routeDirection">方向路由。</param>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.DecimalRouteConstraint">
      <summary>约束某个路由参数，使之仅代表小数值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.DecimalRouteConstraint.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.DecimalRouteConstraint" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.DecimalRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>确定此实例是否等于指定的路由。</summary>
      <returns>如果此实例等于指定的路由，则为 true；否则为 false。</returns>
      <param name="request">请求。</param>
      <param name="route">要比较的路由。</param>
      <param name="parameterName">参数名。</param>
      <param name="values">参数值的列表。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.DoubleRouteConstraint">
      <summary>约束某个路由参数，使之仅代表 64 位浮点值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.DoubleRouteConstraint.#ctor"></member>
    <member name="M:System.Web.Http.Routing.Constraints.DoubleRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)"></member>
    <member name="T:System.Web.Http.Routing.Constraints.FloatRouteConstraint">
      <summary>约束某个路由参数，使之仅代表 32 位浮点值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.FloatRouteConstraint.#ctor"></member>
    <member name="M:System.Web.Http.Routing.Constraints.FloatRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)"></member>
    <member name="T:System.Web.Http.Routing.Constraints.GuidRouteConstraint">
      <summary>约束某个路由参数，使之仅代表 <see cref="T:System.Guid" /> 值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.GuidRouteConstraint.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.GuidRouteConstraint" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.GuidRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>确定此实例是否等于指定的路由。</summary>
      <returns>如果此实例等于指定的路由，则为 true；否则为 false。</returns>
      <param name="request">请求。</param>
      <param name="route">要比较的路由。</param>
      <param name="parameterName">参数名。</param>
      <param name="values">参数值的列表。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.IntRouteConstraint">
      <summary>约束某个路由参数，使之仅代表 32 位整数值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.IntRouteConstraint.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.IntRouteConstraint" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.IntRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>确定此实例是否等于指定的路由。</summary>
      <returns>如果此实例等于指定的路由，则为 true；否则为 false。</returns>
      <param name="request">请求。</param>
      <param name="route">要比较的路由。</param>
      <param name="parameterName">参数名。</param>
      <param name="values">参数值的列表。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.LengthRouteConstraint">
      <summary>将路由参数约束为具有给定长度的或者位于给定长度范围内的字符串。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.LengthRouteConstraint.#ctor(System.Int32)"></member>
    <member name="M:System.Web.Http.Routing.Constraints.LengthRouteConstraint.#ctor(System.Int32,System.Int32)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.LengthRouteConstraint" /> 类的新实例，该类将路由参数约束为具有给定长度的字符串。</summary>
      <param name="minLength">路由参数的最小长度。</param>
      <param name="maxLength">路由参数的最大长度。</param>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.LengthRouteConstraint.Length">
      <summary>获取路由参数的长度（如果已设置）。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.LengthRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)"></member>
    <member name="P:System.Web.Http.Routing.Constraints.LengthRouteConstraint.MaxLength">
      <summary>获取路由参数的最大长度（如果已设置）。</summary>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.LengthRouteConstraint.MinLength">
      <summary>获取路由参数的最小长度（如果已设置）。</summary>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.LongRouteConstraint">
      <summary>约束某个路由参数，使之仅代表 64 位整数值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.LongRouteConstraint.#ctor"></member>
    <member name="M:System.Web.Http.Routing.Constraints.LongRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)"></member>
    <member name="T:System.Web.Http.Routing.Constraints.MaxLengthRouteConstraint">
      <summary>将路由参数约束为有一个最大长度的字符串。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.MaxLengthRouteConstraint.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.MaxLengthRouteConstraint" /> 类的新实例。</summary>
      <param name="maxLength">最大长度。</param>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.MaxLengthRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>确定此实例是否等于指定的路由。</summary>
      <returns>如果此实例等于指定的路由，则为 true；否则为 false。</returns>
      <param name="request">请求。</param>
      <param name="route">要比较的路由。</param>
      <param name="parameterName">参数名。</param>
      <param name="values">参数值的列表。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.MaxLengthRouteConstraint.MaxLength">
      <summary>获取路由参数的最大长度。</summary>
      <returns>路由参数的最大长度。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.MaxRouteConstraint">
      <summary>将路由参数约束为有一个最大值的整数。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.MaxRouteConstraint.#ctor(System.Int64)"></member>
    <member name="M:System.Web.Http.Routing.Constraints.MaxRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)"></member>
    <member name="P:System.Web.Http.Routing.Constraints.MaxRouteConstraint.Max">
      <summary>获取路由参数的最大值。</summary>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.MinLengthRouteConstraint">
      <summary>将路由参数约束为有一个最大长度的字符串。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.MinLengthRouteConstraint.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.MinLengthRouteConstraint" /> 类的新实例。</summary>
      <param name="minLength">最小长度。</param>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.MinLengthRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>确定此实例是否等于指定的路由。</summary>
      <returns>如果此实例等于指定的路由，则为 true；否则为 false。</returns>
      <param name="request">请求。</param>
      <param name="route">要比较的路由。</param>
      <param name="parameterName">参数名。</param>
      <param name="values">参数值的列表。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.MinLengthRouteConstraint.MinLength">
      <summary>获取路由参数的最小长度。</summary>
      <returns>路由参数的最小长度。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.MinRouteConstraint">
      <summary>将路由参数约束为有一个最小值的长型值。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.MinRouteConstraint.#ctor(System.Int64)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.MinRouteConstraint" /> 类的新实例。</summary>
      <param name="min">路由参数的最小值。</param>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.MinRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>确定此实例是否等于指定的路由。</summary>
      <returns>如果此实例等于指定的路由，则为 true；否则为 false。</returns>
      <param name="request">请求。</param>
      <param name="route">要比较的路由。</param>
      <param name="parameterName">参数名。</param>
      <param name="values">参数值的列表。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.MinRouteConstraint.Min">
      <summary>获取路由参数的最小值。</summary>
      <returns>路由参数的最小值。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.OptionalRouteConstraint">
      <summary>根据在可选参数设置为默认值时不会失败的内部约束来约束路由。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.OptionalRouteConstraint.#ctor(System.Web.Http.Routing.IHttpRouteConstraint)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.OptionalRouteConstraint" /> 类的新实例。</summary>
      <param name="innerConstraint">当参数不是无值的可选参数时要匹配的内部约束。</param>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.OptionalRouteConstraint.InnerConstraint">
      <summary>获取当参数不是无值的可选参数时要匹配的内部约束。</summary>
      <returns>当参数不是无值的可选参数时要匹配的内部约束。</returns>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.OptionalRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>确定此实例是否等于指定的路由。</summary>
      <returns>如果此实例等于指定的路由，则为 true；否则为 false。</returns>
      <param name="request">请求。</param>
      <param name="route">要比较的路由。</param>
      <param name="parameterName">参数名。</param>
      <param name="values">参数值的列表。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.RangeRouteConstraint">
      <summary>将路由参数约束为给定值范围内的某个整数。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.RangeRouteConstraint.#ctor(System.Int64,System.Int64)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.RangeRouteConstraint" /> 类的新实例。</summary>
      <param name="min">最小值。</param>
      <param name="max">最大值。</param>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.RangeRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>确定此实例是否等于指定的路由。</summary>
      <returns>如果此实例等于指定的路由，则为 true；否则为 false。</returns>
      <param name="request">请求。</param>
      <param name="route">要比较的路由。</param>
      <param name="parameterName">参数名。</param>
      <param name="values">参数值的列表。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.RangeRouteConstraint.Max">
      <summary>获取路由参数的最大值。</summary>
      <returns>路由参数的最大值。</returns>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.RangeRouteConstraint.Min">
      <summary>获取路由参数的最小值。</summary>
      <returns>路由参数的最小值。</returns>
    </member>
    <member name="T:System.Web.Http.Routing.Constraints.RegexRouteConstraint">
      <summary>约束某个路由参数以匹配正则表达式。</summary>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.RegexRouteConstraint.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.Routing.Constraints.RegexRouteConstraint" /> 类的新实例。</summary>
      <param name="pattern">模式。</param>
    </member>
    <member name="M:System.Web.Http.Routing.Constraints.RegexRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>确定此实例是否等于指定的路由。</summary>
      <returns>如果此实例等于指定的路由，则为 true；否则为 false。</returns>
      <param name="request">请求。</param>
      <param name="route">要比较的路由。</param>
      <param name="parameterName">参数名。</param>
      <param name="values">参数值的列表。</param>
      <param name="routeDirection">路由方向。</param>
    </member>
    <member name="P:System.Web.Http.Routing.Constraints.RegexRouteConstraint.Pattern">
      <summary>获取要匹配的正则表达式模式。</summary>
      <returns>要匹配的正则表达式模式。</returns>
    </member>
    <member name="T:System.Web.Http.Services.Decorator">
      <summary> 提供一个方法，用于检索可由 &lt;see cref="T:System.Web.Http.Services.IDecorator`1" /&gt; 包装的对象的最内层对象。</summary>
    </member>
    <member name="M:System.Web.Http.Services.Decorator.GetInner``1(``0)">
      <summary> 获取不实现 &lt;see cref="T:System.Web.Http.Services.IDecorator`1" /&gt; 的最内层对象。</summary>
      <param name="outer">需要解封的对象。</param>
      <typeparam name="T"></typeparam>
    </member>
    <member name="T:System.Web.Http.Services.DefaultServices">
      <summary>表示由 <see cref="T:System.Web.Http.HttpConfiguration" /> 使用的服务实例的容器。请注意，此容器仅支持已知的类型，在调用用于获取或设置任意服务类型的方法时将引发 <see cref="T:System.ArgumentException" />。对于任意类型的创建，请改用 <see cref="T:System.Web.Http.Dependencies.IDependencyResolver" />。此容器支持的类型包括：<see cref="T:System.Web.Http.Controllers.IActionValueBinder" /><see cref="T:System.Web.Http.Description.IApiExplorer" /><see cref="T:System.Web.Http.Dispatcher.IAssembliesResolver" /><see cref="T:System.Web.Http.Validation.IBodyModelValidator" /><see cref="T:System.Net.Http.Formatting.IContentNegotiator" /><see cref="T:System.Web.Http.Description.IDocumentationProvider" /><see cref="T:System.Web.Http.Filters.IFilterProvider" /><see cref="T:System.Web.Http.Hosting.IHostBufferPolicySelector" /><see cref="T:System.Web.Http.Controllers.IHttpActionInvoker" /><see cref="T:System.Web.Http.Controllers.IHttpActionSelector" /><see cref="T:System.Web.Http.Dispatcher.IHttpControllerActivator" /><see cref="T:System.Web.Http.Dispatcher.IHttpControllerSelector" /><see cref="T:System.Web.Http.Dispatcher.IHttpControllerTypeResolver" /><see cref="T:System.Web.Http.Tracing.ITraceManager" /><see cref="T:System.Web.Http.Tracing.ITraceWriter" /><see cref="T:System.Web.Http.Query.IStructuredQueryBuilder" /><see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /><see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" /><see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /><see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" />如果将不在上述列表中的任何类型传递给此接口中的任何方法，将导致引发 <see cref="T:System.ArgumentException" />。</summary>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Services.DefaultServices" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.#ctor(System.Web.Http.HttpConfiguration)">
      <summary>使用指定的 <see cref="T:System.Web.Http.HttpConfiguration" /> 对象来初始化 <see cref="T:System.Web.Http.Services.DefaultServices" /> 类的新实例。</summary>
      <param name="configuration">
        <see cref="T:System.Web.Http.HttpConfiguration" /> 对象。</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.ClearSingle(System.Type)">
      <summary>从默认服务中删除单实例服务。</summary>
      <param name="serviceType">服务的类型。</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.GetService(System.Type)">
      <summary>获取指定类型的服务。</summary>
      <returns>服务的第一个实例；如果找不到该服务，则为 null。</returns>
      <param name="serviceType">服务的类型。</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.GetServiceInstances(System.Type)">
      <summary>获取给定服务类型的服务对象的列表，并验证该服务类型。</summary>
      <returns>指定类型的服务对象的列表。</returns>
      <param name="serviceType">服务类型。</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.GetServices(System.Type)">
      <summary>获取给定服务类型的服务对象的列表。</summary>
      <returns>指定类型的服务对象的列表；如果未找到该服务，则为空列表。</returns>
      <param name="serviceType">服务的类型。</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.IsSingleService(System.Type)">
      <summary>查询服务类型是否为单实例。</summary>
      <returns>如果该服务类型最多只有一个实例，则为 true；如果该服务类型支持多个实例，则为 false。</returns>
      <param name="serviceType">服务类型。</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.ReplaceSingle(System.Type,System.Object)">
      <summary>替换单实例服务对象。</summary>
      <param name="serviceType">服务类型。</param>
      <param name="service">服务对象，用于替换以前的实例。</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.ResetCache(System.Type)">
      <summary>删除单个服务类型的缓存值。</summary>
      <param name="serviceType">服务类型。</param>
    </member>
    <member name="T:System.Web.Http.Services.IDecorator`1">
      <summary> 定义一个用于公开内部修饰对象的修饰器。</summary>
      <typeparam name="T">此类型参数是协变。即可以使用指定的类型或派生程度更高的类型。 有关协变和逆变的详细信息，请参阅。</typeparam>
    </member>
    <member name="P:System.Web.Http.Services.IDecorator`1.Inner">
      <summary> 获取内部对象。 </summary>
    </member>
    <member name="T:System.Web.Http.Tracing.ITraceManager">
      <summary>表示用于记录方法入口/出口和持续时间的性能跟踪类。</summary>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceManager.Initialize(System.Web.Http.HttpConfiguration)">
      <summary>使用指定的配置初始化 <see cref="T:System.Web.Http.Tracing.ITraceManager" /> 类。</summary>
      <param name="configuration">配置。</param>
    </member>
    <member name="T:System.Web.Http.Tracing.ITraceWriter">
      <summary>表示跟踪编写器。</summary>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriter.Trace(System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.Action{System.Web.Http.Tracing.TraceRecord})">
      <summary> 当且仅当在给定 category 和 level 允许跟踪时，调用指定的 traceAction 以允许在新的 <see cref="T:System.Web.Http.Tracing.TraceRecord" /> 中设置值。</summary>
      <param name="request">当前 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null，但这样做将阻止后续跟踪分析将跟踪与特定请求关联。</param>
      <param name="category">跟踪的逻辑类别。用户可以定义自己的跟踪。</param>
      <param name="level">写入此跟踪时所在的 <see cref="T:System.Web.Http.Tracing.TraceLevel" />。</param>
      <param name="traceAction">启用了跟踪时要调用的操作。在此操作中，调用方应填充给定 <see cref="T:System.Web.Http.Tracing.TraceRecord" /> 的各个字段。</param>
    </member>
    <member name="T:System.Web.Http.Tracing.ITraceWriterExtensions">
      <summary>表示 <see cref="T:System.Web.Http.Tracing.ITraceWriter" /> 的扩展方法。</summary>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Debug(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception)">
      <summary>提供了一组方法和属性，可帮助你调试使用指定编写器、请求、类别和异常的代码。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="exception">在执行过程中出现的错误。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Debug(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception,System.String,System.Object[])">
      <summary>提供了一组方法和属性，可帮助你调试使用指定编写器、请求、类别、异常、消息格式和参数的代码。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="exception">在执行过程中出现的错误。</param>
      <param name="messageFormat">消息的格式。</param>
      <param name="messageArguments">消息参数。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Debug(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.String,System.Object[])">
      <summary>提供了一组方法和属性，可帮助你调试使用指定编写器、请求、类别、异常、消息格式和参数的代码。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="messageFormat">消息的格式。</param>
      <param name="messageArguments">消息参数。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Error(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception)">
      <summary>使用指定的编写器、请求、类别和异常在列表中显示一条错误消息。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="exception">在执行过程中出现的错误。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Error(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception,System.String,System.Object[])">
      <summary>使用指定的编写器、请求、类别、异常、消息格式和参数在列表中显示一条错误消息。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="exception">异常。</param>
      <param name="messageFormat">消息的格式。</param>
      <param name="messageArguments">消息中的参数。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Error(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.String,System.Object[])">
      <summary>使用指定的编写器、请求、类别、消息格式和参数在列表中显示一条错误消息。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="messageFormat">消息的格式。</param>
      <param name="messageArguments">消息中的参数。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Fatal(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception)">
      <summary>使用指定的编写器、请求、类别和异常在 <see cref="T:System.Web.Http.Tracing.ITraceWriterExtensions" /> 类中显示一条错误消息。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="exception">在执行过程中出现的异常。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Fatal(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception,System.String,System.Object[])">
      <summary>使用指定的编写器、请求、类别、异常、消息格式和参数在 <see cref="T:System.Web.Http.Tracing.ITraceWriterExtensions" /> 类中显示一条错误消息。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="exception">异常。</param>
      <param name="messageFormat">消息的格式。</param>
      <param name="messageArguments">消息参数。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Fatal(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.String,System.Object[])">
      <summary>使用指定的编写器、请求、类别、消息格式和参数在 <see cref="T:System.Web.Http.Tracing.ITraceWriterExtensions" /> 类中显示一条错误消息。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="messageFormat">消息的格式。</param>
      <param name="messageArguments">消息参数。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Info(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception)">
      <summary>显示 <see cref="System.Web.Http.Tracing.ITraceWriterExtensions" /> 中的详细信息。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="exception">在执行过程中出现的错误。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Info(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception,System.String,System.Object[])">
      <summary>显示 <see cref="System.Web.Http.Tracing.ITraceWriterExtensions" /> 中的详细信息。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="exception">在执行过程中出现的错误。</param>
      <param name="messageFormat">消息的格式。</param>
      <param name="messageArguments">消息参数。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Info(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.String,System.Object[])">
      <summary>显示 <see cref="System.Web.Http.Tracing.ITraceWriterExtensions" /> 中的详细信息。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="messageFormat">消息的格式。</param>
      <param name="messageArguments">消息参数。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Trace(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.Exception)">
      <summary>指示侦听器集合中的跟踪侦听器。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="level">跟踪级别。</param>
      <param name="exception">在执行过程中出现的错误。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Trace(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.Exception,System.String,System.Object[])">
      <summary>指示侦听器集合中的跟踪侦听器。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="level">跟踪级别。</param>
      <param name="exception">在执行过程中出现的错误。</param>
      <param name="messageFormat">消息的格式。</param>
      <param name="messageArguments">消息参数。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Trace(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.String,System.Object[])">
      <summary>指示侦听器集合中的跟踪侦听器。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="level">跟踪的 <see cref="T:System.Web.Http.Tracing.TraceLevel" />。</param>
      <param name="messageFormat">消息的格式。</param>
      <param name="messageArguments">消息参数。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.TraceBeginEnd(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.String,System.String,System.Action{System.Web.Http.Tracing.TraceRecord},System.Action,System.Action{System.Web.Http.Tracing.TraceRecord},System.Action{System.Web.Http.Tracing.TraceRecord})">
      <summary>跟踪围绕指定操作的开始和结束跟踪。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="level">跟踪的 <see cref="T:System.Web.Http.Tracing.TraceLevel" />。</param>
      <param name="operatorName">执行操作的对象的名称。它可以为 null。</param>
      <param name="operationName">要执行的操作的名称。它可以为 null。</param>
      <param name="beginTrace">要在执行操作之前调用（以允许填充给定 <see cref="T:System.Web.Http.Tracing.TraceRecord" />）的 <see cref="T:System.Action" />。它可以为 null。</param>
      <param name="execute">一个 &lt;see cref="T:System.Func`1" /&gt;，它将返回要执行操作的 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <param name="endTrace">要在成功执行操作之后调用（以允许填充给定 <see cref="T:System.Web.Http.Tracing.TraceRecord" />）的 <see cref="T:System.Action" />。它可以为 null。</param>
      <param name="errorTrace">在执行操作时出错的情况下调用（以允许填充给定 <see cref="T:System.Web.Http.Tracing.TraceRecord" />）的 <see cref="T:System.Action" />。它可以为 null。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.TraceBeginEndAsync``1(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.String,System.String,System.Action{System.Web.Http.Tracing.TraceRecord},System.Func{System.Threading.Tasks.Task{``0}},System.Action{System.Web.Http.Tracing.TraceRecord,``0},System.Action{System.Web.Http.Tracing.TraceRecord})">
      <summary> 跟踪围绕指定操作的开始和结束跟踪。</summary>
      <returns>操作返回的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="level">跟踪的 <see cref="T:System.Web.Http.Tracing.TraceLevel" />。</param>
      <param name="operatorName">执行操作的对象的名称。它可以为 null。</param>
      <param name="operationName">要执行的操作的名称。它可以为 null。</param>
      <param name="beginTrace">要在执行操作之前调用（以允许填充给定 <see cref="T:System.Web.Http.Tracing.TraceRecord" />）的 <see cref="T:System.Action" />。它可以为 null。</param>
      <param name="execute">一个 &lt;see cref="T:System.Func`1" /&gt;，它将返回要执行操作的 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <param name="endTrace">要在成功执行操作之后调用（以允许填充给定 <see cref="T:System.Web.Http.Tracing.TraceRecord" />）的 <see cref="T:System.Action" />。已完成任务的结果也将传递到给操作。此参数可以为 null。</param>
      <param name="errorTrace">在执行操作时出错的情况下调用（以允许填充给定 <see cref="T:System.Web.Http.Tracing.TraceRecord" />）的 <see cref="T:System.Action" />。它可以为 null。</param>
      <typeparam name="TResult">由 <see cref="T:System.Threading.Tasks.Task" /> 生成的结果的类型。</typeparam>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.TraceBeginEndAsync(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.String,System.String,System.Action{System.Web.Http.Tracing.TraceRecord},System.Func{System.Threading.Tasks.Task},System.Action{System.Web.Http.Tracing.TraceRecord},System.Action{System.Web.Http.Tracing.TraceRecord})">
      <summary> 跟踪围绕指定操作的开始和结束跟踪。</summary>
      <returns>操作返回的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="level">跟踪的 <see cref="T:System.Web.Http.Tracing.TraceLevel" />。</param>
      <param name="operatorName">执行操作的对象的名称。它可以为 null。</param>
      <param name="operationName">要执行的操作的名称。它可以为 null。</param>
      <param name="beginTrace">要在执行操作之前调用（以允许填充给定 <see cref="T:System.Web.Http.Tracing.TraceRecord" />）的 <see cref="T:System.Action" />。它可以为 null。</param>
      <param name="execute">一个 &lt;see cref="T:System.Func`1" /&gt;，它将返回要执行操作的 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <param name="endTrace">要在成功执行操作之后调用（以允许填充给定 <see cref="T:System.Web.Http.Tracing.TraceRecord" />）的 <see cref="T:System.Action" />。它可以为 null。</param>
      <param name="errorTrace">在执行操作时出错的情况下调用（以允许填充给定 <see cref="T:System.Web.Http.Tracing.TraceRecord" />）的 <see cref="T:System.Action" />。它可以为 null。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Warn(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception)">
      <summary>指示执行的警告级别。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="exception">在执行过程中出现的错误。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Warn(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception,System.String,System.Object[])">
      <summary>指示执行的警告级别。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="exception">在执行过程中出现的错误。</param>
      <param name="messageFormat">消息的格式。</param>
      <param name="messageArguments">消息参数。</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Warn(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.String,System.Object[])">
      <summary>指示执行的警告级别。</summary>
      <param name="traceWriter">
        <see cref="T:System.Web.Http.Tracing.ITraceWriter" />。</param>
      <param name="request">要与跟踪关联的 <see cref="T:System.Net.Http.HttpRequestMessage" />。它可以为 null。</param>
      <param name="category">跟踪的逻辑类别。</param>
      <param name="messageFormat">消息的格式。</param>
      <param name="messageArguments">消息参数。</param>
    </member>
    <member name="T:System.Web.Http.Tracing.TraceCategories">
      <summary>指定跟踪类别的枚举。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.ActionCategory">
      <summary>操作类别。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.ControllersCategory">
      <summary>控制器类别。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.FiltersCategory">
      <summary>筛选器类别。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.FormattingCategory">
      <summary>格式设置类别。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.MessageHandlersCategory">
      <summary>消息处理程序类别。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.ModelBindingCategory">
      <summary>模型绑定类别。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.RequestCategory">
      <summary>请求类别。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.RoutingCategory">
      <summary>路由类别。</summary>
    </member>
    <member name="T:System.Web.Http.Tracing.TraceKind">
      <summary>指定跟踪操作的类型。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceKind.Begin">
      <summary>标记某个操作的开始的跟踪。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceKind.End">
      <summary>标记某个操作的结束的跟踪。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceKind.Trace">
      <summary>单个跟踪，不是 Begin/End 跟踪对的一部分。</summary>
    </member>
    <member name="T:System.Web.Http.Tracing.TraceLevel">
      <summary>指定跟踪级别的枚举。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Debug">
      <summary>调试跟踪的跟踪级别。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Error">
      <summary>错误跟踪的跟踪级别。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Fatal">
      <summary>严重跟踪的跟踪级别。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Info">
      <summary>信息跟踪的跟踪级别。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Off">
      <summary>已禁用跟踪。</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Warn">
      <summary>警告跟踪的跟踪级别。</summary>
    </member>
    <member name="T:System.Web.Http.Tracing.TraceRecord">
      <summary>表示跟踪记录。</summary>
    </member>
    <member name="M:System.Web.Http.Tracing.TraceRecord.#ctor(System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel)">
      <summary>初始化 <see cref="T:System.Web.Http.Tracing.TraceRecord" /> 类的新实例。</summary>
      <param name="request">消息请求。</param>
      <param name="category">跟踪类别。</param>
      <param name="level">跟踪级别。</param>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Category">
      <summary>获取或设置跟踪类别。</summary>
      <returns>跟踪类别。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Exception">
      <summary>获取或设置异常。</summary>
      <returns>异常。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Kind">
      <summary>获取或设置跟踪的类型。</summary>
      <returns>跟踪的类型。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Level">
      <summary>获取或设置跟踪级别。</summary>
      <returns>跟踪级别。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Message">
      <summary>获取或设置消息。</summary>
      <returns>消息。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Operation">
      <summary>获取或设置所执行的逻辑操作名称。</summary>
      <returns>所执行的逻辑操作名称。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Operator">
      <summary>获取或设置执行操作的对象的逻辑名称。</summary>
      <returns>执行操作的对象的逻辑名称。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Properties">
      <summary>获取用户定义的可选属性。</summary>
      <returns>用户定义的可选属性。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Request">
      <summary>从记录中获取 <see cref="T:System.Net.Http.HttpRequestMessage" />。</summary>
      <returns>记录中的 <see cref="T:System.Net.Http.HttpRequestMessage" />。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.RequestId">
      <summary>从 <see cref="P:System.Web.Http.Tracing.TraceRecord.Request" /> 获取相关 ID。</summary>
      <returns>
        <see cref="P:System.Web.Http.Tracing.TraceRecord.Request" /> 中的相关 ID。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Status">
      <summary>获取或设置与 <see cref="T:System.Net.Http.HttpResponseMessage" /> 关联的 <see cref="T:System.Net.HttpStatusCode" />。</summary>
      <returns>与 <see cref="T:System.Net.Http.HttpResponseMessage" /> 关联的 <see cref="T:System.Net.HttpStatusCode" />。</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Timestamp">
      <summary>获取此跟踪的 <see cref="T:System.DateTime" />（通过 <see cref="P:System.DateTime.UtcNow" />）。</summary>
      <returns>此跟踪的 <see cref="T:System.DateTime" />（通过 <see cref="P:System.DateTime.UtcNow" />）。</returns>
    </member>
    <member name="T:System.Web.Http.Validation.DefaultBodyModelValidator">
      <summary>表示用于递归验证对象的类。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.DefaultBodyModelValidator.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.DefaultBodyModelValidator" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.DefaultBodyModelValidator.ShouldValidateType(System.Type)">
      <summary>确定是否应验证特定类型的实例。</summary>
      <returns>如果应验证该类型，则为 true；否则为 false。</returns>
      <param name="type">要验证的类型。</param>
    </member>
    <member name="M:System.Web.Http.Validation.DefaultBodyModelValidator.Validate(System.Object,System.Type,System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.String)">
      <summary>确定 <paramref name="model" /> 是否有效，并将任何验证错误添加到 <paramref name="actionContext" /> 的 <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" />。</summary>
      <returns>如果模型有效，则为 true，否则为 false。</returns>
      <param name="model">要验证的模型。</param>
      <param name="type">验证时要使用的 <see cref="T:System.Type" />。</param>
      <param name="metadataProvider">用于提供模型元数据的 <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" />。</param>
      <param name="actionContext">要在其中验证模型的 <see cref="T:System.Web.Http.Controllers.HttpActionContext" />。</param>
      <param name="keyPrefix">由于任何验证错误而要向键追加的 <see cref="T:System.String" />。</param>
    </member>
    <member name="T:System.Web.Http.Validation.IBodyModelValidator">
      <summary>表示用于模型验证的接口</summary>
    </member>
    <member name="M:System.Web.Http.Validation.IBodyModelValidator.Validate(System.Object,System.Type,System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.String)">
      <summary> 确定 model 是否有效，并将任何验证错误添加到 actionContext 的 <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" /></summary>
      <returns>如果 model 有效，则为 true，否则为 false。</returns>
      <param name="model">要验证的模型。</param>
      <param name="type">验证时要使用的 <see cref="T:System.Type" />。</param>
      <param name="metadataProvider">用于提供模型元数据的 <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" />。</param>
      <param name="actionContext">要在其中验证模型的 <see cref="T:System.Web.Http.Controllers.HttpActionContext" />。</param>
      <param name="keyPrefix">由于任何验证错误而要向键追加的 <see cref="T:System.String" />。</param>
    </member>
    <member name="T:System.Web.Http.Validation.ModelStateFormatterLogger">
      <summary>此 <see cref="T:System.Net.Http.Formatting.IFormatterLogger" /> 将格式化程序错误记录到提供的 <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" />。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelStateFormatterLogger.#ctor(System.Web.Http.ModelBinding.ModelStateDictionary,System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.ModelStateFormatterLogger" /> 类的新实例。</summary>
      <param name="modelState">模型状态。</param>
      <param name="prefix">前缀。</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelStateFormatterLogger.LogError(System.String,System.Exception)">
      <summary>记录指定的模型错误。</summary>
      <param name="errorPath">错误路径。</param>
      <param name="exception">错误消息。</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelStateFormatterLogger.LogError(System.String,System.String)">
      <summary>记录指定的模型错误。</summary>
      <param name="errorPath">错误路径。</param>
      <param name="errorMessage">错误消息。</param>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidatedEventArgs">
      <summary>为 <see cref="E:System.Web.Http.Validation.ModelValidationNode.Validated" /> 事件提供数据。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidatedEventArgs.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.Validation.ModelValidationNode)">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.ModelValidatedEventArgs" /> 类的新实例。</summary>
      <param name="actionContext">操作上下文。</param>
      <param name="parentNode">父节点。</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidatedEventArgs.ActionContext">
      <summary>获取或设置操作的上下文。</summary>
      <returns>操作的上下文。</returns>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidatedEventArgs.ParentNode">
      <summary>获取或设置此节点的父级。</summary>
      <returns>此节点的父级。</returns>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidatingEventArgs">
      <summary>为 <see cref="E:System.Web.Http.Validation.ModelValidationNode.Validating" /> 事件提供数据。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidatingEventArgs.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.Validation.ModelValidationNode)">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.ModelValidatingEventArgs" /> 类的新实例。</summary>
      <param name="actionContext">操作上下文。</param>
      <param name="parentNode">父节点。</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidatingEventArgs.ActionContext">
      <summary>获取或设置操作的上下文。</summary>
      <returns>操作的上下文。</returns>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidatingEventArgs.ParentNode">
      <summary>获取或设置此节点的父级。</summary>
      <returns>此节点的父级。</returns>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidationNode">
      <summary>为模型验证信息提供一个容器。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationNode.#ctor(System.Web.Http.Metadata.ModelMetadata,System.String)">
      <summary>使用模型元数据和状态键初始化 <see cref="T:System.Web.Http.Validation.ModelValidationNode" /> 类的新实例。</summary>
      <param name="modelMetadata">模型元数据。</param>
      <param name="modelStateKey">模型状态键。</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationNode.#ctor(System.Web.Http.Metadata.ModelMetadata,System.String,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidationNode})">
      <summary>使用模型元数据、模型状态键和子模型验证节点初始化 <see cref="T:System.Web.Http.Validation.ModelValidationNode" /> 类的新实例。</summary>
      <param name="modelMetadata">模型元数据。</param>
      <param name="modelStateKey">模型状态键。</param>
      <param name="childNodes">模型子节点。</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationNode.ChildNodes">
      <summary>获取或设置子节点。</summary>
      <returns>子节点。</returns>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationNode.CombineWith(System.Web.Http.Validation.ModelValidationNode)">
      <summary>将当前的 <see cref="T:System.Web.Http.Validation.ModelValidationNode" /> 实例与指定的 <see cref="T:System.Web.Http.Validation.ModelValidationNode" /> 实例组合在一起。</summary>
      <param name="otherNode">要与当前实例组合的模型验证节点。</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationNode.ModelMetadata">
      <summary>获取或设置模型元数据。</summary>
      <returns>模型元数据。</returns>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationNode.ModelStateKey">
      <summary>获取或设置模型状态键。</summary>
      <returns>模型状态键。</returns>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationNode.SuppressValidation">
      <summary>获取或设置一个值，该值指示是否应取消验证。</summary>
      <returns>如果应取消验证，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationNode.Validate(System.Web.Http.Controllers.HttpActionContext)">
      <summary>使用指定的执行上下文验证模型。</summary>
      <param name="actionContext">操作上下文。</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationNode.Validate(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.Validation.ModelValidationNode)">
      <summary>使用指定的执行上下文和父节点验证模型。</summary>
      <param name="actionContext">操作上下文。</param>
      <param name="parentNode">父节点。</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationNode.ValidateAllProperties">
      <summary>获取或设置一个值，该值指示是否应验证模型的所有属性。</summary>
      <returns>如果应验证模型的所有属性，则为 true；如果应跳过验证，则为 false。</returns>
    </member>
    <member name="E:System.Web.Http.Validation.ModelValidationNode.Validated">
      <summary>已验证模型时发生。</summary>
    </member>
    <member name="E:System.Web.Http.Validation.ModelValidationNode.Validating">
      <summary>正在验证模型时发生。</summary>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidationRequiredMemberSelector">
      <summary>通过检查与成员关联的任何必需 ModelValidators 来表示所需成员的选择。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationRequiredMemberSelector.#ctor(System.Web.Http.Metadata.ModelMetadataProvider,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.ModelValidationRequiredMemberSelector" /> 类的新实例。</summary>
      <param name="metadataProvider">元数据提供程序。</param>
      <param name="validatorProviders">验证程序提供程序。</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationRequiredMemberSelector.IsRequiredMember(System.Reflection.MemberInfo)">
      <summary>指示是否需要该成员才能进行验证。</summary>
      <returns>如果需要该成员才能进行验证，则为 true；否则为 false。</returns>
      <param name="member">成员。</param>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidationResult">
      <summary>为验证结果提供容器。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationResult.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.ModelValidationResult" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationResult.MemberName">
      <summary>获取或设置成员名。</summary>
      <returns>成员名。</returns>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationResult.Message">
      <summary>获取或设置验证结果消息。</summary>
      <returns>验证结果消息。</returns>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidator">
      <summary>提供用于实现验证逻辑的基类。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidator.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.ModelValidator" /> 类的新实例。</summary>
      <param name="validatorProviders">验证程序提供程序。</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidator.GetModelValidator(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>返回模型的复合模型验证程序。</summary>
      <returns>模型的复合模型验证程序。</returns>
      <param name="validatorProviders">验证程序提供程序的枚举。</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidator.IsRequired">
      <summary>获取一个值，该值指示某个模型属性是否是必需的。</summary>
      <returns>如果该模型属性是必需的，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidator.Validate(System.Web.Http.Metadata.ModelMetadata,System.Object)">
      <summary>验证指定的对象。</summary>
      <returns>验证结果的列表。</returns>
      <param name="metadata">元数据。</param>
      <param name="container">容器。</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidator.ValidatorProviders">
      <summary>获取或设置验证程序提供程序的枚举。</summary>
      <returns>验证程序提供程序的枚举。</returns>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidatorProvider">
      <summary>为模型提供验证程序的列表。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidatorProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>获取与此 <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> 关联的验证程序的列表。</summary>
      <returns>验证程序列表。</returns>
      <param name="metadata">元数据。</param>
      <param name="validatorProviders">验证程序提供程序。</param>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.AssociatedValidatorProvider">
      <summary>为用于实现验证提供程序的类提供抽象类。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.AssociatedValidatorProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Providers.AssociatedValidatorProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.AssociatedValidatorProvider.GetTypeDescriptor(System.Type)">
      <summary>获取指定类型的类型描述符。</summary>
      <returns>指定类型的类型描述符。</returns>
      <param name="type">验证提供程序的类型。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.AssociatedValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>使用元数据和验证程序提供程序来获取模型的验证程序。</summary>
      <returns>模型的验证程序。</returns>
      <param name="metadata">元数据。</param>
      <param name="validatorProviders">验证程序提供程序的枚举。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.AssociatedValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>使用元数据、验证程序提供程序和特性列表获取模型的验证程序。</summary>
      <returns>模型的验证程序。</returns>
      <param name="metadata">元数据。</param>
      <param name="validatorProviders">验证程序提供程序的枚举。</param>
      <param name="attributes">特性列表。</param>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.DataAnnotationsModelValidationFactory">
      <summary>表示创建 <see cref="T:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider" /> 实例的方法。</summary>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider">
      <summary>表示 <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> 的实现，该实现为派生自 <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> 的特性提供验证程序。它还为实现 <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" /> 的类型提供验证程序。若要支持客户端验证，您可以通过静态方法在此类上注册适配器，或者让验证特性实现 <see cref="T:System.Web.Http.Validation.IClientValidatable" />。用于支持 IClientValidatable 的逻辑在 <see cref="T:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator" /> 中实现。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>使用指定元数据、验证程序提供程序和特性来获取模型的验证程序。</summary>
      <returns>模型的验证程序。</returns>
      <param name="metadata">元数据。</param>
      <param name="validatorProviders">验证程序提供程序。</param>
      <param name="attributes">特性。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterAdapter(System.Type,System.Type)">
      <summary>注册适配器以提供客户端验证。</summary>
      <param name="attributeType">验证特性的类型。</param>
      <param name="adapterType">适配器的类型。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterAdapterFactory(System.Type,System.Web.Http.Validation.Providers.DataAnnotationsModelValidationFactory)">
      <summary>为验证提供程序注册适配器工厂。</summary>
      <param name="attributeType">特性的类型。</param>
      <param name="factory">将用于为指定特性创建 <see cref="T:System.Web.Http.Validation.ModelValidator" /> 对象的工厂。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterDefaultAdapter(System.Type)">
      <summary>注册默认适配器。</summary>
      <param name="adapterType">适配器的类型。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterDefaultAdapterFactory(System.Web.Http.Validation.Providers.DataAnnotationsModelValidationFactory)">
      <summary>注册默认适配器工厂。</summary>
      <param name="factory">将用于为默认适配器创建 <see cref="T:System.Web.Http.Validation.ModelValidator" /> 对象的工厂。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterDefaultValidatableObjectAdapter(System.Type)">
      <summary>为实现 <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" /> 的对象注册默认的适配器类型。该适配器类型必须派生自 <see cref="T:System.Web.Http.Validation.ModelValidator" />，并且必须包含一个公共构造函数，该构造函数采用 <see cref="T:System.Web.Http.Metadata.ModelMetadata" /> 和 <see cref="T:System.Web.Http.Controllers.HttpActionContext" /> 类型的两个参数。</summary>
      <param name="adapterType">适配器的类型。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterDefaultValidatableObjectAdapterFactory(System.Web.Http.Validation.Providers.DataAnnotationsValidatableObjectAdapterFactory)">
      <summary>为实现 <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" /> 的对象注册默认的适配器工厂。</summary>
      <param name="factory">工厂。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterValidatableObjectAdapter(System.Type,System.Type)">
      <summary>为必须实现 <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" /> 的给定 modelType 注册适配器类型。该适配器类型必须派生自 <see cref="T:System.Web.Http.Validation.ModelValidator" />，并且必须包含一个公共构造函数，该构造函数采用 <see cref="T:System.Web.Http.Metadata.ModelMetadata" /> 和 <see cref="T:System.Web.Http.Controllers.HttpActionContext" /> 类型的两个参数。</summary>
      <param name="modelType">模型类型。</param>
      <param name="adapterType">适配器的类型。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterValidatableObjectAdapterFactory(System.Type,System.Web.Http.Validation.Providers.DataAnnotationsValidatableObjectAdapterFactory)">
      <summary>为必须实现 <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" /> 的给定 modelType 注册适配器工厂。</summary>
      <param name="modelType">模型类型。</param>
      <param name="factory">工厂。</param>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.DataAnnotationsValidatableObjectAdapterFactory">
      <summary>为基于 <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" /> 的验证程序提供工厂。</summary>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.DataMemberModelValidatorProvider">
      <summary>表示数据成员模型的验证程序提供程序。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataMemberModelValidatorProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Providers.DataMemberModelValidatorProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataMemberModelValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>获取模型的验证程序。</summary>
      <returns>模型的验证程序。</returns>
      <param name="metadata">元数据。</param>
      <param name="validatorProviders">验证程序提供程序的枚举器。</param>
      <param name="attributes">特性列表。</param>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.InvalidModelValidatorProvider">
      <summary>
        <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> 实现，提供了用于在模型无效时引发异常的验证程序。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.InvalidModelValidatorProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Providers.InvalidModelValidatorProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.InvalidModelValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>获取与此 <see cref="T:System.Web.Http.Validation.Providers.InvalidModelValidatorProvider" /> 关联的验证程序的列表。</summary>
      <returns>验证程序列表。</returns>
      <param name="metadata">元数据。</param>
      <param name="validatorProviders">验证程序提供程序。</param>
      <param name="attributes">特性列表。</param>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.RequiredMemberModelValidatorProvider">
      <summary>表示所需成员模型验证程序的提供程序。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.RequiredMemberModelValidatorProvider.#ctor(System.Net.Http.Formatting.IRequiredMemberSelector)">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Providers.RequiredMemberModelValidatorProvider" /> 类的新实例。</summary>
      <param name="requiredMemberSelector">所需的成员选择器。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.RequiredMemberModelValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>获取成员模型的验证程序。</summary>
      <returns>成员模型的验证程序。</returns>
      <param name="metadata">元数据。</param>
      <param name="validatorProviders">验证程序提供程序</param>
    </member>
    <member name="T:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator">
      <summary>提供模型验证程序。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.ComponentModel.DataAnnotations.ValidationAttribute)">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator" /> 类的新实例。</summary>
      <param name="validatorProviders">验证程序提供程序。</param>
      <param name="attribute">模型的验证特性。</param>
    </member>
    <member name="P:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator.Attribute">
      <summary>获取或设置模型验证程序的验证特性。</summary>
      <returns>模型验证程序的验证特性。</returns>
    </member>
    <member name="P:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator.IsRequired">
      <summary>获取一个值，该值指示是否需要模型验证。</summary>
      <returns>如果需要模型验证，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator.Validate(System.Web.Http.Metadata.ModelMetadata,System.Object)">
      <summary>验证模型并返回验证错误（如果有）。</summary>
      <returns>模型的验证错误消息的列表，如果未出现错误，则为空列表。</returns>
      <param name="metadata">模型元数据。</param>
      <param name="container">模型的容器。</param>
    </member>
    <member name="T:System.Web.Http.Validation.Validators.ErrorModelValidator">
      <summary>
        <see cref="T:System.Web.Http.Validation.ModelValidator" />，用于表示错误。此验证程序将始终引发异常，而不管实际模型值是多少。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.ErrorModelValidator.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.String)">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Validators.ErrorModelValidator" /> 类的新实例。</summary>
      <param name="validatorProviders">模型验证程序提供程序的列表。</param>
      <param name="errorMessage">异常的错误消息。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.ErrorModelValidator.Validate(System.Web.Http.Metadata.ModelMetadata,System.Object)">
      <summary>验证指定的对象。</summary>
      <returns>验证结果的列表。</returns>
      <param name="metadata">元数据。</param>
      <param name="container">容器。</param>
    </member>
    <member name="T:System.Web.Http.Validation.Validators.RequiredMemberModelValidator">
      <summary>表示所需成员的 <see cref="T:System.Web.Http.Validation.ModelValidator" />。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.RequiredMemberModelValidator.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Validators.RequiredMemberModelValidator" /> 类的新实例。</summary>
      <param name="validatorProviders">验证程序提供程序。</param>
    </member>
    <member name="P:System.Web.Http.Validation.Validators.RequiredMemberModelValidator.IsRequired">
      <summary>获取或设置一个值，该值指示序列化引擎：该成员在验证时必须存在。</summary>
      <returns>如果该成员是必需的，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.RequiredMemberModelValidator.Validate(System.Web.Http.Metadata.ModelMetadata,System.Object)">
      <summary>验证该对象。</summary>
      <returns>验证结果的列表。</returns>
      <param name="metadata">元数据。</param>
      <param name="container">容器。</param>
    </member>
    <member name="T:System.Web.Http.Validation.Validators.ValidatableObjectAdapter">
      <summary>提供可验证的对象适配器。</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.ValidatableObjectAdapter.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>初始化 <see cref="T:System.Web.Http.Validation.Validators.ValidatableObjectAdapter" /> 类的新实例。</summary>
      <param name="validatorProviders">验证提供程序。</param>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.ValidatableObjectAdapter.Validate(System.Web.Http.Metadata.ModelMetadata,System.Object)">
      <summary>验证指定的对象。</summary>
      <returns>验证结果的列表。</returns>
      <param name="metadata">元数据。</param>
      <param name="container">容器。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.IEnumerableValueProvider">
      <summary>表示值提供程序的基类，这些值提供程序的值来自实现 <see cref="T:System.Collections.IEnumerable" /> 接口的集合。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.IEnumerableValueProvider.GetKeysFromPrefix(System.String)">
      <summary>从指定的 <paramref name="prefix" /> 检索键。</summary>
      <returns>来自指定 <paramref name="prefix" /> 的键。</returns>
      <param name="prefix">前缀。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.IUriValueProviderFactory">
      <summary>表示一个可由任何支持创建 <see cref="T:System.Web.Http.ValueProviders.IValueProvider" />（以访问传入 <see cref="T:System.Net.Http.HttpRequestMessage" /> 的 <see cref="T:System.Uri" />）的 <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> 实现的接口。</summary>
    </member>
    <member name="T:System.Web.Http.ValueProviders.IValueProvider">
      <summary>定义 ASP.NET MVC 中的值提供程序所需的方法。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.IValueProvider.ContainsPrefix(System.String)">
      <summary>确定集合是否包含指定的前缀。</summary>
      <returns>如果集合包含指定的前缀，则为 true；否则为 false。</returns>
      <param name="prefix">要搜索的前缀。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.IValueProvider.GetValue(System.String)">
      <summary>使用指定的键来检索值对象。</summary>
      <returns>指定键所对应的值对象；如果找不到该键，则为 null。</returns>
      <param name="key">要检索的值对象的键。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.ValueProviderAttribute">
      <summary> 此特性用于指定自定义 <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" />。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.ValueProviderAttribute" /> 的新实例。</summary>
      <param name="valueProviderFactory">模型联编程序的类型。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderAttribute.#ctor(System.Type[])">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.ValueProviderAttribute" /> 的新实例。</summary>
      <param name="valueProviderFactories">模型联编程序类型的数组。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderAttribute.GetValueProviderFactories(System.Web.Http.HttpConfiguration)">
      <summary>获取值提供程序工厂。</summary>
      <returns>值提供程序工厂的集合。</returns>
      <param name="configuration">配置对象。</param>
    </member>
    <member name="P:System.Web.Http.ValueProviders.ValueProviderAttribute.ValueProviderFactoryTypes">
      <summary>获取值提供程序工厂返回的对象的类型。</summary>
      <returns>类型的集合。</returns>
    </member>
    <member name="T:System.Web.Http.ValueProviders.ValueProviderFactory">
      <summary>表示用来创建值提供程序对象的工厂。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderFactory.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderFactory.GetValueProvider(System.Web.Http.Controllers.HttpActionContext)">
      <summary>为指定控制器上下文返回值提供程序对象。</summary>
      <returns>值提供程序对象。</returns>
      <param name="actionContext">一个对象，该对象封装有关当前 HTTP 请求的信息。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.ValueProviderResult">
      <summary>表示将一个值（如窗体发布或查询字符串中的值）绑定到操作方法参数属性或绑定到该参数本身的结果。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderResult.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.ValueProviderResult" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderResult.#ctor(System.Object,System.String,System.Globalization.CultureInfo)">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.ValueProviderResult" /> 类的新实例。</summary>
      <param name="rawValue">原始值。</param>
      <param name="attemptedValue">尝试的值。</param>
      <param name="culture">区域性。</param>
    </member>
    <member name="P:System.Web.Http.ValueProviders.ValueProviderResult.AttemptedValue">
      <summary>获取或设置要转换为字符串，以便显示的原始值。</summary>
      <returns>转换为字符串以便显示的原始值。</returns>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderResult.ConvertTo(System.Type)">
      <summary>将此结果封装的值转换为指定的类型。</summary>
      <returns>转换后的值。</returns>
      <param name="type">目标类型。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderResult.ConvertTo(System.Type,System.Globalization.CultureInfo)">
      <summary>使用指定的区域性信息将此结果封装的值转换为指定的类型。</summary>
      <returns>转换后的值。</returns>
      <param name="type">目标类型。</param>
      <param name="culture">要在转换中使用的区域性。</param>
    </member>
    <member name="P:System.Web.Http.ValueProviders.ValueProviderResult.Culture">
      <summary>获取或设置区域性。</summary>
      <returns>区域性。</returns>
    </member>
    <member name="P:System.Web.Http.ValueProviders.ValueProviderResult.RawValue">
      <summary>获取或设置值提供程序所提供的原始值。</summary>
      <returns>值提供程序所提供的原始值。</returns>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.CompositeValueProvider">
      <summary>表示值提供程序，其值来自实现了 <see cref="T:System.Collections.IEnumerable" /> 接口的值提供程序列表。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.CompositeValueProvider" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.#ctor(System.Collections.Generic.IList{System.Web.Http.ValueProviders.IValueProvider})">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.CompositeValueProvider" /> 类的新实例。</summary>
      <param name="list">值提供程序列表。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.ContainsPrefix(System.String)">
      <summary>确定集合是否包含指定的 <paramref name="prefix" />。</summary>
      <returns>如果集合包含指定的 <paramref name="prefix" />，则为 true；否则为 false。</returns>
      <param name="prefix">要搜索的前缀。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.GetKeysFromPrefix(System.String)">
      <summary>从指定的 <paramref name="prefix" /> 检索键。</summary>
      <returns>来自指定 <paramref name="prefix" /> 的键。</returns>
      <param name="prefix">前缀，可从中检索键。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.GetValue(System.String)">
      <summary>使用指定的 <paramref name="key" /> 来检索值对象。</summary>
      <returns>指定的 <paramref name="key" /> 的值对象。</returns>
      <param name="key">要检索的值对象的键。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.InsertItem(System.Int32,System.Web.Http.ValueProviders.IValueProvider)">
      <summary>在集合中的指定索引处插入一个元素。</summary>
      <param name="index">从零开始的索引，应在此索引处插入 <paramref name="item" />。</param>
      <param name="item">要插入的对象。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.SetItem(System.Int32,System.Web.Http.ValueProviders.IValueProvider)">
      <summary>替换指定索引处的元素。</summary>
      <param name="index">待替换元素的从零开始的索引。</param>
      <param name="item">位于指定索引处的元素的新值。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.CompositeValueProviderFactory">
      <summary>表示用来创建值提供程序对象列表的工厂。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProviderFactory.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.ValueProviders.ValueProviderFactory})">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.CompositeValueProviderFactory" /> 类的新实例。</summary>
      <param name="factories">值提供程序工厂的集合。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProviderFactory.GetValueProvider(System.Web.Http.Controllers.HttpActionContext)">
      <summary>检索指定控制器上下文的值提供程序对象的列表。</summary>
      <returns>指定控制器上下文的值提供程序对象的列表。</returns>
      <param name="actionContext">一个对象，该对象封装有关当前 HTTP 请求的信息。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider">
      <summary>名称/值对的值提供程序。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.#ctor(System.Collections.Generic.IDictionary{System.String,System.Object},System.Globalization.CultureInfo)"></member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}},System.Globalization.CultureInfo)">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider" /> 类的新实例。</summary>
      <param name="values">提供程序的名称/值对。</param>
      <param name="culture">用于名称/值对的区域性。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.#ctor(System.Func{System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}}},System.Globalization.CultureInfo)">
      <summary>使用用于提供名称/值对的函数委托初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider" /> 类的新实例。</summary>
      <param name="valuesFactory">返回名称/值对的集合的函数委托。</param>
      <param name="culture">用于名称/值对的区域性。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.ContainsPrefix(System.String)">
      <summary>确定集合是否包含指定的前缀。</summary>
      <returns>如果集合包含指定的前缀，则为 true；否则为 false。</returns>
      <param name="prefix">要搜索的前缀。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.GetKeysFromPrefix(System.String)">
      <summary>从前缀获取键。</summary>
      <returns>键。</returns>
      <param name="prefix">前缀。</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.GetValue(System.String)">
      <summary>使用指定的键来检索值对象。</summary>
      <returns>指定的键的值对象。</returns>
      <param name="key">要检索的值对象的键。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.QueryStringValueProvider">
      <summary>表示 <see cref="T:System.Collections.Specialized.NameValueCollection" /> 对象中包含的查询字符串的值提供程序。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.QueryStringValueProvider.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Globalization.CultureInfo)">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.QueryStringValueProvider" /> 类的新实例。</summary>
      <param name="actionContext">一个对象，该对象封装有关当前 HTTP 请求的信息。</param>
      <param name="culture">一个包含有关目标区域性的信息的对象。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.QueryStringValueProviderFactory">
      <summary>表示一个类，该类负责创建查询字符串值提供程序对象的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.QueryStringValueProviderFactory.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.QueryStringValueProviderFactory" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.QueryStringValueProviderFactory.GetValueProvider(System.Web.Http.Controllers.HttpActionContext)">
      <summary>检索指定控制器上下文的值提供程序对象。</summary>
      <returns>查询字符串值提供程序对象。</returns>
      <param name="actionContext">一个对象，该对象封装有关当前 HTTP 请求的信息。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.RouteDataValueProvider">
      <summary>表示实现 IDictionary(Of TKey, TValue) 接口的对象中包含的路由数据的值提供程序。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.RouteDataValueProvider.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Globalization.CultureInfo)">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.RouteDataValueProvider" /> 类的新实例。</summary>
      <param name="actionContext">一个对象，该对象包含有关 HTTP 请求的信息。</param>
      <param name="culture">一个包含有关目标区域性的信息的对象。</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.RouteDataValueProviderFactory">
      <summary>表示用来创建路由数据值提供程序对象的工厂。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.RouteDataValueProviderFactory.#ctor">
      <summary>初始化 <see cref="T:System.Web.Http.ValueProviders.Providers.RouteDataValueProviderFactory" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.RouteDataValueProviderFactory.GetValueProvider(System.Web.Http.Controllers.HttpActionContext)">
      <summary>检索指定控制器上下文的值提供程序对象。</summary>
      <returns>值提供程序对象。</returns>
      <param name="actionContext">一个对象，该对象封装有关当前 HTTP 请求的信息。</param>
    </member>
  </members>
</doc>