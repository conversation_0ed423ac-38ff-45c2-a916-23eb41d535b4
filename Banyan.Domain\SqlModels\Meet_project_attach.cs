﻿using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public class Meet_project_attach
    {
        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        /// <summary>
        /// 来源ID
        /// </summary>
        [SqlField]
        public int SourceId { get; set; }

        [SqlField]
        public int pid { get; set; }

        [SqlField]
        public string Creator { get; set; }

        [SqlField]
        public string pname { get; set; }
        [SqlField]
        public string pcreator { get; set; }
        [SqlField]
        public string pinternalptcp { get; set; }
        [SqlField]
        public string pgroupmember { get; set; }
        [SqlField]
        public string pmanager { get; set; }
        [SqlField]
        public string ddmanager { get; set; }

        [SqlField]
        public int ptoroleid { get; set; }
        [SqlField]
        public bool pisprivate { get; set; }

        [SqlField]
        public byte pstatus { get; set; }

        [SqlField]
        public Byte SourceType { get; set; } = (Byte)SourceTypeEnum.Project;

        [SqlField]
        public string AtName { get; set; }

        [SqlField]
        public string AtSuffix { get; set; }

        [SqlField]
        public string AtUrl { get; set; }

        [SqlField]
        public string Content { get; set; }


        /// <summary>
        /// 附件大小
        /// </summary>
        [SqlField]
        public int AtLength { get; set; }

        [SqlField]
        public DateTime AddTime { get; set; } = DateTime.Now;

        [SqlField]
        public string Remark { get; set; }

        [SqlField]
        public string Path { get; set; }

    }

}
