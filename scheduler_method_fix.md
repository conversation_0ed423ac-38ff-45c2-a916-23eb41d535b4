# RecommendationScheduler 方法调用修复

## 🐛 **问题描述**

在 `Banyan.Web/Global.asax.cs` 文件中，调用了 `RecommendationScheduler` 类不存在的方法：
- `StartAsync()` - 实际方法名是 `Start()`
- `StopAsync()` - 实际方法名是 `Stop()`

## 🔧 **修复内容**

### 修复前的错误代码：
```csharp
// 错误：调用不存在的 StartAsync 方法
bool started = await scheduler.StartAsync(6);

// 错误：调用不存在的 StopAsync 方法  
bool stopped = scheduler.StopAsync();
```

### 修复后的正确代码：
```csharp
// 正确：调用存在的 Start 方法
bool started = scheduler.Start(6);

// 正确：调用存在的 Stop 方法
bool stopped = scheduler.Stop();
```

## 📋 **RecommendationScheduler 实际方法签名**

根据 `Banyan.Apps/RecommendationScheduler.cs` 的实际实现：

```csharp
public class RecommendationScheduler
{
    /// <summary>
    /// 启动推荐调度器
    /// </summary>
    /// <param name="updateIntervalHours">更新间隔（小时）</param>
    /// <returns>是否成功启动</returns>
    public bool Start(int updateIntervalHours = DEFAULT_UPDATE_INTERVAL_HOURS)

    /// <summary>
    /// 停止推荐调度器
    /// </summary>
    /// <returns>是否成功停止</returns>
    public bool Stop()

    /// <summary>
    /// 手动触发推荐更新（异步方法）
    /// </summary>
    /// <returns>操作结果</returns>
    public async Task<bool> TriggerUpdateAsync()
}
```

## ✅ **修复结果**

- ✅ 编译错误已解决
- ✅ `Global.asax.cs` 中的方法调用已修正
- ✅ 推荐调度器可以正常启动和停止
- ✅ 应用程序启动时会正确初始化推荐调度器
- ✅ 应用程序关闭时会正确停止推荐调度器

## 📝 **注意事项**

1. `Start()` 和 `Stop()` 方法是同步方法，返回 `bool` 类型
2. `TriggerUpdateAsync()` 是异步方法，用于手动触发推荐更新
3. 调度器使用单例模式，通过 `RecommendationScheduler.Instance` 访问
4. 调度器会在后台定期更新所有用户的推荐内容

修复完成后，推荐调度器可以正常工作，定期为用户更新个性化推荐内容。
