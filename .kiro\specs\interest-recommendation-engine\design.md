# Design Document: Interest Recommendation Engine

## Overview

The Interest Recommendation Engine is a core component of the intelligent news recommendation system that matches user interest profiles with vectorized news content to provide personalized news recommendations. The system will enable users to receive news recommendations based on their interests, receive weekly email digests of interesting news and investment cases, and track user engagement through link click tracking.

This design document outlines the architecture, components, data models, and implementation approach for the Interest Recommendation Engine.

## Architecture

The Interest Recommendation Engine will be implemented as a multi-layered system with the following components:

1. **Recommendation Core**: The central component responsible for matching user interest vectors with news vectors to generate personalized recommendations.

2. **Caching Layer**: A Redis-based caching system to store recommendation results for quick access and to reduce computational load.

3. **Scheduler**: A background service that periodically updates recommendations for all users.

4. **Email Digest Service**: A component that generates and sends weekly email digests containing personalized news recommendations.

5. **Engagement Tracking**: A system to track user interactions with recommendations and update user interest profiles accordingly.

The system will leverage the existing vector-based infrastructure, including the News Vectorization Feature and User Profile Management components.

### System Interaction Diagram

```mermaid
sequenceDiagram
    participant User
    participant <PERSON><PERSON> as Web Interface
    participant RecEng<PERSON> as Recommendation Engine
    participant <PERSON><PERSON> as Redis Cache
    participant UserPro<PERSON>le as User Profile Service
    participant NewsVector as News Vectorization Service
    participant EmailService as Email Digest Service
    participant Tracker as Engagement Tracker

    User->>WebUI: Request recommendations
    WebUI->>Cache: Check for cached recommendations
    
    alt Cache Hit
        Cache-->>WebUI: Return cached recommendations
    else Cache Miss
        WebUI->>RecEngine: Generate recommendations
        RecEngine->>UserProfile: Get user interest vector
        RecEngine->>NewsVector: Get news vectors
        RecEngine->>RecEngine: Calculate similarities
        RecEngine->>Cache: Store results
        RecEngine-->>WebUI: Return recommendations
    end
    
    WebUI-->>User: Display recommendations
    
    User->>WebUI: Click on recommendation
    WebUI->>Tracker: Record click event
    Tracker->>UserProfile: Update user interest profile
    
    Note over EmailService: Weekly schedule
    EmailService->>RecEngine: Generate recommendations for all users
    EmailService->>EmailService: Format email content
    EmailService-->>User: Send weekly digest email
```

## Components and Interfaces

### 1. NewsRecommendationEngine Class

The core class responsible for generating personalized news recommendations based on user interests.

```csharp
public class NewsRecommendationEngine
{
    // Existing methods
    
    // New methods
    public async Task<List<NewsVectorSimilarity>> GetPersonalizedRecommendationsAsync(int userId, int limit = 10);
    public async Task<List<NewsVectorSimilarity>> GetWeeklyDigestRecommendationsAsync(int userId, int limit = 10);
    public async Task<bool> UpdateUserInterestBasedOnClickAsync(int userId, int newsId);
    public async Task<Dictionary<int, List<NewsVectorSimilarity>>> GenerateBatchRecommendationsAsync(List<int> userIds, int limit = 10);
}
```

### 2. RecommendationCacheManager Class

Manages caching of recommendation results to improve performance.

```csharp
public class RecommendationCacheManager
{
    public async Task<List<NewsVectorSimilarity>> GetCachedRecommendationsAsync(int userId);
    public async Task SetCachedRecommendationsAsync(int userId, List<NewsVectorSimilarity> recommendations);
    public async Task InvalidateCacheAsync(int userId);
    public async Task InvalidateAllCacheAsync();
}
```

### 3. RecommendationScheduler Class

Handles periodic updates of recommendations for all users.

```csharp
public class RecommendationScheduler
{
    public async Task ScheduleRecommendationUpdatesAsync();
    public async Task UpdateRecommendationsForAllUsersAsync();
    public async Task UpdateRecommendationsForUserAsync(int userId);
}
```

### 4. EmailDigestService Class

Generates and sends weekly email digests with personalized news recommendations.

```csharp
public class EmailDigestService
{
    public async Task GenerateAndSendWeeklyDigestAsync();
    public async Task GenerateDigestForUserAsync(int userId);
    public async Task SendDigestEmailAsync(int userId, string emailContent);
}
```

### 5. EngagementTracker Class

Tracks user interactions with recommendations and updates user interest profiles.

```csharp
public class EngagementTracker
{
    public async Task TrackClickAsync(int userId, int newsId, string source);
    public async Task<List<EngagementRecord>> GetUserEngagementHistoryAsync(int userId);
    public async Task<EngagementStatistics> GetEngagementStatisticsAsync();
}
```

## Data Models

### 1. NewsRecommendation

Represents a personalized news recommendation for a user.

```csharp
public class NewsRecommendation
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public int NewsId { get; set; }
    public double RelevanceScore { get; set; }
    public DateTime GeneratedTime { get; set; }
    public bool IsRead { get; set; }
    public bool IsClicked { get; set; }
}
```

### 2. EngagementRecord

Records user engagement with recommendations.

```csharp
public class EngagementRecord
{
    public int Id { get; set; }
    public string UserName { get; set; }
    public int NewsId { get; set; }
    public string NewsTitle { get; set; }
    public DateTime Timestamp { get; set; }
    public string Source { get; set; } // "web" or "email"
}
```

### 3. EmailDigestRecord

Tracks sent email digests.

```csharp
public class EmailDigestRecord
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public DateTime SentTime { get; set; }
    public int NewsCount { get; set; }
    public string NewsIds { get; set; } // Comma-separated list of news IDs
}
```

### 4. NewsVectorSimilarity (Existing)

Represents a news article with its similarity score to a user's interests.

```csharp
public class NewsVectorSimilarity
{
    public int NewsId { get; set; }
    public double Similarity { get; set; }
    public News News { get; set; }
}
```

## Database Schema

### 1. NewsRecommendations Table

```sql
CREATE TABLE NewsRecommendations (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    NewsId INT NOT NULL,
    RelevanceScore FLOAT NOT NULL,
    GeneratedTime DATETIME DEFAULT GETDATE(),
    IsRead BIT DEFAULT 0,
    IsClicked BIT DEFAULT 0,
    CONSTRAINT FK_NewsRecommendations_Users FOREIGN KEY (UserId) REFERENCES Member(Id),
    CONSTRAINT FK_NewsRecommendations_News FOREIGN KEY (NewsId) REFERENCES News(Id)
)
```

### 2. EngagementRecords Table

```sql
CREATE TABLE EngagementRecords (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    UserName NVARCHAR(100) NOT NULL,
    NewsId INT NOT NULL,
    NewsTitle NVARCHAR(255) NOT NULL,
    Timestamp DATETIME DEFAULT GETDATE(),
    Source NVARCHAR(50) NOT NULL,
    CONSTRAINT FK_EngagementRecords_News FOREIGN KEY (NewsId) REFERENCES News(Id)
)
```

### 3. EmailDigestRecords Table

```sql
CREATE TABLE EmailDigestRecords (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    SentTime DATETIME DEFAULT GETDATE(),
    NewsCount INT NOT NULL,
    NewsIds NVARCHAR(MAX) NOT NULL,
    CONSTRAINT FK_EmailDigestRecords_Users FOREIGN KEY (UserId) REFERENCES Member(Id)
)
```

## Error Handling

The Interest Recommendation Engine will implement comprehensive error handling to ensure robustness:

1. **Graceful Degradation**: If user interest vectors are unavailable, fall back to popular news articles.

2. **Retry Mechanisms**: Implement retry logic for transient failures in vector operations or database access.

3. **Logging**: Detailed logging of errors and warnings for troubleshooting.

4. **Monitoring**: Track recommendation generation times and success rates.

5. **Cache Fallbacks**: Use cached recommendations when real-time generation fails.

## Testing Strategy

The testing strategy for the Interest Recommendation Engine includes:

1. **Unit Tests**:
   - Test vector similarity calculations
   - Test recommendation generation logic
   - Test caching mechanisms

2. **Integration Tests**:
   - Test interaction between recommendation engine and user profile service
   - Test interaction between recommendation engine and news vectorization service
   - Test email digest generation and sending

3. **Performance Tests**:
   - Test recommendation generation performance under load
   - Test caching effectiveness
   - Test batch processing performance

4. **User Acceptance Tests**:
   - Verify recommendation relevance
   - Verify email digest formatting and delivery
   - Verify engagement tracking accuracy

## Implementation Considerations

1. **Performance Optimization**:
   - Use Redis caching for recommendation results
   - Implement batch processing for recommendation updates
   - Schedule recommendation updates during off-peak hours

2. **Scalability**:
   - Design for horizontal scaling of recommendation generation
   - Implement efficient database queries and indexing

3. **Maintainability**:
   - Follow existing code patterns and naming conventions
   - Implement comprehensive logging
   - Document key algorithms and data structures

4. **Security**:
   - Ensure proper authentication for recommendation access
   - Validate user inputs
   - Protect sensitive user data

## Deployment Strategy

The Interest Recommendation Engine will be deployed as part of the existing Banyan system:

1. **Database Migration**: Deploy database schema changes for new tables.

2. **Component Deployment**: Deploy new components and update existing ones.

3. **Configuration**: Update configuration settings for caching, scheduling, and email services.

4. **Monitoring**: Set up monitoring for recommendation performance and error rates.

5. **Rollout**: Implement a phased rollout to monitor system performance and user feedback.

## Future Enhancements

Potential future enhancements for the Interest Recommendation Engine:

1. **Advanced Recommendation Algorithms**: Implement collaborative filtering and hybrid recommendation approaches.

2. **Personalization Settings**: Allow users to customize their recommendation preferences.

3. **A/B Testing Framework**: Test different recommendation algorithms and parameters.

4. **Real-time Recommendations**: Implement real-time recommendation updates based on user behavior.

5. **Content Diversity**: Ensure recommendations include diverse content categories.