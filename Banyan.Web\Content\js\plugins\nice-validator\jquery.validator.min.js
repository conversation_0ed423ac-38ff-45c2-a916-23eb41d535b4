/*! nice-validator 1.1.3
 * (c) 2012-2017 <PERSON><PERSON> <<EMAIL>>, MIT Licensed
 * https://github.com/niceue/nice-validator
 */
!function(e){"object"==typeof module&&module.exports?module.exports=e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e(jQuery)}(function(e,t){"use strict";function i(t,n){function s(){a.$el=e(t),a.$el.length?a._init(a.$el[0],n):W(t)&&(z[t]=n)}var a=this;return a instanceof i?void(i.pending?e(window).on("validatorready",s):s()):new i(t,n)}function n(t){function i(){var t=this.options;for(var i in t)i in K&&(this[i]=t[i]);e.extend(this,{_valHook:function(){return"true"===this.element.contentEditable?"text":"val"},getValue:function(){var t=this.element;return"number"===t.type&&t.validity&&t.validity.badInput?"NaN":e(t)[this._valHook()]()},setValue:function(t){e(this.element)[this._valHook()](this.value=t)},getRangeMsg:function(e,t,i){function n(e,t){return o?e>t:e>=t}if(t){var s,a=this,r=a.messages[a._r]||"",l=t[0].split("~"),o="false"===t[1],u=l[0],d=l[1],c="rg",f=[""],g=P(e)&&+e===+e;return 2===l.length?u&&d?(g&&n(e,+u)&&n(+d,e)&&(s=!0),f=f.concat(l),c=o?"gtlt":"rg"):u&&!d?(g&&n(e,+u)&&(s=!0),f.push(u),c=o?"gt":"gte"):!u&&d&&(g&&n(+d,e)&&(s=!0),f.push(d),c=o?"lt":"lte"):(e===+u&&(s=!0),f.push(u),c="eq"),r&&(i&&r[c+i]&&(c+=i),f[0]=r[c]),s||a._rules&&(a._rules[a._i].msg=a.renderMsg.apply(null,f))}},renderMsg:function(){var e=arguments,t=e[0],i=e.length;if(t){for(;--i;)t=t.replace("{"+i+"}",e[i]);return t}}})}function n(i,n,s){this.key=i,this.validator=t,e.extend(this,s,n)}return i.prototype=t,n.prototype=new i,n}function s(e,t){if(J(e)){var i,n=t?t===!0?this:t:s.prototype;for(i in e)g(i)&&(n[i]=r(e[i]))}}function a(e,t){if(J(e)){var i,n=t?t===!0?this:t:a.prototype;for(i in e)n[i]=e[i]}}function r(t){switch(e.type(t)){case"function":return t;case"array":var i=function(){return t[0].test(this.value)||t[1]||!1};return i.msg=t[1],i;case"regexp":return function(){return t.test(this.value)}}}function l(t){var i,n,s;if(t&&t.tagName){switch(t.tagName){case"INPUT":case"SELECT":case"TEXTAREA":case"BUTTON":case"FIELDSET":i=t.form||e(t).closest("."+k);break;case"FORM":i=t;break;default:i=e(t).closest("."+k)}for(n in z)if(e(i).is(n)){s=z[n];break}return e(i).data(p)||e(i)[p](s).data(p)}}function o(e,t){var i=P(X(e,M+"-"+t));if(i&&(i=new Function("return "+i)()))return r(i)}function u(e,t,i){var n=t.msg,s=t._r;return J(n)&&(n=n[s]),W(n)||(n=X(e,V+"-"+s)||X(e,V)||(i?W(i)?i:i[s]:"")),n}function d(e){var t;return e&&(t=D.exec(e)),t&&t[0]}function c(e){return"INPUT"===e.tagName&&"checkbox"===e.type||"radio"===e.type}function f(e){return Date.parse(e.replace(/\.|\-/g,"/"))}function g(e){return/^\w+$/.test(e)}function m(e){var t="#"===e.charAt(0);return e=e.replace(/([:.{(|)}\/\[\]])/g,"\\$1"),t?e:'[name="'+e+'"]:first'}var h,p="validator",v="."+p,_=".rule",y=".field",b=".form",k="nice-"+p,w="msg-box",x="aria-invalid",M="data-rule",V="data-msg",O="data-tip",F="data-ok",$="data-timely",E="data-target",C="data-display",A="data-must",j="novalidate",N=":verifiable",T=/(&)?(!)?\b(\w+)(?:\[\s*(.*?\]?)\s*\]|\(\s*(.*?\)?)\s*\))?\s*(;|\|)?/g,S=/(\w+)(?:\[\s*(.*?\]?)\s*\]|\(\s*(.*?\)?)\s*\))?/,R=/(?:([^:;\(\[]*):)?(.*)/,q=/[^\x00-\xff]/g,D=/top|right|bottom|left/,I=/(?:(cors|jsonp):)?(?:(post|get):)?(.+)/i,H=/[<>'"`\\]|&#x?\d+[A-F]?;?|%3[A-F]/gim,L=e.noop,B=e.proxy,P=e.trim,U=e.isFunction,W=function(e){return"string"==typeof e},J=function(e){return e&&"[object Object]"===Object.prototype.toString.call(e)},Q=document.documentMode||+(navigator.userAgent.match(/MSIE (\d+)/)&&RegExp.$1),X=function(e,i,n){return e&&e.tagName?n===t?e.getAttribute(i):void(null===n?e.removeAttribute(i):e.setAttribute(i,""+n)):null},z={},G={debug:0,theme:"default",ignore:"",focusInvalid:!0,focusCleanup:!1,stopOnError:!1,beforeSubmit:null,valid:null,invalid:null,validation:null,formClass:"n-default",validClass:"n-valid",invalidClass:"n-invalid",bindClassTo:null},K={timely:1,display:null,target:null,ignoreBlank:!1,showOk:!0,dataFilter:function(e){if(W(e)||J(e)&&("error"in e||"ok"in e))return e},msgMaker:function(t){var i;return i='<span role="alert" class="msg-wrap n-'+t.type+'">'+t.arrow,t.result?e.each(t.result,function(e,n){i+='<span class="n-'+n.type+'">'+t.icon+'<span class="n-msg">'+n.msg+"</span></span>"}):i+=t.icon+'<span class="n-msg">'+t.msg+"</span>",i+="</span>"},msgWrapper:"span",msgArrow:"",msgIcon:'<span class="n-icon"></span>',msgClass:"n-right",msgStyle:"",msgShow:null,msgHide:null},Y={};return e.fn.validator=function(t){var n=this,s=arguments;return n.is(N)?n:(n.is("form")||(n=this.find("form")),n.length||(n=this),n.each(function(){var n=e(this).data(p);if(n)if(W(t)){if("_"===t.charAt(0))return;n[t].apply(n,[].slice.call(s,1))}else t&&(n._reset(!0),n._init(this,t));else new i(this,t)}),this)},e.fn.isValid=function(e,i){var n,s,a=l(this[0]),r=U(e);return!a||(r||i!==t||(i=e),a.checkOnly=!!i,s=a.options,n=a._multiValidate(this.is(N)?this:this.find(N),function(t){t||!s.focusInvalid||a.checkOnly||a.$el.find("["+x+"]:first").focus(),r&&(e.length?e(t):t&&e()),a.checkOnly=!1}),r?this:n)},e.extend(e.expr.pseudos||e.expr[":"],{verifiable:function(e){var t=e.nodeName.toLowerCase();return("input"===t&&!{submit:1,button:1,reset:1,image:1}[e.type]||"select"===t||"textarea"===t||"true"===e.contentEditable)&&!e.disabled},filled:function(t){return!!P(e(t).val())}}),i.prototype={_init:function(t,i){var r,l,o,u=this;U(i)&&(i={valid:i}),i=u._opt=i||{},o=X(t,"data-"+p+"-option"),o=u._dataOpt=o&&"{"===o.charAt(0)?new Function("return "+o)():{},l=u._themeOpt=Y[i.theme||o.theme||G.theme],r=u.options=e.extend({},G,K,l,u.options,i,o),u.rules=new s(r.rules,(!0)),u.messages=new a(r.messages,(!0)),u.Field=n(u),u.elements=u.elements||{},u.deferred={},u.errors={},u.fields={},u._initFields(r.fields),u.$el.data(p)||(u.$el.data(p,u).addClass(k+" "+r.formClass).on("form-submit-validate",function(e,t,i,n,s){u.vetoed=s.veto=!u.isValid,u.ajaxFormOptions=n}).on("submit"+v+" validate"+v,B(u,"_submit")).on("reset"+v,B(u,"_reset")).on("showmsg"+v,B(u,"_showmsg")).on("hidemsg"+v,B(u,"_hidemsg")).on("focusin"+v+" click"+v,N,B(u,"_focusin")).on("focusout"+v+" validate"+v,N,B(u,"_focusout")).on("keyup"+v+" input"+v+" compositionstart compositionend",N,B(u,"_focusout")).on("click"+v,":radio,:checkbox","click",B(u,"_focusout")).on("change"+v,'select,input[type="file"]',"change",B(u,"_focusout")),u._NOVALIDATE=X(t,j),X(t,j,j)),W(r.target)&&u.$el.find(r.target).addClass("msg-container")},_guessAjax:function(t){function i(t,i,n){return!!(t&&t[i]&&e.map(t[i],function(e){return~e.namespace.indexOf(n)?1:null}).length)}var n=this;if(!(n.isAjaxSubmit=!!n.options.valid)){var s=(e._data||e.data)(t,"events");n.isAjaxSubmit=i(s,"valid","form")||i(s,"submit","form-plugin")}},_initFields:function(e){function t(e,t){if(null===t||r){var i=a.elements[e];i&&a._resetElement(i,!0),delete a.fields[e]}else a.fields[e]=new a.Field(e,W(t)?{rule:t}:t,a.fields[e])}var i,n,s,a=this,r=null===e;if(r&&(e=a.fields),J(e))for(i in e)if(~i.indexOf(","))for(n=i.split(","),s=n.length;s--;)t(P(n[s]),e[i]);else t(i,e[i]);a.$el.find(N).each(function(){a._parse(this)})},_parse:function(e){var t,i,n,s=this,a=e.name,r=X(e,M);return r&&X(e,M,null),e.id&&("#"+e.id in s.fields||!a||null!==r&&(t=s.fields[a])&&r!==t.rule&&e.id!==t.key)&&(a="#"+e.id),a||(a="#"+(e.id="N"+String(Math.random()).slice(-12))),t=s.getField(a,!0),t.rule=r||t.rule,(i=X(e,C))&&(t.display=i),t.rule&&((null!==X(e,A)||/\b(?:match|checked)\b/.test(t.rule))&&(t.must=!0),/\brequired\b/.test(t.rule)&&(t.required=!0),(n=X(e,$))?t.timely=+n:t.timely>3&&X(e,$,t.timely),s._parseRule(t),t.old={}),W(t.target)&&X(e,E,t.target),W(t.tip)&&X(e,O,t.tip),s.fields[a]=t},_parseRule:function(i){var n=R.exec(i.rule);n&&(i._i=0,n[1]&&(i.display=n[1]),n[2]&&(i._rules=[],n[2].replace(T,function(){var n=arguments;n[4]=n[4]||n[5],i._rules.push({and:"&"===n[1],not:"!"===n[2],or:"|"===n[6],method:n[3],params:n[4]?e.map(n[4].split(", "),P):t})})))},_multiValidate:function(i,n){var s=this,a=s.options;return s.hasError=!1,a.ignore&&(i=i.not(a.ignore)),i.each(function(){if(s._validate(this),s.hasError&&a.stopOnError)return!1}),n&&(s.validating=!0,e.when.apply(null,e.map(s.deferred,function(e){return e})).done(function(){n.call(s,!s.hasError),s.validating=!1})),e.isEmptyObject(s.deferred)?!s.hasError:t},_submit:function(i){var n=this,s=n.options,a=i.target,r="submit"===i.type&&"FORM"===a.tagName&&!i.isDefaultPrevented();i.preventDefault(),h&&~(h=!1)||n.submiting||"validate"===i.type&&n.$el[0]!==a||U(s.beforeSubmit)&&s.beforeSubmit.call(n,a)===!1||(n.isAjaxSubmit===t&&n._guessAjax(a),n._debug("log","\n<<< event: "+i.type),n._reset(),n.submiting=!0,n._multiValidate(n.$el.find(N),function(t){var i,l=t||2===s.debug?"valid":"invalid";t||(s.focusInvalid&&n.$el.find("["+x+"]:first").focus(),i=e.map(n.errors,function(e){return e})),n.submiting=!1,n.isValid=t,U(s[l])&&s[l].call(n,a,i),n.$el.trigger(l+b,[a,i]),n._debug("log",">>> "+l),t&&(n.vetoed?e(a).ajaxSubmit(n.ajaxFormOptions):r&&!n.isAjaxSubmit&&document.createElement("form").submit.call(a))}))},_reset:function(e){var t=this;t.errors={},e&&(t.reseting=!0,t.$el.find(N).each(function(){t._resetElement(this)}),delete t.reseting)},_resetElement:function(e,t){this._setClass(e,null),this.hideMsg(e)},_focusin:function(e){var t,i,n=this,s=n.options,a=e.target;n.validating||"click"===e.type&&document.activeElement===a||(s.focusCleanup&&"true"===X(a,x)&&(n._setClass(a,null),n.hideMsg(a)),i=X(a,O),i?n.showMsg(a,{type:"tip",msg:i}):(X(a,M)&&n._parse(a),(t=X(a,$))&&(8!==t&&9!==t||n._focusout(e))))},_focusout:function(t){var i,n,s,a,r,l,o,u,d,f=this,g=f.options,m=t.target,h=t.type,p="focusin"===h,v="validate"===h,_=0;if("compositionstart"===h&&(f.pauseValidate=!0),"compositionend"===h&&(f.pauseValidate=!1),!f.pauseValidate&&(n=m.name&&c(m)?f.$el.find('input[name="'+m.name+'"]').get(0):m,(s=f.getField(n))&&s.rule)){if(i=s._e,s._e=h,d=s.timely,!v){if(!d||c(m)&&"click"!==h)return;if(r=s.getValue(),s.ignoreBlank&&!r&&!p)return void f.hideMsg(m);if("focusout"===h){if("change"===i)return;if(2===d||8===d){if(a=s.old,!r||!a)return;s.isValid&&!a.showOk?f.hideMsg(m):f._makeMsg(m,s,a)}}else{if(d<2&&!t.data)return;if(l=+new Date,l-(m._ts||0)<100)return;if(m._ts=l,"keyup"===h){if("input"===i)return;if(o=t.keyCode,u={8:1,9:1,16:1,32:1,46:1},9===o&&!r)return;if(o<48&&!u[o])return}p||(_=d<100?"click"===h||"SELECT"===m.tagName?0:400:d)}}g.ignore&&e(m).is(g.ignore)||(clearTimeout(s._t),_?s._t=setTimeout(function(){f._validate(m,s)},_):(v&&(s.old={}),f._validate(m,s)))}},_setClass:function(t,i){var n=e(t),s=this.options;s.bindClassTo&&(n=n.closest(s.bindClassTo)),n.removeClass(s.invalidClass+" "+s.validClass),null!==i&&n.addClass(i?s.validClass:s.invalidClass)},_showmsg:function(e,t,i){var n=this,s=e.target;n.$el.is(s)?J(t)?n.showMsg(t):"tip"===t&&n.$el.find(N+"["+O+"]",s).each(function(){n.showMsg(this,{type:t,msg:i})}):n.showMsg(s,{type:t,msg:i})},_hidemsg:function(t){var i=e(t.target);i.is(N)&&this.hideMsg(i)},_validatedField:function(t,i,n){var s=this,a=s.options,r=i.isValid=n.isValid=!!n.isValid,l=r?"valid":"invalid";n.key=i.key,n.ruleName=i._r,n.id=t.id,n.value=i.value,s.elements[i.key]=n.element=t,s.isValid=s.$el[0].isValid=r?s.isFormValid():r,r?n.type="ok":(s.submiting&&(s.errors[i.key]=n.msg),s.hasError=!0),i.old=n,U(i[l])&&i[l].call(s,t,n),U(a.validation)&&a.validation.call(s,t,n),e(t).attr(x,!r||null).trigger(l+y,[n,s]),s.$el.triggerHandler("validation",[n,s]),s.checkOnly||(s._setClass(t,n.skip||"tip"===n.type?null:r),s._makeMsg.apply(s,arguments))},_makeMsg:function(t,i,n){i.msgMaker&&(n=e.extend({},n),"focusin"===i._e&&(n.type="tip"),this[n.showOk||n.msg||"tip"===n.type?"showMsg":"hideMsg"](t,n,i))},_validatedRule:function(i,n,s,a){n=n||c.getField(i),a=a||{};var r,l,o,d,c=this,f=n._r,g=n.timely,m=9===g||8===g,h=!1;if(null===s)return c._validatedField(i,n,{isValid:!0,skip:!0}),void(n._i=0);if(s===t?o=!0:s===!0||""===s?h=!0:W(s)?r=s:J(s)?s.error?r=s.error:(r=s.ok,h=!0):h=!!s,l=n._rules[n._i],l.not&&(r=t,h="required"===f||!h),l.or)if(h)for(;n._i<n._rules.length&&n._rules[n._i].or;)n._i++;else o=!0;else l.and&&(n.isValid||(o=!0));o?h=!0:(h&&n.showOk!==!1&&(d=X(i,F),r=null===d?W(n.ok)?n.ok:r:d,!W(r)&&W(n.showOk)&&(r=n.showOk),W(r)&&(a.showOk=h)),h&&!m||(r=(u(i,n,r||l.msg||c.messages[f])||c.messages.fallback).replace(/\{0\|?([^\}]*)\}/,function(e,t){return c._getDisplay(i,n.display)||t||c.messages[0]})),h||(n.isValid=h),a.msg=r,e(i).trigger((h?"valid":"invalid")+_,[f,r])),!m||o&&!l.and||(h||n._m||(n._m=r),n._v=n._v||[],n._v.push({type:h?o?"tip":"ok":"error",msg:r||l.msg})),c._debug("log","   "+n._i+": "+f+" => "+(h||r)),(h||m)&&n._i<n._rules.length-1?(n._i++,c._checkRule(i,n)):(n._i=0,m?(a.isValid=n.isValid,a.result=n._v,a.msg=n._m||"",n.value||"focusin"!==n._e||(a.type="tip")):a.isValid=h,c._validatedField(i,n,a),delete n._m,delete n._v)},_checkRule:function(i,n){var s,a,r,l=this,u=n.key,d=n._rules[n._i],c=d.method,f=d.params;l.submiting&&l.deferred[u]||(r=n.old,n._r=c,r&&!n.must&&!d.must&&d.result!==t&&r.ruleName===c&&r.id===i.id&&n.value&&r.value===n.value?s=d.result:(a=o(i,c)||l.rules[c]||L,s=a.call(n,i,f,n),a.msg&&(d.msg=a.msg)),J(s)&&U(s.then)?(l.deferred[u]=s,n.isValid=t,!l.checkOnly&&l.showMsg(i,{type:"loading",msg:l.messages.loading},n),s.then(function(s,a,r){var o,u=P(r.responseText),c=n.dataFilter;/jsonp?/.test(this.dataType)?u=s:"{"===u.charAt(0)&&(u=e.parseJSON(u)),o=c.call(this,u,n),o===t&&(o=c.call(this,u.data,n)),d.data=this.data,d.result=n.old?o:t,l._validatedRule(i,n,o)},function(e,t){l._validatedRule(i,n,l.messages[t]||t)}).always(function(){delete l.deferred[u]})):l._validatedRule(i,n,s))},_validate:function(e,t){var i=this;if(!e.disabled&&null===X(e,j)&&(t=t||i.getField(e),t&&(t._rules||i._parse(e),t._rules)))return i._debug("info",t.key),t.isValid=!0,t.element=e,t.value=t.getValue(),t.required||t.must||t.value||c(e)?(i._checkRule(e,t),t.isValid):(i._validatedField(e,t,{isValid:!0}),!0)},_debug:function(e,t){window.console&&this.options.debug&&console[e](t)},test:function(e,i){var n,s,a,r,l=this,o=S.exec(i);return o&&(a=o[1],a in l.rules&&(r=o[2]||o[3],r=r?r.split(", "):t,s=l.getField(e,!0),s._r=a,s.value=s.getValue(),n=l.rules[a].call(s,e,r))),n===!0||n===t||null===n},_getDisplay:function(e,t){return W(t)?t:U(t)?t.call(this,e):""},_getMsgOpt:function(t,i){var n=i?i:this.options;return e.extend({type:"error",pos:d(n.msgClass),target:n.target,wrapper:n.msgWrapper,style:n.msgStyle,cls:n.msgClass,arrow:n.msgArrow,icon:n.msgIcon},W(t)?{msg:t}:t)},_getMsgDOM:function(i,n){var s,a,r,l,o=e(i);if(o.is(N)?(r=n.target||X(i,E),r&&(r=U(r)?r.call(this,i):"#"===r.charAt(0)?e(r):this.$el.find(r),r.length&&(r.is(N)?(o=r,i=r.get(0)):r.hasClass(w)?s=r:l=r)),s||(a=c(i)&&i.name||!i.id?i.name:i.id,s=(l||this.$el).find(n.wrapper+"."+w+'[for="'+a+'"]'))):s=o,!n.hide&&!s.length)if(s=e("<"+n.wrapper+">").attr({"class":w+(n.cls?" "+n.cls:""),style:n.style||t,"for":a}),l)s.appendTo(l);else if(c(i)){var u=o.parent();s.appendTo(u.is("label")?u.parent():u)}else s[n.pos&&"right"!==n.pos?"insertBefore":"insertAfter"](o);return s},showMsg:function(t,i,n){if(t){var s,a,r,l,o=this,u=o.options;if(J(t)&&!t.jquery&&!i)return void e.each(t,function(e,t){var i=o.elements[e]||o.$el.find(m(e))[0];o.showMsg(i,t)});e(t).is(N)&&(n=n||o.getField(t)),(a=(n||u).msgMaker)&&(i=o._getMsgOpt(i,n),t=(t.name&&c(t)?o.$el.find('input[name="'+t.name+'"]'):e(t)).get(0),i.msg||"error"===i.type||(r=X(t,"data-"+i.type),null!==r&&(i.msg=r)),W(i.msg)&&(l=o._getMsgDOM(t,i),!D.test(l[0].className)&&l.addClass(i.cls),6===Q&&"bottom"===i.pos&&(l[0].style.marginTop=e(t).outerHeight()+"px"),l.html(a.call(o,i))[0].style.display="",U(s=n&&n.msgShow||u.msgShow)&&s.call(o,l,i.type)))}},hideMsg:function(t,i,n){var s,a,r=this,l=r.options;t=e(t).get(0),e(t).is(N)&&(n=n||r.getField(t),n&&(n.isValid||r.reseting)&&X(t,x,null)),i=r._getMsgOpt(i,n),i.hide=!0,a=r._getMsgDOM(t,i),a.length&&(U(s=n&&n.msgHide||l.msgHide)?s.call(r,a,i.type):(a[0].style.display="none",a[0].innerHTML=""))},getField:function(e,i){var n,s,a=this;if(W(e))n=e,e=t;else{if(X(e,M))return a._parse(e);n=e.id&&"#"+e.id in a.fields||!e.name?"#"+e.id:e.name}return((s=a.fields[n])||i&&(s=new a.Field(n)))&&(s.element=e),s},setField:function(e,t){var i={};e&&(W(e)?i[e]=t:i=e,this._initFields(i))},isFormValid:function(){var e,t,i=this.fields;for(e in i)if(t=i[e],t._rules&&(t.required||t.must||t.value)&&!t.isValid)return!1;return!0},holdSubmit:function(e){this.submiting=e===t||e},cleanUp:function(){this._reset(1)},destroy:function(){this._reset(1),this.$el.off(v).removeData(p),X(this.$el[0],j,this._NOVALIDATE)}},e(window).on("beforeunload",function(){this.focus()}),e(document).on("click",":submit",function(){var e,t=this;t.form&&(e=t.getAttributeNode("formnovalidate"),(e&&null!==e.nodeValue||null!==X(t,j))&&(h=!0))}).on("focusin submit validate","form,."+k,function(t){if(null===X(this,j)){var i,n=e(this);!n.data(p)&&(i=l(this))&&(e.isEmptyObject(i.fields)?(X(this,j,j),n.off(v).removeData(p)):"focusin"===t.type?i._focusin(t):i._submit(t))}}),new a({fallback:"This field is not valid.",loading:"Validating..."}),new s({required:function(t,i){var n=this,s=P(n.value),a=!0;if(i)if(1===i.length){if(g(i[0])){if(n.rules[i[0]]&&!s&&!n.test(t,i[0]))return null}else if(!s&&!e(i[0],n.$el).length)return null}else if("not"===i[0])e.each(i.slice(1),function(){return a=s!==P(this)});else if("from"===i[0]){var r,l=n.$el.find(i[1]),o="_validated_";return a=l.filter(function(){var e=n.getField(this);return e&&!!P(e.getValue())}).length>=(i[2]||1),a?s||(r=null):r=u(l[0],n)||!1,e(t).data(o)||l.data(o,1).each(function(){t!==this&&n._validate(this)}).removeData(o),r}return a&&!!s},integer:function(e,t){var i,n="0|",s="[1-9]\\d*",a=t?t[0]:"*";switch(a){case"+":i=s;break;case"-":i="-"+s;break;case"+0":i=n+s;break;case"-0":i=n+"-"+s;break;default:i=n+"-?"+s}return i="^(?:"+i+")$",new RegExp(i).test(this.value)||this.messages.integer&&this.messages.integer[a]},match:function(t,i){if(i){var n,s,a,r,l,o,u,d=this,c=!0,g="eq";if(1===i.length?a=i[0]:(g=i[0],a=i[1]),l=m(a),o=d.$el.find(l)[0]){if(u=d.getField(o),n=d.value,s=u.getValue(),d._match||(d.$el.on("valid"+y+v,l,function(){e(t).trigger("validate")}),d._match=u._match=1),!d.required&&""===n&&""===s)return null;if(r=i[2],r&&(/^date(time)?$/i.test(r)?(n=f(n),s=f(s)):"time"===r&&(n=+n.replace(/:/g,""),s=+s.replace(/:/g,""))),"eq"!==g&&!isNaN(+n)&&isNaN(+s))return!0;switch(g){case"lt":c=+n<+s;break;case"lte":c=+n<=+s;break;case"gte":c=+n>=+s;break;case"gt":c=+n>+s;break;case"neq":c=n!==s;break;default:c=n===s}return c||J(d.messages.match)&&d.messages.match[g].replace("{1}",d._getDisplay(t,u.display||a))}}},range:function(e,t){return this.getRangeMsg(this.value,t)},checked:function(e,t){if(c(e)){var i,n,s=this;return e.name?n=s.$el.find('input[name="'+e.name+'"]').filter(function(){var e=this;return!i&&c(e)&&(i=e),!e.disabled&&e.checked}).length:(i=e,n=i.checked),t?s.getRangeMsg(n,t):!!n||u(i,s,"")||s.messages.required||!1}},length:function(e,t){var i=this.value,n=("true"===t[1]?i.replace(q,"xx"):i).length;return this.getRangeMsg(n,t,t[1]?"_2":"")},remote:function(t,i){if(i){var n,s=this,a=I.exec(i[0]),r=s._rules[s._i],l={},o="",u=a[3],d=a[2]||"POST",c=(a[1]||"").toLowerCase();return r.must=!0,l[t.name]=s.value,i[1]&&e.map(i.slice(1),function(e){var t,i;~e.indexOf("=")?o+="&"+e:(t=e.split(":"),e=P(t[0]),i=P(t[1])||e,l[e]=s.$el.find(m(i)).val())}),l=e.param(l)+o,!s.must&&r.data&&r.data===l?r.result:("cors"!==c&&/^https?:/.test(u)&&!~u.indexOf(location.host)&&(n="jsonp"),e.ajax({url:u,type:d,data:l,dataType:n}))}},filter:function(e,t){var i=this.value,n=i.replace(t?new RegExp("["+t[0]+"]","gm"):H,"");n!==i&&this.setValue(n)}}),i.config=function(t,i){function n(e,t){"rules"===e?new s(t):"messages"===e?new a(t):e in K?K[e]=t:G[e]=t}J(t)?e.each(t,n):W(t)&&n(t,i)},i.setTheme=function(t,i){J(t)?e.extend(!0,Y,t):W(t)&&J(i)&&(Y[t]=e.extend(Y[t],i))},i.load=function(t){if(t){var n,s,a,r=document,l={},o=r.scripts[0];t.replace(/([^?=&]+)=([^&#]*)/g,function(e,t,i){l[t]=i}),n=l.dir||i.dir,i.css||""===l.css||(s=r.createElement("link"),s.rel="stylesheet",s.href=i.css=n+"jquery.validator.css",o.parentNode.insertBefore(s,o)),!i.local&&~t.indexOf("local")&&""!==l.local&&(i.local=(l.local||r.documentElement.lang||"en").replace("_","-"),i.pending=1,s=r.createElement("script"),s.src=n+"local/"+i.local+".js",a="onload"in s?"onload":"onreadystatechange",s[a]=function(){s.readyState&&!/loaded|complete/.test(s.readyState)||(s=s[a]=null,delete i.pending,e(window).triggerHandler("validatorready"))},o.parentNode.insertBefore(s,o))}},function(){for(var e,t,n=document.scripts,s=n.length,a=/(.*validator(?:\.min)?.js)(\?.*(?:local|css|dir)(?:=[\w\-]*)?)?/;s--&&!t;)e=n[s],t=(e.hasAttribute?e.src:e.getAttribute("src",4)||"").match(a);t&&(i.dir=t[1].split("/").slice(0,-1).join("/")+"/",i.load(t[2]))}(),e[p]=i});