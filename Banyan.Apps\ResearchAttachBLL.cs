﻿using Banyan.Code;
using Banyan.MeetDomain;
using DAL.Base;
using System;

namespace Banyan.Apps
{
    public class ResearchAttachBLL : BaseDAL<Attachment>
    {


        public Attachment GetModelByCache(int id)
        {
            string cacheKey = string.Format(RedisKey.Research_attachment, id);
            Attachment model = RedisUtil.Get<Attachment>(cacheKey);
            if (model == null)
            {
                model = base.GetModel(id);
                RedisUtil.Set<Attachment>(cacheKey, model, TimeSpan.FromMinutes(10));
            }
            return model;
        }

        public Attachment GetCache(int articleId, int types)
        {
            string cacheKey = string.Format(RedisKey.attachment_comb_research, types, articleId);
            Attachment model = RedisUtil.Get<Attachment>(cacheKey);
            if (model == null)
            {
                model = base.GetModel($"Types={types} and KeyId={articleId}");
                RedisUtil.Set<Attachment>(cacheKey, model, TimeSpan.FromMinutes(10));
            }
            return model;
        }
    }
}
