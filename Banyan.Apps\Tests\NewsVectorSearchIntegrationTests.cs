using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Banyan.Apps;
using Banyan.Domain;
using Banyan.Code;

namespace Banyan.Apps.Tests
{
    /// <summary>
    /// NewsVectorSearch类的集成测试
    /// 这些测试需要真实的数据库连接和缓存服务
    /// </summary>
    [TestClass]
    public class NewsVectorSearchIntegrationTests
    {
        private NewsVectorSearch _newsVectorSearch;
        private int _testUserId;
        private int _testNewsId;
        private List<int> _createdTestData;

        [TestInitialize]
        public void Setup()
        {
            _newsVectorSearch = new NewsVectorSearch();
            _createdTestData = new List<int>();
            
            // 创建测试数据
            SetupTestData();
        }

        [TestCleanup]
        public void Cleanup()
        {
            // 清理测试数据
            CleanupTestData();
        }

        private void SetupTestData()
        {
            try
            {
                // 创建测试用户（如果不存在）
                var memberBLL = new MemberBLL();
                var testUser = memberBLL.GetModel("OpenId = 'test_integration_user'");
                if (testUser == null)
                {
                    var newUser = new Member
                    {
                        OpenId = "test_integration_user",
                        RealName = "集成测试用户",
                        UserName = "integration_test",
                        CreateTime = DateTime.Now,
                        UpdateTime = DateTime.Now
                    };
                    var userId = memberBLL.Add(newUser);
                    _testUserId = Convert.ToInt32(userId);
                    _createdTestData.Add(_testUserId);
                }
                else
                {
                    _testUserId = testUser.Id;
                }

                // 创建测试新闻
                var newsBLL = new NewsBLL();
                var testNews = new News
                {
                    Title = "集成测试新闻 - " + DateTime.Now.ToString("yyyyMMddHHmmss"),
                    Content = "这是一篇用于集成测试的新闻内容，包含人工智能、机器学习等关键词。",
                    Tag = "人工智能,机器学习,科技",
                    Classify = "科技新闻",
                    Source = "集成测试",
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now,
                    NewsVector = string.Join(",", Enumerable.Range(1, 1024).Select(i => (i * 0.001).ToString()))
                };
                
                var newsId = newsBLL.Add(testNews);
                _testNewsId = Convert.ToInt32(newsId);
                _createdTestData.Add(_testNewsId);
            }
            catch (Exception ex)
            {
                Assert.Fail($"设置测试数据失败: {ex.Message}");
            }
        }

        private void CleanupTestData()
        {
            try
            {
                // 清理缓存
                var cache = CacheFactory.Cache();
                cache.RemoveCache($"user_read_history:{_testUserId}");
                cache.RemoveCache($"user_interest_vector:{_testUserId}");

                // 清理数据库中的测试数据
                if (_testNewsId > 0)
                {
                    var newsBLL = new NewsBLL();
                    newsBLL.Delete(_testNewsId);
                }

                // 注意：通常不删除测试用户，因为可能有外键约束
                // 如果需要删除，请确保先清理相关的依赖数据
            }
            catch (Exception ex)
            {
                // 清理失败不应该影响测试结果，只记录日志
                Console.WriteLine($"清理测试数据失败: {ex.Message}");
            }
        }

        [TestMethod]
        public async Task RecordUserReadNewsAsync_RealDatabase_Success()
        {
            // Arrange
            Assert.IsTrue(_testUserId > 0, "测试用户ID应该大于0");
            Assert.IsTrue(_testNewsId > 0, "测试新闻ID应该大于0");

            // Act
            var result = await _newsVectorSearch.RecordUserReadNewsAsync(_testUserId, _testNewsId);

            // Assert
            Assert.IsTrue(result, "记录用户阅读行为应该成功");

            // 验证缓存中是否有记录
            var cache = CacheFactory.Cache();
            var cacheKey = $"user_read_history:{_testUserId}";
            var readHistory = cache.GetCache<List<int>>(cacheKey);
            
            Assert.IsNotNull(readHistory, "阅读历史缓存应该存在");
            Assert.IsTrue(readHistory.Contains(_testNewsId), "阅读历史应该包含测试新闻ID");
        }

        [TestMethod]
        public async Task GetUserInterestVector_RealDatabase_ReturnsVector()
        {
            // Arrange
            // 先记录一些阅读行为以建立用户兴趣
            await _newsVectorSearch.RecordUserReadNewsAsync(_testUserId, _testNewsId);
            
            // 等待一小段时间让异步更新完成
            await Task.Delay(1000);

            // Act
            var result = await _newsVectorSearch.GetUserInterestVector(_testUserId);

            // Assert
            // 根据实现的fallback机制，应该能够返回某种形式的向量
            // 即使UserProfileBLL没有向量，也应该通过标签关联或阅读历史生成
            Assert.IsNotNull(result, "应该能够获取到用户兴趣向量");
            
            if (result != null)
            {
                Assert.IsTrue(result.Length > 0, "向量维度应该大于0");
                Assert.IsTrue(result.Any(v => v != 0), "向量不应该全为0");
            }
        }

        [TestMethod]
        public async Task GetRecommendationsByReadingHistory_RealDatabase_ReturnsRecommendations()
        {
            // Arrange
            // 先记录一些阅读行为
            await _newsVectorSearch.RecordUserReadNewsAsync(_testUserId, _testNewsId);
            
            // 创建更多测试新闻以便推荐
            var newsBLL = new NewsBLL();
            var additionalNewsIds = new List<int>();
            
            for (int i = 0; i < 3; i++)
            {
                var additionalNews = new News
                {
                    Title = $"相关测试新闻 {i} - " + DateTime.Now.ToString("yyyyMMddHHmmss"),
                    Content = "这是另一篇相关的测试新闻，也包含人工智能相关内容。",
                    Tag = "人工智能,深度学习",
                    Classify = "科技新闻",
                    Source = "测试来源",
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now,
                    NewsVector = string.Join(",", Enumerable.Range(1, 1024).Select(j => ((j + i) * 0.001).ToString()))
                };
                
                var newsId = newsBLL.Add(additionalNews);
                var id = Convert.ToInt32(newsId);
                additionalNewsIds.Add(id);
                _createdTestData.Add(id);
            }

            var filters = new NewsSearchFilters();

            // Act
            var result = await _newsVectorSearch.GetRecommendationsByReadingHistory(_testUserId, 5, 0.1, filters);

            // Assert
            Assert.IsNotNull(result, "推荐结果不应该为null");
            
            // 验证推荐结果不包含已读新闻
            foreach (var recommendation in result)
            {
                Assert.AreNotEqual(_testNewsId, recommendation.NewsId, "推荐结果不应该包含已读新闻");
            }

            // 清理额外创建的新闻
            foreach (var newsId in additionalNewsIds)
            {
                try
                {
                    newsBLL.Delete(newsId);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"清理额外新闻失败: {ex.Message}");
                }
            }
        }

        [TestMethod]
        public async Task FullWorkflow_RecordAndRecommend_WorksEndToEnd()
        {
            // Arrange
            var filters = new NewsSearchFilters
            {
                Category = "科技新闻"
            };

            // Act & Assert

            // 1. 记录用户阅读行为
            var recordResult = await _newsVectorSearch.RecordUserReadNewsAsync(_testUserId, _testNewsId);
            Assert.IsTrue(recordResult, "记录阅读行为应该成功");

            // 2. 等待异步处理完成
            await Task.Delay(2000);

            // 3. 获取用户兴趣向量
            var userVector = await _newsVectorSearch.GetUserInterestVector(_testUserId);
            // 注意：根据实际实现，这里可能返回null，这是正常的

            // 4. 基于阅读历史获取推荐
            var recommendations = await _newsVectorSearch.GetRecommendationsByReadingHistory(_testUserId, 10, 0.1, filters);
            Assert.IsNotNull(recommendations, "推荐结果不应该为null");

            // 5. 验证推荐质量
            foreach (var rec in recommendations)
            {
                Assert.IsTrue(rec.NewsId > 0, "推荐新闻ID应该有效");
                Assert.IsTrue(rec.Similarity >= 0 && rec.Similarity <= 1, "相似度应该在0-1之间");
                Assert.AreNotEqual(_testNewsId, rec.NewsId, "不应该推荐已读新闻");
            }

            Console.WriteLine($"成功为用户 {_testUserId} 生成了 {recommendations.Count} 条推荐");
        }

        [TestMethod]
        public async Task ConcurrentAccess_MultipleUsers_HandledCorrectly()
        {
            // Arrange
            var tasks = new List<Task<bool>>();
            var userIds = new List<int> { _testUserId };
            
            // 如果有多个测试用户，可以添加到列表中
            // 这里为了简化，只使用一个用户进行并发测试

            // Act
            // 模拟多个并发请求
            for (int i = 0; i < 10; i++)
            {
                var userId = userIds[i % userIds.Count];
                tasks.Add(_newsVectorSearch.RecordUserReadNewsAsync(userId, _testNewsId));
            }

            var results = await Task.WhenAll(tasks);

            // Assert
            Assert.IsTrue(results.All(r => r), "所有并发请求都应该成功");

            // 验证缓存状态一致性
            var cache = CacheFactory.Cache();
            var cacheKey = $"user_read_history:{_testUserId}";
            var readHistory = cache.GetCache<List<int>>(cacheKey);
            
            Assert.IsNotNull(readHistory, "阅读历史缓存应该存在");
            Assert.IsTrue(readHistory.Contains(_testNewsId), "阅读历史应该包含测试新闻");
            
            // 验证没有重复记录
            var duplicateCount = readHistory.Count(id => id == _testNewsId);
            Assert.AreEqual(1, duplicateCount, "同一新闻不应该被重复记录");
        }

        [TestMethod]
        public async Task LargeDataSet_Performance_AcceptableResponseTime()
        {
            // Arrange
            var startTime = DateTime.Now;
            var tasks = new List<Task>();

            // Act
            // 模拟大量数据操作
            for (int i = 0; i < 50; i++)
            {
                tasks.Add(_newsVectorSearch.RecordUserReadNewsAsync(_testUserId, _testNewsId));
            }

            await Task.WhenAll(tasks);

            // 测试推荐性能
            var recommendationTasks = new List<Task<List<NewsVectorSimilarity>>>();
            for (int i = 0; i < 10; i++)
            {
                recommendationTasks.Add(_newsVectorSearch.GetRecommendationsByReadingHistory(_testUserId, 10, 0.1, new NewsSearchFilters()));
            }

            var recommendationResults = await Task.WhenAll(recommendationTasks);
            var endTime = DateTime.Now;

            // Assert
            var totalTime = (endTime - startTime).TotalSeconds;
            Assert.IsTrue(totalTime < 30, $"大数据集操作应该在30秒内完成，实际用时: {totalTime}秒");
            
            Assert.IsTrue(recommendationResults.All(r => r != null), "所有推荐请求都应该返回结果");
            
            Console.WriteLine($"大数据集测试完成，总用时: {totalTime:F2}秒");
        }
    }
}
