﻿using Banyan.Code;
using Banyan.Domain;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Banyan.Apps
{
    public class ExitScoreStageBLL : DAL.Base.BaseDAL<ExitScoreStage>
    {
        public static readonly ExitScoreBLL exitScoreBll = new ExitScoreBLL();

        public object ScoreStages(int exitID)
        {
            PortfolioExit portfolio = new PortfolioExitBLL().GetModel(exitID);
            if (portfolio == null)
            {
                return AjaxHelper.JFail("项目不存在");
            }

            List<ExitScoreStage> list = GetList($"exitId={exitID}");
            //int roleId = new PortfolioBLL().GetProjectRoleId(portfolioID);
            object data = list.Select(item =>
            {
                return new
                {
                    item.Id,
                    item.State,
                    item.ActName,
                    item.Description,
                    item.Average,
                    PartnerScoreAvg = new ScoreService().CalPartnerExitScoreAvg(item.exitID, item.Id),
                    //InvestScoreAvg = CalInvestScoreAvg(item.ProjectId, item.Id, roleId),
                    item.People,
                    AddTime = item.AddTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    EndTime = item.EndTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    ProjectName = portfolio.Name,
                };
            });
            return AjaxHelper.JOk(data);
        }

        public object GetStageList(int exitId) // 和ScoreStages接口区别不大，时间表示不同
        {
            Member user = new MemberBLL().GetLogOnUser();
            return AjaxHelper.JOk(new ScoreService().GetExitStageList(user, exitId));
        }
        public object ScoreSet(ExitScoreStage model)
        {
            bool isExist = Exists($"exitId={model.exitID} AND State=1");
            if (isExist)
                return AjaxHelper.JFail("存在未结束的评分活动，请先结束");

            model.State = 1;
            int modelId = Convert.ToInt32(Add(model));
            return modelId > 0 ? AjaxHelper.JOk("1") : AjaxHelper.JFail("创建失败");
        }

        public object ScoreSwitch(int id, int state)
        {
            ExitScoreStage model = GetModel(id);
            if (model == null)
                return AjaxHelper.JFail("评分活动不存在");

            model.State = (byte)state;
            model.EndTime = DateTime.Now;
            bool result = Update(model, "State,EndTime");

            return result ? AjaxHelper.JOk("1") : AjaxHelper.JFail("操作失败");
        }

        /// <summary>
        /// 场次统计信息
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public bool UpdateScoreStage(int exitId, int id, string userName, int score)
        {
           ExitScoreStage stage = GetModel(id);
            if (stage == null)
                return false;

            ExitScore model = new ExitScoreBLL().GetModelBySql($"SELECT COUNT(*) AS UserId, round(AVG(score), 1) AS Score FROM ExitScore WHERE exitId = {exitId} and StageId={id}");
            if (model == null)
                return false;

            stage.Average = model.Score;
            stage.People = model.UserId;
            return Update(stage, "Average,People");
        }
    }
}
