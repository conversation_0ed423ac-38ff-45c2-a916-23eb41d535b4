@{
    ViewBag.Title = "推荐历史";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">推荐历史</h4>
                    <p class="card-category">查看您的推荐记录</p>
                </div>
                <div class="card-body">
                    <!-- Date Range Filter -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="filter-panel">
                                <form id="dateRangeForm" class="form-inline">
                                    <div class="form-group mx-sm-3 mb-2">
                                        <label for="startDate" class="mr-2">开始日期:</label>
                                        <input type="date" class="form-control" id="startDate" name="startDate">
                                    </div>
                                    <div class="form-group mx-sm-3 mb-2">
                                        <label for="endDate" class="mr-2">结束日期:</label>
                                        <input type="date" class="form-control" id="endDate" name="endDate">
                                    </div>
                                    <button type="submit" class="btn btn-primary mb-2">应用过滤</button>
                                    <button type="button" id="resetDates" class="btn btn-secondary mb-2 ml-2">重置</button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Loading Indicator -->
                    <div id="loadingIndicator" class="text-center" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">������...</span>
                        </div>
                        <p>���ڼ�����ʷ����...</p>
                    </div>

                    <!-- Error Message -->
                    <div id="errorMessage" class="alert alert-danger" style="display: none;">
                    </div>

                    <!-- History Table -->
                    <div class="table-responsive">
                        <table class="table table-hover" id="historyTable">
                            <thead>
                                <tr>
                                    <th>���ű���</th>
                                    <th>��Դ</th>
                                    <th>����</th>
                                    <th>�鿴ʱ��</th>
                                    <th>��ض�</th>
                                    <th>����</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- History data will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- No Data Message -->
                    <div id="noDataMessage" class="alert alert-info" style="display: none;">
                        �����Ƽ���ʷ����
                    </div>

                    <!-- Pagination -->
                    <div class="row">
                        <div class="col-md-12">
                            <nav aria-label="�Ƽ���ʷ��ҳ">
                                <ul class="pagination justify-content-center" id="historyPagination">
                                    <!-- Pagination will be generated here -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section scripts {
    <script>
        $(document).ready(function () {
            // Variables
            var currentPage = 1;
            var pageSize = 10;
            var totalPages = 1;
            var isLoading = false;

            // Set default date range (last 30 days)
            var today = new Date();
            var thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(today.getDate() - 30);

            $('#startDate').val(formatDate(thirtyDaysAgo));
            $('#endDate').val(formatDate(today));

            // Initial load
            loadHistoryData();

            // Date range form submission
            $('#dateRangeForm').on('submit', function (e) {
                e.preventDefault();
                currentPage = 1;
                loadHistoryData();
            });

            // Reset date range
            $('#resetDates').on('click', function () {
                $('#startDate').val(formatDate(thirtyDaysAgo));
                $('#endDate').val(formatDate(today));
                currentPage = 1;
                loadHistoryData();
            });

            // Load history data
            function loadHistoryData() {
                if (isLoading) return;

                isLoading = true;
                showLoading(true);
                hideError();
                $('#noDataMessage').hide();

                var startDate = $('#startDate').val();
                var endDate = $('#endDate').val();

                $.ajax({
                    url: '@Url.Action("GetUserEngagementHistory")',
                    type: 'GET',
                    data: {
                        page: currentPage,
                        pageSize: pageSize,
                        startDate: startDate,
                        endDate: endDate
                    },
                    success: function (response) {
                        showLoading(false);
                        isLoading = false;

                        if (response.code === 0 && response.data) {
                            var historyData = response.data.records;
                            totalPages = response.data.totalPages;

                            if (historyData.length === 0) {
                                $('#historyTable').hide();
                                $('#noDataMessage').show();
                                $('#historyPagination').empty();
                            } else {
                                $('#historyTable').show();
                                $('#noDataMessage').hide();
                                renderHistoryData(historyData);
                                renderPagination();
                            }
                        } else {
                            showError(response.msg || '��ȡ��ʷ����ʧ��');
                        }
                    },
                    error: function (xhr, status, error) {
                        showLoading(false);
                        isLoading = false;
                        showError('��ȡ��ʷ����ʱ��������: ' + error);
                    }
                });
            }

            // Render history data
            function renderHistoryData(historyData) {
                var tbody = $('#historyTable tbody');
                tbody.empty();

                historyData.forEach(function (item) {
                    var row = $('<tr></tr>');

                    // Format date
                    var viewDate = new Date(parseInt(item.Timestamp.substr(6)));
                    var formattedDate = viewDate.toLocaleString();

                    // Calculate relevance percentage if available
                    var relevanceHtml = '';
                    if (item.RelevanceScore !== undefined) {
                        var scorePercent = Math.round(item.RelevanceScore * 100);
                        relevanceHtml = `
                            <div class="progress" style="height: 5px;">
                                <div class="progress-bar" role="progressbar" style="width: ${scorePercent}%"></div>
                            </div>
                            <small>${scorePercent}%</small>
                        `;
                    } else {
                        relevanceHtml = '<span class="text-muted">-</span>';
                    }

                    // Build row
                    row.append(`<td>${item.NewsTitle}</td>`);
                    row.append(`<td>${item.Source || '-'}</td>`);
                    row.append(`<td>${item.Category || '-'}</td>`);
                    row.append(`<td>${formattedDate}</td>`);
                    row.append(`<td>${relevanceHtml}</td>`);
                    row.append(`
                        <td>
                            <a href="@Url.Action("News", "Index")/${item.NewsId}" class="btn btn-sm btn-outline-primary">
                                �鿴
                            </a>
                        </td>
                    `);

                    tbody.append(row);
                });
            }

            // Render pagination
            function renderPagination() {
                var pagination = $('#historyPagination');
                pagination.empty();

                // Previous button
                pagination.append(`
                    <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${currentPage - 1}" aria-label="��һҳ">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                `);

                // Page numbers
                var startPage = Math.max(1, currentPage - 2);
                var endPage = Math.min(totalPages, currentPage + 2);

                for (var i = startPage; i <= endPage; i++) {
                    pagination.append(`
                        <li class="page-item ${i === currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" data-page="${i}">${i}</a>
                        </li>
                    `);
                }

                // Next button
                pagination.append(`
                    <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${currentPage + 1}" aria-label="��һҳ">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                `);

                // Add click handlers
                $('.page-link').on('click', function (e) {
                    e.preventDefault();
                    var page = $(this).data('page');

                    if (page >= 1 && page <= totalPages && page !== currentPage) {
                        currentPage = page;
                        loadHistoryData();
                    }
                });
            }

            // Helper function to format date as YYYY-MM-DD
            function formatDate(date) {
                var year = date.getFullYear();
                var month = (date.getMonth() + 1).toString().padStart(2, '0');
                var day = date.getDate().toString().padStart(2, '0');
                return `${year}-${month}-${day}`;
            }

            // Show loading indicator
            function showLoading(show) {
                $('#loadingIndicator').toggle(show);
            }

            // Show error message
            function showError(message) {
                $('#errorMessage').text(message).show();
            }

            // Hide error message
            function hideError() {
                $('#errorMessage').hide();
            }
        });
    </script>

    <style>
        .filter-panel {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
}