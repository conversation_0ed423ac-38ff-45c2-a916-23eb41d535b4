# NewsPrecomputeService 用户推荐预计算功能使用说明

## 概述

`NewsPrecomputeService` 现在支持为所有有兴趣向量的用户预计算个性化推荐结果，提高推荐系统的响应速度。

## 新增功能

### 1. 用户推荐预计算

```csharp
// 获取服务实例
var precomputeService = NewsPrecomputeService.Instance;

// 为所有有兴趣向量的用户预计算推荐结果
var result = await precomputeService.PrecomputeUserRecommendationsAsync(
    recommendationCount: 20,  // 每个用户的推荐数量
    threshold: 0.4           // 相似度阈值
);

Console.WriteLine($"预计算完成：成功 {result.SuccessCount}，失败 {result.FailedCount}，耗时 {result.Duration.TotalSeconds:F2}秒");
```

### 2. 获取预计算的推荐结果

```csharp
// 获取指定用户的预计算推荐结果
int userId = 123;
var recommendations = precomputeService.GetPrecomputedUserRecommendations(userId);

if (recommendations != null && recommendations.Count > 0)
{
    Console.WriteLine($"用户 {userId} 有 {recommendations.Count} 条预计算推荐");
    foreach (var rec in recommendations)
    {
        Console.WriteLine($"新闻ID: {rec.NewsId}, 相似度: {rec.Similarity:F4}, 标题: {rec.News?.Title}");
    }
}
else
{
    Console.WriteLine($"用户 {userId} 没有预计算推荐");
}
```

### 3. 综合预计算（新闻相似度 + 用户推荐）

```csharp
// 同时预计算热门新闻相似度和用户推荐
var combinedResult = await precomputeService.PrecomputeAllAsync(
    hotNewsCount: 50,              // 热门新闻数量
    similarCount: 10,              // 每篇新闻的相似新闻数量
    newsThreshold: 0.5,            // 新闻相似度阈值
    userRecommendationCount: 20,   // 每个用户的推荐数量
    userThreshold: 0.4             // 用户推荐相似度阈值
);

Console.WriteLine($"综合预计算完成，总耗时: {combinedResult.Duration.TotalSeconds:F2}秒");
Console.WriteLine($"新闻预计算：成功 {combinedResult.NewsPrecomputeResult.SuccessCount}");
Console.WriteLine($"用户推荐预计算：成功 {combinedResult.UserPrecomputeResult.SuccessCount}");
```

### 4. 获取预计算统计信息

```csharp
// 获取预计算统计信息
var stats = await precomputeService.GetPrecomputeStatisticsAsync();

Console.WriteLine($"有兴趣向量的用户总数: {stats.TotalUsersWithVectors}");
Console.WriteLine($"有预计算推荐的用户数量: {stats.UsersWithPrecomputedRecommendations}");
Console.WriteLine($"候选新闻数量: {stats.CandidateNewsCount}");
Console.WriteLine($"预计算覆盖率: {stats.CoverageRate:F2}%");
```

### 5. 缓存管理

```csharp
// 清除指定用户的预计算推荐缓存
bool cleared = precomputeService.ClearUserPrecomputedRecommendations(userId);
Console.WriteLine($"用户 {userId} 缓存清除结果: {cleared}");

// 清除所有预计算缓存
bool allCleared = precomputeService.ClearAllPrecomputedCache();
Console.WriteLine($"所有预计算缓存清除结果: {allCleared}");
```

## 缓存机制

### 缓存键格式
- 用户推荐缓存：`precomputed_user_recommendations:{userId}`
- 新闻相似度缓存：`precomputed_similar_news:{newsId}`

### 缓存过期时间
- 用户推荐缓存：12小时
- 新闻相似度缓存：24小时

## 使用场景

### 1. 定时任务预计算
```csharp
// 在定时任务中执行预计算
public async Task ScheduledPrecompute()
{
    var service = NewsPrecomputeService.Instance;
    
    // 每天凌晨2点执行综合预计算
    var result = await service.PrecomputeAllAsync();
    
    // 记录日志
    Logger.Info($"定时预计算完成，新闻：{result.NewsPrecomputeResult.SuccessCount}，用户：{result.UserPrecomputeResult.SuccessCount}");
}
```

### 2. 推荐系统集成
```csharp
// 在推荐系统中优先使用预计算结果
public async Task<List<News>> GetUserRecommendations(int userId)
{
    var precomputeService = NewsPrecomputeService.Instance;
    
    // 首先尝试获取预计算结果
    var precomputedRecommendations = precomputeService.GetPrecomputedUserRecommendations(userId);
    
    if (precomputedRecommendations != null && precomputedRecommendations.Count > 0)
    {
        // 使用预计算结果
        return precomputedRecommendations.Select(r => r.News).ToList();
    }
    else
    {
        // 回退到实时计算
        var recommendationEngine = new NewsRecommendationEngine();
        return await recommendationEngine.GetPersonalizedRecommendationsAsync(userId);
    }
}
```

## 性能优化

1. **批量处理**：预计算过程中使用批量获取向量，减少数据库查询次数
2. **缓存分层**：使用Redis缓存预计算结果，提高访问速度
3. **异步处理**：所有预计算操作都是异步的，不阻塞主线程
4. **进度监控**：提供详细的进度日志，便于监控预计算状态

## 注意事项

1. **内存使用**：预计算过程可能消耗较多内存，建议在低峰期执行
2. **数据一致性**：预计算结果有缓存过期时间，需要定期更新
3. **错误处理**：预计算过程中的错误不会影响系统正常运行
4. **监控告警**：建议监控预计算的成功率和耗时，及时发现问题

## 配置建议

- **推荐数量**：建议每个用户预计算20-50条推荐
- **相似度阈值**：建议设置为0.4-0.6之间
- **执行频率**：建议每天执行1-2次预计算
- **缓存时间**：根据业务需求调整缓存过期时间