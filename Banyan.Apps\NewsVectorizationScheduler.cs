using System;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using Banyan.Code;
using Banyan.Domain;

namespace Banyan.Apps
{
    /// <summary>
    /// 新闻向量化定时任务服务
    /// 定期扫描并向量化未向量化的新闻
    /// </summary>
    public class NewsVectorizationScheduler : IDisposable
    {
        private readonly NewsVectorizationService _vectorizationService;
        private NewsBLL _newsBLL;
        private readonly object _newsBLLLock = new object();
        private Timer _timer;
        private bool _isRunning = false;
        private DateTime _lastRunTime = DateTime.MinValue;
        private DateTime _nextScheduledRunTime = DateTime.MinValue;
        private VectorizationResult _lastRunResult;
        private readonly List<string> _statusMessages = new List<string>();

        #region 配置常量

        // 定时任务配置
        private const int SCAN_INTERVAL_MINUTES = 30; // 每30分钟扫描一次
        private const int BATCH_SIZE = 100; // 每次处理100篇新闻
        private const int MAX_RETRY_COUNT = 3; // 最大重试次数
        private const int RETRY_DELAY_MINUTES = 5; // 重试延迟时间（分钟）
        private const int MAX_STATUS_MESSAGES = 20; // 状态消息最大数量
        private const int HIGH_PRIORITY_DAYS = 7; // 高优先级新闻的天数（最近7天的新闻优先处理）
        private const int MEDIUM_PRIORITY_DAYS = 30; // 中优先级新闻的天数（最近30天的新闻次优先处理）
        private const int MAX_PARALLEL_TASKS = 5; // 最大并行任务数
        private const int MEMORY_CHECK_THRESHOLD_MB = 1024; // 内存检查阈值（MB）
        private const int CPU_USAGE_CHECK_THRESHOLD = 80; // CPU使用率检查阈值（%）

        #endregion

        public NewsVectorizationScheduler()
        {
            _vectorizationService = new NewsVectorizationService();
            // 移除构造函数中的直接初始化，改为延迟初始化
        }

        /// <summary>
        /// 获取NewsBLL实例（延迟初始化）
        /// </summary>
        private NewsBLL NewsBLL
        {
            get
            {
                if (_newsBLL == null)
                {
                    lock (_newsBLLLock)
                    {
                        if (_newsBLL == null)
                        {
                            _newsBLL = new NewsBLL();
                        }
                    }
                }
                return _newsBLL;
            }
        }

        /// <summary>
        /// 启动定时任务
        /// </summary>
        public void Start()
        {
            try
            {
                if (_isRunning)
                {
                    Logger.Warn("新闻向量化定时任务已在运行中");
                    return;
                }

                Logger.Info("启动新闻向量化定时任务");

                // 立即执行一次
                _ = Task.Run(async () => await ExecuteVectorizationAsync());

                // 设置定时器，每30分钟执行一次
                _timer = new Timer(async _ => await ExecuteVectorizationAsync(), null,
                    TimeSpan.FromMinutes(SCAN_INTERVAL_MINUTES),
                    TimeSpan.FromMinutes(SCAN_INTERVAL_MINUTES));

                _isRunning = true;
                Logger.Info($"新闻向量化定时任务已启动，扫描间隔: {SCAN_INTERVAL_MINUTES}分钟");
            }
            catch (Exception ex)
            {
                Logger.Error("启动新闻向量化定时任务失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 停止定时任务
        /// </summary>
        public void Stop()
        {
            try
            {
                if (!_isRunning)
                {
                    Logger.Warn("新闻向量化定时任务未在运行");
                    return;
                }

                Logger.Info("停止新闻向量化定时任务");

                _timer?.Dispose();
                _timer = null;
                _isRunning = false;

                Logger.Info("新闻向量化定时任务已停止");
            }
            catch (Exception ex)
            {
                Logger.Error("停止新闻向量化定时任务失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 执行向量化任务
        /// </summary>
        /// <returns>处理结果</returns>
        private async Task ExecuteVectorizationAsync()
        {
            try
            {
                _lastRunTime = DateTime.Now;
                AddStatusMessage($"开始执行新闻向量化定时任务 - {_lastRunTime:yyyy-MM-dd HH:mm:ss}");
                Logger.Info("开始执行新闻向量化定时任务");

                // 检查系统资源使用情况
                if (!CheckSystemResources())
                {
                    var resourceMessage = "系统资源使用率过高，跳过本次执行";
                    AddStatusMessage(resourceMessage);
                    Logger.Warn(resourceMessage);
                    return;
                }

                var retryCount = 0;
                var success = false;

                while (retryCount < MAX_RETRY_COUNT && !success)
                {
                    try
                    {
                        // 处理高优先级新闻（最近添加的新闻）
                        var highPriorityResult = await ProcessPrioritizedNewsAsync(PriorityLevel.High);

                        // 处理中优先级新闻
                        var mediumPriorityResult = await ProcessPrioritizedNewsAsync(PriorityLevel.Medium);

                        // 处理低优先级新闻
                        var lowPriorityResult = await ProcessPrioritizedNewsAsync(PriorityLevel.Low);

                        // 合并结果
                        var result = MergeResults(highPriorityResult, mediumPriorityResult, lowPriorityResult);

                        // 保存结果以供状态报告使用
                        _lastRunResult = result;

                        // 计算下次运行时间
                        _nextScheduledRunTime = DateTime.Now.AddMinutes(SCAN_INTERVAL_MINUTES);

                        // 记录执行结果
                        var statusMessage = $"任务执行完成 - 总计: {result.TotalProcessed}，成功: {result.SuccessCount}，" +
                                           $"失败: {result.FailedCount}，耗时: {result.Duration.TotalSeconds:F2}秒";
                        AddStatusMessage(statusMessage);

                        Logger.Info($"新闻向量化定时任务执行完成，总计: {result.TotalProcessed}，成功: {result.SuccessCount}，" +
                                   $"失败: {result.FailedCount}，耗时: {result.Duration.TotalSeconds:F2}秒");

                        if (result.FailedCount > 0)
                        {
                            var failedMessage = $"有 {result.FailedCount} 篇新闻处理失败";
                            AddStatusMessage(failedMessage);
                            Logger.Warn($"新闻向量化定时任务{failedMessage}");

                            // 记录详细的错误信息
                            foreach (var error in result.ErrorMessages)
                            {
                                Logger.Warn($"错误详情: {error}");
                            }
                        }

                        success = true;
                    }
                    catch (Exception ex)
                    {
                        retryCount++;
                        var retryMessage = $"执行失败，重试次数: {retryCount}/{MAX_RETRY_COUNT} - {ex.Message}";
                        AddStatusMessage(retryMessage);
                        Logger.Error($"新闻向量化定时任务执行失败，重试次数: {retryCount}/{MAX_RETRY_COUNT}", ex);

                        if (retryCount < MAX_RETRY_COUNT)
                        {
                            // 等待一段时间后重试，使用指数退避策略
                            int delayMinutes = RETRY_DELAY_MINUTES * retryCount;
                            AddStatusMessage($"等待 {delayMinutes} 分钟后重试...");
                            await Task.Delay(TimeSpan.FromMinutes(delayMinutes));
                        }
                    }
                }

                if (!success)
                {
                    var failureMessage = $"执行失败，已达到最大重试次数: {MAX_RETRY_COUNT}";
                    AddStatusMessage(failureMessage);
                    Logger.Error($"新闻向量化定时任务{failureMessage}");
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"执行异常: {ex.Message}";
                AddStatusMessage(errorMessage);
                Logger.Error("新闻向量化定时任务执行异常", ex);
            }
        }

        /// <summary>
        /// 优先级级别枚举
        /// </summary>
        private enum PriorityLevel
        {
            High,   // 高优先级（最近7天的新闻）
            Medium, // 中优先级（最近30天的新闻）
            Low     // 低优先级（其他新闻）
        }

        /// <summary>
        /// 处理指定优先级的新闻
        /// </summary>
        /// <param name="priorityLevel">优先级级别</param>
        /// <returns>处理结果</returns>
        private async Task<VectorizationResult> ProcessPrioritizedNewsAsync(PriorityLevel priorityLevel)
        {
            try
            {
                // 根据优先级确定批量大小和并行度
                int batchSize;
                int parallelism;
                string whereClause;
                string priorityName;

                switch (priorityLevel)
                {
                    case PriorityLevel.High:
                        // 高优先级：最近7天的新闻，使用较大的批量和并行度
                        batchSize = BATCH_SIZE;
                        parallelism = MAX_PARALLEL_TASKS;
                        whereClause = $"(VectorStatus = 0 OR VectorStatus IS NULL) AND CreateTime >= '{DateTime.Now.AddDays(-HIGH_PRIORITY_DAYS):yyyy-MM-dd}'";
                        priorityName = "高优先级";
                        break;
                    case PriorityLevel.Medium:
                        // 中优先级：最近30天但不是最近7天的新闻，使用中等的批量和并行度
                        batchSize = BATCH_SIZE / 2;
                        parallelism = MAX_PARALLEL_TASKS / 2;
                        whereClause = $"(VectorStatus = 0 OR VectorStatus IS NULL) AND CreateTime >= '{DateTime.Now.AddDays(-MEDIUM_PRIORITY_DAYS):yyyy-MM-dd}' AND CreateTime < '{DateTime.Now.AddDays(-HIGH_PRIORITY_DAYS):yyyy-MM-dd}'";
                        priorityName = "中优先级";
                        break;
                    default: // PriorityLevel.Low
                        // 低优先级：30天前的新闻，使用较小的批量和并行度
                        batchSize = BATCH_SIZE / 4;
                        parallelism = MAX_PARALLEL_TASKS / 4;
                        whereClause = $"(VectorStatus = 0 OR VectorStatus IS NULL) AND CreateTime < '{DateTime.Now.AddDays(-MEDIUM_PRIORITY_DAYS):yyyy-MM-dd}'";
                        priorityName = "低优先级";
                        break;
                }

                // 确保并行度至少为1
                parallelism = Math.Max(1, parallelism);

                // 获取符合条件的新闻数量
                int count = NewsBLL.GetCount(whereClause);

                if (count == 0)
                {
                    // 没有符合条件的新闻，返回空结果
                    return new VectorizationResult
                    {
                        StartTime = DateTime.Now,
                        EndTime = DateTime.Now,
                        TotalProcessed = 0,
                        SuccessCount = 0,
                        FailedCount = 0
                    };
                }

                Logger.Info($"开始处理{priorityName}新闻，数量: {count}，批量大小: {batchSize}，并行度: {parallelism}");
                AddStatusMessage($"开始处理{priorityName}新闻，数量: {count}");

                // 获取符合条件的新闻列表
                var newsList = NewsBLL.GetList(whereClause, Math.Min(batchSize, count), 1, "Id, Title, Content, Subject, CreateTime", "CreateTime DESC");

                if (newsList.Count == 0)
                {
                    // 没有获取到新闻，返回空结果
                    return new VectorizationResult
                    {
                        StartTime = DateTime.Now,
                        EndTime = DateTime.Now,
                        TotalProcessed = 0,
                        SuccessCount = 0,
                        FailedCount = 0
                    };
                }

                // 处理新闻
                var result = new VectorizationResult { StartTime = DateTime.Now };

                // 使用并行处理
                using (var semaphore = new SemaphoreSlim(parallelism))
                {
                    var tasks = new List<Task<bool>>();

                    foreach (var news in newsList)
                    {
                        await semaphore.WaitAsync();

                        var task = Task.Run(async () =>
                        {
                            try
                            {
                                return await _vectorizationService.VectorizeSingleNewsAsync(news, NewsBLL);
                            }
                            finally
                            {
                                semaphore.Release();
                            }
                        });

                        tasks.Add(task);
                    }

                    // 等待所有任务完成
                    var results = await Task.WhenAll(tasks);

                    // 统计结果
                    result.TotalProcessed = newsList.Count;
                    result.SuccessCount = results.Count(r => r);
                    result.FailedCount = results.Count(r => !r);
                    result.EndTime = DateTime.Now;
                }

                Logger.Info($"{priorityName}新闻处理完成，总计: {result.TotalProcessed}，成功: {result.SuccessCount}，失败: {result.FailedCount}，耗时: {result.Duration.TotalSeconds:F2}秒");
                AddStatusMessage($"{priorityName}新闻处理完成，成功: {result.SuccessCount}，失败: {result.FailedCount}");

                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"处理优先级新闻失败: {ex.Message}", ex);

                // 返回错误结果
                var result = new VectorizationResult
                {
                    StartTime = DateTime.Now,
                    EndTime = DateTime.Now,
                    TotalProcessed = 0,
                    SuccessCount = 0,
                    FailedCount = 0,
                    ErrorMessages = new List<string> { $"处理优先级新闻失败: {ex.Message}" }
                };

                return result;
            }
        }

        /// <summary>
        /// 合并多个处理结果
        /// </summary>
        /// <param name="results">处理结果列表</param>
        /// <returns>合并后的结果</returns>
        private VectorizationResult MergeResults(params VectorizationResult[] results)
        {
            var mergedResult = new VectorizationResult
            {
                StartTime = results.Min(r => r.StartTime),
                EndTime = results.Max(r => r.EndTime),
                TotalProcessed = results.Sum(r => r.TotalProcessed),
                SuccessCount = results.Sum(r => r.SuccessCount),
                FailedCount = results.Sum(r => r.FailedCount),
                ErrorMessages = new List<string>()
            };

            // 合并错误消息
            foreach (var result in results)
            {
                if (result.ErrorMessages != null && result.ErrorMessages.Count > 0)
                {
                    mergedResult.ErrorMessages.AddRange(result.ErrorMessages);
                }
            }

            return mergedResult;
        }

        /// <summary>
        /// 检查系统资源使用情况
        /// </summary>
        /// <returns>是否可以执行任务</returns>
        private bool CheckSystemResources()
        {
            try
            {
                // 检查内存使用情况
                var process = System.Diagnostics.Process.GetCurrentProcess();
                var memoryUsageMB = process.WorkingSet64 / (1024 * 1024);

                if (memoryUsageMB > MEMORY_CHECK_THRESHOLD_MB)
                {
                    Logger.Warn($"内存使用率过高: {memoryUsageMB}MB > {MEMORY_CHECK_THRESHOLD_MB}MB，跳过本次执行");
                    return false;
                }

                // 在实际环境中，可以添加CPU使用率检查
                // 由于.NET Framework中获取CPU使用率比较复杂，这里简化处理

                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"检查系统资源失败: {ex.Message}", ex);
                // 出错时默认允许执行
                return true;
            }
        }

        /// <summary>
        /// 添加状态消息，并保持消息列表在最大长度以内
        /// </summary>
        /// <param name="message">状态消息</param>
        private void AddStatusMessage(string message)
        {
            lock (_statusMessages)
            {
                _statusMessages.Add($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}");

                // 保持消息列表不超过最大长度
                while (_statusMessages.Count > MAX_STATUS_MESSAGES)
                {
                    _statusMessages.RemoveAt(0);
                }
            }
        }

        /// <summary>
        /// 手动触发向量化任务
        /// </summary>
        /// <param name="batchSize">批量大小</param>
        /// <param name="includeFailedOnly">是否只处理失败的新闻</param>
        /// <param name="maxRetryCount">最大重试次数</param>
        /// <param name="parallelProcessing">是否启用并行处理</param>
        /// <param name="maxDegreeOfParallelism">最大并行度</param>
        /// <param name="newsIds">指定的新闻ID列表，如果为null则处理所有符合条件的新闻</param>
        /// <returns>处理结果</returns>
        public async Task<VectorizationResult> TriggerVectorizationAsync(
            int batchSize = BATCH_SIZE,
            bool includeFailedOnly = false,
            int maxRetryCount = MAX_RETRY_COUNT,
            bool parallelProcessing = false,
            int maxDegreeOfParallelism = 3,
            List<int> newsIds = null)
        {
            try
            {
                Logger.Info($"手动触发新闻向量化任务，批量大小: {batchSize}，只处理失败: {includeFailedOnly}，" +
                           $"最大重试次数: {maxRetryCount}，并行处理: {parallelProcessing}，" +
                           $"指定新闻数量: {(newsIds != null ? newsIds.Count : 0)}");

                AddStatusMessage($"手动触发新闻向量化任务，批量大小: {batchSize}");

                VectorizationResult result;

                // 如果指定了新闻ID列表，则只处理这些新闻
                if (newsIds != null && newsIds.Count > 0)
                {
                    // 获取指定的新闻列表
                    var newsList = new List<News>();
                    foreach (var newsId in newsIds)
                    {
                        var news = NewsBLL.GetModel(newsId);
                        if (news != null)
                        {
                            newsList.Add(news);
                        }
                    }

                    if (newsList.Count == 0)
                    {
                        AddStatusMessage("未找到指定的新闻");
                        return new VectorizationResult
                        {
                            StartTime = DateTime.Now,
                            EndTime = DateTime.Now,
                            TotalProcessed = 0,
                            SuccessCount = 0,
                            FailedCount = 0
                        };
                    }

                    // 处理指定的新闻
                    result = new VectorizationResult { StartTime = DateTime.Now };

                    // 逐个处理指定的新闻
                    int successCount = 0;
                    int failedCount = 0;

                    // 使用并行或顺序处理
                    if (parallelProcessing)
                    {
                        // 使用信号量限制并发数
                        using (var semaphore = new System.Threading.SemaphoreSlim(maxDegreeOfParallelism))
                        {
                            var tasks = new List<Task<bool>>();

                            foreach (var news in newsList)
                            {
                                await semaphore.WaitAsync();

                                var task = Task.Run(async () =>
                                {
                                    try
                                    {
                                        return await _vectorizationService.VectorizeSingleNewsAsync(news, NewsBLL);
                                    }
                                    finally
                                    {
                                        semaphore.Release();
                                    }
                                });

                                tasks.Add(task);
                            }

                            // 等待所有任务完成
                            var results = await Task.WhenAll(tasks);

                            // 统计结果
                            successCount = results.Count(r => r);
                            failedCount = results.Count(r => !r);
                        }
                    }
                    else
                    {
                        // 顺序处理
                        foreach (var news in newsList)
                        {
                            bool success = await _vectorizationService.VectorizeSingleNewsAsync(news, NewsBLL);
                            if (success)
                                successCount++;
                            else
                                failedCount++;
                        }
                    }

                    // 更新结果
                    result.TotalProcessed = newsList.Count;
                    result.SuccessCount = successCount;
                    result.FailedCount = failedCount;
                    result.EndTime = DateTime.Now;
                }
                else
                {
                    // 处理所有符合条件的新闻
                    result = await _vectorizationService.ScanAndVectorizeNewsAsync(
                        batchSize: batchSize,
                        newsBLL: NewsBLL,
                        includeFailedOnly: includeFailedOnly,
                        maxRetryCount: maxRetryCount,
                        parallelProcessing: parallelProcessing,
                        maxDegreeOfParallelism: maxDegreeOfParallelism);
                }

                // 保存结果以供状态报告使用
                _lastRunResult = result;
                _lastRunTime = DateTime.Now;

                // 记录执行结果
                var statusMessage = $"手动触发任务执行完成 - 总计: {result.TotalProcessed}，成功: {result.SuccessCount}，" +
                                   $"失败: {result.FailedCount}，耗时: {result.Duration.TotalSeconds:F2}秒";
                AddStatusMessage(statusMessage);

                Logger.Info($"手动触发新闻向量化任务完成，总计: {result.TotalProcessed}，成功: {result.SuccessCount}，失败: {result.FailedCount}");

                return result;
            }
            catch (Exception ex)
            {
                var errorMessage = $"手动触发新闻向量化任务失败: {ex.Message}";
                AddStatusMessage(errorMessage);
                Logger.Error("手动触发新闻向量化任务失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 获取定时任务状态
        /// </summary>
        /// <param name="includeDetailedStats">是否包含详细统计信息</param>
        /// <returns>任务状态</returns>
        public object GetStatus(bool includeDetailedStats = false)
        {
            try
            {
                // 获取基本状态信息
                var status = new Dictionary<string, object>
                {
                    { "IsRunning", _isRunning },
                    { "CurrentStatus", _isRunning ? "Running" : "Stopped" },
                    { "ScanIntervalMinutes", SCAN_INTERVAL_MINUTES },
                    { "BatchSize", BATCH_SIZE },
                    { "MaxRetryCount", MAX_RETRY_COUNT },
                    { "LastRunTime", _lastRunTime },
                    { "NextScheduledRunTime", _nextScheduledRunTime },
                    { "TimeSinceLastRun", _lastRunTime != DateTime.MinValue ? (DateTime.Now - _lastRunTime).TotalMinutes : 0 },
                    { "TimeUntilNextRun", _nextScheduledRunTime != DateTime.MinValue ? (_nextScheduledRunTime - DateTime.Now).TotalMinutes : 0 }
                };

                // 添加上次运行结果
                if (_lastRunResult != null)
                {
                    var lastRunResult = new Dictionary<string, object>
                    {
                        { "StartTime", _lastRunResult.StartTime },
                        { "EndTime", _lastRunResult.EndTime },
                        { "TotalProcessed", _lastRunResult.TotalProcessed },
                        { "SuccessCount", _lastRunResult.SuccessCount },
                        { "FailedCount", _lastRunResult.FailedCount },
                        { "Duration", _lastRunResult.Duration.TotalSeconds },
                    };

                    // 如果需要包含详细统计信息
                    if (includeDetailedStats)
                    {
                        // 添加错误消息
                        lastRunResult.Add("ErrorMessages", _lastRunResult.ErrorMessages);

                        // 获取向量化统计信息
                        try
                        {
                            var vectorStats = GetVectorizationStats();
                            if (vectorStats != null)
                            {
                                lastRunResult.Add("VectorizationStats", vectorStats);
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Error("获取向量化统计信息失败", ex);
                            lastRunResult.Add("VectorizationStatsError", ex.Message);
                        }
                    }

                    status.Add("LastRunResult", lastRunResult);
                }

                // 添加最近的状态消息
                status.Add("RecentStatusMessages", _statusMessages.ToArray());

                // 添加系统资源信息
                if (includeDetailedStats)
                {
                    try
                    {
                        var systemInfo = new Dictionary<string, object>
                        {
                            { "ProcessMemoryUsageMB", System.Diagnostics.Process.GetCurrentProcess().WorkingSet64 / (1024 * 1024) },
                            { "ProcessorCount", Environment.ProcessorCount },
                            { "Is64BitProcess", Environment.Is64BitProcess },
                            { "MachineName", Environment.MachineName }
                        };
                        status.Add("SystemInfo", systemInfo);
                    }
                    catch (Exception ex)
                    {
                        Logger.Error("获取系统资源信息失败", ex);
                        status.Add("SystemInfoError", ex.Message);
                    }
                }

                return status;
            }
            catch (Exception ex)
            {
                Logger.Error("获取定时任务状态失败", ex);
                return new
                {
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                };
            }
        }

        /// <summary>
        /// 获取向量化统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        private object GetVectorizationStats()
        {
            try
            {
                // 使用NewsBLL查询向量化状态统计
                var newsBLL = NewsBLL;

                // 获取总新闻数
                int totalNews = newsBLL.GetCount("");

                // 获取已向量化新闻数
                int vectorizedNews = newsBLL.GetCount("VectorStatus = 1");

                // 获取向量化失败新闻数
                int failedNews = newsBLL.GetCount("VectorStatus = 2");

                // 获取待向量化新闻数
                int pendingNews = newsBLL.GetCount("VectorStatus = 0 OR VectorStatus IS NULL");

                // 计算向量化覆盖率
                double coverageRate = totalNews > 0 ? (double)vectorizedNews / totalNews * 100 : 0;

                // 获取最近向量化的新闻
                var recentVectorized = newsBLL.GetList("VectorStatus = 1", 5, 1, "Id, Title, VectorUpdateTime", "VectorUpdateTime DESC");
                var recentVectorizedList = recentVectorized.Select(n => new
                {
                    Id = n.Id,
                    Title = n.Title,
                    VectorUpdateTime = n.VectorUpdateTime
                }).ToList();

                return new
                {
                    TotalNews = totalNews,
                    VectorizedNews = vectorizedNews,
                    FailedNews = failedNews,
                    PendingNews = pendingNews,
                    CoverageRate = coverageRate,
                    RecentVectorized = recentVectorizedList
                };
            }
            catch (Exception ex)
            {
                Logger.Error("获取向量化统计信息失败", ex);
                return null;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Stop();
        }


        /// <summary>
        /// 优先处理指定的新闻
        /// 用于紧急处理需要立即向量化的新闻
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <returns>处理结果</returns>
        public async Task<bool> PrioritizeNewsAsync(int newsId)
        {
            try
            {
                Logger.Info($"优先处理新闻，新闻ID: {newsId}");
                AddStatusMessage($"开始优先处理新闻，新闻ID: {newsId}");

                // 获取新闻对象
                var news = NewsBLL.GetModel(newsId);
                if (news == null)
                {
                    var errorMessage = $"优先处理新闻失败：新闻不存在，ID: {newsId}";
                    Logger.Error(errorMessage);
                    AddStatusMessage(errorMessage);
                    return false;
                }

                // 执行向量化处理
                bool success = await _vectorizationService.VectorizeSingleNewsAsync(news, NewsBLL);

                if (success)
                {
                    var successMessage = $"优先处理新闻成功，新闻ID: {newsId}，标题: {news.Title}";
                    Logger.Info(successMessage);
                    AddStatusMessage(successMessage);
                }
                else
                {
                    var failureMessage = $"优先处理新闻失败，新闻ID: {newsId}，标题: {news.Title}";
                    Logger.Error(failureMessage);
                    AddStatusMessage(failureMessage);
                }

                return success;
            }
            catch (Exception ex)
            {
                var errorMessage = $"优先处理新闻异常，新闻ID: {newsId}，错误: {ex.Message}";
                Logger.Error(errorMessage, ex);
                AddStatusMessage(errorMessage);
                return false;
            }
        }

        /// <summary>
        /// 批量优先处理指定的新闻
        /// </summary>
        /// <param name="newsIds">新闻ID列表</param>
        /// <param name="parallelProcessing">是否并行处理</param>
        /// <param name="maxDegreeOfParallelism">最大并行度</param>
        /// <returns>处理结果</returns>
        public async Task<VectorizationResult> PrioritizeNewsListAsync(
            List<int> newsIds,
            bool parallelProcessing = true,
            int maxDegreeOfParallelism = 3)
        {
            try
            {
                if (newsIds == null || newsIds.Count == 0)
                {
                    return new VectorizationResult
                    {
                        StartTime = DateTime.Now,
                        EndTime = DateTime.Now,
                        TotalProcessed = 0,
                        SuccessCount = 0,
                        FailedCount = 0
                    };
                }

                Logger.Info($"批量优先处理新闻，数量: {newsIds.Count}，并行处理: {parallelProcessing}，最大并行度: {maxDegreeOfParallelism}");
                AddStatusMessage($"开始批量优先处理新闻，数量: {newsIds.Count}");

                // 获取新闻列表
                var newsList = new List<News>();
                foreach (var newsId in newsIds)
                {
                    var news = NewsBLL.GetModel(newsId);
                    if (news != null)
                    {
                        newsList.Add(news);
                    }
                }

                if (newsList.Count == 0)
                {
                    AddStatusMessage("未找到指定的新闻");
                    return new VectorizationResult
                    {
                        StartTime = DateTime.Now,
                        EndTime = DateTime.Now,
                        TotalProcessed = 0,
                        SuccessCount = 0,
                        FailedCount = 0
                    };
                }

                // 处理新闻
                var result = new VectorizationResult { StartTime = DateTime.Now };

                // 使用并行或顺序处理
                if (parallelProcessing)
                {
                    // 使用信号量限制并发数
                    using (var semaphore = new SemaphoreSlim(maxDegreeOfParallelism))
                    {
                        var tasks = new List<Task<bool>>();

                        foreach (var news in newsList)
                        {
                            await semaphore.WaitAsync();

                            var task = Task.Run(async () =>
                            {
                                try
                                {
                                    return await _vectorizationService.VectorizeSingleNewsAsync(news, NewsBLL);
                                }
                                finally
                                {
                                    semaphore.Release();
                                }
                            });

                            tasks.Add(task);
                        }

                        // 等待所有任务完成
                        var results = await Task.WhenAll(tasks);

                        // 统计结果
                        result.TotalProcessed = newsList.Count;
                        result.SuccessCount = results.Count(r => r);
                        result.FailedCount = results.Count(r => !r);
                    }
                }
                else
                {
                    // 顺序处理
                    result.TotalProcessed = newsList.Count;
                    result.SuccessCount = 0;
                    result.FailedCount = 0;

                    foreach (var news in newsList)
                    {
                        bool success = await _vectorizationService.VectorizeSingleNewsAsync(news, NewsBLL);
                        if (success)
                            result.SuccessCount++;
                        else
                            result.FailedCount++;
                    }
                }

                result.EndTime = DateTime.Now;

                // 记录执行结果
                var statusMessage = $"批量优先处理新闻完成，总计: {result.TotalProcessed}，成功: {result.SuccessCount}，" +
                                   $"失败: {result.FailedCount}，耗时: {result.Duration.TotalSeconds:F2}秒";
                AddStatusMessage(statusMessage);
                Logger.Info(statusMessage);

                return result;
            }
            catch (Exception ex)
            {
                var errorMessage = $"批量优先处理新闻异常: {ex.Message}";
                Logger.Error(errorMessage, ex);
                AddStatusMessage(errorMessage);

                return new VectorizationResult
                {
                    StartTime = DateTime.Now,
                    EndTime = DateTime.Now,
                    TotalProcessed = 0,
                    SuccessCount = 0,
                    FailedCount = 0,
                    ErrorMessages = new List<string> { errorMessage }
                };
            }
        }
    }
}