{"TestConfiguration": {"UnitTests": {"Description": "单元测试配置 - 不依赖外部资源", "MockData": {"TestUserId": 1, "TestNewsId": 100, "TestUserName": "测试用户", "TestNewsTitle": "测试新闻", "TestVector": [0.1, 0.2, 0.3, 0.4, 0.5], "TestTags": ["人工智能", "机器学习", "科技"], "TestReadingHistory": [10, 20, 30, 40, 50]}, "Assertions": {"MaxResponseTimeMs": 1000, "MinSimilarityThreshold": 0.0, "MaxSimilarityThreshold": 1.0, "MaxCacheSize": 100}}, "IntegrationTests": {"Description": "集成测试配置 - 需要真实数据库和缓存", "Database": {"TestUserPrefix": "test_integration_", "TestNewsPrefix": "集成测试新闻_", "CleanupAfterTest": true, "MaxTestDataRetentionHours": 24}, "Cache": {"TestCachePrefix": "test_", "CacheExpirationMinutes": 60, "CleanupAfterTest": true}, "Performance": {"MaxResponseTimeMs": 5000, "ConcurrentRequestCount": 10, "LargeDataSetSize": 50, "MaxTotalTestTimeSeconds": 30}}, "TestData": {"SampleNews": [{"Title": "人工智能技术突破", "Content": "最新的人工智能技术在图像识别领域取得重大突破...", "Tag": "人工智能,机器学习,图像识别", "Classify": "科技新闻", "Source": "科技日报"}, {"Title": "投资市场分析报告", "Content": "本季度投资市场呈现稳定增长态势，科技股表现突出...", "Tag": "投资,市场分析,科技股", "Classify": "财经新闻", "Source": "财经周刊"}, {"Title": "新能源汽车发展趋势", "Content": "新能源汽车行业持续快速发展，电池技术不断改进...", "Tag": "新能源,汽车,电池技术", "Classify": "产业新闻", "Source": "产业观察"}], "SampleUsers": [{"RealName": "张三", "Interests": ["人工智能", "投资", "科技"], "ReadingBehavior": "高频阅读科技类新闻"}, {"RealName": "李四", "Interests": ["投资", "市场分析", "财经"], "ReadingBehavior": "关注投资和财经新闻"}]}, "ExpectedBehaviors": {"RecordUserReadNewsAsync": {"ValidInput": "应该返回true并更新缓存", "InvalidUserId": "应该返回false", "InvalidNewsId": "应该返回false", "NewsNotFound": "应该返回false", "DuplicateReading": "不应该重复记录同一新闻", "CacheLimit": "阅读历史应该限制在100条以内"}, "GetUserInterestVector": {"CacheHit": "应该从缓存返回向量", "CacheMiss": "应该通过UserProfileBLL获取向量", "FallbackToTags": "如果没有用户向量，应该基于标签关联生成", "FallbackToHistory": "如果没有标签，应该基于阅读历史生成", "AllFallbacksFail": "所有方法都失败时应该返回null"}, "GetRecommendationsByReadingHistory": {"ValidHistory": "应该基于阅读历史返回推荐", "NoHistory": "没有阅读历史时应该返回空列表", "ExcludeReadNews": "推荐结果不应该包含已读新闻", "SimilarityOrdering": "推荐结果应该按相似度降序排列", "LimitRespected": "返回结果数量不应该超过限制"}}, "TestCategories": {"Unit": ["RecordUserReadNewsAsync_ValidParameters_ReturnsTrue", "RecordUserReadNewsAsync_InvalidUserId_ReturnsFalse", "GetUserInterestVector_ValidUserId_ReturnsVectorFromCache", "GetRecommendationsByReadingHistory_ValidParameters_ReturnsRecommendations"], "Integration": ["RecordUserReadNewsAsync_RealDatabase_Success", "GetUserInterestVector_RealDatabase_ReturnsVector", "FullWorkflow_RecordAndRecommend_WorksEndToEnd"], "Performance": ["RecordUserReadNewsAsync_HighVolume_PerformsWell", "LargeDataSet_Performance_AcceptableResponseTime"], "ErrorHandling": ["RecordUserReadNewsAsync_DatabaseException_ReturnsFalse", "GetUserInterestVector_AllFallbacksFail_ReturnsNull"]}}, "Recommendations": {"TestExecution": ["先运行单元测试确保基本功能正确", "在测试环境运行集成测试验证端到端流程", "定期运行性能测试监控系统性能", "在CI/CD流水线中集成自动化测试"], "TestMaintenance": ["定期更新测试数据以反映真实场景", "监控测试执行时间，及时优化慢测试", "保持测试代码的可读性和可维护性", "及时清理过期的测试数据和缓存"], "TroubleShooting": ["如果集成测试失败，首先检查数据库连接", "缓存相关测试失败时检查Redis服务状态", "性能测试失败时检查系统资源使用情况", "随机失败的测试可能存在并发问题"]}}