﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.Linq;

namespace Banyan.Apps
{
    public class ArticleBLL : BaseDAL<Article>
    {
        private readonly AjaxResult ajaxResult = null;

        public ArticleBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        public override object Add(Article model)
        {
            ClearCache(model);
            return base.Add(model);
        }

        public override bool Update(Article model)
        {
            ClearCache(model);
            return base.Update(model);
        }

        public override bool Update(Article model, string fldList)
        {
            ClearCache(model);
            return base.Update(model, fldList);
        }

        public bool ClearCache(Article model)
        {
            try
            {
                RedisUtil.Remove(string.Format(RedisKey.article_model, model.Id));
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取文章缓存
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Article GetModelByCache(int id)
        {
            string cacheKey = string.Format(RedisKey.article_model, id);
            Article model = RedisUtil.Get<Article>(cacheKey);
            if (model == null)
            {
                model = base.GetModel(id);
                RedisUtil.Set<Article>(cacheKey, model, TimeSpan.FromMinutes(10));
            }
            return model;
        }

        /// <summary>
        /// 分页查询记录
        /// </summary>
        /// <param name="paramValues">页面请求参数</param>
        /// <returns></returns>
        public AjaxResult GetPageList(NameValueCollection paramValues)
        {
            Member user = new MemberBLL().GetLogOnUser();

            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            string my = WebHelper.GetValue("my", "", paramValues);
            string strWhere = $"Status<>{(int)ArticleStatus.delete} ";

            if (!(user.Levels == (int)MemberLevels.Administrator || user.Levels == (int)MemberLevels.SuperUser))
            {
                strWhere += $"AND (EditorName='{user.RealName}' ";
                if (string.IsNullOrEmpty(my) || my != "true")
                {
                    if (user.Levels != (int)MemberLevels.LimitedUser)
                    {
                        string roleIds = string.IsNullOrEmpty(user.Groups) ? "0" : $"{user.Groups}";
                        if (!string.IsNullOrEmpty(roleIds))
                            strWhere += $" OR ToRoleId in({roleIds}) ";
                    }
                }
                else
                {
                    string Collects = new CollectDetailBLL().GetCollects(user.Id, 0);
                    //Logger.Info(Collects);
                    if (!string.IsNullOrEmpty(Collects))
                        strWhere += $" OR Id in({Collects}) ";
                }
                strWhere += ") ";
            }

            string title = WebHelper.GetValue("title", string.Empty, paramValues);
            if (!string.IsNullOrWhiteSpace(title))
            {
                strWhere += $"AND (Title like '%{title}%' OR EditorName like '%{title}%' OR Summary like '%{title}%') ";
            }

            string startDate = WebHelper.GetValue("startdate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(startDate))
            {
                strWhere += $"AND AddTime>='{startDate}' ";
            }

            string endDate = WebHelper.GetValue("enddate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(endDate))
            {
                strWhere += $"AND AddTime<'{Convert.ToDateTime(endDate).AddDays(1).ToString("yyyy-MM-dd")}' ";
            }

            int creatorId = WebHelper.GetValueInt("Creator", 0, paramValues);
            if (creatorId > 0)
            {
                strWhere += $"AND Creator={creatorId} ";
            }

            var articleList = GetList(strWhere, pageSize, pageIndex, "*", "Sort DESC, PubTime DESC, PriseCount DESC, CommentCount DESC, ViewCount DESC");
            if (articleList != null && articleList.Count() > 0)
            {
                var roleList = new RoleBLL().GetList(false);
                if (roleList != null && roleList.Count() > 0)
                {
                    foreach (var item in articleList)
                    {
                        item.RoleName = roleList.Where(x => x.Id == item.ToRoleId).Select(x => x.RoleName).FirstOrDefault()?.ToString();
                        item.IsOperate = user.Levels == (byte)MemberLevels.Administrator || item.EditorName == user.RealName;
                    }
                }
            }
            updateLog("Web, Get Article List", "view", strWhere, user);
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = articleList;
            ajaxResult.count = GetCount(strWhere);
            return ajaxResult;
        }

        public byte[] GetListBytes(NameValueCollection paramValues)
        {
            Member user = new MemberBLL().GetLogOnUser();

            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            string my = WebHelper.GetValue("my", "", paramValues);
            string strWhere = $"Status<>{(int)ArticleStatus.delete} ";

            if (!(user.Levels == (int)MemberLevels.Administrator || user.Levels == (int)MemberLevels.SuperUser))
            {
                strWhere += $"AND (Creator={user.Id} ";
                if (string.IsNullOrEmpty(my) || my != "true")
                {
                    if (user.Levels != (int)MemberLevels.LimitedUser)
                    {
                        string roleIds = string.IsNullOrEmpty(user.Groups) ? "0" : $"{user.Groups}";
                        if (!string.IsNullOrEmpty(roleIds))
                            strWhere += $" OR ToRoleId in({roleIds}) ";
                    }
                }
                else
                {
                    string Collects = new CollectDetailBLL().GetCollects(user.Id, 0);
                    //Logger.Info(Collects);
                    if (!string.IsNullOrEmpty(Collects))
                        strWhere += $" OR Id in({Collects}) ";
                }
                strWhere += ") ";
            }
            string title = WebHelper.GetValue("title", string.Empty, paramValues);
            if (!string.IsNullOrWhiteSpace(title))
            {
                strWhere += $"AND (Title like '%{title}%' OR EditorName like '%{title}%' OR Summary like '%{title}%') ";
            }

            string startDate = WebHelper.GetValue("startdate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(startDate))
            {
                strWhere += $"AND AddTime>='{startDate}' ";
            }

            string endDate = WebHelper.GetValue("enddate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(endDate))
            {
                strWhere += $"AND AddTime<'{Convert.ToDateTime(endDate).AddDays(1).ToString("yyyy-MM-dd")}' ";
            }

            int creatorId = WebHelper.GetValueInt("Creator", 0, paramValues);
            if (creatorId > 0)
            {
                strWhere += $"AND Creator={creatorId} ";
            }

            var articleList = GetList(strWhere, int.MaxValue, pageIndex, "*", "Sort DESC, PubTime DESC, PriseCount DESC, CommentCount DESC, ViewCount DESC");
            if (articleList != null && articleList.Count() > 0)
            {
                var roleList = new RoleBLL().GetList(false);
                if (roleList != null && roleList.Count() > 0)
                {
                    foreach (var item in articleList)
                    {
                        item.PubTimeStr = item.PubTime.ToString("yyyy-MM-dd");
                        item.RoleName = roleList.Where(x => x.Id == item.ToRoleId).Select(x => x.RoleName).FirstOrDefault()?.ToString();
                    }
                }
            }
            updateLog("Article", "export to excel", strWhere, user);
            string[] columnsName = { "所属项目组", "访谈对象", "访谈对象背景", "Key Takeaway", "日期", "编辑人" };
            string[] columns = { "RoleName", "Title", "Summary", "Content", "PubTimeStr", "EditorName" };
            return ExcelHelper.ExportExcel<Article>(articleList, columnsName.ToList(), "", false, columns);
        }

        /// <summary>
        /// 更新字段
        /// </summary>
        /// <param name="field"></param>
        /// <returns></returns>
        public AjaxResult FieldSet(NameValueCollection paramValues, Member user)
        {
            var id = WebHelper.GetValueInt("id", 0, paramValues);
            var field = WebHelper.GetValue("field", string.Empty, paramValues);
            var state = WebHelper.GetValueInt("state", 0, paramValues);
            var values = WebHelper.GetValue("values", string.Empty, paramValues);

            if (user == null)
            {
                ajaxResult.code = (int)ResultCode.noright;
                ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.noright);
                return ajaxResult;
            }

            if (id <= 0 || string.IsNullOrWhiteSpace(field))
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                return ajaxResult;
            }

            string oper = field;
            Article model = new Article() { Id = id };
            switch (field)
            {
                case "issue": //超级管理员才有权限上下架文章
                    field = "Status";
                    model.Status = (byte)(state == (int)ArticleStatus.wait ? (int)ArticleStatus.normal : (int)ArticleStatus.wait);
                    break;
                case "delete":
                    field = "Status";
                    updateLog("Web, Delete Article", "delete", "ID:" + id, user);
                    model.Status = (byte)ArticleStatus.delete;
                    break;
                //case "recomend":
                //    field = "IsRecommend";
                //    model.IsRecommend = state > 0 ? false : true;
                //    break;
                //case "top": //超级管理员才允许置顶
                //    field = "IsStick";
                //    model.IsStick = state > 0 ? false : true;
                //    break;
                //case "sort":
                //    field = "Sort";
                //    model.Sort = (byte)state;
                //    break;
                //case "role":
                //    field = "ToRoleIds";
                //    model.ToRoleIds = values;
                //    break;
                case "prise":
                    field = "PriseCount";
                    model = GetModelByCache(id);
                    if (model != null)
                        model.PriseCount += 1;
                    break;
                default:
                    break;
            }

            var result = false;

            //置顶或上下架文章权限限制
            //if (oper.Equals("issue"))
            //{
            //    ajaxResult.code = (int)ResultCode.noright;
            //    ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.noright);
            //    return ajaxResult;
            //}

            // 超过推送时间手动推送
            //var tmpModel = new ArticleBLL().GetModelByCache(model.Id);
            //if (oper.Equals("issue") && model.Status == (int)ArticleStatus.normal && tmpModel.PubTime < DateTime.Now)
            //{
            //    Task<bool> pushResult = new TempletBLL().ArticlePushRemindAsync(model.Id);
            //}

            result = string.IsNullOrEmpty(field) ? false : Update(model, field);
            ajaxResult.code = result ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }

        /// <summary>
        /// 添加或保存分类数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public AjaxResult Save(Article model, bool isCheck = true)
        {
            AjaxResult ajaxResult = new AjaxResult();

            var user = new MemberBLL().GetLogOnUser(model.Creator);
            if (user == null)
            {
                ajaxResult.code = (int)ResultCode.unlogin;
                ajaxResult.msg = "登录态已失效，请刷新页面重试";
                return ajaxResult;
            }

            model.Creator = user.Id;
            model.EditorName = user.RealName;
            //model.Status = (int)ArticleStatus.normal;

            //参数消毒
            if (!string.IsNullOrEmpty(model.Content?.Trim()))
            {
                model.Content = WebHelper.Formatstr(model.Content);
            }

            if (model.Id > 0)
            {
                ajaxResult.data = Update(model, "EditorName,Title,Summary,Content,CoverUrl,PdfUrl,ColumnId,ToRoleId,IsPrivate,Sort,LastTime,PubTime,Status");
                ajaxResult.code = (bool)ajaxResult.data ? (int)ResultCode.success : (int)ResultCode.exception;
                updateLog("Update Article", "update", model.ToJson(), user);
            }
            else
            {
                string sqlStr = $"Status<>{(int)ArticleStatus.delete} "; //获取用户可以观看的项目
                sqlStr += $"AND Creator={user.Id} AND Title='{model.Title}' AND PubTime='{model.PubTime}' ";
                if (GetCount(sqlStr) == 0)
                {
                    model.Id = Convert.ToInt32(Add(model));
                    ajaxResult.code = model.Id > 0 ? (int)ResultCode.success : (int)ResultCode.exception;
                    updateLog("New Article", "Save", model.ToJson(), user);
                }
                else
                {
                    ajaxResult.code = (int)ResultCode.exception;
                    ajaxResult.msg = "该项目已添加，勿重复添加！";
                }
            }
            //var role = new RoleBLL().GetModelByCache(model.ToRoleId);
            //ajaxResult.code = (bool)ajaxResult.data ? (int)ResultCode.success : (int)ResultCode.exception;
            ajaxResult.data = new
            {
                model.Id,
                //model.Creator,
                //model.Title,
                //model.Summary,
                //model.PdfUrl,
                //model.CoverUrl,
                //model.Content,
                //model.EditorName,
                //model.CollectCount,
                //model.CommentCount,
                //model.PriseCount,
                //AddTime = model.PubTime.ToString("yyyy-MM-dd"),
                //RoleName = (role == null ? string.Empty : role.RoleName),
                //IsCollect = new CollectDetailBLL().IsCollect(user.Id, model.Id),
                //IsPraise = new PraiseDetailBLL().IsCollect(user.Id, model.Id),
            };
            return ajaxResult;
        }

        /// <summary>
        /// 判断分类角色判断
        /// </summary>
        /// <param name="classId"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        //public AjaxResult ClassRoleJudge(int classId, int roleId)
        //{
        //    var classBll = new ClassifyBLL();
        //    if (classId <= 0)
        //    {
        //        return null;
        //    }
        //    var classModel = classBll.GetModelByCache(classId);
        //    if (classModel == null)
        //    {
        //        ajaxResult.code = (int)ResultCode.success;
        //        ajaxResult.data = null;
        //        return ajaxResult;
        //    }
        //    if (string.IsNullOrEmpty(classModel.ToRoleIds) || classModel.ToRoleIds.Split(',').Contains(roleId.ToString()))
        //    {
        //        return null;
        //    }
        //    ajaxResult.code = (int)ResultCode.noright;
        //    ajaxResult.data = null;
        //    return ajaxResult;
        //}

        /// <summary>
        /// 文章列表
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public AjaxResult GetArticles(NameValueCollection paramValues)
        {
            #region 用户信息
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var userModel = new MemberBLL().GetModelByCache(uid);
            if (userModel == null || userModel.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = userModel == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = userModel == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }
            #endregion

            #region 交互基本参数
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            string getType = WebHelper.GetValue("getType", string.Empty, paramValues);
            string keyWords = WebHelper.GetValue("keywords", string.Empty, paramValues);

            var articleList = new List<Article>();
            string sqlStr = $"Status<>{(int)ArticleStatus.delete} "; //获取用户可以观看的文章

            if (!(userModel.Levels == (int)MemberLevels.Administrator || userModel.Levels == (int)MemberLevels.SuperUser))
            {
                sqlStr += $"AND (Creator={userModel.Id} ";
                string roleIds = string.IsNullOrEmpty(userModel.Groups) ? "0" : $"{userModel.Groups}";
                if (!string.IsNullOrEmpty(roleIds) && userModel.Levels != (int)MemberLevels.LimitedUser)
                    sqlStr += $" OR ToRoleId in({roleIds})) ";
                else
                    sqlStr += ") ";
            }
            #endregion

            #region 约束条件

            if (!string.IsNullOrEmpty(keyWords))
            {
                sqlStr += $"AND Title like '%{keyWords}%' ";
            }

            int roleId = WebHelper.GetValueInt("roleid", 0, paramValues);
            if (roleId > 0)
            {
                sqlStr += $"AND ToRoleId={roleId} ";
            }
            //else if (!(userModel.Levels == (int)MemberLevels.Administrator || userModel.Levels == (int)MemberLevels.SuperUser))
            //{
            //    string roleIds = string.IsNullOrEmpty(userModel.Groups) ? "0" : $"{userModel.Groups}";
            //    sqlStr += $"AND ToRoleId in({roleIds}) ";
            //}

            int userId = WebHelper.GetValueInt("userid", 0, paramValues);
            if (userId > 0)
            {
                sqlStr += $"AND Creator={userId} ";
            }

            string startDate = WebHelper.GetValue("startdate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(startDate))
            {
                sqlStr += $"AND AddTime>='{startDate}' ";
            }

            string endDate = WebHelper.GetValue("enddate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(endDate))
            {
                sqlStr += $"AND AddTime<'{Convert.ToDateTime(endDate).AddDays(1).ToString("yyyy-MM-dd")}' ";
            }

            if (getType.Trim().Equals("carousel")) //置顶文章
            {
                pageSize = 1000; //获取全部推荐文章
                sqlStr += $"AND IsStick=1 ";
            }
            else if (getType.Trim().Equals("search")) //搜索
            {
                if (!string.IsNullOrEmpty(keyWords))
                {
                    articleList = GetList(sqlStr, 1000, 1, "Id,Title");
                    if (articleList != null && articleList.Count() > 0)
                    {
                        ajaxResult.data = articleList.Select(item =>
                        {
                            return new { item.Id, item.Title };
                        });
                    }
                    ajaxResult.code = (int)ResultCode.success;
                    return ajaxResult;
                }
                else
                {
                    ajaxResult.code = (int)ResultCode.success;
                    ajaxResult.data = null;
                    return ajaxResult;
                }
            }
            #endregion

            //默认按排序值、发布时间、点赞数、评论数、浏览数排序
            articleList = GetList(sqlStr, pageSize, pageIndex, "*", "Sort DESC, PubTime DESC, PriseCount DESC, CommentCount DESC, ViewCount DESC");

            //文章内容格式处理
            if (articleList != null && articleList.Count() > 0)
            {
                var roleList = new RoleBLL().GetList(false);
                var priseList = new PraiseDetailBLL().GetList($"UserId={userModel.Id} AND Mode = {(int)PraiseModeEnum.Interview} AND ArticleId IN({string.Join(",", articleList.Select(x => x.Id).ToArray())})");
                ajaxResult.data = articleList.Select(item =>
                {
                    return new { item.Id, item.Title, item.EditorName, item.Summary, item.CoverUrl, item.PriseCount, AddTime = item.PubTime.ToString("yyyy-MM-dd"), IsPrise = priseList.Where(x => x.ArticleId == item.Id).Count() > 0, RoleName = roleList.Where(x => x.Id == item.ToRoleId).Select(x => x.RoleName).FirstOrDefault()?.ToString() };
                });
            }
            else
            {
                ajaxResult.data = articleList;
            }
            updateLog("MiniApp, Get Article List", "view", sqlStr, userModel);
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;
        }

        /// <summary>
        /// 更新文章浏览量
        /// </summary>
        /// <param name="model"></param>
        public void UpdateViews(Article model)
        {
            var cahceKey = string.Format(RedisKey.article_views, model.Id);
            var viewsCache = RedisUtil.Get<string>(cahceKey);
            if (!string.IsNullOrEmpty(viewsCache))
            {
                var saveTime = Convert.ToDateTime(viewsCache.Split(',')[0]);
                var viewCount = Convert.ToInt32(viewsCache.Split(',')[1]);
                var timeSpan = DateTime.Now - saveTime;

                var upInterval = Convert.ToInt32(ConfigurationManager.AppSettings["ViewCountUpdateInterval"] ?? "30");
                if (timeSpan.TotalSeconds > upInterval)
                {
                    model.ViewCount += viewCount + 1;
                    var result = Update(model, "ViewCount") && RedisUtil.Remove(cahceKey);
                }
                else
                {
                    RedisUtil.Set<string>(cahceKey, $"{viewsCache.Split(',')[0]},{viewCount + 1}");
                }
            }
            else
            {
                RedisUtil.Set<string>(cahceKey, $"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")},1");
            }
        }

        /// <summary>
        /// 获取文章数据
        /// </summary>
        /// <returns></returns>
        public AjaxResult GetArticle(NameValueCollection paramValues)
        {
            int articleId = WebHelper.GetValueInt("aid", 0, paramValues);
            int userId = WebHelper.GetValueInt("uid", 0, paramValues);

            if (articleId <= 0 || userId <= 0)
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                ajaxResult.msg = "参数不合法！";
                return ajaxResult;
            }

            var userModel = new MemberBLL().GetModelByCache(userId);
            if (userModel == null || userModel.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = userModel == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = userModel == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }

            var articleModel = new ArticleBLL().GetModelByCache(articleId);
            if (articleModel == null || articleModel.Status == (int)ArticleStatus.delete)
            {
                ajaxResult.code = (int)ResultCode.notdata;
                return ajaxResult;
            }
            if (articleModel.Creator != userModel.Id && !(userModel.Levels == (int)MemberLevels.Administrator || userModel.Levels == (int)MemberLevels.SuperUser) && !$",{userModel.Groups},".Contains($",{articleModel.ToRoleId},"))
            {
                ajaxResult.code = (int)ResultCode.noright;
                return ajaxResult;
            }
            // 添加记录
            updateLog("MiniApp, Get Article", "view", articleModel.ToJson(), userModel);
            // 更新浏览量
            UpdateViews(articleModel);

            //返回数据
            var role = new RoleBLL().GetModel(articleModel.ToRoleId);
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = new
            {
                articleModel.Id,
                articleModel.Creator,
                articleModel.Title,
                articleModel.Summary,
                articleModel.PdfUrl,
                articleModel.CoverUrl,
                articleModel.Content,
                articleModel.EditorName,
                articleModel.CollectCount,
                articleModel.CommentCount,
                articleModel.PriseCount,
                AddTime = articleModel.PubTime.ToString("yyyy-MM-dd"),
                RoleName = (role == null ? string.Empty : role.RoleName),
                IsCollect = new CollectDetailBLL().IsCollect(userId, articleId),
                IsPraise = new PraiseDetailBLL().IsCollect(userId, articleId),
            };

            return ajaxResult;
        }

        public AjaxResult GetArticleDetail(int id)
        {
            var articleModel = new ArticleBLL().GetModelByCache(id);
            if (articleModel == null || articleModel.Status == (int)ArticleStatus.delete)
            {
                ajaxResult.code = (int)ResultCode.notdata;
                return ajaxResult;
            }
            updateLog("Web, Get Article", "view", articleModel.ToJson(), new MemberBLL().GetLogOnUser(0));
            //浏览量更新
            UpdateViews(articleModel);

            //返回数据
            var role = new RoleBLL().GetModel(articleModel.ToRoleId);
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = new
            {
                articleModel.Id,
                articleModel.Creator,
                articleModel.Title,
                articleModel.Summary,
                articleModel.PdfUrl,
                articleModel.CoverUrl,
                articleModel.Content,
                articleModel.EditorName,
                articleModel.CollectCount,
                articleModel.CommentCount,
                articleModel.PriseCount,
                AddTime = articleModel.PubTime.ToString("yyyy-MM-dd"),
                RoleName = (role == null ? string.Empty : role.RoleName),
            };

            return ajaxResult;
        }

        /// <summary>
        /// 更新数量审计字段
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public bool UpCountField(int id, string type, int raiseCount = 1)
        {
            string field = string.Empty;
            var model = GetModel(id);
            if (model == null)
            {
                return false;
            }
            switch (type)
            {
                case "comment":
                    field = "CommentCount";
                    model.CommentCount += raiseCount;
                    break;
                default:
                    break;
            }

            if (string.IsNullOrEmpty(field))
            {
                return false;
            }
            return Update(model, field);
        }
        /// <summary>
        /// 操作日志更新
        /// </summary>
        /// <param name="page"></param>
        /// <param name="action"></param>
        /// <param name="description"></param>
        /// <param name="userName"></param>
        public void updateLog(string page, string action, string description, Member user)
        {
            SysLog log = new SysLog
            {
                Page = page,
                Action = action,
                Description = description,
                CreatedBy = user == null ? "" : user.RealName,
                Ip = Utility.WebHelper.GetIP(),
                CreatorId = user == null ? 0 : user.Id,
            };
            SysLogBLL logBLL = new SysLogBLL();
            logBLL.Add(log);
        }

        /// <summary>
        /// 索取当前及所有上级分类名称(该处仅获取2级)
        /// </summary>
        /// <param name="list"></param>
        /// <param name="cid"></param>
        /// <returns></returns>
        //public List<string> GetClassNames(List<Classify> list, int cid)
        //{
        //    if (cid <= 0)
        //    {
        //        return new List<string>();
        //    }

        //    var currClass = list.Where(x => x.Id == cid)?.FirstOrDefault();
        //    if (currClass != null && currClass.Id > 0)
        //    {
        //        return list.Where(x => $"{currClass.Id},{currClass.ParentId}".Contains(x.Id.ToString())).Select(x => x.Name).ToList();
        //    }

        //    return null;
        //}

        /// <summary>
        /// 显示Tag
        /// </summary>
        /// <param name="list"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        //public List<String> GetTagNames(List<Classify> list, Article model)
        //{
        //    List<string> tagNames = new List<string>();
        //    string colName = string.Empty;
        //    if (model.ColumnId == (int)ColumnId.cases)
        //    {
        //        colName = "案例";
        //    }
        //    else if (model.ColumnId == (int)ColumnId.weekly)
        //    {
        //        colName = "周报";
        //    }
        //    else if (model.ColumnId == (int)ColumnId.subject)
        //    {
        //        colName = "专题";
        //    }
        //    tagNames.Add(colName);

        //    var className = list.Where(x => x.Id == model.ClassId)?.FirstOrDefault()?.Name;
        //    if (!string.IsNullOrEmpty(className))
        //        tagNames.Add(className);
        //    return tagNames;
        //}
    }
}
