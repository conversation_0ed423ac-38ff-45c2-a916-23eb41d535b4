﻿
@{
    Layout = null;
    Banyan.Domain.Member member = ViewData["manager"] as Banyan.Domain.Member;
    var control = "return false";
    if (member.Levels == (byte)Banyan.Domain.MemberLevels.Administrator || member.Levels == (byte)Banyan.Domain.MemberLevels.SuperUser)
    {
        control = "";
    }
}

<!DOCTYPE html>

<html>
<head>
    <meta name="divport" content="width=device-width" />
    <title>预览</title>
    <link type="text/css" href="/content/js/plugins/layui/css/layui.css" rel="stylesheet" />
    <link href="~/content/css/preview.css?v=@(DateTime.Now.Ticks)" rel="stylesheet" />
    <style>
        .water-mark {
            font-size: 14px;
            color: #c2c2c2; /* 颜色会动态重写，根据配置 */
            position: fixed;
            padding: 0 15px;
            transform: translate(-50%, -50%);
            transform: rotate(-30deg);
            -ms-transform: rotate(-30deg); /* IE 9 */
            -moz-transform: rotate(-30deg); /* Firefox */
            -webkit-transform: rotate(-30deg); /* Safari 和 Chrome */
            -o-transform: rotate(-30deg);
            opacity: 0.3;
            overflow: hidden;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            pointer-events: none;
        }
    </style>
</head>
<body oncontextmenu="@control" oncopy="@control" onselectstart="@control">
    <div id="app" style="display:none;">
        <div class="page-bd_scroll page-bd_fixedbt" v-on:scroll="loadScroll">
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    创建人
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.EditorName}}</label>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    项目组
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.RoleName}}</label>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    访谈对象
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.Title}}</label>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    日期
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.AddTime}}</label>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    访谈对象背景
                </div>
                <div class="weui-flex__item">
                    <pre v-html="objData.Summary"></pre>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    Key Takeaway
                </div>
                <div class="weui-flex__item">
                    <pre v-html="objData.Content"></pre>
                </div>
            </div>
            <div class="weui-cell weui-cell_access box-reply_expert" v-for="item in dataList">
                <div class="weui-cell__hd">
                    <img class="box-avatar_mini" :src="item.UserAvatar" />
                </div>
                <div class="weui-cell__bd weui-cell_primary">
                    <div class="pd-l-5">
                        <div class="air-name">{{item.UserName}}</div>
                        <div class="air-time">{{item.AddTime}}</div>
                        <div class="box-quiz_item airbubble">{{item.Content}}</div>
                    </div>
                </div>
            </div>

            <div class="section-comment section-comment_fixed">
                <div class="weui-flex section-comment-box">
                    <div class="weui-flex__item">
                        <input type="text" class="section-comment_input" v-model="commentIpt" placeholder="发表评论..." />
                    </div>
                    <div class="section-comment__send" @@click="sendComment">发送</div>
                </div>
            </div>
        </div>
        <template v-for="item in waterArray">
            <p class="water-mark" :style="{left: item.wid + '%', top: item.hei + '%'}">@(member.RealName)</p>
        </template>
    </div>
    <script type="text/javascript" src="~/content/js/core/jquery.min.js"></script>
    <script type="text/javascript" src="~/content/js/plugins/layui/layui.js"></script>
    <script type="text/javascript" src="~/content/js/vue/vue.min.js"></script>
    <script type="text/javascript">
        window.addEventListener('keydown', function (e) {
            if (e.keyCode == 83 && (navigator.platform.match('Mac') ? e.metaKey : e.ctrlKey)) {
                e.preventDefault();
            }
        })
        var id = @(ViewBag.id), layer;

        layui.use(['layer'], function () {
            layer = layui.layer;
        });

        var app = new Vue({
            el: '#app',
            data: {
                query: {
                    id: id,
                    aid: id,
                    page: 1,
                },
                objData: {},
                dataList: [],
                loadding: false,
                loadState: -1,
                commentIpt: '',
                waterArray: [],
            },
            methods: {
                loadScroll: function (event) {
                    var that = this;

                    nodeScrollTotalHeigth = event.target.scrollHeight;
                    nodeScrollTop = event.target.scrollTop;
                    nodeHight = $(event.target).height();
                    (nodeScrollTotalHeigth - nodeScrollTop - nodeHight) < 25 && that.loadMore();
                },
                loadMore: function () {
                    var that = this;

                    if (!that.loadding) {
                        that.loadding = true;
                        $.post("/adminapi/getcomments", that.query, function (res) {
                            that.loadding = false;

                            if (res.code != 0 || !res.data || res.data.length == 0) {
                                if (that.query.page == 1) {
                                    that.dataList = [];
                                }
                                that.loadding = true;
                            }
                            else {
                                if (that.query.page == 1) {
                                    that.dataList = res.data;
                                }
                                else {
                                    that.dataList = that.dataList.concat(res.data);
                                }
                                that.query.page++;
                            }
                        });
                    }
                },
                sendComment() {
                    var that = this;
                    if (that.loadState > -1) {
                        return;
                    }
                    if ($.trim(that.commentIpt) == '') {
                        layer.msg('请输入评论内容');
                        return;
                    }
                    that.loadState = 0;
                    var pdata = {
                        content: that.commentIpt,
                        aid: that.query.aid,
                        pid: that.query.pid,
                    };
                    $.post('/adminapi/usercomment', pdata, function (data) {
                        that.loadState = -1;
                        if (data.code == 0) {
                            layer.msg('评论成功');
                            that.dataList.unshift(data.data);
                            that.commentIpt = '';
                        } else {
                            layer.msg(data.msg);
                        }
                    });
                },
                initWaterArr: function () {
                    var that = this;
                    var waterArr = [], wid = 5, hei = 5;
                    for (var i = 0; i < 5; i++) {
                        for (var j = 0; j < 5; j++) {
                            waterArr.push({
                                wid: wid + i * 19,
                                hei: hei + j * 16
                            });
                        }
                    }
                    that.waterArray = waterArr;
                }
            },

            created: function () {
                var that = this;

                $("#app").show();
                $.post('/adminapi/articledetail', that.query, function (data) {
                    console.log(data);
                    if (data.code == 0) {
                        that.objData = data.data;

                        that.loadMore();
                        that.initWaterArr();
                    }
                });
            }
        });
    </script>
</body>
</html>