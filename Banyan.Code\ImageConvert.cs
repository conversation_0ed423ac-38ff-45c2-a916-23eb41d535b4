﻿using Aspose.Words;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Threading;
using System.Text.RegularExpressions;
using System.Linq;

namespace Banyan.Code
{
    public class ImageConvert
    {
        public string pptRedisKey = string.Empty;

        public List<string> Word2ImageConverter(string originFilePath, string imageOutputDirPath, out string textContent)
        {
            return this.Word2ImageConverter(originFilePath, imageOutputDirPath, 0, 0, null, 0, out textContent);
        }

        public string extractText(string filename, int start, int end)
        {
            string extension = Path.GetExtension(filename).ToLower();
            
            if (extension == ".pdf")
            {
                return extractTextFromPdf(filename, start, end);
            }
            else if (extension == ".doc" || extension == ".docx")
            {
                return extractTextFromWord(filename, start, end);
            }
            else
            {
                throw new NotSupportedException($"File format {extension} is not supported for text extraction.");
            }
        }

        private string extractTextFromPdf(string filename, int start, int end)
        {
            Aspose.Pdf.Facades.PdfExtractor ext = new Aspose.Pdf.Facades.PdfExtractor();
            ext.BindPdf(filename);
            ext.StartPage = start;
            ext.EndPage = end;
            ext.ExtractText(System.Text.Encoding.UTF8);
            MemoryStream stream = new MemoryStream();
           
            ext.GetText(stream);
            stream.Position = 0;
            StreamReader reader = new StreamReader(stream, System.Text.Encoding.UTF8);
            string text = reader.ReadToEnd();
            text = RemoveExtraSpaces(text);
            Logger.Info(text);
            return text;
        }

        /// <summary>
        /// 智能清理PDF提取内容中不必要的空格
        /// </summary>
        /// <param name="text">原始文本</param>
        /// <returns>处理后的文本</returns>
        private string RemoveExtraSpaces(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return text;
            }

            // 分析文本的空格密度，决定使用哪种处理策略
            bool hasExcessiveSpaces = AnalyzeSpaceDensity(text);

            if (hasExcessiveSpaces)
            {
                // 文本有严重的空格问题，使用激进处理
                return AggressiveSpaceRemoval(text);
            }
            else
            {
                // 文本相对正常，使用温和处理
                return GentleSpaceRemoval(text);
            }
        }

        /// <summary>
        /// 分析文本的空格密度，判断是否需要激进处理
        /// </summary>
        /// <param name="text">输入文本</param>
        /// <returns>是否有过度空格问题</returns>
        private bool AnalyzeSpaceDensity(string text)
        {
            if (string.IsNullOrEmpty(text) || text.Length < 50)
                return false;

            // 计算空格密度指标
            int totalChars = text.Length;
            int spaceCount = text.Count(c => char.IsWhiteSpace(c));
            double spaceRatio = (double)spaceCount / totalChars;

            // 统计单字母+空格的模式数量
            int singleLetterSpacePatterns = Regex.Matches(text, @"\b[a-zA-Z]\s+[a-zA-Z]\b").Count;
            
            // 统计被拆分的数字（如 "1 2 3"）
            int splitNumberPatterns = Regex.Matches(text, @"\b\d\s+\d\b").Count;

            // 统计中文字符间的空格
            int chineseSpacePatterns = Regex.Matches(text, @"[\u4e00-\u9fff]\s+[\u4e00-\u9fff]").Count;

            // 判断条件（任一条件满足就认为有严重空格问题）
            return spaceRatio > 0.25 ||  // 空格占比超过25%
                   singleLetterSpacePatterns > 5 ||  // 单字母空格模式超过5个
                   splitNumberPatterns > 3 ||  // 拆分数字超过3个
                   chineseSpacePatterns > 10;  // 中文字符间空格超过10个
        }

        /// <summary>
        /// 温和的空格处理（用于正常文本）
        /// </summary>
        /// <param name="text">输入文本</param>
        /// <returns>处理后的文本</returns>
        private string GentleSpaceRemoval(string text)
        {
            // 只处理明显的问题，保留正常的文本格式

            // 1. 去除中文字符之间的空格
            text = Regex.Replace(text, @"(?<=[\u4e00-\u9fff])\s+(?=[\u4e00-\u9fff])", string.Empty);

            // 2. 去除中文标点符号前后的空格
            text = Regex.Replace(text, @"(?<=[\u4e00-\u9fff])\s+(?=[，。！？；：""''（）【】《》〈〉「」『』〔〕［］｛｝])", string.Empty);
            text = Regex.Replace(text, @"(?<=[，。！？；：""''（）【】《》〈〉「」『』〔〕［］｛｝])\s+(?=[\u4e00-\u9fff])", string.Empty);

            // 3. 基本的数字格式化
            text = Regex.Replace(text, @"(?<=\d)\s+(?=[.\-])", string.Empty);
            text = Regex.Replace(text, @"(?<=[.\-])\s+(?=\d)", string.Empty);

            // 4. 合并多余的连续空格
            text = Regex.Replace(text, @"\s{2,}", " ");

            return text.Trim();
        }

        /// <summary>
        /// 激进的空格处理（用于有严重空格问题的文本）
        /// </summary>
        /// <param name="text">输入文本</param>
        /// <returns>处理后的文本</returns>
        private string AggressiveSpaceRemoval(string text)
        {
            // 1. 去除所有中文字符之间的空格
            text = Regex.Replace(text, @"(?<=[\u4e00-\u9fff])\s+(?=[\u4e00-\u9fff])", string.Empty);

            // 2. 去除中文标点符号前后的空格
            text = Regex.Replace(text, @"(?<=[\u4e00-\u9fff])\s+(?=[，。！？；：""''（）【】《》〈〉「」『』〔〕［］｛｝])", string.Empty);
            text = Regex.Replace(text, @"(?<=[，。！？；：""''（）【】《》〈〉「」『』〔〕［］｛｝])\s+(?=[\u4e00-\u9fff])", string.Empty);

            // 3. 处理数字和小数点、连字符之间的空格
            text = Regex.Replace(text, @"(?<=\d)\s+(?=[.\-,])", string.Empty);
            text = Regex.Replace(text, @"(?<=[.\-,])\s+(?=\d)", string.Empty);
            text = Regex.Replace(text, @"(?<=\d)\s+(?=\d)", string.Empty);

            // 4. 激进处理被拆分的单词
            text = ProcessSplitWords(text);

            // 5. 处理标点符号前后的空格
            text = Regex.Replace(text, @"\s+(?=[.,;:!?()[\]{}""'])", string.Empty);
            text = Regex.Replace(text, @"(?<=[.,;:!?()[\]{}""'])\s+", " ");

            // 6. 去除中文与英文/数字之间的多余空格，但保留一个空格作为分隔
            text = Regex.Replace(text, @"(?<=[\u4e00-\u9fff])\s{2,}(?=[a-zA-Z0-9])", " ");
            text = Regex.Replace(text, @"(?<=[a-zA-Z0-9])\s{2,}(?=[\u4e00-\u9fff])", " ");

            // 7. 最后，合并剩余的多余连续空格为单个空格
            text = Regex.Replace(text, @"\s{2,}", " ");

            return text.Trim();
        }

        /// <summary>
        /// 处理被严重拆分的英文单词，如 "G e n e ralAtlantic" -> "GeneralAtlantic"
        /// 更智能的判断，避免误合并正常的单词
        /// </summary>
        /// <param name="text">输入文本</param>
        /// <returns>处理后的文本</returns>
        private string ProcessSplitWords(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            // 只处理特定的明显被错误拆分的模式：
            // 1. 连续5个或更多的单字母+空格（极不可能是正常单词）
            text = Regex.Replace(text, @"\b([a-zA-Z])\s+([a-zA-Z])\s+([a-zA-Z])\s+([a-zA-Z])\s+([a-zA-Z])(?:\s+[a-zA-Z])*\b", match =>
            {
                return Regex.Replace(match.Value, @"\s+", "");
            });

            // 2. 特定的已知问题模式（包含大小写混合的公司名等）
            // 如 "G e n e ral" (首字母大写+小写字母的模式)
            text = Regex.Replace(text, @"\b([A-Z])\s+([a-z])\s+([a-z])\s+([a-z])\s+([a-z])(?:\s+[a-z])*(?=Atlantic|Ventures|Partners|Capital|Tech|Corp|Inc|Ltd)", "$1$2$3$4$5");
            
            // 3. 处理数字+字母的组合 (如 "A 16 z")
            text = Regex.Replace(text, @"\b([A-Z])\s+(\d+)\s+([a-z])\b", "$1$2$3");

            // 4. 处理明显的技术术语或专有名词（包含大写字母的特殊模式）
            text = Regex.Replace(text, @"\b([A-Z][a-z]*)\s+([A-Z][a-z]*)\s+([A-Z][a-z]*)(?=Report|Model|Research|Technical)", "$1$2$3");

            return text;
        }

        private string extractTextFromWord(string filename, int start, int end)
        {
            try
            {
                Aspose.Words.Document doc = new Aspose.Words.Document(filename);
                
                if (doc == null)
                {
                    throw new Exception("Word文件无效或者Word文件被加密！");
                }

                // 如果指定了页面范围，尝试提取特定页面的文本
                if (start > 0 && end > 0 && start <= end)
                {
                    // Word页面提取比较复杂，这里先实现完整文档提取
                    // 如果需要精确的页面提取，可以使用页面分割功能
                    string fullText = doc.ToString(SaveFormat.Text);
                    fullText = RemoveExtraSpaces(fullText);
                    Logger.Info(fullText);
                    return fullText;
                }
                else
                {
                    // 提取完整文档文本
                    string text = doc.ToString(SaveFormat.Text);
                    text = RemoveExtraSpaces(text);
                    Logger.Info(text);
                    return text;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"extractTextFromWord：{ex.Message}", ex);
                throw;
            }
        }
        /// <summary>
        /// 将Word文档转换为图片的方法      
        /// </summary>
        /// <param name="wordInputPath">Word文件路径</param>
        /// <param name="imageOutputDirPath">图片输出路径，如果为空，默认值为Word所在路径</param>      
        /// <param name="startPageNum">从PDF文档的第几页开始转换，如果为0，默认值为1</param>
        /// <param name="endPageNum">从PDF文档的第几页开始停止转换，如果为0，默认值为Word总页数</param>
        /// <param name="imageFormat">设置所需图片格式，如果为null，默认格式为PNG</param>
        /// <param name="resolution">设置图片的像素，数字越大越清晰，如果为0，默认值为128，建议最大值不要超过1024</param>
        private List<string> Word2ImageConverter(string wordInputPath, string imageOutputDirPath, int startPageNum, int endPageNum, ImageFormat imageFormat, int resolution, out string textContent)
        {
            textContent = "";
            List<string> vlist = new List<string>();
            try
            {
                Aspose.Words.Document doc = new Aspose.Words.Document(wordInputPath);

                if (doc == null)
                {
                    throw new Exception("Word文件无效或者Word文件被加密！");
                }

                if (imageOutputDirPath.Trim().Length == 0)
                {
                    imageOutputDirPath = Path.GetDirectoryName(wordInputPath);
                }

                if (!Directory.Exists(imageOutputDirPath))
                {
                    Directory.CreateDirectory(imageOutputDirPath);
                }

                if (startPageNum <= 0)
                {
                    startPageNum = 1;
                }

                if (endPageNum > doc.PageCount || endPageNum <= 0)
                {
                    endPageNum = doc.PageCount;
                }

                if (startPageNum > endPageNum)
                {
                    int tempPageNum = startPageNum; startPageNum = endPageNum; endPageNum = startPageNum;
                }

                if (imageFormat == null)
                {
                    imageFormat = ImageFormat.Jpeg;
                }

                if (resolution <= 0)
                {
                    resolution = 156;
                }

                string imageName = Guid.NewGuid().ToString(); // Path.GetFileNameWithoutExtension(wordInputPath);

                Aspose.Words.Saving.ImageSaveOptions imageSaveOptions = new Aspose.Words.Saving.ImageSaveOptions(Aspose.Words.SaveFormat.Jpeg);
                imageSaveOptions.Resolution = resolution;

                for (int i = startPageNum; i <= endPageNum; i++)
                {
                    try
                    {
                        MemoryStream stream = new MemoryStream();
                        imageSaveOptions.PageIndex = i - 1;
                        string imgPath = Path.Combine(imageOutputDirPath, imageName) + "_" + i.ToString("000") + "." + imageFormat.ToString();
                        doc.Save(stream, imageSaveOptions);
                        Image img = Image.FromStream(stream);
                        Bitmap bm = new Bitmap(img);
                        bm.Save(imgPath, imageFormat);
                        img.Dispose();
                        stream.Dispose();
                        bm.Dispose();
                        vlist.Add(imgPath);

                    }
                    catch (Exception ex)
                    {
                        Logger.Error(ex.Message, ex);
                        //continue;
                    }
                    finally
                    {
                        //System.Threading.Thread.Sleep(20);
                    }
                }
                try
                {
                    textContent = doc.ToString(SaveFormat.Text);
                    textContent = RemoveExtraSpaces(textContent);
                    Logger.Info(textContent);
                }catch(Exception e)
                {
                    Logger.Error(e.Message, e);
                }
                return vlist;
            }
            catch (Exception ex)
            {
                Logger.Error($"Word2ImageConverter：{ex.Message}", ex);
                return new List<string>();
            }
         
        }
         
        //for (int i = startPageNum; i <= endPageNum; i++)
        //{
        //    task[i - startPageNum] = new Task<string>(() =>
        //    {
        //        try
        //        {
        //            MemoryStream stream = new MemoryStream();
        //            imageSaveOptions.PageIndex = i - 1;
        //            string imgPath = Path.Combine(imageOutputDirPath, imageName) + "_" + i.ToString("000") + "." + imageFormat.ToString();
        //            doc.Save(stream, imageSaveOptions);
        //            Image img = Image.FromStream(stream);
        //            Bitmap bm = new Bitmap(img);
        //            bm.Save(imgPath, imageFormat);
        //            img.Dispose();
        //            stream.Dispose();
        //            bm.Dispose();
        //            tmp[i - startPageNum] = imgPath;
        //            return imgPath;
        //        }
        //        catch (Exception ex)
        //        {
        //            Logger.Info(ex.Message);
        //            return "";
        //        }
        //        finally
        //        {
        //            //System.Threading.Thread.Sleep(20);
        //        }
        //    });
        //    //启动任务,并安排到当前任务队列线程中执行任务(System.Threading.Tasks.TaskScheduler)
        //    task[i - startPageNum].Start();
        //}
        //Task.WaitAll(task);
        //for (int i = startPageNum; i <= endPageNum; i++)
        //{
        //    vlist.Add(tmp[i - startPageNum]);
        //}
        //return vlist;
 

        public List<string> Pdf2ImageConverter(string originFilePath, string imageOutputDirPath, out string textContent)
        {
            return this.Pdf2ImageConverter(originFilePath, imageOutputDirPath, 0, 0, 0, out textContent);
        }

        /// <summary>
        /// 将pdf文档转换为图片的方法      
        /// </summary>
        /// <param name="originFilePath">pdf文件路径</param>
        /// <param name="imageOutputDirPath">图片输出路径，如果为空，默认值为pdf所在路径</param>       
        /// <param name="startPageNum">从PDF文档的第几页开始转换，如果为0，默认值为1</param>
        /// <param name="endPageNum">从PDF文档的第几页开始停止转换，如果为0，默认值为pdf总页数</param>       
        /// <param name="resolution">设置图片的像素，数字越大越清晰，如果为0，默认值为128，建议最大值不要超过1024</param>
        private List<string> Pdf2ImageConverter(string originFilePath, string imageOutputDirPath, int startPageNum, int endPageNum, int resolution, out string textContent)
        {
            textContent = "";
            List<string> vlist = new List<string>();
            try
            {
                Aspose.Pdf.Document doc = new Aspose.Pdf.Document(originFilePath);

                if (doc == null)
                {
                    return null;
                }

                if (imageOutputDirPath.Trim().Length == 0)
                {
                    imageOutputDirPath = Path.GetDirectoryName(originFilePath);
                }

                if (!Directory.Exists(imageOutputDirPath))
                {
                    Directory.CreateDirectory(imageOutputDirPath);
                }

                if (startPageNum <= 0)
                {
                    startPageNum = 1;
                }

                if (endPageNum > doc.Pages.Count || endPageNum <= 0)
                {
                    endPageNum = doc.Pages.Count;
                }

                if (startPageNum > endPageNum)
                {
                    int tempPageNum = startPageNum; startPageNum = endPageNum; endPageNum = startPageNum;
                }

                if (resolution <= 0)
                {
                    resolution = 156;
                }
                string imageNamePrefix = Guid.NewGuid().ToString(); // Path.GetFileNameWithoutExtension(originFilePath);
                Aspose.Pdf.Devices.Resolution reso = new Aspose.Pdf.Devices.Resolution(resolution);
                Aspose.Pdf.Devices.JpegDevice jpegDevice = new Aspose.Pdf.Devices.JpegDevice(reso, 100);

                for (int i = startPageNum; i <= endPageNum; i++)
                {
                    try
                    {
                        MemoryStream stream = new MemoryStream();
                        string imgPath = Path.Combine(imageOutputDirPath, imageNamePrefix) + "_" + i.ToString("000") + ".jpg";

                        jpegDevice.Process(doc.Pages[i], stream);

                        Image img = Image.FromStream(stream);
                        Bitmap bm = new Bitmap(img);
                        bm.Save(imgPath, ImageFormat.Jpeg);

                        // 保存一份旋转后的图片
                        bm.RotateFlip(RotateFlipType.Rotate90FlipNone);
                        string imgVtPath = Path.Combine(imageOutputDirPath, imageNamePrefix) + "_" + i.ToString("000") + "_hv.jpg";
                        bm.Save(imgVtPath, ImageFormat.Jpeg);

                        img.Dispose();
                        stream.Dispose();
                        bm.Dispose();
                        vlist.Add(imgPath);
                    }
                    catch (Exception ex)
                    {
                        Logger.Error(ex.Message, ex);
                        //continue;
                    }
                    finally
                    {
                        //System.Threading.Thread.Sleep(20);
                    }
                }
                try
                {
                    textContent = extractText(originFilePath, startPageNum, endPageNum);
                } catch(Exception e)
                {
                    Logger.Error(e.Message + " " + e.StackTrace, e);
                }
                return vlist;
            }
            catch (Exception ex)
            {
                Logger.Error($"Pdf2ImageConverter：{ex.Message}", ex);
                return new List<string>();
            }
 
        }

        public List<string> Ppt2ImageConverter(string originFilePath, string imageOutputDirPath, out string textContent)
        {
            return this.Ppt2ImageConverter(originFilePath, imageOutputDirPath, 0, 0, out textContent);
        }

        /// <summary>
        /// 将pdf文档转换为图片的方法      
        /// </summary>
        /// <param name="originFilePath">ppt文件路径</param>
        /// <param name="imageOutputDirPath">图片输出路径，如果为空，默认值为pdf所在路径</param>       
        /// <param name="startPageNum">从PDF文档的第几页开始转换，如果为0，默认值为1</param>
        /// <param name="endPageNum">从PDF文档的第几页开始停止转换，如果为0，默认值为pdf总页数</param>       
        /// <param name="resolution">设置图片的像素，数字越大越清晰，如果为0，默认值为128，建议最大值不要超过1024</param>
        private List<string> Ppt2ImageConverter(string originFilePath, string imageOutputDirPath, int startPageNum, int endPageNum, out string textContent)
        {
            List<string> vlist = new List<string>();
            try
            {
                Aspose.Slides.Presentation doc = new Aspose.Slides.Presentation(originFilePath);
                //if (doc == null)
                //{
                //    return null;
                //}

                if (imageOutputDirPath.Trim().Length == 0)
                {
                    imageOutputDirPath = Path.GetDirectoryName(originFilePath);
                }

                if (!Directory.Exists(imageOutputDirPath))
                {
                    Directory.CreateDirectory(imageOutputDirPath);
                }

                if (startPageNum <= 0)
                {
                    startPageNum = 1;
                }

                if (endPageNum > doc.Slides.Count || endPageNum <= 0)
                {
                    endPageNum = doc.Slides.Count;
                }

                if (startPageNum > endPageNum)
                {
                    int tempPageNum = startPageNum; startPageNum = endPageNum; endPageNum = startPageNum;
                }

                //先将ppt转换为pdf临时文件
                string tmpPdfPath = originFilePath + ".pdf";

                doc.Save(tmpPdfPath, Aspose.Slides.Export.SaveFormat.Pdf);

                //再将pdf转换为图片
                vlist = this.Pdf2ImageConverter(tmpPdfPath, imageOutputDirPath, out textContent);

                //删除pdf临时文件
                File.Delete(tmpPdfPath);
            }
            catch (Exception ex)
            {
                throw;
            }
            return vlist;
        }
    }
}
