using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Banyan.Code;
using Banyan.Apps.Configs;

namespace Banyan.Apps
{
    /// <summary>
    /// 用户兴趣向量检索类
    /// 负责从用户画像系统中高效检索用户兴趣向量
    /// </summary>
    public class UserInterestVectorRetrieval
    {
        private readonly ICache _cache;
        private readonly UserProfileBLL _userProfileBLL;
        private readonly VectorService _vectorService;

        /// <summary>
        /// 构造函数
        /// </summary>
        public UserInterestVectorRetrieval()
        {
            _cache = CacheFactory.Cache();
            _userProfileBLL = new UserProfileBLL();
            _vectorService = new VectorService();
        }

        /// <summary>
        /// 获取用户兴趣向量
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户兴趣向量</returns>
        public async Task<double[]> GetUserInterestVectorAsync(int userId)
        {
            try
            {
                if (userId <= 0)
                {
                    Logger.Error($"无效的用户ID: {userId}");
                    return null;
                }

                Logger.Info($"开始获取用户 {userId} 的兴趣向量");

                // 1. 尝试从缓存获取
                var cacheKey = $"user_vector:{userId}";
                var cachedVector = _cache.GetCache<double[]>(cacheKey);
                if (cachedVector != null)
                {
                    Logger.Info($"从缓存获取用户 {userId} 兴趣向量成功");
                    return cachedVector;
                }

                // 2. 从数据库获取
                var vector = await _vectorService.GetUserVectorAsync(userId);
                if (vector != null && vector.Length == VectorServiceConfig.VECTOR_DIMENSION)
                {
                    // 缓存向量
                    _cache.WriteCache(vector, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.VECTOR_CACHE_DAYS));
                    Logger.Info($"从数据库获取用户 {userId} 兴趣向量成功");
                    return vector;
                }

                // 3. 如果没有向量，尝试生成
                Logger.Info($"用户 {userId} 没有兴趣向量，尝试生成");
                var profile = await _userProfileBLL.GetOrCreateUserProfile(userId);
                if (profile != null)
                {
                    await _userProfileBLL.GenerateUserInterestVectorAsync(userId, profile);
                    
                    // 再次尝试获取
                    vector = await _vectorService.GetUserVectorAsync(userId);
                    if (vector != null)
                    {
                        _cache.WriteCache(vector, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.VECTOR_CACHE_DAYS));
                        Logger.Info($"生成并获取用户 {userId} 兴趣向量成功");
                        return vector;
                    }
                }

                Logger.Warn($"无法获取用户 {userId} 的兴趣向量");
                return null;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取用户 {userId} 兴趣向量时发生错误: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 批量获取用户兴趣向量
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <returns>用户ID到向量的映射</returns>
        public async Task<Dictionary<int, double[]>> GetUserInterestVectorsBatchAsync(List<int> userIds)
        {
            if (userIds == null || userIds.Count == 0)
            {
                Logger.Error("用户ID列表为空");
                return new Dictionary<int, double[]>();
            }

            try
            {
                Logger.Info($"开始批量获取用户兴趣向量，用户数量: {userIds.Count}");
                
                var result = new Dictionary<int, double[]>();
                var missingUserIds = new List<int>();

                // 1. 尝试从缓存获取所有向量
                foreach (var userId in userIds)
                {
                    var cacheKey = $"user_vector:{userId}";
                    var cachedVector = _cache.GetCache<double[]>(cacheKey);
                    
                    if (cachedVector != null)
                    {
                        result[userId] = cachedVector;
                    }
                    else
                    {
                        missingUserIds.Add(userId);
                    }
                }

                Logger.Info($"从缓存获取用户向量，命中: {result.Count}，未命中: {missingUserIds.Count}");

                // 2. 对于缓存未命中的，从数据库获取
                if (missingUserIds.Count > 0)
                {
                    foreach (var userId in missingUserIds)
                    {
                        var vector = await _vectorService.GetUserVectorAsync(userId);
                        if (vector != null && vector.Length == VectorServiceConfig.VECTOR_DIMENSION)
                        {
                            result[userId] = vector;
                            
                            // 缓存向量
                            var cacheKey = $"user_vector:{userId}";
                            _cache.WriteCache(vector, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.VECTOR_CACHE_DAYS));
                        }
                    }
                }

                Logger.Info($"批量获取用户兴趣向量完成，成功获取: {result.Count}/{userIds.Count}");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"批量获取用户兴趣向量时发生错误: {ex.Message}", ex);
                return new Dictionary<int, double[]>();
            }
        }

        /// <summary>
        /// 获取默认兴趣向量
        /// 当用户没有兴趣向量时使用
        /// </summary>
        /// <returns>默认兴趣向量</returns>
        public double[] GetDefaultInterestVector()
        {
            try
            {
                // 从缓存获取默认向量
                var cacheKey = "default_interest_vector";
                var cachedVector = _cache.GetCache<double[]>(cacheKey);
                if (cachedVector != null)
                {
                    return cachedVector;
                }

                // 创建均匀分布的默认向量
                var defaultVector = new double[VectorServiceConfig.VECTOR_DIMENSION];
                for (int i = 0; i < defaultVector.Length; i++)
                {
                    defaultVector[i] = 1.0 / Math.Sqrt(VectorServiceConfig.VECTOR_DIMENSION);
                }

                // 缓存默认向量
                _cache.WriteCache(defaultVector, cacheKey, DateTime.Now.AddDays(30));
                
                return defaultVector;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取默认兴趣向量时发生错误: {ex.Message}", ex);
                
                // 返回零向量作为最后的回退
                return new double[VectorServiceConfig.VECTOR_DIMENSION];
            }
        }

        /// <summary>
        /// 更新用户兴趣向量缓存
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="vector">用户兴趣向量</param>
        public void UpdateUserInterestVectorCache(int userId, double[] vector)
        {
            if (userId <= 0 || vector == null || vector.Length != VectorServiceConfig.VECTOR_DIMENSION)
            {
                Logger.Error($"更新用户 {userId} 兴趣向量缓存失败：无效的参数");
                return;
            }

            try
            {
                var cacheKey = $"user_vector:{userId}";
                _cache.WriteCache(vector, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.VECTOR_CACHE_DAYS));
                Logger.Info($"更新用户 {userId} 兴趣向量缓存成功");
            }
            catch (Exception ex)
            {
                Logger.Error($"更新用户 {userId} 兴趣向量缓存时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 清除用户兴趣向量缓存
        /// </summary>
        /// <param name="userId">用户ID</param>
        public void ClearUserInterestVectorCache(int userId)
        {
            if (userId <= 0)
            {
                Logger.Error($"清除用户兴趣向量缓存失败：无效的用户ID {userId}");
                return;
            }

            try
            {
                var cacheKey = $"user_vector:{userId}";
                _cache.RemoveCache(cacheKey);
                Logger.Info($"清除用户 {userId} 兴趣向量缓存成功");
            }
            catch (Exception ex)
            {
                Logger.Error($"清除用户 {userId} 兴趣向量缓存时发生错误: {ex.Message}", ex);
            }
        }
    }
}