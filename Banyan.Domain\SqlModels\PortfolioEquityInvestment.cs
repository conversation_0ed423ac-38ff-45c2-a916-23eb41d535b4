﻿using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.BusinessCenterPortal)]
    public class PortfolioEquityInvestment
    { // used for personal center in miniapp
        [SqlField(IsPrimaryKey = true)]
        public string EIID { get; set; }
   
        [SqlField]
        public string portfolioID { get; set; } = string.Empty;
        [SqlField]
        public string fundID { get; set; } = string.Empty;
        [SqlField]
        public string currency { get; set; } = string.Empty;
        [SqlField]
        public string investType { get; set; } = string.Empty;
        //[SqlField]
        //public DateTime closeDate { get; set; } = DateTime.Now;
        [SqlField]
        public string modifiedBy { get; set; } = string.Empty; // closeDate formatted
        [SqlField]
        public decimal payable { get; set; }
        [SqlField]
        public string round { get; set; } = string.Empty;
        [SqlField]
        public int rid { get; set; } 
        //[SqlField]
        //public decimal shareOwnedNo { get; set; }
        [SqlField]
        public string convertFromNoteID { get; set; } = string.Empty; // shareNum formatted
    
        [SqlField]
        public string conversionRatio { get; set; } = string.Empty; // cost formatted
      
        //[SqlField]
        //public decimal cost { get; set; }

        [SqlField]
        public string remarks { get; set; } = string.Empty;


        [SqlField]
        public string createdBy { get; set; } // portfolio Name

    }

}
