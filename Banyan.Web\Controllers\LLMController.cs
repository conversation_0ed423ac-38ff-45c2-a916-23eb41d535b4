using Banyan.Code;
using log4net;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Threading;
using System.Net;
using System.Web;
using Banyan.Domain;
using System.IO;
using System.Configuration;

namespace Banyan.Apps
{
    /// <summary>
    /// 大模型相关服务类
    /// </summary>
    public class LLMController
    {
        private static readonly ILog Logger = LogManager.GetLogger("LLMService");

        #region 聊天相关类定义
        public class Message
        {
            [JsonProperty("role")]
            public string role { get; set; }
            [JsonProperty("content")]
            public string content { get; set; }
            public Message() { }
            public Message(string role, string content)
            {
                this.role = role;
                this.content = content;
            }
            public List<Message> withHistory(List<Message> history)
            {
                if (history != null && history.Count > 0)
                {
                    history.Add(this);
                    return history;
                }
                return new List<Message> { this };
            }
        }

 
        #endregion

        #region MCP相关类定义
        public class MCPResult
        {
            public bool ToolCalled { get; set; }
            public string EnhancedPrompt { get; set; }
            public string StatusMessage { get; set; }
            public List<ProjectSearchResult> ProjectsFound { get; set; } = new List<ProjectSearchResult>();
        }

        public class ProjectSearchResult
        {
            public int Id { get; set; }
            public string Name { get; set; }
            public string Summary { get; set; }
            public string Content { get; set; }
        }

        public class TimeCondition
        {
            public DateTime? StartDate { get; set; }
            public DateTime? EndDate { get; set; }
            public string Description { get; set; }
        }
        #endregion

        #region 联网搜索相关类定义
        public class WebSearchResult
        {
            public string Title { get; set; }
            public string Url { get; set; }
            public string Content { get; set; }
            public string Engine { get; set; }
            public bool IsContentEnhanced { get; set; } = false;
        }

        public class SearxngResponse
        {
            public List<SearxngResult> results { get; set; }
        }

        public class SearxngResult
        {
            public string title { get; set; }
            public string url { get; set; }
            public string content { get; set; }
            public string engine { get; set; }
        }
        #endregion

        #region 流式聊天处理
        /// <summary>
        /// 处理流式聊天请求
        /// </summary>
        public async Task ProcessChatStreamAsync(
            string model,
            string originalPrompt,
            List<Message> history,
            bool enableMCP,
            bool enableWebSearch,
            Member user,
            HttpResponseBase response,
            CancellationToken cancellationToken)
        {
            string userName = user?.RealName ?? "Anonymous";
            
            // 并行执行MCP和联网搜索（都基于原始用户输入）
            string enhancedPrompt = originalPrompt;
            var toolResults = new List<string>();
            
            if ((enableMCP || enableWebSearch) && !string.IsNullOrEmpty(originalPrompt))
            {
                var tasks = new List<Task>();
                
                // MCP工具任务
                Task<MCPResult> mcpTask = null;
                if (enableMCP)
                {
                    mcpTask = ProcessMCPRequestAsync(originalPrompt, user);
                    tasks.Add(mcpTask);
                }
                
                // 联网搜索任务
                Task<List<WebSearchResult>> webSearchTask = null;
                if (enableWebSearch)
                {
                    webSearchTask = PerformWebSearchAsync(originalPrompt, 8);
                    tasks.Add(webSearchTask);
                }
                
                try
                {
                    // 并行执行所有工具任务
                    await Task.WhenAll(tasks);
                    
                    // 处理MCP结果
                    if (mcpTask != null)
                    {
                        var mcpResult = await mcpTask;
                        if (mcpResult != null && mcpResult.ToolCalled && mcpResult.ProjectsFound.Count > 0)
                        {
                            var mcpInfo = new StringBuilder();
                            mcpInfo.AppendLine("\n[MCP工具检索到的项目信息]");
                            foreach (var project in mcpResult.ProjectsFound)
                            {
                                mcpInfo.AppendLine($"\n项目名称: {project.Name}");
                                if (!string.IsNullOrEmpty(project.Summary))
                                {
                                    mcpInfo.AppendLine($"项目概述: {project.Summary}");
                                }
                                if (!string.IsNullOrEmpty(project.Content))
                                {
                                    var content = project.Content.Length > 1000 ? 
                                        project.Content.Substring(0, 1000) + "..." : project.Content;
                                    mcpInfo.AppendLine($"详细信息: {content}");
                                }
                                mcpInfo.AppendLine("---");
                            }
                            toolResults.Add(mcpInfo.ToString());
                            
                            // 发送MCP工具调用状态
                            response.Write($"data: {JsonConvert.SerializeObject(new { tool_status = mcpResult.StatusMessage })}\n\n");
                            response.Flush();
                        }
                    }
                    
                    // 处理联网搜索结果
                    if (webSearchTask != null)
                    {
                        var webSearchResults = await webSearchTask;
                        if (webSearchResults != null && webSearchResults.Count > 0)
                        {
                            var webInfo = new StringBuilder();
                            webInfo.AppendLine("\n[联网搜索结果]");
                            foreach (var result in webSearchResults.Take(5))
                            {
                                webInfo.AppendLine($"\n标题: {result.Title}");
                                webInfo.AppendLine($"链接: {result.Url}");
                                if (!string.IsNullOrEmpty(result.Content))
                                {
                                    var content = result.Content.Length > 500 ? 
                                        result.Content.Substring(0, 500) + "..." : result.Content;
                                    webInfo.AppendLine($"内容摘要: {content}");
                                }
                                webInfo.AppendLine("---");
                            }
                            toolResults.Add(webInfo.ToString());
                            
                            // 发送联网搜索状态
                            response.Write($"data: {JsonConvert.SerializeObject(new { tool_status = $"🌐 联网搜索到 {webSearchResults.Count} 条相关信息" })}\n\n");
                            response.Flush();
                        }
                    }
                }
                catch (Exception toolEx)
                {
                    Logger.Error("工具调用失败", toolEx);
                    // 工具失败不影响正常聊天，继续处理
                }
            }
            
            // 构建最终的增强提示（如果有工具结果）
            if (toolResults.Count > 0)
            {
                enhancedPrompt = string.Join("\n", toolResults) + "\n\n[用户问题]\n" + originalPrompt;
            }
            
            // 流式生成逻辑
            await HttpMethods.HttpLLMStreamPost(
                jsonData: new
                {
                    model = model,
                    stream = true,
                    messages = new Message("user", enhancedPrompt).withHistory(history)
                },
                headht: null,
                callback: data =>
                {
                    try
                    {
                        // 检查客户端是否已断开连接
                        if (response.IsClientConnected == false)
                        {
                            throw new OperationCanceledException("Client disconnected");
                        }

                        if (data == "[DONE]")
                        {
                            response.Write($"data:  {JsonConvert.SerializeObject(new { status = "done" })}\n\n");
                            response.Flush();
                            return;
                        }
                        var jsonData = JsonConvert.SerializeObject(new
                        {
                            c = data
                        });

                        response.Write($"data: {jsonData}\n\n");
                        response.Flush();
                    }
                    catch (Exception ex)
                    {
                        Logger.Info("聊天流式响应写入失败", ex);
                        throw;
                    }
                },
                cancellationToken: cancellationToken,
                user: userName
            );
        }
        #endregion

        #region 项目分析流式处理
        /// <summary>
        /// 处理项目分析流式请求
        /// </summary>
        public async Task ProcessAnalyseStreamAsync(
            int projectId,
            string model,
            Member user,
            HttpResponseBase response,
            CancellationToken cancellationToken)
        {
            string userName = user?.RealName ?? "Anonymous";
            
            var project = new ProjectBLL().GetModel(projectId);
            var str = ProjectBLL.formatProject(project);

            if (str.Length < 150)
            {
                response.Write("data: 信息不足，请填写完项目信息后重试。\n\n");
                response.Flush();
                return;
            }

            await HttpMethods.HttpLLMStreamPost(
                jsonData: new
                {
                    model,
                    stream = true,
                    messages = new[] { new { role = "user", content = str } }
                },
                headht: null,
                callback: data =>
                {
                    try
                    {
                        if (response.IsClientConnected == false)
                        {
                            throw new OperationCanceledException("Client disconnected");
                        }

                        if (data == "[DONE]")
                        {
                            response.Write("event: end\ndata: Stream completed\n\n");
                            response.Flush();
                            return;
                        }

                        var sseData = $"data: {JsonConvert.SerializeObject(data)}\n\n";
                        response.Write(sseData);
                        response.Flush();
                    }
                    catch (Exception ex)
                    {
                        var errorMsg = $"event: error\ndata: {JsonConvert.SerializeObject(new { error = ex.Message })}\n\n";
                        response.Write(errorMsg);
                        response.Flush();
                        throw;
                    }
                },
                cancellationToken: cancellationToken,
                user: userName
            );
        }
        #endregion

        #region 推荐问题生成
        /// <summary>
        /// 生成推荐问题
        /// </summary>
        public List<string> GenerateSuggestedQuestions(string model = "qwen3-30b-a3b-mlx", int count = 6, Member user = null)
        {
            try
            {
                Logger.Info($"LLM_REQUEST:开始获取推荐问题，模型: {model}, 数量: {count}" + user?.RealName ?? "System");
                
                string userName = user?.RealName ?? "Anonymous";
                
                string prompt = $@"你是一个投资管理系统的智能助手。请根据投资管理的业务场景，生成{count}个有用的问题建议。

要求：
1. 问题应该涵盖投资项目管理的各个方面，如项目分析、风险评估、进展跟踪、决策支持等
2. 问题要实用且具体，能够帮助投资经理快速获取有价值的信息
3. 每个问题应该简洁明了，长度适中
4. 问题要多样化，不要重复
5. 直接返回问题列表，每个问题占一行，不需要编号或其他格式

请生成{count}个推荐问题：";

                var response = HttpMethods.HttpLLMPost(new { 
                    model = model,
                    stream = false,
                    messages = new[]
                    {
                        new { role = "user", content = prompt + "/no_think" }
                    }
                }, user: userName);
                
                var questions = new List<string>();
                if (!string.IsNullOrEmpty(response))
                {
                    var lines = response.Split(new char[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                    
                    foreach (var line in lines)
                    {
                        var trimmedLine = line.Trim();
                        trimmedLine = Regex.Replace(trimmedLine, @"^\d+[\.\)]\s*", "");
                        trimmedLine = Regex.Replace(trimmedLine, @"^[-\*\+•]\s*", "");
                        
                        if (!string.IsNullOrWhiteSpace(trimmedLine) && 
                            trimmedLine.Length > 5 && 
                            trimmedLine.Length < 100 &&
                            Regex.IsMatch(trimmedLine, @"[\u4e00-\u9fff]"))
                        {
                            questions.Add(trimmedLine);
                        }
                        
                        if (questions.Count >= count)
                            break;
                    }
                }
                
                // 如果生成的问题不够，补充默认问题
                if (questions.Count < count)
                {
                    var defaultQuestions = new List<string>
                    {
                        "帮我分析一下最近的投资项目有哪些亮点？",
                        "请总结一下本周的项目进展情况",
                        "有哪些项目需要重点关注风险问题？",
                        "帮我整理一下待处理的投资决策事项",
                        "分析一下当前项目组合的行业分布情况",
                        "最近有哪些项目的财务数据需要更新？",
                        "哪些项目的估值变化较大？",
                        "本月需要跟进的重要会议有哪些？",
                        "请帮我比较一下同行业项目的表现",
                        "有哪些项目可能需要追加投资？"
                    };
                    
                    foreach (var defaultQuestion in defaultQuestions.OrderBy(x => Guid.NewGuid()))
                    {
                        if (questions.Count >= count) break;
                        if (!questions.Contains(defaultQuestion))
                        {
                            questions.Add(defaultQuestion);
                        }
                    }
                }
                
                var finalQuestions = questions.Take(count).ToList();
                
                Logger.Info($"LLM_REQUEST:成功生成 {finalQuestions.Count} 个推荐问题" + user?.RealName ?? "System");

                return finalQuestions;
            }
            catch (Exception ex)
            {
                Logger.Error("获取推荐问题失败", ex);
                
                var fallbackQuestions = new List<string>
                {
                    "帮我分析一下最近的投资项目有哪些亮点？",
                    "请总结一下本周的项目进展情况",
                    "有哪些项目需要重点关注风险问题？",
                    "帮我整理一下待处理的投资决策事项",
                    "分析一下当前项目组合的行业分布情况",
                    "最近有哪些项目的财务数据需要更新？"
                }.Take(count).ToList();
                
                return fallbackQuestions;
            }
        }

        /// <summary>
        /// 根据AI回答生成推荐问题
        /// </summary>
        public List<string> GenerateSuggestedQuestionsFromResponse(
            string aiResponse, 
            string userQuestion = "", 
            string model = "qwen3-30b-a3b-mlx", 
            int count = 6, 
            Member user = null)
        {
            try
            {
                Logger.Info($"LLM_REQUEST:开始根据AI回答生成推荐问题，模型: {model}, 数量: {count}" + user?.RealName ?? "System");
                
                string userName = user?.RealName ?? "Anonymous";
                
                string prompt = $@"你是一个投资管理系统的智能助手。请根据以下AI回答的内容，生成{count}个相关的后续问题建议。

用户刚才的问题：{userQuestion}

AI的回答内容：
{aiResponse}

要求：
1. 生成的问题应该与AI回答的内容密切相关，能够帮助用户深入了解或跟进相关话题
2. 问题要实用且具体，符合投资管理的业务场景
3. 每个问题应该简洁明了，长度适中
4. 问题要多样化，不要重复
5. 优先生成能够帮助用户进一步分析、决策或获取更多信息的问题
6. 直接返回问题列表，每个问题占一行，不需要编号或其他格式

请生成{count}个相关的推荐问题：";

                var response = HttpMethods.HttpLLMPost(new { 
                    model,
                    stream = false,
                    messages = new[]
                    {
                        new { role = "user", content = prompt + "/no_think" }
                    }
                }, user: userName);
                
                var questions = new List<string>();
                if (!string.IsNullOrEmpty(response))
                {
                    var lines = response.Split(new char[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                    
                    foreach (var line in lines)
                    {
                        var trimmedLine = line.Trim();
                        trimmedLine = Regex.Replace(trimmedLine, @"^\d+[\.\)]\s*", "");
                        trimmedLine = Regex.Replace(trimmedLine, @"^[-\*\+•]\s*", "");
                        
                        if (!string.IsNullOrWhiteSpace(trimmedLine) && 
                            trimmedLine.Length > 5 && 
                            trimmedLine.Length < 100 &&
                            Regex.IsMatch(trimmedLine, @"[\u4e00-\u9fff]"))
                        {
                            questions.Add(trimmedLine);
                        }
                        
                        if (questions.Count >= count)
                            break;
                    }
                }
                
                if (questions.Count < count)
                {
                    var fallbackQuestions = new List<string>
                    {
                        "还有其他相关的细节需要了解吗？",
                        "这个情况下有什么需要特别注意的风险吗？",
                        "能否提供更多关于这个话题的数据分析？",
                        "基于这个分析，下一步应该如何行动？",
                        "类似的情况在历史上是如何处理的？",
                        "这个结论对其他项目有什么启示？"
                    };
                    
                    foreach (var fallbackQuestion in fallbackQuestions.OrderBy(x => Guid.NewGuid()))
                    {
                        if (questions.Count >= count) break;
                        if (!questions.Contains(fallbackQuestion))
                        {
                            questions.Add(fallbackQuestion);
                        }
                    }
                }
                
                var finalQuestions = questions.Take(count).ToList();
                
                Logger.Info($"LLM_REQUEST:成功根据AI回答生成 {finalQuestions.Count} 个推荐问题" + user?.RealName ?? "System");

                return finalQuestions;
            }
            catch (Exception ex)
            {
                Logger.Error("LLM_REQUEST:根据AI回答生成推荐问题失败", ex);
                
                var fallbackQuestions = new List<string>
                {
                    "还有其他相关的细节需要了解吗？",
                    "这个情况下有什么需要特别注意的风险吗？",
                    "能否提供更多关于这个话题的数据分析？",
                    "基于这个分析，下一步应该如何行动？"
                }.Take(count).ToList();
                
                return fallbackQuestions;
            }
        }
        #endregion

        #region 联网搜索功能
        /// <summary>
        /// 执行联网搜索
        /// </summary>
        public async Task<List<WebSearchResult>> PerformWebSearchAsync(string query, int limit)
        {
            var results = new List<WebSearchResult>();
            var allResults = new List<WebSearchResult>();
            
            try
            {
                int rawSearchLimit = CalculateOptimalSearchLimit(limit);
                
                Logger.Info($"LLM_REQUEST:为获得 {limit} 条高质量结果，将搜索 {rawSearchLimit} 条原始结果进行过滤 (扩展比例: {(double)rawSearchLimit/limit:F1}x)");
                
                var searchQueries = GenerateSearchQueries(query);
                
                using (var httpClient = new HttpClient())
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(30);
                    httpClient.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
                    
                    int maxPages = CalculateMaxPages(limit);
                    int queryCount = Math.Min(searchQueries.Count, limit > 50 ? 3 : 2);
                    
                    Logger.Info($"LLM_REQUEST:搜索策略: {GetSearchModeDescription(limit)}模式，页数: {maxPages}，查询数: {queryCount}");
                    
                    foreach (var searchQuery in searchQueries.Take(queryCount))
                    {
                        for (int page = 1; page <= maxPages; page++)
                        {
                            try
                            {
                                string searxngUrl = "http://*************:8091/search";
                                string engines = "bing,google,duckduckgo,yahoo,yandex";
                                int resultsPerPage = CalculateResultsPerPage(rawSearchLimit, maxPages, queryCount);
                                
                                string searchUrl = $"{searxngUrl}?q={WebUtility.UrlEncode(searchQuery)}&format=json&engines={engines}&pageno={page}&results_per_page={resultsPerPage}";
                                
                                Logger.Info($"LLM_REQUEST:SearXNG请求: 查询='{searchQuery}', 页={page}, 每页={resultsPerPage}条");
                                
                                string jsonResponse = await httpClient.GetStringAsync(searchUrl);
                                
                                if (!string.IsNullOrEmpty(jsonResponse))
                                {
                                    var searchResponse = JsonConvert.DeserializeObject<SearxngResponse>(jsonResponse);
                                    
                                    if (searchResponse?.results != null)
                                    {
                                        foreach (var item in searchResponse.results)
                                        {
                                            if (!string.IsNullOrEmpty(item.title) && !string.IsNullOrEmpty(item.url))
                                            {
                                                if (!allResults.Any(r => r.Url == item.url))
                                                {
                                                    allResults.Add(new WebSearchResult
                                                    {
                                                        Title = item.title,
                                                        Url = item.url,
                                                        Content = item.content ?? "",
                                                        Engine = item.engine ?? "unknown"
                                                    });
                                                }
                                            }
                                        }
                                        
                                        Logger.Info($"LLM_REQUEST:从页面{page}获取到{searchResponse.results.Count}条原始结果，累计{allResults.Count}条");
                                        
                                        if (searchResponse.results.Count < resultsPerPage / 2 && page == 1)
                                        {
                                            Logger.Info($"LLM_REQUEST:警告：SearXNG返回结果({searchResponse.results.Count})少于请求数({resultsPerPage})，可能存在限制");
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                Logger.Error($"LLM_REQUEST:Individual search failed for query: {searchQuery}, page: {page}", ex);
                            }
                            
                            if (allResults.Count >= rawSearchLimit)
                            {
                                Logger.Info($"LLM_REQUEST:已获得足够的原始结果({allResults.Count}条)，停止后续搜索");
                                break;
                            }
                        }
                        
                        if (allResults.Count >= rawSearchLimit)
                        {
                            break;
                        }
                    }
                    
                    await EnhanceResultsWithWebContent(allResults, httpClient);
                }
                
                int totalEnhancedCount = allResults.Count(r => r.IsContentEnhanced);
                Logger.Info($"LLM_REQUEST:内容增强完成: 共增强 {totalEnhancedCount} 条结果 (总共{allResults.Count}条原始结果)");
                
                results = FilterAndRankResults(allResults, query, limit);
            }
            catch (Exception ex)
            {
                Logger.Error($"LLM_REQUEST:SearXNG search failed for query: {query}", ex);
                throw;
            }
            
            return results;
        }

        // ... 继续添加其他搜索相关的私有方法
        #endregion

        #region MCP工具处理
        /// <summary>
        /// 处理MCP工具调用请求
        /// </summary>
        public async Task<MCPResult> ProcessMCPRequestAsync(string userPrompt, Member user)
        {
            var result = new MCPResult();
            
            try
            {
                var now = DateTime.Now;
                string analysisPrompt = $@"请分析以下用户问题是否需要查询项目信息，并解析时间范围。

当前时间：{now:yyyy-MM-dd HH:mm:ss}
当前是星期{((int)now.DayOfWeek == 0 ? "日" : ((int)now.DayOfWeek).ToString())}

用户问题: {userPrompt}

### 如果问题涉及以下任何方面，则需要查询项目信息：
1. 具体项目名称、公司名称、创始人姓名
2. 项目分析、评估、对比、排名
3. 项目状态、进展、阶段、风险评估
4. 财务数据、投资信息、估值、融资情况
5. 项目搜索、筛选、匹配
6. 项目相关的统计、汇总、报告
7. 特定行业或技术领域的项目查询

### 关键词提取原则
需注意搜索对象是具体的项目内容，不存在抽象的，和项目无关的词！关键词需要能匹配到项目实际内容！
提取2-5个核心关键词，优先级如下：
1. **具体名词**：太阳能、芯片、AI、新能源、生物医药等技术/行业词汇
2. **实体名称**：项目名、公司名、人名、地点名
3. **数量限定**：融资轮次、规模、阶段等
4. **特殊要求**：风险等级、状态描述等
4.如果不属于以上，只返回无这个字

### 查询类型分类：
- 特定搜索：针对特定项目、公司、技术、行业的查询，需要关键词过滤
- 统计分析：对项目组合、整体情况、分布状况的分析，根据情况判断是否需要关键词过滤

### 如果问题包含时间限制，请解析出具体的开始时间和结束时间：
- ""本周""/""这周"" → 从本周一到今天
- ""上周"" → 从上周一到上周日  
- ""本月""/""这个月"" → 从本月1号到今天
- ""上月""/""上个月"" → 从上个月1号到上个月最后一天
- ""今年""/""本年"" → 从今年1月1日到今天
- ""去年""/""上年"" → 从去年1月1日到去年12月31日
- ""最近X天""/""近X天"" → 从X天前到今天（包含今天）
- ""最近X个月""/""近X个月"" → 从X个月前的同一天到今天（包含今天）
- ""X年"" → 从该年1月1日到该年12月31日

## 输出格式
请严格按照以下格式回答（只输出关键词，不要其他内容）：
需要查询: [是/否]
查询类型: [特定搜索/统计分析]
搜索关键词: [提取2-5个核心关键词，用逗号分隔；如果无需关键词，填'无']
时间范围: [如果问题包含时间限制，请提取，如：本周/最近/今年/上个月/近三个月/2024年等，没有则填'无']
开始时间: [如果有时间范围，填写yyyy-MM-dd格式的开始日期，没有则填'无']
结束时间: [如果有时间范围，填写yyyy-MM-dd格式的结束日期，没有则填'无']


## 处理示例

**示例1：**
用户在2024-07-15提出问题：""最近三个月有哪些AI项目完成了A轮融资？""
```
需要查询: 是
查询类型: 特定搜索
搜索关键词: AI,人工智能,A轮融资
时间范围: 最近三个月
开始时间: 2024-04-15
结束时间: 无
```
**示例2：**
用户在2024-07-15提出问题：""分析一下2024年项目组合的行业分布情况""
```
需要查询: 是
查询类型: 统计分析
搜索关键词: 无
时间范围: 最近
开始时间: 2024-01-01
结束时间: 2024-12-31
```
**示例3：**
用户在2024-07-15提出问题：""有哪些项目需要重点关注风险问题？""
```
需要查询: 是
查询类型: 统计分析
搜索关键词: 无
时间范围: 无
开始时间: 无
结束时间: 无
```

**示例3：**
用户问题：""帮我整理一下待处理的投资决策事项""
```
需要查询: 是
查询类型: 统计分析
搜索关键词: 无
时间范围: 无
开始时间: 无
结束时间: 无
```

";

                var analysisResponse = HttpMethods.HttpLLMPost(new
                {
                    model = "qwen3-30b-a3b-mlx",
                    stream = false,
                    messages = new[] { new { role = "user", content = analysisPrompt } }
                }, user: user?.RealName ?? "System");

                if (!string.IsNullOrEmpty(analysisResponse))
                {
                    analysisResponse = Regex.Replace(
                        analysisResponse, 
                        @"<think>.*?</think>", 
                        "", 
                        RegexOptions.Singleline | RegexOptions.IgnoreCase
                    ).Trim();

                    Logger.Info($"LLM_REQUEST: MCP调用查询关键点LLM分析结果: {analysisResponse}");

                    var lines = analysisResponse.Split('\n');
                    bool needsQuery = false;
                    string queryType = "";
                    string keywords = "";
                    string timeRange = "";
                    string startDateStr = "";
                    string endDateStr = "";
                    string timeDescription = "";

                    foreach (var line in lines)
                    {
                        if (line.Contains("需要查询") && (line.Contains("是") || line.Contains("yes")))
                        {
                            needsQuery = true;
                        }
                        if (line.Contains("查询类型"))
                        {
                            var colonIndex = line.IndexOf(':');
                            if (colonIndex > 0 && colonIndex < line.Length - 1)
                            {
                                queryType = line.Substring(colonIndex + 1).Trim();
                            }
                        }
                        if (line.Contains("搜索关键词") || line.Contains("关键词"))
                        {
                            var colonIndex = line.IndexOf(':');
                            if (colonIndex > 0 && colonIndex < line.Length - 1)
                            {
                                keywords = line.Substring(colonIndex + 1).Trim();
                            }
                            if(keywords == "无")
                            {
                                keywords = "";
                            }
                        }
                        if (line.Contains("时间范围"))
                        {
                            var colonIndex = line.IndexOf(':');
                            if (colonIndex > 0 && colonIndex < line.Length - 1)
                            {
                                timeRange = line.Substring(colonIndex + 1).Trim();
                            }
                        }
                        if (line.Contains("开始时间"))
                        {
                            var colonIndex = line.IndexOf(':');
                            if (colonIndex > 0 && colonIndex < line.Length - 1)
                            {
                                startDateStr = line.Substring(colonIndex + 1).Trim();
                            }
                        }
                        if (line.Contains("结束时间"))
                        {
                            var colonIndex = line.IndexOf(':');
                            if (colonIndex > 0 && colonIndex < line.Length - 1)
                            {
                                endDateStr = line.Substring(colonIndex + 1).Trim();
                            }
                        }
                    }

                    if (needsQuery)
                    {
                        TimeCondition timeCondition = null;
                        if (!string.IsNullOrEmpty(timeRange) && timeRange != "无" && 
                            !string.IsNullOrEmpty(startDateStr) && startDateStr != "无" &&
                            !string.IsNullOrEmpty(endDateStr) && endDateStr != "无")
                        {
                            try
                            {
                                if (DateTime.TryParse(startDateStr, out DateTime startDate) && 
                                    DateTime.TryParse(endDateStr, out DateTime endDate))
                                {
                                    timeCondition = new TimeCondition
                                    {
                                        StartDate = startDate.Date,
                                        EndDate = endDate.Date.AddDays(1),
                                        Description = !string.IsNullOrEmpty(timeDescription) && timeDescription != "无" ? timeDescription : timeRange
                                    };
                                    
                                    Logger.Info($"LLM_REQUEST:时间解析成功（一次调用） - 输入: {timeRange}, 开始: {timeCondition.StartDate:yyyy-MM-dd}, 结束: {timeCondition.EndDate:yyyy-MM-dd}, 描述: {timeCondition.Description}");
                                }
                                else
                                {
                                    Logger.Warn($"LLM_REQUEST:时间解析失败，日期格式错误 - 开始: {startDateStr}, 结束: {endDateStr}，尝试后备方案");
                                    timeCondition = ParseTimeConditionFallback(timeRange, user);
                                }
                            }
                            catch (Exception ex)
                            {
                                Logger.Error($"LLM_REQUEST:构建时间条件失败: {timeRange}，尝试后备方案", ex);
                                timeCondition = ParseTimeConditionFallback(timeRange, user);
                            }
                        }
                        else if (!string.IsNullOrEmpty(timeRange) && timeRange != "无")
                        {
                            Logger.Info($"LLM_REQUEST:一次调用未能完整解析时间，使用后备方案: {timeRange}");
                            timeCondition = ParseTimeConditionFallback(timeRange, user);
                        }
                        
                        bool isStatisticalAnalysis = queryType.Contains("统计分析") || queryType.Contains("统计") || queryType.Contains("分析");
                        
                        var searchResult = await SearchProjectsForMCPAsync(
                            keywords, 
                            timeCondition, 
                            isStatisticalAnalysis,
                            user
                        );
                        
                        if (searchResult != null && searchResult.Count > 0)
                        {
                            result.ToolCalled = true;
                            result.ProjectsFound = searchResult;
                            
                            var statusMsg = $"🔍 智能检索到 {searchResult.Count} 个相关项目";
                            if (isStatisticalAnalysis)
                            {
                                statusMsg += "（统计分析数据）";
                            }
                            if (timeCondition != null)
                            {
                                statusMsg += $"（时间范围：{timeCondition.Description}）";
                            }
                            result.StatusMessage = statusMsg;
                            
                            var projectInfo = new StringBuilder();
                            if (isStatisticalAnalysis)
                            {
                                projectInfo.AppendLine("[MCP工具为统计分析提供的项目数据]");
                                projectInfo.AppendLine("注意：以下是用于分析的项目数据集，请根据用户问题进行统计和分析。");
                            }
                            else
                            {
                                projectInfo.AppendLine("[MCP工具自动检索到的相关项目信息]");
                            }
                            
                            if (timeCondition != null)
                            {
                                projectInfo.AppendLine($"时间条件：{timeCondition.Description}");
                            }
                            
                            foreach (var project in searchResult)
                            {
                                projectInfo.AppendLine($"\n项目名称: {project.Name}");
                                if (!string.IsNullOrEmpty(project.Summary))
                                {
                                    projectInfo.AppendLine($"项目概述: {project.Summary}");
                                }
                                projectInfo.AppendLine($"项目ID: {project.Id}");
                                if (!string.IsNullOrEmpty(project.Content))
                                {
                                    var contentLimit = isStatisticalAnalysis ? 1500 : 1000;
                                    var content = project.Content.Length > contentLimit ? 
                                        project.Content.Substring(0, contentLimit) + "..." : project.Content;
                                    projectInfo.AppendLine($"详细信息: {content}");
                                }
                                projectInfo.AppendLine("---");
                            }
                            
                            result.EnhancedPrompt = $"{projectInfo}\n\n[用户问题]\n{userPrompt}";
                        }
                        else
                        {
                            var noResultMsg = "🔍 未找到相关项目信息";
                            if (timeCondition != null)
                            {
                                noResultMsg += $"（时间范围：{timeCondition.Description}）";
                            }
                            result.StatusMessage = noResultMsg;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("LLM_REQUEST:MCP工具处理失败", ex);
                result.StatusMessage = "🔧 工具调用失败";
            }
            
            return result;
        }

        /// <summary>
        /// MCP搜索项目
        /// </summary>
        public async Task<List<ProjectSearchResult>> SearchProjectsForMCPAsync(string keywords, TimeCondition timeCondition = null, bool isStatisticalAnalysis = false, Member user = null)
        {
            try
            {
                var projectBLL = new ProjectBLL();
                var searchParams = new System.Collections.Specialized.NameValueCollection();
                
                if (!string.IsNullOrEmpty(keywords))
                {
                    searchParams.Add("Name", keywords);
                }
                if (timeCondition != null)
                {
                    if (timeCondition.StartDate.HasValue)
                    {
                        searchParams.Add("startdate", timeCondition.StartDate.Value.ToString("yyyy-MM-dd"));
                    }
                    if (timeCondition.EndDate.HasValue)
                    {
                        searchParams.Add("enddate", timeCondition.EndDate.Value.ToString("yyyy-MM-dd"));
                    }
                }
                var takeCount = isStatisticalAnalysis ? 30 : 15;
                var count = 0;
                var projects = projectBLL.searchCommon(searchParams, user, 1, takeCount, out count, false, false, true);
                
                if (projects != null)
                {
                    var results = new List<ProjectSearchResult>();
                        
                    foreach (var project in projects.Take(takeCount))
                    {          
                        results.Add(new ProjectSearchResult
                        {
                            Id = Convert.ToInt32(project.Id),
                            Name = project.Name?.ToString() ?? "",
                            Summary = project.Summary?.ToString() ?? "",
                            Content = FormatProjectForMCP(project)
                        });
                    }
                        
                    return results;
                }
            }
            catch (Exception ex)
            {
                Logger.Error("MCP项目搜索失败", ex);
            }
            
            return new List<ProjectSearchResult>();
        }

        /// <summary>
        /// FMS搜索投资组合 - 通过API调用FMS系统
        /// </summary>
        public async Task<List<ProjectSearchResult>> SearchPortfoliosForMCPAsync(string query, Member user = null)
        {
            try
            {
                Logger.Info($"LLM_REQUEST:开始FMS投资组合搜索，查询内容: {query}");
                
                // 调用FMS的API接口
                var fmsBaseUrl = System.Configuration.ConfigurationManager.AppSettings["FMSApiBaseUrl"] ?? "http://localhost:59359";
                var fmsApiUrl = $"{fmsBaseUrl}/AdminApi/SearchPortfoliosForIMS";
                
                using (var httpClient = new HttpClient())
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(30);
                    
                    // 准备请求参数
                    var requestData = new
                    {
                        query = query,
                        userInfo = user != null ? JsonConvert.SerializeObject(user) : ""
                    };
                    
                    var jsonContent = JsonConvert.SerializeObject(requestData);
                    var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                    
                    Logger.Info($"LLM_REQUEST:调用FMS API: {fmsApiUrl}");
                    
                    var response = await httpClient.PostAsync(fmsApiUrl, content);
                    
                    if (response.IsSuccessStatusCode)
                    {
                        var responseContent = await response.Content.ReadAsStringAsync();
                        var apiResult = JsonConvert.DeserializeObject<dynamic>(responseContent);
                        
                        if (apiResult.success == true && apiResult.data != null)
                        {
                            var results = new List<ProjectSearchResult>();
                            var portfolios = apiResult.data;
                            
                            foreach (var portfolio in portfolios)
                            {
                                results.Add(new ProjectSearchResult
                                {
                                    Id = portfolio.Id ?? 0,
                                    Name = portfolio.Name?.ToString() ?? "",
                                    Summary = portfolio.Summary?.ToString() ?? "",
                                    Content = portfolio.Content?.ToString() ?? ""
                                });
                            }
                            
                            Logger.Info($"LLM_REQUEST:FMS投资组合搜索成功，找到 {results.Count} 个投资组合");
                            return results;
                        }
                        else
                        {
                            Logger.Info($"LLM_REQUEST:FMS API返回失败: {apiResult.message}");
                            return new List<ProjectSearchResult>();
                        }
                    }
                    else
                    {
                        Logger.Error($"LLM_REQUEST:FMS API调用失败，状态码: {response.StatusCode}");
                        return new List<ProjectSearchResult>();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"LLM_REQUEST:FMS投资组合搜索失败: {ex.Message}", ex);
                return new List<ProjectSearchResult>();
            }
        }

        /// <summary>
        /// 格式化投资组合信息用于MCP
        /// </summary>
        private string FormatPortfolioForMCP(dynamic portfolio)
        {
            try
            {
                var sb = new StringBuilder();
                
                var portfolioType = portfolio.GetType();
                
                var portfolioID = portfolioType.GetProperty("portfolioID")?.GetValue(portfolio)?.ToString();
                if (!string.IsNullOrEmpty(portfolioID)) sb.AppendLine($"投资组合ID: {portfolioID}");
                
                var officialName = portfolioType.GetProperty("OfficialName")?.GetValue(portfolio)?.ToString();
                if (!string.IsNullOrEmpty(officialName)) sb.AppendLine($"官方名称: {officialName}");
                
                var abbName = portfolioType.GetProperty("abbName")?.GetValue(portfolio)?.ToString();
                if (!string.IsNullOrEmpty(abbName)) sb.AppendLine($"简称: {abbName}");
                
                var ceo = portfolioType.GetProperty("CEO")?.GetValue(portfolio)?.ToString();
                if (!string.IsNullOrEmpty(ceo)) sb.AppendLine($"CEO: {ceo}");
                
                var portfolioManager = portfolioType.GetProperty("portfolioManager")?.GetValue(portfolio)?.ToString();
                if (!string.IsNullOrEmpty(portfolioManager)) sb.AppendLine($"投资经理: {portfolioManager}");
                
                var groupMember = portfolioType.GetProperty("GroupMember")?.GetValue(portfolio)?.ToString();
                if (!string.IsNullOrEmpty(groupMember)) sb.AppendLine($"团队成员: {groupMember}");
                
                var postInvestMember = portfolioType.GetProperty("PostInvestMember")?.GetValue(portfolio)?.ToString();
                if (!string.IsNullOrEmpty(postInvestMember)) sb.AppendLine($"投后管理: {postInvestMember}");
                
                var oneLineDesc = portfolioType.GetProperty("oneLineDesc")?.GetValue(portfolio)?.ToString();
                if (!string.IsNullOrEmpty(oneLineDesc)) sb.AppendLine($"简介: {oneLineDesc}");
                
                var fundName = portfolioType.GetProperty("fundName")?.GetValue(portfolio)?.ToString();
                if (!string.IsNullOrEmpty(fundName)) sb.AppendLine($"基金名称: {fundName}");

                return sb.ToString();
            }
            catch
            {
                return "投资组合信息格式化失败";
            }
        }
        #endregion

        #region 项目分析和文档处理
        /// <summary>
        /// 分析项目（非流式）
        /// </summary>
        public string AnalyseProject(Project project, Member user)
        {
            string userName = user?.RealName ?? "Anonymous";
            
            var str = ProjectBLL.formatProject(project);
            if (str.Length < 150)
            {
                return "信息不足，请填写完项目信息后重试。";
            }
            
            var res = HttpMethods.HttpLLMPost(new { 
                model = "deepseek-r1-0528",
                stream = false,
                messages = new[]
                {
                    new { role = "user", content = str }
                }
            }, user: userName);
            
            if (project.Id != 0) 
            {
                string[] parts = res.Split(new string[] { "</think>" }, StringSplitOptions.None);

                if (parts.Length > 1)
                {
                    string[] thinkParts = parts[0].Split(new string[] { "<think>" }, StringSplitOptions.None);
                    if (thinkParts.Length > 1)
                    {
                        project.ai_reasoning = thinkParts[1];
                    } 
                    else
                    {
                        project.ai_reasoning = thinkParts[0];
                        res = "<think>" + res;
                    }

                    project.ai_question = parts[1];
                }
                project.ai_question_date = DateTime.Now;
                new ProjectBLL().Update(project, "ai_question, ai_reasoning, ai_question_date");
            }
            return new ProjectBLL().MarkdownToHtmlLight(res);
        }

        /// <summary>
        /// LLM提取项目信息
        /// </summary>
        public string LLMExtractProject(Project project, Member user)
        {
            string userName = user?.RealName ?? "Anonymous";
            
            var str = ProjectBLL.formatProject(project);
            if (str.Length < 30)
            {
                return "信息不足，请填写完项目信息后重试。";
            }
            var res = HttpMethods.HttpLLMPost(new { data = str }, user: userName);
            return res;
        }

        /// <summary>
        /// MCP搜索功能
        /// </summary>
        public async Task<List<ProjectSearchResult>> McpSearchAsync(string query, string mcpType, Member user)
        {
            try
            {
                Logger.Info($"LLM_REQUEST:开始MCP搜索，查询内容: {query}, MCP类型: {mcpType} " +user?.RealName ?? "System");
                
                // 根据MCP类型选择不同的搜索逻辑
                if (mcpType == "FMS")
                {
                    // 首先分析用户问题是否需要投资分析
                    var analysisResult = await AnalyzeInvestmentQuestionAsync(query, user);
                    
                    if (analysisResult.NeedsAnalysis)
                    {
                        // 如果需要投资分析，返回分析结果作为搜索结果
                        var results = new List<ProjectSearchResult>();
                        results.Add(new ProjectSearchResult
                        {
                            Id = 0,
                            Name = "投资分析结果",
                            Summary = "基于FMS系统的投资分析",
                            Content = analysisResult.AnalysisResult
                        });
                        
                        Logger.Info($"LLM_REQUEST:FMS投资分析完成，返回分析结果" + user?.RealName ?? "System");
                        return results;
                    }
                    else
                    {
                        // 如果不需要投资分析，搜索投资组合
                        var portfolioResults = await SearchPortfoliosForMCPAsync(query, user);
                        return portfolioResults;
                    }
                }
                else
                {
                    // IMS搜索项目（默认）
                    var mcpResult = await ProcessMCPRequestAsync(query, user);
                    
                    if (mcpResult.ToolCalled && mcpResult.ProjectsFound.Count > 0)
                    {
                        Logger.Info($"LLM_REQUEST:MCP搜索成功，找到 {mcpResult.ProjectsFound.Count} 个项目" + user?.RealName ?? "System");
                        return mcpResult.ProjectsFound;
                    }
                    else
                    {
                        Logger.Info("LLM_REQUEST:MCP搜索未找到相关项目" + user?.RealName ?? "System");
                        return new List<ProjectSearchResult>();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("MCP搜索失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 分析投资问题 - 调用FMS API
        /// </summary>
        private async Task<InvestmentAnalysisResult> AnalyzeInvestmentQuestionAsync(string query, Member user)
        {
            try
            {
                Logger.Info($"LLM_REQUEST:开始分析投资问题: {query}");
                
                // 调用FMS的投资分析API
                var fmsBaseUrl = System.Configuration.ConfigurationManager.AppSettings["FMSApiBaseUrl"] ?? "http://localhost:59359";
                var fmsApiUrl = $"{fmsBaseUrl}/AdminApi/AnalyzeInvestmentQuestion";
                
                using (var httpClient = new HttpClient())
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(30);
                    
                    // 准备请求参数
                    var requestData = new
                    {
                        userPrompt = query,
                        userInfo = user != null ? JsonConvert.SerializeObject(user) : ""
                    };
                    
                    var jsonContent = JsonConvert.SerializeObject(requestData);
                    var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                    
                    Logger.Info($"LLM_REQUEST:调用FMS投资分析API: {fmsApiUrl}");
                    
                    var response = await httpClient.PostAsync(fmsApiUrl, content);
                    
                    if (response.IsSuccessStatusCode)
                    {
                        var responseContent = await response.Content.ReadAsStringAsync();
                        var apiResult = JsonConvert.DeserializeObject<dynamic>(responseContent);
                        
                        if (apiResult.success == true)
                        {
                            var result = new InvestmentAnalysisResult
                            {
                                NeedsAnalysis = apiResult.needsAnalysis ?? false,
                                AnalysisType = apiResult.analysisType?.ToString() ?? "",
                                FundID = apiResult.fundID?.ToString() ?? "",
                                Year = apiResult.year,
                                Quarter = apiResult.quarter,
                                Keywords = apiResult.keywords?.ToString() ?? "",
                                AnalysisResult = apiResult.analysisResult?.ToString() ?? ""
                            };
                            
                            Logger.Info($"LLM_REQUEST:FMS投资分析完成，需要分析: {result.NeedsAnalysis}");
                            return result;
                        }
                        else
                        {
                            Logger.Info($"LLM_REQUEST:FMS投资分析API返回失败: {apiResult.message}");
                            return new InvestmentAnalysisResult { NeedsAnalysis = false };
                        }
                    }
                    else
                    {
                        Logger.Error($"LLM_REQUEST:FMS投资分析API调用失败，状态码: {response.StatusCode}");
                        return new InvestmentAnalysisResult { NeedsAnalysis = false };
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"LLM_REQUEST:FMS投资分析失败: {ex.Message}", ex);
                return new InvestmentAnalysisResult { NeedsAnalysis = false };
            }
        }

        /// <summary>
        /// 投资分析结果
        /// </summary>
        public class InvestmentAnalysisResult
        {
            public bool NeedsAnalysis { get; set; }
            public string AnalysisType { get; set; }
            public string FundID { get; set; }
            public int? Year { get; set; }
            public int? Quarter { get; set; }
            public string Keywords { get; set; }
            public string AnalysisResult { get; set; }
        }
        #endregion

        #region 私有辅助方法
        /// <summary>
        /// 格式化项目信息用于MCP
        /// </summary>
        private string FormatProjectForMCP(dynamic project)
        {
            try
            {
                var sb = new StringBuilder();
                
                var projectType = project.GetType();
                
                var businessData = projectType.GetProperty("BusinessData")?.GetValue(project)?.ToString();
                if (!string.IsNullOrEmpty(businessData)) sb.AppendLine($"商业数据: {businessData}");
                
                var financialData = projectType.GetProperty("FinancialData")?.GetValue(project)?.ToString();
                if (!string.IsNullOrEmpty(financialData)) sb.AppendLine($"财务数据: {financialData}");
                
                var highlight = projectType.GetProperty("HighLight")?.GetValue(project)?.ToString();
                if (!string.IsNullOrEmpty(highlight)) sb.AppendLine($"项目亮点: {highlight}");
                
                var risk = projectType.GetProperty("Risk")?.GetValue(project)?.ToString();
                if (!string.IsNullOrEmpty(risk)) sb.AppendLine($"风险: {risk}");

                var progress = projectType.GetProperty("UpdatedNews")?.GetValue(project)?.ToString();
                if (!string.IsNullOrEmpty(progress)) sb.AppendLine($"新进展: {progress}");

                var date = projectType.GetProperty("PubTime")?.GetValue(project)?.ToString();
                if (!string.IsNullOrEmpty(date)) sb.AppendLine($"日期: {date}");

                return sb.ToString();
            }
            catch
            {
                return "项目信息格式化失败";
            }
        }

        /// <summary>
        /// 解析时间条件（后备方案）
        /// </summary>
        private TimeCondition ParseTimeConditionFallback(string timeRange, Member user)
        {
            if (string.IsNullOrEmpty(timeRange) || timeRange.Trim() == "无")
                return null;
                
            var now = DateTime.Now;
            string userName = user?.RealName ?? "Anonymous";
            
            try
            {
                Logger.Info($"LLM_REQUEST:使用后备方案解析时间条件: {timeRange}");
                
                string prompt = $@"你是一个时间解析专家。请根据用户的时间描述，解析出具体的开始时间和结束时间。

当前时间：{now:yyyy-MM-dd HH:mm:ss}
当前是星期{((int)now.DayOfWeek == 0 ? "日" : ((int)now.DayOfWeek).ToString())}

用户的时间描述：{timeRange}

请根据当前时间，解析出具体的开始时间和结束时间。返回格式必须为JSON，格式如下：
{{
    ""startDate"": ""yyyy-MM-dd"",
    ""endDate"": ""yyyy-MM-dd"",
    ""description"": ""时间描述""
}}

解析规则：
1. 如果是""本周""、""这周""，从本周一到今天（包含今天）
2. 如果是""上周""，从上周一到上周日
3. 如果是""本月""、""这个月""，从本月1号到今天（包含今天）
4. 如果是""上月""、""上个月""，从上个月1号到上个月最后一天
5. 如果是""今年""、""本年""，从今年1月1日到今天（包含今天）
6. 如果是""去年""、""上年""，从去年1月1日到去年12月31日
7. 如果是""最近X天""、""近X天""，从X天前到今天（包含今天）
8. 如果是""最近X个月""，从X个月前的今天到今天（包含今天）
9. 如果是具体年份如""2024年""，从该年1月1日到该年12月31日
10. 如果包含具体日期，请智能解析

请只返回JSON格式，不要包含任何其他内容：";

                var response = HttpMethods.HttpLLMPost(new { 
                    model = "qwen3-30b-a3b-mlx",
                    stream = false,
                    messages = new[]
                    {
                        new { role = "user", content = prompt + "/no_think" }
                    }
                }, user: userName);

                if (string.IsNullOrEmpty(response))
                {
                    Logger.Warn($"LLM_REQUEST:时间解析LLM返回为空: {timeRange}");
                    return null;
                }

                var jsonMatch = Regex.Match(response, @"\{.*\}", RegexOptions.Singleline);
                string jsonStr = jsonMatch.Success ? jsonMatch.Value : response.Trim();

                var timeResult = JsonConvert.DeserializeObject<dynamic>(jsonStr);
                
                var condition = new TimeCondition();
                
                if (DateTime.TryParse(timeResult.startDate?.ToString(), out DateTime startDate))
                {
                    condition.StartDate = startDate.Date;
                }
                else
                {
                    Logger.Warn($"LLM_REQUEST:无法解析开始时间: {timeResult.startDate}");
                    return null;
                }

                if (DateTime.TryParse(timeResult.endDate?.ToString(), out DateTime endDate))
                {
                    condition.EndDate = endDate.Date.AddDays(1);
                }
                else
                {
                    Logger.Warn($"LLM_REQUEST:无法解析结束时间: {timeResult.endDate}");
                    return null;
                }

                condition.Description = timeResult.description?.ToString() ?? timeRange;

                Logger.Info($"LLM_REQUEST:时间解析成功（后备方案） - 输入: {timeRange}, 开始: {condition.StartDate:yyyy-MM-dd}, 结束: {condition.EndDate:yyyy-MM-dd}, 描述: {condition.Description}");
                
                return condition;
            }
            catch (JsonException ex)
            {
                Logger.Error($"LLM_REQUEST:时间解析JSON格式错误: {timeRange}, 响应可能不是有效JSON", ex);
                return null;
            }
            catch (Exception ex)
            {
                Logger.Error($"LLM_REQUEST:解析时间条件失败: {timeRange}", ex);
                return null;
            }
        }

        /// <summary>
        /// 计算最优的原始搜索数量限制
        /// </summary>
        private int CalculateOptimalSearchLimit(int targetLimit)
        {
            if (targetLimit <= 3)
            {
                return Math.Min(targetLimit * 4, 15);
            }
            else if (targetLimit <= 8)
            {
                return Math.Min(targetLimit * 4, 32);
            }
            else if (targetLimit <= 15)
            {
                return Math.Min(targetLimit * 4, 50);
            }
            else if (targetLimit <= 25)
            {
                return Math.Min(targetLimit * 4, 80);
            }
            else if (targetLimit <= 50)
            {
                return Math.Min((int)(targetLimit * 4.0), 250);
            }
            else if (targetLimit <= 100)
            {
                return Math.Min((int)(targetLimit * 5.0), 600);
            }
            else
            {
                return Math.Min((int)(targetLimit * 6.0), 1000);
            }
        }

        /// <summary>
        /// 计算最大搜索页数
        /// </summary>
        private int CalculateMaxPages(int limit)
        {
            if (limit <= 8)
                return 2;
            else if (limit <= 25)
                return 3;
            else if (limit <= 50)
                return 4;
            else if (limit <= 100)
                return 6;
            else
                return 8;
        }
        
        /// <summary>
        /// 计算每页结果数
        /// </summary>
        private int CalculateResultsPerPage(int rawSearchLimit, int maxPages, int queryCount)
        {
            int theoreticalPerPage = Math.Max(1, rawSearchLimit / (maxPages * queryCount));
            int minPerPage = 10;
            int maxPerPage = 100;
            
            if (rawSearchLimit >= 200)
            {
                return Math.Min(maxPerPage, Math.Max(50, theoreticalPerPage));
            }
            else if (rawSearchLimit >= 100)
            {
                return Math.Min(maxPerPage, Math.Max(30, theoreticalPerPage));
            }
            else
            {
                return Math.Min(maxPerPage, Math.Max(minPerPage, theoreticalPerPage));
            }
        }
        
        /// <summary>
        /// 获取搜索模式描述
        /// </summary>
        private string GetSearchModeDescription(int limit)
        {
            if (limit <= 8) return "标准";
            else if (limit <= 25) return "详细";
            else if (limit <= 50) return "广泛";
            else if (limit <= 100) return "海量";
            else return "超大容量";
        }

        /// <summary>
        /// 生成搜索查询
        /// </summary>
        private List<string> GenerateSearchQueries(string originalQuery)
        {
            var queries = new List<string>();
            
            string optimizedQuery = OptimizeSearchQuery(originalQuery);
            queries.Add(optimizedQuery);
            
            if (ContainsBusinessTerms(originalQuery))
            {
                queries.Add($"{optimizedQuery} 商业 企业 公司");
            }
            
            if (ContainsTechTerms(originalQuery))
            {
                queries.Add($"{optimizedQuery} 技术 科技 创新");
            }
            
            if (ContainsFinanceTerms(originalQuery))
            {
                queries.Add($"{optimizedQuery} 金融 投资 财务");
            }
            
            if (optimizedQuery.Length < 10)
            {
                queries.Add($"{optimizedQuery} 2024 最新");
            }
            
            return queries.Distinct().ToList();
        }

        private bool ContainsBusinessTerms(string query)
        {
            string[] businessTerms = { "公司", "企业", "商业", "市场", "营销", "管理", "战略", "品牌", "业务", "运营", "客户", "销售" };
            return businessTerms.Any(term => query.Contains(term));
        }

        private bool ContainsTechTerms(string query)
        {
            string[] techTerms = { "技术", "科技", "AI", "人工智能", "软件", "硬件", "互联网", "数字化", "创新", "研发", "算法", "数据" };
            return techTerms.Any(term => query.Contains(term));
        }

        private bool ContainsFinanceTerms(string query)
        {
            string[] financeTerms = { "投资", "金融", "财务", "资金", "融资", "股票", "基金", "银行", "保险", "财经", "经济", "市值" };
            return financeTerms.Any(term => query.Contains(term));
        }

        /// <summary>
        /// 过滤和排序搜索结果
        /// </summary>
        private List<WebSearchResult> FilterAndRankResults(List<WebSearchResult> allResults, string originalQuery, int limit)
        {
            if (!allResults.Any()) return allResults;
            
            var scoredResults = allResults.Select(result => new
            {
                Result = result,
                Score = CalculateRelevanceScore(result, originalQuery)
            }).ToList();
            
            int originalCount = allResults.Count;
            Logger.Info($"搜索过滤前: {originalCount} 条结果");
            
            double minRelevanceScore = CalculateFilterThreshold(originalCount, limit);
            var filteredResults = scoredResults.Where(x => x.Score >= minRelevanceScore).ToList();
            
            var filteredOutResults = scoredResults.Where(x => x.Score < minRelevanceScore).ToList();
            if (filteredOutResults.Any())
            {
                Logger.Info($"过滤掉 {filteredOutResults.Count} 条低相关性结果 (阈值: {minRelevanceScore:F1}分):");
                foreach (var filtered in filteredOutResults.Take(3))
                {
                    Logger.Info($"  - [{filtered.Score:F1}分] {filtered.Result.Title}");
                }
            }
            
            int minRequiredResults = limit >= 100 ? Math.Min(50, limit) : Math.Min(2, limit);
            
            if (filteredResults.Count < minRequiredResults && scoredResults.Any())
            {
                double fallbackScore = limit >= 100 ? -0.5 : Math.Max(0.1, minRelevanceScore - 1.5);
                filteredResults = scoredResults.Where(x => x.Score >= fallbackScore).ToList();
                Logger.Info($"使用更宽松备用阈值({fallbackScore:F1})，保留 {filteredResults.Count} 条结果");
                
                if (filteredResults.Count < minRequiredResults && scoredResults.Any())
                {
                    double emergencyScore = limit >= 100 ? -2.0 : 0.0;
                    filteredResults = scoredResults.Where(x => x.Score >= emergencyScore).ToList();
                    Logger.Info($"使用紧急阈值({emergencyScore:F1})，保留 {filteredResults.Count} 条结果");
                    
                    if (filteredResults.Count < minRequiredResults && limit >= 100 && scoredResults.Any())
                    {
                        filteredResults = scoredResults.ToList();
                        Logger.Info($"使用最宽松策略，保留所有 {filteredResults.Count} 条结果");
                    }
                }
            }
            
            Logger.Info($"搜索过滤后: {filteredResults.Count} 条结果");
            
            var finalResults = filteredResults
                .OrderByDescending(x => x.Score)
                .Take(limit)
                .ToList();
            
            if (finalResults.Any())
            {
                var returnedResults = finalResults.Select(x => x.Result).ToList();
                var enhancedInFinal = returnedResults.Count(r => r.IsContentEnhanced);
                
                Logger.Info($"最终返回 {finalResults.Count} 条结果，其中 {enhancedInFinal} 条已增强内容:");
                foreach (var result in finalResults.Take(3))
                {
                    var enhancedMark = result.Result.IsContentEnhanced ? " [已增强]" : "";
                    Logger.Info($"  + [{result.Score:F1}分] {result.Result.Title}{enhancedMark}");
                }
                return returnedResults;
            }
            
            return new List<WebSearchResult>();
        }

        /// <summary>
        /// 计算相关性分数
        /// </summary>
        private double CalculateRelevanceScore(WebSearchResult result, string originalQuery)
        {
            double score = 0;
            
            var queryWords = ExtractKeywords(originalQuery);
            var titleWords = result.Title.ToLower();
            var contentWords = result.Content.ToLower();
            
            bool hasMainKeyword = false;
            int keywordMatchCount = 0;
            
            foreach (var word in queryWords.Where(w => w.Length > 1))
            {
                var lowerWord = word.ToLower();
                bool titleMatch = titleWords.Contains(lowerWord);
                bool contentMatch = contentWords.Contains(lowerWord);
                
                if (!titleMatch && !contentMatch)
                {
                    if (word.Length > 2)
                    {
                        string partialWord = word.Substring(0, Math.Min(word.Length - 1, 3));
                        titleMatch = titleWords.Contains(partialWord);
                        contentMatch = contentWords.Contains(partialWord);
                    }
                    
                    if (!titleMatch && !contentMatch)
                    {
                        titleMatch = titleWords.Split(' ').Any(t => t.Contains(lowerWord) || lowerWord.Contains(t));
                        contentMatch = contentWords.Split(' ').Any(c => c.Contains(lowerWord) || lowerWord.Contains(c));
                    }
                }
                
                if (titleMatch || contentMatch)
                {
                    keywordMatchCount++;
                    if (titleMatch) 
                    {
                        score += 2;
                        hasMainKeyword = true;
                    }
                    if (contentMatch) score += 1;
                    
                    if (IsExactWordMatch(titleWords, lowerWord) || IsExactWordMatch(contentWords, lowerWord))
                    {
                        score += 1;
                    }
                }
            }
            
            if (!hasMainKeyword)
            {
                score -= 2;
            }
            
            if (queryWords.Length > 0)
            {
                double coverageRate = (double)keywordMatchCount / queryWords.Length;
                score += coverageRate * 2;
            }
            
            score += CalculateTitleRelevance(result.Title, originalQuery);
            score += EvaluateContentQuality(result.Content);
            
            if (result.Title.Length >= 10 && result.Title.Length <= 100) score += 0.5;
            
            if (IsReliableSource(result.Url)) score += 1;
            
            if (IsIrrelevantContent(result, originalQuery))
            {
                score -= 3;
            }
            
            return Math.Max(0, score);
        }

        /// <summary>
        /// 提取查询中的关键词
        /// </summary>
        private string[] ExtractKeywords(string query)
        {
            string optimizedQuery = OptimizeSearchQuery(query);
            var words = optimizedQuery.Split(new char[] { ' ', '，', ',', '、' }, StringSplitOptions.RemoveEmptyEntries);
            // 过滤掉过短和常见无意义词汇
            string[] stopWords = { "的", "了", "是", "在", "有", "和", "与", "或", "及", "等", "如何", "什么", "怎么", "为什么" };
            return words.Where(w => w.Length > 0 && !stopWords.Contains(w)).ToArray();
        }
        /// <summary>
        /// 检查是否是完整词匹配
        /// </summary>
        private bool IsExactWordMatch(string text, string word)
        {
            // 使用正则表达式确保是完整词匹配，而不是部分匹配
            var pattern = @"\b" + Regex.Escape(word) + @"\b";
            return Regex.IsMatch(text, pattern, RegexOptions.IgnoreCase);
        }
        /// <summary>
        /// 计算标题相关性
        /// </summary>
        private double CalculateTitleRelevance(string title, string query)
        {
            if (string.IsNullOrEmpty(title) || string.IsNullOrEmpty(query)) return 0;
            
            var queryKeywords = ExtractKeywords(query);
            var titleLower = title.ToLower();
            
            double relevanceScore = 0;
            // 检查关键词在标题中的位置权重（越靠前权重越高）
            foreach (var keyword in queryKeywords)
            {
                int index = titleLower.IndexOf(keyword.ToLower());
                if (index >= 0)
                {
                    // 位置权重：标题前面的词权重更高
                    double positionWeight = Math.Max(0.1, 1.0 - (double)index / title.Length);
                    relevanceScore += positionWeight;
                }
            }
            
            return relevanceScore;
        }
        /// <summary>
        /// 评估内容质量
        /// </summary>
        private double EvaluateContentQuality(string content)
        {
            if (string.IsNullOrEmpty(content)) return 0;
            
            double qualityScore = 0;
            // 内容长度合理性
            if (content.Length > 50 && content.Length < 1000) qualityScore += 0.5;
            else if (content.Length >= 1000) qualityScore += 1;
            // 内容结构性（包含标点符号，说明是结构化内容）
            if (content.Contains("。") || content.Contains(".") || content.Contains("，") || content.Contains(","))
            {
                qualityScore += 0.5;
            }
            
            return qualityScore;
        }

        private bool IsIrrelevantContent(WebSearchResult result, string originalQuery)
        {
            // 简化的不相关内容检测逻辑
            return false;
        }

        private bool IsReliableSource(string url)
        {
            if (string.IsNullOrEmpty(url)) return false;
            
            string[] reliableDomains = { 
                "gov.cn", "edu.cn", "wikipedia", "baidu.com", "zhihu.com", 
                "36kr.com", "techcrunch.com", "reuters.com", "bbc.com", 
                "cnn.com", "forbes.com", "bloomberg.com", "wsj.com",
                "163.com", "sina.com", "sohu.com", "qq.com", "people.com.cn"
            };
            
            return reliableDomains.Any(domain => url.ToLower().Contains(domain));
        }

        private double CalculateFilterThreshold(int originalCount, int targetLimit)
        {
            double baseThreshold = 0.3;
            double ratio = (double)originalCount / targetLimit;
            
            if (targetLimit >= 100)
            {
                if (ratio >= 2.5)
                {
                    return 0.4;
                }
                else if (ratio >= 1.5)
                {
                    return 0.1;
                }
                else
                {
                    return -1.0;
                }
            }
            else if (targetLimit >= 50)
            {
                if (ratio >= 3.0)
                {
                    return 0.6;
                }
                else if (ratio >= 2.0)
                {
                    return 0.3;
                }
                else if (ratio >= 1.5)
                {
                    return 0.1;
                }
                else
                {
                    return -0.5;
                }
            }
            else if (ratio >= 3.0)
            {
                return 1.2;
            }
            else if (ratio >= 2.0)
            {
                return 0.8;
            }
            else if (ratio >= 1.5)
            {
                return 0.5;
            }
            else
            {
                return 0.1;
            }
        }

        /// <summary>
        /// 增强搜索结果内容
        /// </summary>
        private async Task EnhanceResultsWithWebContent(List<WebSearchResult> results, HttpClient httpClient)
        {
            const int MIN_CONTENT_LENGTH = 150;
            int MAX_CRAWL_COUNT = Math.Min(Math.Max(3, results.Count / 8), results.Count >= 80 ? 25 : 15);
            const int MAX_CONTENT_LENGTH = 2000;
            
            var crawlTasks = new List<Task>();
            int crawlCount = 0;
            
            foreach (var result in results)
            {
                if ((string.IsNullOrEmpty(result.Content) || result.Content.Length < MIN_CONTENT_LENGTH) 
                    && crawlCount < MAX_CRAWL_COUNT 
                    && IsValidUrlForCrawling(result.Url))
                {
                    crawlCount++;
                    var crawlTask = CrawlWebPageContent(result, httpClient, MAX_CONTENT_LENGTH);
                    crawlTasks.Add(crawlTask);
                }
            }
            
            if (crawlTasks.Any())
            {
                try
                {
                    Logger.Info($"开始爬取 {crawlTasks.Count} 个网页的完整内容 (共{results.Count}条结果)");
                    await Task.WhenAll(crawlTasks);
                    
                    int successCount = results.Count(r => r.IsContentEnhanced);
                    Logger.Info($"网页内容爬取完成，成功增强 {successCount} 条搜索结果");
                }
                catch (Exception ex)
                {
                    Logger.Error("Web content crawling failed", ex);
                }
            }
        }

        /// <summary>
        /// 爬取网页内容
        /// </summary>
        private async Task CrawlWebPageContent(WebSearchResult result, HttpClient httpClient, int maxLength)
        {
            try
            {
                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10)))
                {
                    Logger.Info($"开始爬取网页内容: {result.Url}");
                    string htmlContent = await httpClient.GetStringAsync(result.Url);
                    
                    if (!string.IsNullOrEmpty(htmlContent))
                    {
                        string textContent = ExtractTextFromHtml(htmlContent);
                        
                        if (!string.IsNullOrEmpty(textContent))
                        {
                            int originalLength = textContent.Length;
                            
                            if (textContent.Length > maxLength)
                            {
                                textContent = textContent.Substring(0, maxLength) + "...";
                            }
                            
                            result.Content = textContent;
                            result.IsContentEnhanced = true;
                            
                            Logger.Info($"✓ 成功爬取并增强内容: {result.Url}, 提取文本长度: {originalLength} 字符，已标记为增强");
                        }
                        else
                        {
                            Logger.Info($"网页 {result.Url} 未提取到有效文本内容");
                        }
                    }
                    else
                    {
                        Logger.Info($"网页 {result.Url} 返回空内容");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"Failed to crawl content from {result.Url}", ex);
            }
        }

        /// <summary>
        /// 从HTML中提取文本
        /// </summary>
        private string ExtractTextFromHtml(string html)
        {
            if (string.IsNullOrEmpty(html)) return string.Empty;
            
            try
            {
                html = Regex.Replace(html, @"<script.*?</script>", "", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                html = Regex.Replace(html, @"<style.*?</style>", "", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                html = Regex.Replace(html, @"<[^>]+>", " ");
                html = WebUtility.HtmlDecode(html);
                html = Regex.Replace(html, @"\s+", " ");
                html = html.Trim();
                
                return html;
            }
            catch (Exception ex)
            {
                Logger.Error("HTML text extraction failed", ex);
                return string.Empty;
            }
        }

        /// <summary>
        /// 检查URL是否适合爬取
        /// </summary>
        private bool IsValidUrlForCrawling(string url)
        {
            if (string.IsNullOrEmpty(url)) return false;
            
            try
            {
                var uri = new Uri(url);
                
                if (uri.Scheme != "http" && uri.Scheme != "https") return false;
                
                string[] excludeExtensions = { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".zip", ".rar", ".exe", ".dmg" };
                string path = uri.AbsolutePath.ToLower();
                
                if (excludeExtensions.Any(ext => path.EndsWith(ext))) return false;
                
                string[] excludeDomains = { "youtube.com", "youtu.be", "bilibili.com", "tiktok.com", "instagram.com", "twitter.com", "facebook.com" };
                string host = uri.Host.ToLower();
                
                if (excludeDomains.Any(domain => host.Contains(domain))) return false;
                
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 优化搜索查询
        /// </summary>
        private string OptimizeSearchQuery(string originalQuery)
        {
            if (string.IsNullOrWhiteSpace(originalQuery))
                return originalQuery;

            string query = originalQuery.Trim();
            // 移除常见的问句开头词汇，保留核心关键词
            string[] questionStarters = { "请问", "你知道", "能告诉我", "我想了解", "什么是", "如何", "怎么", "为什么", "哪个", "哪些", "帮我", "给我", "查一下", "搜索" };
            
            foreach (var starter in questionStarters)
            {
                if (query.StartsWith(starter))
                {
                    query = query.Substring(starter.Length).Trim();
                    break;
                }
            }
            // 移除问号、句号和其他标点符号
            query = query.TrimEnd('？', '?', '。', '.', '！', '!', '：', ':', '；', ';');
            // 移除语气词和无关词汇
            string[] fillerWords = { "的话", "一下", "呢", "吧", "啊", "呀", "嘛", "吗", "么", "呗", "哈", "了", "吧", "呢", "请", "谢谢" };
            foreach (var filler in fillerWords)
            {
                query = query.Replace(filler, " ").Trim();
            }
            // 如果查询太短，尝试从原始查询中提取关键词
            if (query.Length < 2)
            {// 从原始查询中提取关键词（去除常见的无意义词汇）
                var words = originalQuery.Split(new char[] { ' ', '，', ',', '、', '的', '了', '是', '在', '有', '和', '与', '或', '及' }, StringSplitOptions.RemoveEmptyEntries);
                var meaningfulWords = words.Where(w => w.Length > 1 && !questionStarters.Contains(w) && !fillerWords.Contains(w));
                if (meaningfulWords.Any())
                {
                    query = string.Join(" ", meaningfulWords);
                }
                else
                {
                    return originalQuery;
                }
            }
            // 清理多余的空格
            query = Regex.Replace(query, @"\s+", " ").Trim();
                
            return query;
        }
        #endregion

      

        /// <summary>
        /// 示例：在用户画像分析中使用Embedding服务
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>分析结果</returns>
        public async Task<AjaxResult> AnalyzeUserProfileWithEmbedding(int userId)
        {
            try
            {
                Logger.Info($"LLM_REQUEST:开始分析用户画像并使用Embedding服务，用户ID: {userId}");
                
                var userProfileBLL = new UserProfileBLL();
                
                // 1. 分析用户画像
                var profile = await userProfileBLL.AnalyzeUserProfileAsync(userId);
                
                if (profile == null)
                {
                    Logger.Error($"LLM_REQUEST:用户画像分析失败，用户ID: {userId}");
                    return new AjaxResult
                    {
                        code = (int)ResultCode.notdata,
                        msg = "用户画像分析失败"
                    };
                }
                
                Logger.Info($"LLM_REQUEST:用户画像分析成功，开始生成兴趣向量");
                
                // 2. 获取用户兴趣向量（现在基于标签级别向量化）
                var userVector = await userProfileBLL.GetUserVectorAsync(userId);
                
                if (userVector != null)
                {
                    Logger.Info($"LLM_REQUEST:用户兴趣向量生成成功，维度: {userVector.Length}");
                    
                    // 获取用户标签关联
                    var userTagRelations = await userProfileBLL.GetUserTagRelationsAsync(userId);
                    
                    return new AjaxResult
                    {
                        code = (int)ResultCode.success,
                        msg = "用户画像分析和向量生成成功",
                        data = new
                        {
                            UserId = userId,
                            UserName = profile.UserName,
                            InterestDescription = profile.InterestDescription,
                            ProjectCount = profile.ProjectCount,
                            VectorDimension = userVector.Length,
                            VectorPreview = userVector.Take(5).ToArray(),
                            TopTags = userTagRelations.Take(5).Select(r => new
                            {
                                TagId = r.TagId,
                                Weight = r.Weight,
                                ClickCount = r.ClickCount
                            }).ToList()
                        }
                    };
                }
                else
                {
                    Logger.Info($"LLM_REQUEST:用户画像分析完成，但未生成向量");
                    
                    return new AjaxResult
                    {
                        code = (int)ResultCode.success,
                        msg = "用户画像分析成功",
                        data = new
                        {
                            UserId = userId,
                            UserName = profile.UserName,
                            InterestDescription = profile.InterestDescription,
                            ProjectCount = profile.ProjectCount
                        }
                    };
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"LLM_REQUEST:用户画像分析失败，用户ID: {userId}", ex);
                return new AjaxResult
                {
                    code = (int)ResultCode.exception,
                    msg = $"用户画像分析失败: {ex.Message}"
                };
            }
        }

    }
} 