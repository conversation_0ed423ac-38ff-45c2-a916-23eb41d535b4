﻿using Banyan.Apps;
using Banyan.Code;
using Banyan.Domain;
using log4net;
using System;
using System.Web.Mvc;
using Banyan.Web.Filters;
using Banyan.Code.Azure;
using System.Text;
using System.Threading.Tasks;

namespace Banyan.Web.Controllers
{
    [WechatAuthFilter]
    public class ApiController : FBaseController
    {
        private static readonly ILog log = LogManager.GetLogger("apicontroller");
        //private RpcClient rpcClient = new RpcClient();
        /// <summary>
        /// 用户登录
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        public ActionResult Logon()
        {
            var ajaxResult = memberBll.Logon(Request.Params);
            return Json(ajaxResult);
        }


        /// <summary>
        /// 授权验证
        /// </summary>
        /// <returns></returns>
        /// 
        [AllowAnonymous]
        [HttpPost]
        public ActionResult AuthVerify()
        {
            
            log.Info("Authverify called");
            var ajaxResult = memberBll.AuthVerify(Request.Params);
            return Json(ajaxResult);
        }

        /// <summary>
        /// 授权信息更新
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        public ActionResult AuthMaterialSet()
        {
            var ajaxResult = memberBll.AuthMaterial(Request.Params);
            return Json(ajaxResult);
        }
        [AllowAnonymous]
        [HttpPost]
        public ActionResult MobileExist()
        {
            string mobile = WebHelper.GetValue("mobile", string.Empty, Request.Params);
            var ajaxResult = Ldap.mobileExist(mobile);
            return Json(ajaxResult);
        }

        //[HttpPost]
        //public ActionResult UpdateAvatar()
        //{
        //    string avatar = WebHelper.GetValue("avatar", string.Empty, Request.Params);
        //    var ajaxResult = memberBll.UpdateAvatar(avatar);
        //    return Json(ajaxResult);
        //}

        [HttpPost]
        [AllowAnonymous]
        public string UploadAvatarImgReplace()
        {
            var request = System.Web.HttpContext.Current.Request;
            Member user = new MemberBLL().GetLogOnUser(Int32.Parse(request.Form["uid"]));
            //string directoryBase = Server.MapPath($"/content/attached/avatars/");
            var filePath = DateTime.Now.ToString("yyyyMMddHHmmssffff") + request.Form["filename"];
            filePath = filePath.Replace("+", "加").Replace("/", "_").Replace("\\", "_");
            var file = System.Web.HttpContext.Current.Request.Files[0];
            AzureBlob azureBlob = new AzureBlob(true);
            var res = azureBlob.UploadToBlobCommon(filePath, "avatars", file);
            //file.SaveAs(directoryBase + filePath);
            //var res = System.Configuration.ConfigurationManager.AppSettings["FileDomain"].ToString() + "/content/attached/avatars/" + filePath;
            Ldap.ReplaceAttr(user.RealName, "labeledURI", res);
            return res;
        }

        [HttpPost]
        [AllowAnonymous]
        public string UploadAvatarImg()
        {
            var request = System.Web.HttpContext.Current.Request;
            //string directoryBase = Server.MapPath($"/content/attached/avatars/");
            var filePath = DateTime.Now.ToString("yyyyMMddHHmmssffff") + request.Form["filename"];
            filePath = filePath.Replace("+", "加").Replace("/", "_").Replace("\\", "_");
            var file = System.Web.HttpContext.Current.Request.Files[0];
            AzureBlob azureBlob = new AzureBlob(true);
            var res = azureBlob.UploadToBlobCommon(filePath, "avatars", file);
            return res;
        }
        /// <summary>
        /// 发送短信验证码
        /// </summary>
        /// <returns></returns>
        //[HttpPost]
        [AllowAnonymous]
        public JsonResult SendSmsCode()
        {
            var ajaxResult = memberBll.SendSmsCode(Request.Params);
            return Json(ajaxResult);
        }

        /// <summary>
        /// 项目组列表
        /// </summary>
        /// <returns></returns>
        public JsonResult GetGroups()
        {
            var ajaxResult = new RoleBLL().GetGroups(Request.Params);
            return Json(ajaxResult);
        }

        public JsonResult feedback(string subject, string content)
        {
            Member user = new MemberBLL().GetLogOnUser();
            Logger.Info($"send feedback: {subject} \n content: {content}", user.RealName);
            Mail.botSendMailToAdmin(user.RealName +":" + subject, content);
            return Json(1);
        }

        /// <summary>
        /// 创建人列表
        /// </summary>
        /// <returns></returns>
        public JsonResult GetCreators(int all = 0)
        {
            var ajaxResult = new MemberBLL().GetCreators(Request.Params);
            return Json(ajaxResult);
        }

        public JsonResult GetCreatorsLimitPartner(int all = 0)
        {
            var ajaxResult = new MemberBLL().GetCreatorsLimitPartner(Request.Params);
            return Json(ajaxResult);
        }
        /// <summary>
        /// 员工列表
        /// </summary>
        /// <returns></returns>
        public JsonResult GetStaffs(int all = 0)
        {
            if (all > 0)
            {
                AjaxResult reuslt = new AjaxResult();
                reuslt.data = new MemberBLL().GetStaffList();
                reuslt.code = 0;
                return Json(reuslt);
            }
            var ajaxResult = new MemberBLL().GetStaffs(Request.Params);
            return Json(ajaxResult);
        }
        /// <summary>
        /// 员工Summary
        /// </summary>
        /// <returns></returns>
        public JsonResult GetProjectSummary()
        {
            var ajaxResult = new MemberBLL().GetPersonalCenterSummary(Request.Params, true);
            return Json(ajaxResult);
        }
        /// <summary>
        /// 访谈记录列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult GetArticles()
        {
            var ajaxResult = articleBll.GetArticles(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult PortfolioListLimited()
        {
            var ajaxResult = new PortfolioBLL().GetPageListLimited(Request.Params, false);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult PortfolioListInvested()
        {
            var ajaxResult = new PortfolioBLL().GetInvestedList(Request.Params);
            return Json(ajaxResult);
        }
        /// <summary>
        /// 访谈记录详情
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult GetArticle()
        {
            var ajaxResult = articleBll.GetArticle(Request.Params);
            return Json(ajaxResult);
        }

        /// <summary>
        /// 访谈记录字段更新
        /// </summary>
        /// <returns></returns>s
        [HttpPost]
        public JsonResult ArticleFieldSet()
        {
            var ajaxResult = articleBll.FieldSet(Request.Params, loginUser);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult GetVisitHistory()
        {
            var ajaxResult = new SysLogBLL().ProjectHistory();
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult LogFileView(string filename, string projectid, string urls)
        {
            var ajaxResult = new SysLogBLL().LogFileView(filename, projectid, urls);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult getFileViewLog()
        {
            var ajaxResult = new SysLogBLL().GetFileViewLog();
            return Json(ajaxResult);
        }
        /// <summary>
        /// 项目约见列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult GetProjects()
        {
            var ajaxResult = projectBll.GetProjects(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult GetNews()
        {
            var ajaxResult = new NewsBLL().GetNews(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult GetReports()
        {
            var ajaxResult = new ReportsBLL().GetReports(Request.Params);
            return Json(ajaxResult);
        }
        // 个人中心页返回特定类型项目
        [HttpPost]
        public JsonResult GetTypeProjects()
        {
            var ajaxResult = projectBll.GetTypeProjects(Request.Params);
            return Json(ajaxResult);
        }

        //[HttpPost]
        //public JsonResult GetProjectsMilvus()
        //{
        //    string content = WebHelper.GetValue("keywords", string.Empty, Request.Params);
        //    AjaxResult ajaxResult = new AjaxResult();
        //    ajaxResult.data = rpcClient.search(content);
        //    ajaxResult.code = (int)ResultCode.success;
        //    return Json(ajaxResult);
        //}
        public JsonResult GetRevisitRight()
        {
            var ajaxResult = new ProjectBLL().GetRevisitRightMobile(Request.Params);
            return Json(ajaxResult);
        }

        /// <summary>
        /// 项目约见列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult GetMeets()
        {
            var ajaxResult = meetBll.GetMeets(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult GetFunds()
        {
            var ajaxResult = new FundBasicInfoBLL().GetList();
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult UpdateFunds(int id, string fundList)
        {
            var ajaxResult = projectBll.UpdateFunds(id, fundList);
            return Json(ajaxResult);
        }
        /// <summary>
        /// 项目约见详情
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult GetProject()
        {
            var ajaxResult = projectBll.GetProject(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult UpdateSummaryViews()
        {
            var ajaxResult = projectBll.UpdateSummaryViews(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult GetProjectCompareList()
        {
            var ajaxResult = projectBll.GetProjectCompareList(Request.Params);
            return Json(ajaxResult);
        }
        /// <summary>
        /// 项目约见详情
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult GetMeet()
        {
            var ajaxResult = meetBll.GetMeet(Request.Params);
            return Json(ajaxResult);
        }

        /// <summary>
        /// 会议纪要
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult GetProjectMemos()
        {
            var ajaxResult = new ProjectMemoBLL().Getmemos(Request.Params);
            return Json(ajaxResult);
        }

        /// <summary>
        /// 项目约见字段更新
        /// </summary>
        /// <returns></returns>s
        [HttpPost]
        public JsonResult ProjectFieldSet()
        {
            var ajaxResult = projectBll.FieldSet(Request.Params, loginUser);
            return Json(ajaxResult);
        }

        /// <summary>
        /// 项目评分列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult GetScores()
        {
            try
            {
                int projectId = Utils.GetRequestInt("pjid", 0);
                Logger.Info($"get project id {projectId} scores", loginUser.RealName);
            }
            catch (Exception e) { }
            return Json(new ScoreService().GetScores());
        }
        [HttpPost]
        public JsonResult PortfolioExitDocs(int id = 0)
        {
            var ajaxResult = new AttachmentFMSBLL().GetPortfolioExitDocs(id, true);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult GetExitScores()
        {
            try
            {
                int exitId = Utils.GetRequestInt("pjid", 0);
                Logger.Info($"get exit id {exitId} scores", loginUser.RealName);
            }
            catch (Exception e) { }
            return Json(new ScoreService().GetExitScores());
        }

        public JsonResult GetGroupManager()
        {
            return Json(memberBll.getGroupManager(Request.Params));
        }

        /// <summary>
        /// 保存项目信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateInput(false)]
        public JsonResult ProjectSave(Project model, string contributions)
        {
            var ajaxResult = projectBll.Save(model, "", contributions, " mobile: ");
            return Json(ajaxResult);
        }

        #region ims score
        public JsonResult ProlongScore(int stageid = 0)
        {
            return Json(new ScoreService().ProlongScore(loginUser, stageid));
        }
      
        /// <summary>
        /// 添加评分
        /// </summary>
        /// <param name="pjid"></param>
        /// <param name="score"></param>
        /// <returns></returns>
        public JsonResult ScoreSet(int pjid = 0, int score = 0)
        {
            return Json(new ScoreService().ScoreSet(loginUser, pjid, score));
        }

        /// <summary>
        /// 最新活动评分
        /// </summary>
        /// <param name="pjid"></param>
        /// <returns></returns>
        public JsonResult GetRecentlyStage(int pjid = 0)
        {
            return Json(new ScoreService().GetRecentlyStage(loginUser, pjid));
        }

        /// <summary>
        /// 所有活动评分
        /// </summary>
        /// <param name="pjid"></param>
        /// <returns></returns>
        public JsonResult GetStageList(int pjid = 0)
        {
            return Json(new ScoreService().GetStageList(loginUser, pjid));
        }
        public JsonResult SetScoreStage(int pjid = 0, int state = 0, string description="", bool syncRecentScore = false)
        {
            return Json(new ScoreService().SetScoreStage(loginUser, pjid, state, description, syncRecentScore));
        }
        #endregion

        #region exit score
 
        public JsonResult ExitScoreSet(int pjid = 0, int score = 0)
        {
            return Json(new ScoreService().ExitScoreSet(loginUser, pjid, score));
        }
         
        public JsonResult GetRecentlyExitStage(int pjid = 0)
        {
            return Json(new ScoreService().GetRecentlyExitStage(loginUser, pjid));
        }

        public JsonResult GetExitStageList(int pjid = 0)
        {
            return Json(new ScoreService().GetExitStageList(loginUser, pjid));
        }
        public JsonResult SetExitScoreStage(int pjid = 0, int state = 0)
        {
            return Json(new ScoreService().SetExitScoreStage(loginUser, pjid, state));
        }
        #endregion

        public JsonResult portfolioExitList()
        {
            var ajaxResult1 = new PortfolioExitBLL().GetPageList(Request.Params);
            return Json(ajaxResult1);
        }
        public JsonResult portfolioExitDetail()
        {
            var ajaxResult1 = new PortfolioExitBLL().GetDetail(Request.Params);
            return Json(ajaxResult1);
        }
        #region contribution
        [HttpPost]
        public JsonResult contributionManagerConfirm(Project model)
        {
            var ajaxResult = new ProjectBLL().contributionManagerConfirm(model);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult contributionPartnerConfirm(Project model)
        {
            var ajaxResult = new ProjectBLL().contributionPartnerConfirm(model);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult AddContribution(Contribution model)
        {
            var ajaxResult = new ContributionBLL().Save(model);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult DelContribution(int id = 0)
        {
            var ajaxResult = new ContributionBLL().Delete(id);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult GetContributions(int projectID = 0)
        {
            var ajaxResult = new ContributionBLL().GetContributions(projectID);
            return Json(ajaxResult);
        }
        #endregion
        /// <summary>
        /// 保存访谈信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateInput(false)]
        public JsonResult ArticleSave(Article model)
        {
            var ajaxResult = articleBll.Save(model, false);
            return Json(ajaxResult);
        }

        /// <summary>
        /// 用户信息更新
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        //[HttpPost]
        //public JsonResult MemberSet(Member model)
        //{
        //    var ajaxResult = memberBll.MemberSet(model);
        //    return Json(ajaxResult);
        //}

        /// <summary>
        /// 文章评论列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult GetComments()
        {
            var ajaxResult = new CommentBLL().GetComments(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult ProjectRelatedNews()
        {
            var ajaxResult = new NewsBLL().ProjectedRelatedNews(Request.Params);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult GetDocs(int id = 0)
        {
            var ajaxResult = new ProjectBLL().GetProjectDocs(id, true);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult GetMeetDocs(int id = 0)
        {
            var ajaxResult = new MeetBLL().GetMeetDocs(id, true);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult MeetAttaches(int id = 0)
        {
            var ajaxResult = new MeetBLL().GetMeetAttaches(id);
            return Json(ajaxResult);
        }


        /// <summary>
        /// 研究报告详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult ResearchDetail(int id = 0, int init = 0)
        {
            var ajaxResult = new ResearchBLL().GetDetail(id, init > 0);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult GetDocImage(int id = 0)
        {
            return Json(new ResearchBLL().GetAttach(id));
        }

        /// <summary>
        /// 添加评论
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult CommentSet()
        {
            var ajaxResult = new CommentBLL().CommentSet(Request.Params);
            return Json(ajaxResult);
        }
      
        /// <summary>
        /// 添加取消收藏
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult CollectSet()
        {
            var ajaxResult = new CollectDetailBLL().CollectSet(Request.Params);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult PraiseSet()
        {
            var ajaxResult = new PraiseDetailBLL().PraiseSet(Request.Params);
            return Json(ajaxResult);
        }

        /// <summary>
        /// 收藏列表信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult GetCollects()
        {
            var ajaxResult = new CollectDetailBLL().GetCollects(Request.Params);
            return Json(ajaxResult);
        }

        public ActionResult GetStr(string str)
        {
            return Content(DESEncrypt.Encrypt($"{str},{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}"));
        }

        [HttpPost]
        public JsonResult DelMeetAttach(int id = 0)
        {
            var ajaxResult = new MeetAttachBLL().DelDoc(id);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult searchType(string type, string keywords)
        {
            AjaxResult ajaxResult = null;
            switch (type)
            {
                case "0":
                    ajaxResult = new ProjectBLL().searchByNameMobile(Request.Params);
                    return Json(ajaxResult);
                case "1":
                    ajaxResult = new ProjectBLL().searchByNameMobile(Request.Params);
                    return Json(ajaxResult);
                case "5":
                    ajaxResult = new ProjectBLL().searchByNameMobile(Request.Params);
                    return Json(ajaxResult);
                default:
                    ajaxResult = new ResearchBLL().searchByNameMobile(Request.Params);
                    return Json(ajaxResult);
            }
        }

        [HttpPost]
        public JsonResult AddMeetAttach(MeetAttach model)
        {
            //if (model.AtSuffix !="DD" && model.AtSuffix != "BP" && new ProjectBLL().ExistProjectDocType(model.SourceId, model.AtSuffix))
            //{
            //    if(!new AttachmentBLL().DelDoc(model.SourceId, model.AtSuffix))
            //    {
            //        log.Info(model);
            //    }
            //}
            var ajaxResult = new MeetAttachBLL().SaveDoc(model);
            return Json(ajaxResult);
        }
        /// <summary>
        /// 保存项目信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateInput(false)]
        public JsonResult MeetSave(MeetWithAttach model)
        {
            var ajaxResult = new MeetBLL().Save(model, "", model.Attach);
            return Json(ajaxResult);
        }


        [HttpPost]
        public JsonResult DealList()
        {
            string projectType = WebHelper.GetValue("projectType", string.Empty, Request.Params);
            if(projectType.Equals("黑名单"))
            {
                var ajaxResult1 = new RestrictedTradingListBLL().GetPageList(Request.Params);
                return Json(ajaxResult1);
            }
            var ajaxResult = new Project_ActiveBLL().GetProjects(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult DealAllList()
        {
            string projectType = WebHelper.GetValue("projectType", string.Empty, Request.Params);
            var ajaxResult = new Project_ActiveBLL().GetAllProjects(Request.Params);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult DealDetail()
        {
            var ajaxResult = new Project_ActiveBLL().GetProject(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult DealHistory()
        {
            var ajaxResult = new Project_Active_ClosedBLL().GetProjectHistory(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult DealHistoryByProject()
        {
            var ajaxResult = new Project_Active_ClosedBLL().GetProjectHistoryByProject(Request.Params);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult DealFundNames()
        {
            var ajaxResult = new Project_ActiveBLL().GetFundNames(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult ProjectCheck(string pname = "")
        {
            var ajaxResult = new ProjectService().ProjectSimilarNameCheck(pname);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult founderSimilarCheck(string pname = "")
        {
            var ajaxResult = new ProjectService().FounderSimilarCheck(pname);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult CompleteScore(int pid, string score)
        {
            var ajaxResult = new ProjectBLL().SetProjectCompleteScore(pid, score);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult PerformanceReport(string user, decimal rate = 7)
        {
            if (String.IsNullOrEmpty(user))
            {
                user = loginUser.RealName;
            }
            var ajaxResult = ReportsGenerator.getPerformanceTable(user, user, rate, 1, 1, 1, 1, 1, false);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult PerformanceReportMini(string user, decimal rate = 7)
        {
            if (String.IsNullOrEmpty(user))
            {
                user = loginUser.RealName;
            }
            var ajaxResult = ReportsGeneratorMini.getperformanceTableMini(user, rate, 1, 1, 1, 1, 1, false);
            return Json(ajaxResult);
        }

        [HttpPost]
        public JsonResult BuybackList()
        {
            var ajaxResult = new BuybackBLL().GetPageList(Request.Params);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult BuybackSave(Buyback model)
        {
            var ajaxResult = new BuybackBLL().Save(model);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult GetBuyback(int id)
        {
            var ajaxResult = new BuybackBLL().GetModel(id);
            return Json(ajaxResult);
        }
        [HttpPost]
        public JsonResult LogSet(SysLog model)
        {
            model.CreatedBy = loginUser.RealName;
            var ajaxResult = new SysLogBLL().Add(model);
            return Json(ajaxResult);
        }

        /// <summary>
        /// 获取基于用户兴趣的推荐新闻
        /// </summary>
        /// <param name="page">页码（从1开始）</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="threshold">相似度阈值（0-1之间）</param>
        /// <param name="category">新闻分类过滤</param>
        /// <param name="source">新闻来源过滤</param>
        /// <param name="startDate">开始日期过滤</param>
        /// <param name="endDate">结束日期过滤</param>
        /// <param name="tagFilter">标签过滤</param>
        /// <returns>推荐新闻列表</returns>
        [HttpPost]
        public async Task<JsonResult> GetNewsRecommendations(
            int page = 1,
            int pageSize = 20,
            double threshold = 0.4,
            string category = null,
            string source = null,
            string startDate = null,
            string endDate = null,
            string tagFilter = null)
        {
            try
            {
                var user = memberBll.GetLogOnUser();
                if (user == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" });
                }

                // 使用推荐引擎获取结果
                var recommendationEngine = new NewsRecommendationEngine();
                var result = await recommendationEngine.GetRecommendationsForApiAsync(
                    user.Id, page, pageSize, threshold, category, source, startDate, endDate, tagFilter);

                return Json(result);
            }
            catch (Exception ex)
            {
                Logger.Error($"获取推荐新闻失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "获取推荐新闻失败: " + ex.Message });
            }
        }
    }
}