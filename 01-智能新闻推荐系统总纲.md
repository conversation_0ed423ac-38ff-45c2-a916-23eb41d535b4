# 智能新闻推荐系统 - 系统总纲

## 一、系统概述

### 核心目标
- 基于用户项目数据，使用大模型生成个性化兴趣画像
- 利用标签抽取+Embedding向量化实现精准推荐
- 支持实时推荐，毫秒级响应
- 使用.NET Framework、SQL Server等技术栈
- **标签级别向量化**：每个兴趣标签独立存储向量表示

### 适用场景
- 30个投资用户，兴趣有重叠
- 用户录入项目后自动分析兴趣标签
- 新闻添加后智能推荐给相关标签用户

## 二、整体架构设计

### 2.1 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户画像管理   │    │   新闻向量化     │    │   兴趣推荐引擎   │
│                 │    │                 │    │                 │
│ • 大模型分析    │    │ • 新闻标签分析  │    │ • 相似度计算    │
│ • 兴趣标签生成  │    │ • 向量化处理    │    │ • 混合推荐算法  │
│ • 标签向量化    │    │ • 向量存储      │    │ • 结果排序      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   推荐结果缓存   │
                    │                 │
                    │ • Redis缓存     │
                    │ • 实时查询      │
                    │ • 过期清理      │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   大模型服务     │
                    │                 │
                    │ • 内容分析      │
                    │ • 标签抽取      │
                    │ • 用户画像生成  │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Embedding服务   │
                    │                 │
                    │ • 标签向量化    │
                    │ • 语义相似度    │
                    │ • 本地计算      │
                    └─────────────────┘
```

### 2.2 核心流程设计
```
离线处理流程：
用户添加项目 → 大模型分析 → 生成兴趣标签 → 标签向量化 → 存储标签向量
     ↓
新闻添加 → 大模型分析 → 生成新闻标签 → 新闻向量化 → 存储新闻向量

实时推荐流程：
用户请求推荐 → 获取用户标签关联 → 获取标签向量 → 计算相似度 → 混合排序 → 返回推荐
```

## 三、核心模块概览

### 3.1 用户画像管理模块
**详细设计请参考：[02-用户画像管理模块.md](02-用户画像管理模块.md)**
- **功能**：AI分析用户项目，生成个性化兴趣画像
- **核心**：兴趣标签管理、动态更新机制、标签向量化处理
- **输出**：用户兴趣标签、标签权重、标签向量

### 3.2 新闻向量化模块
**详细设计请参考：[03-新闻向量化模块.md](03-新闻向量化模块.md)**
- **功能**：新闻内容的AI分析和向量化处理
- **核心**：新闻标签提取、Embedding向量化、批量处理
- **输出**：新闻向量数据、标签信息、向量质量指标

### 3.3 兴趣推荐引擎模块
**详细设计请参考：[04-兴趣推荐引擎模块.md](04-兴趣推荐引擎模块.md)**
- **功能**：基于用户兴趣标签的个性化推荐算法
- **核心**：标签向量相似度计算、混合推荐、个性化优化
- **输出**：个性化推荐列表、相似度分数、推荐原因

### 3.4 推荐结果缓存模块
- **功能**：高性能缓存管理，支持实时查询
- **核心**：Redis缓存策略、过期清理、实时更新
- **输出**：快速响应、缓存命中率优化

### 3.5 大模型服务模块
- **功能**：AI驱动的智能分析服务
- **核心**：用户画像生成、新闻标签分析、批量处理
- **输出**：结构化分析结果、标签权重、兴趣描述

### 3.6 Embedding服务模块
- **功能**：文本向量化服务
- **核心**：标签向量化、语义相似度计算
- **输出**：1024维向量表示、相似度计算

## 四、技术栈选择

### 4.1 核心技术
- **大模型**：deepseek-r1-0528（支持结构化输出）
- **向量化**：text-embedding-bge-m3 （1024维）
- **数据库**：SQL Server（主数据库）+ Redis（缓存）
- **开发框架**：ASP.NET MVC + Web API

### 4.2 性能要求
- **响应时间**：推荐查询 < 100ms
- **并发支持**：30个用户同时在线
- **缓存命中率**：> 90%
- **推荐精度**：> 85%

## 五、推荐算法概述

### 5.1 标签级别推荐策略
系统采用标签级别的推荐策略，结合三种评分方式：
- **标签向量相似度评分**（50%）：基于标签向量与新闻向量的余弦相似度
- **标签匹配评分**（30%）：基于用户标签与新闻标签的匹配程度
- **大模型分析评分**（20%）：基于大模型的内容相关性分析

### 5.2 算法流程
1. 获取用户标签关联和标签向量
2. 获取新闻向量
3. 计算标签向量与新闻向量的余弦相似度
4. 计算标签匹配分数
5. 获取大模型分析评分
6. 按权重组合三个分数
7. 过滤低分内容（< 0.5）
8. 排序返回Top-10推荐

## 六、数据存储结构

### 6.1 核心数据表
- **用户画像表**：存储用户兴趣画像的基本信息
- **兴趣标签表**：管理所有兴趣标签及其向量表示
- **用户标签关联表**：维护用户与标签的关联关系
- **新闻向量表**：存储新闻向量和标签信息
- **推荐结果表**：存储推荐结果和匹配信息
- **推荐统计表**：存储推荐相关的统计数据

### 6.2 缓存策略
- **标签向量缓存**：缓存标签向量，有效期30天
- **新闻向量缓存**：缓存新闻向量，有效期30天
- **推荐结果缓存**：缓存推荐结果，有效期30分钟

## 七、实施计划

### 7.1 开发阶段
1. **第一阶段**：用户画像管理模块（1-2周）
2. **第二阶段**：新闻向量化模块（1-2周）
3. **第三阶段**：兴趣推荐引擎模块（1-2周）
4. **第四阶段**：推荐结果缓存模块（1周）
5. **第五阶段**：大模型服务模块（1周）
6. **第六阶段**：Embedding服务模块（1周）
7. **第七阶段**：系统集成测试（1周）

### 7.2 测试阶段
- **单元测试**：各模块功能验证
- **集成测试**：模块间接口测试
- **性能测试**：响应时间和并发测试
- **用户测试**：真实用户场景测试

## 八、监控和运维

### 8.1 关键指标
- **业务指标**：推荐覆盖率、用户点击率、推荐转化率
- **技术指标**：响应时间、缓存命中率、错误率
- **AI指标**：画像生成成功率、标签分析准确率、标签向量计算精度

### 8.2 告警机制
- AI服务异常告警
- Embedding服务异常告警
- 缓存服务异常告警
- 推荐质量下降告警
- 系统负载过高告警

## 九、扩展性考虑

### 9.1 用户规模扩展
- 当前设计支持30个用户
- 可通过增加兴趣标签、优化AI批量处理、增强缓存策略来扩展
- 支持分布式部署

### 9.2 功能扩展
- 个性化推荐权重调整
- 用户反馈学习机制
- 推荐效果A/B测试
- 多维度推荐策略
- 实时推荐流
- 标签权重动态调整
- 向量数据库集成

## 十、风险评估

### 10.1 技术风险
- **AI服务稳定性**：大模型API调用失败
- **标签向量计算性能**：高并发下的标签向量计算延迟
- **数据一致性**：缓存与数据库同步问题

### 10.2 业务风险
- **推荐质量**：AI分析结果不准确
- **用户体验**：推荐结果不符合预期
- **系统可用性**：关键服务中断

### 10.3 风险缓解措施
- 实现AI服务降级机制
- 建立多层缓存策略
- 定期备份和恢复测试
- 建立监控和告警体系
- 实现标签向量计算本地化

## 十一、方案优势

### 11.1 精度优势
- **标签抽取精度**：大模型深度理解内容语义
- **标签向量化精度**：每个标签独立向量化，避免语义稀释
- **混合算法精度**：结合多种评分方式，提高推荐准确性

### 11.2 性能优势
- **离线处理性能**：标签抽取和向量生成采用异步处理
- **实时推荐性能**：标签向量计算实现毫秒级响应
- **缓存优化性能**：多层缓存机制提高响应速度

### 11.3 成本优势
- **大模型成本控制**：只在内容更新时调用，减少调用次数
- **Embedding成本控制**：支持本地部署，避免API调用成本
- **缓存成本控制**：缓存机制减少重复计算

### 11.4 架构优势
- **标签级别向量化**：避免用户向量存储的复杂性
- **独立更新机制**：标签向量可以独立更新
- **维护成本低**：不需要维护用户级别的向量
- **扩展性好**：易于添加新的兴趣标签

**这是一个平衡精度、性能和成本的最佳解决方案！** 