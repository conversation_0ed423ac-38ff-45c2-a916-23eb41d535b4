﻿using LdapForNet;
using LdapForNet.Native;
using System;
using System.Collections.Generic;
using System.Linq;
using static LdapForNet.Native.Native;

namespace Banyan.Code
{
    public enum LDAP_ERROR_CODE
    {
        PASSWD_ERROR = 1,
        ITEM_NOT_FOUND = 2,
        PASSWD_WEAK = 3,
        SERVICE_ERROR = 4,
        ITEM_DUPLICATE = 5
    }
    public class LdapException : Exception
    {
        public int _errCode { get; set; }
        public int ErrorCode { get; set; }
        public new string Message;
        private readonly Exception innerException;

        public LdapException() { }

        public LdapException(string msg) : base(msg)
        {
            this.Message = msg;
        }

        public LdapException(string msg, Exception innerException) : base(msg, innerException)
        {
            this.Message = msg;
            this.innerException = innerException;
        }
    }
    public class Ldap
    {

        private static LdapConnection createConnection()
        {
            var cn = new LdapConnection();
            cn.Connect(new Uri("ldaps://ldap.gaorongvc.cn:636"));
            try
            {
                cn.Bind(Native.LdapAuthType.Simple, new LdapCredential
                {
                    UserName = "uid=admin,ou=users,dc=gaorongvc,dc=cn",
                    Password = System.Configuration.ConfigurationManager.ConnectionStrings["ldappw"].ConnectionString
            });
            }
            catch (Exception ex)
            {
                Logger.Error("ldap bind admin service error!", ex);
                var newEx = new LdapException("登录服务出现问题，请稍后重试", ex);
                newEx.ErrorCode = (int)LDAP_ERROR_CODE.SERVICE_ERROR;
                throw newEx;
            }
            return cn;
        }
      
        public static List<List<string>> getMembers(string baseDN, params string[] group)
        {
            string groups = "(" + group.Aggregate("|", (res, g) => res += $"({g})") +")";

            using (var cn = createConnection())
            {
                try
                {
                    var entries = cn.Search(baseDN, groups, LdapSearchScope.LDAP_SCOPE_SUBTREE).ToList();
                    var res = new List<List<string>>();
                    var dic = entries.Select(c => c.Attributes["member"]).ToList();
                    foreach (var str in group)
                    {
                        foreach (var c in entries)
                        {
                            var name = c.Attributes["cn"][0];
                            if ($"cn={name}".Equals(str))
                            {
                                res.Add(c.Attributes["member"]);
                            }
                        }
                    }
                    return res;
                }
                catch (Exception ex)
                {
                    Logger.Error($"分组{group}用户列表获取失败!", ex);
                    return null;
                }
            }
        }
        public static List<Dictionary<string, List<string>>> getUsers(string searchStr)
        {

            using (var cn = createConnection())
            {
                try
                {
                    return cn.Search("ou=users,dc=gaorongvc,dc=cn", searchStr, LdapSearchScope.LDAP_SCOPE_ONE).Select(p => p.Attributes)
                        .ToList();
                }
                catch (Exception ex)
                {
                    Logger.Error($"{searchStr}用户获取失败!", ex);
                    return null;
                }
            }
        }

        public static List<string> getUserNameList(string searchStr)
        {

            using (var cn = createConnection())
            {
                try
                {
                    return cn.Search("ou=users,dc=gaorongvc,dc=cn", searchStr, LdapSearchScope.LDAP_SCOPE_SUBTREE).Select(p => p.Dn).Where(p => p.StartsWith("cn"))
                        .ToList();
                }
                catch (Exception ex)
                {
                    Logger.Error($"{searchStr}用户获取失败!", ex);
                    return null;
                }
            }
        }

        public static Dictionary<string, Dictionary<string, List<string>>> getUsersDic(string searchStr)
        {

            using (var cn = createConnection())
            {
                try
                {
                    return cn.Search("ou=users,dc=gaorongvc,dc=cn", searchStr, LdapSearchScope.LDAP_SCOPE_SUBTREE).ToDictionary(p => p.Attributes["cn"][0], p => p.Attributes);

                }
                catch (Exception ex)
                {
                    Logger.Error($"{searchStr}用户获取失败!", ex);
                    return null;
                }
            }
        }

        public static List<Dictionary<string, List<string>>> getGroups()
        {
            using (var cn = createConnection())
            {
                try
                {
                    var entries = cn.Search("ou=invest,ou=groups,dc=gaorongvc,dc=cn", "(objectclass=groupOfNames)", LdapSearchScope.LDAP_SCOPE_SUBTREE);
                    return entries.Select(c => c.Attributes).ToList();
                }
                catch (Exception ex)
                {
                    Logger.Error($"分组列表获取失败!", ex);
                    return null;
                }
            }
        }
        public static Dictionary<string, List<string>> getGroup(string name)
        {
            using (var cn = createConnection())
            {
                try
                {
                    string searchStr = $"(&(cn={name})(objectclass=groupOfNames))";
                    var entries = cn.Search("ou=invest,ou=groups,dc=gaorongvc,dc=cn", searchStr, LdapSearchScope.LDAP_SCOPE_ONELEVEL);
                    return entries.First().Attributes;
                }
                catch (Exception ex)
                {
                    Logger.Error($"分组列表获取失败!", ex);
                    return null;
                }
            }
        }

        public static bool isMemberOf(string name, string group)
        {
            using (var cn = createConnection())
            {
                try
                {
                    string searchStr = $"(&(cn={name})(memberof=cn={group},ou=invest,ou=groups,dc=gaorongvc,dc=cn))";
                    var entries = cn.Search("ou=users,dc=gaorongvc,dc=cn", searchStr, LdapSearchScope.LDAP_SCOPE_SUBTREE);
                    return entries.Count == 1;
                }
                catch (Exception ex)
                {
                    Logger.Error($"用户{name}属于分组{group}判断失败!", ex);
                    return false;
                }
            }
        }
        public static bool isLeave(string name)
        {
            using (var cn = createConnection())
            {
                try
                {
                    string searchStr = $"(&(cn={name})(memberof=cn=leave,ou=groups,dc=gaorongvc,dc=cn))";
                    var entries = cn.Search("ou=users,dc=gaorongvc,dc=cn", searchStr, LdapSearchScope.LDAP_SCOPE_SUBTREE);
                    return entries.Count == 1;
                }
                catch (Exception ex)
                {
                    Logger.Error($"用户{name}属于分组leave判断失败!", ex);
                    return false;
                }
            }
        }

        public static string getMailByName(string name)
        {
            var member = NameMatchedUser(name);
            var mail = "";
            if (member["mail"] != null)
            {
                mail = member["mail"][0];
            }
            return mail;
        }

        public static IList<LdapEntry> getAttributesByMobile(ILdapConnection cn, string mobile, params string[] attrs)
        {
            try
            {
                var response = (SearchResponse)cn.SendRequest(new SearchRequest("ou=users,dc=gaorongvc,dc=cn", $"mobile={mobile}", LdapSearchScope.LDAP_SCOPE_SUBTREE,attrs));

                return (IList<LdapEntry>)response.Entries.Select<DirectoryEntry, LdapEntry>((Func<DirectoryEntry, LdapEntry>)(_ => _.ToLdapEntry())).ToList<LdapEntry>();
            }
            catch (Exception ex)
            {
                Logger.Error($"获取用户mobile={mobile}属性{attrs}失败", ex);
                return null;
            }
        }

        public static IList<LdapEntry> getAttributesByEmployeeNumber(ILdapConnection cn, int employeeNumber, params string[] attrs)
        {
            try
            {
                var response = (SearchResponse)cn.SendRequest(new SearchRequest("ou=users,dc=gaorongvc,dc=cn", $"employeeNumber={employeeNumber}", LdapSearchScope.LDAP_SCOPE_SUBTREE, attrs));

                return (IList<LdapEntry>)response.Entries.Select<DirectoryEntry, LdapEntry>((Func<DirectoryEntry, LdapEntry>)(_ => _.ToLdapEntry())).ToList<LdapEntry>();
            }
            catch (Exception ex)
            {
                Logger.Error($"获取用户employeeNumber={employeeNumber}属性{attrs}失败", ex);
                return null;
            }
        }

        public static IList<LdapEntry> getAttributesByUnionId(ILdapConnection cn, string unionid, params string[] attrs)
        {
            try
            {
                var response = (SearchResponse)cn.SendRequest(new SearchRequest("ou=users,dc=gaorongvc,dc=cn", $"roomNumber:caseExactMatch:={unionid}", LdapSearchScope.LDAP_SCOPE_SUBTREE, attrs));

                return (IList<LdapEntry>)response.Entries.Select<DirectoryEntry, LdapEntry>((Func<DirectoryEntry, LdapEntry>)(_ => _.ToLdapEntry())).ToList<LdapEntry>();
            }
            catch (Exception ex)
            {
                Logger.Error($"获取用户roomNumber={unionid}属性{attrs}失败", ex);
                return null;
            }
        }

        public static IList<LdapEntry> getAttributesByOpenId(ILdapConnection cn, string street, params string[] attrs)
        {
            try
            {
                var response = (SearchResponse)cn.SendRequest(new SearchRequest("ou=users,dc=gaorongvc,dc=cn", $"street={street}", LdapSearchScope.LDAP_SCOPE_SUBTREE, attrs));

                return (IList<LdapEntry>)response.Entries.Select<DirectoryEntry, LdapEntry>((Func<DirectoryEntry, LdapEntry>)(_ => _.ToLdapEntry())).ToList<LdapEntry>();
            }
            catch (Exception ex)
            {
                Logger.Error($"获取用户street={street}属性{attrs}失败", ex);
                return null;
            }
        }
        public static IList<LdapEntry> getAttributes(ILdapConnection cn, params string[] attrs)
        {
            try
            {
                var response = (SearchResponse)cn.SendRequest(new SearchRequest("ou=users,dc=gaorongvc,dc=cn", "(objectClass=*)", LdapSearchScope.LDAP_SCOPE_SUBTREE, attrs));

                return (IList<LdapEntry>)response.Entries.Select<DirectoryEntry, LdapEntry>((Func<DirectoryEntry, LdapEntry>)(_ => _.ToLdapEntry())).ToList<LdapEntry>();
            }
            catch (Exception ex)
            {
                Logger.Error($"获取属性{attrs}失败", ex);
                return null;
            }
        }

        public static int createWithoutId(string name, string phone, string mail, string openId, string avatar, string companyName)
        {
            using (var cn = createConnection())
            {
                try
                {
                    //var entries = cn.Search("ou=users,dc=gaorongvc,dc=cn", "(objectclass=inetOrgPerson)", LdapSearchScope.LDAP_SCOPE_SUBTREE);
                    var maxId = getAttributes(cn, "employeeNumber").Select(val => {
                        if (val.Attributes.ContainsKey("employeeNumber")) {
                            return int.Parse(val.Attributes["employeeNumber"][0]);
                        }
                        return -1;
                        }).Aggregate((next,now) =>
                    {
                        return next > now ? next : now;
                    });
                    var id = maxId + 1;
                    Ldap.add(cn, name, phone, mail, openId, avatar, id+"", companyName);
                    Ldap.addToGroupHelper(cn, "cn=" + name + ",ou=users,dc=gaorongvc,dc=cn", "cn=underReview,ou=groups,dc=gaorongvc,dc=cn");
                    return id;
                }
                catch (Exception ex)
                {
                    Logger.Error("添加用户失败" + " name: " + name + " phone: " + phone + " openId: " + openId + " avatar: " + avatar, ex);
                    return -1;
                }
            }
        }
        public static void add(ILdapConnection cn, string name, string phone, string mail, string openId, string avatar,
            string employeeNumber, string companyName = "高榕资本")
        {
            var entries = cn.Search("ou=users,dc=gaorongvc,dc=cn", $"(cn={name})", LdapSearchScope.LDAP_SCOPE_SUBTREE);
            if (entries.Count == 1)
            {
                var Attributes = new List<LdapModifyAttribute>
                    {
                        new LdapModifyAttribute
                        {
                        LdapModOperation = Native.LdapModOperation.LDAP_MOD_ADD,
                        Type = "street",
                        Values = new List<string> {openId}
                        },
                    };
                if (!entries[0].Attributes.ContainsKey("employeeNumber"))
                {
                    Attributes.Add(
                            new LdapModifyAttribute
                            {
                                LdapModOperation = Native.LdapModOperation.LDAP_MOD_ADD,
                                Type = "employeeNumber",
                                Values = new List<string> { employeeNumber }
                            });
                }
                cn.Modify(new LdapModifyEntry
                {
                    Dn = "cn=" + name + ",ou=users,dc=gaorongvc,dc=cn",
                    Attributes = Attributes
                });
            } else {
                cn.Add(new LdapEntry
                {
                    Dn = "cn=" + name + ",ou=users,dc=gaorongvc,dc=cn",
                    Attributes = new Dictionary<string, List<string>>
                    {
                        {"sn", new List<string> {name}},
                        {"objectclass", new List<string> {"inetOrgPerson"}},
                        {"cn", new List<string> {name}},
                        {"uid", new List<string> {name}},
                        {"employeeNumber", new List<string> {employeeNumber}},
                        {"o", new List<string> {companyName}},
                        {"initials", new List<string> {DateTime.Now.ToString("yyyy/MM/dd-HH:mm:ss")}},
                        {"mobile", new List<string> {phone}},
                        {"mail", new List<string> {mail}},
                        {"street", new List<string> {openId}},
                        {"labeledURI", new List<string> {avatar}}
                    }
                });
            }
           
        }

        public static bool ReplaceAttr(string name, string attr, string val)
        {
            using (var cn = createConnection())
            {
                try
                {
                    cn.Modify(new LdapModifyEntry
                    {
                        Dn = "cn=" + name + ",ou=users,dc=gaorongvc,dc=cn",
                        Attributes = new List<LdapModifyAttribute>
                        {

                            new LdapModifyAttribute
                            {
                            LdapModOperation = Native.LdapModOperation.LDAP_MOD_REPLACE,
                            Type = attr,
                            Values = new List<string> {val}
                            },

                        }
                    });
                    return true;
                }
                catch (Exception ex)
                {
                    Logger.Error("修改用户失败" + " name: " + name, ex);
                    return false;
                }
            }
        }

        public static bool AddAttr(string name, string attr, string val)
        {
            using (var cn = createConnection())
            {
                try
                {
                    cn.Modify(new LdapModifyEntry
                    {
                        Dn = "cn=" + name + ",ou=users,dc=gaorongvc,dc=cn",
                        Attributes = new List<LdapModifyAttribute>
                        {

                            new LdapModifyAttribute
                            {
                            LdapModOperation = Native.LdapModOperation.LDAP_MOD_ADD,
                            Type = attr,
                            Values = new List<string> {val}
                            },

                        }
                    });
                    return true;
                }
                catch (Exception ex)
                {
                    Logger.Error("修改用户失败" + " name: " + name, ex);
                    return false;
                }
            }
        }

        public static void addToGroupHelper(ILdapConnection cn,string userDN, string groupDN)
        {
            try
            {
                cn.Modify(new LdapModifyEntry
                {
                    Dn = groupDN,
                    Attributes = new List<LdapModifyAttribute>
                    {
                        new LdapModifyAttribute
                        {
                        LdapModOperation = LdapModOperation.LDAP_MOD_ADD,
                        Type = "member",
                        Values = new List<string> {userDN}
                        },
                    }
                });
            } catch(Exception ex)
            {
                Logger.Error($"添加用户{userDN}到分组{groupDN}失败", ex);
                throw (ex);
            }
        }
        private static void dicAdd(Dictionary<string, List<string>> dic, string key, Dictionary<string, string> dic2)
        {
            if (dic2.ContainsKey(key))
            {
                dic.Add(key, new List<string> { dic2[key] });
            }
        }
        public static bool AddAttrs(Dictionary<string, string> attrs, string username)
        {
            string name = attrs["cn"];
            using (var cn = createConnection())
            {
                try
                {

                    var maxId = getAttributes(cn, "employeeNumber").Select(val => {
                        if (val.Attributes.ContainsKey("employeeNumber"))
                        {
                            return int.Parse(val.Attributes["employeeNumber"][0]);
                        }
                        return -1;
                    }).Aggregate((next, now) =>
                    {
                        return next > now ? next : now;
                    });
                    var employeeNumber = maxId + 1;

                    var Attrs = new Dictionary<string, List<string>>
                        {
                            {"sn", new List<string> {name}},
                            {"objectclass", new List<string> {"inetOrgPerson"}},
                            {"cn", new List<string> {name}},
                            {"uid", new List<string> {name}},
                            {"employeeNumber", new List<string> {employeeNumber + ""}},
                            {"o", new List<string> {"高榕资本"}},
                            {"initials", new List<string> {DateTime.Now.ToString("yyyy/MM/dd-HH:mm:ss")}}
                        };

                    Ldap.dicAdd(Attrs, "mobile", attrs);
                    Ldap.dicAdd(Attrs, "mail", attrs);
                    cn.Add(new LdapEntry
                    {
                        Dn = "cn=" + name + ",ou=users,dc=gaorongvc,dc=cn",
                        Attributes = Attrs
                    });
                    return true;
                }
                catch (Exception ex)
                {
                    Logger.Error("修改用户失败" + " name: " + name, ex, username);
                    return false;
                }
            }
        }

        public static bool addToGroup(string userCN, string groupDN)
        {
            using (var cn = createConnection())
            {
                try
                {
                    Ldap.addToGroupHelper(cn, "cn=" + userCN + ",ou=users,dc=gaorongvc,dc=cn", groupDN);
                    return true;
                } catch(Exception e)
                {
                    Logger.Error(e.Message, e);
                    return false;
                }
            }
        }

            public static void removeFromGroup(string userCN, string groupDN)
        {
            using (var cn = createConnection())
            {
                try
                {
                    cn.Modify(new LdapModifyEntry
                    {
                        Dn = groupDN,
                        Attributes = new List<LdapModifyAttribute>
                        {
                            new LdapModifyAttribute
                            {
                            LdapModOperation = LdapModOperation.LDAP_MOD_DELETE,
                            Type = "member",
                            Values = new List<string> { "cn=" + userCN + ",ou=users,dc=gaorongvc,dc=cn" }
                            },
                        }
                    });
                }
                catch(Exception ex)
                {
                    if(!ex.Message.Contains("member: no such value"))
                    {
                        Logger.Error($"删除用户{userCN}从分组{groupDN}失败", ex);
                        throw (ex);
                    }
                }
            }
        }
        public static void modifyMobile(string name, string phone)
        {
            using (var cn = createConnection())
            {
                try
                {
                    cn.Modify(new LdapModifyEntry
                    {
                        Dn = "cn=" + name + ",ou=users,dc=gaorongvc,dc=cn",
                        Attributes = new List<LdapModifyAttribute>
                        {
                               new LdapModifyAttribute
                                {
                                    LdapModOperation = LdapModOperation.LDAP_MOD_REPLACE,
                                    Type = "mobile",
                                    Values = new List<string> {phone}
                                },
                        }
                    });
                }
                catch (Exception ex)
                {
                    Logger.Error("修改用户失败" + " name: " + name + " phone: " + phone, ex);
                }
            }
        }
        public static bool verifyPasswdHelper(ILdapConnection cn, string dn, string mobile, string passwd)
        {
            try
            {
                cn.Bind(Native.LdapAuthType.Simple, new LdapCredential
                {
                    UserName = dn,
                    Password = passwd
                });
            }
            catch (Exception e)
            {
                Logger.Info("login fail! user: " + dn + "mobile: " + mobile + " passwd:" + passwd + " " + e.StackTrace);
                return false;
            }
            return true;
        }
         

        public static Dictionary<string, List<string>> mobilePasswdMatchedUser(string mobile, string passwd = "")
        {
            using (var cn = createConnection())
            {
                try
                {
                    string searchStr = string.Format("(mobile={0})", mobile); // (|(cn={0})(uid={0})(mobile={0})(mail={0})(sn={0}))
                    var entries = getAttributesByMobile(cn, mobile, "cn", "mobile", "memberOf", "employeeNumber", "labeledURI", "o","initials", "street");

                    if (entries.Count > 1)
                    {
                        var newEx = new LdapException("ldap出现两个相同手机号的人" + mobile);
                        newEx.ErrorCode = (int)LDAP_ERROR_CODE.ITEM_DUPLICATE;
                        throw newEx;
                    }
                    else if (entries.Count == 0)
                    {
                        var newEx = new LdapException("不存在此手机号的人" + mobile);
                        newEx.ErrorCode = (int)LDAP_ERROR_CODE.ITEM_NOT_FOUND;
                        throw newEx;
                    }

                    if (passwd.IsEmpty())
                    {
                        return entries[0].Attributes;
                    }

                    if (verifyPasswdHelper(cn, entries[0].Dn, mobile, passwd))
                    {
                        if (Validate.isWeakPasswd(passwd))
                        {
                            var newEx = new LdapException("密码过于简单，请重置密码");
                            newEx.ErrorCode = (int)LDAP_ERROR_CODE.PASSWD_WEAK;
                            throw newEx;
                        }
                        return entries[0].Attributes;
                    }
                    else
                    {
                        var newEx = new LdapException("统一登录密码错误");
                        newEx.ErrorCode = (int)LDAP_ERROR_CODE.PASSWD_ERROR;
                        throw newEx;
                    }
                }
                catch (Exception ex)

                {
                    Logger.Info(ex.Message + " " + ex.StackTrace);
                    throw;
                }

            }
        }

        public static Dictionary<string, List<string>> openIdMatchedUser(string street)
        {
            using (var cn = createConnection())
            {
                try
                {
                    string searchStr = string.Format("(street={0})", street); // (|(cn={0})(uid={0})(mobile={0})(mail={0})(sn={0}))
                    var entries = getAttributesByOpenId(cn, street, "cn", "mobile", "memberOf", "employeeNumber", "labeledURI", "o", "initials", "street");

                    if (entries.Count > 1)
                    {
                        var newEx = new LdapException("ldap出现两个相同OpenId的人" + street);
                        newEx.ErrorCode = (int)LDAP_ERROR_CODE.ITEM_DUPLICATE;
                        throw newEx;
                    }
                    else if (entries.Count == 0)
                    {
                        var newEx = new LdapException("不存在此OpenId的人" + street);
                        newEx.ErrorCode = (int)LDAP_ERROR_CODE.ITEM_NOT_FOUND;
                        throw newEx;
                    }
                    Logger.Info($"微信用户登陆：{entries[0].Attributes["cn"][0]}");
                    return entries[0].Attributes;
                }
                catch (Exception ex)

                {
                    Logger.Info(ex.Message + " " + ex.StackTrace);
                    throw;
                }

            }
        }
        public static IList<LdapEntry> getAttributesByFilter(ILdapConnection cn, string filter, params string[] attrs)
        {
            try
            {
                var response = (SearchResponse)cn.SendRequest(new SearchRequest("ou=users,dc=gaorongvc,dc=cn", filter, LdapSearchScope.LDAP_SCOPE_SUBTREE, attrs));

                return (IList<LdapEntry>)response.Entries.Select<DirectoryEntry, LdapEntry>((Func<DirectoryEntry, LdapEntry>)(_ => _.ToLdapEntry())).ToList<LdapEntry>();
            }
            catch (Exception ex)
            {
                Logger.Error($"获取用户{filter}属性{attrs}失败", ex);
                return null;
            }
        }

        public static Dictionary<string, List<string>> NameMatchedUser(string name)
        {
            using (var cn = createConnection())
            {
                try
                {
                    string searchStr = string.Format("(cn={0})", name); // (|(cn={0})(uid={0})(mobile={0})(mail={0})(sn={0}))
                    var entries = getAttributesByFilter(cn, $"name={name}", "cn", "mobile", "memberOf", "employeeNumber", "labeledURI", "o", "initials", "mail", "street", "homePostalAddress");

                    if (entries.Count > 1)
                    {
                        var newEx = new LdapException("ldap出现两个相同name的人" + name);
                        newEx.ErrorCode = (int)LDAP_ERROR_CODE.ITEM_DUPLICATE;
                        throw newEx;
                    }
                    else if (entries.Count == 0)
                    {
                        var newEx = new LdapException("不存在此用户id的人 name:" + name);
                        newEx.ErrorCode = (int)LDAP_ERROR_CODE.ITEM_NOT_FOUND;
                        throw newEx;
                    }
                    return entries[0].Attributes;
                }
                catch (Exception ex)

                {
                    Logger.Info(ex.Message + " " + ex.StackTrace);
                    throw;
                }

            }
        }

        public static Dictionary<string, List<string>> UnionIdMatchedUser(string unionid)
        {
            using (var cn = createConnection())
            {
                try
                {
                    string searchStr = string.Format("(roomNumber:caseExactMatch:={0})", unionid); // (|(cn={0})(uid={0})(mobile={0})(mail={0})(sn={0}))
                    var entries = getAttributesByUnionId(cn, unionid, "cn", "mobile", "memberOf", "employeeNumber", "labeledURI", "o", "initials", "mail", "street", "homePostalAddress");
               

                    if (entries.Count > 1)
                    {
                        var newEx = new LdapException("ldap出现两个相同unionid的人" + unionid);
                        newEx.ErrorCode = (int)LDAP_ERROR_CODE.ITEM_DUPLICATE;
                        throw newEx;
                    }
                    else if (entries.Count == 0)
                    {
                        var newEx = new LdapException("不存在此用户 unionid:" + unionid);
                        newEx.ErrorCode = (int)LDAP_ERROR_CODE.ITEM_NOT_FOUND;
                        throw newEx;
                    }
                    return entries[0].Attributes;
                }
                catch (Exception ex)

                {
                    Logger.Info(ex.Message + " " + ex.StackTrace);
                    throw;
                }

            }
        }

        public static int getIdByName(string name)
        {
            var member = NameMatchedUser(name);
            var id = 0;
            if (member["employeeNumber"] != null)
            {
                id = int.Parse(member["employeeNumber"][0]);
            }
            return id;
        }

        public static DateTime getAddTimeByName(string name)
        {
            var member = NameMatchedUser(name);
            DateTime dt = DateTime.Now;
            if (member["initials"] != null)
            {
                DateTime.TryParseExact(member["initials"][0], "yyyy/MM/dd-HH:mm:ss", new System.Globalization.DateTimeFormatInfo(), System.Globalization.DateTimeStyles.None, out dt);
            }
            return dt;
        }
        public static bool mobileExist(string mobile)
        {
            using (var cn = createConnection())
            {
                try
                {
                    var entries = cn.Search("dc=gaorongvc,dc=cn", string.Format("(mobile={0})", mobile), LdapSearchScope.LDAP_SCOPE_SUBTREE);
                    if (entries.Count >= 1)
                    {
                        return true;
                    }
                    return false;
                }
                catch (Exception ex)

                {
                    Logger.Info(ex.StackTrace);
                    return false;
                }

            }
        }
        public static Dictionary<string, List<string>> IdMatchedUser(int employeeNumber)
        {
            using (var cn = createConnection())
            {
                try
                {
                    string searchStr = string.Format("(employeeNumber={0})", employeeNumber); // (|(cn={0})(uid={0})(mobile={0})(mail={0})(sn={0}))
                    var entries = getAttributesByEmployeeNumber(cn, employeeNumber, "cn", "mobile", "memberOf", "employeeNumber", "labeledURI", "o", "initials", "street");

                    if (entries.Count > 1)
                    {
                        var newEx = new LdapException("ldap出现两个相同employeeNumber的人" + employeeNumber);
                        newEx.ErrorCode = (int)LDAP_ERROR_CODE.ITEM_DUPLICATE;
                        throw newEx;
                    }
                    else if (entries.Count == 0)
                    {
                        var newEx = new LdapException("不存在此用户id的人 employeeNumber:" + employeeNumber);
                        newEx.ErrorCode = (int)LDAP_ERROR_CODE.ITEM_NOT_FOUND;
                        throw newEx;
                    }
                    return entries[0].Attributes;
                }
                catch (Exception ex)

                {
                    Logger.Info(ex.Message + " " + ex.StackTrace);
                    throw;
                }

            }
        }
    }
}
