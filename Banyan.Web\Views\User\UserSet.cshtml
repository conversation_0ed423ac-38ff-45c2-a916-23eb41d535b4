﻿@model Banyan.Domain.Member
@{
    Layout = null;
    var roleList = (List<Banyan.Domain.Role>)ViewData["rolelist"];
    var model = ViewData["model"];
}

<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>高榕创投IMS-GAORONG VENTURES</title>

    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" href="~/content/img/favicons/favicon.ico">
    <link rel="icon" type="image/png" href="~/content/img/favicons/favicon.ico" sizes="16x16">
    <title>添加用户分类</title>
    <link type="text/css" href="/content/js/plugins/layui/css/layui.css" rel="stylesheet" />
    <style>
        .layui-form-label {
            padding: 9px 0;
        }

        .layui-input-block {
            margin-left: 90px;
        }
    </style>
</head>
<body>
    <div style="padding:50px 20px 20px">
        <form class="layui-form" id="user-form" style="display:none;">
            <template v-if="user">
                <div class="layui-form-item">
                    <label class="layui-form-label">用户类型：</label>
                    <div class="layui-input-block">
                        <select name="levels" class="layui-unselect layui-form-select" id="levels" lay-filter="utype">
                            <option value="4" v-bind:selected="user && user.Levels == 4">加入时间受限用户</option>
                            <option value="3" v-bind:selected="user && user.Levels == 3">受限用户</option>
                            <option value="0" v-bind:selected="user && user.Levels == 0">普通用户</option>
                            <option value="2" v-bind:selected="user && user.Levels == 2">超级用户</option>
                            <option value="1" v-bind:selected="user && user.Levels == 1">管理员</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item" v-show="showbox">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-block">
                        <template v-for="(item,index) in roles">
                            <input type="checkbox" v-bind:checked="item.UserSet" name="roleids" lay-skin="primary" :value="item.Id" :title="item.RoleName" />
                        </template>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn">保 存</button>
                    </div>
                </div>
            </template>
        </form>
    </div>
    <script type="text/javascript" src="~/Content/js/core/jquery.min.js"></script>
    <script type="text/javascript" src="~/content/js/plugins/layui/layui.js"></script>

    @*<link href="~/Content/js/plugins/select2/select2.min.css" rel="stylesheet" />
    <link href="~/Content/js/plugins/select2/select2-bootstrap.min.css" rel="stylesheet" />
    <script type="text/javascript" src="~/Content/js/plugins/select2/select2.min.js"></script>*@
    @*<link href="~/Content/js/plugins/nice-validator/jquery.validator.css" rel="stylesheet" />*@
    <script src="~/Content/js/plugins/nice-validator/jquery.validator.min.js"></script>
    <script src="~/Content/js/plugins/nice-validator/local/zh-CN.js"></script>

    <script type="text/javascript" src="/content/js/vue/vue.min.js"></script>

    <script type="text/javascript">
        //$(document).ready(function () {
        //    $('.select2').select2({
        //        language: "zh-CN",
        //        width: "100%",
        //        height: "32px",
        //        theme: "classic"
        //    });
        //    $('#levels').on('select2:select', function (data) {
        //        console.log('select')
        //        app.$data.usertype = parseInt(data.value || '0');
        //        app.$data.showbox = data.value == '0';
        //    })
        //});
        var layer, form;
        layui.use(['layer','form'], function () {
            layer = layui.layer;
            form = layui.form;

            form.on('select(utype)', function (data) {
                console.log('select')
                app.$data.usertype = parseInt(data.value || '0');
                //app.$data.showbox = data.value == '0';
            });
        });

        $(function () {
            $('#user-form').validator({
                theme: 'yellow_top_effect',
                timely: 2,
                stopOnError: true,
                fields: {
                    role: "required;",
                    name: "required;"
                },
                valid: function (form) {
                    console.log('valid')
                    var roleArr = [];
                    $("input[name='roleids']:checked").each(function (i) {//把所有被选中的复选框的值存入数组
                        roleArr[i] = $(this).val();
                    });

                    var pdata = {
                        id: app.$data.userid,
                        levels: app.$data.usertype,
                        realname: $('#uname').val(),
                        companyname: $('#name').val(),
                        telephone: $('#phone').val(),
                        groups: roleArr.join(',')
                    };

                    $.ajax({
                        type: 'post',
                        url: '@(Url.Action("membersave", "adminapi"))',
                        data: pdata,
                        success: function (data) {
                            if (data.code == 0) {
                                parent.layer.closeAll();
                                parent.location.reload();
                            } else {
                                layer.msg(data.msg);
                            }
                        },
                        error: function () {
                            layer.msg("很抱歉，请求异常！");
                        }
                    });
                }
            });
        });

        var app = new Vue({
            el: '#user-form',
            data: {
                userid:@(Model.Id),
                usertype: -1,
                user: null,
                roles: null,
                showbox: false,
            },
            methods: {
                login: function () {

                },
            },
            created: function () {
                $("#user-form").show();
                console.log("show")
                console.log(decodeURI('@(model)'));
                var that = this;
                that.user = JSON.parse(@(Model).ToString());
                that.roles = '@(Model.RoleName)';
                that.usertype = @(Model.Levels);
                that.showbox = that.usertype != 1;


                $.post('/adminapi/getuser', { userid: 0 }, function (data) {
                    if (data.code == 0) {
                        that.roles = data.data.roles;
                        that.usertype = that.user.Levels;
                        that.showbox = that.user.Levels !=1,

                        setTimeout(function () {
                            form.render();
                        }, 500);
                    } else {
                        parent.layer.closeAll();
                        parent.layer.msg(data.msg);
                    }
                });
            }
        });
    </script>
</body>
</html>
