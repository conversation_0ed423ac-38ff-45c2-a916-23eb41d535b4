﻿﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  有关如何配置 ASP.NET 应用程序的详细信息，请访问
  https://go.microsoft.com/fwlink/?LinkId=169433
-->
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
  </configSections>
  <appSettings>
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="PreserveLoginUrl" value="true" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="appKey" value="wx13a4685c552fa65f" />
    <add key="appSecret" value="5585b96303b020fa1a3b73b6785a866d" />
    <add key="appKeyWeb" value="wxe2c6ee8162e702fb" />
    <add key="appSecretWeb" value="7f6e78a4ec892990b9a8de573cdca956" />
    <add key="RedisPath" value="127.0.0.1:6380" />
    <add key="FileDomain" value="https://ims.gaorongvc.com" />
    <add key="ViewCountUpdateInterval" value="30" />
  </appSettings>
      <connectionStrings configProtectionProvider="RsaProtectedConfigurationProvider">
            <EncryptedData Type="http://www.w3.org/2001/04/xmlenc#Element"
                  xmlns="http://www.w3.org/2001/04/xmlenc#">
                  <EncryptionMethod Algorithm="http://www.w3.org/2001/04/xmlenc#tripledes-cbc" />
                  <KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#">
                        <EncryptedKey xmlns="http://www.w3.org/2001/04/xmlenc#">
                              <EncryptionMethod Algorithm="http://www.w3.org/2001/04/xmlenc#rsa-1_5" />
                              <KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#">
                                    <KeyName>Rsa Key</KeyName>
                              </KeyInfo>
                              <CipherData>
                                    <CipherValue>yD6Fgct3bKARtt4yDneNYUxuzWxnd8ybwvabD6+CNrGmLSusLoOAJpJV86J6bjhOUvBOinv9o02cAZdX3t804jRI+o1YwJFBZluGs8XRElu0fJHLjWLkKN0DXQAjW5e4piMcZj+bdayWcxmAnRSftyYrUkZTa1Z6oRvGhQJbeSRJjCSh6xaR1KPt78p6VzsXFxAXTqBfEOiev+SnUyLIgYApvidWx6vMusmkNPlydP/DDIud14nbDoDeP9pCEgc6FLd6qg/M2gcQlBFdRAwQQ4WsCgRO50x+O7g941K7DCaUdlEbNvIAHA6f1qTIhduq2fvJN5vMNbc80f2s6sozfQ==</CipherValue>
                              </CipherData>
                        </EncryptedKey>
                  </KeyInfo>
                  <CipherData>
                        <CipherValue>si1/HynSI55Y54/VrXbH5iiaqhZ/iXDAa7+UYXIAWPSsbchAhRuCO/EC/L1eqC33I15yTQUdWBDsBynqKfRH9A18W+e0zLRIjj3PjX+vqyutIjHBDN1ozswPgEqRxNKNmZDxmq/SuT5krjvKOO9C9q93ADrKJa4lrkYqI8a37EAgAoZJzy6NlYDUVvA/jkF11v6ga6TSbex8X8N2xS5Bozt3tvtnVdKjemy7F1jsuRup+LIKBRJFLdcEvxWGYOa/8dc+GW0Vwo8jFZSu5KHN6p7s/ykBVU3dhqQQCfL34rSZwncSNepE7bO80oGNycFX2cWvpGTulX+snLStk2a6ZKjwyka01nE1EDaZ0x0E9Q7llOHfUJJ9NBEn/+9Cns6GotHg2osP8nNt0/tD0zYwnsojSvCWOOnKU/YVU7ZtbLfqw2d6YgGXlvBitoB66wwGkZUcDvMu69JMDLLe5KiqFSW9IUT0iQ6jHWHpAS/fGRXI3XiAFy5s0U34RjxWlMN9Qp3OwT8CF6hFONvn4kgBQFzZeLwUoQydY3arUiwWhQzmUllAKjgVcH9ZGLfqQRJ2RdsSfpoe6EzQcJYU08KFemfzR6YbnefUfF5GSTlODkXMN4Wtl+Sgg6nD2kUuugwp9pnzTobu9qtQzBOOSyaojGtnXfig72KMgad7S1l4OmpzJD3UcrFkn/CDV0fHZLdye0lzTXSlDw836pKMDK5Vbwv7a9C6fcpU/3WsaKfKJtwH9vHgl4dimAxn+QkGthxTB6wPbuFvXgWET1a2jNUihC5EH0oTRI+k7NIX0WZQBIllHIpCUZgA3ZbxhepJUHBewAHbJNr7i9WMbi0zn1Cmy4Pzq0Kmx4jOEFScyEcPsf2JUbXlOEBdJngkjXEapO949YGm5b3NPProf+kn79gjwOn7mNKW/IQqhEK8tLBysgATrvcBLNZ/0ojF/c3t0ivIyE/HNTyba/gY3/2gGotku9yxfnPIdFkhWSWt4nOLjs0+/WPhrjgmKbje5f5lIoZ/q2aTo87PDsgHJI3JMqah//NHPMpoY8EQN0N7wy8pyqWauw3Kd6q8omDxIuU/y6dUF0DIxXzQJLgRwtj7FT28Cn+t+kDkvFOZ5wYHXByP4VLN5nMFM9sLwopL5de/NzWc/4G6km3XSIGDt44IsAGhILDOLb6PpAxKfC9TnOMZ8L02XSrkJrZpgxx+z6Z35oVGsTHIg13jRAzAscQ/rB+lpmadCShI4tmS+A3UlQRGN3dp/hhu12zfNReIlYFFosuA171L4pFY5sU3Q7jCZYZdADSHzhrHVUIshvP4v3QPB6g=</CipherValue>
                  </CipherData>
            </EncryptedData>
      </connectionStrings>
  <system.web>
    <customErrors mode="Off"/>
    <compilation targetFramework="4.6.1"/>
    <httpRuntime targetFramework="4.6.1" requestValidationMode="2.0" maxRequestLength="2097151"/>
    <pages>
      <namespaces>
        <add namespace="System.Web.Helpers"/>
        <add namespace="System.Web.Mvc"/>
        <add namespace="System.Web.Mvc.Ajax"/>
        <add namespace="System.Web.Mvc.Html"/>
        <add namespace="System.Web.Routing"/>
        <add namespace="System.Web.WebPages"/>
      </namespaces>
    </pages>
    <httpModules>
      <add name="TelemetryCorrelationHttpModule"
        type="Microsoft.AspNet.TelemetryCorrelation.TelemetryCorrelationHttpModule, Microsoft.AspNet.TelemetryCorrelation"/>
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web"/>
    </httpModules>
  </system.web>
  <system.web.extensions>
    <scripting>
      <webServices>
        <!--单位为字节-->
        <jsonSerialization maxJsonLength="********"/>
      </webServices>
    </scripting>
  </system.web.extensions>
  <system.webServer>
    <security>
      <requestFiltering>
        <!-- 1 GB -->
        <requestLimits maxAllowedContentLength="**********"/>
      </requestFiltering>
    </security>
    <!--<staticContent>
      <mimeMap fileExtension=".woff" mimeType="application/x-font-woff" />
      <mimeMap fileExtension=".woff2" mimeType="application/x-font-woff" />
    </staticContent>-->
    <validation validateIntegratedModeConfiguration="false"/>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0"/>
      <remove name="OPTIONSVerbHandler"/>
      <remove name="TRACEVerbHandler"/>
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler"
        preCondition="integratedMode,runtimeVersionv4.0"/>
    </handlers>
    <asp scriptErrorSentToBrowser="true"/>
    <httpErrors errorMode="Detailed">
      <remove statusCode="403" subStatusCode="-1"/>
      <error statusCode="402" path="https://ims.gaorongvc.com" responseMode="Redirect"/>
      <error statusCode="403" prefixLanguageFilePath="" path="https://ims.gaorongvc.com" responseMode="Redirect"/>
    </httpErrors>
 
    <rewrite>
      <rules>
        <rule name="tohttps" stopProcessing="true">
          <match url="(.*)"/>
          <conditions>
            <add input="{HTTPS}" pattern="^OFF$"/>
          </conditions>
          <action type="Redirect" url="https://{HTTP_HOST}/{R:1}"/>
        </rule>
      </rules>
    </rewrite>

    <modules>
      <remove name="TelemetryCorrelationHttpModule"/>
      <add name="TelemetryCorrelationHttpModule"
        type="Microsoft.AspNet.TelemetryCorrelation.TelemetryCorrelationHttpModule, Microsoft.AspNet.TelemetryCorrelation" preCondition="managedHandler"/>
      <remove name="ApplicationInsightsWebTracking"/>
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web"
        preCondition="managedHandler"/>
    </modules>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed"/>
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-5.2.0.0" newVersion="5.2.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.1" newVersion="4.0.1.1"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-2.0.8.0" newVersion="2.0.8.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.ApplicationInsights" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-2.13.1.12554" newVersion="2.13.1.12554"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <log4net>
    <root>
      <level value="ALL"/>
      <appender-ref ref="aiAppender"/>
    </root>
    <appender name="aiAppender" type="Microsoft.ApplicationInsights.Log4NetAppender.ApplicationInsightsAppender, Microsoft.ApplicationInsights.Log4NetAppender">
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%message%newline"/>
      </layout>
    </appender>
  </log4net>
</configuration>
<!--ProjectGuid: 06C08EAD-C0C4-44E7-952E-BF0CBB5546C7-->
