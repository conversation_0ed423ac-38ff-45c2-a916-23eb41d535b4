﻿using Banyan.Apps;
using System.Web.Mvc;

namespace Banyan.Web.Controllers
{
    public class StaffKPIController : BaseController
    {
        public ActionResult StaffKPI(string name = "")
        {
            ViewBag.Name = name;
            var memberlist = new MemberBLL().GetAllList();
            ViewData["memberlist"] = memberlist;
            return View();
        }

        public ActionResult Preview(string id = "")
        {
            ViewBag.Id = id;
            return View();
        }
    }
}