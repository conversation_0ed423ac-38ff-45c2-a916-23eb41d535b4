using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public partial class ProjectMemo
    {
        public ProjectMemo() { }

        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        [SqlField]
        public int UserId { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        [SqlField]
        public int ProjectId { get; set; }

        /// <summary>
        /// memo
        /// </summary>
        [SqlField]
        public string Memo { get; set; } = string.Empty;
        /// <summary>
        /// memo
        /// </summary>
        [SqlField]
        public string Type { get; set; } = string.Empty;
        /// <summary>
        /// 添加时间
        /// </summary>
        [SqlField]
        public DateTime AddTime { get; set; } = DateTime.Now;
        /// <summary>
        /// memo时间
        /// </summary>
        [Sql<PERSON><PERSON>]
        public DateTime PubTime { get; set; } = DateTime.Now;
        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; }
    }
}
