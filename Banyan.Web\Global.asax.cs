﻿using Banyan.Code;
using Microsoft.ApplicationInsights.Extensibility;
using System;
using System.Diagnostics;
using System.IO;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;
using Banyan.Domain;
using Banyan.Apps;
namespace Banyan.Web
{
    public class Global : HttpApplication
    {
        void Application_Start(object sender, EventArgs e)
        {
            DisableApplicationInsightsOnDebug();
            AreaRegistration.RegisterAllAreas();
            RouteConfig.RegisterRoutes(RouteTable.Routes);

            this.StartLog4net();

            // 初始化新闻向量化调度器
            InitializeNewsVectorizationScheduler();
            
            // 初始化推荐调度器
            InitializeRecommendationScheduler();
        }
        
        /// <summary>
        /// 初始化新闻向量化调度器
        /// </summary>
        private void InitializeNewsVectorizationScheduler()
        {
            try
            {
                // 使用单独的线程初始化调度器，避免阻塞应用程序启动
                System.Threading.ThreadPool.QueueUserWorkItem(_ =>
                {
                    try
                    {
                        // 延迟几秒，确保应用程序完全启动
                        System.Threading.Thread.Sleep(5000);
                        
                        // 初始化调度器
                        NewsVectorizationSchedulerManager.Initialize();
                        
                        Logger.Info("新闻向量化调度器已在应用程序启动时初始化");
                    }
                    catch (Exception ex)
                    {
                        Logger.Error("应用程序启动时初始化新闻向量化调度器失败", ex);
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Error("启动新闻向量化调度器线程失败", ex);
            }
        }
        
        /// <summary>
        /// 初始化推荐调度器
        /// </summary>
        private void InitializeRecommendationScheduler()
        {
            try
            {
                // 使用单独的线程初始化调度器，避免阻塞应用程序启动
                System.Threading.ThreadPool.QueueUserWorkItem(async _ =>
                {
                    try
                    {
                        // 延迟10秒，确保应用程序和新闻向量化调度器完全启动
                        await System.Threading.Tasks.Task.Delay(10000);
                        
                        // 启动推荐调度器，默认每6小时更新一次推荐
                        var scheduler = RecommendationScheduler.Instance;
                        bool started = scheduler.Start(6);
                        
                        if (started)
                        {
                            Logger.Info("推荐调度器已在应用程序启动时成功启动，更新间隔: 6小时");
                        }
                        else
                        {
                            Logger.Error("推荐调度器启动失败");
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Error("应用程序启动时初始化推荐调度器失败", ex);
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Error("启动推荐调度器线程失败", ex);
            }
        }

        void Application_End(object sender, EventArgs e)
        {
            try
            {
                // 停止推荐调度器
                var scheduler = RecommendationScheduler.Instance;
                if (scheduler.IsRunning)
                {
                    bool stopped = scheduler.Stop();
                    Logger.Info($"推荐调度器已停止: {stopped}");
                }
                
                // 停止新闻向量化调度器
                NewsVectorizationSchedulerManager.Shutdown();
                
                Logger.Info("应用程序关闭，所有调度器已停止");
            }
            catch (Exception ex)
            {
                Logger.Error("应用程序关闭时停止调度器失败", ex);
            }
        }

        protected void Application_Error(object sender, EventArgs e)
        {
            Member user = new MemberBLL().GetLogOnUser();
            var name = "null";
            if (user != null)
            {
                name = user.RealName;
            }
            Exception exception = Server.GetLastError().GetBaseException();
            //Logger.Info(exception.StackTrace);
            Console.WriteLine(exception);
            try
            {
                if (exception.Message.Contains("was not found or does not implement IController.")||
                    exception.Message.Contains("explicit_not_exist_path") || this.Request.Url.AbsoluteUri.EndsWith("NULL"))
                {
                    Logger.Info(this.Request.Url.AbsoluteUri + ": " + exception.Message, exception, name);
                }
                else 
                {
                    Logger.Error(this.Request.Url.AbsoluteUri + ": " + exception.Message, exception, name);
                 }
            } catch(Exception)
            {
                Logger.Error(exception.Message, exception, name);
            }
            Server.ClearError();
        }

        /// <summary>
        /// Disables the application insights locally.
        /// </summary>
        [Conditional("DEBUG")]
        private static void DisableApplicationInsightsOnDebug()
        {
            TelemetryConfiguration.Active.DisableTelemetry = true;
        }
        private void StartLog4net()
        {
            string filePath = HttpRuntime.AppDomainAppPath.ToString() + "Configs\\Log4net.config";
            if (!File.Exists(filePath))
            {
                return;
            }
            FileInfo fileInfo = new FileInfo(filePath);
            log4net.Config.XmlConfigurator.ConfigureAndWatch(fileInfo);
            log4net.ThreadContext.Properties["trace"] = Guid.NewGuid();
        }
    }
}