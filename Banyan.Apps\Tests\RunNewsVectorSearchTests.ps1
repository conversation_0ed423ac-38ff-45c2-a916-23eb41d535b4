# NewsVectorSearch 测试运行脚本
# 用于运行 NewsVectorSearch 相关的单元测试和集成测试

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("Unit", "Integration", "All")]
    [string]$TestType = "All",
    
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = "TestResults",
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose
)

Write-Host "=== NewsVectorSearch 测试运行器 ===" -ForegroundColor Green
Write-Host "测试类型: $TestType" -ForegroundColor Yellow
Write-Host "输出路径: $OutputPath" -ForegroundColor Yellow
Write-Host ""

# 确保输出目录存在
if (!(Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-Host "创建输出目录: $OutputPath" -ForegroundColor Green
}

# 获取当前时间戳
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

# 构建测试命令
$testAssembly = "Banyan.Apps.dll"
$testResultFile = Join-Path $OutputPath "NewsVectorSearch_TestResults_$timestamp.trx"

# 根据测试类型设置过滤器
$testFilter = switch ($TestType) {
    "Unit" { "TestCategory=Unit|FullyQualifiedName~NewsVectorSearchTests" }
    "Integration" { "TestCategory=Integration|FullyQualifiedName~NewsVectorSearchIntegrationTests" }
    "All" { "FullyQualifiedName~NewsVectorSearch" }
}

Write-Host "开始运行测试..." -ForegroundColor Yellow

try {
    # 检查是否安装了 MSTest
    $vsTestPath = Get-Command "vstest.console.exe" -ErrorAction SilentlyContinue
    if (-not $vsTestPath) {
        Write-Host "未找到 vstest.console.exe，尝试使用 dotnet test..." -ForegroundColor Yellow
        
        # 使用 dotnet test
        $dotnetArgs = @(
            "test"
            "--logger", "trx;LogFileName=$testResultFile"
            "--verbosity", $(if ($Verbose) { "detailed" } else { "normal" })
        )
        
        if ($TestType -ne "All") {
            $dotnetArgs += "--filter", $testFilter
        }
        
        & dotnet @dotnetArgs
        $testExitCode = $LASTEXITCODE
    }
    else {
        # 使用 VSTest
        $vstestArgs = @(
            $testAssembly
            "/Logger:trx;LogFileName=$testResultFile"
            "/TestCaseFilter:$testFilter"
        )
        
        if ($Verbose) {
            $vstestArgs += "/Diag:vstest_$timestamp.log"
        }
        
        & $vsTestPath.Source @vstestArgs
        $testExitCode = $LASTEXITCODE
    }
    
    Write-Host ""
    
    if ($testExitCode -eq 0) {
        Write-Host "✅ 所有测试通过!" -ForegroundColor Green
    }
    else {
        Write-Host "❌ 部分测试失败 (退出代码: $testExitCode)" -ForegroundColor Red
    }
    
    # 显示测试结果文件位置
    if (Test-Path $testResultFile) {
        Write-Host "测试结果文件: $testResultFile" -ForegroundColor Cyan
        
        # 尝试解析测试结果
        try {
            [xml]$testResults = Get-Content $testResultFile
            $testRun = $testResults.TestRun
            
            Write-Host ""
            Write-Host "=== 测试摘要 ===" -ForegroundColor Green
            Write-Host "总计: $($testRun.ResultSummary.Counters.total)" -ForegroundColor White
            Write-Host "通过: $($testRun.ResultSummary.Counters.passed)" -ForegroundColor Green
            Write-Host "失败: $($testRun.ResultSummary.Counters.failed)" -ForegroundColor Red
            Write-Host "跳过: $($testRun.ResultSummary.Counters.skipped)" -ForegroundColor Yellow
            
            if ($testRun.ResultSummary.Counters.failed -gt 0) {
                Write-Host ""
                Write-Host "=== 失败的测试 ===" -ForegroundColor Red
                $failedTests = $testRun.Results.UnitTestResult | Where-Object { $_.outcome -eq "Failed" }
                foreach ($test in $failedTests) {
                    Write-Host "- $($test.testName)" -ForegroundColor Red
                    if ($test.Output.ErrorInfo.Message) {
                        Write-Host "  错误: $($test.Output.ErrorInfo.Message)" -ForegroundColor DarkRed
                    }
                }
            }
        }
        catch {
            Write-Host "无法解析测试结果文件: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
    
    Write-Host ""
    Write-Host "=== 测试建议 ===" -ForegroundColor Cyan
    Write-Host "1. 单元测试应该快速运行且不依赖外部资源" -ForegroundColor White
    Write-Host "2. 集成测试需要真实的数据库和缓存连接" -ForegroundColor White
    Write-Host "3. 如果集成测试失败，请检查数据库连接和测试数据" -ForegroundColor White
    Write-Host "4. 建议在CI/CD中只运行单元测试，集成测试在专门环境中运行" -ForegroundColor White
    
    if ($TestType -eq "Integration") {
        Write-Host ""
        Write-Host "=== 集成测试注意事项 ===" -ForegroundColor Yellow
        Write-Host "- 确保数据库连接正常" -ForegroundColor White
        Write-Host "- 确保Redis缓存服务可用" -ForegroundColor White
        Write-Host "- 测试会创建和清理临时数据" -ForegroundColor White
        Write-Host "- 不要在生产环境运行集成测试" -ForegroundColor Red
    }
}
catch {
    Write-Host "运行测试时发生错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "测试运行完成。" -ForegroundColor Green
exit $testExitCode
