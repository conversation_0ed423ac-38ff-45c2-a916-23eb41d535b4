﻿@{
    Layout = "/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li>用户分类管理</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <div class="tab-pane active">
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="pull-right search-bar"></div>
                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                                <div class="form-group">
                                    <select class="form-control" id="state" name="state" size="1">
                                        <option value="">全部</option>
                                        <option value="1">启用</option>
                                        <option value="0">停用</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="title-name">分类名称</label>
                                    <input class="form-control" type="text" id="classname" name="classname" placeholder="请输入分类名称">
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-default" id="dosearch">搜索</button>
                                    <a class="btn btn-primary" href="javascript:;" onclick="parent.openmodal('新建用户分类','@(Url.Action("RoleSet","Classify"))','400px','570px')"><i class="fa fa-plus"></i>&nbsp;&nbsp;新建分类</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <table class="layui-hide" id="table-list" lay-filter="list-filter"></table>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="bartpl">
    <button class="btn btn-xs {{d.Status == 1 ? 'btn-warning' : 'btn-default'}}" type="button" title="{{d.Status == 1 ? '停用' : '启用'}}" lay-event="enable"><i class="fa fa-toggle-off"></i></button>
    <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="编辑" lay-event="modify" data-original-title="编辑"><i class="fa fa-pencil"></i></button>
    <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="删除" lay-event="delete" data-original-title="删除"><i class="fa fa-times"></i></button>
    <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="排序" lay-event="sort" data-original-title="排序"><i class="fa fa-sort-amount-asc"></i></button>
</script>

@section scripts{
    <script type="text/javascript">
        var layer, date = new Date();
        layui.use(['laypage', 'layer', 'table'], function () {
            var laypage = layui.laypage,
                table = layui.table;
            layer = layui.layer;

            table.render({
                elem: '#table-list'
                , height: 700
                , url: '@(Url.Action("rolelist", "adminapi"))'
                , page: true
                , method: 'post'
                , cols: [[
                    { field: 'Id', title: 'ID', width: 80, sort: true, fixed: 'left' }
                    , { field: 'RoleName', title: '分类名称'}
                    , {
                        field: 'Status', title: '状态', width: 160, templet: function (d) {
                            var statusHtml = '<span class="label label-success">已启用</span>';
                            if (d.Status != 1) {
                                statusHtml = '<span class="label label-danger">已禁用</span>';
                            }
                            return statusHtml;
                        }
                    }
                    , { field: 'Sort', title: '排序', width: 120 }
                    , { field: 'AddTime', title: '创建时间', width: 160, templet: function (d) {
                        return (new Date(parseInt(d.AddTime.replace("/Date(", "").replace(")/", "").split("+")[0]))).pattern("yyyy-MM-dd HH:mm:ss");
                      }}
                    , { fixed: 'right', width: 160, align: 'center', toolbar: '#bartpl' }
                ]]
            });

            table.on('tool(list-filter)', function (obj) {
                var data = obj.data
                    , layEvent = obj.event;

                if (layEvent === 'enable') {
                    fieldset(data.Id, 'enable', data.Status);
                } else if (layEvent === 'delete') {
                    layer.confirm('确认删除该分类吗？', function (index) {
                        layer.close(index);
                        fieldset(data.Id, 'delete', data.Status)
                    });
                } else if (layEvent === 'modify') {
                    parent.openmodal('编辑用户分类','@(Url.Action("roleset", "classify"))?id=' + data.Id,'400px','570px')
                } else if (layEvent === 'recomend') {
                    fieldset(data.Id, 'recomend', data.IsRecommend ? 1 : 0);
                } else if (layEvent === 'top') {
                    fieldset(data.Id, 'top', data.IsStick ? 1 : 0);
                } else if (layEvent === 'sort') {
                    layer.prompt({ title: '排序值设置' }, function (text, index) {
                        if (!/^(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9])$/.test(text)) {
                            layer.msg('请输入0-255的正整数！');
                            return;
                        }
                        layer.close(index);
                        fieldset(data.Id, 'sort', parseInt(text));
                    });
                }
                return;
            });

            laypage.render({
                elem: 'pageBar'
                , count: 100
                , jump: function (obj, first) {
                    if (!first) {
                        layer.msg('第' + obj.curr + '页');
                    }
                }
            });

            $('#dosearch').on('click', function () {
                queryParams = {
                    state: $('#state').val(),
                    title: $('#classname').val(),
                }
                table.reload('table-list', {
                    where: queryParams,
                });
            });
        });

        function fieldset(id, field, state) {
            $.ajax({
                type: 'POST',
                url: '@(Url.Action("roleset", "adminapi"))',
                data: {id: id, field: field, state: state },
                success: function (data) {
                    if (data.code == 0) {
                        layer.msg('操作成功！');
                        $('#dosearch').click();
                    } else {
                        layer.msg(data.msg);
                    }
                },
                error: function () {
                    layui.layer.msg("很抱歉，请求异常！");
                }
            });
        }
    </script>
}
