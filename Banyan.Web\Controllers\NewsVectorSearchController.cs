using Banyan.Apps;
using Banyan.Apps.Configs;
using Banyan.Code;
using Banyan.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace Banyan.Web.Controllers
{
    /// <summary>
    /// 新闻向量搜索控制器
    /// 提供基于向量相似度的新闻搜索API
    /// </summary>
    public class NewsVectorSearchController : BaseController
    {
        /// <summary>
        /// 新闻向量搜索页面
        /// </summary>
        /// <returns>搜索页面</returns>
        public ActionResult Index()
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return RedirectToAction("Index", "Login");
                }

                ViewData["manager"] = user;
                return View();
            }
            catch (Exception ex)
            {
                Logger.Error($"显示新闻向量搜索页面失败，错误: {ex.Message}", ex);
                return RedirectToAction("NewsVector", "Index");
            }
        }
        
        /// <summary>
        /// 个性化新闻推荐页面
        /// </summary>
        /// <returns>推荐页面</returns>
        public ActionResult Recommendations()
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return RedirectToAction("Index", "Login");
                }

                ViewData["manager"] = user;
                return View();
            }
            catch (Exception ex)
            {
                Logger.Error($"显示个性化新闻推荐页面失败，错误: {ex.Message}", ex);
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// 根据文本搜索相似新闻
        /// </summary>
        /// <param name="query">查询文本</param>
        /// <param name="limit">返回结果数量限制</param>
        /// <param name="threshold">相似度阈值（0-1之间）</param>
        /// <param name="category">新闻分类过滤</param>
        /// <param name="source">新闻来源过滤</param>
        /// <param name="startDate">开始日期过滤</param>
        /// <param name="endDate">结束日期过滤</param>
        /// <param name="tagFilter">标签过滤</param>
        /// <returns>相似新闻列表</returns>
        public async Task<JsonResult> SearchByText(
            string query,
            int limit = 10,
            double threshold = 0.5,
            string category = null,
            string source = null,
            string startDate = null,
            string endDate = null,
            string tagFilter = null)
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" }, JsonRequestBehavior.AllowGet);
                }

                if (string.IsNullOrEmpty(query))
                {
                    return Json(new { code = 1, msg = "查询文本不能为空" }, JsonRequestBehavior.AllowGet);
                }

                // 构建过滤条件
                var filters = new NewsSearchFilters
                {
                    Category = category,
                    Source = source,
                    Tag = tagFilter
                };

                // 解析日期
                if (!string.IsNullOrEmpty(startDate) && DateTime.TryParse(startDate, out DateTime parsedStartDate))
                {
                    filters.StartDate = parsedStartDate;
                }

                if (!string.IsNullOrEmpty(endDate) && DateTime.TryParse(endDate, out DateTime parsedEndDate))
                {
                    filters.EndDate = parsedEndDate;
                }

                // 执行搜索
                var newsBLL = new NewsBLL();
                var result = await newsBLL.SearchSimilarNewsByTextAsync(query, limit, threshold, filters);

                // 记录日志
                Logger.Info($"用户 {user.RealName} 执行了文本相似度搜索，查询: {query}，结果数量: {(result.data as List<NewsVectorSimilarity>)?.Count ?? 0}");

                return Json(result, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error($"文本相似度搜索失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "搜索失败: " + ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 获取与特定新闻相似的新闻
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <param name="limit">返回结果数量限制</param>
        /// <param name="threshold">相似度阈值（0-1之间）</param>
        /// <param name="category">新闻分类过滤</param>
        /// <param name="source">新闻来源过滤</param>
        /// <param name="startDate">开始日期过滤</param>
        /// <param name="endDate">结束日期过滤</param>
        /// <param name="tagFilter">标签过滤</param>
        /// <returns>相似新闻列表</returns>
        public async Task<JsonResult> GetSimilar(
            int newsId,
            int limit = 10,
            double threshold = 0.5,
            string category = null,
            string source = null,
            string startDate = null,
            string endDate = null,
            string tagFilter = null)
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" }, JsonRequestBehavior.AllowGet);
                }

                if (newsId <= 0)
                {
                    return Json(new { code = 1, msg = "新闻ID无效" }, JsonRequestBehavior.AllowGet);
                }

                // 构建过滤条件
                var filters = new NewsSearchFilters
                {
                    Category = category,
                    Source = source,
                    Tag = tagFilter
                };

                // 解析日期
                if (!string.IsNullOrEmpty(startDate) && DateTime.TryParse(startDate, out DateTime parsedStartDate))
                {
                    filters.StartDate = parsedStartDate;
                }

                if (!string.IsNullOrEmpty(endDate) && DateTime.TryParse(endDate, out DateTime parsedEndDate))
                {
                    filters.EndDate = parsedEndDate;
                }

                // 执行搜索
                var newsBLL = new NewsBLL();
                var result = await newsBLL.GetSimilarNewsAsync(newsId, limit, threshold, filters);

                // 记录日志
                Logger.Info($"用户 {user.RealName} 获取了与新闻ID {newsId} 相似的新闻，结果数量: {(result.data as List<NewsVectorSimilarity>)?.Count ?? 0}");

                return Json(result, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error($"获取相似新闻失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "获取相似新闻失败: " + ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 获取新闻分类列表
        /// </summary>
        /// <returns>分类列表</returns>
        public JsonResult GetCategories()
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" }, JsonRequestBehavior.AllowGet);
                }

                // 获取所有新闻分类
                var categories = new List<string> { "资讯", "融资", "新股", "政策新闻", "热门" };

                return Json(new { code = 0, data = categories }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error($"获取新闻分类列表失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "获取分类列表失败: " + ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 获取新闻来源列表
        /// </summary>
        /// <returns>来源列表</returns>
        public JsonResult GetSources()
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" }, JsonRequestBehavior.AllowGet);
                }

                // 从数据库获取所有新闻来源
                var newsBLL = new NewsBLL();
                var sources = newsBLL.GetDistinctValues("Source");

                return Json(new { code = 0, data = sources }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error($"获取新闻来源列表失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "获取来源列表失败: " + ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 获取新闻标签列表
        /// </summary>
        /// <returns>标签列表</returns>
        public JsonResult GetTags()
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" }, JsonRequestBehavior.AllowGet);
                }

                // 从数据库获取所有新闻标签
                var newsBLL = new NewsBLL();
                var tags = newsBLL.GetDistinctValues("Tag");

                return Json(new { code = 0, data = tags }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error($"获取新闻标签列表失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "获取标签列表失败: " + ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }
        


        /// <summary>
        /// 记录用户阅读新闻
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <returns>操作结果</returns>
        public async Task<JsonResult> RecordRead(int newsId)
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return Json(new { code = 1, msg = "用户未登录" }, JsonRequestBehavior.AllowGet);
                }

                if (newsId <= 0)
                {
                    return Json(new { code = 1, msg = "新闻ID无效" }, JsonRequestBehavior.AllowGet);
                }

                // 记录阅读
                var newsVectorSearch = new NewsVectorSearch();
                var success = await newsVectorSearch.RecordUserReadNewsAsync(user.Id, newsId);

                // 记录日志
                Logger.Info($"用户 {user.RealName} 阅读了新闻 {newsId}，记录结果: {success}");

                return Json(new { code = 0, success = success }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error($"记录用户阅读新闻失败: {ex.Message}", ex);
                return Json(new { code = 1, msg = "记录阅读失败: " + ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 相似新闻页面
        /// </summary>
        /// <param name="id">新闻ID</param>
        /// <returns>相似新闻页面</returns>
        public ActionResult SimilarNews(int id)
        {
            try
            {
                var user = new MemberBLL().GetLogOnUser();
                if (user == null)
                {
                    return RedirectToAction("Index", "Login");
                }

                // 检查新闻是否存在
                var newsBLL = new NewsBLL();
                var news = newsBLL.GetModel(id);
                if (news == null)
                {
                    return RedirectToAction("Index");
                }

                ViewBag.NewsId = id;
                ViewBag.NewsTitle = news.Title;
                
                return View();
            }
            catch (Exception ex)
            {
                Logger.Error($"显示相似新闻页面失败，新闻ID: {id}，错误: {ex.Message}", ex);
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// 计算推荐评分
        /// </summary>
        /// <param name="item">推荐项</param>
        /// <param name="tags">标签列表</param>
        /// <param name="userId">用户ID</param>
        /// <returns>评分结果</returns>
        private RecommendationScores CalculateRecommendationScores(NewsVectorSimilarity item, List<object> tags, int userId)
        {
            try
            {
                // 1. 向量相似度评分 (0-100)
                // 使用非线性变换增加区分度
                var rawSimilarity = Math.Max(0, Math.Min(1, item.Similarity));
                var vectorSimilarity = TransformSimilarityScore(rawSimilarity);

                // 2. 标签匹配评分 (0-100) - 同时获取匹配的标签信息
                var tagMatchResult = CalculateTagMatchScoreWithDetails(tags, userId);
                var tagMatchScore = tagMatchResult.Score;

                // 3. 时效性评分 (0-100) - 
                var timelinessScore = CalculateTimelinessScore(item.News?.PubTime);

                // 4. 内容质量评分 (0-100)
                var qualityScore = CalculateContentQualityScore(item.News);

                // 5. 综合评分计算 (重新调整权重，去除时效性影响)
                // 使用配置类中的权重
                var finalScore =
                    vectorSimilarity * Banyan.Apps.Configs.RecommendationScoringConfig.VECTOR_SIMILARITY_WEIGHT +
                    tagMatchScore * Banyan.Apps.Configs.RecommendationScoringConfig.TAG_MATCH_WEIGHT +
                    timelinessScore * Banyan.Apps.Configs.RecommendationScoringConfig.TIMELINESS_WEIGHT +
                    qualityScore * Banyan.Apps.Configs.RecommendationScoringConfig.QUALITY_WEIGHT;

                return new RecommendationScores
                {
                    VectorSimilarity = Math.Round((double)vectorSimilarity, 2),
                    TagMatchScore = Math.Round((double)tagMatchScore, 2),
                    TimelinessScore = Math.Round((double)timelinessScore, 2),
                    QualityScore = Math.Round((double)qualityScore, 2),
                    FinalScore = Math.Round((double)finalScore, 2),
                    MatchedTagCount = tagMatchResult.MatchedTags.Count,
                    MatchedTags = tagMatchResult.MatchedTags
                };
            }
            catch (Exception ex)
            {
                Logger.Error($"计算推荐评分失败: {ex.Message}", ex);

                // 返回基础评分
                var basicScore = Math.Round(Math.Max(0, Math.Min(1, item.Similarity)) * 60, 2);
                return new RecommendationScores
                {
                    VectorSimilarity = basicScore,
                    TagMatchScore = basicScore * 0.8,
                    TimelinessScore = 50,
                    QualityScore = 50,
                    FinalScore = basicScore,
                    MatchedTagCount = 0,
                    MatchedTags = new List<MatchedTagInfo>()
                };
            }
        }

        /// <summary>
        /// 变换相似度分数以增加区分度
        /// </summary>
        /// <param name="rawSimilarity">原始相似度 (0-1)</param>
        /// <returns>变换后的分数 (0-100)</returns>
        private double TransformSimilarityScore(double rawSimilarity)
        {
            // 使用S型曲线变换，增加中等相似度的区分度
            // 公式: score = 100 * (1 / (1 + exp(-k * (x - 0.5))))
            // 其中k控制曲线陡峭度，x是原始相似度

            var k = 8.0; // 调整这个值可以改变区分度
            var transformed = 1.0 / (1.0 + Math.Exp(-k * (rawSimilarity - 0.5)));

            // 映射到0-100范围，并增加一些随机性以避免完全相同的分数
            var score = transformed * 80 + 10; // 基础分数范围10-90

            // 添加基于内容的微调
            var contentAdjustment = (rawSimilarity * 1000) % 10 - 5; // -5到+5的微调
            score += contentAdjustment;

            return Math.Max(10, Math.Min(95, score));
        }

        /// <summary>
        /// 用户标签缓存
        /// </summary>
        private static readonly Dictionary<int, List<UserTagRelation>> _userTagsCache = new Dictionary<int, List<UserTagRelation>>();
        private static readonly Dictionary<int, Dictionary<int, UserInterestTag>> _userInterestTagsCache = new Dictionary<int, Dictionary<int, UserInterestTag>>();
        private static DateTime _lastCacheUpdate = DateTime.MinValue;
        private static readonly object _cacheLock = new object();

        /// <summary>
        /// 计算标签匹配评分（优化版）
        /// </summary>
        /// <param name="tags">新闻标签</param>
        /// <param name="userId">用户ID</param>
        /// <returns>标签匹配分数</returns>
        private double CalculateTagMatchScore(List<object> tags, int userId)
        {
            var result = CalculateTagMatchScoreWithDetails(tags, userId);
            return result.Score;
        }

        /// <summary>
        /// 计算标签匹配评分并返回详细匹配信息
        /// </summary>
        /// <param name="tags">新闻标签</param>
        /// <param name="userId">用户ID</param>
        /// <returns>标签匹配结果</returns>
        private TagMatchResult CalculateTagMatchScoreWithDetails(List<object> tags, int userId)
        {
            var result = new TagMatchResult();

            try
            {
                if (tags == null || tags.Count == 0)
                {
                    result.Score = 5; // 无标签时给予很低的基础分数
                    return result;
                }

                // 获取用户标签（使用缓存）
                var userTags = GetUserTagsFromCache(userId);
                var userInterestTags = GetUserInterestTagsFromCache(userId);

                if (userTags == null || userTags.Count == 0)
                {
                    result.Score = 10; // 用户无兴趣标签时给予较低的基础分数
                    return result;
                }

                double totalScore = 0;
                double maxPossibleScore = 0;

                // 计算标签匹配度
                foreach (dynamic newsTag in tags)
                {
                    var newsTagName = newsTag.name?.ToString()?.ToLower();
                    var newsTagWeight = Convert.ToDouble(newsTag.weight ?? 0);
                    var newsTagType = newsTag.type?.ToString() ?? "unknown";
                    var newsTagCategory = newsTag.category?.ToString() ?? "未分类";

                    if (string.IsNullOrEmpty(newsTagName)) continue;

                    // 查找匹配的用户标签（使用更严格的匹配逻辑）
                    var matchingUserTag = userTags.FirstOrDefault(ut =>
                    {
                        if (userInterestTags.TryGetValue(ut.TagId, out var tag))
                        {
                            var userTagName = tag.Name.ToLower().Trim();
                            var newsTagNameLower = newsTagName.Trim();

                            // 更严格的匹配条件：
                            // 1. 完全匹配
                            if (userTagName == newsTagNameLower) return true;

                            // 2. 包含匹配（但要求长度相近，避免过度匹配）
                            var lengthDiff = Math.Abs(userTagName.Length - newsTagNameLower.Length);
                            if (lengthDiff <= 2) // 长度差不超过2个字符
                            {
                                return userTagName.Contains(newsTagNameLower) || newsTagNameLower.Contains(userTagName);
                            }

                            // 3. 对于较长的标签，允许部分匹配
                            if (userTagName.Length >= 4 && newsTagNameLower.Length >= 4)
                            {
                                return userTagName.Contains(newsTagNameLower) || newsTagNameLower.Contains(userTagName);
                            }
                        }
                        return false;
                    });

                    if (matchingUserTag != null && userInterestTags.TryGetValue(matchingUserTag.TagId, out var userTag))
                    {
                        // 计算匹配分数：用户权重 * 新闻权重，并根据匹配类型调整
                        var userTagName = userTag.Name.ToLower().Trim();
                        var newsTagNameLower = newsTagName.Trim();

                        // 根据匹配精确度调整分数
                        double matchQuality = 1.0;
                        if (userTagName == newsTagNameLower)
                        {
                            matchQuality = 1.0; // 完全匹配，满分
                        }
                        else if (userTagName.Contains(newsTagNameLower) || newsTagNameLower.Contains(userTagName))
                        {
                            // 包含匹配，根据长度比例调整
                            var shorterLength = Math.Min(userTagName.Length, newsTagNameLower.Length);
                            var longerLength = Math.Max(userTagName.Length, newsTagNameLower.Length);
                            matchQuality = (double)shorterLength / longerLength; // 0.5-1.0之间
                        }

                        var baseMatchScore = matchingUserTag.Weight * newsTagWeight * 100;
                        var adjustedMatchScore = baseMatchScore * matchQuality;
                        totalScore += adjustedMatchScore;

                        // 添加匹配的标签信息，清理特殊字符
                        result.MatchedTags.Add(new MatchedTagInfo
                        {
                            UserTagName = CleanTagName(userTag.Name),
                            NewsTagName = CleanTagName(newsTag.name?.ToString()),
                            UserTagWeight = Math.Round(matchingUserTag.Weight, 3),
                            NewsTagWeight = Math.Round(newsTagWeight, 3),
                            MatchScore = Math.Round(adjustedMatchScore, 2),
                            NewsTagType = newsTagType,
                            NewsTagCategory = newsTagCategory
                        });
                    }

                    maxPossibleScore += newsTagWeight * 100;
                }

                // 计算最终分数
                if (maxPossibleScore > 0)
                {
                    var baseScore = (totalScore / maxPossibleScore) * 100;

                    // 根据匹配标签数量给予奖励
                    var matchBonus = Math.Min(result.MatchedTags.Count * 5, 20);

                    result.Score = Math.Max(15, Math.Min(90, baseScore + matchBonus));
                }
                else
                {
                    // 如果没有任何标签匹配，给予更低的基础分数
                    result.Score = result.MatchedTags.Count > 0 ? 25 : 5;
                }

                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"计算标签匹配评分失败: {ex.Message}", ex);
                result.Score = 30;
                return result;
            }
        }

        /// <summary>
        /// 从缓存获取用户标签
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户标签列表</returns>
        private List<UserTagRelation> GetUserTagsFromCache(int userId)
        {
            lock (_cacheLock)
            {
                // 缓存15分钟
                if (DateTime.Now - _lastCacheUpdate > TimeSpan.FromMinutes(15))
                {
                    _userTagsCache.Clear();
                    _userInterestTagsCache.Clear();
                    _lastCacheUpdate = DateTime.Now;
                }

                if (!_userTagsCache.TryGetValue(userId, out var userTags))
                {
                    var userTagRelationBLL = new UserTagRelationBLL();
                    userTags = userTagRelationBLL.GetList($"UserId = {userId}", 50, 1, "*", "Weight DESC");
                    _userTagsCache[userId] = userTags ?? new List<UserTagRelation>();
                }

                return userTags;
            }
        }

        /// <summary>
        /// 从缓存获取用户兴趣标签
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户兴趣标签字典</returns>
        private Dictionary<int, UserInterestTag> GetUserInterestTagsFromCache(int userId)
        {
            lock (_cacheLock)
            {
                if (!_userInterestTagsCache.TryGetValue(userId, out var userInterestTags))
                {
                    userInterestTags = new Dictionary<int, UserInterestTag>();
                    var userTags = GetUserTagsFromCache(userId);

                    if (userTags != null && userTags.Count > 0)
                    {
                        var userInterestTagBLL = new UserInterestTagBLL();
                        var tagIds = userTags.Select(ut => ut.TagId).Distinct().ToList();

                        // 批量获取标签信息
                        foreach (var tagId in tagIds)
                        {
                            var tag = userInterestTagBLL.GetModel(tagId);
                            if (tag != null)
                            {
                                userInterestTags[tagId] = tag;
                            }
                        }
                    }

                    _userInterestTagsCache[userId] = userInterestTags;
                }

                return userInterestTags;
            }
        }

        /// <summary>
        /// 计算时效性评分
        /// </summary>
        /// <param name="publishTime">发布时间</param>
        /// <returns>时效性分数</returns>
        private double CalculateTimelinessScore(DateTime? publishTime)
        {
            if (!publishTime.HasValue)
            {
                return 40; // 无发布时间时给予中等分数
            }

            var daysSincePublish = (DateTime.Now - publishTime.Value).TotalDays;

            // 时效性评分：越新的新闻分数越高
            if (daysSincePublish <= 1) return 95;      // 1天内
            if (daysSincePublish <= 3) return 85;      // 3天内
            if (daysSincePublish <= 7) return 75;      // 1周内
            if (daysSincePublish <= 14) return 60;     // 2周内
            if (daysSincePublish <= 30) return 40;     // 1月内
            if (daysSincePublish <= 90) return 25;     // 3月内

            return 10; // 超过3个月
        }

        /// <summary>
        /// 计算内容质量评分
        /// </summary>
        /// <param name="news">新闻对象</param>
        /// <returns>内容质量分数</returns>
        private double CalculateContentQualityScore(News news)
        {
            if (news == null) return 40;

            double score = 50; // 基础分数

            // 标题质量评分
            if (!string.IsNullOrEmpty(news.Title))
            {
                var titleLength = news.Title.Length;
                if (titleLength >= 10 && titleLength <= 100) score += 10;
                if (titleLength >= 15 && titleLength <= 60) score += 5; // 理想长度额外加分
            }

            // 内容长度评分
            if (!string.IsNullOrEmpty(news.Content))
            {
                var contentLength = news.Content.Length;
                if (contentLength >= 200) score += 10;
                if (contentLength >= 500) score += 5;
                if (contentLength >= 1000) score += 5;
            }

            //// 来源可靠性评分
            //if (!string.IsNullOrEmpty(news.Source))
            //{
            //    var reliableSources = new[] { "新华社", "人民日报", "央视新闻", "财新", "第一财经", "界面新闻" };
            //    if (reliableSources.Any(s => news.Source.Contains(s)))
            //    {
            //        score += 15;
            //    }
            //}

            // 分类完整性评分
            if (!string.IsNullOrEmpty(news.Classify) && news.Classify != "未分类")
            {
                score += 5;
            }

            return Math.Max(20, Math.Min(90, score));
        }

        /// <summary>
        /// 清理标签名称中的特殊字符
        /// </summary>
        /// <param name="tagName">原始标签名称</param>
        /// <returns>清理后的标签名称</returns>
        private string CleanTagName(string tagName)
        {
            if (string.IsNullOrEmpty(tagName))
            {
                return string.Empty;
            }

            // 移除可能导致显示问题的特殊字符，保留中文、英文、数字和常用符号
            var cleaned = System.Text.RegularExpressions.Regex.Replace(tagName, @"[^\u4e00-\u9fa5a-zA-Z0-9\s\-_()（）]", "");

            // 清理多余的空格
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"\s+", " ").Trim();

            return cleaned;
        }
    }
}