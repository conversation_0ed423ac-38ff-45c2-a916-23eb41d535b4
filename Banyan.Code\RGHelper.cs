﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using igos_data;
using System.Text;
using System.Threading.Tasks;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using System.Diagnostics;
using System.Windows.Media.Media3D;

namespace Banyan.Code
{
	public class RGHelper
	{
		protected static decimal ChinaRound(decimal value, int decimals)
		{
			decimal result = 0;
			if (value < 0)
			{
				decimal t = Math.Round(-value, decimals, MidpointRounding.AwayFromZero);
				result = -t;
			}
			else
			{
				result = Math.Round(value, decimals, MidpointRounding.AwayFromZero);
			}
			return result;
		}
		public static string addCombineHeader()
		{
			return string.Format(@"<tr><td style=""width:1%;""></td>
<td style=""width:8%;height:20px;font-size:18px;font-weight:bold;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">Fund</td>
<td style=""width:8%;height:20px;font-size:18px;font-weight:bold;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">Project</td>
<td style=""width:2%;height:20px;font-size:18px;font-weight:bold;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">No.</td>
<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin;border-left-style:solid;border-left-width:thin;"">Total Cost</td>
<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">Realized</td>
<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">UnRealized</td>
<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">Total Value</td>
<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;;border-top-style:solid;border-top-width:thin;border-left-style:solid;border-left-width:thin;"">Multiples</td>
<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin""> Mark-up </td>
<td style=""width:4%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">IRR</td>
<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;;border-top-style:solid;border-top-width:thin;border-left-style:solid;border-left-width:thin;"">贡献价值</td>
<td style=""width:3%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">权重</td>
<td style=""width:4%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">项目负责人</td>
<td style=""width:4%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">项目来源</td>
<td style=""width:4%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">项目组成员</td>
<td style=""width:4%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">投后负责人</td>
<td style=""width:4%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">投后组成员</td>
</tr>");
        }
		public static string addHeader(bool summary)
		{
			//        @"<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">Equity</td>
			//<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">Loan</td>"
			string html = summary == true ? @"<tr><td style=""width:1%;""></td><td colspan=""2"" style=""width:17%;height:20px;font-size:18px;font-weight:bold;text-align:center;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium""></td>" : string.Format(@"<tr><td style=""width:1%;""></td>
<td style=""width:7%;height:20px;font-size:18px;font-weight:bold;text-align:left;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium"">Fund</td>
<td style=""width:10%;height:20px;font-size:18px;font-weight:bold;text-align:left;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium"">Project</td>");
			html += string.Format(@"
<td style=""width:3%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium"">No.</td>
<td style=""width:8%;height:20px;font-size:18px;font-weight:bold;;text-align:right;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium;border-left-style:solid;border-left-width:thin;"">Total Cost</td>
<td style=""width:8%;height:20px;font-size:18px;font-weight:bold;;text-align:right;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium"">Realized</td>
<td style=""width:8%;height:20px;font-size:18px;font-weight:bold;;text-align:right;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium"">UnRealized</td>
<td style=""width:8%;height:20px;font-size:18px;font-weight:bold;;text-align:right;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium"">Total Value</td>
<td style=""width:8%;height:20px;font-size:18px;font-weight:bold;;text-align:right;border-bottom-style:solid;border-bottom-width:medium;;border-top-style:solid;border-top-width:medium;border-left-style:solid;border-left-width:thin;"">Mark-up</td>
<td style=""width:7%;height:20px;font-size:18px;font-weight:bold;;text-align:right;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium;font-style:oblique"">Multiples</td>
<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:right;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium;font-style:oblique"">IRR</td>
<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:right;border-bottom-style:solid;border-bottom-width:medium;border-top-style:solid;border-top-width:medium;font-style:oblique;"">Realized</td><td style=""width:1%;""></td></tr>");
			//"<td style=""width:6%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;;border-top-style:solid;border-top-width:thin;border-left-style:solid;border-left-width:thin;"">Proceeds</td>
			//<td style=""width:7%;height:20px;font-size:18px;font-weight:bold;;text-align:center;border-bottom-style:solid;border-bottom-width:thin;border-top-style:solid;border-top-width:thin"">Valuation</td>"
			return html;
		}
        private static string fmtColHelper(string data, string style = "", string align = "right")
        {
            return string.Format(@"<td style=""text-align:{1};font-size:16px;height:20px;font-family:'Times New Roman';{2}"">{0}</td>", data, align, style);
        }

        public static string fmtCol(string data, bool left = false)
        {
            var style = "border-bottom-style:solid;border-bottom-width:thin;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right;";
            if (left)
            {
                style += "border-left-style:solid;border-left-width:thin;";
            }
            else
            {
                style += "font-style:oblique;";
            }
            return string.Format(@"<td style=""{0}"">{1}</td>", style, data);
        }
        public static string fmtRow(string rowNum, string currency, DataRow dr, decimal totalCost,  decimal Realized, decimal UnRealized, double IRR)
        {
            string mutiple = totalCost == 0 || Realized + UnRealized == 0 ? "-" : string.Format("{0:N1}", ChinaRound((Realized + UnRealized) / (totalCost), 1));
            string DPI = totalCost == 0 ? "" : string.Format("{0:N2}", ChinaRound(Realized / (totalCost), 2));
            var res = string.Format(@"<tr style=""{0}""><td></td>
                    <td style=""font-size:16px;font-family:'Times New Roman'"">{1}</td>
                    <td style=""font-size:16px;font-family:'Times New Roman'"">{2}{3}</td>",
                    currency == "CNY" ? "" : "background-color: lightgray;",
                    dr["fundFamillyName"].ToString(),
                    dr["abbName"].ToString().Replace("（跟谁学拆分）", "").Replace("（美利金融分拆）", "").Replace("（度周末补偿）", ""),
                    "");
            res += fmtColHelper(rowNum, "", "center");
            res += fmtColHelper(fmt(totalCost), "border-left-style:solid;border-left-width:thin;");
            res += fmtColHelper(fmt(Realized));
            res += fmtColHelper(fmt(UnRealized));
            res += fmtColHelper(fmt(Realized + UnRealized));
            res += fmtColHelper(fmt(Realized + UnRealized - totalCost), "border-left-style:solid;border-left-width:thin;");
            res += fmtColHelper(mutiple, "font-style:oblique;");
            res += fmtColHelper(Math.Abs(IRR) < 0.1 ? "" : string.Format("{0:N1}", ChinaRound((decimal)IRR, 1)) + "%", "font-style:oblique;");
            res += fmtColHelper(DPI == "0.00" || DPI == "" ? "-" : DPI, "font-style:oblique;") + "<td></td></tr>";
            return res;
        }

        public static string fmtRowSumCombine(string currency, decimal totalInvestmentTotalCost, decimal totalProceeds, decimal totalValuation, double IRR, decimal totalInvestmentCost, decimal totalWeightMarkUp)
        {
            var res = string.Format(@"<tr><td style=""width:1%;""></td>      
                    <td colspan=""2"" style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman'""></td>
                    <td style=""text-align:center;font-size:16px;height:20px;font-family:'Times New Roman'"">{0}</td>", currency);
            res += fmtColCombine(fmt(totalInvestmentTotalCost), "border-left-style:solid;border-left-width:thin;");
            res += fmtColCombine(fmt(totalProceeds));
            res += fmtColCombine(fmt(totalValuation));
            res += fmtColCombine(fmt(totalProceeds + totalValuation));
            res += fmtColCombine(totalInvestmentTotalCost == 0 ? "" : string.Format("{0:N1}", ChinaRound((totalProceeds + totalValuation) / (totalInvestmentTotalCost), 1)),
                "border-left-style:solid;border-left-width:thin;", "center");
            res += fmtColCombine(fmt(totalProceeds + totalValuation - totalInvestmentTotalCost));
            res += fmtColCombine(Math.Abs(IRR) < 0.1 ? "" : (string.Format("{0:N2}", IRR) + "%"));
            res += fmtColCombine(string.Format("{0:N0}", ChinaRound(totalWeightMarkUp, 0)), "border-left-style:solid;border-left-width:thin;font-weight: bold");
            return res + @"<td style=""width:1%;""></td></tr>";
        }

        public static string fmtRowCombine(string rowNum, DataRow dr, decimal totalCost, decimal proceeds, decimal loanProceeds, decimal totalProceeds, decimal Valuation, decimal loanValuation, double IRR, decimal weight)
		{
			var Realized = proceeds + loanProceeds;
			var UnRealized = Valuation + loanValuation;
            var res = string.Format(@"<tr><td style=""width:1%;""></td>
                    <td style=""font-size:16px;font-family:'Times New Roman'"">{0}</td>
                    <td style=""font-size:16px;font-family:'Times New Roman'"">{1}</td>", dr["fundFamillyName"].ToString(), dr["abbName"].ToString());
            res += fmtColHelper(rowNum, "", "center");
            res += fmtColHelper(fmt(totalCost,""), "border-left-style:solid;border-left-width:thin;");
            res += fmtColHelper(fmt(Realized, ""));
            res += fmtColHelper(fmt(UnRealized)); 
            res += fmtColHelper(fmt(Realized + UnRealized)); 
            res += fmtColHelper((totalCost) != 0 ? string.Format("{0:N1}", (Realized + UnRealized) / (totalCost)) : "", "border-left-style:solid;border-left-width:thin;", "center");
            res += fmtColHelper(fmt(Realized + UnRealized - totalCost , ""));
            res += fmtColHelper(Math.Abs(IRR) < 0.1 ? "" : string.Format("{0:N2}", ChinaRound((decimal)IRR, 2)) + "%");
            res += fmtColHelper(fmt((Realized + UnRealized - totalCost) * weight, ""), "border-left-style:solid;border-left-width:thin;font-weight:bold"); // Realized + UnRealized - totalCost
            res += fmtColHelper(string.Format("{0:N1}", weight * 100) + "%");
			res += fmtColHelper(dr["portfolioManager"].ToString());
            res += fmtColHelper(dr["portfolioIntroducer"].ToString());
            res += fmtColHelper(dr["groupMember"].ToString());
            res += fmtColHelper(dr["postInvestManager"].ToString());
            res += fmtColHelper(dr["postInvestMember"].ToString());
            return res + @"<td style=""width:1%;""></td></tr>";
        }
      
		public static string formatHelper(decimal totalInvestmentTotalCostTemp, decimal totalProceedsTemp, decimal totalValuationTemp, double IRR)
		{
			var res = fmtCol(fmt(totalInvestmentTotalCostTemp), true);
			res += fmtCol(fmt(totalProceedsTemp));
			res += fmtCol(fmt(totalValuationTemp));
			res += fmtCol(fmt(totalProceedsTemp + totalValuationTemp));
			res += fmtCol(fmt(totalProceedsTemp + totalValuationTemp - totalInvestmentTotalCostTemp));
			res += fmtCol(totalInvestmentTotalCostTemp == 0 || totalProceedsTemp + totalValuationTemp == 0 ? "-" : string.Format("{0:N1}", ChinaRound((totalProceedsTemp + totalValuationTemp) / (totalInvestmentTotalCostTemp), 1)));
			res += fmtCol(Math.Abs(IRR) < 0.01 ? "" : string.Format("{0:N1}", ChinaRound((decimal)IRR, 1)) + "%");
			res += fmtCol(totalInvestmentTotalCostTemp == 0 || totalProceedsTemp == 0 ? "-" :
	string.Format("{0:N2}", ChinaRound(totalProceedsTemp / (totalInvestmentTotalCostTemp), 2)) == "0.00" ? "-" : string.Format("{0:N2}", ChinaRound(totalProceedsTemp / (totalInvestmentTotalCostTemp), 2)));
			return res;
		}
        private static string fmtColCombine(string data, string style = "", string align = "right")
        {
            return string.Format(@"<td style=""border-bottom-style:double;border-bottom-width:thin;font-size:16px;height:20px;font-family:'Times New Roman';text-align:{1};{2}"">{0}</td>", data, align, style);
        }

   
        private static string fmtColTotalCombine(string data, string style = "", string align = "right")
        {
            return string.Format(@"<td style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:{1};{2}"">{0}</td>", data, align, style);
        }

        public static string fmtRowSumTotalCombine(int totalNum, string PMName, decimal totalInvestmentTotalCost, decimal totalProceeds, decimal totalValuation, double IRR, decimal totalInvestmentCost, decimal totalWeightMarkUp)
        {
			var res = string.Format(@"<tr><td style=""width:1%;""></td>               
                    <td colspan=""2"" style=""border-bottom-style:double;border-bottom-width:medium;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{1}{2}</td>
<td style=""text-align:center;font-size:16px;height:20px;font-family:'Times New Roman';border-bottom-style:double;border-bottom-width:medium;"">{0}</td>",
(totalNum).ToString(), PMName, "汇总（CNY）");
            res += fmtColTotalCombine(fmt(totalInvestmentTotalCost), "border-left-style:solid;border-left-width:thin;");
            res += fmtColTotalCombine(fmt(totalProceeds));
            res += fmtColTotalCombine(fmt(totalValuation));
            res += fmtColTotalCombine(fmt(totalProceeds + totalValuation));
            res += fmtColTotalCombine(totalInvestmentTotalCost == 0 ? "" : string.Format("{0:N1}", ChinaRound((totalProceeds + totalValuation) / (totalInvestmentTotalCost), 1)),
            "border-left-style:solid;border-left-width:thin;", "center");
            res += fmtColTotalCombine(fmt(totalProceeds + totalValuation - totalInvestmentTotalCost));
            res += fmtColTotalCombine(Math.Abs(IRR) < 0.1 ? "" : (string.Format("{0:N2}", IRR) + "%"));
            res += fmtColTotalCombine(string.Format("{0:N0}", ChinaRound(totalWeightMarkUp, 0)), "border-left-style:solid;border-left-width:thin;font-weight: bold");
            return res + @"<td colspan=""6""></td><td style=""width:1%;""></td></tr>";
        }

        public static string fmtRowSumTotal(int totalNum, string PMName, decimal totalInvestmentTotalCost, decimal totalProceeds, decimal totalValuation, double IRRTotal, decimal totalInvestmentCost)
        {
            return string.Format(@"<tr style=""""><td></td>               
                    <td colspan=""2"" style=""border-bottom-style:solid;border-bottom-width:thin;font-size:16px;height:20px;font-family:'Times New Roman';text-align:right"">{1}{2}</td>
<td style=""text-align:center;font-size:16px;height:20px;font-family:'Times New Roman';border-bottom-style:solid;border-bottom-width:thin;"">{0}</td>", (totalNum).ToString(), PMName, "汇总（CNY）") +
            formatHelper(totalInvestmentTotalCost, totalProceeds, totalValuation, IRRTotal) + "<td></td></tr>";
        }
        public static string fmtRowSum(string currency, decimal totalInvestmentTotalCostTemp, decimal totalProceedsTemp, decimal totalValuationTemp, double IRR)
		{
			return string.Format(@"<tr style=""{0}""><td></td>                   
 <td colspan=""2"" style=""text-align:right;font-size:16px;height:20px;font-family:'Times New Roman'"">{1}{2}</td>
<td style=""text-align:center;font-size:16px;height:20px;font-family:'Times New Roman'"">{3}</td>",
						currency == "CNY" ? "" : "background-color: lightgray;", "", "", currency) + formatHelper(totalInvestmentTotalCostTemp, totalProceedsTemp, totalValuationTemp, IRR) + "<td></td></tr>";
        }
        public static string fmt(decimal n, string limiter = "-")
		{
			return n == 0 ? limiter : string.Format("{0:N0}", ChinaRound(n, 0));
		}
		public static DataTable getShareStructureTable(string portfolioID)
        {
            DataTable dt = new DataTable();
            if (!string.IsNullOrEmpty(portfolioID))
            {
                string search = @"select
	distinct FundBasicInfo.fundFamillyName,
	PortfolioShareStructure.investType,
	PortfolioShareStructure.currency,
	PortfolioShareStructure.portfolioID,
	PortfolioShareStructure.fundID,
	PortfolioShareStructure.closeDate,
	PortfolioShareStructure.shareType,
	PortfolioShareStructure.shareOwner,
	PortfolioShareStructure.abbName,
	isnull(PortfolioShareStructure.cost,
	0)+ isnull(PortfolioShareStructure.otherFees,
	0) as cost,
	(isnull(PortfolioShareStructure.shareOwnedNo,
	0) * cast(isnull( case
		when conversionRatio = '' then 1
		else conversionRatio
	end,
	1) as decimal(18,
	8))) as shareOwnedNo,
	case
		when shareOwnedNo != 0 then cost / shareOwnedNo
		else 0
	end as maxPrice,
	securityType,
	round,
	isnull(preMoney,
	0) as preMoney,
	isnull(postMoney,
	0) as postMoney
from
	PortfolioShareStructure
inner join DDL_SecurityType on
	DDL_SecurityType.securityTypeID = PortfolioShareStructure.securityTypeID
left outer join (
	select
		distinct fundFamillyName,
		fundID
	from
		FundBasicInfo) as FundBasicInfo
on
	FundBasicInfo.fundID = PortfolioShareStructure.fundID";
                search += @" where PortfolioShareStructure.portfolioID='" + portfolioID + @"' order by closedate,round desc;";
                Data_basic db = new Data_basic();
                if (db.ExecuteDataset(search) && db.AffectedRow > 0)
                    dt = db.Dataset.Tables[0];
            }
            return dt;
        }
		public static void getData(Data_basic db, ref DataTable costValuation, ref DataTable investHistory)
		{
            string search = @"SELECT
	[ViewPortfolioInfo].[portfolioID],
	[currency],
	[ViewPortfolioInfo].abbName,
	[FundFamily2PortfolioInvestSummary].fundFamillyName,
	[ViewPortfolioInfo].projectManager,
	[portfolioIntroducer],
	[portfolioManager],
	[groupMember],
	[postInvestMember],
	[postInvestManager],
	sum(isnull([shareProceeds], 0)+ isnull([otherProceeds], 0)) as proceeds,
	sum([loanProceeds]) as [loanProceeds],
	sum(isnull([cost], 0)) as [cost],
	sum(isnull([loan], 0)) as [loan],
	sum(isnull([loanRelization], 0)) as [loanRelization],
	sum(isnull([costRelization], 0)) as [costRelization],
	sum(isnull([totalCost], 0)) as [totalCost] ,
	sum(isnull([carryingCost], 0)) as [carryingCost],
	sum(isnull([GL], 0)) as [GL],
	sum(isnull(shareOwnedNo, 0)) as shareOwnedNo,
	Valuation.Valuation,
	Valuation.loanValuation,
	valuationDate
FROM
	(
	select
		fundFamillyName,
		[portfolioID],
		[initialDate],
		[shareProceeds],
		[otherProceeds],
		[loanProceeds],
		[cost],
		[loan],
		[loanRelization],
		[costRelization],
		[shareOwnedNo],
		[carryingCost],
		[totalCost],
		[GL],
		[sumProceeds],
		[currency]
	FROM
		FundFamily2PortfolioInvestSummary) as FundFamily2PortfolioInvestSummary
left outer join [ViewPortfolioInfo] on
	[ViewPortfolioInfo].portfolioID = [FundFamily2PortfolioInvestSummary].portfolioID
left outer join (
	select
		distinct sum(isnull(Valuation, 0)) as Valuation,
		sum(isnull(loanValuation, 0)) as loanValuation,
		fundFamillyName,
		portfolioID,
		max(valuationDate) as valuationDate
	FROM
		(
		select
			   fundFamillyName,
				Valuation,
				loanValuation,
				portfolioID,
				valuationDate
			from
				dbo.PortfolioValuation_Latest
			inner join FundBasicInfo on
				FundBasicInfo.fundID = PortfolioValuation_Latest.fundID ) as a
	group by
		FundFamillyName,
		portfolioID) as Valuation 
 on
	Valuation.portfolioID = [FundFamily2PortfolioInvestSummary].portfolioID
	and Valuation.fundFamillyName = [FundFamily2PortfolioInvestSummary].fundFamillyName
group by
	[ViewPortfolioInfo].[portfolioID],
	[currency],
	abbName,
	projectManager,
	[FundFamily2PortfolioInvestSummary].fundFamillyName,
	valuation,
	loanValuation,
	[portfolioIntroducer],
	[portfolioManager],
	[groupMember],
	[postInvestMember],
	[postInvestManager],
	valuationDate
order by
	projectManager,
	[currency],
	fundFamillyName,
	abbName;";

            string searchInsestHistory = @"select
	[InvestHistorySummary].portfolioid,
	closeDate,
	currency,
	sum(cost) as cost,
	sum(proceeds) as proceeds,
	sum(costRelization) as costRelization,
	fundFamillyName,
	projectManager,
	[portfolioIntroducer],
	[portfolioManager],
	[groupMember],
	[postInvestMember],
	[postInvestManager]
from
	(
	select
		[InvestHistoryForIRR].portfolioid,
		closeDate,
		currency,
		cost,
		proceeds,
		costRelization,
		fundFamillyName
	from
		[InvestHistoryForIRR]) as [InvestHistorySummary]
left outer join ViewPortfolioInfo on
	ViewPortfolioInfo.portfolioID = [InvestHistorySummary].portfolioID
group by
	[InvestHistorySummary].portfolioid,
	closeDate,
	currency,
	fundFamillyName,
	projectManager,
	[portfolioIntroducer],
	[portfolioManager],
	[groupMember],
	[postInvestMember],
	[postInvestManager]
order by
	portfolioid,
	closeDate,
	currency;";
            if (db.ExecuteDataset(search + searchInsestHistory) && db.AffectedRow > 0)
            {
                costValuation = db.Dataset.Tables[0];
                investHistory = db.Dataset.Tables[1];
            }
        }
    }
}
