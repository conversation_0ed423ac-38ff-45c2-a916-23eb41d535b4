﻿using Entity.Base;
using System;
using Utility;

namespace Banyan.MeetDomain
{
    [Serializable]
    [SqlTable(dbEnum.Survey)]
    public class Article
    {
        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        /// <summary>
        /// 作者
        /// </summary>
        [SqlField]
        public string EditorName { get; set; } = string.Empty;

        /// <summary>
        /// 文章标题
        /// </summary>
        [SqlField]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 文章简述
        /// </summary>
        [SqlField]
        public string Summary { get; set; } = string.Empty;

        /// <summary>
        /// 文章内容
        /// </summary>
        [SqlField]
        public string Content { get; set; } = string.Empty;


        /// <summary>
        /// 封面图
        /// </summary>
        [SqlField]
        public string CoverUrl { get; set; } = string.Empty;

        /// <summary>
        /// 栏目Id{案例、周报、专题}
        /// </summary>
        [SqlField]
        public int ColumnId { get; set; }

        public string ColumnName { get; set; } = string.Empty;

        /// <summary>
        /// 分类Id{逗号分隔}
        /// </summary>
        [SqlField]
        public int ClassId { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        [SqlField]
        public string ClassName { get; set; } = string.Empty;

        /// <summary>
        /// 排序值，降序排列
        /// </summary>
        [SqlField]
        public byte Sort { get; set; }

        /// <summary>
        /// 阅读角色
        /// </summary>
        [SqlField]
        public string ToRoleIds { get; set; } = string.Empty;

        /// <summary>
        /// 文章状态
        /// </summary>
        [SqlField]
        public byte Status { get; set; } = (int)ResearchStatus.wait;

        /// <summary>
        /// 文章发布时间
        /// </summary>
        [SqlField]
        public DateTime PubTime { get; set; } = DateTime.Now;

        public String PubTimeStr = string.Empty;

        /// <summary>
        /// 报告完成时间
        /// </summary>
        [SqlField]
        public DateTime ReportTime { get; set; } = DateTime.Now;
        /// <summary>
        /// 创建人
        /// </summary>
        [SqlField]
        public string CreatorName { get; set; } = string.Empty;

    }

    public enum ResearchStatus
    {
        /// <summary>
        /// 编辑中
        /// </summary>
        editing = 3,

        /// <summary>
        /// 已下架
        /// </summary>
        wait = 2,

        /// <summary>
        /// 已上架
        /// </summary>
        normal = 1,

        /// <summary>
        /// 已删除
        /// </summary>
        delete = 0,
    }


    public enum ColumnId
    {
        /// <summary>
        /// 案例
        /// </summary>
        cases = 1,

        /// <summary>
        /// 周报
        /// </summary>
        weekly = 2,

        /// <summary>
        /// 专题
        /// </summary>
        subject = 3,

        /// <summary>
        /// 每周热点
        /// </summary>
        weekhot = 4,

        /// <summary>
        /// 内部报告
        /// </summary>
        interalReport = 5,
    }

    public static class ResearchEnum
    {
        public static string GetColumnName(this int columnId)
        {
            string columnName = string.Empty;
            switch (columnId)
            {
                case 1:
                    columnName = "案例";
                    break;
                case 2:
                    columnName = "周报";
                    break;
                case 3:
                    columnName = "专题";
                    break;
                case 4:
                    columnName = "每周热点";
                    break;
                case 5:
                    columnName = "内部报告";
                    break;
                default:
                    break;
            }
            return columnName;
        }
    }

    public class ResearchDoc
    {
        public int AId { get; set; } = 0;

        public string Title { get; set; } = string.Empty;

        public string ColumnName { get; set; } = string.Empty;

        public string ClassName { get; set; } = string.Empty;

        public int ViewCount { get; set; } = 0;

        public int CommentCount { get; set; } = 0;

        public int CollectCount { get; set; } = 0;

        public int UserViews { get; set; } = 0;
    }

}
