﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;

/// <summary>
/// XIRR 的摘要说明
/// </summary>

namespace Xirr
{
    public class Program
    {
        private const Double DaysPerYear = 365.0;
        private const int MaxIterations = 100;
        private const double DefaultTolerance = 1E-6;
        private const double DefaultGuess = 0.1;

        private static readonly Func<List<CashItem>, Double> NewthonsMethod =
            cf => NewtonsMethodImplementation(cf, Xnpv, XnpvPrime);

        private static readonly Func<List<CashItem>, Double> BisectionMethod =
            cf => BisectionMethodImplementation(cf, Xnpv);
        public static decimal PostMoneyCompute(DataTable dt, decimal USDtoRMBRate, string currency)
        {
            try
            {
                string search = "shareType='Banyan'";

                decimal latestRoundPostValue = 0;
                decimal TotalShareOwnedNo = Convert.ToDecimal(dt.Compute("sum(shareOwnedNo)", string.Empty).ToString());

                string firstInvestDate = dt.Compute("min(closeDate)", search).ToString();

                string latestCloseDate = dt.Compute("max(closeDate)", string.Empty).ToString();
                decimal maxPrice = Convert.ToDecimal(dt.Compute("max(maxPrice)", string.Empty).ToString());

                decimal firstRoundMaxPrice = 0;
                if (string.IsNullOrEmpty(firstInvestDate))
                    return 0;
                DataRow[] firstInvestRows = dt.Select("closeDate='" + firstInvestDate + "'", "round desc");

                if (firstInvestRows != null && firstInvestRows.Length > 0)
                {
                    DataTable firstInvestTable = firstInvestRows.CopyToDataTable();
                    firstRoundMaxPrice = Convert.ToDecimal(firstInvestTable.Compute("max(maxPrice)", string.Empty).ToString());
                    latestRoundPostValue = firstRoundMaxPrice * TotalShareOwnedNo;
                    if (firstInvestDate != latestCloseDate)
                    {
                        DataTable LatestInvestTable = dt.Select("closeDate>'" + firstInvestDate + "'", "round desc").CopyToDataTable();
                        decimal latesMaxPrice = 0;
                        Decimal.TryParse(LatestInvestTable.Compute("max(maxPrice)", string.Empty).ToString(), out latesMaxPrice);

                        if (LatestInvestTable.Compute("max(maxPrice)", string.Empty) != null)
                            latesMaxPrice = Convert.ToDecimal(LatestInvestTable.Compute("max(maxPrice)", string.Empty).ToString());
                        if (latesMaxPrice > 0)
                        {
                            DataTable LatestCloseInvestTable = dt.Select("closeDate='" + latestCloseDate + "'", "round desc").CopyToDataTable();
                            decimal latestCloseMaxPrice = 0;
                            Decimal.TryParse(LatestCloseInvestTable.Compute("max(maxPrice)", string.Empty).ToString(), out latestCloseMaxPrice);

                            if (latestCloseMaxPrice > 0)
                            {
                                latestRoundPostValue = latestCloseMaxPrice * TotalShareOwnedNo;
                                if (LatestCloseInvestTable.Select("currency='USD'  and maxPrice='" + latestCloseMaxPrice + "'", "round desc").Length > 0 && currency == "CNY")//USD Valuation
                                {
                                    latestRoundPostValue = latestRoundPostValue * USDtoRMBRate;
                                }
                                if (LatestCloseInvestTable.Select("currency='CNY'  and maxPrice='" + latestCloseMaxPrice + "'", "round desc").Length > 0 && currency == "USD")//CNY Valuation
                                {
                                    latestRoundPostValue = latestRoundPostValue / USDtoRMBRate;
                                }
                            }
                            else
                            {
                                latestRoundPostValue = latesMaxPrice * TotalShareOwnedNo;
                                if (LatestInvestTable.Select("currency='USD' and maxPrice='" + latesMaxPrice + "'", "round desc").Length > 0 && currency == "CNY")//USD
                                {
                                    latestRoundPostValue = latestRoundPostValue * USDtoRMBRate;
                                }
                                if (LatestCloseInvestTable.Select("currency='CNY'  and maxPrice='" + latesMaxPrice + "'", "round desc").Length > 0 && currency == "USD")//CNY Valuation
                                {
                                    latestRoundPostValue = latestRoundPostValue / USDtoRMBRate;
                                }
                            }

                            latestRoundPostValue = Convert.ToDecimal(LatestCloseInvestTable.Compute("max(postMoney)", string.Empty).ToString()) > 0 ? Convert.ToDecimal(LatestCloseInvestTable.Compute("max(postMoney)", string.Empty).ToString()) : latestRoundPostValue;
                        }
                    }
                }

                return latestRoundPostValue;
            }
            catch (ArgumentException)
            {
                return 0;
            }
            catch (InvalidOperationException)
            {
                return 0;
            }
        }
        // out decimal currentOwnership,out decimal firstRoundOwnership,out string firstRound,out string lastRound, out string otherInvestor, out decimal firstRoundCost,out decimal followOnCost, out decimal firstRoundValue,out decimal LatestBanyanTotalValuation,out decimal latestRoundPostValue,out decimal latestRoundPreValue,out string valuationMethod,out string YorN
        public static decimal ValuationCompute(DataTable dt, string fundFamillyName, decimal USDtoRMBRate, decimal cost, string currency, DataRow portfolioSummary)
        {
            try
            {
                string search = string.IsNullOrEmpty(fundFamillyName) ? "shareType='Banyan' and currency='" + currency + "'" : "shareType='Banyan' and fundFamillyName='" + fundFamillyName + "'";
                decimal LatestBanyanTotalValuation = 0;
                decimal currentOwnership = 0;
                //decimal latestRoundPostValue = 0;
                decimal TotalShareOwnedNo = Convert.ToDecimal(dt.Compute("sum(shareOwnedNo)", string.Empty).ToString());
                decimal banyanSumSharesNo = 0;
                if (TotalShareOwnedNo > 0 && dt.Compute("sum(shareOwnedNo)", search) != null)
                {
                    banyanSumSharesNo = Convert.ToDecimal(dt.Compute("sum(shareOwnedNo)", search).ToString());
                    currentOwnership = banyanSumSharesNo / TotalShareOwnedNo;
                }
                string firstInvestDate = dt.Compute("min(closeDate)", search).ToString();

                string latestCloseDate = dt.Compute("max(closeDate)", string.Empty).ToString();
                decimal maxPrice = Convert.ToDecimal(dt.Compute("max(maxPrice)", string.Empty).ToString());

                decimal firstRoundMaxPrice = 0;

                DataRow[] firstInvestRows = dt.Select("closeDate='" + firstInvestDate + "'", "round desc");
                //cost>0,for cal cost
                //search = string.IsNullOrEmpty(fundFamillyName) ? "shareType='Banyan' and cost>0" : "shareType='Banyan' and cost>0 and fundFamillyName='" + fundFamillyName + "'";
                if (firstInvestRows != null && firstInvestRows.Length > 0)
                {
                    DataTable firstInvestTable = firstInvestRows.CopyToDataTable();
                    firstRoundMaxPrice = Convert.ToDecimal(firstInvestTable.Compute("max(maxPrice)", string.Empty).ToString());

                    LatestBanyanTotalValuation = firstRoundMaxPrice * banyanSumSharesNo;

                    if (firstInvestDate != latestCloseDate && TotalShareOwnedNo > 0)
                    {
                        DataRow[] LatestInvestRows = dt.Select("closeDate>'" + firstInvestDate + "'", "round desc");

                        DataTable LatestInvestTable = LatestInvestRows.CopyToDataTable();
                        decimal latesMaxPrice = 0;
                        Decimal.TryParse(LatestInvestTable.Compute("max(maxPrice)", string.Empty).ToString(), out latesMaxPrice);

                        if (LatestInvestTable.Compute("max(maxPrice)", string.Empty) != null)
                            latesMaxPrice = Convert.ToDecimal(LatestInvestTable.Compute("max(maxPrice)", string.Empty).ToString());
                        if (latesMaxPrice > 0)
                        {
                            DataRow[] LatestCloseInvestRows = dt.Select("closeDate='" + latestCloseDate + "'", "round desc");

                            DataTable LatestCloseInvestTable = LatestCloseInvestRows.CopyToDataTable();
                            decimal latestCloseMaxPrice = 0;
                            Decimal.TryParse(LatestCloseInvestTable.Compute("max(maxPrice)", string.Empty).ToString(), out latestCloseMaxPrice);

                            if (latestCloseMaxPrice > 0)
                            {
                                LatestBanyanTotalValuation = latestCloseMaxPrice * banyanSumSharesNo;
                                //latestRoundPostValue = latestCloseMaxPrice * TotalShareOwnedNo;
                                if (LatestCloseInvestTable.Select("currency='USD'  and maxPrice='" + latestCloseMaxPrice + "'", "round desc").Length > 0 && (fundFamillyName.Contains("RMB") || currency == "CNY"))//USD Valuation
                                {
                                    LatestBanyanTotalValuation = LatestBanyanTotalValuation * USDtoRMBRate;
                                    //latestRoundPostValue = latestRoundPostValue * USDtoRMBRate;
                                }
                                if (LatestCloseInvestTable.Select("currency='CNY'  and maxPrice='" + latestCloseMaxPrice + "'", "round desc").Length > 0 && (fundFamillyName.Contains("USD") || currency == "USD"))//CNY Valuation
                                {
                                    LatestBanyanTotalValuation = LatestBanyanTotalValuation / USDtoRMBRate;
                                    //latestRoundPostValue = latestRoundPostValue / USDtoRMBRate;
                                }
                            }
                            else
                            {
                                LatestBanyanTotalValuation = latesMaxPrice * banyanSumSharesNo;
                                //latestRoundPostValue = latesMaxPrice * TotalShareOwnedNo;
                                if (LatestInvestTable.Select("currency='USD' and maxPrice='" + latesMaxPrice + "'", "round desc").Length > 0 && (!fundFamillyName.Contains("RMB") || currency == "CNY"))//USD
                                {
                                    LatestBanyanTotalValuation = LatestBanyanTotalValuation * USDtoRMBRate;
                                    //latestRoundPostValue = latestRoundPostValue * USDtoRMBRate;
                                }
                                if (LatestInvestTable.Select("currency='CNY'  and maxPrice='" + latesMaxPrice + "'", "round desc").Length > 0 && (!fundFamillyName.Contains("USD") || currency == "USD"))//CNY Valuation
                                {
                                    LatestBanyanTotalValuation = LatestBanyanTotalValuation / USDtoRMBRate;
                                    //latestRoundPostValue = latestRoundPostValue / USDtoRMBRate;
                                }
                            }
                            //cover by postMoney
                            decimal postMoney = Convert.ToDecimal(LatestCloseInvestTable.Compute("max(postMoney)", string.Empty).ToString());
                            if (postMoney > 0)
                            {
                                LatestBanyanTotalValuation = postMoney * currentOwnership;
                                if (LatestCloseInvestTable.Select("currency='USD'  and postMoney='" + postMoney + "'", "round desc").Length > 0 && (fundFamillyName.Contains("RMB") || currency == "CNY"))//USD Valuation
                                    LatestBanyanTotalValuation = LatestBanyanTotalValuation * USDtoRMBRate;
                                if (LatestCloseInvestTable.Select("currency='CNY'  and postMoney='" + postMoney + "'", "round desc").Length > 0 && (fundFamillyName.Contains("USD") || currency == "USD"))//CNY Valuation
                                    LatestBanyanTotalValuation = LatestBanyanTotalValuation / USDtoRMBRate;
                            }

                        }
                        else
                        {
                            LatestBanyanTotalValuation = cost;
                        }
                    }
                    else
                    {
                        LatestBanyanTotalValuation = cost;
                        //cover by postMoney
                        //decimal postMoney = 0;
                        //postMoney = Convert.ToDecimal(firstInvestTable.Compute("max(postMoney)", string.Empty).ToString());
                        //if (postMoney > 0)
                        //{
                        //    LatestBanyanTotalValuation = postMoney * currentOwnership;
                        //    if (firstInvestTable.Select("currency='USD'  and postMoney='" + postMoney + "'", "round desc").Length > 0 && (fundFamillyName.Contains("RMB") || currency == "CNY"))//USD Valuation
                        //        LatestBanyanTotalValuation = LatestBanyanTotalValuation * USDtoRMBRate;
                        //    if (firstInvestTable.Select("currency='CNY'  and postMoney='" + postMoney + "'", "round desc").Length > 0 && (fundFamillyName.Contains("USD") || currency == "USD"))//CNY Valuation
                        //        LatestBanyanTotalValuation = LatestBanyanTotalValuation / USDtoRMBRate;
                        //}
                    }
                }

                if (Math.Abs(cost - LatestBanyanTotalValuation) < 1000)
                    LatestBanyanTotalValuation = cost;
                if (banyanSumSharesNo == 0 && cost <= 0)
                    LatestBanyanTotalValuation = 0;
                //cover by input valution 
                if (!string.IsNullOrEmpty(latestCloseDate) && !string.IsNullOrEmpty(portfolioSummary["valuationDate"].ToString()) && DateTime.Parse(portfolioSummary["valuationDate"].ToString()) >= DateTime.Parse(latestCloseDate))
                {
                    if (!string.IsNullOrEmpty(portfolioSummary["valuation"].ToString()))
                        LatestBanyanTotalValuation = decimal.Parse(portfolioSummary["valuation"].ToString());
                }
                return LatestBanyanTotalValuation;
            }
            catch (ArgumentException)
            {
                return cost;
            }
            catch (InvalidOperationException)
            {
                return cost;
            }
        }
        public static decimal CaptablePostByPortfolio(DataTable dt, decimal USDtoRMBRate, string currency)
        {
            try
            {
                decimal LatestTotalValuation = 0;
                decimal TotalShareOwnedNo = Convert.ToDecimal(dt.Compute("sum(shareOwnedNo)", string.Empty).ToString());
                string firstInvestDate = dt.Compute("min(closeDate)", "shareType='Banyan'").ToString();
                string latestCloseDate = dt.Compute("max(closeDate)", string.Empty).ToString();
                decimal maxPrice = Convert.ToDecimal(dt.Compute("max(maxPrice)", string.Empty).ToString());

                decimal firstRoundMaxPrice = 0;

                DataRow[] firstInvestRows = dt.Select("closeDate='" + firstInvestDate + "'", "round desc");

                if (TotalShareOwnedNo > 0 && firstInvestRows != null && firstInvestRows.Length > 0)
                {
                    DataTable firstInvestTable = firstInvestRows.CopyToDataTable();
                    firstRoundMaxPrice = Convert.ToDecimal(firstInvestTable.Compute("max(maxPrice)", string.Empty).ToString());

                    LatestTotalValuation = firstRoundMaxPrice * TotalShareOwnedNo;

                    if (firstInvestDate != latestCloseDate && TotalShareOwnedNo > 0)
                    {
                        DataRow[] LatestInvestRows = dt.Select("closeDate>'" + firstInvestDate + "'", "round desc");

                        DataTable LatestInvestTable = LatestInvestRows.CopyToDataTable();
                        decimal latesMaxPrice = 0;
                        Decimal.TryParse(LatestInvestTable.Compute("max(maxPrice)", string.Empty).ToString(), out latesMaxPrice);

                        if (LatestInvestTable.Compute("max(maxPrice)", string.Empty) != null)
                            latesMaxPrice = Convert.ToDecimal(LatestInvestTable.Compute("max(maxPrice)", string.Empty).ToString());
                        if (latesMaxPrice > 0)
                        {
                            DataRow[] LatestCloseInvestRows = dt.Select("closeDate='" + latestCloseDate + "'", "round desc");

                            DataTable LatestCloseInvestTable = LatestCloseInvestRows.CopyToDataTable();
                            decimal latestCloseMaxPrice = 0;
                            Decimal.TryParse(LatestCloseInvestTable.Compute("max(maxPrice)", string.Empty).ToString(), out latestCloseMaxPrice);

                            if (latestCloseMaxPrice > 0)
                            {
                                LatestTotalValuation = latestCloseMaxPrice * TotalShareOwnedNo;
                                if (LatestCloseInvestTable.Select("currency='USD'  and maxPrice='" + latestCloseMaxPrice + "'", "round desc").Length > 0 && currency == "CNY")//USD Valuation
                                    LatestTotalValuation = LatestTotalValuation * USDtoRMBRate;

                                if (LatestCloseInvestTable.Select("currency='CNY'  and maxPrice='" + latestCloseMaxPrice + "'", "round desc").Length > 0 && currency == "USD")//CNY Valuation
                                    LatestTotalValuation = LatestTotalValuation / USDtoRMBRate;
                            }
                            else
                            {
                                LatestTotalValuation = latesMaxPrice * TotalShareOwnedNo;
                                if (LatestInvestTable.Select("currency='USD' and maxPrice='" + latesMaxPrice + "'", "round desc").Length > 0 && currency == "CNY")//USD
                                    LatestTotalValuation = LatestTotalValuation * USDtoRMBRate;
                                if (LatestInvestTable.Select("currency='CNY'  and maxPrice='" + latesMaxPrice + "'", "round desc").Length > 0 && currency == "USD")//CNY Valuation
                                    LatestTotalValuation = LatestTotalValuation / USDtoRMBRate;
                            }
                            //cover by postMoney
                            decimal postMoney = Convert.ToDecimal(LatestCloseInvestTable.Compute("max(postMoney)", string.Empty).ToString());
                            if (postMoney > 0)
                            {
                                LatestTotalValuation = postMoney;
                                if (LatestCloseInvestTable.Select("currency='USD'  and postMoney='" + postMoney + "'", "round desc").Length > 0 && currency == "CNY")//USD Valuation
                                    LatestTotalValuation = LatestTotalValuation * USDtoRMBRate;
                                if (LatestCloseInvestTable.Select("currency='CNY'  and postMoney='" + postMoney + "'", "round desc").Length > 0 && currency == "USD")//CNY Valuation
                                    LatestTotalValuation = LatestTotalValuation / USDtoRMBRate;
                            }

                        }
                    }
                    else
                    {
                        //cover by postMoney
                        decimal postMoney = 0;
                        postMoney = Convert.ToDecimal(firstInvestTable.Compute("max(postMoney)", string.Empty).ToString());
                        if (postMoney > 0)
                        {
                            LatestTotalValuation = postMoney;
                            if (firstInvestTable.Select("currency='USD'  and postMoney='" + postMoney + "'", "round desc").Length > 0 && currency == "CNY")//USD Valuation
                                LatestTotalValuation = LatestTotalValuation * USDtoRMBRate;
                            if (firstInvestTable.Select("currency='CNY'  and postMoney='" + postMoney + "'", "round desc").Length > 0 && currency == "USD")//CNY Valuation
                                LatestTotalValuation = LatestTotalValuation / USDtoRMBRate;
                        }
                    }
                }

                return LatestTotalValuation;
            }
            catch (ArgumentException)
            {
                return 0;
            }
            catch (InvalidOperationException)
            {
                return 0;
            }
        }
        public static decimal fundOwnership(DataTable dt, string fundFamillyName)
        {
            try
            {
                decimal currentOwnership = 0;
                string search = string.IsNullOrEmpty(fundFamillyName) ? "shareType='Banyan'" : "shareType='Banyan' and fundFamillyName='" + fundFamillyName + "'";
                decimal TotalShareOwnedNo = Convert.ToDecimal(dt.Compute("sum(shareOwnedNo)", string.Empty).ToString());
                decimal banyanSumSharesNo = 0;
                if (TotalShareOwnedNo > 0 && dt.Compute("sum(shareOwnedNo)", search) != null)
                {
                    banyanSumSharesNo = Convert.ToDecimal(dt.Compute("sum(shareOwnedNo)", search).ToString());
                    currentOwnership = banyanSumSharesNo / TotalShareOwnedNo;
                }

                return currentOwnership;
            }
            catch (ArgumentException)
            {
                return 0;
            }
            catch (InvalidOperationException)
            {
                return 0;
            }
        }
        public static decimal[] MarkUpCompute(DataTable dt, string fundFamillyName, decimal currentPostMoney, decimal currentPreMoney)
        {
            string firstInvestDate = "";
            string lastCloseDate = "";
            string firstRound = "";
            decimal totalCost = 0;
            decimal currentOwnership = 0;
            decimal firstRoundValue = 0;
            DateTime initialDate = new DateTime();
            string search = string.IsNullOrEmpty(fundFamillyName) ? "shareType='Banyan'" : "shareType='Banyan' and fundFamillyName='" + fundFamillyName + "'";

            if (dt != null && dt.Rows.Count > 0)//ignore no equity
            {
                decimal TotalShareOwnedNo = Convert.ToDecimal(dt.Compute("sum(shareOwnedNo)", string.Empty).ToString());
                if (TotalShareOwnedNo > 0 && dt.Compute("sum(shareOwnedNo)", search) != null)
                    currentOwnership = Convert.ToDecimal(dt.Compute("sum(shareOwnedNo)", search).ToString()) / TotalShareOwnedNo;
                firstInvestDate = dt.Compute("min(closeDate)", search).ToString();
                totalCost = Convert.ToDecimal(dt.Select(search, "round desc").CopyToDataTable().Compute("sum(cost)", string.Empty).ToString());
                DateTime.TryParse(firstInvestDate, out initialDate);
                string latestCloseDate = dt.Compute("max(closeDate)", string.Empty).ToString();
                lastCloseDate = latestCloseDate;
                decimal maxPrice = Convert.ToDecimal(dt.Compute("max(maxPrice)", string.Empty).ToString());

                decimal firstRoundMaxPrice = 0;

                DataTable firstRoundTable = dt.Select("closeDate<='" + firstInvestDate + "'", "round asc").CopyToDataTable();
                DataRow[] firstInvestRows = dt.Select("closeDate='" + firstInvestDate + "'", "round desc");
                //cost>0,for cal cost
                search += " and cost>0 ";
                if (firstInvestRows != null && firstInvestRows.Length > 0)
                {
                    DataTable firstInvestTable = firstInvestRows.CopyToDataTable();
                    firstRoundMaxPrice = Convert.ToDecimal(firstInvestTable.Compute("max(maxPrice)", string.Empty).ToString());
                    if (firstInvestTable.Select(search, "round asc").Length > 0)
                    {
                        firstRound = firstInvestTable.Select(search, "round asc")[0]["round"].ToString();
                    }
                    if (string.IsNullOrEmpty(firstRound))
                        firstRound = firstInvestRows[0]["round"].ToString();

                    firstRoundValue = firstRoundMaxPrice * Convert.ToDecimal(firstRoundTable.Compute("sum(shareOwnedNo)", string.Empty).ToString());

                    if (firstInvestDate != latestCloseDate)
                    {
                        DataRow[] LatestInvestRows = dt.Select("closeDate>'" + firstInvestDate + "'", "round desc");

                        DataTable LatestInvestTable = LatestInvestRows.CopyToDataTable();
                        //round adjustment
                        if (!string.IsNullOrEmpty(firstRound) && LatestInvestTable != null && LatestInvestTable.Rows.Count > 0)
                        {
                            string roundSearch = "Round='" + firstRound + "' and cost>0 and investType <> 'Shares Split' and investType <> 'Shares Sale'";
                            DataRow[] roundAdj = LatestInvestTable.Select(roundSearch, "round desc");
                            if (roundAdj != null && roundAdj.Length > 0)
                            {
                                firstRoundValue = firstRoundMaxPrice * (Convert.ToDecimal(firstRoundTable.Compute("sum(shareOwnedNo)", string.Empty).ToString()) + Convert.ToDecimal(LatestInvestTable.Compute("sum(shareOwnedNo)", roundSearch).ToString()));
                            }
                        }
                        //end
                    }

                    firstRoundValue = Convert.ToDecimal(firstRoundTable.Compute("max(postMoney)", string.Empty).ToString()) > 0 ? Convert.ToDecimal(firstRoundTable.Compute("max(postMoney)", string.Empty).ToString()) : firstRoundValue;
                }
            }

            decimal[] markups = new decimal[2];
            markups[0] = currentPostMoney / firstRoundValue;
            markups[1] = totalCost;
            return markups;
        }
        public static List<Xirr.Program.CashItem> getCashFlow(DataRow[] InvHis, DateTime endDate, double endValuation)
        {
            List<Xirr.Program.CashItem> cashFlow = new List<Xirr.Program.CashItem>();
            if (InvHis != null && InvHis.Length > 0)
            {
                for (int x = 0; x < InvHis.Length; x++)
                {
                    DateTime closeDate = DateTime.Parse(InvHis[x]["closeDate"].ToString());
                    double amount = 0;
                    double tCost = double.Parse(InvHis[x]["cost"].ToString());
                    double tProceeds = double.Parse(InvHis[x]["proceeds"].ToString());
                    double tCostRelization = double.Parse(InvHis[x]["costRelization"].ToString());
                    if (tCost > 0 && tProceeds == 0)
                        amount = -tCost;
                    else
                    {
                        if (tProceeds > 0)
                            amount = tProceeds;
                        else
                        {
                            if (tCost + tCostRelization == 0)
                                amount = tProceeds;
                            else
                                amount = 0;
                        }
                    }
                    cashFlow.Add(new Xirr.Program.CashItem(closeDate, amount));
                }

                if (cashFlow != null && cashFlow.Count > 0)
                {
                    cashFlow.Add(new Xirr.Program.CashItem(endDate, endValuation));
                    return cashFlow;
                }
                else
                    return null;
            }
            else
                return null;
        }
        public static double IRRCalByCashFlow(List<Xirr.Program.CashItem> cashFlow)
        {
            try
            {
                if (cashFlow != null && cashFlow.Count > 0)
                {
                    return Xirr.Program.RunScenario(cashFlow);
                }
                else
                    return 0;
            }
            catch (ArgumentException)
            {
                return 0;
            }
            catch (InvalidOperationException)
            {
                return 0;
            }
        }
        public static double IRRCal(DataRow[] InvHis, DateTime endDate, double endValuation)
        {
            try
            {
                List<Xirr.Program.CashItem> cashFlow = new List<Xirr.Program.CashItem>();
                if (InvHis != null && InvHis.Length > 0)
                {
                    for (int x = 0; x < InvHis.Length; x++)
                    {
                        DateTime closeDate = DateTime.Parse(InvHis[x]["closeDate"].ToString());
                        double amount = 0;
                        double tCost = double.Parse(InvHis[x]["cost"].ToString());
                        double tProceeds = double.Parse(InvHis[x]["proceeds"].ToString());
                        double tCostRelization = double.Parse(InvHis[x]["costRelization"].ToString());
                        if (tCost > 0 && tProceeds == 0)
                            amount = -tCost;
                        else
                        {
                            if (tProceeds > 0)
                                amount = tProceeds;
                            else
                            {
                                if (tCost + tCostRelization == 0)
                                    amount = tProceeds;
                                else
                                    amount = 0;
                            }
                        }
                        //if (tProceeds + tCostRelization == 0)
                        //    amount = -tCost;//invest
                        //else
                        //{
                        //    //share sell
                        //    if (tCost + tCostRelization == 0)
                        //        amount = tProceeds;
                        //    else {
                        //        if (tCost == 0 && tProceeds > 0)//dividends
                        //            amount = tProceeds;
                        //        else
                        //            amount = -(tCost + tProceeds);
                        //    }
                        //}
                        cashFlow.Add(new Xirr.Program.CashItem(closeDate, amount));
                    }

                    if (cashFlow != null && cashFlow.Count > 0)
                    {
                        cashFlow.Add(new Xirr.Program.CashItem(endDate, endValuation));
                        return Xirr.Program.RunScenario(cashFlow);
                    }
                    else
                        return 0;
                }
                else
                    return 0;
            }
            catch (ArgumentException)
            {
                return 0;
            }
            catch (InvalidOperationException)
            {
                return 0;
            }
        }
        public static double IRRCal(DataRow[] InvHis, DateTime endDate, double endCNYValuation, double rate)
        {
            try
            {
                List<Xirr.Program.CashItem> cashFlow = new List<Xirr.Program.CashItem>();
                if (InvHis != null && InvHis.Length > 0)
                {
                    for (int x = 0; x < InvHis.Length; x++)
                    {
                        DateTime closeDate = DateTime.Parse(InvHis[x]["closeDate"].ToString());
                        double amount = 0;
                        double tCost = double.Parse(InvHis[x]["cost"].ToString());
                        double tProceeds = double.Parse(InvHis[x]["proceeds"].ToString());
                        double tCostRelization = double.Parse(InvHis[x]["costRelization"].ToString());
                        string currency = InvHis[x]["currency"].ToString();
                        if (tCost > 0 && tProceeds == 0)
                            amount = rate > 0 && currency == "USD" ? -tCost * rate : -tCost;
                        else
                        {
                            if (tProceeds > 0)
                                amount = rate > 0 && currency == "USD" ? tProceeds * rate : tProceeds;
                            else
                            {
                                if (tCost + tCostRelization == 0)
                                    amount = rate > 0 && currency == "USD" ? tProceeds * rate : tProceeds;
                                else
                                    amount = 0;
                            }
                        }
                        //if (tProceeds + tCostRelization == 0)
                        //    amount = -tCost;//invest
                        //else
                        //{
                        //    //share sell
                        //    if (tCost + tCostRelization == 0)
                        //        amount = tProceeds;
                        //    else {
                        //        if (tCost == 0 && tProceeds > 0)//dividends
                        //            amount = tProceeds;
                        //        else
                        //            amount = -(tCost + tProceeds);
                        //    }
                        //}
                        cashFlow.Add(new Xirr.Program.CashItem(closeDate, amount));
                    }

                    if (cashFlow != null && cashFlow.Count > 0)
                    {
                        cashFlow.Add(new Xirr.Program.CashItem(endDate, endCNYValuation));
                        return Xirr.Program.RunScenario(cashFlow);
                    }
                    else
                        return 0;
                }
                else
                    return 0;
            }
            catch (ArgumentException)
            {
                return 0;
            }
            catch (InvalidOperationException)
            {
                return 0;
            }
        }
        public static double RunScenario(List<CashItem> cashFlow)
        {
            try
            {
                try
                {
                    var result = CalcXirr(cashFlow, NewthonsMethod);
                    return result;
                }
                catch (InvalidOperationException)
                {
                    // Failed: try another algorithm
                    var result = CalcXirr(cashFlow, BisectionMethod);
                    return result;
                }
            }
            catch (ArgumentException)
            {
                return 0;
            }
            catch (InvalidOperationException)
            {
                return 0;
            }
        }

        private static double CalcXirr(List<CashItem> cashFlow, Func<List<CashItem>, double> method)
        {
            if (cashFlow.Count(cf => cf.Amount > 0) == 0)
                throw new ArgumentException("Add at least one positive item");

            if (cashFlow.Count(c => c.Amount < 0) == 0)
                throw new ArgumentException("Add at least one negative item");

            var result = method(cashFlow);

            if (Double.IsInfinity(result))
                throw new InvalidOperationException("Could not calculate: Infinity");

            if (Double.IsNaN(result))
                throw new InvalidOperationException("Could not calculate: Not a number");

            return result;
        }

        private static Double NewtonsMethodImplementation(List<CashItem> cashFlow,
                                                          Func<List<CashItem>, Double, Double> f,
                                                          Func<List<CashItem>, Double, Double> df,
                                                          Double guess = DefaultGuess,
                                                          Double tolerance = DefaultTolerance,
                                                          int maxIterations = MaxIterations)
        {
            var x0 = guess;
            var i = 0;
            Double error;
            do
            {
                var dfx0 = df(cashFlow, x0);
                if (Math.Abs(dfx0 - 0) < Double.Epsilon)
                    throw new InvalidOperationException("Could not calculate: No solution found. df(x) = 0");

                var fx0 = f(cashFlow, x0);
                var x1 = x0 - fx0 / dfx0;
                error = Math.Abs(x1 - x0);

                x0 = x1;
            } while (error > tolerance && ++i < maxIterations);
            if (i == maxIterations)
                throw new InvalidOperationException("Could not calculate: No solution found. Max iterations reached.");

            return x0;
        }

        internal static Double BisectionMethodImplementation(List<CashItem> cashFlow,
                                                             Func<List<CashItem>, Double, Double> f,
                                                             Double tolerance = DefaultTolerance,
                                                             int maxIterations = MaxIterations)
        {
            // From "Applied Numerical Analysis" by Gerald
            var brackets = Brackets.Find(Xnpv, cashFlow);
            if (Math.Abs(brackets.First - brackets.Second) < Double.Epsilon)
                throw new ArgumentException("Could not calculate: bracket failed");

            Double f3;
            Double result;
            var x1 = brackets.First;
            var x2 = brackets.Second;

            var i = 0;
            do
            {
                var f1 = f(cashFlow, x1);
                var f2 = f(cashFlow, x2);

                if (Math.Abs(f1) < Double.Epsilon && Math.Abs(f2) < Double.Epsilon)
                    throw new InvalidOperationException("Could not calculate: No solution found");

                if (f1 * f2 > 0)
                    throw new ArgumentException("Could not calculate: bracket failed for x1, x2");

                result = (x1 + x2) / 2;
                f3 = f(cashFlow, result);

                if (f3 * f1 < 0)
                    x2 = result;
                else
                    x1 = result;
            } while (Math.Abs(x1 - x2) / 2 > tolerance && Math.Abs(f3) > Double.Epsilon && ++i < maxIterations);

            if (i == maxIterations)
                throw new InvalidOperationException("Could not calculate: No solution found");

            return result;
        }

        private static Double Xnpv(List<CashItem> cashFlow, Double rate)
        {
            if (rate <= -1)
                rate = -1 + 1E-10; // Very funky ... Better check what an IRR <= -100% means

            var startDate = cashFlow.OrderBy(i => i.Date).First().Date;
            return
                (from item in cashFlow
                 let days = -(item.Date - startDate).Days
                 select item.Amount * Math.Pow(1 + rate, days / DaysPerYear)).Sum();
        }

        private static Double XnpvPrime(List<CashItem> cashFlow, Double rate)
        {
            var startDate = cashFlow.OrderBy(i => i.Date).First().Date;
            return (from item in cashFlow
                    let daysRatio = -(item.Date - startDate).Days / DaysPerYear
                    select item.Amount * daysRatio * Math.Pow(1.0 + rate, daysRatio - 1)).Sum();
        }

        public struct Brackets
        {
            public readonly Double First;
            public readonly Double Second;

            public Brackets(Double first, Double second)
            {
                First = first;
                Second = second;
            }

            internal static Brackets Find(Func<List<CashItem>, Double, Double> f,
                                          List<CashItem> cashFlow,
                                          Double guess = DefaultGuess,
                                          int maxIterations = MaxIterations)
            {
                const Double bracketStep = 0.5;
                var leftBracket = guess - bracketStep;
                var rightBracket = guess + bracketStep;
                var i = 0;
                while (f(cashFlow, leftBracket) * f(cashFlow, rightBracket) > 0 && i++ < maxIterations)
                {
                    leftBracket -= bracketStep;
                    rightBracket += bracketStep;
                }

                return i >= maxIterations
                           ? new Brackets(0, 0)
                           : new Brackets(leftBracket, rightBracket);
            }
        }

        public struct CashItem
        {
            public DateTime Date;
            public Double Amount;

            public CashItem(DateTime date, Double amount)
            {
                Date = date;
                Amount = amount;
            }
        }
    }
}