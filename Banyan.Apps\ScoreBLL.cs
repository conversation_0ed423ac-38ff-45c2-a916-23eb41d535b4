﻿using Banyan.Code;
using Banyan.Domain;
using System;
using System.Collections.Specialized;
using System.Linq;

namespace Banyan.Apps
{
    public class ScoreBLL : DAL.Base.BaseDAL<ProjectScore>
    {

        private readonly AjaxResult ajaxResult = null;

        public ScoreBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        /// <summary>
        /// 分页查询记录
        /// </summary>
        /// <param name="paramValues">页面请求参数</param>
        /// <returns></returns>
        public AjaxResult GetPageList(NameValueCollection paramValues)
        {
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            int pid = WebHelper.GetValueInt("pid", 0, paramValues);

            string strWhere = "";

            if (uid > 0)
            {
                strWhere += $"AND UserId={uid} ";
            }
            if (pid > 0)
            {
                strWhere += $"AND ProjectId={pid} ";
            }

            var ScoreList = GetList(strWhere, pageSize, pageIndex, "*", "Id DESC");
            //if (ScoreList != null && ScoreList.Count() > 0)
            //{
            //    var users = new MemberBLL().GetList($"Id in({string.Join(",", ScoreList.Select(x => x.UserId).Distinct().ToArray())})");
            //    foreach (var item in ScoreList)
            //    {
            //        item.UserName = users.Where(x => x.Id == item.UserId)?.FirstOrDefault()?.RealName;
            //    }
            //}

            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = ScoreList;
            ajaxResult.count = GetCount(strWhere);
            return ajaxResult;
        }

        public AjaxResult Save(ProjectScore model)
        {
            AjaxResult ajaxResult = new AjaxResult();

            if (model.UserId > 0 && model.ProjectId > 0)
            {
                string strWhere = $" UserId={model.UserId} AND ProjectId={model.ProjectId} ";
                var ScoreList = GetModelBySql(strWhere);
                if (ScoreList.Id > 0)
                {
                    model.Id = ScoreList.Id;
                    ajaxResult.data = Update(model);
                }
                else
                {
                    ajaxResult.data = Convert.ToInt32(Add(model)) > 0;
                }
            }
            else
            {
                ajaxResult.data = Convert.ToInt32(Add(model)) > 0;
            }
            ajaxResult.code = (bool)ajaxResult.data ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }

        /// <summary>
        /// 属性更新
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public AjaxResult ScoreSet(NameValueCollection paramValues)
        {
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            int pid = WebHelper.GetValueInt("pid", 0, paramValues);

            var score = WebHelper.GetValueInt("score", 0, paramValues);
            var model = new ProjectScore
            {
                ProjectId = pid,
                UserId = uid,
                Score = score,
            };
            if (uid > 0 && pid > 0)
            {
                string strWhere = $" UserId={uid} AND ProjectId={pid} ";
                var ScoreList = GetModelBySql(strWhere);
                if (ScoreList.Id > 0)
                {
                    model.Id = ScoreList.Id;
                    ajaxResult.data = Update(model);
                }
                else
                {
                    ajaxResult.data = Convert.ToInt32(Add(model)) > 0;
                    //articleBll.UpCountField(model.ArticleId, "comment");
                }
            }
            else
            {
                ajaxResult.data = Convert.ToInt32(Add(model)) > 0;
                //articleBll.UpCountField(model.ArticleId, "comment");
            }

            ajaxResult.code = (bool)ajaxResult.data ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }

        /// <summary>
        /// 添加评分
        /// </summary>
        /// <param name="user"></param>
        /// <param name="pid"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        public AjaxResult ScoreSet(Member user, int pid, int score)
        {
            if (user == null)
            {
                ajaxResult.code = (int)ResultCode.unlogin;
                ajaxResult.msg = "请先登录";
                return ajaxResult;
            }
            var model = new ProjectScore
            {
                ProjectId = pid,
                UserId = user.Id,
                Score = score,
            };
            if (user.Id > 0 && pid > 0)
            {
                string strWhere = $" UserId={user.Id} AND ProjectId={pid} ";
                var ScoreList = GetModelBySql(strWhere);
                if (ScoreList.Id > 0)
                {
                    model.Id = ScoreList.Id;
                    ajaxResult.data = Update(model);
                }
                else
                {
                    ajaxResult.data = Convert.ToInt32(Add(model)) > 0;
                }
            }
            else
            {
                ajaxResult.data = Convert.ToInt32(Add(model)) > 0;
            }

            ajaxResult.code = model.Id > 0 ? (int)ResultCode.success : (int)ResultCode.exception;
            return ajaxResult;
        }

        /// <summary>
        /// 获取评分
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public AjaxResult GetScores(NameValueCollection paramValues)
        {
            int ProjectId = WebHelper.GetValueInt("pid", 0, paramValues);
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);

            if (ProjectId < 0)
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.paramerror);
                return ajaxResult;
            }

            ajaxResult.code = (int)ResultCode.success;

            var ScoreList = GetList($" ProjectId={ProjectId} ", pageSize, pageIndex, "*", "AddTime DESC");
            if (ScoreList != null && ScoreList.Count() > 0)
            {
                var userAvatars = new MemberBLL().GetList($"{string.Join(",", ScoreList.Select(x => x.UserId).ToArray())}"); //查询用户头像信息
                ajaxResult.data = ScoreList.Select(item =>
                {
                    var user = userAvatars.Where(x => x.Id == item.UserId)?.FirstOrDefault();
                    return new
                    {
                        item.Id,
                        item.Score,
                        item.ProjectId,
                        item.UserId,
                        UserName = user?.RealName,
                        UserAvatar = user?.Avatar,
                        AddTime = item.AddTime.ToString("yyyy-MM-dd HH:mm"),
                    };
                });
            }
            return ajaxResult;
        }
    }
}
