﻿using System;
using System.Security.Cryptography;
using System.Text;

namespace Banyan.Code
{
    /// <summary>
    /// MD5加密
    /// </summary>
    public class Md5
    {
        public static string md5(string pwd, int type)
        {
            byte[] result = Encoding.Default.GetBytes(pwd);
            MD5CryptoServiceProvider md5 = new MD5CryptoServiceProvider();
            byte[] output = md5.ComputeHash(result);
            if (type == 16)
                return BitConverter.ToString(output).Replace("-", "").ToLower().Substring(8, 16);
            else
                return BitConverter.ToString(output).Replace("-", "").ToLower();
        }
    }
}
