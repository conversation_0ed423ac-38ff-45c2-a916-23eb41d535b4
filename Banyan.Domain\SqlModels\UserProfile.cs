using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    /// <summary>
    /// 用户兴趣画像表
    /// </summary>
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public class UserProfile
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [SqlField]
        public int UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        [SqlField]
        public string UserName { get; set; }

        /// <summary>
        /// 兴趣描述
        /// </summary>
        [SqlField]
        public string InterestDescription { get; set; }

        /// <summary>
        /// 项目数量
        /// </summary>
        [SqlField]
        public int ProjectCount { get; set; }

        /// <summary>
        /// 标签权重JSON
        /// </summary>
        [SqlField]
        public string TagWeights { get; set; }

        /// <summary>
        /// 点击统计JSON
        /// </summary>
        [SqlField]
        public string ClickStats { get; set; }

        /// <summary>
        /// 用户兴趣向量JSON（1024维向量）
        /// </summary>
        [SqlField]
        public string InterestVector { get; set; }

        /// <summary>
        /// 向量更新时间
        /// </summary>
        [SqlField]
        public DateTime VectorUpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SqlField]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [SqlField]
        public DateTime UpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 状态：0-禁用，1-启用
        /// </summary>
        [SqlField]
        public int Status { get; set; } = 1;
    }
}