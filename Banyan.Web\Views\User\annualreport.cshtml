﻿@using Banyan.Domain
@{
    ViewBag.Name = "附件检索";
    Layout = "/Views/Shared/_LayoutSuper.cshtml";
    var yearList = ViewData["yearList"] as List<int>;
    var creatorlist = (List<Banyan.Domain.Member>)ViewData["creatorlist"];
    var manager = ViewData["manager"] as Banyan.Domain.Member;
    var isAdmin = manager.Levels == (byte)Banyan.Domain.MemberLevels.Administrator || manager.Levels == (byte)Banyan.Domain.MemberLevels.SuperUser;
}

<style>
    td .layui-table-cell, .layui-table-cell img {
        height: 35px !important;
        line-height: 35px !important;
        padding: 0 15px;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
        box-sizing: border-box;
    }

    a {
        color: #4E6EF2;
    }
    .link {
        font-size: 18px;
        text-decoration: underline;
    }
</style>

<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li>年终总结</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <div class="tab-pane active">
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="pull-right search-bar"></div>
                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">

                                @if (isAdmin)
                                {
                                    <div class="form-group">
                                        <select class="form-control" id="creator" name="creator" size="1">
                                            <option value="">编辑人</option>
                                            @if (creatorlist != null && creatorlist.Count() > 0)
                                            {
                                                foreach (var creator in creatorlist)
                                                {
                                                    <option value="@(creator.RealName)">@(creator.RealName)</option>
                                                }
                                            }
                                        </select>
                                    </div>
                                }
                                <div class="form-group">
                                    <div>
                                        <select class="form-control" id="year" size="1" name="year" v-model="model.year">
                                            @foreach (var year in yearList)
                                            {
                                                <option value=@year>@year</option>
                                            }
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="sr-only" for="title-name">搜索</label>
                                    <input class="form-control" type="text" id="keyname" name="keyname" placeholder="搜索">
                                </div>

                                <div class="form-group">
                                    <a class="btn btn-default" id="dosearch">搜索</a>
                                </div>


                                <div class="form-group">
                                    <a class="btn btn-default" id="create"><i class="fa fa-plus"></i>&nbsp;&nbsp;新建</a>
                                </div>

                            </form>

                        </div>
                    </div>
                </div>
                <table class="layui-hide" id="table-list" lay-filter="list-filter"></table>
            </div>
        </div>
    </div>
</div>
@section modal{
    <div id="modal">
        <div class="edit-modal form-horizontal" style='margin: 20px;text-align:center' id="project-form" name="project-form">
            <h3 style="margin: 60px; font-size: 22px;">点击进入对应类型填报</h3>
            <div class="form-group">
                <a class="link" href="/User/AnnualReportSetInvest">投资团队</a>
            </div>
            <div class="form-group">
                <a class="link" href="/User/AnnualReportSet">DD团队</a>
            </div>
            <div class="form-group">
                <a class="link" href="/User/AnnualReportSet">参谋部</a>
            </div>
            <div class="form-group">
                <a class="link" href="/User/AnnualReportSet">后台支持团队</a>
            </div>
        </div>
    </div>
}
<script type="text/html" id="bartpl">
    <div class="btn-group" role="group" data-toggle="buttons">
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="预览" lay-event="preview" data-original-title="预览"><i class="fa fa-eye"></i></button>
        {{#  if(d.isOperate){ }}
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="编辑" lay-event="modify" data-original-title="编辑"><i class="fa fa-pencil"></i></button>
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="删除" lay-event="delete" data-original-title="删除"><i class="fa fa-times"></i></button>
        {{#  } }}
    </div>
</script>


@section scripts{
    <link href="/content/js/plugins/select2/select2.min.css" rel="stylesheet" />
    <link href="/content/js/plugins/select2/select2-bootstrap.min.css" rel="stylesheet" />
    <script type="text/javascript" src="/content/js/plugins/select2/select2.min.js"></script>
    <link href="~/Content/js/plugins/lightgallery/css/lightgallery.min.css" rel="stylesheet" />

    <script src="~/Content/js/plugins/lightgallery/js/picturefill.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lightgallery.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-fullscreen.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-thumbnail.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-video.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-autoplay.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-zoom.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-hash.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/lg-pager.min.js"></script>
    <script src="~/Content/js/plugins/lightgallery/js/jquery.mousewheel.min.js"></script>
    <script type="text/javascript">
        function GetStringDate(dateStr) {
            if (!dateStr) return ''
            var date = eval('new ' + dateStr.substr(1, dateStr.length - 2));
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            var res = year + "-" + month + "-" + day;
            if (res == "1-1-1") return "";
            return res;
        }

        function preview(id) {
            var h = document.documentElement.clientHeight || document.body.clientHeight;
            layer.open({
                type: 2,
                area: ['950px', h * 0.9 + 'px'],
                title: "项目预览",
                content: '/user/annualReportPreview?id=' + id,
            });
        }

        layui.use(['laypage', 'layer', 'table', 'upload', 'laydate'], function () {

            var laypage = layui.laypage,
                layer = layui.layer,
                laydate = layui.laydate,
                table = layui.table;

            table.render({
                elem: '#table-list'
                , height: 570
                , url: '@(Url.Action("annualreportlist", "adminapi"))'
                , page: { limit: 10, hash: 'page', curr: location.hash.replace('#!page=', '') }
                , method: 'post'
                , cols: [[
                     {
                        field: 'year', title: '年度', width: 100, templet: function (d) {
                            return `<a href="/user/annualReportPreview?id=${d.Id}" target="_blank">${d.year}</a>`
                        }
                    },
                    {
                        field: 'status', title: '状态', width: 100, templet: function (d) {
                            if (d.status == 1) {
                                return "已发布";
                            } else if(d.status == 2) {
                                return "草稿";
                            }
                        }
                    }
                    ,
                    {
                        field: 'type', title: '类别', width: 100
                    }
                    , {
                        field: 'AddTime', title: '添加时间', sort:true, width: 110, templet: function (d) {
                            return GetStringDate(d.AddTime)
                        }
                    }
                    , {
                        field: 'Creator', title: '创建人', width: 100
                    }
                    , {
                        field: 'AddTime', title: '修改时间', sort: true, width: 110, templet: function (d) {
                            return GetStringDate(d.UpdateTime)
                        }
                    }
                    , { fixed: 'right', width: 120, align: 'center', toolbar: '#bartpl' }
                ]],
                done: function () { }
            });
            table.on('tool(list-filter)', function (obj) {
                var data = obj.data
                    , layEvent = obj.event;

                if (layEvent === 'delete') {
                    layer.confirm('确认删除该项目吗？', function (index) {
                        layer.close(index);
                        $.ajax({
                            type: 'POST',
                            url: '@(Url.Action("annualReportDelete", "adminapi"))',
                            data: { id: data.Id },
                            success: function (data) {
                                if (data.code == 0) {
                                    layer.msg('操作成功！');
                                    $('#dosearch').click();
                                } else {
                                    layer.msg(data.msg);
                                }
                            },
                            error: function () {
                                layui.layer.msg("很抱歉，请求异常！");
                            }
                        });
                    });
                } else if (layEvent === 'modify') {
                    window.location.href = "/user/annualReportSet" + data.type +"?id=" + data.Id;
                }  else if (layEvent === 'preview') {
                    preview(data.Id);
                }
                return;
            });

            $('#create').on('click', function () {
                window.location.href = "/User/AnnualReportSetInvest"
                //layer.open({
                //    type: 1,
                //    area: ['550px', '450px'],
                //    title: "请选择类型",
                //    content: $('#modal')
                //});
            });

            $('#keyname').on('keypress', function (event) {
                if (event.keyCode === 13) {
                    $('#dosearch').trigger('click');
                }
            });
            $('#dosearch').on('click', dosearch);
            function dosearch() {
                queryParams = {
                    Name: $('#keyname').val(),
                    Creator: $('#creator').val(),
                    year: $('#year').val(),
                }
                table.reload('table-list', {
                    where: queryParams, page: { curr: 1 },
                });
            }

            $('#suffix').on('change', dosearch);

        })

    </script>
}
