﻿using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public class Attachment
    {
        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        /// <summary>
        /// 来源ID
        /// </summary>
        [SqlField]
        public int SourceId { get; set; }

        [SqlField]
        public Byte SourceType { get; set; } = (Byte)SourceTypeEnum.Project;

        [SqlField]
        public string AtName { get; set; }

        [SqlField]
        public string AtSuffix { get; set; }

        [SqlField]
        public string AtUrl { get; set; }

        [SqlField]
        public string Creator { get; set; }

        [SqlField]
        public string Content { get; set; }

        /// <summary>
        /// 附件大小
        /// </summary>
        [SqlField]
        public int AtLength { get; set; }

        [SqlField]
        public DateTime AddTime { get; set; } = DateTime.Now;

        [SqlField]
        public string Remark { get; set; }

        [SqlField]
        public string Path { get; set; }

    }

    public enum SourceTypeEnum
    {
        Project = 1,
        Meet = 2
    }
}
