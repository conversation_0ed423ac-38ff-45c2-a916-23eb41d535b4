using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Banyan.Code;
using Banyan.Domain;
using DAL.Base;

namespace Banyan.Apps
{
    /// <summary>
    /// 新闻预计算服务
    /// 独立的服务，负责预计算热门新闻的相似度和用户个性化推荐，避免循环依赖
    /// </summary>
    public class NewsPrecomputeService
    {
        private readonly NewsBLL _newsBLL;
        private readonly VectorService _vectorService;
        private readonly NewsVectorizationService _vectorizationService;
        private readonly UserInterestVectorRetrieval _userInterestVectorRetrieval;
        private readonly MemberBLL _memberBLL;
        
        // 单例实例
        private static readonly Lazy<NewsPrecomputeService> _instance = 
            new Lazy<NewsPrecomputeService>(() => new NewsPrecomputeService(), LazyThreadSafetyMode.ExecutionAndPublication);
        
        // 预计算缓存键前缀
        private const string PRECOMPUTED_SIMILAR_NEWS_PREFIX = "precomputed_similar_news:";
        private const string PRECOMPUTED_USER_RECOMMENDATIONS_PREFIX = "precomputed_user_recommendations:";
        
        // 预计算缓存过期时间（小时）
        private const int PRECOMPUTED_CACHE_HOURS = 72;
        private const int USER_RECOMMENDATIONS_CACHE_HOURS = 36;
        
        /// <summary>
        /// 获取NewsPrecomputeService的单例实例
        /// </summary>
        public static NewsPrecomputeService Instance => _instance.Value;
        
        /// <summary>
        /// 私有构造函数，确保单例模式
        /// </summary>
        private NewsPrecomputeService()
        {
            _newsBLL = new NewsBLL();
            _vectorService = new VectorService();
            _vectorizationService = new NewsVectorizationService();
            _userInterestVectorRetrieval = new UserInterestVectorRetrieval();
            _memberBLL = new MemberBLL();
            
            Logger.Info("NewsPrecomputeService initialized");
        }
        
        /// <summary>
        /// 预计算热门新闻的相似新闻
        /// </summary>
        /// <param name="topN">热门新闻数量</param>
        /// <param name="similarCount">每篇新闻的相似新闻数量</param>
        /// <param name="threshold">相似度阈值</param>
        /// <returns>预计算结果</returns>
        public async Task<PrecomputeResult> PrecomputeHotNewsRecommendationsAsync(int topN = 50, int similarCount = 10, double threshold = 0.5)
        {
            var result = new PrecomputeResult
            {
                StartTime = DateTime.Now,
                TotalNewsCount = topN,
                SuccessCount = 0,
                FailedCount = 0
            };
            
            try
            {
                Logger.Info($"开始预计算热门新闻的相似新闻，热门新闻数量: {topN}，相似新闻数量: {similarCount}");
                
                // 1. 获取热门新闻
                var hotNews = GetHotNews(topN);
                if (hotNews.Count == 0)
                {
                    Logger.Warn("没有找到热门新闻，预计算结束");
                    result.EndTime = DateTime.Now;
                    result.Duration = result.EndTime - result.StartTime;
                    return result;
                }
                
                Logger.Info($"找到 {hotNews.Count} 篇热门新闻");
                
                // 2. 为每篇热门新闻预计算相似新闻
                int processedCount = 0;
                
                foreach (var news in hotNews)
                {
                    try
                    {
                        // 检查是否已经有预计算的缓存
                        string cacheKey = $"{PRECOMPUTED_SIMILAR_NEWS_PREFIX}{news.Id}";
                        var cachedResult = RedisUtil.Get<List<NewsVectorSimilarity>>(cacheKey);

                        if (cachedResult != null && cachedResult.Count > 0)
                        {
                            Logger.Debug($"新闻 {news.Id} 已有预计算缓存，跳过");
                            result.SuccessCount++;
                            continue;
                        }

                        // 计算相似新闻
                        var similarNews = await ComputeSimilarNews(news.Id, similarCount, threshold);

                        // 缓存结果
                        if (similarNews != null && similarNews.Count > 0)
                        {
                            RedisUtil.Set<List<NewsVectorSimilarity>>(cacheKey, similarNews, TimeSpan.FromHours(PRECOMPUTED_CACHE_HOURS));
                            result.SuccessCount++;
                            Logger.Debug($"成功预计算新闻 {news.Id} 的相似新闻，数量: {similarNews.Count}");
                        }
                        else
                        {
                            result.FailedCount++;
                            Logger.Warn($"新闻 {news.Id} 没有找到相似新闻");
                        }
                    }
                    catch (Exception ex)
                    {
                        result.FailedCount++;
                        Logger.Error($"预计算新闻 {news.Id} 的相似新闻失败: {ex.Message}", ex);
                    }
                    
                    // 更新进度
                    processedCount++;
                    if (processedCount % 10 == 0 || processedCount == hotNews.Count)
                    {
                        double progressPercentage = (double)processedCount / hotNews.Count * 100;
                        Logger.Info($"预计算进度: {progressPercentage:F2}% ({processedCount}/{hotNews.Count})");
                    }
                    
                    // 避免过度占用系统资源，添加短暂延迟
                    await Task.Delay(100);
                }
                
                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;
                
                Logger.Info($"预计算完成，总数: {hotNews.Count}，成功: {result.SuccessCount}，失败: {result.FailedCount}，耗时: {result.Duration.TotalSeconds:F2}秒");
                return result;
            }
            catch (Exception ex)
            {
                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;
                result.FailedCount = result.TotalNewsCount - result.SuccessCount;
                
                Logger.Error($"预计算热门新闻的相似新闻失败: {ex.Message}", ex);
                return result;
            }
        }
        
        /// <summary>
        /// 获取预计算的相似新闻
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <returns>相似新闻列表，如果没有预计算则返回null</returns>
        public List<NewsVectorSimilarity> GetPrecomputedSimilarNews(int newsId)
        {
            try
            {
                string cacheKey = $"{PRECOMPUTED_SIMILAR_NEWS_PREFIX}{newsId}";
                return RedisUtil.Get<List<NewsVectorSimilarity>>(cacheKey);
            }
            catch (Exception ex)
            {
                Logger.Error($"获取预计算的相似新闻失败，新闻ID: {newsId}, 错误: {ex.Message}", ex);
                return null;
            }
        }
        
        /// <summary>
        /// 计算相似新闻（核心算法，独立于NewsVectorSearch）
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <param name="limit">返回结果数量限制</param>
        /// <param name="threshold">相似度阈值</param>
        /// <returns>相似新闻列表</returns>
        private async Task<List<NewsVectorSimilarity>> ComputeSimilarNews(int newsId, int limit, double threshold)
        {
            try
            {
                // 1. 获取目标新闻的向量
                var targetVector = await _vectorizationService.GetNewsVectorAsync(newsId, _newsBLL);
                if (targetVector == null || targetVector.Length == 0)
                {
                    Logger.Warn($"新闻ID {newsId} 没有有效的向量");
                    return new List<NewsVectorSimilarity>();
                }
                
                // 2. 获取候选新闻ID（排除自身，只获取最近30天的新闻）
                var thirtyDaysAgo = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd");
                var where = $"VectorStatus = 1 AND PubTime >= '{thirtyDaysAgo}' AND Id != {newsId}";
                var candidateNews = _newsBLL.GetList(where, 500, 1, "Id", "PubTime DESC, Id DESC");
                
                if (candidateNews.Count == 0)
                {
                    Logger.Warn($"没有找到候选新闻用于计算与新闻ID {newsId} 的相似度");
                    return new List<NewsVectorSimilarity>();
                }
                
                var candidateIds = candidateNews.Select(n => n.Id).ToList();
                
                // 3. 批量获取候选新闻的向量
                var candidateVectors = await _vectorizationService.GetNewsVectorsBatchAsync(candidateIds, _newsBLL);
                if (candidateVectors.Count == 0)
                {
                    Logger.Warn($"没有找到有效的候选新闻向量");
                    return new List<NewsVectorSimilarity>();
                }
                
                // 4. 计算相似度
                var similarities = new List<NewsVectorSimilarity>();
                double targetMagnitude = CalculateVectorMagnitude(targetVector);
                
                foreach (var entry in candidateVectors)
                {
                    int candidateId = entry.Key;
                    double[] candidateVector = entry.Value;
                    
                    double similarity = CalculateCosineSimilarity(targetVector, candidateVector, targetMagnitude);
                    
                    if (similarity >= threshold)
                    {
                        similarities.Add(new NewsVectorSimilarity
                        {
                            NewsId = candidateId,
                            Similarity = similarity
                        });
                    }
                }
                
                // 5. 排序并限制数量
                var result = similarities
                    .OrderByDescending(s => s.Similarity)
                    .Take(limit)
                    .ToList();
                
                // 6. 填充新闻详情
                await FillNewsDetails(result);
                
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"计算新闻ID {newsId} 的相似新闻失败: {ex.Message}", ex);
                return new List<NewsVectorSimilarity>();
            }
        }
        
        /// <summary>
        /// 计算余弦相似度（优化版本）
        /// </summary>
        /// <param name="vector1">向量1</param>
        /// <param name="vector2">向量2</param>
        /// <param name="magnitude1">向量1的模长（预计算）</param>
        /// <returns>相似度</returns>
        private double CalculateCosineSimilarity(double[] vector1, double[] vector2, double magnitude1)
        {
            if (vector1 == null || vector2 == null || vector1.Length != vector2.Length)
            {
                return 0;
            }

            double dotProduct = 0;
            double magnitude2 = 0;

            for (int i = 0; i < vector1.Length; i++)
            {
                dotProduct += vector1[i] * vector2[i];
                magnitude2 += vector2[i] * vector2[i];
            }

            magnitude2 = Math.Sqrt(magnitude2);

            if (magnitude1 == 0 || magnitude2 == 0)
            {
                return 0;
            }

            return dotProduct / (magnitude1 * magnitude2);
        }
        
        /// <summary>
        /// 计算向量的模长
        /// </summary>
        /// <param name="vector">向量</param>
        /// <returns>模长</returns>
        private double CalculateVectorMagnitude(double[] vector)
        {
            if (vector == null || vector.Length == 0)
            {
                return 0;
            }
            
            double sumOfSquares = 0;
            for (int i = 0; i < vector.Length; i++)
            {
                sumOfSquares += vector[i] * vector[i];
            }
            
            return Math.Sqrt(sumOfSquares);
        }
        
        /// <summary>
        /// 填充新闻详情
        /// </summary>
        /// <param name="similarities">相似度结果列表</param>
        /// <returns>任务</returns>
        private async Task FillNewsDetails(List<NewsVectorSimilarity> similarities)
        {
            if (similarities == null || similarities.Count == 0)
            {
                return;
            }

            await Task.Delay(1); // 避免编译器警告

            try
            {
                // 获取所有新闻ID
                var newsIds = similarities.Select(s => s.NewsId).ToList();
                
                // 构建SQL查询，批量获取新闻数据
                var idList = string.Join(",", newsIds);
                var fields = "Id, Title, Source, Classify, PubTime";
                var where = $"Id IN ({idList})";
                
                var newsList = _newsBLL.GetList(where, newsIds.Count, 1, fields, "Id");
                
                // 创建ID到新闻的映射
                var newsMap = newsList.ToDictionary(n => n.Id);
                
                // 填充详情
                foreach (var similarity in similarities)
                {
                    if (newsMap.TryGetValue(similarity.NewsId, out var news))
                    {
                        similarity.News = news;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("填充新闻详情失败", ex);
            }
        }
        
        /// <summary>
        /// 为所有有兴趣向量的用户预计算推荐结果
        /// </summary>
        /// <param name="recommendationCount">每个用户的推荐数量</param>
        /// <param name="threshold">相似度阈值</param>
        /// <returns>预计算结果</returns>
        public async Task<PrecomputeResult> PrecomputeUserRecommendationsAsync(int recommendationCount = 20, double threshold = 0.4)
        {
            var result = new PrecomputeResult
            {
                StartTime = DateTime.Now,
                TotalNewsCount = 0, // 这里表示用户数量
                SuccessCount = 0,
                FailedCount = 0
            };
            
            try
            {
                Logger.Info($"开始为所有有兴趣向量的用户预计算推荐结果，推荐数量: {recommendationCount}，相似度阈值: {threshold}");
                
                // 1. 获取所有活跃用户
                var activeUsers = await GetActiveUsersWithInterestVectorsAsync();
                if (activeUsers.Count == 0)
                {
                    Logger.Warn("没有找到有兴趣向量的活跃用户，预计算结束");
                    result.EndTime = DateTime.Now;
                    result.Duration = result.EndTime - result.StartTime;
                    return result;
                }
                
                result.TotalNewsCount = activeUsers.Count; // 设置总用户数
                Logger.Info($"找到 {activeUsers.Count} 个有兴趣向量的活跃用户");
                
                // 2. 为每个用户预计算推荐结果
                int processedCount = 0;
                
                foreach (var userId in activeUsers)
                {
                    try
                    {
                        // 检查是否已经有预计算的缓存
                        string cacheKey = $"{PRECOMPUTED_USER_RECOMMENDATIONS_PREFIX}{userId}";
                        var cachedResult = RedisUtil.Get<List<NewsVectorSimilarity>>(cacheKey);

                        if (cachedResult != null && cachedResult.Count > 0)
                        {
                            Logger.Debug($"用户 {userId} 已有预计算推荐缓存，跳过");
                            result.SuccessCount++;
                            continue;
                        }

                        // 计算用户推荐
                        var userRecommendations = await ComputeUserRecommendations(userId, recommendationCount, threshold);

                        // 缓存结果
                        if (userRecommendations != null && userRecommendations.Count > 0)
                        {
                            RedisUtil.Set<List<NewsVectorSimilarity>>(cacheKey, userRecommendations, TimeSpan.FromHours(USER_RECOMMENDATIONS_CACHE_HOURS));
                            result.SuccessCount++;
                            Logger.Debug($"成功预计算用户 {userId} 的推荐结果，数量: {userRecommendations.Count}");
                        }
                        else
                        {
                            result.FailedCount++;
                            Logger.Warn($"用户 {userId} 没有找到推荐结果");
                        }
                    }
                    catch (Exception ex)
                    {
                        result.FailedCount++;
                        Logger.Error($"预计算用户 {userId} 的推荐结果失败: {ex.Message}", ex);
                    }
                    
                    // 更新进度
                    processedCount++;
                    if (processedCount % 5 == 0 || processedCount == activeUsers.Count)
                    {
                        double progressPercentage = (double)processedCount / activeUsers.Count * 100;
                        Logger.Info($"用户推荐预计算进度: {progressPercentage:F2}% ({processedCount}/{activeUsers.Count})");
                    }
                    
                    // 避免过度占用系统资源，添加短暂延迟
                    await Task.Delay(200);
                }
                
                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;
                
                Logger.Info($"用户推荐预计算完成，总用户数: {activeUsers.Count}，成功: {result.SuccessCount}，失败: {result.FailedCount}，耗时: {result.Duration.TotalSeconds:F2}秒");
                return result;
            }
            catch (Exception ex)
            {
                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;
                result.FailedCount = result.TotalNewsCount - result.SuccessCount;
                
                Logger.Error($"预计算用户推荐失败: {ex.Message}", ex);
                return result;
            }
        }
        
        /// <summary>
        /// 获取预计算的用户推荐结果
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>推荐结果列表，如果没有预计算则返回null</returns>
        public List<NewsVectorSimilarity> GetPrecomputedUserRecommendations(int userId)
        {
            try
            {
                string cacheKey = $"{PRECOMPUTED_USER_RECOMMENDATIONS_PREFIX}{userId}";
                return RedisUtil.Get<List<NewsVectorSimilarity>>(cacheKey);
            }
            catch (Exception ex)
            {
                Logger.Error($"获取预计算的用户推荐失败，用户ID: {userId}, 错误: {ex.Message}", ex);
                return null;
            }
        }
        
        /// <summary>
        /// 获取所有有兴趣向量的活跃用户
        /// </summary>
        /// <returns>用户ID列表</returns>
        private async Task<List<int>> GetActiveUsersWithInterestVectorsAsync()
        {
            try
            {
                // 获取所有活跃用户 - 使用带缓存的后台任务专用方法
                var allUsers = _memberBLL.GetAllListForBackgroundTask(false); // 不包括审核中的用户，带缓存
                var activeUserIds = new List<int>();

                foreach (var user in allUsers)
                {
                    if (user.Status == (int)MemberStatus.enable && user.Id > 0)
                    {
                        // 检查用户是否有兴趣向量
                        var userVector = await _userInterestVectorRetrieval.GetUserInterestVectorAsync(user.Id);
                        if (userVector != null && userVector.Length > 0)
                        {
                            activeUserIds.Add(user.Id);
                        }
                    }
                }

                Logger.Info($"找到 {activeUserIds.Count} 个有兴趣向量的活跃用户");
                return activeUserIds;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取有兴趣向量的活跃用户失败: {ex.Message}", ex);
                return new List<int>();
            }
        }

        
        /// <summary>
        /// 计算用户推荐（核心算法，独立于NewsRecommendationEngine）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="limit">返回结果数量限制</param>
        /// <param name="threshold">相似度阈值</param>
        /// <returns>推荐结果列表</returns>
        private async Task<List<NewsVectorSimilarity>> ComputeUserRecommendations(int userId, int limit, double threshold)
        {
            try
            {
                // 1. 获取用户兴趣向量
                var userVector = await _userInterestVectorRetrieval.GetUserInterestVectorAsync(userId);
                if (userVector == null || userVector.Length == 0)
                {
                    Logger.Warn($"用户ID {userId} 没有有效的兴趣向量");
                    return new List<NewsVectorSimilarity>();
                }
                
                // 2. 获取候选新闻（最近30天的已向量化新闻）
                var thirtyDaysAgo = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd");
                var where = $"VectorStatus = 1 AND PubTime >= '{thirtyDaysAgo}'";
                var candidateNews = _newsBLL.GetList(where, 500, 1, "Id", "PubTime DESC, Id DESC");
                
                if (candidateNews.Count == 0)
                {
                    Logger.Warn($"没有找到候选新闻用于计算用户 {userId} 的推荐");
                    return new List<NewsVectorSimilarity>();
                }
                
                var candidateIds = candidateNews.Select(n => n.Id).ToList();
                
                // 3. 批量获取候选新闻的向量
                var candidateVectors = await _vectorizationService.GetNewsVectorsBatchAsync(candidateIds, _newsBLL);
                if (candidateVectors.Count == 0)
                {
                    Logger.Warn($"没有找到有效的候选新闻向量");
                    return new List<NewsVectorSimilarity>();
                }
                
                // 4. 计算相似度
                var similarities = new List<NewsVectorSimilarity>();
                double userMagnitude = CalculateVectorMagnitude(userVector);
                
                foreach (var entry in candidateVectors)
                {
                    int candidateId = entry.Key;
                    double[] candidateVector = entry.Value;
                    
                    double similarity = CalculateCosineSimilarity(userVector, candidateVector, userMagnitude);
                    
                    if (similarity >= threshold)
                    {
                        similarities.Add(new NewsVectorSimilarity
                        {
                            NewsId = candidateId,
                            Similarity = similarity
                        });
                    }
                }
                
                // 5. 排序并限制数量
                var result = similarities
                    .OrderByDescending(s => s.Similarity)
                    .Take(limit)
                    .ToList();
                
                // 6. 填充新闻详情
                await FillNewsDetails(result);
                
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"计算用户 {userId} 的推荐失败: {ex.Message}", ex);
                return new List<NewsVectorSimilarity>();
            }
        }
        
        /// <summary>
        /// 综合预计算：同时预计算热门新闻相似度和用户推荐
        /// </summary>
        /// <param name="hotNewsCount">热门新闻数量</param>
        /// <param name="similarCount">每篇新闻的相似新闻数量</param>
        /// <param name="newsThreshold">新闻相似度阈值</param>
        /// <param name="userRecommendationCount">每个用户的推荐数量</param>
        /// <param name="userThreshold">用户推荐相似度阈值</param>
        /// <returns>综合预计算结果</returns>
        public async Task<CombinedPrecomputeResult> PrecomputeAllAsync(
            int hotNewsCount = 50, 
            int similarCount = 10, 
            double newsThreshold = 0.5,
            int userRecommendationCount = 20, 
            double userThreshold = 0.4)
        {
            var result = new CombinedPrecomputeResult
            {
                StartTime = DateTime.Now
            };
            
            try
            {
                Logger.Info("开始综合预计算：热门新闻相似度 + 用户推荐");
                
                // 1. 预计算热门新闻的相似新闻
                Logger.Info("第一阶段：预计算热门新闻相似度");
                result.NewsPrecomputeResult = await PrecomputeHotNewsRecommendationsAsync(hotNewsCount, similarCount, newsThreshold);
                
                // 2. 预计算用户推荐
                Logger.Info("第二阶段：预计算用户推荐");
                result.UserPrecomputeResult = await PrecomputeUserRecommendationsAsync(userRecommendationCount, userThreshold);
                
                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;
                
                Logger.Info($"综合预计算完成，总耗时: {result.Duration.TotalSeconds:F2}秒");
                Logger.Info($"新闻预计算：成功 {result.NewsPrecomputeResult.SuccessCount}，失败 {result.NewsPrecomputeResult.FailedCount}");
                Logger.Info($"用户推荐预计算：成功 {result.UserPrecomputeResult.SuccessCount}，失败 {result.UserPrecomputeResult.FailedCount}");
                
                return result;
            }
            catch (Exception ex)
            {
                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;
                
                Logger.Error($"综合预计算失败: {ex.Message}", ex);
                return result;
            }
        }
        
        /// <summary>
        /// 清除所有预计算缓存
        /// </summary>
        /// <returns>清除结果</returns>
        public async Task<bool> ClearAllPrecomputedCacheAsync()
        {
            try
            {
                Logger.Info("开始清除所有预计算缓存");

                int clearedCount = 0;

                // 清除用户推荐缓存
                var activeUsers = await GetActiveUsersWithInterestVectorsAsync();
                foreach (var userId in activeUsers)
                {
                    string cacheKey = $"{PRECOMPUTED_USER_RECOMMENDATIONS_PREFIX}{userId}";
                    if (RedisUtil.Remove(cacheKey))
                    {
                        clearedCount++;
                    }
                }
                Logger.Info($"清除用户推荐预计算缓存: {clearedCount} 个");

                // 清除新闻相似度缓存
                var hotNews = GetHotNews(100); // 获取更多热门新闻进行清理
                int newsCleared = 0;
                foreach (var news in hotNews)
                {
                    string cacheKey = $"{PRECOMPUTED_SIMILAR_NEWS_PREFIX}{news.Id}";
                    if (RedisUtil.Remove(cacheKey))
                    {
                        newsCleared++;
                    }
                }
                Logger.Info($"清除新闻相似度预计算缓存: {newsCleared} 个");

                clearedCount += newsCleared;
                Logger.Info($"预计算缓存清除完成，总清除数量: {clearedCount}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"清除预计算缓存失败: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 清除指定用户的预计算推荐缓存
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>清除结果</returns>
        public bool ClearUserPrecomputedRecommendations(int userId)
        {
            try
            {
                string cacheKey = $"{PRECOMPUTED_USER_RECOMMENDATIONS_PREFIX}{userId}";
                bool result = RedisUtil.Remove(cacheKey);
                
                if (result)
                {
                    Logger.Info($"成功清除用户 {userId} 的预计算推荐缓存");
                }
                else
                {
                    Logger.Warn($"用户 {userId} 的预计算推荐缓存不存在或清除失败");
                }
                
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"清除用户 {userId} 预计算推荐缓存失败: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 获取预计算统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public async Task<PrecomputeStatistics> GetPrecomputeStatisticsAsync()
        {
            var stats = new PrecomputeStatistics();

            try
            {
                // 检查Redis连接状态
                stats.RedisConnectionStatus = CheckRedisConnection();

                // 统计有兴趣向量的用户数量
                var usersWithVectors = await GetActiveUsersWithInterestVectorsAsync();
                stats.TotalUsersWithVectors = usersWithVectors.Count;

                // 统计有预计算推荐的用户数量
                int usersWithPrecomputedRecommendations = 0;
                foreach (var userId in usersWithVectors)
                {
                    string cacheKey = $"{PRECOMPUTED_USER_RECOMMENDATIONS_PREFIX}{userId}";
                    var cachedResult = RedisUtil.Get<List<NewsVectorSimilarity>>(cacheKey);
                    if (cachedResult != null && cachedResult.Count > 0)
                    {
                        usersWithPrecomputedRecommendations++;
                    }
                }
                stats.UsersWithPrecomputedRecommendations = usersWithPrecomputedRecommendations;

                // 统计热门新闻和预计算相似新闻
                var hotNews = GetHotNews(50);
                stats.HotNewsCount = hotNews.Count;

                int hotNewsWithPrecomputed = 0;
                foreach (var news in hotNews)
                {
                    string cacheKey = $"{PRECOMPUTED_SIMILAR_NEWS_PREFIX}{news.Id}";
                    var cachedResult = RedisUtil.Get<List<NewsVectorSimilarity>>(cacheKey);
                    if (cachedResult != null && cachedResult.Count > 0)
                    {
                        hotNewsWithPrecomputed++;
                    }
                }
                stats.HotNewsWithPrecomputedSimilarity = hotNewsWithPrecomputed;

                // 统计最近30天的候选新闻数量
                var thirtyDaysAgo = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd");
                var where = $"VectorStatus = 1 AND PubTime >= '{thirtyDaysAgo}'";
                var candidateNews = _newsBLL.GetList(where, 1000, 1, "Id", "Id");
                stats.CandidateNewsCount = candidateNews.Count;

                Logger.Info($"预计算统计：总用户 {stats.TotalUsersWithVectors}，有推荐缓存 {stats.UsersWithPrecomputedRecommendations} ({stats.UserRecommendationCoverageRate:F1}%)，" +
                           $"热门新闻 {stats.HotNewsCount}，有相似新闻缓存 {stats.HotNewsWithPrecomputedSimilarity} ({stats.HotNewsCoverageRate:F1}%)，" +
                           $"候选新闻 {stats.CandidateNewsCount}，Redis状态: {(stats.RedisConnectionStatus ? "正常" : "异常")}");
            }
            catch (Exception ex)
            {
                Logger.Error($"获取预计算统计信息失败: {ex.Message}", ex);
            }

            return stats;
        }
        
        /// <summary>
        /// 强制刷新指定用户的预计算推荐（忽略现有缓存）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="recommendationCount">推荐数量</param>
        /// <param name="threshold">相似度阈值</param>
        /// <returns>刷新结果</returns>
        public async Task<bool> ForceRefreshUserRecommendationsAsync(int userId, int recommendationCount = 20, double threshold = 0.4)
        {
            try
            {
                Logger.Info($"强制刷新用户 {userId} 的预计算推荐");

                // 先清除现有缓存
                ClearUserPrecomputedRecommendations(userId);

                // 重新计算推荐
                var userRecommendations = await ComputeUserRecommendations(userId, recommendationCount, threshold);

                if (userRecommendations != null && userRecommendations.Count > 0)
                {
                    string cacheKey = $"{PRECOMPUTED_USER_RECOMMENDATIONS_PREFIX}{userId}";
                    RedisUtil.Set<List<NewsVectorSimilarity>>(cacheKey, userRecommendations, TimeSpan.FromHours(USER_RECOMMENDATIONS_CACHE_HOURS));
                    Logger.Info($"成功强制刷新用户 {userId} 的预计算推荐，数量: {userRecommendations.Count}");
                    return true;
                }
                else
                {
                    Logger.Warn($"用户 {userId} 强制刷新后仍无推荐结果");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"强制刷新用户 {userId} 预计算推荐失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 检查Redis连接状态
        /// </summary>
        /// <returns>连接状态</returns>
        public bool CheckRedisConnection()
        {
            try
            {
                // 尝试设置和获取一个测试键
                string testKey = "precompute_test_connection";
                string testValue = DateTime.Now.ToString();

                RedisUtil.Set(testKey, testValue, TimeSpan.FromSeconds(10));
                string retrievedValue = RedisUtil.GetValue(testKey);

                bool isConnected = testValue.Equals(retrievedValue);
                Logger.Info($"Redis连接检查: {(isConnected ? "正常" : "异常")}");

                // 清理测试键
                RedisUtil.Remove(testKey);

                return isConnected;
            }
            catch (Exception ex)
            {
                Logger.Error($"Redis连接检查失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取热门新闻
        /// </summary>
        /// <param name="topN">返回数量</param>
        /// <returns>热门新闻列表</returns>
        private List<News> GetHotNews(int topN)
        {
            try
            {
                // 获取最近7天的热门新闻
                var endDate = DateTime.Now;
                var startDate = endDate.AddDays(-7);

                var where = $"VectorStatus = 1 AND PubTime >= '{startDate:yyyy-MM-dd}' AND PubTime <= '{endDate:yyyy-MM-dd}'";
                var orderBy = "ViewCount DESC, PubTime DESC"; // 按浏览量和发布时间排序
                var fields = "Id, Title, Source, Classify, PubTime";

                return _newsBLL.GetList(where, topN, 1, fields, orderBy);
            }
            catch (Exception ex)
            {
                Logger.Error($"获取热门新闻失败: {ex.Message}", ex);
                return new List<News>();
            }
        }
    }
    
    /// <summary>
    /// 预计算结果
    /// </summary>
    public class PrecomputeResult
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }
        
        /// <summary>
        /// 处理时长
        /// </summary>
        public TimeSpan Duration { get; set; }
        
        /// <summary>
        /// 总新闻数量（或用户数量）
        /// </summary>
        public int TotalNewsCount { get; set; }
        
        /// <summary>
        /// 成功数量
        /// </summary>
        public int SuccessCount { get; set; }
        
        /// <summary>
        /// 失败数量
        /// </summary>
        public int FailedCount { get; set; }
    }
    
    /// <summary>
    /// 综合预计算结果
    /// </summary>
    public class CombinedPrecomputeResult
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }
        
        /// <summary>
        /// 总处理时长
        /// </summary>
        public TimeSpan Duration { get; set; }
        
        /// <summary>
        /// 新闻预计算结果
        /// </summary>
        public PrecomputeResult NewsPrecomputeResult { get; set; }
        
        /// <summary>
        /// 用户推荐预计算结果
        /// </summary>
        public PrecomputeResult UserPrecomputeResult { get; set; }
    }
    
    /// <summary>
    /// 预计算统计信息
    /// </summary>
    public class PrecomputeStatistics
    {
        /// <summary>
        /// 有兴趣向量的用户总数
        /// </summary>
        public int TotalUsersWithVectors { get; set; }

        /// <summary>
        /// 有预计算推荐的用户数量
        /// </summary>
        public int UsersWithPrecomputedRecommendations { get; set; }

        /// <summary>
        /// 候选新闻数量
        /// </summary>
        public int CandidateNewsCount { get; set; }

        /// <summary>
        /// 热门新闻数量
        /// </summary>
        public int HotNewsCount { get; set; }

        /// <summary>
        /// 有预计算相似新闻的热门新闻数量
        /// </summary>
        public int HotNewsWithPrecomputedSimilarity { get; set; }

        /// <summary>
        /// Redis连接状态
        /// </summary>
        public bool RedisConnectionStatus { get; set; }

        /// <summary>
        /// 统计生成时间
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 用户推荐预计算覆盖率
        /// </summary>
        public double UserRecommendationCoverageRate => TotalUsersWithVectors > 0 ?
            (double)UsersWithPrecomputedRecommendations / TotalUsersWithVectors * 100 : 0;

        /// <summary>
        /// 热门新闻预计算覆盖率
        /// </summary>
        public double HotNewsCoverageRate => HotNewsCount > 0 ?
            (double)HotNewsWithPrecomputedSimilarity / HotNewsCount * 100 : 0;
    }
}