﻿@using Banyan.Domain
@{
    Layout = "/Views/Shared/_LayoutAdmin.cshtml";
    var member = ViewData["manager"] as Banyan.Domain.Member;
    List<Banyan.Domain.Member> recList = (List<Banyan.Domain.Member>)ViewData["recList"];
    List<Banyan.Domain.Member> memberList = (List<Banyan.Domain.Member>)ViewData["userList"];
}
<style>
    #page-app {
        padding: 20px;
    }

    label {
        font-size: 14px;
    }

    .head-title {
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        margin: 15px;
    }
    .ck-powered-by-balloon {
        display: none !important;
    }

    a {
        color: #208aee;
    }
</style>
<div class="col-md-12">
    <div class="block">

        <div class="block-Summary block-Summary-full" style="display:none;" id="page-app">
            <form class="form-horizontal" method="post" id="project-form" name="project-form">
                <input type="hidden" name="id" id="id" v-model="model.Id" />
                <input type="hidden" name="status" id="status" v-model="model.Status" />

                <h3 style="text-align: center; margin: 30px; font-weight: bold;">高榕创投年度总结</h3>
                <hr />
                <h4 class="head-title">一、个人层面</h4>
                <div class="form-group">
                    <label class="col-md-12" for="investProject">&bull;&nbsp;请列出加入高榕以来个人所主导投资的项目及增值情况</label>
                    <div class="col-md-12">
                        <div id="posit">
                            <textarea class="form-control" id="investProject" name="content" rows="3" data-target="#posit"></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-12" for="investContribution">&bull;&nbsp;请列出本年度参与投资的项目（不包括主导投资）及贡献</label>
                    <div class="col-md-12">
                        <div id="posit">
                            <textarea class="form-control" id="investContribution" name="content" rows="3" data-target="#posit"></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-12" for="investSummary">&bull;&nbsp;请统计本年度以下工作的完成情况</label>
                    <div class="col-md-12">
                        <div id="posit">
                            <textarea class="form-control" id="investSummary" name="content" rows="3" data-target="#posit"></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-12" for="postInvest">&bull;&nbsp;请针对你所主导/参与投后管理的项目，填写以下投后管理与服务事项表格（如参与投后管理项目较多，可挑选最有亮点的投后管理案例）</label>
                    <div class="col-md-12">
                        <div id="posit">
                            <textarea class="form-control" id="postInvest" name="content" rows="3" data-target="#posit"></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-12" for="investQuit">&bull;&nbsp;请简述今年你在项目退出方面所做的努力及成效</label>
                    <div class="col-md-12">
                        <div id="posit">
                            <textarea class="form-control" id="investQuit" name="content" rows="5" v-model="model.investQuit"></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-12" for="investFocus">&bull;&nbsp;请列出本年度重点关注的方向或领域，并从项目覆盖、行业人脉、研究深度、Pitch deal的能力等多个角度评价自己的表现</label>
                    <div class="col-md-12">
                        <div id="posit">
                            <textarea class="form-control" id="investFocus" name="content" rows="5" v-model="model.investFocus"></textarea>
                        </div>
                    </div>
                </div>


                <div class="form-group">
                    <label class="col-md-12" for="investStatus">&bull;&nbsp;请对自己本年度的工作状态做简单总结（2022年之前入职的同事可以回顾上一年度的个人总结，简单评价自己对2022年的目标是否实现）</label>
                    <div class="col-md-12">
                        <div id="posit">
                            <textarea class="form-control" id="investStatus" name="content" rows="5" v-model="model.investStatus"></textarea>
                        </div>
                    </div>
                </div>


                <h4 class="head-title">二、公司层面</h4>
                <div class="form-group">
                    <label class="col-md-12" for="companyStream">&bull;&nbsp;对目前的小组工作流程、小组成员合作方式、团队建设、跨部门配合、表决流程等是否有建议</label>
                    <div class="col-md-12">
                        <div id="posit">
                            <textarea class="form-control" id="companyStream" name="content" rows="5" v-model="model.companyStream"></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-12" for="projectMiss">&bull;&nbsp;请列举你认为小组本年度错过的好项目</label>
                    <div class="col-md-12">
                        <div id="posit">
                            <textarea class="form-control" id="projectMiss" name="content" rows="5" v-model="model.projectMiss"></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-12" for="roleModel">&bull;&nbsp;列举行业中你最认可的三家投资机构及三位投资人，并简述他们值得学习的地方。</label>
                    <div class="col-md-12">
                        <div id="posit">
                            <textarea class="form-control" id="roleModel" name="content" rows="5" v-model="model.roleModel"></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-12" for="companyLearn">&bull;&nbsp;其他基金有哪些具体做法值得我们学习</label>
                    <div class="col-md-12">
                        <div id="posit">
                            <textarea class="form-control" id="companyLearn" name="content" rows="5" v-model="model.companyLearn"></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-12" for="goodStaff">&bull;&nbsp;你认为过去一年合伙人之外哪三位同事（也可以低于三位）表现优秀，为什么</label>
                    <div class="col-md-12">
                        <div id="posit">
                            <textarea class="form-control" id="goodStaff" name="content" rows="5" v-model="model.goodStaff"></textarea>
                        </div>
                    </div>
                </div>



                <h4 class="head-title">三、明年工作展望</h4>

                <div class="form-group">
                    <label class="col-md-12" for="nextYearWork">&bull;&nbsp;请列出个人在下一年度的主要工作目标或方法</label>
                    <div class="col-md-12">
                        <div id="posit">
                            <textarea class="form-control" id="nextYearWork" name="content" rows="5" v-model="model.nextYearWork"></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-12" for="selfImprove">&bull;&nbsp;请列出自己需要提升的业务能力，未来的提升计划，是否有公司可以提供的帮助</label>
                    <div class="col-md-12">
                        <div id="posit">
                            <textarea class="form-control" id="selfImprove" name="content" rows="5" v-model="model.selfImprove"></textarea>
                        </div>
                    </div>
                </div>


                <div class="form-group">
                    <label class="col-md-12" for="environmentFight">&bull;&nbsp;你认为高榕应该做哪些具体的工作来适应多变的大环境，保持基金战斗力？</label>
                    <div class="col-md-12">
                        <div id="posit">
                            <textarea class="form-control" id="environmentFight" name="content" rows="5" v-model="model.environmentFight"></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-12" for="investSuggest">&bull;&nbsp;你对目前的投资策略有何建议</label>
                    <div class="col-md-12">
                        <div id="posit">
                            <textarea class="form-control" id="investSuggest" name="content" rows="5" v-model="model.investSuggest"></textarea>
                        </div>
                    </div>
                </div>


                <div class="form-group">
                    <div class="col-md-12 col-md-offset-5">
                        <button class="btn btn-info" @@click="saveData(2)" type="button"><i class="fa fa-save push-5-r"></i>保 存</button>
                        <button class="btn btn-info" @@click="saveData(1)" type="button"><i class="fa fa-save push-5-r"></i>发 布</button>
                        <button class="btn btn-minw btn-warning" @@click="saveData(-1)" type="button">取消并返回</button>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>

@section scripts{
    <link href="~/Content/js/plugins/kindeditor/themes/default/default.css" rel="stylesheet" />
    <style>
        .ke-icon-replacebtn {
            width: 16px;
            height: 16px;
        }
    </style>
    <script src="~/Content/js/plugins/ckeditor.js"></script>
    @*<script src="~/Content/js/plugins/kindeditor/kindeditor-all.js"></script>*@
    @*<script src="~/Content/js/plugins/kindeditor/lang/zh-CN.js"></script>*@
    <script src="/Content/js/plugins/jquery.ui.widget.js"></script>
    <script src="/Content/js/plugins/jquery.iframe-transport.js"></script>
    <link href="/Content/js/plugins/select2/select2.min.css" rel="stylesheet" />
    <link href="/Content/js/plugins/select2/select2-bootstrap.min.css" rel="stylesheet" />
    <script type="text/javascript" src="/Content/js/plugins/select2/select2.min.js"></script>
    <script src="~/Content/js/plugins/city-select/js/lazyload-min.js"></script>

    <script type="text/javascript">
        var investProject, investContribution, investSummary, postInvest;
        function initEditor(callback, tag, height = '325px') {
            ClassicEditor
                .create(document.querySelector(tag), {
                    // toolbar: [ 'heading', '|', 'bold', 'italic', 'link' ]
                    toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', 'blockQuote', 'insertTable',  'undo', 'redo', ],
                })
                .then(editor => {
                    callback(editor)
                    //console.log(Array.from(editor.ui.componentFactory.names()));
                })
                .catch(err => {
                    console.error(err.stack);
                });
            //ClassicEditor.builtinPlugins.map(plugin => console.log(plugin.pluginName));
        }
        $(document).ready(function () {
            initEditor(function (editor) {
                investProject = editor
            }, "#investProject")
            initEditor(function (editor) {
                investContribution = editor
            }, "#investContribution")
            initEditor(function (editor) {
                investSummary = editor
            }, "#investSummary")
            initEditor(function (editor) {
                postInvest = editor
            }, "#postInvest")

        })

        editor1 = null


        //KindEditor.ready(function (K) {
        //    editor1 = initEditor(K, "#investProject", "350px");
        //    editor2 = initEditor(K, "#investContribution", "250px");
        //    editor3 = initEditor(K, "#investSummary", "150px");
        //    editor4 = initEditor(K, "#postInvest", "420px");
        //});

        //function initEditor(K, html, height = '325px') {
        //    return K.create(html, {
        //        uploadJson: '/content/js/plugins/kindeditor/asp.net/upload_json.ashx',
        //        items: [
        //            'fontname', 'fontsize', 'table', '|', 'forecolor', 'hilitecolor', 'bold', 'italic', 'underline',
        //            'removeformat', '|', 'justifyleft', 'justifycenter', 'justifyright', 'insertorderedlist',
        //            'insertunorderedlist', '|', 'hr', 'emoticons', 'link']
        //        ,
        //        imageSizeLimit: '5MB',
        //        imageUploadLimit: 30,
        //        fileManagerJson: '/content/js/plugins/kindeditor/asp.net/file_manager_json.ashx',
        //        allowFileManager: true,
        //        urlType: 'domain',
        //        afterCreate: function () {
        //            var self = this;
        //            K.ctrl(document, 13, function () {
        //                self.sync();
        //            });
        //            K.ctrl(self.edit.doc, 13, function () {
        //                self.sync();
        //            });
        //        },
        //        afterUpload: function (data) {
        //            this.sync();
        //        },
        //        afterBlur: function () {
        //            this.sync();
        //        },
        //        height,
        //    });
        //}




        $(document).ready(function () {
            $('.select2').select2({
                language: "zh-CN",
                width: "100%",
                height: "32px",
                theme: "classic"
            });
        });

        function trimVal(val) {
            return $.trim(val);
        }

        function init(res) {
            investProject.setData(res.data.investProject);
            investContribution.setData(res.data.investContribution);
            investSummary.setData(res.data.investSummary);
            postInvest.setData(res.data.postInvest);
            //editor1.html(res.data.investProject);
            //editor1.sync();
            //editor2.html(res.data.investContribution);
            //editor2.sync();
            //editor3.html(res.data.investSummary);
            //editor3.sync();
            //editor4.html(res.data.postInvest);
            //editor4.sync();
        }

        var timer;
        var app = new Vue({
            el: '#page-app',
            data: {
                id: @(ViewData["id"]),
                model: {  type: "invest" },
                progress: 0,
                loadState: -1,
                keywords: '',
                summary: '',
                GroupMembers: []
            },
            methods: {

                initData: function () {
                    var that = this;
                    $.post('/adminapi/annualReportDetail', { id: that.id }, function (res) {
                        if (res.code == 0) {
                            if (investProject) {
                                init(res);
                            } else {
                                setTimeout(function () {
                                    init(res);
                                }, 1500)
                            }

                            that.model = res.data;
                        } else {
                            that.loadState = -999;
                        }
                    }).error(function (xhr, errorText, errorType) {
                        that.loadState = -999;
                    });
                },

                paramCheck: function () {
                    var errVal, that = this;
                    //if (trimVal(that.model.Name) == '') {
                    //    errVal = '请输入名称';
                    //}
                    if (errVal) {
                        layer.msg(errVal);
                        return false;
                    }
                    return true;
                },

                deleteData: function (status) {
                    var that = this;
                    layer.confirm('确认删除吗？', function (ind) {
                        layer.close(ind);
                        that.saveData(status);
                    });
                },
                saveData: function (status) {
                    var that = this;
                    if (that.loadState > -1)
                        return;
                    if (status < 0) {
                        window.history.go(-1);
                        return;
                    }

                    that.model.investProject = investProject.getData();
                    that.model.investContribution = investContribution.getData();
                    that.model.investSummary = investSummary.getData();
                    that.model.postInvest = postInvest.getData();
                    //that.model.investFocus = $("investFocus").val();
                    //that.model.investMiss = $("#investMiss").val();
                    //that.model.imporove = $("#imporove").val();
                    //that.model.summary = $("#summary").val();
                    that.model.status = status;

                    if (that.paramCheck()) {
                        var that = this;
                        that.loadState = 0;

                        $.post('/adminapi/annualReportSave', { model: that.model }, function (res) {
                            if (!res || res.code != 0) {
                                that.loadState = -1;
                                layer.msg(res.msg || "服务器繁忙，请稍后重试...");
                                return;
                            }
                            window.onbeforeunload = null
                            layer.msg("操作成功");
                            //setTimeout(function () {
                            //    parent.window.location.href = '/User/annualreport';
                            //}, 1200);

                        }).error(function (xhr, errorText, errorType) {
                            that.loadState = -1;
                            layer.msg("服务器繁忙，请稍后重试...");
                        });
                    }
                }

            },
            created: function () {
                var that = this
                this.initData();


                $("#page-app").show();
                //this.initGallery();
            }
        });
        window.onbeforeunload = function (e) {
            e.returnValue = '有未保存的修改'
            return "有未保存的修改"
        }
    </script>
}
