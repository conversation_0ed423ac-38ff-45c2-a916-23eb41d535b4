﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;

namespace Banyan.Apps
{
    public class Project_ActiveBLL : BaseDAL<Project_Active>
    {
        private readonly AjaxResult ajaxResult = null;

        public Project_ActiveBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        public override object Add(Project_Active model)
        {
            ClearCache(model);
            return base.Add(model);
        }

        public override bool Update(Project_Active model)
        {
            ClearCache(model);
            return base.Update(model);
        }

        public override bool Update(Project_Active model, string fldList)
        {
            ClearCache(model);
            return base.Update(model, fldList);
        }

        public bool ClearCache(Project_Active model)
        {
            try
            {
                RedisUtil.Remove(string.Format(RedisKey.Project_Active_model, model.ProjectID));
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取项目缓存
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Project_Active GetModelByCache(string id)
        {
            string cacheKey = string.Format(RedisKey.Project_Active_model, id);
            Project_Active model = RedisUtil.Get<Project_Active>(cacheKey);
            if (model == null)
            {
                model = base.GetModel($"ProjectId='{id}'");
                RedisUtil.Set<Project_Active>(cacheKey, model, TimeSpan.FromMinutes(10));
            }
            return model;
        }

        /// <summary>
        /// 分页查询记录
        /// </summary>
        /// <param name="paramValues">页面请求参数</param>
        /// <returns></returns>
        public AjaxResult GetPageList(NameValueCollection paramValues)
        {
            Member user = new MemberBLL().GetLogOnUser();

            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            string lastweek = DateTime.Now.AddDays(-(int)DateTime.Now.DayOfWeek).ToShortDateString();
            string last14Days = DateTime.Now.AddDays(-14).ToShortDateString();
            string strWhere = $"approved='0' AND (([Project_Active].closed is null and  [Project_Active].modifiedDate>'" + last14Days + @"') or ([Project_Active].closed='1' and [Project_Active].modifiedDate>'" + lastweek + @"'))";

            #region search
            string Name = WebHelper.GetValue("Name", string.Empty, paramValues);
            if (!string.IsNullOrWhiteSpace(Name))
            {
                strWhere += $@"AND (abbName like '%{Name}%' OR fullName like '%{Name}%') ";
            }

            #endregion
            string sort = " investFund DESC,projectType DESC, abbName DESC,modifiedDate DESC ";

            var DealList = GetList(strWhere, pageSize, pageIndex, "*", sort);
            //if (DealList != null && DealList.Count() > 0)
            //{
            //    var roleList = new RoleBLL().GetList($"Status={(int)RoleStatus.enable}", int.MaxValue, 1, "Id,RoleName");
            //    if (roleList != null && roleList.Count() > 0)
            //    {
            //        foreach (var item in DealList)
            //        {
            //            item.RoleName = roleList.Where(x => x.Id == item.ToRoleId).Select(x => x.RoleName).FirstOrDefault()?.ToString();
            //            item.IsOperate = user.Levels == (byte)MemberLevels.Administrator || item.Creator == user.Id;
            //            item.Source = GetSource(item.Source);
            //        }
            //    }
            //}
            //Logger.Info(JsonConvert.SerializeObject(DealList));
            //updateLog("Web, Get Project_Active List", "view", strWhere, user);
            Logger.Info($"Get Project_Active List {strWhere}", user.RealName);
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = DealList;
            ajaxResult.count = GetCount(strWhere);
            return ajaxResult;
        }

        /// <summary>
        /// Excel数据bytes
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public byte[] GetListBytes(NameValueCollection paramValues)
        {
            //Member user = new MemberBLL().GetLogOnUser();

            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            string lastweek = DateTime.Now.AddDays(-(int)DateTime.Now.DayOfWeek).ToShortDateString();
            string last14Days = DateTime.Now.AddDays(-14).ToShortDateString();
            string strWhere = $"approved='0' AND (([Project_Active].closed is null and  [Project_Active].modifiedDate>'" + last14Days + @"') or ([Project_Active].closed='1' and [Project_Active].modifiedDate>'" + lastweek + @"'))";
            #region search
            string Name = WebHelper.GetValue("Name", string.Empty, paramValues);
            if (!string.IsNullOrWhiteSpace(Name))
            {
                strWhere += $@"AND (abbName like '%{Name}%' OR fullName like '%{Name}%') ";
            }

            #endregion
            string sort = " investFund DESC,projectType DESC, abbName DESC,modifiedDate DESC ";

            var DealList = GetList(strWhere, pageSize, pageIndex, "*", sort);
            // Logger.Info("Web, Export Project_Active List: userID," + user.Id);
            // updateLog("Project_Active", "export to excel", strWhere, user);
            string[] columnsName = { "项目组", "项目名", "项目负责人", "编辑人", "日期", "高榕参会人", "公司参会人", "项目来源", "项目介绍人", "项目联系人", "DD负责人", "城市", "公司成立年份", "简介", "团队背景", "业务数据", "财务数据", "股权结构", "历史及当前融资方案", "提到的同行/供应商", "项目亮点", "项目风险", "What's New", "是否拿到数据包及下一步安排", "项目状态" };
            string[] columns = { "RoleName", "Name", "ProjectManager", "EditorName", "PubTimeStr", "InteralPTCP", "Participant", "Source", "Introducer", "finder", "DDManager", "city", "foundedYear", "Summary", "Background", "BusinessData", "FinancialData", "ShareStructure", "InvestHistory", "CompareProduct", "HighLight", "Risk", "UpdatedNews", "NextStep", "nextStepStatus" };
            return ExcelHelper.ExportExcel<Project_Active>(DealList, columnsName.ToList(), "", false, columns);
        }

        public AjaxResult GetProjects(NameValueCollection paramValues)
        {
            #region 用户信息
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var userModel = new MemberBLL().GetModelByCache(uid);
            if (userModel == null || userModel.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = userModel == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = userModel == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }
            #endregion

            #region 交互基本参数
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            //string getType = WebHelper.GetValue("getType", string.Empty, paramValues);
            string keyWords = WebHelper.GetValue("keywords", string.Empty, paramValues);
            string projectType = WebHelper.GetValue("projectType", string.Empty, paramValues);
            string pm = WebHelper.GetValue("pm", string.Empty, paramValues);
            string lastweek = DateTime.Now.AddDays(-(int)DateTime.Now.DayOfWeek).ToShortDateString();
            string last14Days = DateTime.Now.AddDays(-14).ToShortDateString();
            string strWhere = $"approved='0' AND ((([Project_Active].closed is null or [Project_Active].closed = 0) and  [Project_Active].modifiedDate>'" + last14Days + @"') or ([Project_Active].closed=1 and [Project_Active].modifiedDate>'" + lastweek + @"'))";

            #region search
            string search = "";
            if (!string.IsNullOrWhiteSpace(keyWords))
            {
                search = $@" (abbName like '%{keyWords}%' OR fullName like '%{keyWords}%' OR projectManager like '%{keyWords}%') ";
            }

            if (!string.IsNullOrWhiteSpace(projectType))
            {
                search += string.IsNullOrEmpty(search) ? $@"  projectType='{projectType}'" : $@" AND projectType='{projectType}'";
            }
            if (!string.IsNullOrWhiteSpace(pm))
            {
                search += string.IsNullOrEmpty(search) ? $@"  projectManager='{pm}' " : $@" AND projectManager='{pm}' ";
            }

            string startDate = WebHelper.GetValue("startdate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(startDate))
            {
                search += string.IsNullOrEmpty(search) ? $" createdDate>='{startDate}' " : $" AND createdDate>='{startDate}' ";
            }

            string endDate = WebHelper.GetValue("enddate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(endDate))
            {
                search += string.IsNullOrEmpty(search) ? $" createdDate<='{endDate}' " : $" AND createdDate<='{endDate}' ";
            }
            string fundName = WebHelper.GetValue("fundName", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(fundName))
                search += string.IsNullOrEmpty(search) ? string.Format(@" (investFund='{0}' or (investFund like '%,{0}' or investFund like '{0},%' or investFund like '%,{0},%')) ", fundName) : string.Format(@" and (investFund='{0}' or (investFund like '%,{0}' or investFund like '{0},%' or investFund like '%,{0},%')) ", fundName);
            strWhere = string.IsNullOrEmpty(search) ? strWhere : search;
            #endregion
            string sort = " investFund DESC,projectType DESC, abbName DESC,modifiedDate DESC, postMoney DESC ";
            if (!string.IsNullOrWhiteSpace(keyWords))
            {
                sort = " modifiedDate DESC, investFund DESC,projectType DESC, abbName DESC, postMoney DESC ";
            }

            var DealList = new List<Project_Active>();

            #endregion

            #region 约束条件
            if (!(userModel.Levels == (int)MemberLevels.Administrator || userModel.Levels == (int)MemberLevels.SuperUser || userModel.Levels == (int)MemberLevels.DealRMB || userModel.Levels == (int)MemberLevels.DealUSD || userModel.Levels == (int)MemberLevels.DealALL || userModel.RealName == "钟秋月" ))
            {
                strWhere += $@" AND projectManager like '%{userModel.RealName}%' ";
            }

            if(userModel.Levels == (int)MemberLevels.DealRMB)
            {
                strWhere += $@" AND investFund like '%RMB%' ";
            }

            if(userModel.Levels == (int)MemberLevels.DealUSD)
            {
                strWhere += $@" AND investFund like '%USD%' ";
            }
            //if (getType.Trim().Equals("typelist")) //搜索
            //{
            //    if (!string.IsNullOrEmpty(keyWords))
            //    {
            //        DealList = GetList(strWhere, 1000, 1, "ProjectID,abbName");
            //        if (DealList != null && DealList.Count() > 0)
            //        {
            //            ajaxResult.data = DealList.Select(item =>
            //            {
            //                return new { item.ProjectID, item.abbName };
            //            });
            //        }
            //        ajaxResult.code = (int)ResultCode.success;
            //        return ajaxResult;
            //    }
            //    else
            //    {
            //        ajaxResult.code = (int)ResultCode.success;
            //        ajaxResult.data = null;
            //        return ajaxResult;
            //    }
            //}
            #endregion
            //默认按排序值、发布时间、点赞数、评论数、浏览数排序
            if (userModel.RealName == "钟秋月")
            {
                DealList = GetList(strWhere, int.MaxValue, pageIndex, "*", sort);
                var companyDic = new PortfolioBLL().getManagedCompanies(userModel.RealName, true)
                    .ToDictionary(val => val.Name, val => true);
                DealList = DealList.Where(val => companyDic.ContainsKey(val.abbName)).ToList();
                ajaxResult.count = DealList.Count;
            } else
            {
                DealList = GetList(strWhere, pageSize, pageIndex, "*", sort);
                ajaxResult.count = GetCount(strWhere);
            }

            //项目内容格式处理
            ajaxResult.data = DealList.Select(item =>
            {
                string stasusWhereSql = "(discussType='Closed' or discussType='签署完成') AND projectID='{item.ProjectID}'";
                var dealStatus = new Project_ActiveStatusBLL().GetList(stasusWhereSql)?.FirstOrDefault();

                return new
                {
                    item.ProjectID,
                    item.abbName,
                    item.projectType,
                    item.fullName,
                    item.investFund,
                    item.shares,
                    item.ownership,
                    item.firstInvest,
                    investedAmount = item.investedAmount == 0 ? "" : string.Format("{0:N0}", item.investedAmount),
                    preMoney = item.preMoney == 0 ? "" : string.Format("{0:N0}", item.preMoney),
                    postMoney = item.postMoney == 0 ? "" : string.Format("{0:N0}", item.postMoney),
                    totalInvestAmount = item.totalInvestAmount == 0 ? "" : string.Format("{0:N0}", item.totalInvestAmount),
                    item.otherInvestor,
                    remark = dealStatus != null && !string.IsNullOrEmpty(dealStatus.dicussDate.ToString("yyyy-MM-dd")) ?
                    dealStatus.dicussDate.ToString("yyyy-MM-dd") + " | " + item.remark : item.modifiedDate.ToString("yyyy-MM-dd") + " | " + item.remark,
                    item.BoardSeatType,
                    projectManager = string.IsNullOrEmpty(item.projectManager2) ? item.projectManager : item.projectManager + "，" + item.projectManager2,
                    item.oneLineDesc,
                    item.currency,
                    item.closed,
                    item.InhouseCounsel,
                    modifiedDate = item.modifiedDate.ToString("yyyy-MM-dd"),
                    createdDate = item.createdDate.ToString("yyyy-MM-dd"),
                };
            });

            updateLog("MiniApp, Get Project_Active List", "view", strWhere, userModel);

            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;
        }

        public List<Project_Active> GetDealsByPortfolioID(string id)
        {
            var res = GetList($"portfolio='{id}'", int.MaxValue, 0, "*", "createdDate desc");
            res = res.Select(val =>
            {
                val.createdDateStr = val.createdDate.ToString("yyyy-MM-dd");
                val.modifiedDateStr = val.modifiedDate.ToString("yyyy-MM-dd");
                return val;
            }).ToList();
            return res;
        }
        public AjaxResult GetAllProjects(NameValueCollection paramValues)
        {
            #region 用户信息
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var userModel = new MemberBLL().GetModelByCache(uid);
            if (userModel == null || userModel.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = userModel == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = userModel == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }
            #endregion

            #region 交互基本参数
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", int.MaxValue, paramValues);
            //string getType = WebHelper.GetValue("getType", string.Empty, paramValues);
            string keyWords = WebHelper.GetValue("keywords", string.Empty, paramValues);
            string projectType = WebHelper.GetValue("projectType", string.Empty, paramValues);
            string pm = WebHelper.GetValue("pm", string.Empty, paramValues);
            string lastweek = DateTime.Now.AddDays(-(int)DateTime.Now.DayOfWeek).ToShortDateString();
            string last14Days = DateTime.Now.AddDays(-14).ToShortDateString();
            string strWhere = $"approved='0' ";

            #region search
            string search = "";
            if (!string.IsNullOrWhiteSpace(keyWords))
            {
                search = $@" (abbName like '%{keyWords}%' OR fullName like '%{keyWords}%' OR projectManager like '%{keyWords}%') ";
            }

            if (!string.IsNullOrWhiteSpace(projectType))
            {
                search += string.IsNullOrEmpty(search) ? $@"  projectType='{projectType}'" : $@" AND projectType='{projectType}'";
            }
            if (!string.IsNullOrWhiteSpace(pm))
            {
                search += string.IsNullOrEmpty(search) ? $@"  projectManager='{pm}' " : $@" AND projectManager='{pm}' ";
            }

            string fundName = WebHelper.GetValue("fundName", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(fundName))
                search += string.IsNullOrEmpty(search) ? string.Format(@" (investFund='{0}' or (investFund like '%,{0}' or investFund like '{0},%' or investFund like '%,{0},%')) ", fundName) : string.Format(@" and (investFund='{0}' or (investFund like '%,{0}' or investFund like '{0},%' or investFund like '%,{0},%')) ", fundName);
            strWhere = string.IsNullOrEmpty(search) ? strWhere : search;
            #endregion
            string sort = " investFund DESC,projectType DESC, abbName DESC,modifiedDate DESC, postMoney DESC ";
            if (!string.IsNullOrWhiteSpace(keyWords))
            {
                sort = " modifiedDate DESC, investFund DESC,projectType DESC, abbName DESC, postMoney DESC ";
            }

            var DealList = new List<Project_Active>();

            #endregion

            #region 约束条件
            if (!(userModel.Levels == (int)MemberLevels.Administrator || userModel.Levels == (int)MemberLevels.SuperUser || userModel.Levels == (int)MemberLevels.DealRMB || userModel.Levels == (int)MemberLevels.DealUSD || userModel.Levels == (int)MemberLevels.DealALL))
            {
                strWhere += $@" AND projectManager like '%{userModel.RealName}%' ";
            }

            if (userModel.Levels == (int)MemberLevels.DealRMB)
            {
                strWhere += $@" AND investFund like '%RMB%' ";
            }

            if (userModel.Levels == (int)MemberLevels.DealUSD)
            {
                strWhere += $@" AND investFund like '%USD%' ";
            }


            #endregion
            //默认按排序值、发布时间、点赞数、评论数、浏览数排序
            //DealList = GetList(strWhere, pageSize, pageIndex, "*", sort);
            DealList = GetListBySql(@"select pa.* from Project_Active pa 
inner join
(select abbName, max(modifiedDate) as modifiedDate , max(postMoney) as postMoney  from Project_Active pa2 group by abbName) as pb
on pa.abbName = pb.abbName and pa.modifiedDate  = pb.modifiedDate and pa.postMoney = pb.postMoney
where " + strWhere + " order by " + sort);
            //项目内容格式处理
            ajaxResult.data = DealList.Select(item =>
            {
                string statusWhereSql = "(discussType='Closed' or discussType='签署完成') AND projectID='{item.ProjectID}'";
                var dealStatus = new Project_ActiveStatusBLL().GetList(statusWhereSql)?.FirstOrDefault();

                return new
                {
                    item.ProjectID,
                    item.abbName,
                    item.projectType,
                    item.fullName,
                    item.investFund,
                    item.shares,
                    item.ownership,
                    item.firstInvest,
                    investedAmount = item.investedAmount == 0 ? "" : string.Format("{0:N0}", item.investedAmount),
                    preMoney = item.preMoney == 0 ? "" : string.Format("{0:N0}", item.preMoney),
                    postMoney = item.postMoney == 0 ? "" : string.Format("{0:N0}", item.postMoney),
                    totalInvestAmount = item.totalInvestAmount == 0 ? "" : string.Format("{0:N0}", item.totalInvestAmount),
                    item.otherInvestor,
                    remark = dealStatus != null && !string.IsNullOrEmpty(dealStatus.dicussDate.ToString("yyyy-MM-dd")) ?
                    dealStatus.dicussDate.ToString("yyyy-MM-dd") + " | " + item.remark : item.modifiedDate.ToString("yyyy-MM-dd") + " | " + item.remark,
                    item.BoardSeatType,
                    projectManager = string.IsNullOrEmpty(item.projectManager2) ? item.projectManager : item.projectManager + "，" + item.projectManager2,
                    item.oneLineDesc,
                    item.currency,
                    item.closed,
                    item.InhouseCounsel,
                    modifiedDate = item.modifiedDate.ToString("yyyy-MM-dd"),
                    createdDate = item.createdDate.ToString("yyyy-MM-dd"),
                };
            });

            updateLog("MiniApp, Get Project_Active List", "view", strWhere, userModel);
            ajaxResult.count = DealList.Count;
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;
        }
        public AjaxResult GetFundNames(NameValueCollection paramValues)
        {
            #region 用户信息
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var userModel = new MemberBLL().GetModelByCache(uid);
            if (userModel == null || userModel.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = userModel == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = userModel == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }
            #endregion

            var DealList = new List<Project_Active>();

            DealList = GetListNoPage("");
            List<string> fundNameList = DealList.Select(p => p.investFund).ToList().Distinct().ToList();
            List<string> distinctFundNames = new List<string>();
            foreach (string item in fundNameList)
            {
                //    if (item.Contains(","))
                //    {
                string[] fundNames = item.Split(',');
                foreach (string name in fundNames)
                {
                    if (!distinctFundNames.Contains(name) && !string.IsNullOrEmpty(name))
                        distinctFundNames.Add(name);
                }
                //}
                //else
                //    if (!distinctFundNames.Contains(item))
                //    distinctFundNames.Add(item);
            }
            //排序
            distinctFundNames.Sort();
            ajaxResult.data = distinctFundNames;

            ajaxResult.count = distinctFundNames.Count;
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;
        }
        
            /// <summary>
            /// 获取项目数据
            /// </summary>
            /// <returns></returns>
            public AjaxResult GetProject(NameValueCollection paramValues)
        {
            string ProjectId = WebHelper.GetValue("aid", "", paramValues);
            int userId = WebHelper.GetValueInt("uid", 0, paramValues);
            //string tag = WebHelper.GetValue("tag", string.Empty, paramValues);

            if (string.IsNullOrEmpty(ProjectId) || userId <= 0)
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                ajaxResult.msg = "参数不合法！";
                return ajaxResult;
            }

            var userModel = new MemberBLL().GetModelByCache(userId);
            if (userModel == null || userModel.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = userModel == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = userModel == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }

            var ProjectModel = new Project_ActiveBLL().GetModelByCache(ProjectId);
            if (ProjectModel == null)
            {
                ajaxResult.code = (int)ResultCode.notdata;
                return ajaxResult;
            }
            //if ( !(userModel.Levels == (int)MemberLevels.Administrator || userModel.Levels == (int)MemberLevels.SuperUser))
            //{
            //    ajaxResult.code = (int)ResultCode.noright;
            //    return ajaxResult;
            //}

            // 添加记录
            updateLog("MiniApp, Get Project_Active", "view", "projectID:" + ProjectId + ",Name:" + ProjectModel.abbName, userModel);

            //返回数据
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = new
            {
                ProjectModel.ProjectID,
                ProjectModel.abbName,
                ProjectModel.fullName,
                ProjectModel.projectType,
                ProjectModel.investFund,
                ProjectModel.firstInvest,
                ProjectModel.ownership,
                ProjectModel.shares,
                ProjectModel.otherInvestor,
                remark = ProjectModel.remark,
                ProjectModel.closed,
                ProjectModel.BoardSeatType,
                projectManager = string.IsNullOrEmpty(ProjectModel.projectManager2) ? ProjectModel.projectManager : ProjectModel.projectManager + "，" + ProjectModel.projectManager2,
                ProjectModel.currency,
                investedAmount = ProjectModel.investedAmount == 0 ? "" : string.Format("{0:N0}", ProjectModel.investedAmount),
                preMoney = ProjectModel.preMoney == 0 ? "" : string.Format("{0:N0}", ProjectModel.preMoney),
                postMoney = ProjectModel.postMoney == 0 ? "" : string.Format("{0:N0}", ProjectModel.postMoney),
                totalInvestAmount = ProjectModel.totalInvestAmount == 0 ? "" : string.Format("{0:N0}", ProjectModel.totalInvestAmount),
                ProjectModel.InhouseCounsel,
                modifiedDate = ProjectModel.modifiedDate.ToString("yyyy-MM-dd"),
            };

            return ajaxResult;
        }

        public AjaxResult GetProjectDetail(string id, bool backAll = false)
        {
            Project_Active ProjectModel = null;
            if (string.IsNullOrEmpty(id))
            {
                ProjectModel = new Project_Active();
            }
            else
                ProjectModel = GetModelByCache(id);

            if (backAll)
            {
                ajaxResult.code = (int)ResultCode.success;
                ProjectModel = ProjectModel ?? new Project_Active();
                ajaxResult.data = ProjectModel;
                return ajaxResult;
            }
            if (ProjectModel == null)
            {
                ajaxResult.code = (int)ResultCode.notdata;
                return ajaxResult;
            }
            //浏览量更新
            //UpdateViews(ProjectModel);
            updateLog("Web, Get Project_Active", "view", "projectID:" + id + ",Name:" + ProjectModel.abbName, new MemberBLL().GetLogOnUser(0));

            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = new
            {
                ProjectModel.ProjectID,
                ProjectModel.abbName,
                ProjectModel.fullName,
                ProjectModel.projectType,
                ProjectModel.investFund,
                ProjectModel.firstInvest,
                ProjectModel.ownership,
                ProjectModel.shares,
                ProjectModel.investedAmount,
                ProjectModel.otherInvestor,
                ProjectModel.totalInvestAmount,
                ProjectModel.preMoney,
                ProjectModel.postMoney,
                ProjectModel.remark,
                ProjectModel.closed,
            };

            return ajaxResult;
        }

        /// <summary>
        /// 操作日志更新
        /// </summary>
        /// <param name="page"></param>
        /// <param name="action"></param>
        /// <param name="description"></param>
        /// <param name="userName"></param>
        public void updateLog(string page, string action, string description, Member user)
        {
            SysLog log = new SysLog
            {
                Page = page,
                Action = action,
                Description = description,
                CreatedBy = user == null ? "" : user.RealName,
                Ip = Utility.WebHelper.GetIP(),
                CreatorId = user == null ? 0 : user.Id,
            };
            SysLogBLL logBLL = new SysLogBLL();
            logBLL.Add(log);
        }
    }
}
