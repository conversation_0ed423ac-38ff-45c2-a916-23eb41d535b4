<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute">
            <summary>
            Se usa para especificar el elemento (archivo o directorio) para la implementación por prueba.
            Puede especificarse en la clase de prueba o en el método de prueba.
            Puede tener varias instancias del atributo para especificar más de un elemento.
            La ruta de acceso del elemento puede ser absoluta o relativa. Si es relativa, lo es respecto a RunConfig.RelativePathRoot.
            </summary>
            <example>
            [DeploymentItem("file1.xml")]
            [DeploymentItem("file2.xml", "DataFiles")]
            [DeploymentItem("bin\Debug")]
            </example>
            <remarks>
            Putting this in here so that UWP discovery works. We still do not want users to be using DeploymentItem in the UWP world - Hence making it internal.
            We should separate out DeploymentItem logic in the adapter via a Framework extensiblity point.
            Filed https://github.com/Microsoft/testfx/issues/100 to track this.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/>.
            </summary>
            <param name="path">Archivo o directorio para implementar. La ruta de acceso es relativa al directorio de salida de compilación. El elemento se copiará en el mismo directorio que los ensamblados de prueba implementados.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String,System.String)">
            <summary>
            Inicializa una nueva instancia de la clase <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/>.
            </summary>
            <param name="path">Ruta de acceso relativa o absoluta al archivo o directorio para implementar. La ruta de acceso es relativa al directorio de salida de compilación. El elemento se copiará en el mismo directorio que los ensamblados de prueba implementados.</param>
            <param name="outputDirectory">Ruta de acceso del directorio en el que se van a copiar los elementos. Puede ser absoluta o relativa respecto al directorio de implementación. Todos los archivos y directorios que identifica <paramref name="path"/> se copiarán en este directorio.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.Path">
            <summary>
            Obtiene la ruta de acceso al archivo o carpeta de origen que se debe copiar.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.OutputDirectory">
            <summary>
            Obtiene la ruta de acceso al directorio donde se copia el elemento.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AppContainer.UITestMethodAttribute">
            <summary>
            Ejecuta el código de la prueba en el subproceso de la interfaz de usuario para aplicaciones de la Tienda Windows.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AppContainer.UITestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Ejecuta el método de prueba en el subproceso de la interfaz de usuario.
            </summary>
            <param name="testMethod">
            El método de prueba.
            </param>
            <returns>
            Una matriz de <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/> Instancias.
            </returns>
            Throws <exception cref="T:System.NotSupportedException"> when run on an async test method.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext">
            <summary>
            Clase TestContext. Esta clase debe ser totalmente abstracta y no contener ningún
            miembro. El adaptador implementará los miembros. Los usuarios del marco solo deben
            tener acceso a esta clase a través de una interfaz bien definida.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.Properties">
            <summary>
            Obtiene las propiedades de una prueba.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.FullyQualifiedTestClassName">
            <summary>
            Obtiene el nombre completo de la clase que contiene el método de prueba que se está ejecutando.
            </summary>
            <remarks>
            This property can be useful in attributes derived from ExpectedExceptionBaseAttribute.
            Those attributes have access to the test context, and provide messages that are included
            in the test results. Users can benefit from messages that include the fully-qualified
            class name in addition to the name of the test method currently being executed.
            </remarks>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestName">
            <summary>
            Obtiene el nombre del método de prueba que se está ejecutando.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.CurrentTestOutcome">
            <summary>
            Obtiene el resultado de la prueba actual.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String)">
            <summary>
            Used to write trace messages while the test is running
            </summary>
            <param name="message">formatted message string</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String,System.Object[])">
            <summary>
            Used to write trace messages while the test is running
            </summary>
            <param name="format">format string</param>
            <param name="args">the arguments</param>
        </member>
    </members>
</doc>
