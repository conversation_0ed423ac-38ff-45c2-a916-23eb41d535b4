/**
 * Engagement Tracker
 * 
 * This script provides client-side functionality for tracking user engagement
 * with recommendations, including clicks, views, and other interactions.
 */

var EngagementTracker = (function () {
    // Private variables
    var _config = {
        trackingEnabled: true,
        trackingEndpoint: '/Recommendation/TrackClick',
        debugMode: false,
        sessionTimeout: 30 * 60 * 1000, // 30 minutes
        batchSize: 10,
        batchInterval: 5000 // 5 seconds
    };
    
    var _state = {
        sessionId: null,
        queue: [],
        processingQueue: false,
        lastActivity: Date.now()
    };
    
    // Private methods
    
    /**
     * Initialize the engagement tracker
     * @param {Object} options - Configuration options
     */
    function _init(options) {
        // Merge options with default config
        if (options) {
            _config = Object.assign(_config, options);
        }
        
        // Generate session ID
        _state.sessionId = _generateSessionId();
        
        // Set up activity tracking
        _setupActivityTracking();
        
        // Set up queue processing
        _setupQueueProcessing();
        
        // Log initialization if in debug mode
        _debug('Engagement tracker initialized with session ID: ' + _state.sessionId);
    }
    
    /**
     * Generate a unique session ID
     * @returns {string} Session ID
     */
    function _generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * Set up tracking of user activity
     */
    function _setupActivityTracking() {
        // Update last activity time on user interactions
        $(document).on('click mousemove keydown scroll', function () {
            _state.lastActivity = Date.now();
        });
        
        // Check for session timeout periodically
        setInterval(function () {
            var now = Date.now();
            var elapsed = now - _state.lastActivity;
            
            // If session timed out, generate a new session ID
            if (elapsed > _config.sessionTimeout) {
                _state.sessionId = _generateSessionId();
                _state.lastActivity = now;
                _debug('Session timed out, new session ID: ' + _state.sessionId);
            }
        }, 60000); // Check every minute
    }
    
    /**
     * Set up processing of tracking event queue
     */
    function _setupQueueProcessing() {
        // Process queue periodically
        setInterval(function () {
            _processQueue();
        }, _config.batchInterval);
    }
    
    /**
     * Process events in the tracking queue
     */
    function _processQueue() {
        // If already processing or queue is empty, do nothing
        if (_state.processingQueue || _state.queue.length === 0) {
            return;
        }
        
        _state.processingQueue = true;
        
        // Take a batch of events from the queue
        var batch = _state.queue.splice(0, _config.batchSize);
        
        // If in debug mode, log the batch
        _debug('Processing batch of ' + batch.length + ' events');
        
        // Process each event in the batch
        var promises = batch.map(function (event) {
            return _sendTrackingEvent(event);
        });
        
        // When all events in the batch are processed
        Promise.all(promises)
            .then(function (results) {
                _debug('Batch processed successfully');
            })
            .catch(function (error) {
                _debug('Error processing batch: ' + error);
                
                // Put failed events back in the queue
                _state.queue = batch.concat(_state.queue);
            })
            .finally(function () {
                _state.processingQueue = false;
            });
    }
    
    /**
     * Send a tracking event to the server
     * @param {Object} event - Event data
     * @returns {Promise} Promise that resolves when the event is sent
     */
    function _sendTrackingEvent(event) {
        return new Promise(function (resolve, reject) {
            // If tracking is disabled, resolve immediately
            if (!_config.trackingEnabled) {
                resolve();
                return;
            }
            
            // Add session ID to event data
            event.sessionId = _state.sessionId;
            
            $.ajax({
                url: _config.trackingEndpoint,
                type: 'POST',
                data: event,
                success: function (response) {
                    _debug('Event sent successfully: ' + JSON.stringify(event));
                    resolve(response);
                },
                error: function (xhr, status, error) {
                    _debug('Error sending event: ' + error);
                    reject(error);
                }
            });
        });
    }
    
    /**
     * Log debug message if debug mode is enabled
     * @param {string} message - Debug message
     */
    function _debug(message) {
        if (_config.debugMode) {
            console.log('[EngagementTracker] ' + message);
        }
    }
    
    /**
     * Track a click on a recommendation
     * @param {number} newsId - ID of the clicked news
     * @param {string} source - Source of the click (web, email, widget, etc.)
     */
    function _trackClick(newsId, source) {
        if (!_config.trackingEnabled) return;
        
        var event = {
            type: 'click',
            newsId: newsId,
            source: source || 'web',
            timestamp: Date.now()
        };
        
        // Add to queue
        _state.queue.push(event);
        _debug('Click event queued: ' + JSON.stringify(event));
        
        // If queue is getting large, process immediately
        if (_state.queue.length >= _config.batchSize * 2) {
            _processQueue();
        }
    }
    
    /**
     * Track a view of a recommendation
     * @param {number} newsId - ID of the viewed news
     * @param {string} source - Source of the view (web, email, widget, etc.)
     */
    function _trackView(newsId, source) {
        if (!_config.trackingEnabled) return;
        
        var event = {
            type: 'view',
            newsId: newsId,
            source: source || 'web',
            timestamp: Date.now()
        };
        
        // Add to queue
        _state.queue.push(event);
        _debug('View event queued: ' + JSON.stringify(event));
    }
    
    /**
     * Track an impression of a recommendation (when it's shown to the user)
     * @param {number} newsId - ID of the news
     * @param {string} source - Source of the impression (web, email, widget, etc.)
     */
    function _trackImpression(newsId, source) {
        if (!_config.trackingEnabled) return;
        
        var event = {
            type: 'impression',
            newsId: newsId,
            source: source || 'web',
            timestamp: Date.now()
        };
        
        // Add to queue
        _state.queue.push(event);
        _debug('Impression event queued: ' + JSON.stringify(event));
    }
    
    /**
     * Track a custom engagement event
     * @param {string} eventType - Type of event
     * @param {number} newsId - ID of the news
     * @param {string} source - Source of the event
     * @param {Object} additionalData - Additional event data
     */
    function _trackCustomEvent(eventType, newsId, source, additionalData) {
        if (!_config.trackingEnabled) return;
        
        var event = {
            type: eventType,
            newsId: newsId,
            source: source || 'web',
            timestamp: Date.now()
        };
        
        // Add additional data if provided
        if (additionalData) {
            event.data = additionalData;
        }
        
        // Add to queue
        _state.queue.push(event);
        _debug('Custom event queued: ' + JSON.stringify(event));
    }
    
    // Public API
    return {
        init: _init,
        trackClick: _trackClick,
        trackView: _trackView,
        trackImpression: _trackImpression,
        trackCustomEvent: _trackCustomEvent,
        
        // Configuration methods
        enable: function () {
            _config.trackingEnabled = true;
            _debug('Tracking enabled');
        },
        disable: function () {
            _config.trackingEnabled = false;
            _debug('Tracking disabled');
        },
        setDebugMode: function (enabled) {
            _config.debugMode = enabled;
            _debug('Debug mode ' + (enabled ? 'enabled' : 'disabled'));
        }
    };
})();