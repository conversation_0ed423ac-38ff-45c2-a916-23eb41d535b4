<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod">
            <summary>
            TestMethod pour exécution.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestMethodName">
            <summary>
            Obtient le nom de la méthode de test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestClassName">
            <summary>
            Obtient le nom de la classe de test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ReturnType">
            <summary>
            Obtient le type de retour de la méthode de test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ParameterTypes">
            <summary>
            Obtient les paramètres de la méthode de test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.MethodInfo">
            <summary>
            Obtient le methodInfo de la méthode de test.
            </summary>
            <remarks>
            This is just to retrieve additional information about the method.
            Do not directly invoke the method using MethodInfo. Use ITestMethod.Invoke instead.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.Invoke(System.Object[])">
            <summary>
            Appelle la méthode de test.
            </summary>
            <param name="arguments">
            Arguments à passer à la méthode de test. (Exemple : pour un test piloté par les données)
            </param>
            <returns>
            Résultat de l'appel de la méthode de test.
            </returns>
            <remarks>
            This call handles asynchronous test methods as well.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAllAttributes(System.Boolean)">
            <summary>
            Obtient tous les attributs de la méthode de test.
            </summary>
            <param name="inherit">
            Indique si l'attribut défini dans la classe parente est valide.
            </param>
            <returns>
            Tous les attributs.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAttributes``1(System.Boolean)">
            <summary>
            Obtient l'attribut du type spécifique.
            </summary>
            <typeparam name="AttributeType"> System.Attribute type. </typeparam>
            <param name="inherit">
            Indique si l'attribut défini dans la classe parente est valide.
            </param>
            <returns>
            Attributs du type spécifié.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Helper">
            <summary>
            Assistance.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNull(System.Object,System.String,System.String)">
            <summary>
            Paramètre de vérification non null.
            </summary>
            <param name="param">
            Paramètre.
            </param>
            <param name="parameterName">
            Nom du paramètre.
            </param>
            <param name="message">
            Message.
            </param>
            <exception cref="T:System.ArgumentNullException"> Throws argument null exception when parameter is null. </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNullOrEmpty(System.String,System.String,System.String)">
            <summary>
            Paramètre de vérification non null ou vide.
            </summary>
            <param name="param">
            Paramètre.
            </param>
            <param name="parameterName">
            Nom du paramètre.
            </param>
            <param name="message">
            Message.
            </param>
            <exception cref="T:System.ArgumentException"> Throws ArgumentException when parameter is null. </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod">
            <summary>
            Énumération liée à la façon dont nous accédons aux lignes de données dans les tests pilotés par les données.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Sequential">
            <summary>
            Les lignes sont retournées dans un ordre séquentiel.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random">
            <summary>
            Les lignes sont retournées dans un ordre aléatoire.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute">
            <summary>
            Attribut permettant de définir les données inline d'une méthode de test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/>.
            </summary>
            <param name="data1"> Objet de données. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object,System.Object[])">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> qui accepte un tableau d'arguments.
            </summary>
            <param name="data1"> Objet de données. </param>
            <param name="moreData"> Plus de données. </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.Data">
            <summary>
            Obtient les données permettant d'appeler la méthode de test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.DisplayName">
            <summary>
            Obtient ou définit le nom d'affichage dans les résultats des tests à des fins de personnalisation.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            <summary>
            Exception d'assertion non concluante.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String,System.Exception)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
            <param name="msg"> Message. </param>
            <param name="ex"> Exception. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
            <param name="msg"> Message. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException">
            <summary>
            Classe InternalTestFailureException. Sert à indiquer l'échec interne d'un cas de test
            </summary>
            <remarks>
            This class is only added to preserve source compatibility with the V1 framework.
            For all practical purposes either use AssertFailedException/AssertInconclusiveException.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String,System.Exception)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
            <param name="msg"> Message d'exception. </param>
            <param name="ex"> Exception. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
            <param name="msg"> Message d'exception. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute">
            <summary>
            Attribut indiquant d'attendre une exception du type spécifié
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> avec le type attendu
            </summary>
            <param name="exceptionType">Type de l'exception attendue</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type,System.String)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> avec
            le type attendu et le message à inclure quand aucune exception n'est levée par le test.
            </summary>
            <param name="exceptionType">Type de l'exception attendue</param>
            <param name="noExceptionMessage">
            Message à inclure dans le résultat de test en cas d'échec du test lié à la non-levée d'une exception
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.ExceptionType">
            <summary>
            Obtient une valeur indiquant le type de l'exception attendue
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.AllowDerivedTypes">
            <summary>
            Obtient ou définit une valeur indiquant si les types dérivés du type de l'exception attendue peuvent
            être éligibles comme prévu
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.NoExceptionMessage">
            <summary>
            Obtient le message à inclure dans le résultat de test en cas d'échec du test lié à la non-levée d'une exception
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.Verify(System.Exception)">
            <summary>
            Vérifie que le type de l'exception levée par le test unitaire est bien attendu
            </summary>
            <param name="exception">Exception levée par le test unitaire</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute">
            <summary>
            Classe de base des attributs qui spécifient d'attendre une exception d'un test unitaire
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> avec un message d'absence d'exception par défaut
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor(System.String)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> avec un message d'absence d'exception
            </summary>
            <param name="noExceptionMessage">
            Message à inclure dans le résultat de test en cas d'échec du test lié à la non-levée d'une
            exception
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.NoExceptionMessage">
            <summary>
            Obtient le message à inclure dans le résultat de test en cas d'échec du test lié à la non-levée d'une exception
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.SpecifiedNoExceptionMessage">
            <summary>
            Obtient le message à inclure dans le résultat de test en cas d'échec du test lié à la non-levée d'une exception
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.GetDefaultNoExceptionMessage(System.String)">
            <summary>
            Obtient le message d'absence d'exception par défaut
            </summary>
            <param name="expectedExceptionAttributeTypeName">Nom du type de l'attribut ExpectedException</param>
            <returns>Message d'absence d'exception par défaut</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.Verify(System.Exception)">
            <summary>
            Détermine si l'exception est attendue. Si la méthode est retournée, cela
            signifie que l'exception est attendue. Si la méthode lève une exception, cela
            signifie que l'exception n'est pas attendue, et que le message de l'exception levée
            est inclus dans le résultat de test. La classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert"/> peut être utilisée par
            commodité. Si <see cref="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive"/> est utilisé et si l'assertion est un échec,
            le résultat de test a la valeur Non concluant.
            </summary>
            <param name="exception">Exception levée par le test unitaire</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.RethrowIfAssertException(System.Exception)">
            <summary>
            Lève à nouveau l'exception, s'il s'agit de AssertFailedException ou de AssertInconclusiveException
            </summary>
            <param name="exception">Exception à lever de nouveau, s'il s'agit d'une exception d'assertion</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper">
            <summary>
            Cette classe permet à l'utilisateur d'effectuer des tests unitaires pour les types basés sur des types génériques.
            GenericParameterHelper répond à certaines contraintes usuelles des types génériques,
            exemple :
            1. constructeur par défaut public
            2. implémentation d'une interface commune : IComparable, IEnumerable
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> qui
            répond à la contrainte 'newable' dans les génériques C#.
            </summary>
            <remarks>
            This constructor initializes the Data property to a random value.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor(System.Int32)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> qui
            initialise la propriété Data en lui assignant une valeur fournie par l'utilisateur.
            </summary>
            <param name="data">Valeur entière</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Data">
            <summary>
            Obtient ou définit les données
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Equals(System.Object)">
            <summary>
            Compare la valeur de deux objets GenericParameterHelper
            </summary>
            <param name="obj">objet à comparer</param>
            <returns>true si obj a la même valeur que l'objet GenericParameterHelper de 'this'.
            sinon false.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetHashCode">
            <summary>
            Retourne un code de hachage pour cet objet.
            </summary>
            <returns>Code de hachage.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.CompareTo(System.Object)">
            <summary>
            Compare les données des deux objets <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </summary>
            <param name="obj">Objet à comparer.</param>
            <returns>
            Nombre signé indiquant les valeurs relatives de cette instance et de cette valeur.
            </returns>
            <exception cref="T:System.NotSupportedException">
            Thrown when the object passed in is not an instance of <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetEnumerator">
            <summary>
            Retourne un objet IEnumerator dont la longueur est dérivée de
            la propriété Data.
            </summary>
            <returns>Objet IEnumerator</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Clone">
            <summary>
            Retourne un objet GenericParameterHelper égal à
            l'objet actuel.
            </summary>
            <returns>Objet cloné.</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger">
            <summary>
            Permet aux utilisateurs de journaliser/d'écrire des traces de tests unitaires à des fins de diagnostic.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessageHandler">
            <summary>
            Gestionnaire de LogMessage.
            </summary>
            <param name="message">Message à journaliser.</param>
        </member>
        <member name="E:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.OnLogMessage">
            <summary>
            Événement à écouter. Déclenché quand le writer de test unitaire écrit un message.
            Sert principalement à être consommé par un adaptateur.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessage(System.String,System.Object[])">
            <summary>
            API à appeler par le writer de test pour journaliser les messages.
            </summary>
            <param name="format">Format de chaîne avec des espaces réservés.</param>
            <param name="args">Paramètres des espaces réservés.</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute">
            <summary>
            Attribut TestCategory utilisé pour spécifier la catégorie d'un test unitaire.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.#ctor(System.String)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute"/> et applique la catégorie au test.
            </summary>
            <param name="testCategory">
            Catégorie de test.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.TestCategories">
            <summary>
            Obtient les catégories de test appliquées au test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute">
            <summary>
            Classe de base de l'attribut "Category"
            </summary>
            <remarks>
            The reason for this attribute is to let the users create their own implementation of test categories.
            - test framework (discovery, etc) deals with TestCategoryBaseAttribute.
            - The reason that TestCategories property is a collection rather than a string,
              is to give more flexibility to the user. For instance the implementation may be based on enums for which the values can be OR'ed
              in which case it makes sense to have single attribute rather than multiple ones on the same test.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.#ctor">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute"/>.
            Applique la catégorie au test. Les chaînes retournées par TestCategories
            sont utilisées avec la commande /category pour filtrer les tests
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.TestCategories">
            <summary>
            Obtient la catégorie de test appliquée au test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            <summary>
            Classe AssertFailedException. Sert à indiquer l'échec d'un cas de test
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
            <param name="msg"> Message. </param>
            <param name="ex"> Exception. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
            <param name="msg"> Message. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/>.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert">
            <summary>
            Collection de classes d'assistance permettant de tester diverses conditions dans
            des tests unitaires. Si la condition testée n'est pas remplie, une exception
            est levée.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.That">
            <summary>
            Obtient l'instance singleton de la fonctionnalité Assert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void IsOfType&lt;T&gt;(this Assert assert, object obj)"
            Users could then use a syntax similar to the default assertions which in this case is "Assert.That.IsOfType&lt;Dog&gt;(animal);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean)">
            <summary>
            Teste si la condition spécifiée a la valeur true, et lève une exception
            si la condition a la valeur false.
            </summary>
            <param name="condition">
            Condition censée être vraie (true) pour le test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String)">
            <summary>
            Teste si la condition spécifiée a la valeur true, et lève une exception
            si la condition a la valeur false.
            </summary>
            <param name="condition">
            Condition censée être vraie (true) pour le test.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="condition"/>
            est false. Le message s'affiche dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String,System.Object[])">
            <summary>
            Teste si la condition spécifiée a la valeur true, et lève une exception
            si la condition a la valeur false.
            </summary>
            <param name="condition">
            Condition censée être vraie (true) pour le test.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="condition"/>
            est false. Le message s'affiche dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean)">
            <summary>
            Teste si la condition spécifiée a la valeur false, et lève une exception
            si la condition a la valeur true.
            </summary>
            <param name="condition">
            Condition censée être fausse (false) pour le test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String)">
            <summary>
            Teste si la condition spécifiée a la valeur false, et lève une exception
            si la condition a la valeur true.
            </summary>
            <param name="condition">
            Condition censée être fausse (false) pour le test.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="condition"/>
            est true. Le message s'affiche dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String,System.Object[])">
            <summary>
            Teste si la condition spécifiée a la valeur false, et lève une exception
            si la condition a la valeur true.
            </summary>
            <param name="condition">
            Condition censée être fausse (false) pour le test.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="condition"/>
            est true. Le message s'affiche dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object)">
            <summary>
            Teste si l'objet spécifié a une valeur null, et lève une exception
            si ce n'est pas le cas.
            </summary>
            <param name="value">
            Objet censé avoir une valeur null pour le test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String)">
            <summary>
            Teste si l'objet spécifié a une valeur null, et lève une exception
            si ce n'est pas le cas.
            </summary>
            <param name="value">
            Objet censé avoir une valeur null pour le test.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="value"/>
            n'a pas une valeur null. Le message s'affiche dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String,System.Object[])">
            <summary>
            Teste si l'objet spécifié a une valeur null, et lève une exception
            si ce n'est pas le cas.
            </summary>
            <param name="value">
            Objet censé avoir une valeur null pour le test.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="value"/>
            n'a pas une valeur null. Le message s'affiche dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object)">
            <summary>
            Teste si l'objet spécifié a une valeur non null, et lève une exception
            s'il a une valeur null.
            </summary>
            <param name="value">
            Objet censé ne pas avoir une valeur null pour le test.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String)">
            <summary>
            Teste si l'objet spécifié a une valeur non null, et lève une exception
            s'il a une valeur null.
            </summary>
            <param name="value">
            Objet censé ne pas avoir une valeur null pour le test.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="value"/>
            a une valeur null. Le message s'affiche dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String,System.Object[])">
            <summary>
            Teste si l'objet spécifié a une valeur non null, et lève une exception
            s'il a une valeur null.
            </summary>
            <param name="value">
            Objet censé ne pas avoir une valeur null pour le test.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="value"/>
            a une valeur null. Le message s'affiche dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object)">
            <summary>
            Teste si les objets spécifiés font référence au même objet, et
            lève une exception si les deux entrées ne font pas référence au même objet.
            </summary>
            <param name="expected">
            Premier objet à comparer. Valeur attendue par le test.
            </param>
            <param name="actual">
            Second objet à comparer. Il s'agit de la valeur produite par le code testé.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String)">
            <summary>
            Teste si les objets spécifiés font référence au même objet, et
            lève une exception si les deux entrées ne font pas référence au même objet.
            </summary>
            <param name="expected">
            Premier objet à comparer. Valeur attendue par le test.
            </param>
            <param name="actual">
            Second objet à comparer. Il s'agit de la valeur produite par le code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            n'est pas identique à <paramref name="expected"/>. Le message s'affiche
            dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Teste si les objets spécifiés font référence au même objet, et
            lève une exception si les deux entrées ne font pas référence au même objet.
            </summary>
            <param name="expected">
            Premier objet à comparer. Valeur attendue par le test.
            </param>
            <param name="actual">
            Second objet à comparer. Il s'agit de la valeur produite par le code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            n'est pas identique à <paramref name="expected"/>. Le message s'affiche
            dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object)">
            <summary>
            Teste si les objets spécifiés font référence à des objets distincts, et
            lève une exception si les deux entrées font référence au même objet.
            </summary>
            <param name="notExpected">
            Premier objet à comparer. Il s'agit de la valeur à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Second objet à comparer. Il s'agit de la valeur produite par le code testé.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String)">
            <summary>
            Teste si les objets spécifiés font référence à des objets distincts, et
            lève une exception si les deux entrées font référence au même objet.
            </summary>
            <param name="notExpected">
            Premier objet à comparer. Il s'agit de la valeur à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Second objet à comparer. Il s'agit de la valeur produite par le code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est identique à <paramref name="notExpected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Teste si les objets spécifiés font référence à des objets distincts, et
            lève une exception si les deux entrées font référence au même objet.
            </summary>
            <param name="notExpected">
            Premier objet à comparer. Il s'agit de la valeur à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Second objet à comparer. Il s'agit de la valeur produite par le code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est identique à <paramref name="notExpected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0)">
            <summary>
            Teste si les valeurs spécifiées sont identiques, et lève une exception
            si les deux valeurs sont différentes. Les types numériques distincts sont considérés comme
            différents même si les valeurs logiques sont identiques. 42L n'est pas égal à 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Première valeur à comparer. Valeur attendue par le test.
            </param>
            <param name="actual">
            Seconde valeur à comparer. Il s'agit de la valeur produite par le code testé.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String)">
            <summary>
            Teste si les valeurs spécifiées sont identiques, et lève une exception
            si les deux valeurs sont différentes. Les types numériques distincts sont considérés comme
            différents même si les valeurs logiques sont identiques. 42L n'est pas égal à 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Première valeur à comparer. Valeur attendue par le test.
            </param>
            <param name="actual">
            Seconde valeur à comparer. Il s'agit de la valeur produite par le code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            n'est pas égal à <paramref name="expected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Teste si les valeurs spécifiées sont identiques, et lève une exception
            si les deux valeurs sont différentes. Les types numériques distincts sont considérés comme
            différents même si les valeurs logiques sont identiques. 42L n'est pas égal à 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            Première valeur à comparer. Valeur attendue par le test.
            </param>
            <param name="actual">
            Seconde valeur à comparer. Il s'agit de la valeur produite par le code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            n'est pas égal à <paramref name="expected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0)">
            <summary>
            Teste si les valeurs spécifiées sont différentes, et lève une exception
            si les deux valeurs sont identiques. Les types numériques distincts sont considérés comme
            différents même si les valeurs logiques sont identiques. 42L n'est pas égal à 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Première valeur à comparer. Il s'agit de la valeur à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde valeur à comparer. Il s'agit de la valeur produite par le code testé.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String)">
            <summary>
            Teste si les valeurs spécifiées sont différentes, et lève une exception
            si les deux valeurs sont identiques. Les types numériques distincts sont considérés comme
            différents même si les valeurs logiques sont identiques. 42L n'est pas égal à 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Première valeur à comparer. Il s'agit de la valeur à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde valeur à comparer. Il s'agit de la valeur produite par le code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est égal à <paramref name="notExpected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            Teste si les valeurs spécifiées sont différentes, et lève une exception
            si les deux valeurs sont identiques. Les types numériques distincts sont considérés comme
            différents même si les valeurs logiques sont identiques. 42L n'est pas égal à 42.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            Première valeur à comparer. Il s'agit de la valeur à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde valeur à comparer. Il s'agit de la valeur produite par le code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est égal à <paramref name="notExpected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object)">
            <summary>
            Teste si les objets spécifiés sont identiques, et lève une exception
            si les deux objets ne sont pas identiques. Les types numériques distincts sont considérés comme
            différents même si les valeurs logiques sont identiques. 42L n'est pas égal à 42.
            </summary>
            <param name="expected">
            Premier objet à comparer. Objet attendu par le test.
            </param>
            <param name="actual">
            Second objet à comparer. Il s'agit de l'objet produit par le code testé.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String)">
            <summary>
            Teste si les objets spécifiés sont identiques, et lève une exception
            si les deux objets ne sont pas identiques. Les types numériques distincts sont considérés comme
            différents même si les valeurs logiques sont identiques. 42L n'est pas égal à 42.
            </summary>
            <param name="expected">
            Premier objet à comparer. Objet attendu par le test.
            </param>
            <param name="actual">
            Second objet à comparer. Il s'agit de l'objet produit par le code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            n'est pas égal à <paramref name="expected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Teste si les objets spécifiés sont identiques, et lève une exception
            si les deux objets ne sont pas identiques. Les types numériques distincts sont considérés comme
            différents même si les valeurs logiques sont identiques. 42L n'est pas égal à 42.
            </summary>
            <param name="expected">
            Premier objet à comparer. Objet attendu par le test.
            </param>
            <param name="actual">
            Second objet à comparer. Il s'agit de l'objet produit par le code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            n'est pas égal à <paramref name="expected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object)">
            <summary>
            Teste si les objets spécifiés sont différents, et lève une exception
            si les deux objets sont identiques. Les types numériques distincts sont considérés comme
            différents même si les valeurs logiques sont identiques. 42L n'est pas égal à 42.
            </summary>
            <param name="notExpected">
            Premier objet à comparer. Il s'agit de la valeur à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Second objet à comparer. Il s'agit de l'objet produit par le code testé.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String)">
            <summary>
            Teste si les objets spécifiés sont différents, et lève une exception
            si les deux objets sont identiques. Les types numériques distincts sont considérés comme
            différents même si les valeurs logiques sont identiques. 42L n'est pas égal à 42.
            </summary>
            <param name="notExpected">
            Premier objet à comparer. Il s'agit de la valeur à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Second objet à comparer. Il s'agit de l'objet produit par le code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est égal à <paramref name="notExpected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            Teste si les objets spécifiés sont différents, et lève une exception
            si les deux objets sont identiques. Les types numériques distincts sont considérés comme
            différents même si les valeurs logiques sont identiques. 42L n'est pas égal à 42.
            </summary>
            <param name="notExpected">
            Premier objet à comparer. Il s'agit de la valeur à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Second objet à comparer. Il s'agit de l'objet produit par le code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est égal à <paramref name="notExpected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single)">
            <summary>
            Teste si les valeurs float spécifiées sont identiques, et lève une exception
            si elles sont différentes.
            </summary>
            <param name="expected">
            Première valeur float à comparer. Valeur float attendue par le test.
            </param>
            <param name="actual">
            Seconde valeur float à comparer. Il s'agit de la valeur float produite par le code testé.
            </param>
            <param name="delta">
            Précision nécessaire. Une exception est levée uniquement si
            <paramref name="actual"/> est différent de <paramref name="expected"/>
            de plus de <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Teste si les valeurs float spécifiées sont identiques, et lève une exception
            si elles sont différentes.
            </summary>
            <param name="expected">
            Première valeur float à comparer. Valeur float attendue par le test.
            </param>
            <param name="actual">
            Seconde valeur float à comparer. Il s'agit de la valeur float produite par le code testé.
            </param>
            <param name="delta">
            Précision nécessaire. Une exception est levée uniquement si
            <paramref name="actual"/> est différent de <paramref name="expected"/>
            de plus de <paramref name="delta"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est différent de <paramref name="expected"/> de plus de
            <paramref name="delta"/>. Le message s'affiche dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Teste si les valeurs float spécifiées sont identiques, et lève une exception
            si elles sont différentes.
            </summary>
            <param name="expected">
            Première valeur float à comparer. Valeur float attendue par le test.
            </param>
            <param name="actual">
            Seconde valeur float à comparer. Il s'agit de la valeur float produite par le code testé.
            </param>
            <param name="delta">
            Précision nécessaire. Une exception est levée uniquement si
            <paramref name="actual"/> est différent de <paramref name="expected"/>
            de plus de <paramref name="delta"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est différent de <paramref name="expected"/> de plus de
            <paramref name="delta"/>. Le message s'affiche dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single)">
            <summary>
            Teste si les valeurs float spécifiées sont différentes, et lève une exception
            si elles sont identiques.
            </summary>
            <param name="notExpected">
            Première valeur float à comparer. Il s'agit de la valeur float à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde valeur float à comparer. Il s'agit de la valeur float produite par le code testé.
            </param>
            <param name="delta">
            Précision nécessaire. Une exception est levée uniquement si
            <paramref name="actual"/> est différent de <paramref name="notExpected"/>
            d'au maximum <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Teste si les valeurs float spécifiées sont différentes, et lève une exception
            si elles sont identiques.
            </summary>
            <param name="notExpected">
            Première valeur float à comparer. Il s'agit de la valeur float à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde valeur float à comparer. Il s'agit de la valeur float produite par le code testé.
            </param>
            <param name="delta">
            Précision nécessaire. Une exception est levée uniquement si
            <paramref name="actual"/> est différent de <paramref name="notExpected"/>
            d'au maximum <paramref name="delta"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est égal à <paramref name="notExpected"/> ou diffère de moins de
            <paramref name="delta"/>. Le message s'affiche dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            Teste si les valeurs float spécifiées sont différentes, et lève une exception
            si elles sont identiques.
            </summary>
            <param name="notExpected">
            Première valeur float à comparer. Il s'agit de la valeur float à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde valeur float à comparer. Il s'agit de la valeur float produite par le code testé.
            </param>
            <param name="delta">
            Précision nécessaire. Une exception est levée uniquement si
            <paramref name="actual"/> est différent de <paramref name="notExpected"/>
            d'au maximum <paramref name="delta"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est égal à <paramref name="notExpected"/> ou diffère de moins de
            <paramref name="delta"/>. Le message s'affiche dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double)">
            <summary>
            Teste si les valeurs double spécifiées sont identiques, et lève une exception
            si elles sont différentes.
            </summary>
            <param name="expected">
            Première valeur double à comparer. Valeur double attendue par le test.
            </param>
            <param name="actual">
            Seconde valeur double à comparer. Il s'agit de la valeur double produite par le code testé.
            </param>
            <param name="delta">
            Précision nécessaire. Une exception est levée uniquement si
            <paramref name="actual"/> est différent de <paramref name="expected"/>
            de plus de <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Teste si les valeurs double spécifiées sont identiques, et lève une exception
            si elles sont différentes.
            </summary>
            <param name="expected">
            Première valeur double à comparer. Valeur double attendue par le test.
            </param>
            <param name="actual">
            Seconde valeur double à comparer. Il s'agit de la valeur double produite par le code testé.
            </param>
            <param name="delta">
            Précision nécessaire. Une exception est levée uniquement si
            <paramref name="actual"/> est différent de <paramref name="expected"/>
            de plus de <paramref name="delta"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est différent de <paramref name="expected"/> de plus de
            <paramref name="delta"/>. Le message s'affiche dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Teste si les valeurs double spécifiées sont identiques, et lève une exception
            si elles sont différentes.
            </summary>
            <param name="expected">
            Première valeur double à comparer. Valeur double attendue par le test.
            </param>
            <param name="actual">
            Seconde valeur double à comparer. Il s'agit de la valeur double produite par le code testé.
            </param>
            <param name="delta">
            Précision nécessaire. Une exception est levée uniquement si
            <paramref name="actual"/> est différent de <paramref name="expected"/>
            de plus de <paramref name="delta"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est différent de <paramref name="expected"/> de plus de
            <paramref name="delta"/>. Le message s'affiche dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double)">
            <summary>
            Teste si les valeurs double spécifiées sont différentes, et lève une exception
            si elles sont identiques.
            </summary>
            <param name="notExpected">
            Première valeur double à comparer. Il s'agit de la valeur double à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde valeur double à comparer. Il s'agit de la valeur double produite par le code testé.
            </param>
            <param name="delta">
            Précision nécessaire. Une exception est levée uniquement si
            <paramref name="actual"/> est différent de <paramref name="notExpected"/>
            d'au maximum <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Teste si les valeurs double spécifiées sont différentes, et lève une exception
            si elles sont identiques.
            </summary>
            <param name="notExpected">
            Première valeur double à comparer. Il s'agit de la valeur double à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde valeur double à comparer. Il s'agit de la valeur double produite par le code testé.
            </param>
            <param name="delta">
            Précision nécessaire. Une exception est levée uniquement si
            <paramref name="actual"/> est différent de <paramref name="notExpected"/>
            d'au maximum <paramref name="delta"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est égal à <paramref name="notExpected"/> ou diffère de moins de
            <paramref name="delta"/>. Le message s'affiche dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            Teste si les valeurs double spécifiées sont différentes, et lève une exception
            si elles sont identiques.
            </summary>
            <param name="notExpected">
            Première valeur double à comparer. Il s'agit de la valeur double à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde valeur double à comparer. Il s'agit de la valeur double produite par le code testé.
            </param>
            <param name="delta">
            Précision nécessaire. Une exception est levée uniquement si
            <paramref name="actual"/> est différent de <paramref name="notExpected"/>
            d'au maximum <paramref name="delta"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est égal à <paramref name="notExpected"/> ou diffère de moins de
            <paramref name="delta"/>. Le message s'affiche dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean)">
            <summary>
            Teste si les chaînes spécifiées sont identiques, et lève une exception
            si elles sont différentes. La culture invariante est utilisée pour la comparaison.
            </summary>
            <param name="expected">
            Première chaîne à comparer. Chaîne attendue par le test.
            </param>
            <param name="actual">
            Seconde chaîne à comparer. Il s'agit de la chaîne produite par le code testé.
            </param>
            <param name="ignoreCase">
            Booléen indiquant une comparaison qui respecte la casse ou non. (true
            indique une comparaison qui ne respecte pas la casse.)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Teste si les chaînes spécifiées sont identiques, et lève une exception
            si elles sont différentes. La culture invariante est utilisée pour la comparaison.
            </summary>
            <param name="expected">
            Première chaîne à comparer. Chaîne attendue par le test.
            </param>
            <param name="actual">
            Seconde chaîne à comparer. Il s'agit de la chaîne produite par le code testé.
            </param>
            <param name="ignoreCase">
            Booléen indiquant une comparaison qui respecte la casse ou non. (true
            indique une comparaison qui ne respecte pas la casse.)
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            n'est pas égal à <paramref name="expected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Teste si les chaînes spécifiées sont identiques, et lève une exception
            si elles sont différentes. La culture invariante est utilisée pour la comparaison.
            </summary>
            <param name="expected">
            Première chaîne à comparer. Chaîne attendue par le test.
            </param>
            <param name="actual">
            Seconde chaîne à comparer. Il s'agit de la chaîne produite par le code testé.
            </param>
            <param name="ignoreCase">
            Booléen indiquant une comparaison qui respecte la casse ou non. (true
            indique une comparaison qui ne respecte pas la casse.)
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            n'est pas égal à <paramref name="expected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Teste si les chaînes spécifiées sont identiques, et lève une exception
            si elles sont différentes.
            </summary>
            <param name="expected">
            Première chaîne à comparer. Chaîne attendue par le test.
            </param>
            <param name="actual">
            Seconde chaîne à comparer. Il s'agit de la chaîne produite par le code testé.
            </param>
            <param name="ignoreCase">
            Booléen indiquant une comparaison qui respecte la casse ou non. (true
            indique une comparaison qui ne respecte pas la casse.)
            </param>
            <param name="culture">
            Objet CultureInfo qui fournit des informations de comparaison spécifiques à la culture.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Teste si les chaînes spécifiées sont identiques, et lève une exception
            si elles sont différentes.
            </summary>
            <param name="expected">
            Première chaîne à comparer. Chaîne attendue par le test.
            </param>
            <param name="actual">
            Seconde chaîne à comparer. Il s'agit de la chaîne produite par le code testé.
            </param>
            <param name="ignoreCase">
            Booléen indiquant une comparaison qui respecte la casse ou non. (true
            indique une comparaison qui ne respecte pas la casse.)
            </param>
            <param name="culture">
            Objet CultureInfo qui fournit des informations de comparaison spécifiques à la culture.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            n'est pas égal à <paramref name="expected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Teste si les chaînes spécifiées sont identiques, et lève une exception
            si elles sont différentes.
            </summary>
            <param name="expected">
            Première chaîne à comparer. Chaîne attendue par le test.
            </param>
            <param name="actual">
            Seconde chaîne à comparer. Il s'agit de la chaîne produite par le code testé.
            </param>
            <param name="ignoreCase">
            Booléen indiquant une comparaison qui respecte la casse ou non. (true
            indique une comparaison qui ne respecte pas la casse.)
            </param>
            <param name="culture">
            Objet CultureInfo qui fournit des informations de comparaison spécifiques à la culture.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            n'est pas égal à <paramref name="expected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean)">
            <summary>
            Teste si les chaînes spécifiées sont différentes, et lève une exception
            si elles sont identiques. La culture invariante est utilisée pour la comparaison.
            </summary>
            <param name="notExpected">
            Première chaîne à comparer. Il s'agit de la chaîne à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde chaîne à comparer. Il s'agit de la chaîne produite par le code testé.
            </param>
            <param name="ignoreCase">
            Booléen indiquant une comparaison qui respecte la casse ou non. (true
            indique une comparaison qui ne respecte pas la casse.)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Teste si les chaînes spécifiées sont différentes, et lève une exception
            si elles sont identiques. La culture invariante est utilisée pour la comparaison.
            </summary>
            <param name="notExpected">
            Première chaîne à comparer. Il s'agit de la chaîne à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde chaîne à comparer. Il s'agit de la chaîne produite par le code testé.
            </param>
            <param name="ignoreCase">
            Booléen indiquant une comparaison qui respecte la casse ou non. (true
            indique une comparaison qui ne respecte pas la casse.)
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est égal à <paramref name="notExpected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            Teste si les chaînes spécifiées sont différentes, et lève une exception
            si elles sont identiques. La culture invariante est utilisée pour la comparaison.
            </summary>
            <param name="notExpected">
            Première chaîne à comparer. Il s'agit de la chaîne à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde chaîne à comparer. Il s'agit de la chaîne produite par le code testé.
            </param>
            <param name="ignoreCase">
            Booléen indiquant une comparaison qui respecte la casse ou non. (true
            indique une comparaison qui ne respecte pas la casse.)
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est égal à <paramref name="notExpected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Teste si les chaînes spécifiées sont différentes, et lève une exception
            si elles sont identiques.
            </summary>
            <param name="notExpected">
            Première chaîne à comparer. Il s'agit de la chaîne à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde chaîne à comparer. Il s'agit de la chaîne produite par le code testé.
            </param>
            <param name="ignoreCase">
            Booléen indiquant une comparaison qui respecte la casse ou non. (true
            indique une comparaison qui ne respecte pas la casse.)
            </param>
            <param name="culture">
            Objet CultureInfo qui fournit des informations de comparaison spécifiques à la culture.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            Teste si les chaînes spécifiées sont différentes, et lève une exception
            si elles sont identiques.
            </summary>
            <param name="notExpected">
            Première chaîne à comparer. Il s'agit de la chaîne à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde chaîne à comparer. Il s'agit de la chaîne produite par le code testé.
            </param>
            <param name="ignoreCase">
            Booléen indiquant une comparaison qui respecte la casse ou non. (true
            indique une comparaison qui ne respecte pas la casse.)
            </param>
            <param name="culture">
            Objet CultureInfo qui fournit des informations de comparaison spécifiques à la culture.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est égal à <paramref name="notExpected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            Teste si les chaînes spécifiées sont différentes, et lève une exception
            si elles sont identiques.
            </summary>
            <param name="notExpected">
            Première chaîne à comparer. Il s'agit de la chaîne à laquelle le test est censé ne pas
            correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde chaîne à comparer. Il s'agit de la chaîne produite par le code testé.
            </param>
            <param name="ignoreCase">
            Booléen indiquant une comparaison qui respecte la casse ou non. (true
            indique une comparaison qui ne respecte pas la casse.)
            </param>
            <param name="culture">
            Objet CultureInfo qui fournit des informations de comparaison spécifiques à la culture.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est égal à <paramref name="notExpected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type)">
            <summary>
            Teste si l'objet spécifié est une instance du
            type attendu, et lève une exception si le type attendu n'est pas dans
            la hiérarchie d'héritage de l'objet.
            </summary>
            <param name="value">
            Objet censé être du type spécifié pour le test.
            </param>
            <param name="expectedType">
            Le type attendu de <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Teste si l'objet spécifié est une instance du
            type attendu, et lève une exception si le type attendu n'est pas dans
            la hiérarchie d'héritage de l'objet.
            </summary>
            <param name="value">
            Objet censé être du type spécifié pour le test.
            </param>
            <param name="expectedType">
            Le type attendu de <paramref name="value"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="value"/>
            n'est pas une instance de <paramref name="expectedType"/>. Le message
            s'affiche dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Teste si l'objet spécifié est une instance du
            type attendu, et lève une exception si le type attendu n'est pas dans
            la hiérarchie d'héritage de l'objet.
            </summary>
            <param name="value">
            Objet censé être du type spécifié pour le test.
            </param>
            <param name="expectedType">
            Le type attendu de <paramref name="value"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="value"/>
            n'est pas une instance de <paramref name="expectedType"/>. Le message
            s'affiche dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type)">
            <summary>
            Teste si l'objet spécifié n'est pas une instance du mauvais
            type, et lève une exception si le type spécifié est dans
            la hiérarchie d'héritage de l'objet.
            </summary>
            <param name="value">
            Objet censé ne pas être du type spécifié pour le test.
            </param>
            <param name="wrongType">
            Type auquel <paramref name="value"/> ne doit pas correspondre.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            Teste si l'objet spécifié n'est pas une instance du mauvais
            type, et lève une exception si le type spécifié est dans
            la hiérarchie d'héritage de l'objet.
            </summary>
            <param name="value">
            Objet censé ne pas être du type spécifié pour le test.
            </param>
            <param name="wrongType">
            Type auquel <paramref name="value"/> ne doit pas correspondre.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="value"/>
            est une instance de <paramref name="wrongType"/>. Le message s'affiche
            dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            Teste si l'objet spécifié n'est pas une instance du mauvais
            type, et lève une exception si le type spécifié est dans
            la hiérarchie d'héritage de l'objet.
            </summary>
            <param name="value">
            Objet censé ne pas être du type spécifié pour le test.
            </param>
            <param name="wrongType">
            Type auquel <paramref name="value"/> ne doit pas correspondre.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="value"/>
            est une instance de <paramref name="wrongType"/>. Le message s'affiche
            dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail">
            <summary>
            Lève AssertFailedException.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String)">
            <summary>
            Lève AssertFailedException.
            </summary>
            <param name="message">
            Message à inclure dans l'exception. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String,System.Object[])">
            <summary>
            Lève AssertFailedException.
            </summary>
            <param name="message">
            Message à inclure dans l'exception. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive">
            <summary>
            Lève AssertInconclusiveException.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String)">
            <summary>
            Lève AssertInconclusiveException.
            </summary>
            <param name="message">
            Message à inclure dans l'exception. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String,System.Object[])">
            <summary>
            Lève AssertInconclusiveException.
            </summary>
            <param name="message">
            Message à inclure dans l'exception. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Equals(System.Object,System.Object)">
            <summary>
            Les surcharges statiques d'equals comparent les instances de deux types pour déterminer si leurs références sont
            égales entre elles. Cette méthode ne doit <b>pas</b> être utilisée pour évaluer si deux instances sont
            égales entre elles. Cet objet est <b>toujours</b> levé avec Assert.Fail. Utilisez
            Assert.AreEqual et les surcharges associées dans vos tests unitaires.
            </summary>
            <param name="objA"> Objet A </param>
            <param name="objB"> Objet B </param>
            <returns> False, toujours. </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action)">
            <summary>
            Teste si le code spécifié par le délégué <paramref name="action"/> lève une exception précise de type <typeparamref name="T"/> (et non d'un type dérivé)
            et lève
            <code>
            AssertFailedException
            </code>
            si le code ne lève pas d'exception, ou lève une exception d'un autre type que <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Délégué du code à tester et censé lever une exception.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Type de l'exception censée être levée.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String)">
            <summary>
            Teste si le code spécifié par le délégué <paramref name="action"/> lève une exception précise de type <typeparamref name="T"/> (et non d'un type dérivé)
            et lève
            <code>
            AssertFailedException
            </code>
            si le code ne lève pas d'exception, ou lève une exception d'un autre type que <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Délégué du code à tester et censé lever une exception.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="action"/>
            ne lève pas d'exception de type <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Type de l'exception censée être levée.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object})">
            <summary>
            Teste si le code spécifié par le délégué <paramref name="action"/> lève une exception précise de type <typeparamref name="T"/> (et non d'un type dérivé)
            et lève
            <code>
            AssertFailedException
            </code>
            si le code ne lève pas d'exception, ou lève une exception d'un autre type que <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Délégué du code à tester et censé lever une exception.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Type de l'exception censée être levée.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String)">
            <summary>
            Teste si le code spécifié par le délégué <paramref name="action"/> lève une exception précise de type <typeparamref name="T"/> (et non d'un type dérivé)
            et lève
            <code>
            AssertFailedException
            </code>
            si le code ne lève pas d'exception, ou lève une exception d'un autre type que <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Délégué du code à tester et censé lever une exception.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="action"/>
            ne lève pas d'exception de type <typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Type de l'exception censée être levée.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String,System.Object[])">
            <summary>
            Teste si le code spécifié par le délégué <paramref name="action"/> lève une exception précise de type <typeparamref name="T"/> (et non d'un type dérivé)
            et lève
            <code>
            AssertFailedException
            </code>
            si le code ne lève pas d'exception, ou lève une exception d'un autre type que <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Délégué du code à tester et censé lever une exception.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="action"/>
            ne lève pas d'exception de type <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throw exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Type de l'exception censée être levée.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String,System.Object[])">
            <summary>
            Teste si le code spécifié par le délégué <paramref name="action"/> lève une exception précise de type <typeparamref name="T"/> (et non d'un type dérivé)
            et lève
            <code>
            AssertFailedException
            </code>
            si le code ne lève pas d'exception, ou lève une exception d'un autre type que <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Délégué du code à tester et censé lever une exception.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="action"/>
            ne lève pas d'exception de type <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Type de l'exception censée être levée.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Teste si le code spécifié par le délégué <paramref name="action"/> lève une exception précise de type <typeparamref name="T"/> (et non d'un type dérivé)
            et lève
            <code>
            AssertFailedException
            </code>
            si le code ne lève pas d'exception, ou lève une exception d'un autre type que <typeparamref name="T"/>.
            </summary>
            <param name="action">
            Délégué du code à tester et censé lever une exception.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Le <see cref="T:System.Threading.Tasks.Task"/> qui exécute le délégué.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            Teste si le code spécifié par le délégué <paramref name="action"/> lève une exception précise de type <typeparamref name="T"/> (et non d'un type dérivé)
            et lève <code>AssertFailedException</code> si le code ne lève pas d'exception, ou lève une exception d'un autre type que <typeparamref name="T"/>.
            </summary>
            <param name="action">Délégué du code à tester et censé lever une exception.</param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="action"/>
            ne lève pas d'exception de type <typeparamref name="T"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Le <see cref="T:System.Threading.Tasks.Task"/> qui exécute le délégué.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String,System.Object[])">
            <summary>
            Teste si le code spécifié par le délégué <paramref name="action"/> lève une exception précise de type <typeparamref name="T"/> (et non d'un type dérivé)
            et lève <code>AssertFailedException</code> si le code ne lève pas d'exception, ou lève une exception d'un autre type que <typeparamref name="T"/>.
            </summary>
            <param name="action">Délégué du code à tester et censé lever une exception.</param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="action"/>
            ne lève pas d'exception de type <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            Le <see cref="T:System.Threading.Tasks.Task"/> qui exécute le délégué.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNullChars(System.String)">
            <summary>
            Remplace les caractères Null ('\0') par "\\0".
            </summary>
            <param name="input">
            Chaîne à rechercher.
            </param>
            <returns>
            Chaîne convertie où les caractères null sont remplacés par "\\0".
            </returns>
            <remarks>
            This is only public and still present to preserve compatibility with the V1 framework.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.HandleFail(System.String,System.String,System.Object[])">
            <summary>
            Fonction d'assistance qui crée et lève AssertionFailedException
            </summary>
            <param name="assertionName">
            nom de l'assertion levant une exception
            </param>
            <param name="message">
            message décrivant les conditions de l'échec d'assertion
            </param>
            <param name="parameters">
            Paramètres.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.CheckParameterNotNull(System.Object,System.String,System.String,System.String,System.Object[])">
            <summary>
            Vérifie la validité des conditions du paramètre
            </summary>
            <param name="param">
            Paramètre.
            </param>
            <param name="assertionName">
            Nom de l'assertion.
            </param>
            <param name="parameterName">
            nom du paramètre
            </param>
            <param name="message">
            message d'exception liée à un paramètre non valide
            </param>
            <param name="parameters">
            Paramètres.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNulls(System.Object)">
            <summary>
            Convertit en toute sécurité un objet en chaîne, en gérant les valeurs null et les caractères Null.
            Les valeurs null sont converties en "(null)". Les caractères Null sont convertis en "\\0".
            </summary>
            <param name="input">
            Objet à convertir en chaîne.
            </param>
            <returns>
            Chaîne convertie.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert">
            <summary>
            Assertion de chaîne.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.That">
            <summary>
            Obtient l'instance singleton de la fonctionnalité CollectionAssert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void ContainsWords(this StringAssert cusomtAssert, string value, ICollection substrings)"
            Users could then use a syntax similar to the default assertions which in this case is "StringAssert.That.ContainsWords(value, substrings);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String)">
            <summary>
            Teste si la chaîne indiquée contient la sous-chaîne spécifiée
            et lève une exception si la sous-chaîne ne figure pas dans
            la chaîne de test.
            </summary>
            <param name="value">
            Chaîne censée contenir <paramref name="substring"/>.
            </param>
            <param name="substring">
            Chaîne censée se trouver dans <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String)">
            <summary>
            Teste si la chaîne indiquée contient la sous-chaîne spécifiée
            et lève une exception si la sous-chaîne ne figure pas dans
            la chaîne de test.
            </summary>
            <param name="value">
            Chaîne censée contenir <paramref name="substring"/>.
            </param>
            <param name="substring">
            Chaîne censée se trouver dans <paramref name="value"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="substring"/>
            n'est pas dans <paramref name="value"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String,System.Object[])">
            <summary>
            Teste si la chaîne indiquée contient la sous-chaîne spécifiée
            et lève une exception si la sous-chaîne ne figure pas dans
            la chaîne de test.
            </summary>
            <param name="value">
            Chaîne censée contenir <paramref name="substring"/>.
            </param>
            <param name="substring">
            Chaîne censée se trouver dans <paramref name="value"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="substring"/>
            n'est pas dans <paramref name="value"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String)">
            <summary>
            Teste si la chaîne indiquée commence par la sous-chaîne spécifiée
            et lève une exception si la chaîne de test ne commence pas par la
            sous-chaîne.
            </summary>
            <param name="value">
            Chaîne censée commencer par <paramref name="substring"/>.
            </param>
            <param name="substring">
            Chaîne censée être un préfixe de <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String)">
            <summary>
            Teste si la chaîne indiquée commence par la sous-chaîne spécifiée
            et lève une exception si la chaîne de test ne commence pas par la
            sous-chaîne.
            </summary>
            <param name="value">
            Chaîne censée commencer par <paramref name="substring"/>.
            </param>
            <param name="substring">
            Chaîne censée être un préfixe de <paramref name="value"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="value"/>
            ne commence pas par <paramref name="substring"/>. Le message
            s'affiche dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Teste si la chaîne indiquée commence par la sous-chaîne spécifiée
            et lève une exception si la chaîne de test ne commence pas par la
            sous-chaîne.
            </summary>
            <param name="value">
            Chaîne censée commencer par <paramref name="substring"/>.
            </param>
            <param name="substring">
            Chaîne censée être un préfixe de <paramref name="value"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="value"/>
            ne commence pas par <paramref name="substring"/>. Le message
            s'affiche dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String)">
            <summary>
            Teste si la chaîne indiquée finit par la sous-chaîne spécifiée
            et lève une exception si la chaîne de test ne finit pas par la
            sous-chaîne.
            </summary>
            <param name="value">
            Chaîne censée finir par <paramref name="substring"/>.
            </param>
            <param name="substring">
            Chaîne censée être un suffixe de <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String)">
            <summary>
            Teste si la chaîne indiquée finit par la sous-chaîne spécifiée
            et lève une exception si la chaîne de test ne finit pas par la
            sous-chaîne.
            </summary>
            <param name="value">
            Chaîne censée finir par <paramref name="substring"/>.
            </param>
            <param name="substring">
            Chaîne censée être un suffixe de <paramref name="value"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="value"/>
            ne finit pas par <paramref name="substring"/>. Le message
            s'affiche dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            Teste si la chaîne indiquée finit par la sous-chaîne spécifiée
            et lève une exception si la chaîne de test ne finit pas par la
            sous-chaîne.
            </summary>
            <param name="value">
            Chaîne censée finir par <paramref name="substring"/>.
            </param>
            <param name="substring">
            Chaîne censée être un suffixe de <paramref name="value"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="value"/>
            ne finit pas par <paramref name="substring"/>. Le message
            s'affiche dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Teste si la chaîne spécifiée correspond à une expression régulière, et
            lève une exception si la chaîne ne correspond pas à l'expression.
            </summary>
            <param name="value">
            Chaîne censée correspondre à <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Expression régulière qui <paramref name="value"/> est
            censé correspondre.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Teste si la chaîne spécifiée correspond à une expression régulière, et
            lève une exception si la chaîne ne correspond pas à l'expression.
            </summary>
            <param name="value">
            Chaîne censée correspondre à <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Expression régulière qui <paramref name="value"/> est
            censé correspondre.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="value"/>
            ne correspond pas <paramref name="pattern"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Teste si la chaîne spécifiée correspond à une expression régulière, et
            lève une exception si la chaîne ne correspond pas à l'expression.
            </summary>
            <param name="value">
            Chaîne censée correspondre à <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Expression régulière qui <paramref name="value"/> est
            censé correspondre.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="value"/>
            ne correspond pas <paramref name="pattern"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Teste si la chaîne spécifiée ne correspond pas à une expression régulière
            et lève une exception si la chaîne correspond à l'expression.
            </summary>
            <param name="value">
            Chaîne censée ne pas correspondre à <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Expression régulière qui <paramref name="value"/> est
            censé ne pas correspondre.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Teste si la chaîne spécifiée ne correspond pas à une expression régulière
            et lève une exception si la chaîne correspond à l'expression.
            </summary>
            <param name="value">
            Chaîne censée ne pas correspondre à <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Expression régulière qui <paramref name="value"/> est
            censé ne pas correspondre.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="value"/>
            correspond à <paramref name="pattern"/>. Le message s'affiche dans les
            résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            Teste si la chaîne spécifiée ne correspond pas à une expression régulière
            et lève une exception si la chaîne correspond à l'expression.
            </summary>
            <param name="value">
            Chaîne censée ne pas correspondre à <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            Expression régulière qui <paramref name="value"/> est
            censé ne pas correspondre.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="value"/>
            correspond à <paramref name="pattern"/>. Le message s'affiche dans les
            résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert">
            <summary>
            Collection de classes d'assistance permettant de tester diverses conditions associées
            à des collections dans les tests unitaires. Si la condition testée n'est pas
            remplie, une exception est levée.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.That">
            <summary>
            Obtient l'instance singleton de la fonctionnalité CollectionAssert.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void AreEqualUnordered(this CollectionAssert cusomtAssert, ICollection expected, ICollection actual)"
            Users could then use a syntax similar to the default assertions which in this case is "CollectionAssert.That.AreEqualUnordered(list1, list2);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object)">
            <summary>
            Teste si la collection indiquée contient l'élément spécifié
            et lève une exception si l'élément n'est pas dans la collection.
            </summary>
            <param name="collection">
            Collection dans laquelle rechercher l'élément.
            </param>
            <param name="element">
            Élément censé se trouver dans la collection.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Teste si la collection indiquée contient l'élément spécifié
            et lève une exception si l'élément n'est pas dans la collection.
            </summary>
            <param name="collection">
            Collection dans laquelle rechercher l'élément.
            </param>
            <param name="element">
            Élément censé se trouver dans la collection.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="element"/>
            n'est pas dans <paramref name="collection"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Teste si la collection indiquée contient l'élément spécifié
            et lève une exception si l'élément n'est pas dans la collection.
            </summary>
            <param name="collection">
            Collection dans laquelle rechercher l'élément.
            </param>
            <param name="element">
            Élément censé se trouver dans la collection.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="element"/>
            n'est pas dans <paramref name="collection"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object)">
            <summary>
            Teste si la collection indiquée ne contient pas l'élément spécifié
            et lève une exception si l'élément est dans la collection.
            </summary>
            <param name="collection">
            Collection dans laquelle rechercher l'élément.
            </param>
            <param name="element">
            Élément censé ne pas se trouver dans la collection.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            Teste si la collection indiquée ne contient pas l'élément spécifié
            et lève une exception si l'élément est dans la collection.
            </summary>
            <param name="collection">
            Collection dans laquelle rechercher l'élément.
            </param>
            <param name="element">
            Élément censé ne pas se trouver dans la collection.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="element"/>
            est dans <paramref name="collection"/>. Le message s'affiche dans les
            résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            Teste si la collection indiquée ne contient pas l'élément spécifié
            et lève une exception si l'élément est dans la collection.
            </summary>
            <param name="collection">
            Collection dans laquelle rechercher l'élément.
            </param>
            <param name="element">
            Élément censé ne pas se trouver dans la collection.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="element"/>
            est dans <paramref name="collection"/>. Le message s'affiche dans les
            résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection)">
            <summary>
            Teste si tous les éléments de la collection spécifiée ont des valeurs non null, et lève
            une exception si un élément a une valeur null.
            </summary>
            <param name="collection">
            Collection dans laquelle rechercher les éléments ayant une valeur null.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String)">
            <summary>
            Teste si tous les éléments de la collection spécifiée ont des valeurs non null, et lève
            une exception si un élément a une valeur null.
            </summary>
            <param name="collection">
            Collection dans laquelle rechercher les éléments ayant une valeur null.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="collection"/>
            contient un élément ayant une valeur null. Le message s'affiche dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Teste si tous les éléments de la collection spécifiée ont des valeurs non null, et lève
            une exception si un élément a une valeur null.
            </summary>
            <param name="collection">
            Collection dans laquelle rechercher les éléments ayant une valeur null.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="collection"/>
            contient un élément ayant une valeur null. Le message s'affiche dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection)">
            <summary>
            Teste si tous les éléments de la collection spécifiée sont uniques ou non, et
            lève une exception si deux éléments de la collection sont identiques.
            </summary>
            <param name="collection">
            Collection dans laquelle rechercher les éléments dupliqués.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String)">
            <summary>
            Teste si tous les éléments de la collection spécifiée sont uniques ou non, et
            lève une exception si deux éléments de la collection sont identiques.
            </summary>
            <param name="collection">
            Collection dans laquelle rechercher les éléments dupliqués.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="collection"/>
            contient au moins un élément dupliqué. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Teste si tous les éléments de la collection spécifiée sont uniques ou non, et
            lève une exception si deux éléments de la collection sont identiques.
            </summary>
            <param name="collection">
            Collection dans laquelle rechercher les éléments dupliqués.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="collection"/>
            contient au moins un élément dupliqué. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Teste si une collection est un sous-ensemble d'une autre collection et
            lève une exception si un élément du sous-ensemble ne se trouve pas également dans le
            sur-ensemble.
            </summary>
            <param name="subset">
            Collection censée être un sous-ensemble de <paramref name="superset"/>.
            </param>
            <param name="superset">
            Collection censée être un sur-ensemble de <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Teste si une collection est un sous-ensemble d'une autre collection et
            lève une exception si un élément du sous-ensemble ne se trouve pas également dans le
            sur-ensemble.
            </summary>
            <param name="subset">
            Collection censée être un sous-ensemble de <paramref name="superset"/>.
            </param>
            <param name="superset">
            Collection censée être un sur-ensemble de <paramref name="subset"/>
            </param>
            <param name="message">
            Message à inclure dans l'exception quand un élément présent dans
            <paramref name="subset"/> est introuvable dans <paramref name="superset"/>.
            Le message s'affiche dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Teste si une collection est un sous-ensemble d'une autre collection et
            lève une exception si un élément du sous-ensemble ne se trouve pas également dans le
            sur-ensemble.
            </summary>
            <param name="subset">
            Collection censée être un sous-ensemble de <paramref name="superset"/>.
            </param>
            <param name="superset">
            Collection censée être un sur-ensemble de <paramref name="subset"/>
            </param>
            <param name="message">
            Message à inclure dans l'exception quand un élément présent dans
            <paramref name="subset"/> est introuvable dans <paramref name="superset"/>.
            Le message s'affiche dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Teste si une collection n'est pas un sous-ensemble d'une autre collection et
            lève une exception si tous les éléments du sous-ensemble se trouvent également dans le
            sur-ensemble.
            </summary>
            <param name="subset">
            Collection censée ne pas être un sous-ensemble de <paramref name="superset"/>.
            </param>
            <param name="superset">
            Collection censée ne pas être un sur-ensemble de <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Teste si une collection n'est pas un sous-ensemble d'une autre collection et
            lève une exception si tous les éléments du sous-ensemble se trouvent également dans le
            sur-ensemble.
            </summary>
            <param name="subset">
            Collection censée ne pas être un sous-ensemble de <paramref name="superset"/>.
            </param>
            <param name="superset">
            Collection censée ne pas être un sur-ensemble de <paramref name="subset"/>
            </param>
            <param name="message">
            Message à inclure dans l'exception quand chaque élément présent dans
            <paramref name="subset"/> est également trouvé dans <paramref name="superset"/>.
            Le message s'affiche dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Teste si une collection n'est pas un sous-ensemble d'une autre collection et
            lève une exception si tous les éléments du sous-ensemble se trouvent également dans le
            sur-ensemble.
            </summary>
            <param name="subset">
            Collection censée ne pas être un sous-ensemble de <paramref name="superset"/>.
            </param>
            <param name="superset">
            Collection censée ne pas être un sur-ensemble de <paramref name="subset"/>
            </param>
            <param name="message">
            Message à inclure dans l'exception quand chaque élément présent dans
            <paramref name="subset"/> est également trouvé dans <paramref name="superset"/>.
            Le message s'affiche dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Teste si deux collections contiennent les mêmes éléments, et lève une
            exception si l'une des collections contient un élément non présent dans l'autre
            collection.
            </summary>
            <param name="expected">
            Première collection à comparer. Ceci contient les éléments que le test
            attend.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par
            le code testé.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Teste si deux collections contiennent les mêmes éléments, et lève une
            exception si l'une des collections contient un élément non présent dans l'autre
            collection.
            </summary>
            <param name="expected">
            Première collection à comparer. Ceci contient les éléments que le test
            attend.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par
            le code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand un élément est trouvé
            dans l'une des collections mais pas l'autre. Le message s'affiche
            dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Teste si deux collections contiennent les mêmes éléments, et lève une
            exception si l'une des collections contient un élément non présent dans l'autre
            collection.
            </summary>
            <param name="expected">
            Première collection à comparer. Ceci contient les éléments que le test
            attend.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par
            le code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand un élément est trouvé
            dans l'une des collections mais pas l'autre. Le message s'affiche
            dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Teste si deux collections contiennent des éléments distincts, et lève une
            exception si les deux collections contiennent des éléments identiques, indépendamment
            de l'ordre.
            </summary>
            <param name="expected">
            Première collection à comparer. Ceci contient les éléments que le test
            est censé différencier des éléments de la collection réelle.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par
            le code testé.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Teste si deux collections contiennent des éléments distincts, et lève une
            exception si les deux collections contiennent des éléments identiques, indépendamment
            de l'ordre.
            </summary>
            <param name="expected">
            Première collection à comparer. Ceci contient les éléments que le test
            est censé différencier des éléments de la collection réelle.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par
            le code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            contient les mêmes éléments que <paramref name="expected"/>. Le message
            s'affiche dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Teste si deux collections contiennent des éléments distincts, et lève une
            exception si les deux collections contiennent des éléments identiques, indépendamment
            de l'ordre.
            </summary>
            <param name="expected">
            Première collection à comparer. Ceci contient les éléments que le test
            est censé différencier des éléments de la collection réelle.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par
            le code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            contient les mêmes éléments que <paramref name="expected"/>. Le message
            s'affiche dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type)">
            <summary>
            Teste si tous les éléments de la collection spécifiée sont des instances
            du type attendu, et lève une exception si le type attendu
            n'est pas dans la hiérarchie d'héritage d'un ou de plusieurs éléments.
            </summary>
            <param name="collection">
            Collection contenant des éléments que le test considère comme étant
            du type spécifié.
            </param>
            <param name="expectedType">
            Type attendu de chaque élément de <paramref name="collection"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String)">
            <summary>
            Teste si tous les éléments de la collection spécifiée sont des instances
            du type attendu, et lève une exception si le type attendu
            n'est pas dans la hiérarchie d'héritage d'un ou de plusieurs éléments.
            </summary>
            <param name="collection">
            Collection contenant des éléments que le test considère comme étant
            du type spécifié.
            </param>
            <param name="expectedType">
            Type attendu de chaque élément de <paramref name="collection"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand un élément présent dans
            <paramref name="collection"/> n'est pas une instance de
            <paramref name="expectedType"/>. Le message s'affiche dans les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String,System.Object[])">
            <summary>
            Teste si tous les éléments de la collection spécifiée sont des instances
            du type attendu, et lève une exception si le type attendu
            n'est pas dans la hiérarchie d'héritage d'un ou de plusieurs éléments.
            </summary>
            <param name="collection">
            Collection contenant des éléments que le test considère comme étant
            du type spécifié.
            </param>
            <param name="expectedType">
            Type attendu de chaque élément de <paramref name="collection"/>.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand un élément présent dans
            <paramref name="collection"/> n'est pas une instance de
            <paramref name="expectedType"/>. Le message s'affiche dans les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Teste si les collections spécifiées sont égales entre elles, et lève une exception
            si les deux collections ne sont pas égales entre elles. L'égalité est définie quand il existe les mêmes
            éléments dans le même ordre et en même quantité. Des références différentes à la même
            valeur sont considérées comme égales entre elles.
            </summary>
            <param name="expected">
            Première collection à comparer. Collection attendue par les tests.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par le
            code testé.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Teste si les collections spécifiées sont égales entre elles, et lève une exception
            si les deux collections ne sont pas égales entre elles. L'égalité est définie quand il existe les mêmes
            éléments dans le même ordre et en même quantité. Des références différentes à la même
            valeur sont considérées comme égales entre elles.
            </summary>
            <param name="expected">
            Première collection à comparer. Collection attendue par les tests.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par le
            code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            n'est pas égal à <paramref name="expected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Teste si les collections spécifiées sont égales entre elles, et lève une exception
            si les deux collections ne sont pas égales entre elles. L'égalité est définie quand il existe les mêmes
            éléments dans le même ordre et en même quantité. Des références différentes à la même
            valeur sont considérées comme égales entre elles.
            </summary>
            <param name="expected">
            Première collection à comparer. Collection attendue par les tests.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par le
            code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            n'est pas égal à <paramref name="expected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Teste si les collections spécifiées sont différentes, et lève une exception
            si les deux collections sont égales entre elles. L'égalité est définie quand il existe les mêmes
            éléments dans le même ordre et en même quantité. Des références différentes à la même
            valeur sont considérées comme égales entre elles.
            </summary>
            <param name="notExpected">
            Première collection à comparer. Collection à laquelle les tests sont censés
            ne pas correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par le
            code testé.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            Teste si les collections spécifiées sont différentes, et lève une exception
            si les deux collections sont égales entre elles. L'égalité est définie quand il existe les mêmes
            éléments dans le même ordre et en même quantité. Des références différentes à la même
            valeur sont considérées comme égales entre elles.
            </summary>
            <param name="notExpected">
            Première collection à comparer. Collection à laquelle les tests sont censés
            ne pas correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par le
            code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est égal à <paramref name="notExpected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            Teste si les collections spécifiées sont différentes, et lève une exception
            si les deux collections sont égales entre elles. L'égalité est définie quand il existe les mêmes
            éléments dans le même ordre et en même quantité. Des références différentes à la même
            valeur sont considérées comme égales entre elles.
            </summary>
            <param name="notExpected">
            Première collection à comparer. Collection à laquelle les tests sont censés
            ne pas correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par le
            code testé.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est égal à <paramref name="notExpected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Teste si les collections spécifiées sont égales entre elles, et lève une exception
            si les deux collections ne sont pas égales entre elles. L'égalité est définie quand il existe les mêmes
            éléments dans le même ordre et en même quantité. Des références différentes à la même
            valeur sont considérées comme égales entre elles.
            </summary>
            <param name="expected">
            Première collection à comparer. Collection attendue par les tests.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par le
            code testé.
            </param>
            <param name="comparer">
            Implémentation de comparaison à utiliser durant la comparaison d'éléments de la collection.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Teste si les collections spécifiées sont égales entre elles, et lève une exception
            si les deux collections ne sont pas égales entre elles. L'égalité est définie quand il existe les mêmes
            éléments dans le même ordre et en même quantité. Des références différentes à la même
            valeur sont considérées comme égales entre elles.
            </summary>
            <param name="expected">
            Première collection à comparer. Collection attendue par les tests.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par le
            code testé.
            </param>
            <param name="comparer">
            Implémentation de comparaison à utiliser durant la comparaison d'éléments de la collection.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            n'est pas égal à <paramref name="expected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Teste si les collections spécifiées sont égales entre elles, et lève une exception
            si les deux collections ne sont pas égales entre elles. L'égalité est définie quand il existe les mêmes
            éléments dans le même ordre et en même quantité. Des références différentes à la même
            valeur sont considérées comme égales entre elles.
            </summary>
            <param name="expected">
            Première collection à comparer. Collection attendue par les tests.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par le
            code testé.
            </param>
            <param name="comparer">
            Implémentation de comparaison à utiliser durant la comparaison d'éléments de la collection.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            n'est pas égal à <paramref name="expected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            Teste si les collections spécifiées sont différentes, et lève une exception
            si les deux collections sont égales entre elles. L'égalité est définie quand il existe les mêmes
            éléments dans le même ordre et en même quantité. Des références différentes à la même
            valeur sont considérées comme égales entre elles.
            </summary>
            <param name="notExpected">
            Première collection à comparer. Collection à laquelle les tests sont censés
            ne pas correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par le
            code testé.
            </param>
            <param name="comparer">
            Implémentation de comparaison à utiliser durant la comparaison d'éléments de la collection.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            Teste si les collections spécifiées sont différentes, et lève une exception
            si les deux collections sont égales entre elles. L'égalité est définie quand il existe les mêmes
            éléments dans le même ordre et en même quantité. Des références différentes à la même
            valeur sont considérées comme égales entre elles.
            </summary>
            <param name="notExpected">
            Première collection à comparer. Collection à laquelle les tests sont censés
            ne pas correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par le
            code testé.
            </param>
            <param name="comparer">
            Implémentation de comparaison à utiliser durant la comparaison d'éléments de la collection.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est égal à <paramref name="notExpected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            Teste si les collections spécifiées sont différentes, et lève une exception
            si les deux collections sont égales entre elles. L'égalité est définie quand il existe les mêmes
            éléments dans le même ordre et en même quantité. Des références différentes à la même
            valeur sont considérées comme égales entre elles.
            </summary>
            <param name="notExpected">
            Première collection à comparer. Collection à laquelle les tests sont censés
            ne pas correspondre <paramref name="actual"/>.
            </param>
            <param name="actual">
            Seconde collection à comparer. Il s'agit de la collection produite par le
            code testé.
            </param>
            <param name="comparer">
            Implémentation de comparaison à utiliser durant la comparaison d'éléments de la collection.
            </param>
            <param name="message">
            Message à inclure dans l'exception quand <paramref name="actual"/>
            est égal à <paramref name="notExpected"/>. Le message s'affiche dans
            les résultats des tests.
            </param>
            <param name="parameters">
            Tableau de paramètres à utiliser pour la mise en forme de <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOfHelper(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            Détermine si la première collection est un sous-ensemble de la seconde
            collection. Si l'un des deux ensembles contient des éléments dupliqués, le nombre
            d'occurrences de l'élément dans le sous-ensemble doit être inférieur ou
            égal au nombre d'occurrences dans le sur-ensemble.
            </summary>
            <param name="subset">
            Collection dans laquelle le test est censé être contenu <paramref name="superset"/>.
            </param>
            <param name="superset">
            Collection que le test est censé contenir <paramref name="subset"/>.
            </param>
            <returns>
            True si <paramref name="subset"/> est un sous-ensemble de
            <paramref name="superset"/>, sinon false.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.GetElementCounts(System.Collections.ICollection,System.Int32@)">
            <summary>
            Construit un dictionnaire contenant le nombre d'occurrences de chaque
            élément dans la collection spécifiée.
            </summary>
            <param name="collection">
            Collection à traiter.
            </param>
            <param name="nullCount">
            Nombre d'éléments de valeur null dans la collection.
            </param>
            <returns>
            Dictionnaire contenant le nombre d'occurrences de chaque élément
            dans la collection spécifiée.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.FindMismatchedElement(System.Collections.ICollection,System.Collections.ICollection,System.Int32@,System.Int32@,System.Object@)">
            <summary>
            Recherche un élément incompatible parmi les deux collections. Un élément incompatible
            est un élément qui n'apparaît pas avec la même fréquence dans la
            collection attendue et dans la collection réelle. Les
            collections sont supposées être des références non null distinctes ayant le
            même nombre d'éléments. L'appelant est responsable de ce niveau de
            vérification. S'il n'existe aucun élément incompatible, la fonction retourne
            la valeur false et les paramètres out ne doivent pas être utilisés.
            </summary>
            <param name="expected">
            Première collection à comparer.
            </param>
            <param name="actual">
            Seconde collection à comparer.
            </param>
            <param name="expectedCount">
            Nombre attendu d'occurrences de
            <paramref name="mismatchedElement"/> ou 0, s'il n'y a aucune incompatibilité
            des éléments.
            </param>
            <param name="actualCount">
            Nombre réel d'occurrences de
            <paramref name="mismatchedElement"/> ou 0, s'il n'y a aucune incompatibilité
            des éléments.
            </param>
            <param name="mismatchedElement">
            Élément incompatible (pouvant avoir une valeur null), ou valeur null s'il n'existe aucun
            élément incompatible.
            </param>
            <returns>
            true si un élément incompatible est trouvé ; sinon, false.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.ObjectComparer">
            <summary>
            compare les objets via object.Equals
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException">
            <summary>
            Classe de base pour les exceptions de framework.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String,System.Exception)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
            <param name="msg"> Message. </param>
            <param name="ex"> Exception. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/>.
            </summary>
            <param name="msg"> Message. </param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages">
            <summary>
              Une classe de ressource fortement typée destinée, entre autres, à la consultation des chaînes localisées.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ResourceManager">
            <summary>
              Retourne l'instance ResourceManager mise en cache utilisée par cette classe.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Culture">
            <summary>
              Remplace la propriété CurrentUICulture du thread actuel pour toutes
              les recherches de ressources à l'aide de cette classe de ressource fortement typée.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AccessStringInvalidSyntax">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : La chaîne Access comporte une syntaxe non valide.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ActualHasMismatchedElements">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : La collection attendue contient {1} occurrence(s) de &lt;{2}&gt;. La collection réelle contient {3} occurrence(s). {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AllItemsAreUniqueFailMsg">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Un élément dupliqué a été trouvé : &lt;{1}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualCaseFailMsg">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Attendu : &lt;{1}&gt;. La casse est différente pour la valeur réelle : &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDeltaFailMsg">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Différence attendue non supérieure à &lt;{3}&gt; comprise entre la valeur attendue &lt;{1}&gt; et la valeur réelle &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDifferentTypesFailMsg">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Attendu : &lt;{1} ({2})&gt;. Réel : &lt;{3} ({4})&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualFailMsg">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Attendu : &lt;{1}&gt;. Réel : &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualDeltaFailMsg">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Différence attendue supérieure à &lt;{3}&gt; comprise entre la valeur attendue &lt;{1}&gt; et la valeur réelle &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualFailMsg">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Toute valeur attendue sauf : &lt;{1}&gt;. Réel : &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreSameGivenValues">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Ne passez pas de types valeur à AreSame(). Les valeurs converties en Object ne seront plus jamais les mêmes. Si possible, utilisez AreEqual(). {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AssertionFailed">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Échec de {0}. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AsyncUITestMethodNotSupported">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : async TestMethod utilisé avec UITestMethodAttribute n'est pas pris en charge. Supprimez async ou utilisez TestMethodAttribute.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsEmpty">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Les deux collections sont vides. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameElements">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Les deux collections contiennent des éléments identiques.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameReference">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Les deux collections Reference pointent vers le même objet Collection. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothSameElements">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Les deux collections contiennent les mêmes éléments. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.CollectionEqualReason">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : {0}({1}).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_NullInMessages">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : (null).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_ObjectString">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : (objet).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ContainsFail">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : La chaîne '{0}' ne contient pas la chaîne '{1}'. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DataDrivenResultDisplayName">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : {0} ({1}).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DoNotUseAssertEquals">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Assert.Equals ne doit pas être utilisé pour les assertions. Utilisez Assert.AreEqual et des surcharges à la place.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementNumbersDontMatch">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Le nombre d'éléments dans les collections ne correspond pas. Attendu : &lt;{1}&gt;. Réel : &lt;{2}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementsAtIndexDontMatch">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Les éléments à l'index {0} ne correspondent pas.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : L'élément à l'index {1} n'est pas du type attendu. Type attendu : &lt;{2}&gt;. Type réel : &lt;{3}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch2">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : L'élément à l'index {1} est (null). Type attendu : &lt;{2}&gt;.{0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EndsWithFail">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : La chaîne '{0}' ne se termine pas par la chaîne '{1}'. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EqualsTesterInvalidArgs">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Argument non valide - EqualsTester ne peut pas utiliser de valeurs null.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ErrorInvalidCast">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Impossible de convertir un objet de type {0} en {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InternalObjectNotValid">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : L'objet interne référencé n'est plus valide.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidParameterToAssert">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Le paramètre '{0}' est non valide. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidPropertyType">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : La propriété {0} a le type {1} ; type attendu {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsInstanceOfFailMsg">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : {0} Type attendu : &lt;{1}&gt;. Type réel : &lt;{2}&gt;.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsMatchFail">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : La chaîne '{0}' ne correspond pas au modèle '{1}'. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotInstanceOfFailMsg">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Type incorrect : &lt;{1}&gt;. Type réel : &lt;{2}&gt;. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotMatchFail">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : La chaîne '{0}' correspond au modèle '{1}'. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoDataRow">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Aucun DataRowAttribute spécifié. Au moins un DataRowAttribute est nécessaire avec DataTestMethodAttribute.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoExceptionThrown">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Aucune exception levée. Exception {1} attendue. {0}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NullParameterToAssert">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Le paramètre '{0}' est non valide. La valeur ne peut pas être null. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NumberOfElementsDiff">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Nombre d'éléments différent.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorConstructorNotFound">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : 
                 Le constructeur doté de la signature spécifiée est introuvable. Vous devrez peut-être régénérer votre accesseur private,
                 ou le membre est peut-être private et défini sur une classe de base. Si le dernier cas est vrai, vous devez transmettre le type
                 qui définit le membre dans le constructeur de PrivateObject.
               .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorMemberNotFound">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : 
                 Le membre spécifié ({0}) est introuvable. Vous devrez peut-être régénérer votre accesseur private,
                 ou le membre est peut-être private et défini sur une classe de base. Si le dernier cas est vrai, vous devez transmettre le type
                 qui définit le membre dans le constructeur de PrivateObject.
               .
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.StartsWithFail">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : La chaîne '{0}' ne commence pas par la chaîne '{1}'. {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_ExpectedExceptionTypeMustDeriveFromException">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : Le type de l'exception attendue doit être System.Exception ou un type dérivé de System.Exception.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_FailedToGetExceptionMessage">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : (Échec de la réception du message pour une exception de type {0} en raison d'une exception.).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoException">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : La méthode de test n'a pas levé l'exception attendue {0}. {1}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoExceptionDefault">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : La méthode de test n'a pas levé d'exception. Une exception était attendue par l'attribut {0} défini sur la méthode de test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongException">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : La méthode de test a levé l'exception {0}, mais l'exception {1} était attendue. Message d'exception : {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongExceptionDerivedAllowed">
            <summary>
              Recherche une chaîne localisée semblable à celle-ci : La méthode de test a levé l'exception {0}, mais l'exception {1} (ou un type dérivé de cette dernière) était attendue. Message d'exception : {2}.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.WrongExceptionThrown">
             <summary>
               Recherche une chaîne localisée semblable à celle-ci : L'exception {2} a été levée, mais l'exception {1} était attendue. {0}
            Message d'exception : {3}
            Arborescence des appels de procédure : {4}.
             </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome">
            <summary>
            résultats du test unitaire
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Failed">
            <summary>
            Le test a été exécuté mais des problèmes se sont produits.
            Il peut s'agir de problèmes liés à des exceptions ou des échecs d'assertion.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Inconclusive">
            <summary>
            Test effectué, mais nous ne pouvons pas dire s'il s'agit d'une réussite ou d'un échec.
            Utilisable éventuellement pour les tests abandonnés.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Passed">
            <summary>
            Le test a été exécuté sans problème.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.InProgress">
            <summary>
            Le test est en cours d'exécution.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Error">
            <summary>
            Une erreur système s'est produite pendant que nous tentions d'exécuter un test.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Timeout">
            <summary>
            Délai d'expiration du test.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Aborted">
            <summary>
            Test abandonné par l'utilisateur.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Unknown">
            <summary>
            Le test est dans un état inconnu
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper">
            <summary>
            Fournit une fonctionnalité d'assistance pour le framework de tests unitaires
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper.GetExceptionMsg(System.Exception)">
            <summary>
            Obtient les messages d'exception, notamment les messages de toutes les exceptions internes
            de manière récursive
            </summary>
            <param name="ex">Exception pour laquelle les messages sont obtenus</param>
            <returns>chaîne avec les informations du message d'erreur</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout">
            <summary>
            Énumération des délais d'expiration, qui peut être utilisée avec la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/>.
            Le type de l'énumération doit correspondre
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout.Infinite">
            <summary>
            Infini.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute">
            <summary>
            Attribut de la classe de test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute.GetTestMethodAttribute(Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute)">
            <summary>
            Obtient un attribut de méthode de test qui permet d'exécuter ce test.
            </summary>
            <param name="testMethodAttribute">Instance d'attribut de méthode de test définie sur cette méthode.</param>
            <returns>Le <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/> à utiliser pour exécuter ce test.</returns>
            <remarks>Extensions can override this method to customize how all methods in a class are run.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute">
            <summary>
            Attribut de la méthode de test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Exécute une méthode de test.
            </summary>
            <param name="testMethod">Méthode de test à exécuter.</param>
            <returns>Tableau d'objets TestResult qui représentent le ou les résultats du test.</returns>
            <remarks>Extensions can override this method to customize running a TestMethod.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestInitializeAttribute">
            <summary>
            Attribut d'initialisation du test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCleanupAttribute">
            <summary>
            Attribut de nettoyage du test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute">
            <summary>
            Attribut ignore.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute">
            <summary>
            Attribut de la propriété de test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.#ctor(System.String,System.String)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute"/>.
            </summary>
            <param name="name">
            Nom.
            </param>
            <param name="value">
            Valeur.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Name">
            <summary>
            Obtient le nom.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Value">
            <summary>
            Obtient la valeur.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute">
            <summary>
            Attribut d'initialisation de la classe.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute">
            <summary>
            Attribut de nettoyage de la classe.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyInitializeAttribute">
            <summary>
            Attribut d'initialisation de l'assembly.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyCleanupAttribute">
            <summary>
            Attribut de nettoyage de l'assembly.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute">
            <summary>
            Propriétaire du test
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.#ctor(System.String)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute"/>.
            </summary>
            <param name="owner">
            Propriétaire.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.Owner">
            <summary>
            Obtient le propriétaire.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute">
            <summary>
            Attribut Priority utilisé pour spécifier la priorité d'un test unitaire.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.#ctor(System.Int32)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute"/>.
            </summary>
            <param name="priority">
            Priorité.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.Priority">
            <summary>
            Obtient la priorité.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute">
            <summary>
            Description du test
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.#ctor(System.String)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute"/> pour décrire un test.
            </summary>
            <param name="description">Description.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.Description">
            <summary>
            Obtient la description d'un test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute">
            <summary>
            URI de structure de projet CSS
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.#ctor(System.String)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute"/> pour l'URI de structure de projet CSS.
            </summary>
            <param name="cssProjectStructure">URI de structure de projet CSS.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.CssProjectStructure">
            <summary>
            Obtient l'URI de structure de projet CSS.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute">
            <summary>
            URI d'itération CSS
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.#ctor(System.String)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute"/> pour l'URI d'itération CSS.
            </summary>
            <param name="cssIteration">URI d'itération CSS.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.CssIteration">
            <summary>
            Obtient l'URI d'itération CSS.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute">
            <summary>
            Attribut WorkItem permettant de spécifier un élément de travail associé à ce test.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.#ctor(System.Int32)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute"/> pour l'attribut WorkItem.
            </summary>
            <param name="id">ID d'un élément de travail.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.Id">
            <summary>
            Obtient l'ID d'un élément de travail associé.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute">
            <summary>
            Attribut Timeout utilisé pour spécifier le délai d'expiration d'un test unitaire.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(System.Int32)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/>.
            </summary>
            <param name="timeout">
            Délai d'expiration.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> avec un délai d'expiration prédéfini
            </summary>
            <param name="timeout">
            Délai d'expiration
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.Timeout">
            <summary>
            Obtient le délai d'attente.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult">
            <summary>
            Objet TestResult à retourner à l'adaptateur.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.#ctor">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DisplayName">
            <summary>
            Obtient ou définit le nom d'affichage du résultat. Utile pour retourner plusieurs résultats.
            En cas de valeur null, le nom de la méthode est utilisé en tant que DisplayName.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Outcome">
            <summary>
            Obtient ou définit le résultat de l'exécution du test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestFailureException">
            <summary>
            Obtient ou définit l'exception levée en cas d'échec du test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogOutput">
            <summary>
            Obtient ou définit la sortie du message journalisé par le code de test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogError">
            <summary>
            Obtient ou définit la sortie du message journalisé par le code de test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DebugTrace">
            <summary>
            Obtient ou définit les traces de débogage du code de test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestContextMessages">
            <summary>
            Gets or sets the debug traces by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Duration">
            <summary>
            Obtient ou définit la durée de l'exécution du test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DatarowIndex">
            <summary>
            Obtient ou définit l'index de ligne de données dans la source de données. Défini uniquement pour les résultats de
            l'exécution individuelle de la ligne de données d'un test piloté par les données.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ReturnValue">
            <summary>
            Obtient ou définit la valeur renvoyée de la méthode de test. (Toujours null).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ResultFiles">
            <summary>
            Obtient ou définit les fichiers de résultats attachés par le test.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute">
            <summary>
            Spécifie la chaîne de connexion, le nom de la table et la méthode d'accès aux lignes pour les tests pilotés par les données.
            </summary>
            <example>
            [DataSource("Provider=SQLOLEDB.1;Data Source=source;Integrated Security=SSPI;Initial Catalog=EqtCoverage;Persist Security Info=False", "MyTable")]
            [DataSource("dataSourceNameFromConfigFile")]
            </example>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultProviderName">
            <summary>
            Nom du fournisseur par défaut de DataSource.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultDataAccessMethod">
            <summary>
            Méthode d'accès aux données par défaut.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String,System.String,Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>. Cette instance va être initialisée avec un fournisseur de données, une chaîne de connexion, une table de données et une méthode d'accès aux données pour accéder à la source de données.
            </summary>
            <param name="providerInvariantName">Nom du fournisseur de données invariant, par exemple System.Data.SqlClient</param>
            <param name="connectionString">
            Chaîne de connexion spécifique au fournisseur de données.
            AVERTISSEMENT : La chaîne de connexion peut contenir des données sensibles (par exemple, un mot de passe).
            La chaîne de connexion est stockée en texte brut dans le code source et dans l'assembly compilé.
            Restreignez l'accès au code source et à l'assembly pour protéger ces informations sensibles.
            </param>
            <param name="tableName">Nom de la table de données.</param>
            <param name="dataAccessMethod">Spécifie l'ordre d'accès aux données.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>. Cette instance va être initialisée avec une chaîne de connexion et un nom de table.
            Spécifiez la chaîne de connexion et la table de données permettant d'accéder à la source de données OLEDB.
            </summary>
            <param name="connectionString">
            Chaîne de connexion spécifique au fournisseur de données.
            AVERTISSEMENT : La chaîne de connexion peut contenir des données sensibles (par exemple, un mot de passe).
            La chaîne de connexion est stockée en texte brut dans le code source et dans l'assembly compilé.
            Restreignez l'accès au code source et à l'assembly pour protéger ces informations sensibles.
            </param>
            <param name="tableName">Nom de la table de données.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>. Cette instance va être initialisée avec un fournisseur de données et une chaîne de connexion associés au nom du paramètre.
            </summary>
            <param name="dataSourceSettingName">Nom d'une source de données trouvée dans la section &lt;microsoft.visualstudio.qualitytools&gt; du fichier app.config.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ProviderInvariantName">
            <summary>
            Obtient une valeur représentant le fournisseur de données de la source de données.
            </summary>
            <returns>
            Nom du fournisseur de données. Si aucun fournisseur de données n'a été désigné au moment de l'initialisation de l'objet, le fournisseur par défaut de System.Data.OleDb est retourné.
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ConnectionString">
            <summary>
            Obtient une valeur représentant la chaîne de connexion de la source de données.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.TableName">
            <summary>
            Obtient une valeur indiquant le nom de la table qui fournit les données.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataAccessMethod">
             <summary>
             Obtient la méthode utilisée pour accéder à la source de données.
             </summary>
            
             <returns>
             Une des valeurs <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod"/> possibles. Si <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> n'est pas initialisé, ce qui entraîne le retour de la valeur par défaut <see cref="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random"/>.
             </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataSourceSettingName">
            <summary>
            Obtient le nom d'une source de données trouvée dans la section &lt;microsoft.visualstudio.qualitytools&gt; du fichier app.config.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute">
            <summary>
            Attribut du test piloté par les données, où les données peuvent être spécifiées inline.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            Recherche toutes les lignes de données et les exécute.
            </summary>
            <param name="testMethod">
            Méthode de test.
            </param>
            <returns>
            Tableau des <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.RunDataDrivenTest(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod,Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute[])">
            <summary>
            Exécute la méthode de test piloté par les données.
            </summary>
            <param name="testMethod"> Méthode de test à exécuter. </param>
            <param name="dataRows"> Ligne de données. </param>
            <returns> Résultats de l'exécution. </returns>
        </member>
    </members>
</doc>
