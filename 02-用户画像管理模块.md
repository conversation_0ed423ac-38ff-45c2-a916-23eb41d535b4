# 用户画像管理模块 - 详细设计

## 一、模块概述

### 1.1 功能定位
用户画像管理模块是智能新闻推荐系统的核心组件，负责通过AI分析用户项目数据，生成个性化的兴趣画像，为新闻推荐提供精准的用户兴趣标签。

### 1.2 核心职责
- AI分析用户项目数据，生成兴趣画像
- 管理用户兴趣标签和权重
- 维护标签与用户的关联关系
- 支持动态更新和实时同步
- **标签级别向量化**：每个兴趣标签独立存储向量表示

## 二、数据模型设计

### 2.1 用户兴趣画像结构
用户兴趣画像包含以下核心信息：
- **基本信息**：用户ID、用户名、兴趣描述
- **技术领域**：AI、区块链、云计算、大数据、物联网等
- **行业领域**：医疗、消费、金融、教育、制造等
- **投资关键词**：体现用户投资偏好和关注点
- **兴趣标签**：具体可操作的标签列表
- **标签权重**：每个标签的重要性评分（0-1）
- **行为数据**：点击统计、最近交互、多样性偏好

### 2.2 兴趣标签结构（核心）
兴趣标签系统包含：
- **标签分类**：技术、行业、投资等不同类别
- **标签属性**：名称、关键词、用户数量、活跃度
- **标签统计**：点击次数、点击率、最后使用时间
- **标签权重**：基于用户行为和AI分析的动态权重
- **标签向量**：每个标签独立存储1024维向量表示

## 三、AI分析流程设计

### 3.1 分析流程概述
```
1. 收集用户最近100个项目数据
2. 构建AI提示词，包含项目详细信息
3. 调用大模型分析用户兴趣标签
4. 解析AI返回的JSON结果，包含标签和权重
5. 自动识别用户所属兴趣标签
6. 更新用户画像和标签关联信息
7. 为新增标签生成向量表示
```

### 3.2 AI提示词设计
AI提示词包含以下要素：
- **项目数据格式化**：项目名称、描述、投资金额、时间、阶段、行业、技术
- **分析要求**：标签权重范围、技术领域分类、行业领域分类
- **输出格式**：JSON结构化输出，便于程序解析
- **质量控制**：避免主观臆测，基于项目数据进行分析

### 3.3 AI结果解析
解析过程包括：
- **JSON解析**：将AI返回的文本转换为结构化数据
- **数据验证**：检查必要字段是否存在
- **降级处理**：解析失败时使用规则引擎生成默认画像
- **数据补充**：为缺失字段提供默认值

## 四、标签管理系统

### 4.1 标签创建和更新
标签管理流程：
- **新标签创建**：AI分析发现新标签时自动创建
- **标签更新**：根据用户行为动态调整标签属性
- **标签关联**：维护标签与用户的关联关系
- **标签统计**：实时更新标签的使用统计
- **标签向量化**：为每个标签生成独立的向量表示

### 4.2 标签权重计算
权重计算考虑以下因素：
- **AI分析权重**：来自大模型分析的初始权重（60%）
- **点击行为权重**：用户点击行为反映的兴趣强度（30%）
- **最近交互权重**：最近交互的标签获得额外权重（10%）
- **权重归一化**：确保最终权重在0-1范围内

### 4.3 标签向量化（核心功能）
每个兴趣标签独立进行向量化处理：

#### 4.3.1 向量生成流程
```
标签文本构建 → Embedding服务调用 → 向量存储 → 缓存更新
```

#### 4.3.2 标签文本构建
```csharp
// 构建标签向量化文本
private string BuildTagVectorText(UserInterestTag tag)
{
    var textBuilder = new System.Text.StringBuilder();
    
    // 主要文本：标签名称
    textBuilder.Append(tag.Name);
    
    // 添加关键词
    if (!string.IsNullOrEmpty(tag.Keywords))
    {
        textBuilder.Append(" ").Append(tag.Keywords);
    }
    
    // 添加分类信息
    if (!string.IsNullOrEmpty(tag.Category))
    {
        textBuilder.Append(" ").Append(tag.Category);
    }
    
    return textBuilder.ToString().Trim();
}
```

#### 4.3.3 向量存储结构
- **向量维度**：1024维
- **存储格式**：逗号分隔的浮点数字符串
- **更新机制**：标签内容变化时自动重新生成向量
- **缓存策略**：向量缓存30天，提高查询性能

## 五、推荐计算架构

### 5.1 标签级别推荐算法
系统采用标签级别的推荐计算，避免用户向量存储：

#### 5.1.1 推荐流程
```
用户请求推荐 → 获取用户标签关联 → 获取标签向量 → 计算相似度 → 混合排序 → 返回推荐
```

#### 5.1.2 核心算法
```csharp
// 基于标签级别的推荐算法
public async Task<List<NewsRecommendation>> GetTagBasedRecommendationsAsync(int userId)
{
    var userTagRelations = await GetUserTagRelationsAsync(userId);
    var recommendations = new List<NewsRecommendation>();
    
    foreach (var news in allNews)
    {
        var score = await CalculateTagBasedScoreAsync(userTagRelations, news);
        if (score > 0.3) // 推荐阈值
        {
            recommendations.Add(new NewsRecommendation
            {
                NewsId = news.Id,
                Score = score,
                RecommendationReason = $"基于用户兴趣标签匹配，相似度: {score:F2}"
            });
        }
    }
    
    return recommendations.OrderByDescending(r => r.Score).Take(10).ToList();
}
```

### 5.2 标签匹配评分计算
```csharp
// 计算标签匹配分数
private async Task<double> CalculateTagBasedScoreAsync(List<UserTagRelation> userTagRelations, News news)
{
    var totalScore = 0.0;
    var totalWeight = 0.0;
    
    foreach (var relation in userTagRelations)
    {
        var tagVector = await GetTagVectorAsync(relation.TagId);
        var newsVector = await GetNewsVectorAsync(news.Id);
        
        if (tagVector != null && newsVector != null)
        {
            var similarity = CalculateCosineSimilarity(tagVector, newsVector);
            var weightedScore = similarity * relation.Weight;
            
            totalScore += weightedScore;
            totalWeight += relation.Weight;
        }
    }
    
    return totalWeight > 0 ? totalScore / totalWeight : 0.0;
}
```

## 六、更新机制设计

### 6.1 触发更新条件
用户画像更新在以下情况触发：
- **新增项目**：用户添加新项目时
- **画像过期**：超过7天未更新时
- **手动触发**：管理员手动标记需要更新
- **行为变化**：用户行为发生显著变化时

### 6.2 标签向量更新
标签向量更新策略：
- **新标签创建**：创建标签时自动生成向量
- **标签内容变化**：标签名称或关键词变化时重新生成向量
- **批量更新**：支持批量更新过期或无效的标签向量
- **缓存同步**：向量更新时同步更新缓存

## 七、数据库设计

### 7.1 核心数据表
- **用户画像表**：存储用户兴趣画像的基本信息
- **兴趣标签表**：管理所有兴趣标签及其向量表示
- **用户标签关联表**：维护用户与标签的关联关系
- **标签分类表**：管理标签的分类体系

### 7.2 索引设计
为提高查询性能，建立以下索引：
- 用户画像表的更新时间索引
- 兴趣标签表的分类和活跃度索引
- 用户标签关联表的用户ID和标签ID索引

## 八、缓存策略

### 8.1 Redis缓存设计
缓存策略包括：
- **标签向量缓存**：缓存30天，标签向量相对稳定
- **用户标签缓存**：缓存7天，标签关联变化较慢
- **标签用户缓存**：缓存7天，用户标签关系
- **缓存更新**：标签向量更新时同步更新相关缓存

### 8.2 缓存键设计
采用统一的缓存键命名规范：
- 标签向量：`tag_vector:{tagId}`
- 用户标签：`user_tags:{userId}`
- 标签用户：`tag_users:{tagId}`

## 九、性能优化

### 9.1 AI调用优化
优化策略包括：
- **批量处理**：合并多个用户数据为一次AI调用
- **智能缓存**：缓存AI分析结果，避免重复分析
- **降级机制**：AI服务失败时使用规则引擎
- **并发控制**：限制并发AI调用数量

### 9.2 向量计算优化
向量计算优化措施：
- **标签向量缓存**：缓存标签向量，避免重复计算
- **批量向量获取**：批量获取多个标签向量
- **相似度计算优化**：优化余弦相似度计算算法
- **内存管理**：优化向量数据的内存使用

## 十、监控和统计

### 10.1 关键指标监控
监控指标包括：
- **用户画像覆盖率**：有画像的用户比例
- **平均标签数量**：每个用户的平均标签数
- **热门标签统计**：使用频率最高的标签
- **画像更新成功率**：AI分析的成功率
- **标签向量覆盖率**：有向量的标签比例

### 10.2 标签统计分析
标签统计包括：
- **标签总数和活跃标签数**
- **按分类的标签分布**
- **最受欢迎的标签排名**
- **标签点击率和用户分布**
- **标签向量生成成功率**

## 十一、错误处理和降级

### 11.1 AI服务降级
当AI服务不可用时：
- **规则引擎**：基于用户历史行为的简单分析
- **默认画像**：为所有用户提供基础画像
- **标签提取**：从项目数据中直接提取标签
- **权重计算**：基于项目数量计算简单权重

### 11.2 向量服务降级
当向量服务不可用时：
- **标签匹配**：使用标签名称的文本匹配
- **关键词匹配**：使用标签关键词进行匹配
- **缓存回退**：使用缓存的向量数据
- **服务恢复**：服务恢复后的数据同步

## 十二、扩展性设计

### 12.1 用户规模扩展
支持更多用户的策略：
- **标签体系扩展**：增加更多细分类别的标签
- **AI批量优化**：优化批量处理算法
- **缓存策略增强**：采用分布式缓存
- **数据库分片**：按用户ID进行数据分片

### 12.2 功能扩展
未来功能扩展方向：
- **个性化权重**：支持用户自定义标签权重
- **兴趣演化**：跟踪用户兴趣的变化趋势
- **社交影响**：考虑用户社交网络的影响
- **多维度画像**：增加更多维度的用户特征
- **标签相似度**：基于标签向量的相似标签推荐 