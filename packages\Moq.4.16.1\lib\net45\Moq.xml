<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Moq</name>
    </assembly>
    <members>
        <member name="T:Moq.ActionObserver">
            <summary>
              <see cref="T:Moq.ActionObserver"/> is a kind of <see cref="T:Moq.ExpressionReconstructor"/> that works by
              applying a <see cref="T:System.Action`1"/> delegate to a light-weight proxy that records the invocation
              happening to it, and auto-generates the same kind of recording proxy for its return value.
              That way, a chain of invocation records is generated from which a LINQ expression tree can be
              reconstructed.
            </summary>
        </member>
        <member name="M:Moq.Async.Awaitable.TryGetResultRecursive(System.Object)">
            <summary>
              Recursively gets the result of (i.e. "unwraps") completed awaitables
              until a value is found that isn't a successfully completed awaitable.
            </summary>
            <remarks>
              As an example, given <paramref name="obj"/> := <c>Task.FromResult(Task.FromResult(42))</c>,
              this method will return <c>42</c>.
            </remarks>
            <param name="obj">The (possibly awaitable) object to be "unwrapped".</param>
        </member>
        <member name="T:Moq.Async.AwaitableFactory`1">
            <summary>
              Abstract base class that facilitates type-safe implementation of <see cref="T:Moq.Async.IAwaitableFactory"/>
              for awaitables that do not produce a result when awaited.
            </summary>
        </member>
        <member name="T:Moq.Async.AwaitableFactory`2">
            <summary>
              Abstract base class that facilitates type-safe implementation of <see cref="T:Moq.Async.IAwaitableFactory"/>
              for awaitables that produce a result when awaited.
            </summary>
        </member>
        <member name="T:Moq.Capture">
            <summary>
            Allows to create parameter captures in setup expressions.
            </summary>
        </member>
        <member name="M:Moq.Capture.In``1(System.Collections.Generic.ICollection{``0})">
            <summary>
            Creates a parameter capture that will store values in a collection.
            </summary>
            <typeparam name="T">The captured object type</typeparam>
            <param name="collection">The collection that will store captured parameter values</param>
            <example>
            Arrange code:
            <code>
            var parameters = new List{string}();
            mock.Setup(x => x.DoSomething(Capture.In(parameters)));
            </code>
            Assert code:
            <code>
            Assert.Equal("Hello!", parameters.Single());
            </code>
            </example>
        </member>
        <member name="M:Moq.Capture.In``1(System.Collections.Generic.IList{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            Creates a parameter capture that will store specific values in a collection.
            </summary>
            <typeparam name="T">The captured object type</typeparam>
            <param name="collection">The collection that will store captured parameter values</param>
            <param name="predicate">A predicate used to filter captured parameters</param>
            <example>
            Arrange code:
            <code>
            var parameters = new List{string}();
            mock.Setup(x => x.DoSomething(Capture.In(parameters, p => p.StartsWith("W"))));
            </code>
            Assert code:
            <code>
            Assert.Equal("Hello!", parameters.Single());
            </code>
            </example>
        </member>
        <member name="M:Moq.Capture.With``1(Moq.CaptureMatch{``0})">
            <summary>
            Creates a parameter capture using specified <see cref="T:Moq.CaptureMatch`1"/>.
            </summary>
            <typeparam name="T">The captured object type</typeparam>
            <example>
            Arrange code:
            <code>
            var capturedValue = string.Empty;
            var match = new CaptureMatch{string}(x => capturedValue = x);
            mock.Setup(x => x.DoSomething(Capture.With(match)));
            </code>
            Assert code:
            <code>
            Assert.Equal("Hello!", capturedValue);
            </code>
            </example>
        </member>
        <member name="T:Moq.CaptureMatch`1">
            <summary>
            Allows creation custom matchers that can be used on setups to capture parameter values.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:Moq.CaptureMatch`1.#ctor(System.Action{`0})">
            <summary>
            Initializes an instance of the capture match.
            </summary>
            <param name="captureCallback">An action to run on captured value</param>
        </member>
        <member name="M:Moq.CaptureMatch`1.#ctor(System.Action{`0},System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Initializes an instance of the capture match.
            </summary>
            <param name="captureCallback">An action to run on captured value</param>
            <param name="predicate">A predicate used to filter captured parameters</param>
        </member>
        <member name="T:Moq.DefaultValue">
            <summary>
            Determines the way default values are generated 
            calculated for loose mocks.
            </summary>
        </member>
        <member name="F:Moq.DefaultValue.Empty">
            <summary>
            Default behavior, which generates empty values for 
            value types (i.e. default(int)), empty array and 
            enumerables, and nulls for all other reference types.
            </summary>
        </member>
        <member name="F:Moq.DefaultValue.Mock">
            <summary>
            Whenever the default value generated by <see cref="F:Moq.DefaultValue.Empty"/> 
            is null, replaces this value with a mock (if the type 
            can be mocked). 
            </summary>
            <remarks>
            For sealed classes, a null value will be generated.
            </remarks>
        </member>
        <member name="F:Moq.DefaultValue.Custom">
            <summary>
              <para>
                All default value generation strategies other than <see cref="F:Moq.DefaultValue.Empty"/> or <see cref="F:Moq.DefaultValue.Mock"/>
                are represented by this enumeration value.
              </para>
              <para>
                Do not set <see cref="P:Moq.Mock.DefaultValue"/> (nor <see cref="P:Moq.MockFactory.DefaultValue"/>) to this value.
                If you want to set up a custom default value generation strategy, set <see cref="P:Moq.Mock.DefaultValueProvider"/>
                or <see cref="P:Moq.MockFactory.DefaultValueProvider"/> instead.
              </para>
            </summary>
            <remarks>
            </remarks>
        </member>
        <member name="T:Moq.DefaultValueProvider">
            <summary>
            <see cref="T:Moq.DefaultValueProvider"/> is the abstract base class for default value providers.
            These are responsible for producing e. g. return values when mock methods or properties get invoked unexpectedly.
            In other words, whenever there is no setup that would determine the return value for a particular invocation,
            Moq asks the mock's default value provider to produce a return value.
            </summary>
        </member>
        <member name="P:Moq.DefaultValueProvider.Empty">
            <summary>
            Gets the <see cref="T:Moq.DefaultValueProvider"/> corresponding to <see cref="F:Moq.DefaultValue.Empty"/>;
            that is, a default value provider returning "empty" values e. g. for collection types.
            </summary>
        </member>
        <member name="P:Moq.DefaultValueProvider.Mock">
            <summary>
            Gets the <see cref="T:Moq.DefaultValueProvider"/> corresponding to <see cref="F:Moq.DefaultValue.Mock"/>;
            that is, a default value provider returning mocked objects or "empty" values for unmockable types.
            </summary>
        </member>
        <member name="M:Moq.DefaultValueProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Moq.DefaultValueProvider"/> class.
            </summary>
        </member>
        <member name="P:Moq.DefaultValueProvider.Kind">
            <summary>
            Gets the <see cref="T:Moq.DefaultValue"/> enumeration value that corresponds to this default value provider.
            Must be overridden by Moq's internal providers that have their own corresponding <see cref="T:Moq.DefaultValue"/>.
            </summary>
        </member>
        <member name="M:Moq.DefaultValueProvider.GetDefaultValue(System.Type,Moq.Mock)">
            <summary>
            Produces a default value of the specified type.
            Must be overridden in derived classes.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> of the requested default value.</param>
            <param name="mock">The <see cref="T:Moq.Mock"/> on which an unexpected invocation has occurred.</param>
            <remarks>
            Implementations may assume that all parameters have valid, non-<see langword="null"/>, non-<see langword="void"/> values.
            </remarks>
        </member>
        <member name="M:Moq.DefaultValueProvider.GetDefaultParameterValue(System.Reflection.ParameterInfo,Moq.Mock)">
            <summary>
              <para>
                Produces a default argument value for the specified method parameter.
                May be overridden in derived classes.
              </para>
              <para>
                By default, this method will delegate to <see cref="M:Moq.DefaultValueProvider.GetDefaultValue(System.Type,Moq.Mock)"/>.
              </para>
            </summary>
            <param name="parameter">The <see cref="T:System.Reflection.ParameterInfo"/> describing the method parameter for which a default argument value should be produced.</param>
            <param name="mock">The <see cref="T:Moq.Mock"/> on which an unexpected invocation has occurred.</param>
            <remarks>
            Implementations may assume that all parameters have valid, non-<see langword="null"/>, non-<see langword="void"/> values.
            </remarks>
        </member>
        <member name="M:Moq.DefaultValueProvider.GetDefaultReturnValue(System.Reflection.MethodInfo,Moq.Mock)">
            <summary>
              <para>
                Produces a default return value for the specified method.
                May be overridden in derived classes.
              </para>
              <para>
                By default, this method will delegate to <see cref="M:Moq.DefaultValueProvider.GetDefaultValue(System.Type,Moq.Mock)"/>.
              </para>
            </summary>
            <param name="method">The <see cref="T:System.Reflection.MethodInfo"/> describing the method for which a default return value should be produced.</param>
            <param name="mock">The <see cref="T:Moq.Mock"/> on which an unexpected invocation has occurred.</param>
            <remarks>
            Implementations may assume that all parameters have valid, non-<see langword="null"/>, non-<see langword="void"/> values.
            </remarks>
        </member>
        <member name="T:Moq.EmptyDefaultValueProvider">
            <summary>
            A <see cref="T:Moq.DefaultValueProvider"/> that returns an empty default value 
            for invocations that do not have setups or return values, with loose mocks.
            This is the default behavior for a mock.
            </summary>
        </member>
        <member name="T:Moq.Evaluator">
            <summary>
            Provides partial evaluation of subtrees, whenever they can be evaluated locally.
            </summary>
            <author>Matt Warren: http://blogs.msdn.com/mattwar</author>
            <contributor>Documented by InSTEDD: http://www.instedd.org</contributor>
        </member>
        <member name="M:Moq.Evaluator.PartialEval(System.Linq.Expressions.Expression,System.Func{System.Linq.Expressions.Expression,System.Boolean})">
            <summary>
            Performs evaluation and replacement of independent sub-trees
            </summary>
            <param name="expression">The root of the expression tree.</param>
            <param name="fnCanBeEvaluated">A function that decides whether a given expression
            node can be part of the local function.</param>
            <returns>A new tree with sub-trees evaluated and replaced.</returns>
        </member>
        <member name="M:Moq.Evaluator.PartialEval(System.Linq.Expressions.Expression)">
            <summary>
            Performs evaluation and replacement of independent sub-trees
            </summary>
            <param name="expression">The root of the expression tree.</param>
            <returns>A new tree with sub-trees evaluated and replaced.</returns>
        </member>
        <member name="T:Moq.Evaluator.SubtreeEvaluator">
            <summary>
            Evaluates and replaces sub-trees when first candidate is reached (top-down)
            </summary>
        </member>
        <member name="T:Moq.Evaluator.Nominator">
            <summary>
            Performs bottom-up analysis to determine which nodes can possibly
            be part of an evaluated sub-tree.
            </summary>
        </member>
        <member name="T:Moq.ExpressionCompiler">
            <summary>
              An <see cref="T:Moq.ExpressionCompiler"/> compiles LINQ expression trees (<see cref="T:System.Linq.Expressions.Expression"/>) to delegates.
              Whenever Moq needs to compile an expression tree, it uses the instance set up by <see cref="P:Moq.ExpressionCompiler.Instance"/>.
            </summary>
        </member>
        <member name="P:Moq.ExpressionCompiler.Default">
            <summary>
              The default <see cref="T:Moq.ExpressionCompiler"/> instance, which simply delegates to the framework's <see cref="M:System.Linq.Expressions.LambdaExpression.Compile"/>.
            </summary>
        </member>
        <member name="P:Moq.ExpressionCompiler.Instance">
            <summary>
              Gets or sets the <see cref="T:Moq.ExpressionCompiler"/> instance that Moq uses to compile <see cref="T:System.Linq.Expressions.Expression"/> (LINQ expression trees).
              Defaults to <see cref="P:Moq.ExpressionCompiler.Default"/>.
            </summary>
        </member>
        <member name="M:Moq.ExpressionCompiler.#ctor">
            <summary>
              Initializes a new instance of the <see cref="T:Moq.ExpressionCompiler"/> class.
            </summary>
        </member>
        <member name="M:Moq.ExpressionCompiler.Compile(System.Linq.Expressions.LambdaExpression)">
            <summary>
              Compiles the specified LINQ expression tree.
            </summary>
            <param name="expression">The LINQ expression tree that should be compiled.</param>
        </member>
        <member name="M:Moq.ExpressionCompiler.Compile``1(System.Linq.Expressions.Expression{``0})">
            <summary>
              Compiles the specified LINQ expression tree.
            </summary>
            <typeparam name="TDelegate">The type of delegate to which the expression will be compiled.</typeparam>
            <param name="expression">The LINQ expression tree that should be compiled.</param>
        </member>
        <member name="M:Moq.ExpressionExtensions.ConvertIfNeeded(System.Linq.Expressions.Expression,System.Type)">
            <summary>
              Wraps this <paramref name="expression"/> in a <see cref="F:System.Linq.Expressions.ExpressionType.Convert"/> node if needed.
            </summary>
            <param name="expression">The <see cref="T:System.Linq.Expressions.Expression"/> which should be wrapped.</param>
            <param name="type">The <see cref="T:System.Type"/> with which to make the <paramref name="expression"/> compatible.</param>
            <remarks>
              LINQ expression trees generally enforce type compatibility rules that are stricter than
              the assignment-compatibility used by e.g. <see cref="M:System.Type.IsAssignableFrom(System.Type)"/>. For
              example, while <see langword="int"/> is assignable-to <see langword="object"/>, you
              will need a conversion in a LINQ expression tree to model the value-type boxing operation.
            </remarks>
        </member>
        <member name="M:Moq.ExpressionExtensions.CanSplit(System.Linq.Expressions.Expression)">
            <summary>
              Checks whether the given expression <paramref name="e"/> can be split by <see cref="M:Moq.ExpressionExtensions.Split(System.Linq.Expressions.LambdaExpression,System.Boolean)"/>.
            </summary>
        </member>
        <member name="M:Moq.ExpressionExtensions.Split(System.Linq.Expressions.LambdaExpression,System.Boolean)">
            <summary>
              Splits an expression such as `<c>m => m.A.B(x).C[y] = z</c>` into a chain of parts
              that can be set up one at a time:
              <list>
                <item>`<c>m => m.A</c>`</item>,
                <item>`<c>... => ....B(x)</c>`</item>,
                <item>`<c>... => ....C</c>`</item>,
                <item>`<c>... => ...[y] = z</c>`</item>.
              </list>
              <para>
                The split points are chosen such that each part has exactly one associated
                <see cref="T:System.Reflection.MethodInfo"/> and optionally some argument expressions.
              </para>
            </summary>
            <exception cref="T:System.ArgumentException">
              It was not possible to completely split up the expression.
            </exception>
        </member>
        <member name="M:Moq.ExpressionExtensions.ToPropertyInfo(System.Linq.Expressions.LambdaExpression)">
            <summary>
            Converts the body of the lambda expression into the <see cref="T:System.Reflection.PropertyInfo"/> referenced by it.
            </summary>
        </member>
        <member name="M:Moq.ExpressionExtensions.IsProperty(System.Linq.Expressions.LambdaExpression)">
            <summary>
            Checks whether the body of the lambda expression is a property access.
            </summary>
        </member>
        <member name="M:Moq.ExpressionExtensions.IsPropertyIndexer(System.Linq.Expressions.LambdaExpression)">
            <summary>
              Checks whether the body of the lambda expression is a indexer access.
            </summary>
        </member>
        <member name="M:Moq.ExpressionExtensions.ToStringFixed(System.Linq.Expressions.Expression)">
            <devdoc>
            TODO: remove this code when https://connect.microsoft.com/VisualStudio/feedback/ViewFeedback.aspx?FeedbackID=331583 
            is fixed.
            </devdoc>
        </member>
        <member name="M:Moq.ExpressionExtensions.Apply(System.Linq.Expressions.Expression,System.Linq.Expressions.ExpressionVisitor)">
            <summary>
              Applies the specified <see cref="T:System.Linq.Expressions.ExpressionVisitor"/> to this expression tree.
            </summary>
            <param name="expression">The <see cref="T:System.Linq.Expressions.Expression"/> to which <paramref name="visitor"/> should be applied.</param>
            <param name="visitor">The <see cref="T:System.Linq.Expressions.ExpressionVisitor"/> that should be applied to <paramref name="expression"/>.</param>
        </member>
        <member name="T:Moq.ExpressionReconstructor">
            <summary>
              A <see cref="T:Moq.ExpressionReconstructor"/> reconstructs LINQ expression trees (<see cref="T:System.Linq.Expressions.LambdaExpression"/>)
              from <see cref="T:System.Action"/> delegates. It is the counterpart to <see cref="T:Moq.ExpressionCompiler"/>.
            </summary>
        </member>
        <member name="M:Moq.ExpressionReconstructor.ReconstructExpression``1(System.Action{``0},System.Object[])">
            <summary>
              Reconstructs a <see cref="T:System.Linq.Expressions.LambdaExpression"/> from the given <see cref="T:System.Action`1"/> delegate.
            </summary>
            <param name="action">The <see cref="T:System.Action"/> delegate for which to reconstruct a LINQ expression tree.</param>
            <param name="ctorArgs">Arguments to pass to a parameterized constructor of <typeparamref name="T"/>. (Optional.)</param>
        </member>
        <member name="M:Moq.Expressions.Visitors.ConstructorCallVisitor.ExtractArgumentValues(System.Linq.Expressions.LambdaExpression)">
            <summary>
            Extracts the arguments from a lambda expression that calls a constructor.
            </summary>
            <param name="newExpression">The constructor expression.</param>
            <returns>Extracted argument values.</returns>
        </member>
        <member name="T:Moq.Expressions.Visitors.EvaluateCaptures">
            <summary>
              Evaluates variables that have been closed over by a lambda function.
            </summary>
        </member>
        <member name="T:Moq.Expressions.Visitors.UpgradePropertyAccessorMethods">
            <summary>
              Replaces <see cref="F:System.Linq.Expressions.ExpressionType.Call"/> nodes for property or indexer accessor methods
              with equivalent <see cref="F:System.Linq.Expressions.ExpressionType.MemberAccess"/> nodes.
              <para>
                <list type="bullet">
                  <item>
                    In the case of getter accessors such as `x.get_Property()`, the result will be
                    a single <see cref="F:System.Linq.Expressions.ExpressionType.MemberAccess"/> node: `x.Property`.
                  </item>
                  <item>
                    In the case of setter accessors such as `x.set_Property(y)`, the result will be
                    a combination of <see cref="F:System.Linq.Expressions.ExpressionType.Assign"/> and <see cref="F:System.Linq.Expressions.ExpressionType.MemberAccess"/>:
                    `x.Property = y`.
                  </item>
                </list>
              </para>
            </summary>
        </member>
        <member name="M:Moq.Extensions.GetDefaultValue(System.Type)">
            <summary>
              Gets the default value for the specified type. This is the Reflection counterpart of C#'s <see langword="default"/> operator.
            </summary>
        </member>
        <member name="M:Moq.Extensions.GetImplementingMethod(System.Reflection.MethodInfo,System.Type)">
            <summary>
              Gets the least-derived <see cref="T:System.Reflection.MethodInfo"/> in the given type that provides
              the implementation for the given <paramref name="method"/>.
            </summary>
        </member>
        <member name="M:Moq.Extensions.IsDelegateType(System.Type)">
            <summary>
              Gets whether the given <paramref name="type"/> is a delegate type.
            </summary>
        </member>
        <member name="M:Moq.Extensions.SubstituteTypeMatchers(System.Type,System.Type)">
            <summary>
              Visits all constituent parts of <paramref name="type"/>, replacing all type matchers
              that match the type argument at the corresponding position in <paramref name="other"/>.
            </summary>
            <param name="type">The type to be matched. May be, or contain, type matchers.</param>
            <param name="other">The type argument to match against <paramref name="type"/>.</param>
        </member>
        <member name="M:Moq.Guard.NotNull(System.Object,System.String)">
            <summary>
            Ensures the given <paramref name="value"/> is not null.
            Throws <see cref="T:System.ArgumentNullException"/> otherwise.
            </summary>
        </member>
        <member name="M:Moq.Guard.NotNullOrEmpty(System.String,System.String)">
            <summary>
            Ensures the given string <paramref name="value"/> is not null or empty.
            Throws <see cref="T:System.ArgumentNullException"/> in the first case, or 
            <see cref="T:System.ArgumentException"/> in the latter.
            </summary>
        </member>
        <member name="T:Moq.IInvocation">
            <summary>
            Provides information about an invocation of a mock object.
            </summary>
        </member>
        <member name="P:Moq.IInvocation.Method">
            <summary>
            Gets the method of the invocation.
            </summary>
        </member>
        <member name="P:Moq.IInvocation.Arguments">
            <summary>
            Gets the arguments of the invocation.
            </summary>
        </member>
        <member name="P:Moq.IInvocation.MatchingSetup">
            <summary>
              Gets the setup that matched this invocation (or <see langword="null"/> if there was no matching setup).
            </summary>
        </member>
        <member name="P:Moq.IInvocation.IsVerified">
            <summary>
              Gets whether this invocation was successfully verified by any of the various <c>`Verify`</c> methods.
            </summary>
        </member>
        <member name="P:Moq.IInvocation.ReturnValue">
            <summary>
            The value being returned for a non-void method if no exception was thrown.
            </summary>
        </member>
        <member name="P:Moq.IInvocation.Exception">
            <summary>
            Optional exception if the method invocation results in an exception being thrown.
            </summary>
        </member>
        <member name="T:Moq.IInvocationList">
            <summary>
            A list of invocations which have been performed on a mock.
            </summary>
        </member>
        <member name="M:Moq.IInvocationList.Clear">
            <summary>
            Resets all invocations recorded for this mock.
            </summary>
        </member>
        <member name="T:Moq.IMock`1">
            <summary>
            Covariant interface for <see cref="T:Moq.Mock`1"/> such that casts between IMock&lt;Employee&gt; to IMock&lt;Person&gt;
            are possible. Only covers the covariant members of <see cref="T:Moq.Mock`1"/>.
            </summary>
        </member>
        <member name="P:Moq.IMock`1.Object">
            <summary>
              Exposes the mocked object instance.
            </summary>
        </member>
        <member name="P:Moq.IMock`1.Behavior">
            <summary>
              Behavior of the mock, according to the value set in the constructor.
            </summary>
        </member>
        <member name="P:Moq.IMock`1.CallBase">
            <summary>
              Whether the base member virtual implementation will be called for mocked classes if no setup is matched.
              Defaults to <see langword="false"/>.
            </summary>
        </member>
        <member name="P:Moq.IMock`1.DefaultValue">
            <summary>
              Specifies the behavior to use when returning default values for unexpected invocations on loose mocks.
            </summary>
        </member>
        <member name="T:Moq.IMocked`1">
            <summary>
            Implemented by all generated mock object instances.
            </summary>
        </member>
        <member name="P:Moq.IMocked`1.Mock">
            <summary>
            Reference the Mock that contains this as the <c>mock.Object</c> value.
            </summary>
        </member>
        <member name="T:Moq.IMocked">
            <summary>
            Implemented by all generated mock object instances.
            </summary>
        </member>
        <member name="P:Moq.IMocked.Mock">
            <summary>
            Reference the Mock that contains this as the <c>mock.Object</c> value.
            </summary>
        </member>
        <member name="T:Moq.IInterceptor">
            <summary>
            This role interface represents a <see cref="T:Moq.Mock"/>'s ability to intercept method invocations for its <see cref="P:Moq.Mock.Object"/>.
            It is meant for use by <see cref="T:Moq.ProxyFactory"/>.
            </summary>
        </member>
        <member name="T:Moq.Mock">
            <summary>
              Base class for mocks and static helper class with methods that apply to mocked objects,
              such as <see cref="M:Moq.Mock.Get``1(``0)"/> to retrieve a <see cref="T:Moq.Mock`1"/> from an object instance.
            </summary>
        </member>
        <member name="M:Moq.Mock.Of``1">
            <summary>
            Creates a mock object of the indicated type.
            </summary>
            <typeparam name="T">The type of the mocked object.</typeparam>
            <returns>The mocked object created.</returns>
        </member>
        <member name="M:Moq.Mock.Of``1(Moq.MockBehavior)">
            <summary>
            Creates a mock object of the indicated type.
            </summary>
            <param name="behavior">Behavior of the mock.</param>
            <typeparam name="T">The type of the mocked object.</typeparam>
            <returns>The mocked object created.</returns>
        </member>
        <member name="M:Moq.Mock.Of``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            Creates a mock object of the indicated type.
            </summary>
            <param name="predicate">The predicate with the specification of how the mocked object should behave.</param>
            <typeparam name="T">The type of the mocked object.</typeparam>
            <returns>The mocked object created.</returns>
        </member>
        <member name="M:Moq.Mock.Of``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},Moq.MockBehavior)">
            <summary>
            Creates a mock object of the indicated type.
            </summary>
            <param name="predicate">The predicate with the specification of how the mocked object should behave.</param>
            <param name="behavior">Behavior of the mock.</param>
            <typeparam name="T">The type of the mocked object.</typeparam>
            <returns>The mocked object created.</returns>
        </member>
        <member name="M:Moq.Mock.#ctor">
            <summary>
              Initializes a new instance of the <see cref="T:Moq.Mock"/> class.
            </summary>
        </member>
        <member name="M:Moq.Mock.Get``1(``0)">
             <summary>
               Retrieves the mock object for the given object instance.
             </summary>
             <param name="mocked">The instance of the mocked object.</param>
             <typeparam name="T">
               Type of the mock to retrieve.
               Can be omitted as it's inferred from the object instance passed in as the <paramref name="mocked"/> instance.
             </typeparam>
             <returns>The mock associated with the mocked object.</returns>
             <exception cref="T:System.ArgumentException">The received <paramref name="mocked"/> instance was not created by Moq.</exception>
             <example group="advanced">
               The following example shows how to add a new setup to an object instance
               which is not the original <see cref="T:Moq.Mock`1"/> but rather the object associated with it:
               <code>
                 // Typed instance, not the mock, is retrieved from some test API.
                 HttpContextBase context = GetMockContext();
            
                 // context.Request is the typed object from the "real" API
                 // so in order to add a setup to it, we need to get
                 // the mock that "owns" it
                 Mock&lt;HttpRequestBase&gt; request = Mock.Get(context.Request);
            
                 request.Setup(req => req.AppRelativeCurrentExecutionFilePath)
                        .Returns(tempUrl);
               </code>
             </example>
        </member>
        <member name="M:Moq.Mock.Verify(Moq.Mock[])">
            <summary>
              Verifies that all verifiable expectations have been met.
            </summary>
            <exception cref="T:Moq.MockException">Not all verifiable expectations were met.</exception>
        </member>
        <member name="M:Moq.Mock.VerifyAll(Moq.Mock[])">
            <summary>
              Verifies all expectations regardless of whether they have been flagged as verifiable.
            </summary>
            <exception cref="T:Moq.MockException">At least one expectation was not met.</exception>
        </member>
        <member name="P:Moq.Mock.AdditionalInterfaces">
            <summary>
            Gets the interfaces additionally implemented by the mock object.
            </summary>
            <remarks>
            This list may be modified by calls to <see cref="M:Moq.Mock.As``1"/> up until the first call to <see cref="P:Moq.Mock.Object"/>.
            </remarks>
        </member>
        <member name="P:Moq.Mock.Behavior">
            <summary>
              Behavior of the mock, according to the value set in the constructor.
            </summary>
        </member>
        <member name="P:Moq.Mock.CallBase">
            <summary>
              Whether the base member virtual implementation will be called for mocked classes if no setup is matched.
              Defaults to <see langword="false"/>.
            </summary>
        </member>
        <member name="P:Moq.Mock.DefaultValue">
            <summary>
              Specifies the behavior to use when returning default values for unexpected invocations on loose mocks.
            </summary>
        </member>
        <member name="P:Moq.Mock.Object">
            <summary>
              Gets the mocked object instance.
            </summary>
        </member>
        <member name="P:Moq.Mock.InheritedInterfaces">
            <summary>
              Gets the interfaces directly inherited from the mocked type (<see cref="P:Moq.Mock.MockedType"/>).
            </summary>
        </member>
        <member name="P:Moq.Mock.Invocations">
            <summary>
            Gets list of invocations which have been performed on this mock.
            </summary>
        </member>
        <member name="M:Moq.Mock.OnGetObject">
            <summary>
              Returns the mocked object value.
            </summary>
        </member>
        <member name="P:Moq.Mock.MockedType">
            <summary>
            Retrieves the type of the mocked object, its generic type argument.
            This is used in the auto-mocking of hierarchy access.
            </summary>
        </member>
        <member name="P:Moq.Mock.DefaultValueProvider">
            <summary>
            Gets or sets the <see cref="T:Moq.DefaultValueProvider"/> instance that will be used
            e. g. to produce default return values for unexpected invocations.
            </summary>
        </member>
        <member name="P:Moq.Mock.AutoSetupPropertiesDefaultValueProvider">
            <summary>
            The <see cref="T:Moq.DefaultValueProvider"/> used to initialize automatically stubbed properties.
            It is equal to the value of <see cref="P:Moq.Mock.DefaultValueProvider"/> at the time when
            <see cref="M:Moq.Mock.SetupAllProperties(Moq.Mock)"/> was last called.
            </summary>
        </member>
        <member name="P:Moq.Mock.Setups">
            <summary>
              Gets the setups that have been configured on this mock,
              in chronological order (that is, oldest setup first, most recent setup last).
            </summary>
        </member>
        <member name="P:Moq.Mock.Switches">
            <summary>
            A set of switches that influence how this mock will operate.
            You can opt in or out of certain features via this property.
            </summary>
        </member>
        <member name="M:Moq.Mock.Verify">
             <summary>
               Verifies that all verifiable expectations have been met.
             </summary>
             <exception cref="T:Moq.MockException">Not all verifiable expectations were met.</exception>
             <example group="verification">
               This example sets up an expectation and marks it as verifiable.
               After the mock is used, a <c>Verify()</c> call is issued on the mock
               to ensure the method in the setup was invoked:
               <code>
                 var mock = new Mock&lt;IWarehouse&gt;();
                 this.Setup(x =&gt; x.HasInventory(TALISKER, 50))
                     .Returns(true)
                     .Verifiable();
            
                 ...
            
                 // Will throw if the test code did not call HasInventory.
                 this.Verify();
               </code>
             </example>
        </member>
        <member name="M:Moq.Mock.VerifyAll">
             <summary>
               Verifies all expectations regardless of whether they have been flagged as verifiable.
             </summary>
             <exception cref="T:Moq.MockException">At least one expectation was not met.</exception>
             <example>
               This example sets up an expectation without marking it as verifiable.
               After the mock is used, a <see cref="M:Moq.Mock.VerifyAll(Moq.Mock[])"/> call is issued on the mock
               to ensure that all expectations are met:
               <code>
                 var mock = new Mock&lt;IWarehouse&gt;();
                 this.Setup(x =&gt; x.HasInventory(TALISKER, 50))
                     .Returns(true);
            
                 ...
            
                 // Will throw if the test code did not call HasInventory,
                 // even though that expectation was not marked as verifiable.
                 mock.VerifyAll();
               </code>
             </example>
        </member>
        <member name="M:Moq.Mock.As``1">
            <summary>
              Adds an interface implementation to the mock, allowing setups to be specified for it.
            </summary>
            <remarks>
              This method can only be called before the first use of the mock <see cref="P:Moq.Mock.Object"/> property,
              at which point the runtime type has already been generated and no more interfaces can be added to it.
              <para>
                Also, <typeparamref name="TInterface"/> must be an interface and not a class,
                which must be specified when creating the mock instead.
              </para>
            </remarks>
            <typeparam name="TInterface">Type of interface to cast the mock to.</typeparam>
            <exception cref="T:System.ArgumentException">The <typeparamref name="TInterface"/> specified is not an interface.</exception>
            <exception cref="T:System.InvalidOperationException">
              The mock type has already been generated by accessing the <see cref="P:Moq.Mock.Object"/> property.
            </exception>
        </member>
        <member name="M:Moq.Mock.SetReturnsDefault``1(``0)">
            <summary>
            Defines the default return value for all mocked methods or properties with return type <typeparamref name= "TReturn" />.
            </summary>
            <typeparam name="TReturn">The return type for which to define a default value.</typeparam>
            <param name="value">The default return value.</param>
            <remarks>
            Default return value is respected only when there is no matching setup for a method call.
            </remarks>
        </member>
        <member name="M:Moq.Invocation.#ctor(System.Type,System.Reflection.MethodInfo,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Moq.Invocation"/> class.
            </summary>
            <param name="proxyType">The <see cref="T:System.Type"/> of the concrete proxy object on which a method is being invoked.</param>
            <param name="method">The method being invoked.</param>
            <param name="arguments">The arguments with which the specified <paramref name="method"/> is being invoked.</param>
        </member>
        <member name="P:Moq.Invocation.Method">
            <summary>
            Gets the method of the invocation.
            </summary>
        </member>
        <member name="P:Moq.Invocation.Arguments">
            <summary>
            Gets the arguments of the invocation.
            </summary>
            <remarks>
            Arguments may be modified. Derived classes must ensure that by-reference parameters are written back
            when the invocation is ended by a call to any of the three <c>Returns</c> methods.
            </remarks>
        </member>
        <member name="M:Moq.Invocation.CallBase">
            <summary>
              Calls the <see langword="base"/> method implementation
              and returns its return value (or <see langword="null"/> for <see langword="void"/> methods).
            </summary>
        </member>
        <member name="M:Moq.Invocation.ToString">
            <inheritdoc/>
        </member>
        <member name="T:Moq.Invocation.ExceptionResult">
            <summary>
            Internal type to mark invocation results as "exception occurred during execution". The type just
            wraps the Exception so a thrown exception can be distinguished from an <see cref="T:System.Exception"/>
            return value.
            </summary>
        </member>
        <member name="T:Moq.InvocationAction">
            <summary>
              A delegate-like type for use with `setup.Callback` which instructs the `Callback` verb
              to provide the callback with the current <see cref="T:Moq.IInvocation"/>, instead of
              with a list of arguments.
              <para>
                This type is useful in scenarios involving generic type argument matchers (such as
                <see cref="T:Moq.It.IsAnyType" />) as <see cref="T:Moq.IInvocation"/> allows the discovery of both
                arguments and type arguments.
              </para>
            </summary>
        </member>
        <member name="M:Moq.InvocationAction.#ctor(System.Action{Moq.IInvocation})">
            <summary>
              Initializes a new instance of the <see cref="T:Moq.InvocationAction"/> type.
            </summary>
            <param name="action">The delegate that should be wrapped by this instance.</param>
        </member>
        <member name="T:Moq.InvocationFunc">
            <summary>
              A delegate-like type for use with `setup.Returns` which instructs the `Returns` verb
              to provide the callback with the current <see cref="T:Moq.IInvocation"/>, instead of
              with a list of arguments.
              <para>
                This type is useful in scenarios involving generic type argument matchers (such as
                <see cref="T:Moq.It.IsAnyType" />) as <see cref="T:Moq.IInvocation"/> allows the discovery of both
                arguments and type arguments.
              </para>
            </summary>
        </member>
        <member name="M:Moq.InvocationFunc.#ctor(System.Func{Moq.IInvocation,System.Object})">
            <summary>
              Initializes a new instance of the <see cref="T:Moq.InvocationFunc"/> type.
            </summary>
            <param name="func">The delegate that should be wrapped by this instance.</param>
        </member>
        <member name="T:Moq.InvocationShape">
            <summary>
              Describes the "shape" of an invocation against which concrete <see cref="T:Moq.Invocation"/>s can be matched.
              <para>
                This shape is described by <see cref="F:Moq.InvocationShape.Expression"/> which has the general form
                `mock => mock.Method(...arguments)`. Because the method and arguments are frequently needed,
                they are cached in <see cref="F:Moq.InvocationShape.Method"/> and <see cref="F:Moq.InvocationShape.Arguments"/>
                for faster access.
              </para>
            </summary>
        </member>
        <member name="T:Moq.ISetup">
            <summary>
              A setup configured on a mock.
            </summary>
            <seealso cref="P:Moq.Mock.Setups"/>
        </member>
        <member name="P:Moq.ISetup.Expression">
            <summary>
              The setup expression.
            </summary>
        </member>
        <member name="P:Moq.ISetup.InnerMock">
            <summary>
              Gets the inner mock of this setup (if present and known).
              <para>
                An "inner mock" is the <see cref="T:Moq.Mock"/> instance associated with a setup's return value,
                if that setup is configured to return a mock object.
              </para>
              <para>
                This property will be <see langword="null"/> if a setup either does not return a mock object,
                or if Moq cannot safely determine its return value without risking any side effects. For instance,
                Moq is able to inspect the return value if it is a constant (e.g. <c>`.Returns(value)`</c>);
                if, on the other hand, it gets computed by a factory function (e.g. <c>`.Returns(() => value)`</c>),
                Moq will not attempt to retrieve that value just to find the inner mock,
                since calling a user-provided function could have effects beyond Moq's understanding and control.
              </para>
            </summary>
        </member>
        <member name="P:Moq.ISetup.IsConditional">
            <summary>
              Gets whether this setup is conditional.
            </summary>
            <seealso cref="M:Moq.Mock`1.When(System.Func{System.Boolean})"/>
        </member>
        <member name="P:Moq.ISetup.IsMatched">
            <summary>
              Gets whether this setup was matched by at least one invocation on the mock.
            </summary>
        </member>
        <member name="P:Moq.ISetup.IsOverridden">
            <summary>
              Gets whether this setup has been overridden
              (that is, whether it is being shadowed by a more recent non-conditional setup with an equal expression).
            </summary>
        </member>
        <member name="P:Moq.ISetup.IsVerifiable">
            <summary>
              Gets whether this setup is "verifiable".
            </summary>
            <remarks>
              This property gets sets by the <c>`.Verifiable()`</c> setup verb.
              <para>
                Note that setups can be verified even if this property is <see langword="false"/>:
                <see cref="M:Moq.Mock.VerifyAll"/> completely ignores this property.
                <see cref="M:Moq.Mock.Verify"/>, however, will only verify setups where this property is <see langword="true"/>.
              </para>
            </remarks>
        </member>
        <member name="P:Moq.ISetup.Mock">
            <summary>
              Returns the <see cref="P:Moq.ISetup.Mock"/> instance to which this setup belongs.
            </summary>
        </member>
        <member name="P:Moq.ISetup.OriginalExpression">
            <summary>
              Returns the original setup expression from which this setup resulted.
              <para>
                For setups doing a simple member access or method invocation (such as <c>`mock => mock.Member`</c>),
                this property will be equal to <see cref="P:Moq.ISetup.Expression"/>.
              </para>
              <para>
                For setups whose expression involves member chaining (such as <c>`parent => parent.Child.Member`</c>),
                Moq does not create a single setup, but one for each member access/invocation.
                The example just given will result in two setups:
                <list type="number">
                  <item>a setup for <c>`parent => parent.Child`</c> on the parent mock; and</item>
                  <item>on its inner mock, a setup for <c>`(child) => (child).Member`</c>.</item>
                </list>
                These are the setups that will be put in the mocks' <see cref="P:Moq.Mock.Setups"/> collections;
                their <see cref="P:Moq.ISetup.Expression"/> will return the partial expression for just a single member access,
                while their <see cref="P:Moq.ISetup.OriginalExpression"/> will return the original, full expression.
              </para>
              <para>
                This property may also return <see langword="null"/> if this setup was created automatically,
                e.g. by <see cref="M:Moq.Mock`1.SetupAllProperties"/> or by <see cref="F:Moq.DefaultValue.Mock"/>.
              </para>
            </summary>
        </member>
        <member name="M:Moq.ISetup.Verify(System.Boolean)">
            <summary>
              Verifies this setup and optionally all verifiable setups of its inner mock (if present and known).
              <para>
                If <paramref name="recursive"/> is set to <see langword="true"/>,
                the semantics of this method are essentially the same as those of <see cref="M:Moq.Mock.Verify"/>,
                except that this setup (instead of a mock) is used as the starting point for verification,
                and will always be verified itself (even if not flagged as verifiable).
              </para>
            </summary>
            <param name="recursive">
              Specifies whether recursive verification should be performed.
            </param>
            <exception cref="T:Moq.MockException">
              Verification failed due to one or more unmatched setups.
            </exception>
            <seealso cref="M:Moq.ISetup.VerifyAll"/>
            <seealso cref="M:Moq.Mock.Verify"/>
        </member>
        <member name="M:Moq.ISetup.VerifyAll">
            <summary>
              Verifies this setup and all setups of its inner mock (if present and known),
              regardless of whether they have been flagged as verifiable.
              <para>
                The semantics of this method are essentially the same as those of <see cref="M:Moq.Mock.VerifyAll"/>,
                except that this setup (instead of a mock) is used as the starting point for verification.
              </para>
            </summary>
            <exception cref="T:Moq.MockException">
              Verification failed due to one or more unmatched setups.
            </exception>
            <seealso cref="M:Moq.ISetup.Verify(System.Boolean)"/>
            <seealso cref="M:Moq.Mock.VerifyAll"/>
        </member>
        <member name="T:Moq.ISetupList">
            <summary>
              A list of setups that have been configured on a mock,
              in chronological order (that is, oldest setup first, most recent setup last).
            </summary>
        </member>
        <member name="T:Moq.It">
            <summary>
              Allows the specification of a matching condition for an argument in a method invocation,
              rather than a specific argument value. "It" refers to the argument being matched.
            </summary>
            <remarks>
              This class allows the setup to match a method invocation with an arbitrary value,
              with a value in a specified range, or even one that matches a given predicate.
            </remarks>
        </member>
        <member name="T:Moq.It.Ref`1">
            <summary>
            Contains matchers for <see langword="ref"/> (C#) / <see langword="ByRef"/> (VB.NET) parameters of type <typeparamref name="TValue"/>.
            </summary>
            <typeparam name="TValue">The parameter type.</typeparam>
        </member>
        <member name="F:Moq.It.Ref`1.IsAny">
            <summary>
            Matches any value that is assignment-compatible with type <typeparamref name="TValue"/>.
            </summary>
        </member>
        <member name="M:Moq.It.IsAny``1">
            <summary>
              Matches any value of the given <typeparamref name="TValue"/> type.
            </summary>
            <typeparam name="TValue">Type of the value.</typeparam>
            <remarks>
              Typically used when the actual argument value for a method call is not relevant.
            </remarks>
            <example>
              <code>
                // Throws an exception for a call to Remove with any string value.
                mock.Setup(x => x.Remove(It.IsAny&lt;string&gt;())).Throws(new InvalidOperationException());
              </code>
            </example>
        </member>
        <member name="T:Moq.It.IsAnyType">
            <summary>
              A type matcher that matches any generic type argument.
              <para>
                If the generic type parameter is constrained to <see langword="struct"/> (C#) / <see langword="Structure"/>
                (VB.NET), use <see cref="T:Moq.It.IsValueType"/> instead.
              </para>
              <para>
                If the generic type parameter has more specific constraints,
                you can define your own type matcher inheriting from the type to which the type parameter is constrained.
                See <see cref="T:Moq.TypeMatcherAttribute"/> and <see cref="T:Moq.ITypeMatcher"/>.
              </para>
            </summary>
        </member>
        <member name="M:Moq.It.IsNotNull``1">
            <summary>
              Matches any value of the given <typeparamref name="TValue"/> type, except null.
            </summary>
            <typeparam name="TValue">Type of the value.</typeparam>
        </member>
        <member name="M:Moq.It.Is``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
              Matches any value that satisfies the given predicate.
            </summary>
            <param name="match">The predicate used to match the method argument.</param>
            <typeparam name="TValue">Type of the argument to check.</typeparam>
            <remarks>
              Allows the specification of a predicate to perform matching of method call arguments.
            </remarks>
            <example>
              This example shows how to return the value <c>1</c> whenever the argument to
              the <c>Do</c> method is an even number.
              <code>
                mock.Setup(x =&gt; x.Do(It.Is&lt;int&gt;(i =&gt; i % 2 == 0)))
                    .Returns(1);
              </code>
            </example>
            <example>
              This example shows how to throw an exception if the argument to the method
              is a negative number:
              <code>
                mock.Setup(x =&gt; x.GetUser(It.Is&lt;int&gt;(i =&gt; i &lt; 0)))
                    .Throws(new ArgumentException());
              </code>
            </example>
        </member>
        <member name="M:Moq.It.Is``1(System.Linq.Expressions.Expression{System.Func{System.Object,System.Type,System.Boolean}})">
            <summary>
              Matches any value that satisfies the given predicate.
              <para>
                Use this overload when you specify a type matcher for <typeparamref name="TValue"/>.
                The <paramref name="match"/> callback you provide will then receive the actual parameter type
                as well as the invocation argument.
              </para>
            </summary>
            <param name="match">The predicate used to match the method argument.</param>
            <typeparam name="TValue">Type of the argument to check.</typeparam>
            <remarks>
              Allows the specification of a predicate to perform matching of method call arguments.
            </remarks>
        </member>
        <member name="M:Moq.It.Is``1(``0,System.Collections.Generic.IEqualityComparer{``0})">
            <summary>
              Matches any value that equals the <paramref name="value"/> using the <paramref name="comparer"/>.
              To use the default comparer for the specified object, specify the value inline,
              i.e. <code>mock.Verify(service => service.DoWork(value))</code>.
              <para>
                Use this overload when you specify a value and a comparer.
              </para>
            </summary>
            <param name="value">The value to match with.</param>
            <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> with which the values should be compared.</param>
            <typeparam name="TValue">Type of the argument to check.</typeparam>
        </member>
        <member name="M:Moq.It.IsInRange``1(``0,``0,Moq.Range)">
            <summary>
              Matches any value that is in the range specified.
            </summary>
            <param name="from">The lower bound of the range.</param>
            <param name="to">The upper bound of the range.</param>
            <param name="rangeKind">The kind of range. See <see cref="T:Moq.Range"/>.</param>
            <typeparam name="TValue">Type of the argument to check.</typeparam>
            <example>
              The following example shows how to expect a method call with an integer argument
              within the 0..100 range.
              <code>
                mock.Setup(x => x.HasInventory(
                                    It.IsAny&lt;string&gt;(),
                                    It.IsInRange(0, 100, Range.Inclusive)))
                    .Returns(false);
              </code>
            </example>
        </member>
        <member name="M:Moq.It.IsIn``1(System.Collections.Generic.IEnumerable{``0})">
             <summary>
               Matches any value that is present in the sequence specified.
             </summary>
             <param name="items">The sequence of possible values.</param>
             <typeparam name="TValue">Type of the argument to check.</typeparam>
             <example>
               The following example shows how to expect a method call with an integer argument
               with value from a list.
               <code>
                 var values = new List&lt;int&gt; { 1, 2, 3 };
            
                 mock.Setup(x => x.HasInventory(
                                     It.IsAny&lt;string&gt;(),
                                     It.IsIn(values)))
                     .Returns(false);
               </code>
             </example>
        </member>
        <member name="M:Moq.It.IsIn``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
            <summary>
              Matches any value that is present in the sequence specified.
            </summary>
            <param name="items">The sequence of possible values.</param>
            <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> with which the values should be compared.</param>
            <typeparam name="TValue">Type of the argument to check.</typeparam>
        </member>
        <member name="M:Moq.It.IsIn``1(``0[])">
            <summary>
              Matches any value that is present in the sequence specified.
            </summary>
            <param name="items">The sequence of possible values.</param>
            <typeparam name="TValue">Type of the argument to check.</typeparam>
            <example>
              The following example shows how to expect a method call with an integer argument
              with a value of 1, 2, or 3.
              <code>
                mock.Setup(x => x.HasInventory(
                                    It.IsAny&lt;string&gt;(),
                                    It.IsIn(1, 2, 3)))
                    .Returns(false);
              </code>
            </example>
        </member>
        <member name="M:Moq.It.IsNotIn``1(System.Collections.Generic.IEnumerable{``0})">
             <summary>
               Matches any value that is not found in the sequence specified.
             </summary>
             <param name="items">The sequence of disallowed values.</param>
             <typeparam name="TValue">Type of the argument to check.</typeparam>
             <example>
               The following example shows how to expect a method call with an integer argument
               with value not found from a list.
               <code>
                 var values = new List&lt;int&gt; { 1, 2, 3 };
            
                 mock.Setup(x => x.HasInventory(
                                     It.IsAny&lt;string&gt;(),
                                     It.IsNotIn(values)))
                     .Returns(false);
               </code>
             </example>
        </member>
        <member name="M:Moq.It.IsNotIn``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
            <summary>
              Matches any value that is not found in the sequence specified.
            </summary>
            <param name="items">The sequence of disallowed values.</param>
            <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> with which the values should be compared.</param>
            <typeparam name="TValue">Type of the argument to check.</typeparam>
        </member>
        <member name="M:Moq.It.IsNotIn``1(``0[])">
            <summary>
              Matches any value that is not found in the sequence specified.
            </summary>
            <param name="items">The sequence of disallowed values.</param>
            <typeparam name="TValue">Type of the argument to check.</typeparam>
            <example>
              The following example shows how to expect a method call with an integer argument
              of any value except 1, 2, or 3.
              <code>
                mock.Setup(x => x.HasInventory(
                                    It.IsAny&lt;string&gt;(),
                                    It.IsNotIn(1, 2, 3)))
                    .Returns(false);
              </code>
            </example>
        </member>
        <member name="M:Moq.It.IsRegex(System.String)">
            <summary>
              Matches a string argument if it matches the given regular expression pattern.
            </summary>
            <param name="regex">The pattern to use to match the string argument value.</param>
            <example>
              The following example shows how to expect a call to a method where the string argument
              matches the given regular expression:
              <code>
                mock.Setup(x => x.Check(It.IsRegex("[a-z]+")))
                    .Returns(1);
              </code>
            </example>
        </member>
        <member name="M:Moq.It.IsRegex(System.String,System.Text.RegularExpressions.RegexOptions)">
            <summary>
              Matches a string argument if it matches the given regular expression pattern.
            </summary>
            <param name="regex">The pattern to use to match the string argument value.</param>
            <param name="options">The options used to interpret the pattern.</param>
            <example>
              The following example shows how to expect a call to a method where the string argument
              matches the given regular expression, in a case insensitive way:
              <code>
                mock.Setup(x => x.Check(It.IsRegex("[a-z]+", RegexOptions.IgnoreCase)))
                    .Returns(1);
              </code>
            </example>
        </member>
        <member name="T:Moq.It.IsSubtype`1">
            <summary>
              A type matcher that matches subtypes of <typeparamref name="T"/>, as well as <typeparamref name="T"/> itself.
            </summary>
            <typeparam name="T">The type whose subtypes should match.</typeparam>
        </member>
        <member name="T:Moq.It.IsValueType">
            <summary>
              A type matcher that matches any value type.
            </summary>
        </member>
        <member name="T:Moq.ITypeMatcher">
            <summary>
              Types that implement this interface represent a criterion against which generic type arguments are matched.
              <para>
                To be used in combination with <see cref="T:Moq.TypeMatcherAttribute"/>.
              </para>
            </summary>
        </member>
        <member name="M:Moq.ITypeMatcher.Matches(System.Type)">
            <summary>
              Matches the provided type argument against the criterion represented by this type matcher.
            </summary>
            <param name="typeArgument">
              The generic type argument that should be matched.
            </param>
            <returns>
              <see langword="true"/> if the provided type argument matched the criterion represented by this instance;
              otherwise, <see langword="false"/>.
            </returns>
        </member>
        <member name="T:Moq.Language.Flow.ICallbackResult">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.Flow.ICallBaseResult">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.Flow.IReturnsResult`1">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.Flow.IReturnsThrows`2">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.Flow.IReturnsThrowsGetter`2">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.Flow.ISetup`1">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.Flow.ISetup`2">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.Flow.ISetupGetter`2">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.Flow.ISetupSetter`2">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.Flow.IThrowsResult">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.ICallback">
            <summary>
            Defines the <c>Callback</c> verb and overloads.
            </summary>
        </member>
        <member name="M:Moq.Language.ICallback.Callback(Moq.InvocationAction)">
            <summary>
              Specifies a callback to invoke when the method is called that receives the original <see cref="T:Moq.IInvocation"/>.
              <para>
                This overload is intended to be used in scenarios involving generic type argument matchers
                (such as <see cref="T:Moq.It.IsAnyType"/>). The callback will receive the current <see cref="T:Moq.IInvocation"/>,
                which allows discovery of both arguments and type arguments.
              </para>
              <para>
                For all other use cases, you should prefer the other <c>Callback</c> overloads as they provide
                better static type safety.
              </para>
            </summary>
            <example>
              <code>
                Figure out the generic type argument used for a mocked method call:
                mock.Setup(m => m.DoSomethingWith&lt;It.IsAnyType&gt;(...))
                    .Callback(new InvocationAction(invocation =>
                             {
                                 var typeArgument = invocation.Method.GetGenericArguments()[0];
                                 // do something interesting with the type argument
                             });
                mock.Object.DoSomethingWith&lt;Something&gt;();
              </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback(System.Delegate)">
             <summary>
             Specifies a callback of any delegate type to invoke when the method is called.
             This overload specifically allows you to define callbacks for methods with by-ref parameters.
             By-ref parameters can be assigned to.
             </summary>
             <param name="callback">The callback method to invoke. Must have return type <c>void</c> (C#) or be a <c>Sub</c> (VB.NET).</param>
             <example>
             Invokes the given callback with the concrete invocation argument value. You can modify
             by-ref parameters inside the callback.
             <code>
             delegate void ExecuteAction(ref Command command);
            
             Command c = ...;
             mock.Setup(x => x.Execute(ref c))
                 .Callback(new ExecuteAction((ref Command command) => Console.WriteLine("Executing command...")));
             </code>
             </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback(System.Action)">
            <summary>
            Specifies a callback to invoke when the method is called.
            </summary>
            <param name="action">The callback method to invoke.</param>
            <example>
            The following example specifies a callback to set a boolean 
            value that can be used later:
            <code>
            var called = false;
            mock.Setup(x => x.Execute())
                .Callback(() => called = true);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``1(System.Action{``0})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T">The argument type of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <example>
            Invokes the given callback with the concrete invocation argument value. 
            <para>
            Notice how the specific string argument is retrieved by simply declaring 
            it as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(It.IsAny&lt;string&gt;()))
                .Callback((string command) => Console.WriteLine(command));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``2(System.Action{``0,``1})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2) => Console.WriteLine(arg1 + arg2));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``3(System.Action{``0,``1,``2})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3) => Console.WriteLine(arg1 + arg2 + arg3));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``4(System.Action{``0,``1,``2,``3})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4) => Console.WriteLine(arg1 + arg2 + arg3 + arg4));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``5(System.Action{``0,``1,``2,``3,``4})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``6(System.Action{``0,``1,``2,``3,``4,``5})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``7(System.Action{``0,``1,``2,``3,``4,``5,``6})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``8(System.Action{``0,``1,``2,``3,``4,``5,``6,``7})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``9(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``10(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``11(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``12(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``13(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12, string arg13) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``14(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12, string arg13, string arg14) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``15(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <typeparam name="T15">The type of the fifteenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12, string arg13, string arg14, string arg15) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14 + arg15));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``16(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <typeparam name="T15">The type of the fifteenth argument of the invoked method.</typeparam>
            <typeparam name="T16">The type of the sixteenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12, string arg13, string arg14, string arg15, string arg16) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14 + arg15 + arg16));
            </code>
            </example>
        </member>
        <member name="T:Moq.Language.ICallback`2">
            <summary>
            Defines the <c>Callback</c> verb and overloads for callbacks on
            setups that return a value.
            </summary>
            <typeparam name="TMock">Mocked type.</typeparam>
            <typeparam name="TResult">Type of the return value of the setup.</typeparam>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback(Moq.InvocationAction)">
            <summary>
              Specifies a callback to invoke when the method is called that receives the original <see cref="T:Moq.IInvocation"/>.
              <para>
                This overload is intended to be used in scenarios involving generic type argument matchers
                (such as <see cref="T:Moq.It.IsAnyType"/>). The callback will receive the current <see cref="T:Moq.IInvocation"/>,
                which allows discovery of both arguments and type arguments.
              </para>
              <para>
                For all other use cases, you should prefer the other <c>Callback</c> overloads as they provide
                better static type safety.
              </para>
            </summary>
            <example>
                Figure out the generic type argument used for a mocked method call:
              <code>
                mock.Setup(m => m.DoSomethingWith&lt;It.IsAnyType&gt;(...))
                    .Callback(new InvocationAction(invocation =>
                             {
                                 var typeArgument = invocation.Method.GetGenericArguments()[0];
                                 // do something interesting with the type argument
                             });
                mock.Object.DoSomethingWith&lt;Something&gt;();
              </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback(System.Delegate)">
             <summary>
             Specifies a callback of any delegate type to invoke when the method is called.
             This overload specifically allows you to define callbacks for methods with by-ref parameters.
             By-ref parameters can be assigned to.
             </summary>
             <param name="callback">The callback method to invoke. Must have return type <c>void</c> (C#) or be a <c>Sub</c> (VB.NET).</param>
             <example>
             Invokes the given callback with the concrete invocation argument value. You can modify
             by-ref parameters inside the callback.
             <code>
             delegate void ExecuteAction(ref Command command);
            
             Command c = ...;
             mock.Setup(x => x.Execute(ref c))
                 .Callback(new ExecuteAction((ref Command command) => Console.WriteLine("Executing command...")));
             </code>
             </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback(System.Action)">
            <summary>
            Specifies a callback to invoke when the method is called.
            </summary>
            <param name="action">The callback method to invoke.</param>
            <example>
            The following example specifies a callback to set a boolean value that can be used later:
            <code>
            var called = false;
            mock.Setup(x => x.Execute())
                .Callback(() => called = true)
                .Returns(true);
            </code>
            Note that in the case of value-returning methods, after the <c>Callback</c>
            call you can still specify the return value.
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``1(System.Action{``0})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T">The type of the argument of the invoked method.</typeparam>
            <param name="action">Callback method to invoke.</param>
            <example>
            Invokes the given callback with the concrete invocation argument value.
            <para>
            Notice how the specific string argument is retrieved by simply declaring
            it as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(It.IsAny&lt;string&gt;()))
                .Callback(command => Console.WriteLine(command))
                .Returns(true);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``2(System.Action{``0,``1})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2) => Console.WriteLine(arg1 + arg2));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``3(System.Action{``0,``1,``2})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3) => Console.WriteLine(arg1 + arg2 + arg3));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``4(System.Action{``0,``1,``2,``3})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4) => Console.WriteLine(arg1 + arg2 + arg3 + arg4));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``5(System.Action{``0,``1,``2,``3,``4})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``6(System.Action{``0,``1,``2,``3,``4,``5})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``7(System.Action{``0,``1,``2,``3,``4,``5,``6})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``8(System.Action{``0,``1,``2,``3,``4,``5,``6,``7})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``9(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``10(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``11(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``12(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11, arg12) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``13(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11, arg12, arg13) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``14(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11, arg12, arg13, arg14) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``15(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <typeparam name="T15">The type of the fifteenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11, arg12, arg13, arg14, arg15) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14 + arg15));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``16(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <typeparam name="T15">The type of the fifteenth argument of the invoked method.</typeparam>
            <typeparam name="T16">The type of the sixteenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11, arg12, arg13, arg14, arg15, arg16) => Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14 + arg15 + arg16));
            </code>
            </example>
        </member>
        <member name="T:Moq.Language.ICallbackGetter`2">
            <summary>
            Defines the <c>Callback</c> verb for property getter setups.
            </summary>
            <seealso cref="M:Moq.Mock`1.SetupGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})"/>
            <typeparam name="TMock">Mocked type.</typeparam>
            <typeparam name="TProperty">Type of the property.</typeparam>
        </member>
        <member name="M:Moq.Language.ICallbackGetter`2.Callback(System.Action)">
            <summary>
            Specifies a callback to invoke when the property is retrieved.
            </summary>
            <param name="action">Callback method to invoke.</param>
            <example>
            Invokes the given callback with the property value being set. 
            <code>
            mock.SetupGet(x => x.Suspended)
                .Callback(() => called = true)
                .Returns(true);
            </code>
            </example>
        </member>
        <member name="T:Moq.Language.ICallbackSetter`1">
            <summary>
            Defines the <c>Callback</c> verb for property setter setups.
            </summary>
            <typeparam name="TProperty">Type of the property.</typeparam>
        </member>
        <member name="M:Moq.Language.ICallbackSetter`1.Callback(System.Action{`0})">
            <summary>
            Specifies a callback to invoke when the property is set that receives the 
            property value being set.
            </summary>
            <param name="action">Callback method to invoke.</param>
            <example>
            Invokes the given callback with the property value being set. 
            <code>
            mock.SetupSet(x => x.Suspended)
                .Callback((bool state) => Console.WriteLine(state));
            </code>
            </example>
        </member>
        <member name="T:Moq.Language.ICallBase">
            <summary>
            Defines the <c>CallBase</c> verb.
            </summary>
        </member>
        <member name="M:Moq.Language.ICallBase.CallBase">
            <summary>
            Calls the real method of the object.
            </summary>
        </member>
        <member name="T:Moq.Language.IRaise`1">
            <summary>
            Defines the <c>Raises</c> verb.
            </summary>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)">
            <summary>
            Specifies the event that will be raised 
            when the setup is met.
            </summary>
            <param name="eventExpression">An expression that represents an event attach or detach action.</param>
            <param name="args">The event arguments to pass for the raised event.</param>
            <example>
            The following example shows how to raise an event when 
            the setup is met:
            <code>
            var mock = new Mock&lt;IContainer&gt;();
            
            mock.Setup(add => add.Add(It.IsAny&lt;string&gt;(), It.IsAny&lt;object&gt;()))
                .Raises(add => add.Added += null, EventArgs.Empty);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.Func{System.EventArgs})">
            <summary>
            Specifies the event that will be raised 
            when the setup is matched.
            </summary>
            <param name="eventExpression">An expression that represents an event attach or detach action.</param>
            <param name="func">A function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.Object[])">
            <summary>
            Specifies the custom event that will be raised 
            when the setup is matched.
            </summary>
            <param name="eventExpression">An expression that represents an event attach or detach action.</param>
            <param name="args">The arguments to pass to the custom delegate (non EventHandler-compatible).</param>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``1(System.Action{`0},System.Func{``0,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``2(System.Action{`0},System.Func{``0,``1,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``3(System.Action{`0},System.Func{``0,``1,``2,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``4(System.Action{`0},System.Func{``0,``1,``2,``3,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``5(System.Action{`0},System.Func{``0,``1,``2,``3,``4,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``6(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``7(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``8(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``9(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <typeparam name="T9">The type of the ninth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``10(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <typeparam name="T9">The type of the ninth argument received by the expected invocation.</typeparam>
            <typeparam name="T10">The type of the tenth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``11(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <typeparam name="T9">The type of the ninth argument received by the expected invocation.</typeparam>
            <typeparam name="T10">The type of the tenth argument received by the expected invocation.</typeparam>
            <typeparam name="T11">The type of the eleventh argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``12(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <typeparam name="T9">The type of the ninth argument received by the expected invocation.</typeparam>
            <typeparam name="T10">The type of the tenth argument received by the expected invocation.</typeparam>
            <typeparam name="T11">The type of the eleventh argument received by the expected invocation.</typeparam>
            <typeparam name="T12">The type of the twelfth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``13(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <typeparam name="T9">The type of the ninth argument received by the expected invocation.</typeparam>
            <typeparam name="T10">The type of the tenth argument received by the expected invocation.</typeparam>
            <typeparam name="T11">The type of the eleventh argument received by the expected invocation.</typeparam>
            <typeparam name="T12">The type of the twelfth argument received by the expected invocation.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``14(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <typeparam name="T9">The type of the ninth argument received by the expected invocation.</typeparam>
            <typeparam name="T10">The type of the tenth argument received by the expected invocation.</typeparam>
            <typeparam name="T11">The type of the eleventh argument received by the expected invocation.</typeparam>
            <typeparam name="T12">The type of the twelfth argument received by the expected invocation.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument received by the expected invocation.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``15(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <typeparam name="T9">The type of the ninth argument received by the expected invocation.</typeparam>
            <typeparam name="T10">The type of the tenth argument received by the expected invocation.</typeparam>
            <typeparam name="T11">The type of the eleventh argument received by the expected invocation.</typeparam>
            <typeparam name="T12">The type of the twelfth argument received by the expected invocation.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument received by the expected invocation.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument received by the expected invocation.</typeparam>
            <typeparam name="T15">The type of the fifteenth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``16(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <typeparam name="T9">The type of the ninth argument received by the expected invocation.</typeparam>
            <typeparam name="T10">The type of the tenth argument received by the expected invocation.</typeparam>
            <typeparam name="T11">The type of the eleventh argument received by the expected invocation.</typeparam>
            <typeparam name="T12">The type of the twelfth argument received by the expected invocation.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument received by the expected invocation.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument received by the expected invocation.</typeparam>
            <typeparam name="T15">The type of the fifteenth argument received by the expected invocation.</typeparam>
            <typeparam name="T16">The type of the sixteenth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="T:Moq.Language.IReturns`2">
            <summary>
            Defines the <c>Returns</c> verb.
            </summary>
            <typeparam name="TMock">Mocked type.</typeparam>
            <typeparam name="TResult">Type of the return value from the expression.</typeparam>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns(`1)">
            <summary>
            Specifies the value to return.
            </summary>
            <param name="value">The value to return, or <see langword="null"/>.</param>
            <example>
            Return a <c>true</c> value from the method call:
            <code>
            mock.Setup(x => x.Execute("ping"))
                .Returns(true);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns(Moq.InvocationFunc)">
            <summary>
              Specifies a function that will calculate the value to return from the method.
              <para>
                This overload is intended to be used in scenarios involving generic type argument matchers,
                such as <see cref="T:Moq.It.IsAnyType"/>. The function will receive the current <see cref="T:Moq.IInvocation"/>,
                which allows discovery of both arguments and type arguments.
              </para>
              <para>
                For all other use cases, you should prefer the other <c>Returns</c> overloads as they provide
                better static type safety.
              </para>
            </summary>
            <example>
              Mock a method to act like a generic factory method:
              <code>
                factory.Setup(m => m.Create&lt;It.IsAnyType&gt;())
                       .Returns(new InvocationFunc(invocation =>
                                {
                                    var typeArgument = invocation.Method.GetGenericArguments()[0];
                                    return Activator.CreateInstance(typeArgument);
                                });
                var something = factory.Object.Create&lt;Something&gt;();
              </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns(System.Delegate)">
             <summary>
             Specifies a function that will calculate the value to return from the method.
             This overload specifically allows you to specify a function with by-ref parameters.
             Those by-ref parameters can be assigned to (though you should probably do that from
             a <c>Callback</c> instead).
             </summary>
             <param name="valueFunction">The function that will calculate the return value.</param>
             <example group="returns">
             Return a calculated value when the method is called:
             <code>
             delegate bool ExecuteFunc(ref Command command);
            
             Command c = ...;
             mock.Setup(x => x.Execute(ref c))
                 .Returns(new ExecuteFunc((ref Command command) => command.IsExecutable));
             </code>
             </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns(System.Func{`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method.
            </summary>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <example group="returns">
            Return a calculated value when the method is called:
            <code>
            mock.Setup(x => x.Execute("ping"))
                .Returns(() => returnValues[0]);
            </code>
            The lambda expression to retrieve the return value is lazy-executed, 
            meaning that its value may change depending on the moment the method 
            is executed and the value the <c>returnValues</c> array has at 
            that moment.
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``1(System.Func{``0,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T">The type of the argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <example group="returns">
            Return a calculated value which is evaluated lazily at the time of the invocation.
            <para>
            The lookup list can change between invocations and the setup 
            will return different values accordingly. Also, notice how the specific 
            string argument is retrieved by simply declaring it as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(It.IsAny&lt;string&gt;()))
                .Returns((string command) => returnValues[command]);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.CallBase">
            <summary>
            Calls the real method of the object and returns its return value.
            </summary>
            <returns>The value calculated by the real method of the object.</returns>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``2(System.Func{``0,``1,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2) => arg1 + arg2);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``3(System.Func{``0,``1,``2,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3) => arg1 + arg2 + arg3);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``4(System.Func{``0,``1,``2,``3,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4) => arg1 + arg2 + arg3 + arg4);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``5(System.Func{``0,``1,``2,``3,``4,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5) => arg1 + arg2 + arg3 + arg4 + arg5);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``6(System.Func{``0,``1,``2,``3,``4,``5,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``7(System.Func{``0,``1,``2,``3,``4,``5,``6,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``8(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``9(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``10(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``11(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``12(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``13(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12, string arg13) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``14(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12, string arg13, string arg14) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``15(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <typeparam name="T15">The type of the fifteenth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12, string arg13, string arg14, string arg15) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14 + arg15);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``16(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the ninth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <typeparam name="T15">The type of the fifteenth argument of the invoked method.</typeparam>
            <typeparam name="T16">The type of the sixteenth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12, string arg13, string arg14, string arg15, string arg16) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14 + arg15 + arg16);
            </code>
            </example>
        </member>
        <member name="T:Moq.Language.IReturnsGetter`2">
            <summary>
            Defines the <c>Returns</c> verb for property get setups.
            </summary>
            <typeparam name="TMock">Mocked type.</typeparam>
            <typeparam name="TProperty">Type of the property.</typeparam>
        </member>
        <member name="M:Moq.Language.IReturnsGetter`2.Returns(`1)">
            <summary>
            Specifies the value to return.
            </summary>
            <param name="value">The value to return, or <see langword="null"/>.</param>
            <example>
            Return a <c>true</c> value from the property getter call:
            <code>
            mock.SetupGet(x => x.Suspended)
                .Returns(true);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturnsGetter`2.Returns(System.Func{`1})">
            <summary>
            Specifies a function that will calculate the value to return for the property.
            </summary>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <example>
            Return a calculated value when the property is retrieved:
            <code>
            mock.SetupGet(x => x.Suspended)
                .Returns(() => returnValues[0]);
            </code>
            The lambda expression to retrieve the return value is lazy-executed, 
            meaning that its value may change depending on the moment the property  
            is retrieved and the value the <c>returnValues</c> array has at 
            that moment.
            </example>
        </member>
        <member name="M:Moq.Language.IReturnsGetter`2.CallBase">
            <summary>
            Calls the real property of the object and returns its return value.
            </summary>
            <returns>The value calculated by the real property of the object.</returns>
        </member>
        <member name="T:Moq.Language.ISetupConditionResult`1">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="M:Moq.Language.ISetupConditionResult`1.Setup(System.Linq.Expressions.Expression{System.Action{`0}})">
            <summary>
            The expectation will be considered only in the former condition.
            </summary>
            <param name="expression"></param>
            <returns></returns>
        </member>
        <member name="M:Moq.Language.ISetupConditionResult`1.Setup``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            The expectation will be considered only in the former condition.
            </summary>
            <typeparam name="TResult"></typeparam>
            <param name="expression"></param>
            <returns></returns>
        </member>
        <member name="M:Moq.Language.ISetupConditionResult`1.SetupGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Setups the get.
            </summary>
            <typeparam name="TProperty">The type of the property.</typeparam>
            <param name="expression">The expression.</param>
            <returns></returns>
        </member>
        <member name="M:Moq.Language.ISetupConditionResult`1.SetupSet``1(System.Action{`0})">
            <summary>
            Setups the set.
            </summary>
            <typeparam name="TProperty">The type of the property.</typeparam>
            <param name="setterExpression">The setter expression.</param>
            <returns></returns>
        </member>
        <member name="M:Moq.Language.ISetupConditionResult`1.SetupSet(System.Action{`0})">
            <summary>
            Setups the set.
            </summary>
            <param name="setterExpression">The setter expression.</param>
            <returns></returns>
        </member>
        <member name="T:Moq.Language.ISetupSequentialAction">
            <summary>
            Defines the <c>Pass</c> and <c>Throws</c> verbs for sequence setups
            on <c>void</c> methods.
            </summary>
        </member>
        <member name="M:Moq.Language.ISetupSequentialAction.Pass">
            <summary>
            Configures the next call in the sequence to do nothing.
            </summary>
            <example>
            The following code configures the first call to <c>Execute()</c>
            to do nothing, and the second call to throw an exception.
            <code>
            mock.SetupSequence(m => m.Execute())
               .Pass()
               .Throws&lt;InvalidOperationException&gt;();
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ISetupSequentialAction.Throws``1">
            <summary>
            Configures the next call in the sequence to throw an exception.
            </summary>
            <example>
            The following code configures the first call to <c>Execute()</c>
            to do nothing, and the second call to throw an exception.
            <code>
            mock.SetupSequence(m => m.Execute())
               .Pass()
               .Throws&lt;InvalidOperationException&gt;();
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ISetupSequentialAction.Throws(System.Exception)">
            <summary>
            Configures the next call in the sequence to throw an exception.
            </summary>
            <example>
            The following code configures the first call to <c>Execute()</c>
            to do nothing, and the second call to throw an exception.
            <code>
            mock.SetupSequence(m => m.Execute())
               .Pass()
               .Throws(new InvalidOperationException());
            </code>
            </example>
        </member>
        <member name="T:Moq.Language.ISetupSequentialResult`1">
            <summary>
            Language for ReturnSequence
            </summary>
        </member>
        <member name="M:Moq.Language.ISetupSequentialResult`1.Returns(`0)">
            <summary>
            Returns value
            </summary>
        </member>
        <member name="M:Moq.Language.ISetupSequentialResult`1.Returns(System.Func{`0})">
            <summary>
            Uses delegate to get return value
            </summary>
            <param name="valueFunction">The function that will calculate the return value.</param> 
        </member>
        <member name="M:Moq.Language.ISetupSequentialResult`1.Throws(System.Exception)">
            <summary>
            Throws an exception
            </summary>
        </member>
        <member name="M:Moq.Language.ISetupSequentialResult`1.Throws``1">
            <summary>
            Throws an exception
            </summary>
        </member>
        <member name="M:Moq.Language.ISetupSequentialResult`1.CallBase">
            <summary>
            Calls original method
            </summary>
        </member>
        <member name="T:Moq.Language.IThrows">
            <summary>
            Defines the <c>Throws</c> verb.
            </summary>
        </member>
        <member name="M:Moq.Language.IThrows.Throws(System.Exception)">
            <summary>
            Specifies the exception to throw when the method is invoked.
            </summary>
            <param name="exception">Exception instance to throw.</param>
            <example>
            This example shows how to throw an exception when the method is 
            invoked with an empty string argument:
            <code>
            mock.Setup(x =&gt; x.Execute(""))
                .Throws(new ArgumentException());
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IThrows.Throws``1">
            <summary>
            Specifies the type of exception to throw when the method is invoked.
            </summary>
            <typeparam name="TException">Type of exception to instantiate and throw when the setup is matched.</typeparam>
            <example>
            This example shows how to throw an exception when the method is 
            invoked with an empty string argument:
            <code>
            mock.Setup(x =&gt; x.Execute(""))
                .Throws&lt;ArgumentException&gt;();
            </code>
            </example>
        </member>
        <member name="T:Moq.Language.IVerifies">
            <summary>
            Defines the <c>Verifiable</c> verb.
            </summary>
        </member>
        <member name="M:Moq.Language.IVerifies.Verifiable">
            <summary>
            Marks the expectation as verifiable, meaning that a call 
            to <see cref="M:Moq.Mock.Verify"/> will check if this particular 
            expectation was met.
            </summary>
            <example>
            The following example marks the expectation as verifiable:
            <code>
            mock.Expect(x => x.Execute("ping"))
                .Returns(true)
                .Verifiable();
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IVerifies.Verifiable(System.String)">
            <summary>
            Marks the expectation as verifiable, meaning that a call 
            to <see cref="M:Moq.Mock.Verify"/> will check if this particular 
            expectation was met, and specifies a message for failures.
            </summary>
            <example>
            The following example marks the expectation as verifiable:
            <code>
            mock.Expect(x => x.Execute("ping"))
                .Returns(true)
                .Verifiable("Ping should be executed always!");
            </code>
            </example>
        </member>
        <member name="T:Moq.Language.IOccurrence">
            <summary>
            Defines occurrence members to constraint setups.
            </summary>
        </member>
        <member name="M:Moq.Language.IOccurrence.AtMostOnce">
            <summary>
            The expected invocation can happen at most once.
            </summary>
            <example>
            <code>
            var mock = new Mock&lt;ICommand&gt;();
            mock.Setup(foo => foo.Execute("ping"))
                .AtMostOnce();
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IOccurrence.AtMost(System.Int32)">
            <summary>
            The expected invocation can happen at most specified number of times.
            </summary>
            <param name="callCount">The number of times to accept calls.</param>
            <example>
            <code>
            var mock = new Mock&lt;ICommand&gt;();
            mock.Setup(foo => foo.Execute("ping"))
                .AtMost( 5 );
            </code>
            </example>
        </member>
        <member name="T:Moq.Linq.MockQueryable`1">
            <summary>
            A default implementation of IQueryable for use with QueryProvider
            </summary>
        </member>
        <member name="M:Moq.Linq.MockSetupsBuilder.ConvertToSetupReturns(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
            <summary>
              Converts a taken-apart binary expression such as `m.A.B` (==) `x` to
              `Mocks.SetupReturns(Mock.Get(m), m' => m'.A.B, (object)x)`.
            </summary>
            <param name="left">Body of the expression to set up.</param>
            <param name="right">Return value to be configured for <paramref name="left"/>.</param>
        </member>
        <member name="T:Moq.Linq.MockSetupsBuilder.ReplaceMockObjectWithParameter">
            <summary>
              Locates the root mock object in a setup expression (which is usually, but not always, a <see cref="T:System.Linq.Expressions.ParameterExpression"/>),
              stores a reference to it, and finally replaces it with a new <see cref="T:System.Linq.Expressions.ParameterExpression"/>.
            </summary>
        </member>
        <member name="T:Moq.MockRepository">
            <summary>
            Utility repository class to use to construct multiple 
            mocks when consistent verification is 
            desired for all of them.
            </summary>
            <remarks>
            If multiple mocks will be created during a test, passing 
            the desired <see cref="T:Moq.MockBehavior"/> (if different than the 
            <see cref="F:Moq.MockBehavior.Default"/> or the one 
            passed to the repository constructor) and later verifying each
            mock can become repetitive and tedious.
            <para>
            This repository class helps in that scenario by providing a 
            simplified creation of multiple mocks with a default 
            <see cref="T:Moq.MockBehavior"/> (unless overridden by calling
            <see cref="M:Moq.MockFactory.Create``1(Moq.MockBehavior)"/>) and posterior verification.
            </para>
            </remarks>
            <example group="repository">
            The following is a straightforward example on how to 
            create and automatically verify strict mocks using a <see cref="T:Moq.MockRepository"/>:
            <code>
            var repository = new MockRepository(MockBehavior.Strict);
            
            var foo = repository.Create&lt;IFoo&gt;();
            var bar = repository.Create&lt;IBar&gt;();
            
            // no need to call Verifiable() on the setup 
            // as we'll be validating all of them anyway.
            foo.Setup(f => f.Do());
            bar.Setup(b => b.Redo());
            
            // exercise the mocks here
            
            repository.VerifyAll(); 
            // At this point all setups are already checked 
            // and an optional MockException might be thrown. 
            // Note also that because the mocks are strict, any invocation 
            // that doesn't have a matching setup will also throw a MockException.
            </code>
            The following examples shows how to setup the repository 
            to create loose mocks and later verify only verifiable setups:
            <code>
            var repository = new MockRepository(MockBehavior.Loose);
            
            var foo = repository.Create&lt;IFoo&gt;();
            var bar = repository.Create&lt;IBar&gt;();
            
            // this setup will be verified when we verify the repository
            foo.Setup(f => f.Do()).Verifiable();
            	
            // this setup will NOT be verified 
            foo.Setup(f => f.Calculate());
            	
            // this setup will be verified when we verify the repository
            bar.Setup(b => b.Redo()).Verifiable();
            
            // exercise the mocks here
            // note that because the mocks are Loose, members 
            // called in the interfaces for which no matching
            // setups exist will NOT throw exceptions, 
            // and will rather return default values.
            
            repository.Verify();
            // At this point verifiable setups are already checked 
            // and an optional MockException might be thrown.
            </code>
            The following examples shows how to setup the repository with a 
            default strict behavior, overriding that default for a 
            specific mock:
            <code>
            var repository = new MockRepository(MockBehavior.Strict);
            
            // this particular one we want loose
            var foo = repository.Create&lt;IFoo&gt;(MockBehavior.Loose);
            var bar = repository.Create&lt;IBar&gt;();
            
            // specify setups
            
            // exercise the mocks here
            
            repository.Verify();
            </code>
            </example>
            <seealso cref="T:Moq.MockBehavior"/>
        </member>
        <member name="M:Moq.MockRepository.Of``1">
            <summary>
            Access the universe of mocks of the given type, to retrieve those 
            that behave according to the LINQ query specification.
            </summary>
            <typeparam name="T">The type of the mocked object to query.</typeparam>
        </member>
        <member name="M:Moq.MockRepository.Of``1(Moq.MockBehavior)">
            <summary>
            Access the universe of mocks of the given type, to retrieve those
            that behave according to the LINQ query specification.
            </summary>
            <param name="behavior">Behavior of the mocks.</param>
            <typeparam name="T">The type of the mocked object to query.</typeparam>
        </member>
        <member name="M:Moq.MockRepository.Of``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            Access the universe of mocks of the given type, to retrieve those 
            that behave according to the LINQ query specification.
            </summary>
            <param name="specification">The predicate with the setup expressions.</param>
            <typeparam name="T">The type of the mocked object to query.</typeparam>
        </member>
        <member name="M:Moq.MockRepository.Of``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},Moq.MockBehavior)">
            <summary>
            Access the universe of mocks of the given type, to retrieve those
            that behave according to the LINQ query specification.
            </summary>
            <param name="specification">The predicate with the setup expressions.</param>
            <param name="behavior">Behavior of the mocks.</param>
            <typeparam name="T">The type of the mocked object to query.</typeparam>
        </member>
        <member name="M:Moq.MockRepository.OneOf``1">
            <summary>
            Creates a mock object of the indicated type.
            </summary>
            <typeparam name="T">The type of the mocked object.</typeparam>
            <returns>The mocked object created.</returns>
        </member>
        <member name="M:Moq.MockRepository.OneOf``1(Moq.MockBehavior)">
            <summary>
            Creates a mock object of the indicated type.
            </summary>
            <param name="behavior">Behavior of the mock.</param>
            <typeparam name="T">The type of the mocked object.</typeparam>
            <returns>The mocked object created.</returns>
        </member>
        <member name="M:Moq.MockRepository.OneOf``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            Creates a mock object of the indicated type.
            </summary>
            <param name="specification">The predicate with the setup expressions.</param>
            <typeparam name="T">The type of the mocked object.</typeparam>
            <returns>The mocked object created.</returns>
        </member>
        <member name="M:Moq.MockRepository.OneOf``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},Moq.MockBehavior)">
            <summary>
            Creates a mock object of the indicated type.
            </summary>
            <param name="specification">The predicate with the setup expressions.</param>
            <param name="behavior">Behavior of the mock.</param>
            <typeparam name="T">The type of the mocked object.</typeparam>
            <returns>The mocked object created.</returns>
        </member>
        <member name="M:Moq.MockRepository.CreateMockQuery``1(Moq.MockBehavior)">
            <summary>
            Creates the mock query with the underlying queryable implementation.
            </summary>
        </member>
        <member name="M:Moq.MockRepository.CreateQueryable``1(Moq.MockBehavior)">
            <summary>
            Wraps the enumerator inside a queryable.
            </summary>
        </member>
        <member name="M:Moq.MockRepository.CreateMocks``1(Moq.MockBehavior)">
            <summary>
            Method that is turned into the actual call from .Query{T}, to 
            transform the queryable query into a normal enumerable query.
            This method is never used directly by consumers.
            </summary>
        </member>
        <member name="M:Moq.MockRepository.#ctor(Moq.MockBehavior)">
            <summary>
            Initializes the repository with the given <paramref name="defaultBehavior"/> 
            for newly created mocks from the repository.
            </summary>
            <param name="defaultBehavior">The behavior to use for mocks created 
            using the <see cref="M:Moq.MockFactory.Create``1"/> repository method if not overridden
            by using the <see cref="M:Moq.MockFactory.Create``1(Moq.MockBehavior)"/> overload.</param>
        </member>
        <member name="T:Moq.Mocks">
            <summary>
            Allows querying the universe of mocks for those that behave 
            according to the LINQ query specification.
            </summary>
        </member>
        <member name="M:Moq.Mocks.Of``1">
            <summary>
            Access the universe of mocks of the given type, to retrieve those 
            that behave according to the LINQ query specification.
            </summary>
            <typeparam name="T">The type of the mocked object to query.</typeparam>
        </member>
        <member name="M:Moq.Mocks.Of``1(Moq.MockBehavior)">
            <summary>
            Access the universe of mocks of the given type, to retrieve those
            that behave according to the LINQ query specification.
            </summary>
            <param name="behavior">Behavior of the mocks.</param>
            <typeparam name="T">The type of the mocked object to query.</typeparam>
        </member>
        <member name="M:Moq.Mocks.Of``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            Access the universe of mocks of the given type, to retrieve those 
            that behave according to the LINQ query specification.
            </summary>
            <param name="specification">The predicate with the setup expressions.</param>
            <typeparam name="T">The type of the mocked object to query.</typeparam>
        </member>
        <member name="M:Moq.Mocks.Of``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},Moq.MockBehavior)">
            <summary>
            Access the universe of mocks of the given type, to retrieve those
            that behave according to the LINQ query specification.
            </summary>
            <param name="specification">The predicate with the setup expressions.</param>
            <param name="behavior">Behavior of the mocks.</param>
            <typeparam name="T">The type of the mocked object to query.</typeparam>
        </member>
        <member name="M:Moq.Mocks.OneOf``1">
            <summary>
            Creates a mock object of the indicated type.
            </summary>
            <typeparam name="T">The type of the mocked object.</typeparam>
            <returns>The mocked object created.</returns>
        </member>
        <member name="M:Moq.Mocks.OneOf``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            Creates a mock object of the indicated type.
            </summary>
            <param name="specification">The predicate with the setup expressions.</param>
            <typeparam name="T">The type of the mocked object.</typeparam>
            <returns>The mocked object created.</returns>
        </member>
        <member name="M:Moq.Mocks.CreateMockQuery``1(Moq.MockBehavior)">
            <summary>
            Creates the mock query with the underlying queryable implementation.
            </summary>
        </member>
        <member name="M:Moq.Mocks.CreateQueryable``1(Moq.MockBehavior)">
            <summary>
            Wraps the enumerator inside a queryable.
            </summary>
        </member>
        <member name="M:Moq.Mocks.CreateMocks``1(Moq.MockBehavior)">
            <summary>
            Method that is turned into the actual call from .Query{T}, to 
            transform the queryable query into a normal enumerable query.
            This method is never used directly by consumers.
            </summary>
        </member>
        <member name="T:Moq.LookupOrFallbackDefaultValueProvider">
            <summary>
              Abstract base class for default value providers that look up and delegate to value factory functions for the requested type(s).
              If a request cannot be satisfied by any registered factory, the default value gets produced by a fallback strategy.
            </summary>
            <remarks>
              <para>
                Derived classes can register and deregister factory functions with <see cref="M:Moq.LookupOrFallbackDefaultValueProvider.Register(System.Type,System.Func{System.Type,Moq.Mock,System.Object})"/> and <see cref="M:Moq.LookupOrFallbackDefaultValueProvider.Deregister(System.Type)"/>,
                respectively.
              </para>
              <para>
                The fallback value generation strategy is implemented by the overridable <see cref="M:Moq.LookupOrFallbackDefaultValueProvider.GetFallbackDefaultValue(System.Type,Moq.Mock)"/> method.
              </para>
              <para>
                This base class sets up factory functions for task types (<see cref="T:System.Threading.Tasks.Task"/>, <see cref="T:System.Threading.Tasks.Task`1"/>,
                and <see cref="T:System.Threading.Tasks.ValueTask`1"/>) that produce completed tasks containing default values.
                If this behavior is not desired, derived classes may deregister those standard factory functions via <see cref="M:Moq.LookupOrFallbackDefaultValueProvider.Deregister(System.Type)"/>.
              </para>
            </remarks>
        </member>
        <member name="M:Moq.LookupOrFallbackDefaultValueProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Moq.LookupOrFallbackDefaultValueProvider"/> class.
            </summary>
        </member>
        <member name="M:Moq.LookupOrFallbackDefaultValueProvider.Deregister(System.Type)">
            <summary>
            Deregisters any factory function that might currently be registered for the given type(s).
            Subsequent requests for values of the given type(s) will be handled by the fallback strategy.
            </summary>
            <param name="factoryKey">The type(s) for which to remove any registered factory function.</param>
        </member>
        <member name="M:Moq.LookupOrFallbackDefaultValueProvider.Register(System.Type,System.Func{System.Type,Moq.Mock,System.Object})">
            <summary>
            Registers a factory function for the given type(s).
            Subsequent requests for values of the given type(s) will be handled by the specified function.
            </summary>
            <param name="factoryKey">
              <para>
                The type(s) for which to register the given <paramref name="factory"/> function.
              </para>
              <para>
                All array types are represented by <c><see langword="typeof"/>(<see cref="T:System.Array"/>)</c>.
                Generic types are represented by their open generic type definition, e. g. <c><see langword="typeof"/>(<see cref="T:System.Collections.IEnumerable"/>&lt;&gt;)</c>.
              </para>
            </param>
            <param name="factory">The factory function responsible for producing values for the given type.</param>
        </member>
        <member name="M:Moq.LookupOrFallbackDefaultValueProvider.GetDefaultParameterValue(System.Reflection.ParameterInfo,Moq.Mock)">
            <inheritdoc/>
        </member>
        <member name="M:Moq.LookupOrFallbackDefaultValueProvider.GetDefaultReturnValue(System.Reflection.MethodInfo,Moq.Mock)">
            <inheritdoc/>
        </member>
        <member name="M:Moq.LookupOrFallbackDefaultValueProvider.GetDefaultValue(System.Type,Moq.Mock)">
            <inheritdoc/>
        </member>
        <member name="M:Moq.LookupOrFallbackDefaultValueProvider.GetFallbackDefaultValue(System.Type,Moq.Mock)">
            <summary>
            Determines the default value for the given <paramref name="type"/> when no suitable factory is registered for it.
            May be overridden in derived classes.
            </summary>
            <param name="type">The type of which to produce a value.</param>
            <param name="mock">The <see cref="T:Moq.Mock"/> on which an unexpected invocation has occurred.</param>
        </member>
        <member name="T:Moq.Match">
             <summary>
               Allows creating custom value matchers that can be used on setups and verification,
               completely replacing the built-in <see cref="T:Moq.It"/> class with your own
               argument matching rules.
             </summary>
             <remarks>
               Argument matching is used to determine whether a concrete invocation in the mock
               matches a given setup. This matching mechanism is fully extensible.
             </remarks>
             <example>
               Creating a custom matcher is straightforward. You just need to create a method
               that returns a value from a call to <see cref="M:Moq.Match.Create``1(System.Predicate{``0})"/>
               with your matching condition and optional friendly render expression:
               <code>
                 public Order IsBigOrder()
                 {
                     return Match.Create&lt;Order&gt;(
                                o => o.GrandTotal &gt;= 5000,
                                () => IsBigOrder());  // a friendly expression to render on failures
                 }
               </code>
               This method can be used in any mock setup invocation:
               <code>
                 mock.Setup(m => m.Submit(IsBigOrder())
                     .Throws&lt;UnauthorizedAccessException&gt;();
               </code>
               At runtime, Moq knows that the return value was a matcher and
               evaluates your predicate with the actual value passed into your predicate.
               <para>
                 Another example might be a case where you want to match a lists of orders
                 that contains a particular one. You might create matcher like the following:
               </para>
               <code>
                 public static class Orders
                 {
                     public static IEnumerable&lt;Order&gt; Contains(Order order)
                     {
                         return Match.Create&lt;IEnumerable&lt;Order&gt;&gt;(orders => orders.Contains(order));
                     }
                 }
               </code>
               Now we can invoke this static method instead of an argument in an invocation:
               <code>
                 var order = new Order { ... };
                 var mock = new Mock&lt;IRepository&lt;Order&gt;&gt;();
            
                 mock.Setup(x =&gt; x.Save(Orders.Contains(order)))
                     .Throws&lt;ArgumentException&gt;();
               </code>
             </example>
        </member>
        <member name="M:Moq.Match.Matcher``1">
            <devdoc>
            Provided for the sole purpose of rendering the delegate passed to the 
            matcher constructor if no friendly render lambda is provided.
            </devdoc>
        </member>
        <member name="M:Moq.Match.Create``1(System.Predicate{``0})">
            <summary>
              Initializes the matcher with the condition that will be checked
              in order to match invocation values.
            </summary>
            <param name="condition">The condition to match against actual values.</param>
        </member>
        <member name="M:Moq.Match.Create``1(System.Predicate{``0},System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
              Initializes the matcher with the condition that will be checked
              in order to match invocation values.
            </summary>
            <param name="condition">The condition to match against actual values.</param>
            <param name="renderExpression">
              A lambda representation of the matcher, to be used when rendering error messages,
              such as <c>() => It.IsAny&lt;string&lt;()</c>.
            </param>
        </member>
        <member name="M:Moq.Match.Create``1(System.Func{System.Object,System.Type,System.Boolean},System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
              Initializes the matcher with the condition that will be checked in order to match invocation values.
              <para>
                The <paramref name="condition"/> predicate of this overload will not only be provided with a
                method argument, but also with the associated parameter's type. This parameter type essentially
                overrides <typeparamref name="T"/> in cases where the latter is a type matcher. Therefore,
                use this method overload if you want your custom matcher to work together with type matchers.
              </para>
            </summary>
            <param name="condition">
              The condition to match against actual values.
              <para>
                This function will be passed the invocation argument, as well as the type of the associated parameter.
              </para>
            </param>
            <param name="renderExpression">
              A lambda representation of the matcher.
            </param>
        </member>
        <member name="T:Moq.Match`1">
            <summary>
              Allows creating custom value matchers that can be used on setups and verification,
              completely replacing the built-in <see cref="T:Moq.It"/> class with your own
              argument matching rules.
            </summary>
            <typeparam name="T">Type of the value to match.</typeparam>
        </member>
        <member name="M:Moq.Match`1.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Moq.Match`1.Equals(Moq.Match{`0})">
            <inheritdoc/>
        </member>
        <member name="M:Moq.Match`1.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="T:Moq.MatcherObserver">
            <summary>
              A per-thread observer that records invocations to matchers for later inspection.
            </summary>
            <remarks>
              <para>
                This component requires the active cooperation of the respective subsystem.
                That is, invoked matchers call into <see cref="M:Moq.MatcherObserver.OnMatch(Moq.Match)"/> if an
                observer is active on the current thread.
              </para>
            </remarks>
        </member>
        <member name="M:Moq.MatcherObserver.GetNextTimestamp">
            <summary>
              Returns the current timestamp. The next call will return a timestamp greater than this one,
              allowing you to order invocations and matcher observations.
            </summary>
        </member>
        <member name="M:Moq.MatcherObserver.OnMatch(Moq.Match)">
            <summary>
              Adds the specified <see cref="T:Moq.Match"/> as an observation.
            </summary>
        </member>
        <member name="M:Moq.MatcherObserver.TryGetLastMatch(Moq.Match@)">
            <summary>
              Checks whether at least one <see cref="T:Moq.Match"/> observation is available,
              and if so, returns the last one.
            </summary>
            <param name="match">The observed <see cref="T:Moq.Match"/> matcher observed last.</param>
        </member>
        <member name="T:Moq.Matchers.MatcherAttributeMatcher">
            <summary>
            Matcher to treat static functions as matchers.
            
            mock.Setup(x => x.StringMethod(A.MagicString()));
            
            public static class A 
            {
                [Matcher]
                public static string MagicString() { return null; }
                public static bool MagicString(string arg)
                {
                    return arg == "magic";
                }
            }
            
            Will succeed if: mock.Object.StringMethod("magic");
            and fail with any other call.
            </summary>
        </member>
        <member name="T:Moq.MockBehavior">
            <summary>
            Options to customize the behavior of the mock. 
            </summary>
        </member>
        <member name="F:Moq.MockBehavior.Strict">
            <summary>
            Causes the mock to always throw 
            an exception for invocations that don't have a 
            corresponding setup.
            </summary>
        </member>
        <member name="F:Moq.MockBehavior.Loose">
            <summary>
            Will never throw exceptions, returning default  
            values when necessary (null for reference types, 
            zero for value types or empty enumerables and arrays).
            </summary>
        </member>
        <member name="F:Moq.MockBehavior.Default">
            <summary>
            Default mock behavior, which equals <see cref="F:Moq.MockBehavior.Loose"/>.
            </summary>
        </member>
        <member name="T:Moq.MockDefaultValueProvider">
            <summary>
            A <see cref="T:Moq.DefaultValueProvider"/> that returns an empty default value 
            for non-mockable types, and mocks for all other types (interfaces and
            non-sealed classes) that can be mocked.
            </summary>
        </member>
        <member name="T:Moq.MockException">
            <summary>
            Exception thrown by mocks when they are not properly set up,
            when setups are not matched, when verification fails, etc.
            </summary>
            <remarks>
            A distinct exception type is provided so that exceptions
            thrown by a mock can be distinguished from other exceptions
            that might be thrown in tests.
            <para>
            Moq does not provide a richer hierarchy of exception types, as
            tests typically should <em>not</em> catch or expect exceptions
            from mocks. These are typically the result of changes
            in the tested class or its collaborators' implementation, and
            result in fixes in the mock setup so that they disappear and
            allow the test to pass.
            </para>
            </remarks>
        </member>
        <member name="M:Moq.MockException.MoreThanOneCall(Moq.MethodCall,System.Int32)">
            <summary>
              Returns the exception to be thrown when a setup limited by <see cref="M:Moq.Language.IOccurrence.AtMostOnce"/> is matched more often than once.
            </summary>
        </member>
        <member name="M:Moq.MockException.MoreThanNCalls(Moq.MethodCall,System.Int32,System.Int32)">
            <summary>
              Returns the exception to be thrown when a setup limited by <see cref="M:Moq.Language.IOccurrence.AtMost(System.Int32)"/> is matched more often than the specified maximum number of times.
            </summary>
        </member>
        <member name="M:Moq.MockException.NoMatchingCalls(Moq.Mock,System.Linq.Expressions.LambdaExpression,System.String,Moq.Times,System.Int32)">
            <summary>
              Returns the exception to be thrown when <see cref="M:Moq.Mock.Verify(Moq.Mock[])"/> finds no invocations (or the wrong number of invocations) that match the specified expectation.
            </summary>
        </member>
        <member name="M:Moq.MockException.NoSetup(Moq.Invocation)">
            <summary>
              Returns the exception to be thrown when a strict mock has no setup corresponding to the specified invocation.
            </summary>
        </member>
        <member name="M:Moq.MockException.ReturnValueRequired(Moq.Invocation)">
            <summary>
              Returns the exception to be thrown when a strict mock has no setup that provides a return value for the specified invocation.
            </summary>
        </member>
        <member name="M:Moq.MockException.UnmatchedSetup(Moq.Setup)">
            <summary>
              Returns the exception to be thrown when a setup was not matched.
            </summary>
        </member>
        <member name="M:Moq.MockException.Combined(System.Collections.Generic.IEnumerable{Moq.MockException},System.String)">
            <summary>
              Returns an exception whose message is the concatenation of the given <paramref name="errors"/>' messages
              and whose reason(s) is the combination of the given <paramref name="errors"/>' reason(s).
              Used by <see cref="M:Moq.MockFactory.VerifyMocks(System.Action{Moq.Mock})"/> when it finds one or more mocks with verification errors.
            </summary>
        </member>
        <member name="M:Moq.MockException.UnverifiedInvocations(Moq.Mock,System.Collections.Generic.IEnumerable{Moq.Invocation})">
            <summary>
              Returns the exception to be thrown when <see cref="M:Moq.Mock.VerifyNoOtherCalls(Moq.Mock)"/> finds invocations that have not been verified.
            </summary>
        </member>
        <member name="P:Moq.MockException.IsVerificationError">
            <summary>
            Indicates whether this exception is a verification fault raised by Verify()
            </summary>
        </member>
        <member name="M:Moq.MockException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Supports the serialization infrastructure.
            </summary>
            <param name="info">Serialization information.</param>
            <param name="context">Streaming context.</param>
        </member>
        <member name="M:Moq.MockException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Supports the serialization infrastructure.
            </summary>
            <param name="info">Serialization information.</param>
            <param name="context">Streaming context.</param>
        </member>
        <member name="T:Moq.MockExtensions">
            <summary>
            Provides additional methods on mocks.
            </summary>
        </member>
        <member name="M:Moq.MockExtensions.Reset(Moq.Mock)">
            <summary>
            Resets this mock's state. This includes its setups, configured default return values, registered event handlers, and all recorded invocations.
            </summary>
            <param name="mock">The mock whose state should be reset.</param>
        </member>
        <member name="M:Moq.MockExtensions.ResetCalls(Moq.Mock)">
            <summary>
            Resets all invocations recorded for this mock.
            </summary>
            <param name="mock">The mock whose recorded invocations should be reset.</param>
        </member>
        <member name="T:Moq.MockSequence">
            <summary>
            Helper class to setup a full trace between many mocks
            </summary>
        </member>
        <member name="M:Moq.MockSequence.#ctor">
            <summary>
            Initialize a trace setup
            </summary>
        </member>
        <member name="P:Moq.MockSequence.Cyclic">
            <summary>
            Allow sequence to be repeated
            </summary>
        </member>
        <member name="T:Moq.MockSequenceHelper">
            <summary>
            Contains extension methods that are related to <see cref="T:Moq.MockSequence"/>.
            </summary>
        </member>
        <member name="M:Moq.MockSequenceHelper.InSequence``1(Moq.Mock{``0},Moq.MockSequence)">
            <summary>
            Perform an expectation in the trace.
            </summary>
        </member>
        <member name="T:Moq.Mock`1">
             <summary>
               Provides a mock implementation of <typeparamref name="T"/>.
             </summary>
             <typeparam name="T">Type to mock, which can be an interface, a class, or a delegate.</typeparam>
             <remarks>
               Any interface type can be used for mocking, but for classes, only abstract and virtual members can be mocked.
               <para>
                 The behavior of the mock with regards to the setups and the actual calls is determined by the optional
                 <see cref = "T:Moq.MockBehavior" /> that can be passed to the<see cref="M:Moq.Mock`1.#ctor(Moq.MockBehavior)"/> constructor.
               </para>
             </remarks>
             <example group="overview">
               The following example shows establishing setups with specific values for method invocations:
               <code>
                 // Arrange
                 var order = new Order(TALISKER, 50);
                 var warehouse = new Mock&lt;IWarehouse&gt;();
                 warehouse.Setup(w => w.HasInventory(TALISKER, 50)).Returns(true);
            
                 // Act
                 order.Fill(warehouse.Object);
            
                 // Assert
                 Assert.True(order.IsFilled);
               </code>
             </example>
             <example group="overview">
               The following example shows how to use the <see cref="T:Moq.It"/> class
               to specify conditions for arguments instead of specific values:
               <code>
                 // Arrange
                 var order = new Order(TALISKER, 50);
                 var warehouse = new Mock&lt;IWarehouse&gt;();
            
                 // shows how to expect a value within a range:
                 warehouse.Setup(x => x.HasInventory(
                                          It.IsAny&lt;string&gt;(),
                                          It.IsInRange(0, 100, Range.Inclusive)))
                          .Returns(false);
            
                 // shows how to throw for unexpected calls.
                 warehouse.Setup(x => x.Remove(
                                          It.IsAny&lt;string&gt;(),
                                          It.IsAny&lt;int&gt;()))
                          .Throws(new InvalidOperationException());
            
                 // Act
                 order.Fill(warehouse.Object);
            
                 // Assert
                 Assert.False(order.IsFilled);
               </code>
             </example>
        </member>
        <member name="M:Moq.Mock`1.#ctor(System.Boolean)">
            <summary>
            Ctor invoked by AsTInterface exclusively.
            </summary>
        </member>
        <member name="M:Moq.Mock`1.#ctor">
            <summary>
              Initializes an instance of the mock with <see cref="F:Moq.MockBehavior.Default"/> behavior.
            </summary>
            <example>
              <code>
                var mock = new Mock&lt;IFormatProvider&gt;();
              </code>
            </example>
        </member>
        <member name="M:Moq.Mock`1.#ctor(System.Object[])">
            <summary>
              Initializes an instance of the mock with <see cref="F:Moq.MockBehavior.Default"/> behavior
              and with the given constructor arguments for the class. (Only valid when <typeparamref name="T"/> is a class.)
            </summary>
            <param name="args">Optional constructor arguments if the mocked type is a class.</param>
            <remarks>
              The mock will try to find the best match constructor given the constructor arguments,
              and invoke that to initialize the instance.This applies only for classes, not interfaces.
            </remarks>
            <example>
              <code>
                var mock = new Mock&lt;MyProvider&gt;(someArgument, 25);
              </code>
            </example>
        </member>
        <member name="M:Moq.Mock`1.#ctor(Moq.MockBehavior)">
            <summary>
              Initializes an instance of the mock with the specified <see cref="T:Moq.MockBehavior"/> behavior.
            </summary>
            <param name="behavior">Behavior of the mock.</param>
            <example>
              <code>
                var mock = new Mock&lt;IFormatProvider&gt;(MockBehavior.Strict);
              </code>
            </example>
        </member>
        <member name="M:Moq.Mock`1.#ctor(Moq.MockBehavior,System.Object[])">
            <summary>
              Initializes an instance of the mock with a specific <see cref="T:Moq.MockBehavior"/> behavior
              and with the given constructor arguments for the class.
            </summary>
            <param name="behavior">Behavior of the mock.</param>
            <param name="args">Optional constructor arguments if the mocked type is a class.</param>
            <remarks>
              The mock will try to find the best match constructor given the constructor arguments,
              and invoke that to initialize the instance. This applies only to classes, not interfaces.
            </remarks>
        </member>
        <member name="M:Moq.Mock`1.#ctor(System.Linq.Expressions.Expression{System.Func{`0}},Moq.MockBehavior)">
            <summary>
              Initializes an instance of the mock using the given constructor call including its
              argument values and with a specific <see cref="T:Moq.MockBehavior"/> behavior.
            </summary>
            <param name="newExpression">Lambda expression that creates an instance of <typeparamref name="T"/>.</param>
            <param name="behavior">Behavior of the mock.</param>
            <example>
            <code>var mock = new Mock&lt;MyProvider&gt;(() => new MyProvider(someArgument, 25), MockBehavior.Loose);</code>
            </example>
        </member>
        <member name="P:Moq.Mock`1.Behavior">
            <inheritdoc/>
        </member>
        <member name="P:Moq.Mock`1.CallBase">
            <inheritdoc/>
        </member>
        <member name="P:Moq.Mock`1.DefaultValueProvider">
            <summary>
            Gets or sets the <see cref="P:Moq.Mock`1.DefaultValueProvider"/> instance that will be used
            e. g. to produce default return values for unexpected invocations.
            </summary>
        </member>
        <member name="P:Moq.Mock`1.Object">
            <summary>
              Exposes the mocked object instance.
            </summary>
        </member>
        <member name="P:Moq.Mock`1.Name">
            <summary>
              Allows naming of your mocks, so they can be easily identified in error messages (e.g. from failed assertions).
            </summary>
        </member>
        <member name="M:Moq.Mock`1.ToString">
            <summary>
              Returns the name of the mock.
            </summary>
        </member>
        <member name="M:Moq.Mock`1.OnGetObject">
            <summary>
            Returns the mocked object value.
            </summary>
        </member>
        <member name="P:Moq.Mock`1.Switches">
            <summary>
            A set of switches that influence how this mock will operate.
            You can opt in or out of certain features via this property.
            </summary>
        </member>
        <member name="M:Moq.Mock`1.As``1">
             <summary>
               Adds an interface implementation to the mock, allowing setups to be specified for it.
             </summary>
             <remarks>
               This method can only be called before the first use of the mock <see cref="P:Moq.Mock`1.Object"/> property,
               at which point the runtime type has already been generated and no more interfaces can be added to it.
               <para>
                 Also, <typeparamref name="TInterface"/> must be an interface and not a class,
                 which must be specified when creating the mock instead.
               </para>
             </remarks>
             <typeparam name="TInterface">Type of interface to cast the mock to.</typeparam>
             <exception cref="T:System.ArgumentException">The <typeparamref name="TInterface"/> specified is not an interface.</exception>
             <exception cref="T:System.InvalidOperationException">
               The mock type has already been generated by accessing the <see cref="P:Moq.Mock`1.Object"/> property.
             </exception>
             <example>
               The following example creates a mock for the main interface
               and later adds <see cref="T:System.IDisposable"/> to it to verify it's called by the consumer code:
               <code>
                 var mock = new Mock&lt;IProcessor&gt;();
                 mock.Setup(x =&gt; x.Execute("ping"));
            
                 // add IDisposable interface
                 var disposable = mock.As&lt;IDisposable&gt;();
                 disposable.Setup(d => d.Dispose())
                           .Verifiable();
               </code>
             </example>
        </member>
        <member name="M:Moq.Mock`1.Setup(System.Linq.Expressions.Expression{System.Action{`0}})">
            <summary>
              Specifies a setup on the mocked type for a call to a <see langword="void"/> method.
            </summary>
            <param name="expression">Lambda expression that specifies the expected method invocation.</param>
            <remarks>
              If more than one setup is specified for the same method or property,
              the latest one wins and is the one that will be executed.
            </remarks>
            <example group="setups">
              <code>
                var mock = new Mock&lt;IProcessor&gt;();
                mock.Setup(x => x.Execute("ping"));
              </code>
            </example>
        </member>
        <member name="M:Moq.Mock`1.Setup``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
              Specifies a setup on the mocked type for a call to a non-<see langword="void"/> (value-returning) method.
            </summary>
            <param name="expression">Lambda expression that specifies the method invocation.</param>
            <typeparam name="TResult">Type of the return value. Typically omitted as it can be inferred from the expression.</typeparam>
            <remarks>
              If more than one setup is specified for the same method or property,
              the latest one wins and is the one that will be executed.
            </remarks>
            <example group="setups">
              <code>
                mock.Setup(x => x.HasInventory("Talisker", 50))
                    .Returns(true);
              </code>
            </example>
        </member>
        <member name="M:Moq.Mock`1.SetupGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
              Specifies a setup on the mocked type for a call to a property getter.
            </summary>
            <param name="expression">Lambda expression that specifies the property getter.</param>
            <typeparam name="TProperty">Type of the property. Typically omitted as it can be inferred from the expression.</typeparam>
            <remarks>
              If more than one setup is set for the same property getter,
              the latest one wins and is the one that will be executed.
            </remarks>
            <example group="setups">
              <code>
                mock.SetupGet(x => x.Suspended)
                    .Returns(true);
              </code>
            </example>
        </member>
        <member name="M:Moq.Mock`1.SetupSet``1(System.Action{`0})">
            <summary>
              Specifies a setup on the mocked type for a call to a property setter.
            </summary>
            <param name="setterExpression">The Lambda expression that sets a property to a value.</param>
            <typeparam name="TProperty">Type of the property. Typically omitted as it can be inferred from the expression.</typeparam>
            <remarks>
              If more than one setup is set for the same property setter,
              the latest one wins and is the one that will be executed.
              <para>
                This overloads allows the use of a callback already typed for the property type.
              </para>
            </remarks>
            <example group="setups">
              <code>
                mock.SetupSet(x => x.Suspended = true);
              </code>
            </example>
        </member>
        <member name="M:Moq.Mock`1.SetupSet(System.Action{`0})">
            <summary>
              Specifies a setup on the mocked type for a call to a property setter.
            </summary>
            <param name="setterExpression">Lambda expression that sets a property to a value.</param>
            <remarks>
              If more than one setup is set for the same property setter,
              the latest one wins and is the one that will be executed.
            </remarks>
            <example group="setups">
              <code>
                mock.SetupSet(x => x.Suspended = true);
              </code>
            </example>
        </member>
        <member name="M:Moq.Mock`1.SetupAdd(System.Action{`0})">
            <summary>
              Specifies a setup on the mocked type for a call to an event add.
            </summary>
            <param name="addExpression">Lambda expression that adds an event.</param>
            <remarks>
              If more than one setup is set for the same event add,
              the latest one wins and is the one that will be executed.
            </remarks>
            <example group="setups">
              <code>
                mock.SetupAdd(x => x.EventHandler += (s, e) => {});
              </code>
            </example>
        </member>
        <member name="M:Moq.Mock`1.SetupRemove(System.Action{`0})">
            <summary>
              Specifies a setup on the mocked type for a call to an event remove.
            </summary>
            <param name="removeExpression">Lambda expression that removes an event.</param>
            <remarks>
              If more than one setup is set for the same event remove,
              the latest one wins and is the one that will be executed.
            </remarks>
            <example group="setups">
              <code>
                mock.SetupRemove(x => x.EventHandler -= (s, e) => {});
              </code>
            </example>
        </member>
        <member name="M:Moq.Mock`1.SetupProperty``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
              Specifies that the given property should have "property behavior",
              meaning that setting its value will cause it to be saved and later returned when the property is requested.
              (This is also known as "stubbing".)
            </summary>
            <param name="property">Property expression to stub.</param>
            <typeparam name="TProperty">
              Type of the property, inferred from the property expression (does not need to be specified).
            </typeparam>
            <example group="setups">
              If you have an interface with an int property <c>Value</c>,
              you might stub it using the following straightforward call:
              <code>
                var mock = new Mock&lt;IHaveValue&gt;();
                mock.SetupProperty(v => v.Value);
              </code>
              After the <c>SetupProperty</c> call has been issued, setting and retrieving
              the object value will behave as expected:
              <code>
                IHaveValue v = mock.Object;
                v.Value = 5;
                Assert.Equal(5, v.Value);
              </code>
            </example>
        </member>
        <member name="M:Moq.Mock`1.SetupProperty``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},``0)">
             <summary>
               Specifies that the given property should have "property behavior",
               meaning that setting its value will cause it to be saved and later returned when the property is requested.
               This overload allows setting the initial value for the property.
               (This is also known as "stubbing".)
             </summary>
             <param name="property">Property expression to stub.</param>
             <param name="initialValue">Initial value for the property.</param>
             <typeparam name="TProperty">
               Type of the property, inferred from the property expression (does not need to be specified).
             </typeparam>
             <example group="setups">
               If you have an interface with an int property <c>Value</c>,
               you might stub it using the following straightforward call:
               <code>
                 var mock = new Mock&lt;IHaveValue&gt;();
                 mock.SetupProperty(v => v.Value, 5);
               </code>
               After the <c>SetupProperty</c> call has been issued, setting and retrieving the object value
               will behave as expected:
               <code>
                 IHaveValue v = mock.Object;
                 Assert.Equal(5, v.Value); // Initial value was stored
            
                 // New value set which changes the initial value
                 v.Value = 6;
                 Assert.Equal(6, v.Value);
               </code>
             </example>
        </member>
        <member name="M:Moq.Mock`1.SetupAllProperties">
            <summary>
              Specifies that the all properties on the mock should have "property behavior",
              meaning that setting their value will cause them to be saved and later returned when the properties is requested.
              (This is also known as "stubbing".)
              The default value for each property will be the one generated as specified by the <see cref="P:Moq.Mock.DefaultValue"/>
              property for the mock.
            </summary>
            <remarks>
              If the mock's <see cref="P:Moq.Mock.DefaultValue"/> is set to <see cref="F:Moq.DefaultValue.Mock"/>,
              the mocked default values will also get all properties setup recursively.
            </remarks>
        </member>
        <member name="M:Moq.Mock`1.SetupSequence``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Return a sequence of values, once per call.
            </summary>
        </member>
        <member name="M:Moq.Mock`1.SetupSequence(System.Linq.Expressions.Expression{System.Action{`0}})">
            <summary>
            Performs a sequence of actions, one per call.
            </summary>
        </member>
        <member name="M:Moq.Mock`1.When(System.Func{System.Boolean})">
            <summary>
              Allows setting up a conditional setup.
              Conditional setups are only matched by an invocation
              when the specified condition evaluates to <see langword="true"/>
              at the time when the invocation occurs.
            </summary>
            <param name="condition">
              The condition that should be checked
              when a setup is being matched against an invocation.
            </param>
        </member>
        <member name="M:Moq.Mock`1.Verify(System.Linq.Expressions.Expression{System.Action{`0}})">
             <summary>
               Verifies that a specific invocation matching the given expression was performed on the mock.
               Use in conjunction with the default <see cref="F:Moq.MockBehavior.Loose"/>.
             </summary>
             <param name="expression">Expression to verify.</param>
             <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
             <example group="verification">
               This example assumes that the mock has been used, and later we want to verify
               that a given invocation with specific parameters was performed:
               <code>
                 var mock = new Mock&lt;IProcessor&gt;();
            
                 ... // exercise mock
            
                 // Will throw if the test code didn't call Execute with a "ping" string argument.
                 mock.Verify(proc => proc.Execute("ping"));
               </code>
             </example>
        </member>
        <member name="M:Moq.Mock`1.Verify(System.Linq.Expressions.Expression{System.Action{`0}},Moq.Times)">
            <summary>
              Verifies that a specific invocation matching the given expression was performed on the mock.
              Use in conjunction with the default <see cref="F:Moq.MockBehavior.Loose"/>.
            </summary>
            <param name="expression">Expression to verify.</param>
            <param name="times">The number of times a method is expected to be called.</param>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.Verify(System.Linq.Expressions.Expression{System.Action{`0}},System.Func{Moq.Times})">
            <summary>
              Verifies that a specific invocation matching the given expression was performed on the mock.
              Use in conjunction with the default <see cref="F:Moq.MockBehavior.Loose"/>.
            </summary>
            <param name="expression">Expression to verify.</param>
            <param name="times">The number of times a method is expected to be called.</param>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.Verify(System.Linq.Expressions.Expression{System.Action{`0}},System.String)">
            <summary>
              Verifies that a specific invocation matching the given expression was performed on the mock,
              specifying a failure error message.
              Use in conjunction with the default <see cref="F:Moq.MockBehavior.Loose"/>.
            </summary>
            <param name="expression">Expression to verify.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
        </member>
        <member name="M:Moq.Mock`1.Verify(System.Linq.Expressions.Expression{System.Action{`0}},Moq.Times,System.String)">
            <summary>
              Verifies that a specific invocation matching the given expression was performed on the mock,
              specifying a failure error message.
              Use in conjunction with the default <see cref="F:Moq.MockBehavior.Loose"/>.
            </summary>
            <param name="expression">Expression to verify.</param>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.Verify(System.Linq.Expressions.Expression{System.Action{`0}},System.Func{Moq.Times},System.String)">
            <summary>
              Verifies that a specific invocation matching the given expression was performed on the mock,
              specifying a failure error message.
              Use in conjunction with the default <see cref="F:Moq.MockBehavior.Loose"/>.
            </summary>
            <param name="expression">Expression to verify.</param>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.Verify``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
             <summary>
               Verifies that a specific invocation matching the given expression was performed on the mock.
               Use in conjunction with the default <see cref="F:Moq.MockBehavior.Loose"/>.
             </summary>
             <param name="expression">Expression to verify.</param>
             <typeparam name="TResult">Type of return value from the expression.</typeparam>
             <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
             <example group="verification">
               This example assumes that the mock has been used, and later we want to verify
               that a given invocation with specific parameters was performed:
               <code>
                 var mock = new Mock&lt;IWarehouse&gt;();
            
                 ... // exercise mock
            
                 // Will throw if the test code didn't call HasInventory.
                 mock.Verify(warehouse => warehouse.HasInventory(TALISKER, 50));
               </code>
             </example>
        </member>
        <member name="M:Moq.Mock`1.Verify``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},Moq.Times)">
            <summary>
              Verifies that a specific invocation matching the given expression was performed on the mock.
              Use in conjunction with the default <see cref="F:Moq.MockBehavior.Loose"/>.
            </summary>
            <param name="expression">Expression to verify.</param>
            <param name="times">The number of times a method is expected to be called.</param>
            <typeparam name="TResult">Type of return value from the expression.</typeparam>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.Verify``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{Moq.Times})">
            <summary>
              Verifies that a specific invocation matching the given expression was performed on the mock.
              Use in conjunction with the default <see cref="F:Moq.MockBehavior.Loose"/>.
            </summary>
            <param name="expression">Expression to verify.</param>
            <param name="times">The number of times a method is expected to be called.</param>
            <typeparam name="TResult">Type of return value from the expression.</typeparam>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.Verify``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.String)">
             <summary>
               Verifies that a specific invocation matching the given expression was performed on the mock,
               specifying a failure error message.
             </summary>
             <param name="expression">Expression to verify.</param>
             <param name="failMessage">Message to show if verification fails.</param>
             <typeparam name="TResult">Type of return value from the expression.</typeparam>
             <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
             <example group="verification">
               This example assumes that the mock has been used, and later we want to verify
               that a given invocation with specific parameters was performed:
               <code>
                 var mock = new Mock&lt;IWarehouse&gt;();
            
                 ... // exercise mock
            
                 // Will throw if the test code didn't call HasInventory.
                 mock.Verify(warehouse => warehouse.HasInventory(TALISKER, 50),
                             "When filling orders, inventory has to be checked");
               </code>
             </example>
        </member>
        <member name="M:Moq.Mock`1.Verify``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},Moq.Times,System.String)">
            <summary>
              Verifies that a specific invocation matching the given expression was performed on the mock,
              specifying a failure error message.
            </summary>
            <param name="expression">Expression to verify.</param>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <typeparam name="TResult">Type of return value from the expression.</typeparam>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifyGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
             <summary>
               Verifies that a property was read on the mock.
             </summary>
             <param name="expression">Expression to verify.</param>
             <typeparam name="TProperty">
               Type of the property to verify. Typically omitted as it can be inferred from the expression's return type.
             </typeparam>
             <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
             <example group="verification">
               This example assumes that the mock has been used, and later we want to verify
               that a given property was retrieved from it:
               <code>
                 var mock = new Mock&lt;IWarehouse&gt;();
            
                 ... // exercise mock
            
                 // Will throw if the test code didn't retrieve the IsClosed property.
                 mock.VerifyGet(warehouse => warehouse.IsClosed);
               </code>
             </example>
        </member>
        <member name="M:Moq.Mock`1.VerifyGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},Moq.Times)">
            <summary>
              Verifies that a property was read on the mock.
            </summary>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="expression">Expression to verify.</param>
            <typeparam name="TProperty">
              Type of the property to verify. Typically omitted as it can be inferred from the expression's return type.
            </typeparam>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifyGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{Moq.Times})">
            <summary>
              Verifies that a property was read on the mock.
            </summary>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="expression">Expression to verify.</param>
            <typeparam name="TProperty">
              Type of the property to verify. Typically omitted as it can be inferred from the expression's return type.
            </typeparam>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifyGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.String)">
            <summary>
              Verifies that a property was read on the mock, specifying a failure error message.
            </summary>
            <param name="expression">Expression to verify.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <typeparam name="TProperty">
              Type of the property to verify. Typically omitted as it can be inferred from the expression's return type.
            </typeparam>
            <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
        </member>
        <member name="M:Moq.Mock`1.VerifyGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},Moq.Times,System.String)">
            <summary>
              Verifies that a property was read on the mock, specifying a failure error message.
            </summary>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="expression">Expression to verify.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <typeparam name="TProperty">
              Type of the property to verify. Typically omitted as it can be inferred from the expression's return type.
            </typeparam>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifyGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{Moq.Times},System.String)">
            <summary>
              Verifies that a property was read on the mock, specifying a failure error message.
            </summary>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="expression">Expression to verify.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <typeparam name="TProperty">
              Type of the property to verify. Typically omitted as it can be inferred from the expression's return type.
            </typeparam>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifySet(System.Action{`0})">
             <summary>
               Verifies that a property was set on the mock.
             </summary>
             <param name="setterExpression">Expression to verify.</param>
             <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
             <example group="verification">
               This example assumes that the mock has been used, and later we want to verify
               that a given property was set on it:
               <code>
                 var mock = new Mock&lt;IWarehouse&gt;();
            
                 ... // exercise mock
            
                 // Will throw if the test code didn't set the IsClosed property.
                 mock.VerifySet(warehouse => warehouse.IsClosed = true);
               </code>
             </example>
        </member>
        <member name="M:Moq.Mock`1.VerifySet(System.Action{`0},Moq.Times)">
            <summary>
              Verifies that a property was set on the mock.
            </summary>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="setterExpression">Expression to verify.</param>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifySet(System.Action{`0},System.Func{Moq.Times})">
            <summary>
              Verifies that a property was set on the mock.
            </summary>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="setterExpression">Expression to verify.</param>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifySet(System.Action{`0},System.String)">
             <summary>
               Verifies that a property was set on the mock, specifying a failure message.
             </summary>
             <param name="setterExpression">Expression to verify.</param>
             <param name="failMessage">Message to show if verification fails.</param>
             <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
             <example>
               This example assumes that the mock has been used, and later we want to verify
               that a given property was set on it:
               <code>
                 var mock = new Mock&lt;IWarehouse&gt;();
            
                 ... // exercise mock
            
                 // Will throw if the test code didn't set the IsClosed property.
                 mock.VerifySet(warehouse =&gt; warehouse.IsClosed = true,
                                "Warehouse should always be closed after the action");
               </code>
             </example>
        </member>
        <member name="M:Moq.Mock`1.VerifySet(System.Action{`0},Moq.Times,System.String)">
            <summary>
              Verifies that a property was set on the mock, specifying a failure message.
            </summary>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="setterExpression">Expression to verify.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifySet(System.Action{`0},System.Func{Moq.Times},System.String)">
            <summary>
              Verifies that a property was set on the mock, specifying a failure message.
            </summary>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="setterExpression">Expression to verify.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifyAdd(System.Action{`0})">
             <summary>
               Verifies that an event was added to the mock.
             </summary>
             <param name="addExpression">Expression to verify.</param>
             <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
             <example group="verification">
               This example assumes that the mock has been used, and later we want to verify
               that a given event handler was subscribed to an event:
               <code>
                 var mock = new Mock&lt;IWarehouse&gt;();
            
                 ... // exercise mock
            
                 // Will throw if the test code didn't subscribe to the OnClosed event.
                 mock.VerifyAdd(warehouse => warehouse.OnClosed += It.IsAny&lt;EventHandler&gt;());
               </code>
             </example>
        </member>
        <member name="M:Moq.Mock`1.VerifyAdd(System.Action{`0},Moq.Times)">
            <summary>
              Verifies that an event was added to the mock.
            </summary>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="addExpression">Expression to verify.</param>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifyAdd(System.Action{`0},System.Func{Moq.Times})">
            <summary>
              Verifies that an event was added to the mock.
            </summary>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="addExpression">Expression to verify.</param>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifyAdd(System.Action{`0},System.String)">
            <summary>
              Verifies that an event was added to the mock, specifying a failure message.
            </summary>
            <param name="addExpression">Expression to verify.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
        </member>
        <member name="M:Moq.Mock`1.VerifyAdd(System.Action{`0},Moq.Times,System.String)">
            <summary>
              Verifies that an event was added to the mock, specifying a failure message.
            </summary>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="addExpression">Expression to verify.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifyAdd(System.Action{`0},System.Func{Moq.Times},System.String)">
            <summary>
              Verifies that an event was added to the mock, specifying a failure message.
            </summary>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="addExpression">Expression to verify.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifyRemove(System.Action{`0})">
             <summary>
               Verifies that an event was removed from the mock.
             </summary>
             <param name="removeExpression">Expression to verify.</param>
             <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
             <example group="verification">
               This example assumes that the mock has been used, and later we want to verify
               that a given event handler was removed from an event:
               <code>
                 var mock = new Mock&lt;IWarehouse&gt;();
            
                 ... // exercise mock
            
                 // Will throw if the test code didn't unsubscribe from the OnClosed event.
                 mock.VerifyRemove(warehouse => warehouse.OnClose -= It.IsAny&lt;EventHandler&gt;());
               </code>
             </example>
        </member>
        <member name="M:Moq.Mock`1.VerifyRemove(System.Action{`0},Moq.Times)">
            <summary>
              Verifies that an event was removed from the mock.
            </summary>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="removeExpression">Expression to verify.</param>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifyRemove(System.Action{`0},System.Func{Moq.Times})">
            <summary>
              Verifies that an event was removed from the mock.
            </summary>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="removeExpression">Expression to verify.</param>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifyRemove(System.Action{`0},System.String)">
            <summary>
              Verifies that an event was removed from the mock, specifying a failure message.
            </summary>
            <param name="removeExpression">Expression to verify.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
        </member>
        <member name="M:Moq.Mock`1.VerifyRemove(System.Action{`0},Moq.Times,System.String)">
            <summary>
              Verifies that an event was removed from the mock, specifying a failure message.
            </summary>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="removeExpression">Expression to verify.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifyRemove(System.Action{`0},System.Func{Moq.Times},System.String)">
            <summary>
              Verifies that an event was removed from the mock, specifying a failure message.
            </summary>
            <param name="times">The number of times a method is expected to be called.</param>
            <param name="removeExpression">Expression to verify.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <exception cref="T:Moq.MockException">
              The invocation was not called the number of times specified by <paramref name="times"/>.
            </exception>
        </member>
        <member name="M:Moq.Mock`1.VerifyNoOtherCalls">
            <summary>
            Verifies that there were no calls other than those already verified.
            </summary>
            <exception cref="T:Moq.MockException">There was at least one invocation not previously verified.</exception>
        </member>
        <member name="M:Moq.Mock`1.Raise(System.Action{`0},System.EventArgs)">
             <summary>
               Raises the event referenced in <paramref name="eventExpression"/> using the given <paramref name="args"/> argument.
             </summary>
             <exception cref="T:System.ArgumentException">
               The <paramref name="args"/> argument is invalid for the target event invocation,
               or the <paramref name="eventExpression"/> is not an event attach or detach expression.
             </exception>
             <example>
               The following example shows how to raise a
               <see cref="E:System.ComponentModel.INotifyPropertyChanged.PropertyChanged"/> event:
               <code>
                 var mock = new Mock&lt;IViewModel&gt;();
                 mock.Raise(x => x.PropertyChanged -= null, new PropertyChangedEventArgs("Name"));
               </code>
             </example>
             <example>
               This example shows how to invoke an event with a custom event arguments class
               in a view that will cause its corresponding presenter to react by changing its state:
               <code>
                 var mockView = new Mock&lt;IOrdersView&gt;();
                 var presenter = new OrdersPresenter(mockView.Object);
            
                 // Check that the presenter has no selection by default
                 Assert.Null(presenter.SelectedOrder);
            
                 // Raise the event with a specific arguments data
                 mockView.Raise(v => v.SelectionChanged += null, new OrderEventArgs { Order = new Order("moq", 500) });
            
                 // Now the presenter reacted to the event, and we have a selected order
                 Assert.NotNull(presenter.SelectedOrder);
                 Assert.Equal("moq", presenter.SelectedOrder.ProductName);
               </code>
             </example>
        </member>
        <member name="M:Moq.Mock`1.Raise(System.Action{`0},System.Object[])">
            <summary>
              Raises the event referenced in <paramref name="eventExpression"/> using the given <paramref name="args"/> argument for a non-<see cref="T:System.EventHandler"/>-typed event.
            </summary>
            <exception cref="T:System.ArgumentException">
              The <paramref name="args"/> arguments are invalid for the target event invocation,
              or the <paramref name="eventExpression"/> is not an event attach or detach expression.
            </exception>
            <example>
              The following example shows how to raise a custom event that does not adhere
              to the standard <c>EventHandler</c>:
              <code>
                var mock = new Mock&lt;IViewModel&gt;();
                mock.Raise(x => x.MyEvent -= null, "Name", bool, 25);
              </code>
            </example>
        </member>
        <member name="M:Moq.Mock`1.Expect(System.Linq.Expressions.Expression{System.Action{`0}})">
            <summary>
            Obsolete.
            </summary>
        </member>
        <member name="M:Moq.Mock`1.Expect``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Obsolete.
            </summary>
        </member>
        <member name="M:Moq.Mock`1.ExpectGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Obsolete.
            </summary>
        </member>
        <member name="M:Moq.Mock`1.ExpectSet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Obsolete.
            </summary>
        </member>
        <member name="M:Moq.Mock`1.ExpectSet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},``0)">
            <summary>
            Obsolete.
            </summary>
        </member>
        <member name="T:Moq.MatcherAttribute">
            <summary>
            Marks a method as a matcher, which allows complete replacement 
            of the built-in <see cref="T:Moq.It"/> class with your own argument 
            matching rules.
            </summary>
            <remarks>
            <para>
            <b>This feature has been deprecated in favor of the new 
            and simpler <see cref="T:Moq.Match`1"/>.
            </b>
            </para>
            <para>
            The argument matching is used to determine whether a concrete 
            invocation in the mock matches a given setup. This 
            matching mechanism is fully extensible. 
            </para>
            <para>
            There are two parts of a matcher: the compiler matcher 
            and the runtime matcher.
            <list type="bullet">
            <item>
            <term>Compiler matcher</term>
            <description>Used to satisfy the compiler requirements for the 
            argument. Needs to be a method optionally receiving any arguments 
            you might need for the matching, but with a return type that 
            matches that of the argument. 
            <para>
            Let's say I want to match a lists of orders that contains 
            a particular one. I might create a compiler matcher like the following:
            </para>
            <code>
            public static class Orders
            {
              [Matcher]
              public static IEnumerable&lt;Order&gt; Contains(Order order)
              {
                return null;
              }
            }
            </code>
            Now we can invoke this static method instead of an argument in an 
            invocation:
            <code>
            var order = new Order { ... };
            var mock = new Mock&lt;IRepository&lt;Order&gt;&gt;();
            
            mock.Setup(x =&gt; x.Save(Orders.Contains(order)))
                .Throws&lt;ArgumentException&gt;();
            </code>
            Note that the return value from the compiler matcher is irrelevant. 
            This method will never be called, and is just used to satisfy the 
            compiler and to signal Moq that this is not a method that we want 
            to be invoked at runtime.
            </description>
            </item>
            <item>
            <term>Runtime matcher</term>
            <description>
            The runtime matcher is the one that will actually perform evaluation 
            when the test is run, and is defined by convention to have the 
            same signature as the compiler matcher, but where the return 
            value is the first argument to the call, which contains the 
            object received by the actual invocation at runtime:
            <code>
              public static bool Contains(IEnumerable&lt;Order&gt; orders, Order order)
              {
                return orders.Contains(order);
              }
            </code>
            At runtime, the mocked method will be invoked with a specific 
            list of orders. This value will be passed to this runtime 
            matcher as the first argument, while the second argument is the 
            one specified in the setup (<c>x.Save(Orders.Contains(order))</c>).
            <para>
            The boolean returned determines whether the given argument has been 
            matched. If all arguments to the expected method are matched, then 
            the setup matches and is evaluated.
            </para>
            </description>
            </item>
            </list>
            </para>
            Using this extensible infrastructure, you can easily replace the entire 
            <see cref="T:Moq.It"/> set of matchers with your own. You can also avoid the 
            typical (and annoying) lengthy expressions that result when you have 
            multiple arguments that use generics.
            </remarks>
            <example>
            The following is the complete example explained above:
            <code>
            public static class Orders
            {
              [Matcher]
              public static IEnumerable&lt;Order&gt; Contains(Order order)
              {
                return null;
              }
              
              public static bool Contains(IEnumerable&lt;Order&gt; orders, Order order)
              {
                return orders.Contains(order);
              }
            }
            </code>
            And the concrete test using this matcher:
            <code>
            var order = new Order { ... };
            var mock = new Mock&lt;IRepository&lt;Order&gt;&gt;();
            
            mock.Setup(x =&gt; x.Save(Orders.Contains(order)))
                .Throws&lt;ArgumentException&gt;();
                
            // use mock, invoke Save, and have the matcher filter.
            </code>
            </example>
        </member>
        <member name="T:Moq.MockLegacyExtensions">
            <summary>
            Contains obsolete API members as extension methods so that existing code continues to compile,
            but new code doesn't see them.
            </summary>
        </member>
        <member name="M:Moq.MockLegacyExtensions.SetupSet``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},``1)">
            <summary>
            Obsolete.
            </summary>
        </member>
        <member name="M:Moq.MockLegacyExtensions.VerifySet``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},``1)">
            <summary>
            Obsolete.
            </summary>
        </member>
        <member name="M:Moq.MockLegacyExtensions.VerifySet``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},``1,System.String)">
            <summary>
            Obsolete.
            </summary>
        </member>
        <member name="T:Moq.MockFactory">
            <summary>
            Utility factory class to use to construct multiple 
            mocks when consistent verification is 
            desired for all of them.
            </summary>
            <remarks>
            If multiple mocks will be created during a test, passing 
            the desired <see cref="T:Moq.MockBehavior"/> (if different than the 
            <see cref="F:Moq.MockBehavior.Default"/> or the one 
            passed to the factory constructor) and later verifying each
            mock can become repetitive and tedious.
            <para>
            This factory class helps in that scenario by providing a 
            simplified creation of multiple mocks with a default 
            <see cref="T:Moq.MockBehavior"/> (unless overridden by calling
            <see cref="M:Moq.MockFactory.Create``1(Moq.MockBehavior)"/>) and posterior verification.
            </para>
            </remarks>
            <example group="factory">
            The following is a straightforward example on how to 
            create and automatically verify strict mocks using a <see cref="T:Moq.MockFactory"/>:
            <code>
            var factory = new MockFactory(MockBehavior.Strict);
            
            var foo = factory.Create&lt;IFoo&gt;();
            var bar = factory.Create&lt;IBar&gt;();
            
            // no need to call Verifiable() on the setup 
            // as we'll be validating all of them anyway.
            foo.Setup(f => f.Do());
            bar.Setup(b => b.Redo());
            
            // exercise the mocks here
            
            factory.VerifyAll(); 
            // At this point all setups are already checked 
            // and an optional MockException might be thrown. 
            // Note also that because the mocks are strict, any invocation 
            // that doesn't have a matching setup will also throw a MockException.
            </code>
            The following examples shows how to setup the factory 
            to create loose mocks and later verify only verifiable setups:
            <code>
            var factory = new MockFactory(MockBehavior.Loose);
            
            var foo = factory.Create&lt;IFoo&gt;();
            var bar = factory.Create&lt;IBar&gt;();
            
            // this setup will be verified when we verify the factory
            foo.Setup(f => f.Do()).Verifiable();
            	
            // this setup will NOT be verified 
            foo.Setup(f => f.Calculate());
            	
            // this setup will be verified when we verify the factory
            bar.Setup(b => b.Redo()).Verifiable();
            
            // exercise the mocks here
            // note that because the mocks are Loose, members 
            // called in the interfaces for which no matching
            // setups exist will NOT throw exceptions, 
            // and will rather return default values.
            
            factory.Verify();
            // At this point verifiable setups are already checked 
            // and an optional MockException might be thrown.
            </code>
            The following examples shows how to setup the factory with a 
            default strict behavior, overriding that default for a 
            specific mock:
            <code>
            var factory = new MockFactory(MockBehavior.Strict);
            
            // this particular one we want loose
            var foo = factory.Create&lt;IFoo&gt;(MockBehavior.Loose);
            var bar = factory.Create&lt;IBar&gt;();
            
            // specify setups
            
            // exercise the mocks here
            
            factory.Verify();
            </code>
            </example>
            <seealso cref="T:Moq.MockBehavior"/>
        </member>
        <member name="M:Moq.MockFactory.#ctor(Moq.MockBehavior)">
            <summary>
            Initializes the factory with the given <paramref name="defaultBehavior"/> 
            for newly created mocks from the factory.
            </summary>
            <param name="defaultBehavior">The behavior to use for mocks created 
            using the <see cref="M:Moq.MockFactory.Create``1"/> factory method if not overridden
            by using the <see cref="M:Moq.MockFactory.Create``1(Moq.MockBehavior)"/> overload.</param>
        </member>
        <member name="P:Moq.MockFactory.Behavior">
            <summary>
              Gets the default <see cref="T:Moq.MockBehavior"/> of mocks created by this repository.
            </summary>
        </member>
        <member name="P:Moq.MockFactory.CallBase">
            <summary>
            Whether the base member virtual implementation will be called 
            for mocked classes if no setup is matched. Defaults to <see langword="false"/>.
            </summary>
        </member>
        <member name="P:Moq.MockFactory.DefaultValue">
            <summary>
            Specifies the behavior to use when returning default values for 
            unexpected invocations on loose mocks.
            </summary>
        </member>
        <member name="P:Moq.MockFactory.DefaultValueProvider">
            <summary>
            Gets or sets the <see cref="T:Moq.DefaultValueProvider"/> instance that will be used
            e. g. to produce default return values for unexpected invocations.
            </summary>
        </member>
        <member name="P:Moq.MockFactory.Mocks">
            <summary>
            Gets the mocks that have been created by this factory and 
            that will get verified together.
            </summary>
        </member>
        <member name="P:Moq.MockFactory.Switches">
            <summary>
            A set of switches that influence how mocks created by this factory will operate.
            You can opt in or out of certain features via this property.
            </summary>
        </member>
        <member name="M:Moq.MockFactory.Create``1">
            <summary>
            Creates a new mock with the default <see cref="T:Moq.MockBehavior"/> 
            specified at factory construction time.
            </summary>
            <typeparam name="T">Type to mock.</typeparam>
            <returns>A new <see cref="T:Moq.Mock`1"/>.</returns>
            <example ignore="true">
            <code>
            var factory = new MockFactory(MockBehavior.Strict);
            
            var foo = factory.Create&lt;IFoo&gt;();
            // use mock on tests
            
            factory.VerifyAll();
            </code>
            </example>
        </member>
        <member name="M:Moq.MockFactory.Create``1(System.Object[])">
            <summary>
            Creates a new mock with the default <see cref="T:Moq.MockBehavior"/> 
            specified at factory construction time and with the 
            the given constructor arguments for the class.
            </summary>
            <remarks>
            The mock will try to find the best match constructor given the 
            constructor arguments, and invoke that to initialize the instance. 
            This applies only to classes, not interfaces.
            </remarks>
            <typeparam name="T">Type to mock.</typeparam>
            <param name="args">Constructor arguments for mocked classes.</param>
            <returns>A new <see cref="T:Moq.Mock`1"/>.</returns>
            <example ignore="true">
            <code>
            var factory = new MockFactory(MockBehavior.Default);
            
            var mock = factory.Create&lt;MyBase&gt;("Foo", 25, true);
            // use mock on tests
            
            factory.Verify();
            </code>
            </example>
        </member>
        <member name="M:Moq.MockFactory.Create``1(Moq.MockBehavior)">
            <summary>
            Creates a new mock with the given <paramref name="behavior"/>.
            </summary>
            <typeparam name="T">Type to mock.</typeparam>
            <param name="behavior">Behavior to use for the mock, which overrides 
            the default behavior specified at factory construction time.</param>
            <returns>A new <see cref="T:Moq.Mock`1"/>.</returns>
            <example group="factory">
            The following example shows how to create a mock with a different 
            behavior to that specified as the default for the factory:
            <code>
            var factory = new MockFactory(MockBehavior.Strict);
            
            var foo = factory.Create&lt;IFoo&gt;(MockBehavior.Loose);
            </code>
            </example>
        </member>
        <member name="M:Moq.MockFactory.Create``1(Moq.MockBehavior,System.Object[])">
            <summary>
            Creates a new mock with the given <paramref name="behavior"/> 
            and with the given constructor arguments for the class.
            </summary>
            <remarks>
            The mock will try to find the best match constructor given the 
            constructor arguments, and invoke that to initialize the instance. 
            This applies only to classes, not interfaces.
            </remarks>
            <typeparam name="T">Type to mock.</typeparam>
            <param name="behavior">Behavior to use for the mock, which overrides 
            the default behavior specified at factory construction time.</param>
            <param name="args">Constructor arguments for mocked classes.</param>
            <returns>A new <see cref="T:Moq.Mock`1"/>.</returns>
            <example group="factory">
            The following example shows how to create a mock with a different 
            behavior to that specified as the default for the factory, passing 
            constructor arguments:
            <code>
            var factory = new MockFactory(MockBehavior.Default);
            
            var mock = factory.Create&lt;MyBase&gt;(MockBehavior.Strict, "Foo", 25, true);
            </code>
            </example>
        </member>
        <member name="M:Moq.MockFactory.Create``1(System.Linq.Expressions.Expression{System.Func{``0}},Moq.MockBehavior)">
            <summary>
            Creates an instance of the mock using the given constructor call including its
            argument values and with a specific <see cref="T:Moq.MockBehavior"/> behavior.
            </summary>
            <typeparam name="T">Type to mock.</typeparam>
            <param name="newExpression">Lambda expression that creates an instance of <typeparamref name="T"/>.</param>
            <param name="behavior">Behavior of the mock.</param>
            <returns>A new <see cref="T:Moq.Mock`1"/>.</returns>
            <example ignore="true">
            <code>
            var factory = new MockFactory(MockBehavior.Default);
            
            var mock = factory.Create&lt;MyClass&gt;(() => new MyClass("Foo", 25, true), MockBehavior.Loose);
            // use mock on tests
            
            factory.Verify();
            </code>
            </example>
        </member>
        <member name="M:Moq.MockFactory.CreateMock``1(Moq.MockBehavior,System.Object[])">
            <summary>
            Implements creation of a new mock within the factory.
            </summary>
            <typeparam name="T">Type to mock.</typeparam>
            <param name="behavior">The behavior for the new mock.</param>
            <param name="args">Optional arguments for the construction of the mock.</param>
        </member>
        <member name="M:Moq.MockFactory.Verify">
            <summary>
            Verifies all verifiable setups on all mocks created by this factory.
            </summary>
            <seealso cref="M:Moq.Mock.Verify"/>
            <exception cref="T:Moq.MockException">One or more mocks had setups that were not satisfied.</exception>
        </member>
        <member name="M:Moq.MockFactory.VerifyAll">
            <summary>
            Verifies all setups on all mocks created by this factory.
            </summary>
            <seealso cref="M:Moq.Mock.Verify"/>
            <exception cref="T:Moq.MockException">One or more mocks had setups that were not satisfied.</exception>
        </member>
        <member name="M:Moq.MockFactory.VerifyNoOtherCalls">
            <summary>
            Calls <see cref="M:Moq.Mock`1.VerifyNoOtherCalls"/> on all mocks created by this factory.
            </summary>
            <seealso cref="M:Moq.Mock`1.VerifyNoOtherCalls"/>
            <exception cref="T:Moq.MockException">One or more mocks had invocations that were not verified.</exception>
        </member>
        <member name="M:Moq.MockFactory.VerifyMocks(System.Action{Moq.Mock})">
            <summary>
            Invokes <paramref name="verifyAction"/> for each mock
            in <see cref="P:Moq.MockFactory.Mocks"/>, and accumulates the resulting
            verification exceptions that might be
            thrown from the action.
            </summary>
            <param name="verifyAction">The action to execute against 
            each mock.</param>
        </member>
        <member name="T:Moq.ObsoleteMockExtensions">
            <summary>
            Provides additional methods on mocks.
            </summary>
            <devdoc>
            Provided as extension methods as they confuse the compiler 
            with the overloads taking Action.
            </devdoc>
        </member>
        <member name="M:Moq.ObsoleteMockExtensions.SetupSet``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Specifies a setup on the mocked type for a call to 
            to a property setter, regardless of its value.
            </summary>
            <remarks>
            If more than one setup is set for the same property setter, 
            the latest one wins and is the one that will be executed.
            </remarks>
            <typeparam name="TProperty">Type of the property. Typically omitted as it can be inferred from the expression.</typeparam>
            <typeparam name="T">Type of the mock.</typeparam>
            <param name="mock">The target mock for the setup.</param>
            <param name="expression">Lambda expression that specifies the property setter.</param>
            <example group="setups">
            <code>
            mock.SetupSet(x =&gt; x.Suspended);
            </code>
            </example>
            <devdoc>
            This method is not legacy, but must be on an extension method to avoid 
            confusing the compiler with the new Action syntax.
            </devdoc>
        </member>
        <member name="M:Moq.ObsoleteMockExtensions.VerifySet``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Verifies that a property has been set on the mock, regardless of its value.
            </summary>
            <example group="verification">
            This example assumes that the mock has been used, 
            and later we want to verify that a given invocation 
            with specific parameters was performed:
            <code>
            var mock = new Mock&lt;IWarehouse&gt;();
            // exercise mock
            //...
            // Will throw if the test code didn't set the IsClosed property.
            mock.VerifySet(warehouse =&gt; warehouse.IsClosed);
            </code>
            </example>
            <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
            <param name="expression">Expression to verify.</param>
            <param name="mock">The mock instance.</param>
            <typeparam name="T">Mocked type.</typeparam>
            <typeparam name="TProperty">Type of the property to verify. Typically omitted as it can 
            be inferred from the expression's return type.</typeparam>
        </member>
        <member name="M:Moq.ObsoleteMockExtensions.VerifySet``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.String)">
            <summary>
            Verifies that a property has been set on the mock, specifying a failure  
            error message. 
            </summary>
            <example group="verification">
            This example assumes that the mock has been used, 
            and later we want to verify that a given invocation 
            with specific parameters was performed:
            <code>
            var mock = new Mock&lt;IWarehouse&gt;();
            // exercise mock
            //...
            // Will throw if the test code didn't set the IsClosed property.
            mock.VerifySet(warehouse =&gt; warehouse.IsClosed);
            </code>
            </example>
            <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
            <param name="expression">Expression to verify.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <param name="mock">The mock instance.</param>
            <typeparam name="T">Mocked type.</typeparam>
            <typeparam name="TProperty">Type of the property to verify. Typically omitted as it can 
            be inferred from the expression's return type.</typeparam>
        </member>
        <member name="M:Moq.ObsoleteMockExtensions.VerifySet``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},Moq.Times)">
            <summary>
            Verifies that a property has been set on the mock, regardless 
            of the value but only the specified number of times.
            </summary>
            <example group="verification">
            This example assumes that the mock has been used, 
            and later we want to verify that a given invocation 
            with specific parameters was performed:
            <code>
            var mock = new Mock&lt;IWarehouse&gt;();
            // exercise mock
            //...
            // Will throw if the test code didn't set the IsClosed property.
            mock.VerifySet(warehouse =&gt; warehouse.IsClosed);
            </code>
            </example>
            <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by
            <paramref name="times"/>.</exception>
            <param name="mock">The mock instance.</param>
            <typeparam name="T">Mocked type.</typeparam>
            <param name="times">The number of times a method is allowed to be called.</param>
            <param name="expression">Expression to verify.</param>
            <typeparam name="TProperty">Type of the property to verify. Typically omitted as it can 
            be inferred from the expression's return type.</typeparam>
        </member>
        <member name="M:Moq.ObsoleteMockExtensions.VerifySet``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},Moq.Times,System.String)">
            <summary>
            Verifies that a property has been set on the mock, regardless 
            of the value but only the specified number of times, and specifying a failure  
            error message. 
            </summary>
            <example group="verification">
            This example assumes that the mock has been used, 
            and later we want to verify that a given invocation 
            with specific parameters was performed:
            <code>
            var mock = new Mock&lt;IWarehouse&gt;();
            // exercise mock
            //...
            // Will throw if the test code didn't set the IsClosed property.
            mock.VerifySet(warehouse =&gt; warehouse.IsClosed);
            </code>
            </example>
            <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by
            <paramref name="times"/>.</exception>
            <param name="mock">The mock instance.</param>
            <typeparam name="T">Mocked type.</typeparam>
            <param name="times">The number of times a method is allowed to be called.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <param name="expression">Expression to verify.</param>
            <typeparam name="TProperty">Type of the property to verify. Typically omitted as it can 
            be inferred from the expression's return type.</typeparam>
        </member>
        <member name="T:Moq.SequenceExtensions">
            <summary>
            Helper for sequencing return values in the same method.
            </summary>
        </member>
        <member name="M:Moq.SequenceExtensions.SetupSequence``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Return a sequence of values, once per call.
            </summary>
        </member>
        <member name="M:Moq.SequenceExtensions.SetupSequence``1(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Action{``0}})">
            <summary>
            Performs a sequence of actions, one per call.
            </summary>
        </member>
        <member name="M:Moq.SequenceExtensions.ReturnsAsync``1(Moq.Language.ISetupSequentialResult{System.Threading.Tasks.Task{``0}},``0)">
            <summary>
            Return a sequence of tasks, once per call.
            </summary>
        </member>
        <member name="M:Moq.SequenceExtensions.ReturnsAsync``1(Moq.Language.ISetupSequentialResult{System.Threading.Tasks.Task{``0}},System.Func{``0})">
            <summary>
            Return a sequence of tasks, once per call.
            </summary>
        </member>
        <member name="M:Moq.SequenceExtensions.ReturnsAsync``1(Moq.Language.ISetupSequentialResult{System.Threading.Tasks.ValueTask{``0}},``0)">
            <summary>
            Return a sequence of tasks, once per call.
            </summary>
        </member>
        <member name="M:Moq.SequenceExtensions.ReturnsAsync``1(Moq.Language.ISetupSequentialResult{System.Threading.Tasks.ValueTask{``0}},System.Func{``0})">
            <summary>
            Return a sequence of tasks, once per call.
            </summary>
        </member>
        <member name="M:Moq.SequenceExtensions.PassAsync(Moq.Language.ISetupSequentialResult{System.Threading.Tasks.Task})">
            <summary>
            Return a sequence of tasks, once per call.
            </summary>
        </member>
        <member name="M:Moq.SequenceExtensions.PassAsync(Moq.Language.ISetupSequentialResult{System.Threading.Tasks.ValueTask})">
            <summary>
            Return a sequence of tasks, once per call.
            </summary>
        </member>
        <member name="M:Moq.SequenceExtensions.ThrowsAsync``1(Moq.Language.ISetupSequentialResult{System.Threading.Tasks.Task{``0}},System.Exception)">
            <summary>
            Throws a sequence of exceptions, once per call.
            </summary>
        </member>
        <member name="M:Moq.SequenceExtensions.ThrowsAsync``1(Moq.Language.ISetupSequentialResult{System.Threading.Tasks.ValueTask{``0}},System.Exception)">
            <summary>
            Throws a sequence of exceptions, once per call.
            </summary>
        </member>
        <member name="M:Moq.SequenceExtensions.ThrowsAsync(Moq.Language.ISetupSequentialResult{System.Threading.Tasks.Task},System.Exception)">
            <summary>
            Throws a sequence of exceptions, once per call.
            </summary>
        </member>
        <member name="M:Moq.SequenceExtensions.ThrowsAsync(Moq.Language.ISetupSequentialResult{System.Threading.Tasks.ValueTask},System.Exception)">
            <summary>
            Throws a sequence of exceptions, once per call.
            </summary>
        </member>
        <member name="T:Moq.ParameterTypes">
            <summary>
              Allocation-free adapter type for treating a `ParameterInfo[]` array like a `Type[]` array.
            </summary>
        </member>
        <member name="T:Moq.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.AlreadyInitialized">
            <summary>
              Looks up a localized string similar to Mock type has already been initialized by accessing its Object property. Adding interfaces must be done before that..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.ArgumentCannotBeEmpty">
            <summary>
              Looks up a localized string similar to Value cannot be an empty string..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.ArgumentMatcherWillNeverMatch">
            <summary>
              Looks up a localized string similar to Matcher &apos;{0}&apos; is unmatchable: An implicit conversion operator will convert arguments of type &apos;{1}&apos; to the parameter&apos;s type &apos;{2}&apos;, which is assignment-incompatible..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.AsMustBeInterface">
            <summary>
              Looks up a localized string similar to Can only add interfaces to the mock..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.CallBaseCannotBeUsedWithDelegateMocks">
            <summary>
              Looks up a localized string similar to CallBase cannot be used with Delegate mocks..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.CantSetReturnValueForVoid">
            <summary>
              Looks up a localized string similar to Can&apos;t set return value for void method {0}..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.ConstructorArgsForDelegate">
            <summary>
              Looks up a localized string similar to Constructor arguments cannot be passed for delegate mocks..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.ConstructorArgsForInterface">
            <summary>
              Looks up a localized string similar to Constructor arguments cannot be passed for interface mocks..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.ConstructorNotFound">
            <summary>
              Looks up a localized string similar to A matching constructor for the given arguments was not found on the mocked type..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.DelaysMustBeGreaterThanZero">
            <summary>
              Looks up a localized string similar to Delays have to be greater than zero to ensure an async callback is used..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.FieldsNotSupported">
            <summary>
              Looks up a localized string similar to Expression {0} involves a field access, which is not supported. Use properties instead..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.InvalidCallbackNotADelegateWithReturnTypeVoid">
            <summary>
              Looks up a localized string similar to Invalid callback. This overload of the &quot;Callback&quot; method only accepts &quot;void&quot; (C#) or &quot;Sub&quot; (VB.NET) delegates with parameter types matching those of the set up method..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.InvalidCallbackParameterCountMismatch">
            <summary>
              Looks up a localized string similar to Invalid callback. Setup on method with {0} parameter(s) cannot invoke callback with different number of parameters ({1})..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.InvalidCallbackParameterMismatch">
            <summary>
              Looks up a localized string similar to Invalid callback. Setup on method with parameters ({0}) cannot invoke callback with parameters ({1})..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.InvalidCallbackReturnTypeMismatch">
            <summary>
              Looks up a localized string similar to Invalid callback. Setup on method with return type &apos;{0}&apos; cannot invoke callback with return type &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.InvalidMockGetType">
             <summary>
               Looks up a localized string similar to Cannot retrieve a mock with the given object type {0} as it&apos;s not the main type of the mock or any of its additional interfaces.
            Please cast the argument to one of the supported types: {1}.
            Remember that there&apos;s no generics covariance in the CLR, so your object must be one of these types in order for the call to succeed..
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.InvalidReturnsCallbackNotADelegateWithReturnType">
            <summary>
              Looks up a localized string similar to Invalid callback. This overload of the &quot;Returns&quot; method only accepts non-&quot;void&quot; (C#) or &quot;Function&quot; (VB.NET) delegates with parameter types matching those of the set up method..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.LastMemberHasNonInterceptableReturnType">
            <summary>
              Looks up a localized string similar to The return type of the last member shown above is not mockable..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.LinqBinaryOperatorNotSupported">
            <summary>
              Looks up a localized string similar to The equals (&quot;==&quot; or &quot;=&quot; in VB) and the conditional &apos;and&apos; (&quot;&amp;&amp;&quot; or &quot;AndAlso&quot; in VB) operators are the only ones supported in the query specification expression. Unsupported expression: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.LinqMethodNotSupported">
            <summary>
              Looks up a localized string similar to LINQ method &apos;{0}&apos; not supported..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.LinqMethodNotVirtual">
            <summary>
              Looks up a localized string similar to Expression contains a call to a method which is not virtual (overridable in VB) or abstract. Unsupported expression: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.MatcherAssignmentFailedDuringExpressionReconstruction">
             <summary>
               Looks up a localized string similar to Could not determine the correct positions for all argument matchers ({0} in total) used in a call to this method: {1}.
            This could be caused by an unrecognized type conversion, coercion, narrowing, or widening, and is most likely a bug in Moq. Please report your use case to the Moq team..
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.MemberMissing">
            <summary>
              Looks up a localized string similar to Member {0}.{1} does not exist..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.MethodIsPublic">
             <summary>
               Looks up a localized string similar to Method {0}.{1} is public. Use strong-typed Expect overload instead:
            mock.Setup(x =&gt; x.{1}());
            .
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.MethodMissing">
            <summary>
              Looks up a localized string similar to No protected method {0}.{1} found whose signature is compatible with the provided arguments ({2})..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.MethodNotVisibleToProxyFactory">
             <summary>
               Looks up a localized string similar to Cannot set up {0}.{1} because it is not accessible to the proxy generator used by Moq:
            {2}.
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.MinDelayMustBeLessThanMaxDelay">
            <summary>
              Looks up a localized string similar to Minimum delay has to be lower than maximum delay..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.MockExceptionMessage">
             <summary>
               Looks up a localized string similar to {0} invocation failed with mock behavior {1}.
            {2}.
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NextMemberNonInterceptable">
            <summary>
              Looks up a localized string similar to The next member after the last one shown above is non-virtual, sealed, or not visible to the proxy factory..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoConstructorCallFound">
            <summary>
              Looks up a localized string similar to No constructor call could be found..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoInvocationsPerformed">
            <summary>
              Looks up a localized string similar to No invocations performed..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsAtLeast">
            <summary>
              Looks up a localized string similar to Expected invocation on the mock at least {0} times, but was {2} times: .
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsAtLeastOnce">
            <summary>
              Looks up a localized string similar to Expected invocation on the mock at least once, but was never performed: .
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsAtMost">
            <summary>
              Looks up a localized string similar to Expected invocation on the mock at most {1} times, but was {2} times: .
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsAtMostOnce">
            <summary>
              Looks up a localized string similar to Expected invocation on the mock at most once, but was {2} times: .
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsBetweenExclusive">
            <summary>
              Looks up a localized string similar to Expected invocation on the mock between {0} and {1} times (Exclusive), but was {2} times: .
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsBetweenInclusive">
            <summary>
              Looks up a localized string similar to Expected invocation on the mock between {0} and {1} times (Inclusive), but was {2} times: .
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsExactly">
            <summary>
              Looks up a localized string similar to Expected invocation on the mock exactly {0} times, but was {2} times: .
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsNever">
            <summary>
              Looks up a localized string similar to Expected invocation on the mock should never have been performed, but was {2} times: .
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsOnce">
            <summary>
              Looks up a localized string similar to Expected invocation on the mock once, but was {2} times: .
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoSetup">
            <summary>
              Looks up a localized string similar to All invocations on the mock must have a corresponding setup..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.ObjectInstanceNotMock">
            <summary>
              Looks up a localized string similar to Object instance was not created by Moq..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.OutExpressionMustBeConstantValue">
            <summary>
              Looks up a localized string similar to Out expression must evaluate to a constant value..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.PerformedInvocations">
            <summary>
              Looks up a localized string similar to Performed invocations:.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.PropertyGetNotFound">
            <summary>
              Looks up a localized string similar to Property {0}.{1} does not have a getter..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.PropertySetNotFound">
            <summary>
              Looks up a localized string similar to Property {0}.{1} does not have a setter..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.ProtectedMemberNotFound">
            <summary>
              Looks up a localized string similar to Type {0} does not have matching protected member: {1}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.RefExpressionMustBeConstantValue">
            <summary>
              Looks up a localized string similar to Ref expression must evaluate to a constant value..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.ReturnValueRequired">
            <summary>
              Looks up a localized string similar to Invocation needs to return a value and therefore must have a corresponding setup that provides it..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.SetupNotEventAdd">
            <summary>
              Looks up a localized string similar to Expression is not an event add: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.SetupNotEventRemove">
            <summary>
              Looks up a localized string similar to Expression is not an event remove: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.SetupNotProperty">
            <summary>
              Looks up a localized string similar to Expression is not a property access: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.SetupNotSetter">
            <summary>
              Looks up a localized string similar to Expression is not a setter: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.TypeHasNoDefaultConstructor">
            <summary>
              Looks up a localized string similar to Type {0} does not have a default (public parameterless) constructor..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.TypeNotImplementInterface">
            <summary>
              Looks up a localized string similar to Type {0} does not implement required interface {1}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.TypeNotMockable">
            <summary>
              Looks up a localized string similar to Type to mock ({0}) must be an interface, a delegate, or a non-sealed, non-static class..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnexpectedPublicProperty">
             <summary>
               Looks up a localized string similar to To specify a setup for public property {0}.{1}, use the typed overloads, such as:
            mock.Setup(x =&gt; x.{1}).Returns(value);
            mock.SetupGet(x =&gt; x.{1}).Returns(value); //equivalent to previous one
            mock.SetupSet(x =&gt; x.{1}).Callback(callbackDelegate);
            .
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnexpectedTranslationOfMemberAccess">
            <summary>
              Looks up a localized string similar to Unexpected translation of a member access: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnhandledBindingType">
            <summary>
              Looks up a localized string similar to Unhandled binding type: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnhandledExpressionType">
            <summary>
              Looks up a localized string similar to Unhandled expression type: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnmatchedSetup">
             <summary>
               Looks up a localized string similar to {0}:
            This setup was not matched..
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnsupportedExpression">
            <summary>
              Looks up a localized string similar to Unsupported expression: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnsupportedExpressionWithHint">
             <summary>
               Looks up a localized string similar to Unsupported expression: {0}
            {1}.
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnsupportedExtensionMethod">
            <summary>
              Looks up a localized string similar to Extension methods (here: {0}) may not be used in setup / verification expressions..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnsupportedMember">
            <summary>
              Looks up a localized string similar to Member {0} is not supported for protected mocking..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnsupportedNonOverridableMember">
            <summary>
              Looks up a localized string similar to Non-overridable members (here: {0}) may not be used in setup / verification expressions..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnsupportedStaticMember">
            <summary>
              Looks up a localized string similar to Static members (here: {0}) may not be used in setup / verification expressions..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnverifiedInvocations">
             <summary>
               Looks up a localized string similar to {0}:
            This mock failed verification due to the following unverified invocations:.
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UseItExprIsNullRatherThanNullArgumentValue">
            <summary>
              Looks up a localized string similar to Use ItExpr.IsNull&lt;TValue&gt; rather than a null argument value, as it prevents proper method lookup..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UseItIsOtherOverload">
            <summary>
              Looks up a localized string similar to It is impossible to call the provided strongly-typed predicate due to the use of a type matcher. Provide a weakly-typed predicate with two parameters (object, Type) instead..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.VerificationErrorsOfInnerMock">
            <summary>
              Looks up a localized string similar to {0}:.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.VerificationErrorsOfMock">
             <summary>
               Looks up a localized string similar to {0}:
            This mock failed verification due to the following:.
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.VerificationErrorsOfMockRepository">
            <summary>
              Looks up a localized string similar to The mock repository failed verification due to the following:.
            </summary>
        </member>
        <member name="T:Moq.Protected.IProtectedAsMock`2">
            <summary>
            Allows setups to be specified for protected members (methods and properties)
            seen through another type with corresponding members (that is, members
            having identical signatures as the ones to be set up).
            </summary>
            <typeparam name="T">Type of the mocked object.</typeparam>
            <typeparam name="TAnalog">
            Any type with members whose signatures are identical to the mock's protected members (except for their accessibility level).
            </typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedAsMock`2.Setup(System.Linq.Expressions.Expression{System.Action{`1}})">
            <summary>
            Specifies a setup on the mocked type for a call to a <see langword="void"/> method.
            </summary>
            <param name="expression">Lambda expression that specifies the expected method invocation.</param>
            <seealso cref="M:Moq.Mock`1.Setup(System.Linq.Expressions.Expression{System.Action{`0}})"/>
        </member>
        <member name="M:Moq.Protected.IProtectedAsMock`2.Setup``1(System.Linq.Expressions.Expression{System.Func{`1,``0}})">
            <summary>
            Specifies a setup on the mocked type for a call to a value-returning method.
            </summary>
            <typeparam name="TResult">Type of the return value. Typically omitted as it can be inferred from the expression.</typeparam>
            <param name="expression">Lambda expression that specifies the expected method invocation.</param>
            <seealso cref="M:Moq.Mock`1.Setup``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})"/>
        </member>
        <member name="M:Moq.Protected.IProtectedAsMock`2.SetupGet``1(System.Linq.Expressions.Expression{System.Func{`1,``0}})">
            <summary>
            Specifies a setup on the mocked type for a call to a property getter.
            </summary>
            <typeparam name="TProperty">Type of the property. Typically omitted as it can be inferred from the expression.</typeparam>
            <param name="expression">Lambda expression that specifies the property getter.</param>
        </member>
        <member name="M:Moq.Protected.IProtectedAsMock`2.SetupProperty``1(System.Linq.Expressions.Expression{System.Func{`1,``0}},``0)">
            <summary>
            Specifies that the given property should have "property behavior",
            meaning that setting its value will cause it to be saved and later returned when the property is requested.
            (This is also known as "stubbing".)
            </summary>
            <typeparam name="TProperty">Type of the property. Typically omitted as it can be inferred from the expression.</typeparam>
            <param name="expression">Lambda expression that specifies the property.</param>
            <param name="initialValue">Initial value for the property.</param>
        </member>
        <member name="M:Moq.Protected.IProtectedAsMock`2.SetupSequence``1(System.Linq.Expressions.Expression{System.Func{`1,``0}})">
            <summary>
            Return a sequence of values, once per call.
            </summary>
            <typeparam name="TResult">Type of the return value. Typically omitted as it can be inferred from the expression.</typeparam>
            <param name="expression">Lambda expression that specifies the expected method invocation.</param>
        </member>
        <member name="M:Moq.Protected.IProtectedAsMock`2.SetupSequence(System.Linq.Expressions.Expression{System.Action{`1}})">
            <summary>
            Performs a sequence of actions, one per call.
            </summary>
            <param name="expression">Lambda expression that specifies the expected method invocation.</param>
        </member>
        <member name="M:Moq.Protected.IProtectedAsMock`2.Verify(System.Linq.Expressions.Expression{System.Action{`1}},System.Nullable{Moq.Times},System.String)">
            <summary>
            Verifies that a specific invocation matching the given expression was performed on the mock.
            Use in conjunction with the default <see cref="F:Moq.MockBehavior.Loose"/>.
            </summary>
            <param name="expression">Lambda expression that specifies the method invocation.</param>
            <param name="times">
            Number of times that the invocation is expected to have occurred.
            If omitted, assumed to be <see cref="M:Moq.Times.AtLeastOnce"/>.
            </param>
            <param name="failMessage">Message to include in the thrown <see cref="T:Moq.MockException"/> if verification fails.</param>
            <exception cref="T:Moq.MockException">The specified invocation did not occur (or did not occur the specified number of times).</exception>
        </member>
        <member name="M:Moq.Protected.IProtectedAsMock`2.Verify``1(System.Linq.Expressions.Expression{System.Func{`1,``0}},System.Nullable{Moq.Times},System.String)">
            <summary>
            Verifies that a specific invocation matching the given expression was performed on the mock.
            Use in conjunction with the default <see cref="F:Moq.MockBehavior.Loose"/>.
            </summary>
            <typeparam name="TResult">Type of the return value. Typically omitted as it can be inferred from the expression.</typeparam>
            <param name="expression">Lambda expression that specifies the method invocation.</param>
            <param name="times">
            Number of times that the invocation is expected to have occurred.
            If omitted, assumed to be <see cref="M:Moq.Times.AtLeastOnce"/>.
            </param>
            <param name="failMessage">Message to include in the thrown <see cref="T:Moq.MockException"/> if verification fails.</param>
            <exception cref="T:Moq.MockException">The specified invocation did not occur (or did not occur the specified number of times).</exception>
        </member>
        <member name="M:Moq.Protected.IProtectedAsMock`2.VerifyGet``1(System.Linq.Expressions.Expression{System.Func{`1,``0}},System.Nullable{Moq.Times},System.String)">
            <summary>
            Verifies that a property was read on the mock.
            </summary>
            <typeparam name="TProperty">Type of the property. Typically omitted as it can be inferred from the expression.</typeparam>
            <param name="expression">Lambda expression that specifies the method invocation.</param>
            <param name="times">
            Number of times that the invocation is expected to have occurred.
            If omitted, assumed to be <see cref="M:Moq.Times.AtLeastOnce"/>.
            </param>
            <param name="failMessage">Message to include in the thrown <see cref="T:Moq.MockException"/> if verification fails.</param>
            <exception cref="T:Moq.MockException">The specified invocation did not occur (or did not occur the specified number of times).</exception>
        </member>
        <member name="T:Moq.Protected.IProtectedMock`1">
            <summary>
            Allows setups to be specified for protected members by using their 
            name as a string, rather than strong-typing them which is not possible 
            due to their visibility.
            </summary>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.As``1">
            <summary>
            Set up protected members (methods and properties) seen through another type with identical member signatures.
            </summary>
            <typeparam name="TAnalog">
            Any type with members whose signatures are identical to the mock's protected members (except for their accessibility level).
            </typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Setup(System.String,System.Object[])">
            <summary>
            Specifies a setup for a void method invocation with the given 
            <paramref name="voidMethodName"/>, optionally specifying arguments for the method call.
            </summary>
            <param name="voidMethodName">The name of the void method to be invoked.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Setup(System.String,System.Boolean,System.Object[])">
            <summary>
            Specifies a setup for a void method invocation with the given
            <paramref name="voidMethodName"/>, optionally specifying arguments for the method call.
            </summary>
            <param name="voidMethodName">The name of the void method to be invoked.</param>
            <param name="exactParameterMatch">Should the parameter types match exactly types that were provided</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used,
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Setup(System.String,System.Type[],System.Boolean,System.Object[])">
            <summary>
            Specifies a setup for a void method invocation with the given
            <paramref name="voidMethodName"/>, optionally specifying arguments for the method call.
            </summary>
            <param name="voidMethodName">The name of the void method to be invoked.</param>
            <param name="genericTypeArguments">An array of types to be substituted for the type parameters of the current generic method definition.</param>
            <param name="exactParameterMatch">Should the parameter types match exactly types that were provided</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used,
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Setup``1(System.String,System.Object[])">
            <summary>
            Specifies a setup for an invocation on a property or a non void method with the given 
            <paramref name="methodOrPropertyName"/>, optionally specifying arguments for the method call.
            </summary>
            <param name="methodOrPropertyName">The name of the method or property to be invoked.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
            <typeparam name="TResult">The return type of the method or property.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Setup``1(System.String,System.Boolean,System.Object[])">
            <summary>
            Specifies a setup for an invocation on a property or a non void method with the given 
            <paramref name="methodOrPropertyName"/>, optionally specifying arguments for the method call.
            </summary>
            <param name="methodOrPropertyName">The name of the method or property to be invoked.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
            <param name="exactParameterMatch">Should the parameter types match exactly types that were provided</param>
            <typeparam name="TResult">The return type of the method or property.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Setup``1(System.String,System.Type[],System.Boolean,System.Object[])">
            <summary>
            Specifies a setup for an invocation on a property or a non void method with the given 
            <paramref name="methodOrPropertyName"/>, optionally specifying arguments for the method call.
            </summary>
            <param name="methodOrPropertyName">The name of the method or property to be invoked.</param>
            <param name="genericTypeArguments">An array of types to be substituted for the type parameters of the current generic method definition.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
            <param name="exactParameterMatch">Should the parameter types match exactly types that were provided</param>
            <typeparam name="TResult">The return type of the method or property.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.SetupGet``1(System.String)">
            <summary>
            Specifies a setup for an invocation on a property getter with the given 
            <paramref name="propertyName"/>.
            </summary>
            <param name="propertyName">The name of the property.</param>
            <typeparam name="TProperty">The type of the property.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.SetupSet``1(System.String,System.Object)">
            <summary>
            Specifies a setup for an invocation on a property setter with the given 
            <paramref name="propertyName"/>.
            </summary>
            <param name="propertyName">The name of the property.</param>
            <param name="value">The property value. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
            <typeparam name="TProperty">The type of the property.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.SetupSequence(System.String,System.Object[])">
            <summary>
            Performs a sequence of actions, one per call.
            </summary>
            <param name="methodOrPropertyName">Name of the method or property being set up.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used,
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.SetupSequence(System.String,System.Boolean,System.Object[])">
            <summary>
            Performs a sequence of actions, one per call.
            </summary>
            <param name="methodOrPropertyName">Name of the method or property being set up.</param>
            <param name="exactParameterMatch">Determines whether the parameter types should exactly match the types provided.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used,
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.SetupSequence(System.String,System.Type[],System.Boolean,System.Object[])">
            <summary>
            Performs a sequence of actions, one per call.
            </summary>
            <param name="methodOrPropertyName">Name of the method or property being set up.</param>
            <param name="genericTypeArguments">An array of types to be substituted for the type parameters of the current generic method definition.</param>
            <param name="exactParameterMatch">Determines whether the parameter types should exactly match the types provided.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used,
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.SetupSequence``1(System.String,System.Object[])">
            <summary>
            Return a sequence of values, once per call.
            </summary>
            <param name="methodOrPropertyName">Name of the method or property being set up.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used,
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
            <typeparam name="TResult">Return type of the method or property being set up.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.SetupSequence``1(System.String,System.Boolean,System.Object[])">
            <summary>
            Return a sequence of values, once per call.
            </summary>
            <param name="methodOrPropertyName">Name of the method or property being set up.</param>
            <param name="exactParameterMatch">Determines whether the parameter types should exactly match the types provided.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used,
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
            <typeparam name="TResult">Return type of the method or property being set up.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.SetupSequence``1(System.String,System.Type[],System.Boolean,System.Object[])">
            <summary>
            Return a sequence of values, once per call.
            </summary>
            <param name="methodOrPropertyName">Name of the method or property being set up.</param>
            <param name="genericTypeArguments">An array of types to be substituted for the type parameters of the current generic method definition.</param>
            <param name="exactParameterMatch">Determines whether the parameter types should exactly match the types provided.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used,
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
            <typeparam name="TResult">Return type of the method or property being set up.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Verify(System.String,Moq.Times,System.Object[])">
            <summary>
            Specifies a verify for a void method with the given <paramref name="methodName"/>,
            optionally specifying arguments for the method call. Use in conjunction with the default
            <see cref="F:Moq.MockBehavior.Loose"/>.
            </summary>
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by
            <paramref name="times"/>.</exception>
            <param name="methodName">The name of the void method to be verified.</param>
            <param name="times">The number of times a method is allowed to be called.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Verify(System.String,System.Type[],Moq.Times,System.Object[])">
            <summary>
            Specifies a verify for a void method with the given <paramref name="methodName"/>,
            optionally specifying arguments for the method call. Use in conjunction with the default
            <see cref="F:Moq.MockBehavior.Loose"/>.
            </summary>
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by
            <paramref name="times"/>.</exception>
            <param name="methodName">The name of the void method to be verified.</param>
            <param name="genericTypeArguments">An array of types to be substituted for the type parameters of the current generic method definition.</param>
            <param name="times">The number of times a method is allowed to be called.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Verify(System.String,Moq.Times,System.Boolean,System.Object[])">
            <summary>
            Specifies a verify for a void method with the given <paramref name="methodName"/>,
            optionally specifying arguments for the method call. Use in conjunction with the default
            <see cref="F:Moq.MockBehavior.Loose"/>.
            </summary>
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by
            <paramref name="times"/>.</exception>
            <param name="methodName">The name of the void method to be verified.</param>
            <param name="times">The number of times a method is allowed to be called.</param>
            <param name="exactParameterMatch">Should the parameter types match exactly types that were provided</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Verify(System.String,System.Type[],Moq.Times,System.Boolean,System.Object[])">
            <summary>
            Specifies a verify for a void method with the given <paramref name="methodName"/>,
            optionally specifying arguments for the method call. Use in conjunction with the default
            <see cref="F:Moq.MockBehavior.Loose"/>.
            </summary>
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by
            <paramref name="times"/>.</exception>
            <param name="methodName">The name of the void method to be verified.</param>
            <param name="genericTypeArguments">An array of types to be substituted for the type parameters of the current generic method definition.</param>
            <param name="times">The number of times a method is allowed to be called.</param>
            <param name="exactParameterMatch">Should the parameter types match exactly types that were provided</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Verify``1(System.String,Moq.Times,System.Object[])">
            <summary>
            Specifies a verify for an invocation on a property or a non void method with the given 
            <paramref name="methodName"/>, optionally specifying arguments for the method call.
            </summary>
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by 
            <paramref name="times"/>.</exception>
            <param name="methodName">The name of the method or property to be invoked.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
            <param name="times">The number of times a method is allowed to be called.</param>
            <typeparam name="TResult">The type of return value from the expression.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Verify``1(System.String,System.Type[],Moq.Times,System.Object[])">
            <summary>
            Specifies a verify for an invocation on a property or a non void method with the given 
            <paramref name="methodName"/>, optionally specifying arguments for the method call.
            </summary>
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by 
            <paramref name="times"/>.</exception>
            <param name="methodName">The name of the method or property to be invoked.</param>
            <param name="genericTypeArguments">An array of types to be substituted for the type parameters of the current generic method definition.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
            <param name="times">The number of times a method is allowed to be called.</param>
            <typeparam name="TResult">The type of return value from the expression.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Verify``1(System.String,Moq.Times,System.Boolean,System.Object[])">
            <summary>
            Specifies a verify for an invocation on a property or a non void method with the given 
            <paramref name="methodName"/>, optionally specifying arguments for the method call.
            </summary>
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by 
            <paramref name="times"/>.</exception>
            <param name="methodName">The name of the method or property to be invoked.</param>
            <param name="exactParameterMatch">Should the parameter types match exactly types that were provided</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
            <param name="times">The number of times a method is allowed to be called.</param>
            <typeparam name="TResult">The type of return value from the expression.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Verify``1(System.String,System.Type[],Moq.Times,System.Boolean,System.Object[])">
            <summary>
            Specifies a verify for an invocation on a property or a non void method with the given 
            <paramref name="methodName"/>, optionally specifying arguments for the method call.
            </summary>
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by 
            <paramref name="times"/>.</exception>
            <param name="methodName">The name of the method or property to be invoked.</param>
            <param name="genericTypeArguments">An array of types to be substituted for the type parameters of the current generic method definition.</param>
            <param name="exactParameterMatch">Should the parameter types match exactly types that were provided</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
            <param name="times">The number of times a method is allowed to be called.</param>
            <typeparam name="TResult">The type of return value from the expression.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.VerifyGet``1(System.String,Moq.Times)">
            <summary>
            Specifies a verify for an invocation on a property getter with the given 
            <paramref name="propertyName"/>.
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by 
            <paramref name="times"/>.</exception>
            </summary>
            <param name="propertyName">The name of the property.</param>
            <param name="times">The number of times a method is allowed to be called.</param>
            <typeparam name="TProperty">The type of the property.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.VerifySet``1(System.String,Moq.Times,System.Object)">
            <summary>
            Specifies a setup for an invocation on a property setter with the given 
            <paramref name="propertyName"/>.
            </summary>
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by 
            <paramref name="times"/>.</exception>
            <param name="propertyName">The name of the property.</param>
            <param name="times">The number of times a method is allowed to be called.</param>
            <param name="value">The property value.</param>
            <typeparam name="TProperty">The type of the property. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</typeparam>
        </member>
        <member name="T:Moq.Protected.ItExpr">
            <summary>
            Allows the specification of a matching condition for an 
            argument in a protected member setup, rather than a specific 
            argument value. "ItExpr" refers to the argument being matched.
            </summary>
            <remarks>
            <para>Use this variant of argument matching instead of 
            <see cref="T:Moq.It"/> for protected setups.</para>
            This class allows the setup to match a method invocation 
            with an arbitrary value, with a value in a specified range, or 
            even one that matches a given predicate, or null.
            </remarks>
        </member>
        <member name="T:Moq.Protected.ItExpr.Ref`1">
            <summary>
            Contains matchers for <see langword="ref"/> (C#) / <see langword="ByRef"/> (VB.NET) parameters of type <typeparamref name="TValue"/>.
            </summary>
            <typeparam name="TValue">The parameter type.</typeparam>
        </member>
        <member name="P:Moq.Protected.ItExpr.Ref`1.IsAny">
            <summary>
            Matches any value that is assignment-compatible with type <typeparamref name="TValue"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Moq.Protected.ItExpr.IsNull``1">
            <summary>
            Matches a null value of the given <typeparamref name="TValue"/> type.
            </summary>
            <remarks>
            Required for protected mocks as the null value cannot be used 
            directly as it prevents proper method overload selection.
            </remarks>
            <example>
            <code>
            // Throws an exception for a call to Remove with a null string value.
            mock.Protected()
                .Setup("Remove", ItExpr.IsNull&lt;string&gt;())
                .Throws(new InvalidOperationException());
            </code>
            </example>
            <typeparam name="TValue">Type of the value.</typeparam>
        </member>
        <member name="M:Moq.Protected.ItExpr.IsAny``1">
            <summary>
            Matches any value of the given <typeparamref name="TValue"/> type.
            </summary>
            <remarks>
            Typically used when the actual argument value for a method 
            call is not relevant.
            </remarks>
            <example>
            <code>
            // Throws an exception for a call to Remove with any string value.
            mock.Protected()
                .Setup("Remove", ItExpr.IsAny&lt;string&gt;())
                .Throws(new InvalidOperationException());
            </code>
            </example>
            <typeparam name="TValue">Type of the value.</typeparam>
        </member>
        <member name="M:Moq.Protected.ItExpr.Is``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            Matches any value that satisfies the given predicate.
            </summary>
            <typeparam name="TValue">Type of the argument to check.</typeparam>
            <param name="match">The predicate used to match the method argument.</param>
            <remarks>
            Allows the specification of a predicate to perform matching 
            of method call arguments.
            </remarks>
            <example>
            This example shows how to return the value <c>1</c> whenever the argument to the 
            <c>Do</c> method is an even number.
            <code>
            mock.Protected()
                .Setup("Do", ItExpr.Is&lt;int&gt;(i =&gt; i % 2 == 0))
                .Returns(1);
            </code>
            This example shows how to throw an exception if the argument to the 
            method is a negative number:
            <code>
            mock.Protected()
                .Setup("GetUser", ItExpr.Is&lt;int&gt;(i =&gt; i &lt; 0))
                .Throws(new ArgumentException());
            </code>
            </example>
        </member>
        <member name="M:Moq.Protected.ItExpr.IsInRange``1(``0,``0,Moq.Range)">
            <summary>
            Matches any value that is in the range specified.
            </summary>
            <typeparam name="TValue">Type of the argument to check.</typeparam>
            <param name="from">The lower bound of the range.</param>
            <param name="to">The upper bound of the range.</param>
            <param name="rangeKind">The kind of range. See <see cref="T:Moq.Range"/>.</param>
            <example>
            The following example shows how to expect a method call 
            with an integer argument within the 0..100 range.
            <code>
            mock.Protected()
                .Setup("HasInventory",
                        ItExpr.IsAny&lt;string&gt;(),
                        ItExpr.IsInRange(0, 100, Range.Inclusive))
                .Returns(false);
            </code>
            </example>
        </member>
        <member name="M:Moq.Protected.ItExpr.IsRegex(System.String)">
            <summary>
            Matches a string argument if it matches the given regular expression pattern.
            </summary>
            <param name="regex">The pattern to use to match the string argument value.</param>
            <example>
            The following example shows how to expect a call to a method where the 
            string argument matches the given regular expression:
            <code>
            mock.Protected()
                .Setup("Check", ItExpr.IsRegex("[a-z]+"))
                .Returns(1);
            </code>
            </example>
        </member>
        <member name="M:Moq.Protected.ItExpr.IsRegex(System.String,System.Text.RegularExpressions.RegexOptions)">
            <summary>
            Matches a string argument if it matches the given regular expression pattern.
            </summary>
            <param name="regex">The pattern to use to match the string argument value.</param>
            <param name="options">The options used to interpret the pattern.</param>
            <example>
            The following example shows how to expect a call to a method where the 
            string argument matches the given regular expression, in a case insensitive way:
            <code>
            mock.Protected()
                .Setup("Check", ItExpr.IsRegex("[a-z]+", RegexOptions.IgnoreCase))
                .Returns(1);
            </code>
            </example>
        </member>
        <member name="T:Moq.Protected.ProtectedAsMock`2.DuckReplacer">
            <summary>
            <see cref="T:System.Linq.Expressions.ExpressionVisitor"/> used to replace occurrences of `TAnalog.Member` sub-expressions with `T.Member`.
            </summary>
        </member>
        <member name="T:Moq.Protected.ProtectedExtension">
            <summary>
            Enables the <c>Protected()</c> method on <see cref="T:Moq.Mock`1"/>, 
            allowing setups to be set for protected members by using their 
            name as a string, rather than strong-typing them which is not possible 
            due to their visibility.
            </summary>
        </member>
        <member name="M:Moq.Protected.ProtectedExtension.Protected``1(Moq.Mock{``0})">
            <summary>
            Enable protected setups for the mock.
            </summary>
            <typeparam name="T">Mocked object type. Typically omitted as it can be inferred from the mock instance.</typeparam>
            <param name="mock">The mock to set the protected setups on.</param>
        </member>
        <member name="T:Moq.CastleProxyFactory">
            <summary>
            An implementation of <see cref="T:Moq.ProxyFactory"/> that is based on Castle DynamicProxy.
            </summary>
        </member>
        <member name="M:Moq.CastleProxyFactory.CreateProxy(System.Type,Moq.IInterceptor,System.Type[],System.Object[])">
            <inheritdoc />
        </member>
        <member name="T:Moq.CastleProxyFactory.IncludeObjectMethodsHook">
            <summary>
            This hook tells Castle DynamicProxy to proxy the default methods it suggests,
            plus some of the methods defined by <see cref="T:System.Object"/>, e.g. so we can intercept
            <see cref="M:System.Object.ToString"/> and give mocks useful default names.
            </summary>
        </member>
        <member name="T:Moq.Internals.InterfaceProxy">
            <summary>Do not use. (Moq requires this class so that <see langword="object"/> methods can be set up on interface mocks.)</summary>
        </member>
        <member name="M:Moq.Internals.InterfaceProxy.Equals(System.Object)">
            <summary/>
        </member>
        <member name="M:Moq.Internals.InterfaceProxy.GetHashCode">
            <summary/>
        </member>
        <member name="M:Moq.Internals.InterfaceProxy.ToString">
            <summary/>
        </member>
        <member name="T:Moq.Internals.IProxy">
            <summary>Do not use. (Moq requires this interface so that <see langword="object"/> methods can be set up on interface mocks.)</summary>
        </member>
        <member name="P:Moq.Internals.IProxy.Interceptor">
            <summary/>
        </member>
        <member name="P:Moq.ProxyFactory.Instance">
            <summary>
            Gets the global <see cref="T:Moq.ProxyFactory"/> instance used by Moq.
            </summary>
        </member>
        <member name="T:Moq.Range">
            <summary>
            Kind of range to use in a filter specified through 
            <see cref="M:Moq.It.IsInRange``1(``0,``0,Moq.Range)"/>.
            </summary>
        </member>
        <member name="F:Moq.Range.Inclusive">
            <summary>
            The range includes the <c>to</c> and 
            <c>from</c> values.
            </summary>
        </member>
        <member name="F:Moq.Range.Exclusive">
            <summary>
            The range does not include the <c>to</c> and 
            <c>from</c> values.
            </summary>
        </member>
        <member name="T:Moq.ReturnsExtensions">
            <summary>
            Defines async extension methods on IReturns.
            </summary>
        </member>
        <member name="M:Moq.ReturnsExtensions.ReturnsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.Task{``1}},``1)">
            <summary>
            Specifies the value to return from an asynchronous method.
            </summary>
            <typeparam name="TMock">Mocked type.</typeparam>
            <typeparam name="TResult">Type of the return value.</typeparam>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="value">The value to return, or <see longword="null"/>.</param>
        </member>
        <member name="M:Moq.ReturnsExtensions.ReturnsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.ValueTask{``1}},``1)">
            <summary>
            Specifies the value to return from an asynchronous method.
            </summary>
            <typeparam name="TMock">Mocked type.</typeparam>
            <typeparam name="TResult">Type of the return value.</typeparam>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="value">The value to return, or <see longword="null"/>.</param>
        </member>
        <member name="M:Moq.ReturnsExtensions.ReturnsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.Task{``1}},System.Func{``1})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <typeparam name="TMock">Mocked type.</typeparam>
            <typeparam name="TResult">Type of the return value.</typeparam>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.ReturnsExtensions.ReturnsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.ValueTask{``1}},System.Func{``1})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <typeparam name="TMock">Mocked type.</typeparam>
            <typeparam name="TResult">Type of the return value.</typeparam>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.ReturnsExtensions.ThrowsAsync``1(Moq.Language.IReturns{``0,System.Threading.Tasks.Task},System.Exception)">
            <summary>
            Specifies the exception to throw when the asynchronous method is invoked.
            </summary>
            <typeparam name="TMock">Mocked type.</typeparam>
            <param name="mock">Returns verb which represents the mocked type and the task return type</param>
            <param name="exception">Exception instance to throw.</param>
        </member>
        <member name="M:Moq.ReturnsExtensions.ThrowsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.Task{``1}},System.Exception)">
            <summary>
            Specifies the exception to throw when the asynchronous method is invoked.
            </summary>
            <typeparam name="TMock">Mocked type.</typeparam>
            <typeparam name="TResult">Type of the return value.</typeparam>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="exception">Exception instance to throw.</param>
        </member>
        <member name="M:Moq.ReturnsExtensions.ThrowsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.ValueTask{``1}},System.Exception)">
            <summary>
            Specifies the exception to throw when the asynchronous method is invoked.
            </summary>
            <typeparam name="TMock">Mocked type.</typeparam>
            <typeparam name="TResult">Type of the return value.</typeparam>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="exception">Exception instance to throw.</param>
        </member>
        <member name="M:Moq.ReturnsExtensions.ReturnsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.Task{``1}},``1,System.TimeSpan)">
            <summary>
            Allows to specify the delayed return value of an asynchronous method.
            </summary>
        </member>
        <member name="M:Moq.ReturnsExtensions.ReturnsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.ValueTask{``1}},``1,System.TimeSpan)">
            <summary>
            Allows to specify the delayed return value of an asynchronous method.
            </summary>
        </member>
        <member name="M:Moq.ReturnsExtensions.ReturnsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.Task{``1}},``1,System.TimeSpan,System.TimeSpan)">
            <summary>
            Allows to specify the delayed return value of an asynchronous method.
            </summary>
        </member>
        <member name="M:Moq.ReturnsExtensions.ReturnsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.ValueTask{``1}},``1,System.TimeSpan,System.TimeSpan)">
            <summary>
            Allows to specify the delayed return value of an asynchronous method.
            </summary>
        </member>
        <member name="M:Moq.ReturnsExtensions.ReturnsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.Task{``1}},``1,System.TimeSpan,System.TimeSpan,System.Random)">
            <summary>
            <para>Allows to specify the delayed return value of an asynchronous method.</para>
            <para>Use the <see cref="F:Moq.ReturnsExtensions.Random"/> argument to pass in (seeded) random generators used across your unit test.</para>
            </summary>
        </member>
        <member name="M:Moq.ReturnsExtensions.ReturnsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.ValueTask{``1}},``1,System.TimeSpan,System.TimeSpan,System.Random)">
            <summary>
            <para>Allows to specify the delayed return value of an asynchronous method.</para>
            <para>Use the <see cref="F:Moq.ReturnsExtensions.Random"/> argument to pass in (seeded) random generators used across your unit test.</para>
            </summary>
        </member>
        <member name="M:Moq.ReturnsExtensions.ThrowsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.Task{``1}},System.Exception,System.TimeSpan)">
            <summary>
            Allows to specify the exception thrown by an asynchronous method.
            </summary>
        </member>
        <member name="M:Moq.ReturnsExtensions.ThrowsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.ValueTask{``1}},System.Exception,System.TimeSpan)">
            <summary>
            Allows to specify the exception thrown by an asynchronous method.
            </summary>
        </member>
        <member name="M:Moq.ReturnsExtensions.ThrowsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.Task{``1}},System.Exception,System.TimeSpan,System.TimeSpan)">
            <summary>
            Allows to specify the exception thrown by an asynchronous method.
            </summary>
        </member>
        <member name="M:Moq.ReturnsExtensions.ThrowsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.ValueTask{``1}},System.Exception,System.TimeSpan,System.TimeSpan)">
            <summary>
            Allows to specify the exception thrown by an asynchronous method.
            </summary>
        </member>
        <member name="M:Moq.ReturnsExtensions.ThrowsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.Task{``1}},System.Exception,System.TimeSpan,System.TimeSpan,System.Random)">
            <summary>
            <para>Allows to specify the exception thrown by an asynchronous method.</para> 
            <para>Use the <see cref="F:Moq.ReturnsExtensions.Random"/> argument to pass in (seeded) random generators used across your unit test.</para>
            </summary>
        </member>
        <member name="M:Moq.ReturnsExtensions.ThrowsAsync``2(Moq.Language.IReturns{``0,System.Threading.Tasks.ValueTask{``1}},System.Exception,System.TimeSpan,System.TimeSpan,System.Random)">
            <summary>
            <para>Allows to specify the exception thrown by an asynchronous method.</para> 
            <para>Use the <see cref="F:Moq.ReturnsExtensions.Random"/> argument to pass in (seeded) random generators used across your unit test.</para>
            </summary>
        </member>
        <member name="T:Moq.GeneratedReturnsExtensions">
            <summary>
            Defines async extension methods on IReturns.
            </summary>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``3(Moq.Language.IReturns{``1,System.Threading.Tasks.Task{``2}},System.Func{``0,``2})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <typeparam name="T">Type of the function parameter.</typeparam>
            <typeparam name="TMock">Mocked type.</typeparam>
            <typeparam name="TResult">Type of the return value.</typeparam>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``4(Moq.Language.IReturns{``2,System.Threading.Tasks.Task{``3}},System.Func{``0,``1,``3})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``5(Moq.Language.IReturns{``3,System.Threading.Tasks.Task{``4}},System.Func{``0,``1,``2,``4})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``6(Moq.Language.IReturns{``4,System.Threading.Tasks.Task{``5}},System.Func{``0,``1,``2,``3,``5})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``7(Moq.Language.IReturns{``5,System.Threading.Tasks.Task{``6}},System.Func{``0,``1,``2,``3,``4,``6})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``8(Moq.Language.IReturns{``6,System.Threading.Tasks.Task{``7}},System.Func{``0,``1,``2,``3,``4,``5,``7})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``9(Moq.Language.IReturns{``7,System.Threading.Tasks.Task{``8}},System.Func{``0,``1,``2,``3,``4,``5,``6,``8})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``10(Moq.Language.IReturns{``8,System.Threading.Tasks.Task{``9}},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``9})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``11(Moq.Language.IReturns{``9,System.Threading.Tasks.Task{``10}},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``10})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``12(Moq.Language.IReturns{``10,System.Threading.Tasks.Task{``11}},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``11})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``13(Moq.Language.IReturns{``11,System.Threading.Tasks.Task{``12}},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``12})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``14(Moq.Language.IReturns{``12,System.Threading.Tasks.Task{``13}},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``13})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``15(Moq.Language.IReturns{``13,System.Threading.Tasks.Task{``14}},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``14})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``16(Moq.Language.IReturns{``14,System.Threading.Tasks.Task{``15}},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``15})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``17(Moq.Language.IReturns{``15,System.Threading.Tasks.Task{``16}},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``16})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``3(Moq.Language.IReturns{``1,System.Threading.Tasks.ValueTask{``2}},System.Func{``0,``2})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <typeparam name="T">Type of the function parameter.</typeparam>
            <typeparam name="TMock">Mocked type.</typeparam>
            <typeparam name="TResult">Type of the return value.</typeparam>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``4(Moq.Language.IReturns{``2,System.Threading.Tasks.ValueTask{``3}},System.Func{``0,``1,``3})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``5(Moq.Language.IReturns{``3,System.Threading.Tasks.ValueTask{``4}},System.Func{``0,``1,``2,``4})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``6(Moq.Language.IReturns{``4,System.Threading.Tasks.ValueTask{``5}},System.Func{``0,``1,``2,``3,``5})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``7(Moq.Language.IReturns{``5,System.Threading.Tasks.ValueTask{``6}},System.Func{``0,``1,``2,``3,``4,``6})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``8(Moq.Language.IReturns{``6,System.Threading.Tasks.ValueTask{``7}},System.Func{``0,``1,``2,``3,``4,``5,``7})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``9(Moq.Language.IReturns{``7,System.Threading.Tasks.ValueTask{``8}},System.Func{``0,``1,``2,``3,``4,``5,``6,``8})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``10(Moq.Language.IReturns{``8,System.Threading.Tasks.ValueTask{``9}},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``9})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``11(Moq.Language.IReturns{``9,System.Threading.Tasks.ValueTask{``10}},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``10})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``12(Moq.Language.IReturns{``10,System.Threading.Tasks.ValueTask{``11}},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``11})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``13(Moq.Language.IReturns{``11,System.Threading.Tasks.ValueTask{``12}},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``12})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``14(Moq.Language.IReturns{``12,System.Threading.Tasks.ValueTask{``13}},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``13})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``15(Moq.Language.IReturns{``13,System.Threading.Tasks.ValueTask{``14}},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``14})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``16(Moq.Language.IReturns{``14,System.Threading.Tasks.ValueTask{``15}},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``15})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="M:Moq.GeneratedReturnsExtensions.ReturnsAsync``17(Moq.Language.IReturns{``15,System.Threading.Tasks.ValueTask{``16}},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``16})">
            <summary>
            Specifies a function that will calculate the value to return from the asynchronous method.
            </summary>
            <param name="mock">Returns verb which represents the mocked type and the task of return type</param>
            <param name="valueFunction">The function that will calculate the return value.</param>
        </member>
        <member name="T:Moq.SequenceSetup">
            <summary>
              Programmable setup used by <see cref="M:Moq.Mock.SetupSequence(Moq.Mock,System.Linq.Expressions.LambdaExpression)"/>.
            </summary>
        </member>
        <member name="M:Moq.Setup.TryGetReturnValue(System.Object@)">
            <summary>
              Attempts to get this setup's return value without invoking user code
              (which could have side effects beyond Moq's understanding and control).
            </summary>
        </member>
        <member name="M:Moq.Setup.Verify(System.Boolean,System.Func{Moq.ISetup,System.Boolean},System.Collections.Generic.HashSet{Moq.Mock})">
            <summary>
              Verifies this setup and those of its inner mock (if present and known).
            </summary>
            <param name="recursive">
              Specifies whether recursive verification should be performed.
            </param>
            <param name="predicate">
              Specifies which setups should be verified.
            </param>
            <param name="verifiedMocks">
              The set of mocks that have already been verified.
            </param>
            <exception cref="T:Moq.MockException">
              This setup or any of its inner mock (if present and known) failed verification.
            </exception>
        </member>
        <member name="T:Moq.StubbedPropertyGetterSetup">
            <summary>
              Setup used by <see cref="M:Moq.Mock.SetupAllProperties(Moq.Mock)"/> for property getters.
            </summary>
        </member>
        <member name="T:Moq.StubbedPropertySetterSetup">
            <summary>
              Setup used by <see cref="M:Moq.Mock.SetupAllProperties(Moq.Mock)"/> for property setters.
            </summary>
        </member>
        <member name="T:Moq.Switches">
            <summary>
            Represents a switch, or a combination of switches, that can be either enabled or disabled.
            When set via <see cref="P:Moq.Mock.Switches"/> or <see cref="P:Moq.MockFactory.Switches"/>, they determine how a mock will operate.
            </summary>
        </member>
        <member name="F:Moq.Switches.Default">
            <summary>
            The default set of switches. The switches covered by this enumeration value may change between different versions of Moq.
            </summary>
        </member>
        <member name="F:Moq.Switches.CollectDiagnosticFileInfoForSetups">
            <summary>
            When enabled, specifies that source file information should be collected for each setup.
            This results in more helpful error messages, but may affect performance.
            </summary>
        </member>
        <member name="T:Moq.Times">
            <summary>
              Defines the number of invocations allowed by a mocked method.
            </summary>
        </member>
        <member name="M:Moq.Times.Deconstruct(System.Int32@,System.Int32@)">
            <summary>Deconstructs this instance.</summary>
            <param name="from">This output parameter will receive the minimum required number of calls satisfying this instance (i.e. the lower inclusive bound).</param>
            <param name="to">This output parameter will receive the maximum allowed number of calls satisfying this instance (i.e. the upper inclusive bound).</param>
        </member>
        <member name="M:Moq.Times.AtLeast(System.Int32)">
            <summary>
              Specifies that a mocked method should be invoked <paramref name="callCount"/> times
              as minimum.
            </summary>
            <param name="callCount">The minimum number of times.</param>
            <returns>An object defining the allowed number of invocations.</returns>
        </member>
        <member name="M:Moq.Times.AtLeastOnce">
            <summary>
              Specifies that a mocked method should be invoked one time as minimum.
            </summary>
            <returns>An object defining the allowed number of invocations.</returns>
        </member>
        <member name="M:Moq.Times.AtMost(System.Int32)">
            <summary>
              Specifies that a mocked method should be invoked <paramref name="callCount"/> times
              as maximum.
            </summary>
            <param name="callCount">The maximum number of times.</param>
            <returns>An object defining the allowed number of invocations.</returns>
        </member>
        <member name="M:Moq.Times.AtMostOnce">
            <summary>
              Specifies that a mocked method should be invoked one time as maximum.
            </summary>
            <returns>An object defining the allowed number of invocations.</returns>
        </member>
        <member name="M:Moq.Times.Between(System.Int32,System.Int32,Moq.Range)">
            <summary>
              Specifies that a mocked method should be invoked between
              <paramref name="callCountFrom"/> and <paramref name="callCountTo"/> times.
            </summary>
            <param name="callCountFrom">The minimum number of times.</param>
            <param name="callCountTo">The maximum number of times.</param>
            <param name="rangeKind">The kind of range. See <see cref="T:Moq.Range"/>.</param>
            <returns>An object defining the allowed number of invocations.</returns>
        </member>
        <member name="M:Moq.Times.Exactly(System.Int32)">
            <summary>
              Specifies that a mocked method should be invoked exactly
              <paramref name="callCount"/> times.
            </summary>
            <param name="callCount">The times that a method or property can be called.</param>
            <returns>An object defining the allowed number of invocations.</returns>
        </member>
        <member name="M:Moq.Times.Never">
            <summary>
              Specifies that a mocked method should not be invoked.
            </summary>
            <returns>An object defining the allowed number of invocations.</returns>
        </member>
        <member name="M:Moq.Times.Once">
            <summary>
              Specifies that a mocked method should be invoked exactly one time.
            </summary>
            <returns>An object defining the allowed number of invocations.</returns>
        </member>
        <member name="M:Moq.Times.Equals(Moq.Times)">
            <summary>
              Returns a value indicating whether this instance is equal to a specified <see cref="T:Moq.Times"/> value.
            </summary>
            <param name="other">A <see cref="T:Moq.Times"/> value to compare to this instance.</param>
            <returns>
              <see langword="true"/> if <paramref name="other"/> has the same value as this instance;
              otherwise, <see langword="false"/>.
            </returns>
        </member>
        <member name="M:Moq.Times.Equals(System.Object)">
            <summary>
              Returns a value indicating whether this instance is equal to a specified <see cref="T:Moq.Times"/> value.
            </summary>
            <param name="obj">An object to compare to this instance.</param>
            <returns>
              <see langword="true"/> if <paramref name="obj"/> has the same value as this instance;
              otherwise, <see langword="false"/>.
            </returns>
        </member>
        <member name="M:Moq.Times.GetHashCode">
            <summary>
              Returns a hash code for this instance.
            </summary>
            <returns>
             A hash code for this instance, suitable for use in hashing algorithms
             and data structures like a hash table.
            </returns>
        </member>
        <member name="M:Moq.Times.op_Equality(Moq.Times,Moq.Times)">
            <summary>
              Determines whether two specified <see cref="T:Moq.Times"/> objects have the same value.
            </summary>
            <param name="left">The first <see cref="T:Moq.Times"/>.</param>
            <param name="right">The second <see cref="T:Moq.Times"/>.</param>
            <returns>
              <see langword="true"/> if <paramref name="left"/> has the same value as <paramref name="right"/>;
              otherwise, <see langword="false"/>.
            </returns>
        </member>
        <member name="M:Moq.Times.op_Inequality(Moq.Times,Moq.Times)">
            <summary>
              Determines whether two specified <see cref="T:Moq.Times"/> objects have different values.
            </summary>
            <param name="left">The first <see cref="T:Moq.Times"/>.</param>
            <param name="right">The second <see cref="T:Moq.Times"/>.</param>
            <returns>
              <see langword="true"/> if the value of <paramref name="left"/> is different from
              <paramref name="right"/>'s; otherwise, <see langword="false"/>.
            </returns>
        </member>
        <member name="M:Moq.Times.ToString">
            <inheritdoc/>
        </member>
        <member name="M:Moq.Times.Validate(System.Int32)">
            <summary>
              Checks whether the specified number of invocations matches the constraint described by this instance.
            </summary>
            <param name="count">The number of invocations to check.</param>
            <returns>
              <see langword="true"/> if <paramref name="count"/> matches the constraint described by this instance;
              otherwise, <see langword="false"/>.
            </returns>
        </member>
        <member name="T:Moq.TypeMatcherAttribute">
            <summary>
              Marks a type as a type matcher, optionally specifying another <see cref="T:Moq.ITypeMatcher"/> type that will perform the matching.
              <para>
                Type matchers preferably implement <see cref="T:Moq.ITypeMatcher"/> themselves. Use the parameterized form of this attribute
                where this is not possible, such as when the type matcher needs to be a <see langword="delegate"/> or
                <see langword="enum"/> type in order to satisfy generic type constraints of the method where it is used.
              </para>
            </summary>
        </member>
        <member name="M:Moq.TypeMatcherAttribute.#ctor">
            <summary>
              Initializes a new instance of the <see cref="T:Moq.TypeMatcherAttribute"/> class.
              <para>
                Use this constructor overload if the type on which this attribute is placed implements <see cref="T:Moq.ITypeMatcher"/> itself.
              </para>
            </summary>
        </member>
        <member name="M:Moq.TypeMatcherAttribute.#ctor(System.Type)">
            <summary>
              Initializes a new instance of the <see cref="T:Moq.TypeMatcherAttribute"/> class.
              <para>
                Use this constructor overload if the type on which this attribute is placed does not implement <see cref="T:Moq.ITypeMatcher"/>.
                The specified type will instead provide the implementation of <see cref="T:Moq.ITypeMatcher"/>.
              </para>
            </summary>
            <param name="type">The <see cref="P:Moq.TypeMatcherAttribute.Type"/> of a type that implements <see cref="T:Moq.ITypeMatcher"/>.</param>
        </member>
        <member name="T:Moq.IFluentInterface">
            <summary>
            Interface that is used to build fluent interfaces by hiding methods declared by <see cref="T:System.Object"/> from IntelliSense.
            </summary>
            <remarks>
            Code that consumes implementations of this interface should expect one of two things:
            <list type = "number">
              <item>When referencing the interface from within the same solution (project reference), you will still see the methods this interface is meant to hide.</item>
              <item>When referencing the interface through the compiled output assembly (external reference), the standard Object methods will be hidden as intended.</item>
              <item>When using Resharper, be sure to configure it to respect the attribute: Options, go to Environment | IntelliSense | Completion Appearance and check "Filter members by [EditorBrowsable] attribute".</item>
            </list>
            See https://kzu.github.io/IFluentInterface for more information.
            </remarks>
            <nuget id="IFluentInterface" />
        </member>
        <member name="M:Moq.IFluentInterface.GetType">
            <summary>
            Redeclaration that hides the <see cref="M:System.Object.GetType"/> method from IntelliSense.
            </summary>
        </member>
        <member name="M:Moq.IFluentInterface.GetHashCode">
            <summary>
            Redeclaration that hides the <see cref="M:System.Object.GetHashCode"/> method from IntelliSense.
            </summary>
        </member>
        <member name="M:Moq.IFluentInterface.ToString">
            <summary>
            Redeclaration that hides the <see cref="M:System.Object.ToString"/> method from IntelliSense.
            </summary>
        </member>
        <member name="M:Moq.IFluentInterface.Equals(System.Object)">
            <summary>
            Redeclaration that hides the <see cref="M:System.Object.Equals(System.Object)"/> method from IntelliSense.
            </summary>
        </member>
        <member name="T:TypeNameFormatter.TypeName">
            <summary>
              Contains the two extension methods
              <see cref="M:TypeNameFormatter.TypeName.AppendFormattedName(System.Text.StringBuilder,System.Type,TypeNameFormatter.TypeNameFormatOptions)"/> and
              <see cref="M:TypeNameFormatter.TypeName.GetFormattedName(System.Type,TypeNameFormatter.TypeNameFormatOptions)"/>.
            </summary>
        </member>
        <member name="M:TypeNameFormatter.TypeName.AppendFormattedName(System.Text.StringBuilder,System.Type,TypeNameFormatter.TypeNameFormatOptions)">
            <summary>
              Appends a string representation of the specified type to this instance.
            </summary>
            <param name="stringBuilder">The <see cref="T:System.Text.StringBuilder"/> instance to which to append.</param>
            <param name="type">The <see cref="T:System.Type"/> of which a string representation should be appended.</param>
            <param name="options">Any combination of formatting options that should be applied. (Optional.)</param>
            <returns>A reference to this instance after the append operation has completed.</returns>
        </member>
        <member name="M:TypeNameFormatter.TypeName.GetFormattedName(System.Type,TypeNameFormatter.TypeNameFormatOptions)">
            <summary>
              Gets a string representation of this instance.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> of which a string representation is requested.</param>
            <param name="options">Any combination of formatting options that should be applied. (Optional.)</param>
            <returns>A string representation of this instance.</returns>
        </member>
        <member name="M:TypeNameFormatter.TypeName.IsSet(TypeNameFormatter.TypeNameFormatOptions,TypeNameFormatter.TypeNameFormatOptions)">
            <remarks>
              Replacement for <see cref="M:System.Enum.HasFlag(System.Enum)"/>
              which may be slow or even unavailable on earlier target frameworks.
            </remarks>
        </member>
        <member name="M:TypeNameFormatter.TypeName.GetDeclaredProperties(System.Type)">
            <remarks>
              Allows uniform reflection across all target frameworks.
            </remarks>
        </member>
        <member name="M:TypeNameFormatter.TypeName.GetGenericTypeArguments(System.Type)">
            <remarks>
              Allows uniform reflection across all target frameworks.
            </remarks>
        </member>
        <member name="M:TypeNameFormatter.TypeName.IsGenericType(System.Type)">
            <remarks>
              Allows uniform reflection across all target frameworks.
            </remarks>
        </member>
        <member name="M:TypeNameFormatter.TypeName.IsConstructedGenericType(System.Type)">
            <remarks>
              Allows uniform reflection across all target frameworks.
            </remarks>
        </member>
        <member name="T:TypeNameFormatter.TypeNameFormatOptions">
            <summary>
              An enumeration of available options when a <see cref="T:System.Type"/> name's string representation is requested.
            </summary>
        </member>
        <member name="F:TypeNameFormatter.TypeNameFormatOptions.Default">
            <summary>
              The default type name formatting options.
            </summary>
        </member>
        <member name="F:TypeNameFormatter.TypeNameFormatOptions.Namespaces">
            <summary>
              Specifies that a type's namespace should be included.
              <example>
                For example, the type <see cref="T:System.Action"/> is formatted as <c>"Action"</c> by default.
                When this flag is specified, it will be formatted as <c>"System.Action"</c>.
              </example>
            </summary>
        </member>
        <member name="F:TypeNameFormatter.TypeNameFormatOptions.NoAnonymousTypes">
            <summary>
              Specifies that anonymous types should not be transformed to C#-like syntax.
              <example>
              For example, the anonymous type of <c>"new { Name = "Blob", Count = 17 }"</c> is formatted as
              <c>"{string Name, int Count}"</c> by default. When this flag is specified, it will be formatted as
              the raw "display class" name, which looks something like <c>"&lt;&gt;f__AnonymousType5&lt;string, int&gt;"</c>.
              </example>
            </summary>
        </member>
        <member name="F:TypeNameFormatter.TypeNameFormatOptions.NoGenericParameterNames">
            <summary>
              Specifies that an open generic type's parameter names should be omitted.
              <example>
                For example, the open generic type <see cref="T:System.IEquatable`1"/> is formatted as <c>"IEquatable&lt;T&gt;"</c> by default.
                When this flag is specified, it will be formatted as <c>"IEquatable&lt;&gt;"</c>.
              </example>
            </summary>
        </member>
        <member name="F:TypeNameFormatter.TypeNameFormatOptions.NoKeywords">
            <summary>
              Specifies that primitive types should not be mapped to their corresponding C# language keywords.
              <example>
                For example, the type <see cref="T:System.Int32"/> is formatted as <c>"int"</c> by default.
                When this flag is specified, it will be formatted as <c>"Int32"</c>.
              </example>
            </summary>
        </member>
        <member name="F:TypeNameFormatter.TypeNameFormatOptions.NoNullableQuestionMark">
            <summary>
              Specifies that nullable types should not be simplified to C# question mark syntax.
              <example>
              For example, the type <see cref="T:System.Nullable`1"/> of <see cref="T:System.Int32"/> is formatted as <c>"int?"</c> by default.
              When this flag is specified, it will be formatted as <c>"Nullable&lt;int&gt;"</c>.
              </example>
            </summary>
        </member>
        <member name="F:TypeNameFormatter.TypeNameFormatOptions.NoTuple">
            <summary>
              Specifies that value tuple types should not be transformed to C# tuple syntax.
              <example>
              For example, the type <see cref="T:System.ValueTuple`2"/> of <see cref="T:System.Boolean"/>, <see cref="T:System.Int32"/>
              is formatted as <c>"(bool, int)"</c> by default. When this flag is specified,
              it will be formatted as <c>"ValueTuple&lt;bool, int&gt;"</c>.
              </example>
            </summary>
        </member>
    </members>
</doc>
