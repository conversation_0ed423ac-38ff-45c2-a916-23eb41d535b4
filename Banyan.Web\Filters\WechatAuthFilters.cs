﻿
using Banyan.Apps;
using System;
using System.Linq;
using System.Web.Mvc;
using Banyan.Code;
using Banyan.Domain;

namespace Banyan.Web.Filters
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, Inherited = true, AllowMultiple = false)]
    public class WechatAuthFilterAttribute : ActionFilterAttribute
    {
        public WechatAuthFilterAttribute() { }

        /// <summary>
        /// 是否允许匿名
        /// </summary>
        public bool Allowanonymous { get; set; }

        public sealed override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            if (Allowanonymous) return;
            if (IsAuth(filterContext)) return;
            bool flag = filterContext.ActionDescriptor.IsDefined(typeof(AllowAnonymousAttribute), true);
            if (flag) return;
            UnauthorizedRequest(filterContext);
        }

        public sealed override void OnActionExecuted(ActionExecutedContext filterContext)
        {
            base.OnActionExecuted(filterContext);
        }

        public sealed override void OnResultExecuting(ResultExecutingContext filterContext)
        {
            base.OnResultExecuting(filterContext);
        }

        public sealed override void OnResultExecuted(ResultExecutedContext filterContext)
        {
            base.OnResultExecuted(filterContext);
        }

        private bool IsAuth(ActionExecutingContext filterContext)
        {
            var cookie = filterContext.HttpContext.Request.Cookies["banyan_logon_user"];
            Member user = null;
            if (cookie != null)
            {
                string currUserDes = cookie.Value;
                if (!string.IsNullOrEmpty(currUserDes))
                {
                    string[] desInfo = DESEncrypt.Decrypt(currUserDes).Split(',');

                    try
                    {
                        if (desInfo.Length < 4) return false;
                        string dateTime = desInfo[3];
                        var cookieDate = Convert.ToDateTime(dateTime); 
                        if( cookieDate.AddHours(16).CompareTo(DateTime.Now) < 0 )
                        {
                            return false;
                        }
                        user = new MemberBLL().GetModelByCache(Convert.ToInt32(desInfo[0]));
                    } catch(Exception e)
                    {
                        Logger.Error(e.Message, e);
                        return false;
                    }
                }
            }
            // get cookie
            if (user != null)
            {
                return AuthorizeCore(user.RealName, user.Status);
            }
            else
            {
                return false;
            }
        }

        private void UnauthorizedRequest(ActionExecutingContext filterContext)
        {
            if (filterContext.HttpContext.Request.IsAjaxRequest())
                UnauthorizedAjaxRequest(filterContext);
            else
                UnauthorizedGenericRequest(filterContext);
        }

        /// <summary>
        /// 权限校验
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="userRoles"></param>
        /// <returns></returns>
        protected virtual bool AuthorizeCore(string userName, byte userStatus)
        {
            return userStatus == (int)MemberStatus.enable;
        }

        /// <summary>
        /// 未授权处理
        /// </summary>
        /// <param name="filterContext"></param>
        protected virtual void UnauthorizedGenericRequest(ActionExecutingContext filterContext)
        {
            filterContext.HttpContext.Response.StatusCode = 401;
            filterContext.Result = new JsonResult { Data = new { code = 0, msg = "nologin" }, JsonRequestBehavior = JsonRequestBehavior.AllowGet };
        }

        protected virtual void UnauthorizedAjaxRequest(ActionExecutingContext filterContext)
        {
            var acceptTypes = filterContext.HttpContext.Request.AcceptTypes;
            if (acceptTypes.Contains("*/*") || acceptTypes.Contains("application/json"))
            {
                filterContext.Result = new JsonResult { Data = new { code = 0, msg = "nologin" }, JsonRequestBehavior = JsonRequestBehavior.AllowGet };
            }
            else
            {
                filterContext.Result = new ContentResult { Content = "nologin" };
            }
        }

    }
}