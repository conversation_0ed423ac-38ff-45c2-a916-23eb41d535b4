@model Banyan.Web.Controllers.EngagementRecordsViewModel
@{
    ViewBag.Title = "Engagement Records";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <h2>Engagement Records</h2>
    
    @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
    {
        <div class="alert alert-danger">
            @ViewBag.ErrorMessage
        </div>
    }
    
    <!-- Filter Form -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Filter Records</h5>
        </div>
        <div class="card-body">
            <form method="get" action="@Url.Action("Records")" class="form-inline">
                <div class="form-group mr-2">
                    <label for="startDate" class="mr-2">Start Date:</label>
                    <input type="date" id="startDate" name="startDate" class="form-control" value="@Model.StartDate.ToString("yyyy-MM-dd")" />
                </div>
                <div class="form-group mr-2">
                    <label for="endDate" class="mr-2">End Date:</label>
                    <input type="date" id="endDate" name="endDate" class="form-control" value="@Model.EndDate.ToString("yyyy-MM-dd")" />
                </div>
                <div class="form-group mr-2">
                    <label for="userName" class="mr-2">User Name:</label>
                    <input type="text" id="userName" name="userName" class="form-control" value="@Model.UserName" placeholder="Filter by user name" />
                </div>
                <button type="submit" class="btn btn-primary">Apply Filter</button>
                @if (!string.IsNullOrEmpty(Model.UserName))
                {
                    <a href="@Url.Action("Records", new { startDate = Model.StartDate.ToString("yyyy-MM-dd"), endDate = Model.EndDate.ToString("yyyy-MM-dd") })" class="btn btn-secondary ml-2">
                        Clear User Filter
                    </a>
                }
            </form>
        </div>
    </div>
    
    <!-- Records Table -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Engagement Records</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User Name</th>
                            <th>News ID</th>
                            <th>News Title</th>
                            <th>Timestamp</th>
                            <th>Source</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.Records != null && Model.Records.Any())
                        {
                            foreach (var record in Model.Records)
                            {
                                <tr>
                                    <td>@record.Id</td>
                                    <td>
                                        <a href="@Url.Action("UserDetails", new { userName = record.UserName, startDate = Model.StartDate.ToString("yyyy-MM-dd"), endDate = Model.EndDate.ToString("yyyy-MM-dd") })">
                                            @record.UserName
                                        </a>
                                    </td>
                                    <td>@record.NewsId</td>
                                    <td>@record.NewsTitle</td>
                                    <td>@record.Timestamp.ToString("yyyy-MM-dd HH:mm:ss")</td>
                                    <td>
                                        @if (record.Source.Equals("web", StringComparison.OrdinalIgnoreCase))
                                        {
                                            <span class="badge badge-primary">Web</span>
                                        }
                                        else if (record.Source.Equals("email", StringComparison.OrdinalIgnoreCase))
                                        {
                                            <span class="badge badge-success">Email</span>
                                        }
                                        else
                                        {
                                            <span class="badge badge-secondary">@record.Source</span>
                                        }
                                    </td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="6" class="text-center">No records found</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Pagination -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            Showing page @Model.CurrentPage
        </div>
        <div>
            @if (Model.CurrentPage > 1)
            {
                <a href="@Url.Action("Records", new { startDate = Model.StartDate.ToString("yyyy-MM-dd"), endDate = Model.EndDate.ToString("yyyy-MM-dd"), userName = Model.UserName, page = Model.CurrentPage - 1 })" class="btn btn-outline-primary">
                    Previous Page
                </a>
            }
            
            @if (Model.Records != null && Model.Records.Count == Model.PageSize)
            {
                <a href="@Url.Action("Records", new { startDate = Model.StartDate.ToString("yyyy-MM-dd"), endDate = Model.EndDate.ToString("yyyy-MM-dd"), userName = Model.UserName, page = Model.CurrentPage + 1 })" class="btn btn-outline-primary ml-2">
                    Next Page
                </a>
            }
        </div>
    </div>
    
    <!-- Export Button -->
    <div class="mb-4">
        <a href="@Url.Action("ExportCsv", new { startDate = Model.StartDate.ToString("yyyy-MM-dd"), endDate = Model.EndDate.ToString("yyyy-MM-dd") })" class="btn btn-success">
            <i class="fa fa-download"></i> Export to CSV
        </a>
        <a href="@Url.Action("Index", new { startDate = Model.StartDate.ToString("yyyy-MM-dd"), endDate = Model.EndDate.ToString("yyyy-MM-dd") })" class="btn btn-primary">
            <i class="fa fa-chart-bar"></i> Back to Dashboard
        </a>
    </div>
</div>