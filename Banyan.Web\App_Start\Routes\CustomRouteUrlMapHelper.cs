﻿using System;
using System.Linq;
using System.Web.Mvc;
using System.Web.Routing;

namespace Banyan.Web
{
    public static class CustomRouteUrlMapHelper
    {
        public static CustomRouteUrl MapCustomRouteUrl(this RouteCollection routes, string name, string url)
        {
            return routes.MapCustomRouteUrl(name, url, null, null);
        }
        public static CustomRouteUrl MapCustomRouteUrl(this RouteCollection routes, string name, string url, object defaults)
        {
            return routes.MapCustomRouteUrl(name, url, defaults, null);
        }
        public static CustomRouteUrl MapCustomRouteUrl(this RouteCollection routes, string name, string url, string[] namespaces)
        {
            return routes.MapCustomRouteUrl(name, url, null, null, namespaces);
        }
        public static CustomRouteUrl MapCustomRouteUrl(this RouteCollection routes, string name, string url, object defaults, object constraints)
        {
            return routes.MapCustomRouteUrl(name, url, defaults, constraints, null);
        }
        public static CustomRouteUrl MapCustomRouteUrl(this RouteCollection routes, string name, string url, object defaults, string[] namespaces)
        {
            return routes.MapCustomRouteUrl(name, url, defaults, null, namespaces);
        }
        public static CustomRouteUrl MapCustomRouteUrl(this RouteCollection routes, string name, string url, object defaults, object constraints, string[] namespaces)
        {
            if (routes == null) throw new ArgumentNullException("routes");
            if (url == null) throw new ArgumentNullException("url");
            CustomRouteUrl route2 = new CustomRouteUrl(url, new MvcRouteHandler());
            route2.Defaults = new RouteValueDictionary(defaults);
            route2.Constraints = new RouteValueDictionary(constraints);
            route2.DataTokens = new RouteValueDictionary();
            CustomRouteUrl item = route2;
            if ((namespaces != null) && (namespaces.Length > 0))
                item.DataTokens["Namespaces"] = namespaces;
            routes.Add(name, item);
            return item;
        }

        public static CustomRouteUrl MapCustomRouteUrl(this AreaRegistrationContext context, string name, string url)
        {
            return context.MapCustomRouteUrl(name, url, null);
        }
        public static CustomRouteUrl MapCustomRouteUrl(this AreaRegistrationContext context, string name, string url, object defaults)
        {
            return context.MapCustomRouteUrl(name, url, defaults, null);
        }
        public static CustomRouteUrl MapCustomRouteUrl(this AreaRegistrationContext context, string name, string url, string[] namespaces)
        {
            return context.MapCustomRouteUrl(name, url, null, namespaces);
        }
        public static CustomRouteUrl MapCustomRouteUrl(this AreaRegistrationContext context, string name, string url, object defaults, object constraints)
        {
            return context.MapCustomRouteUrl(name, url, defaults, constraints, null);
        }
        public static CustomRouteUrl MapCustomRouteUrl(this AreaRegistrationContext context, string name, string url, object defaults, string[] namespaces)
        {
            return context.MapCustomRouteUrl(name, url, defaults, null, namespaces);
        }
        public static CustomRouteUrl MapCustomRouteUrl(this AreaRegistrationContext context, string name, string url, object defaults, object constraints, string[] namespaces)
        {
            if ((namespaces == null) && (context.Namespaces != null))
                namespaces = context.Namespaces.ToArray<string>();
            CustomRouteUrl route = context.Routes.MapCustomRouteUrl(name, url, defaults, constraints, namespaces);
            route.DataTokens["area"] = context.AreaName;
            bool flag = (namespaces == null) || (namespaces.Length == 0);
            route.DataTokens["UseNamespaceFallback"] = flag;
            return route;
        }
    }
}