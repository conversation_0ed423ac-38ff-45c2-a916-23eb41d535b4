﻿@{
    Layout = null;
    var logUser = ViewData["manager"] as Banyan.Domain.Member;
    var urlPath = Request.Url.AbsolutePath;
}
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no" charset="utf-8" />
    <title>高榕创投IMS-GAORONG VENTURES</title>
    <link rel="shortcut icon" href="~/content/img/favicons/favicon.ico">
    <link rel="icon" type="image/png" href="~/content/img/favicons/favicon.ico" sizes="16x16">
    <link rel="icon" type="image/png" href="~/content/img/favicons/favicon.ico" sizes="32x32">

    <link href="~/content/css/bootstrap.min.css" type="text/css" rel="stylesheet" />
    <link href="~/content/css/oneui.min.css" type="text/css" rel="stylesheet" />
    <link type="text/css" href="/content/js/plugins/layui/css/layui.css" rel="stylesheet" />

    <style>
        td .layui-table-cell, .layui-table-cell img {
            height: 35px !important;
            line-height: 35px !important;
        }
        .select2-container--open .select2-dropdown {
            left: 0;
            z-index: 999999999;
        }
        .water-mark {
            font-size: 14px;
            color: #c2c2c2; /* 颜色会动态重写，根据配置 */
            position: fixed;
            padding: 0 15px;
            transform: translate(-50%, -50%);
            transform: rotate(-30deg);
            -ms-transform: rotate(-30deg); /* IE 9 */
            -moz-transform: rotate(-30deg); /* Firefox */
            -webkit-transform: rotate(-30deg); /* Safari 和 Chrome */
            -o-transform: rotate(-30deg);
            opacity: 0.3;
            overflow: hidden;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            pointer-events: none;
        }
    </style>

</head>
<body>
    <div id="page-container" class="">
        <main id="main-container" style="padding-top:75px;">
            <div id="ie-warn" style="display:none;color: red; text-align:center; font-weight:500;">不兼容IE浏览器，请使用Chrome、FireFox、Edge浏览器访问!</div>
            @RenderBody()
        </main>
    </div>
    <div style="width:1px;height:1px;overflow:hidden;position:relative">
        @RenderSection("modal", false)
    </div>
    <script type="text/javascript" src="~/content/js/core/jquery.min.js"></script>
    <script type="text/javascript" src="~/content/js/oneui.min.js"></script>
    <script type="text/javascript" src="~/content/js/plugins/layui/layui.js"></script>
    <script type="text/javascript" src="/content/js/vue/vue.min.js"></script>
    <!--
    要收集有关应用程序的最终用户使用情况分析,
    请将以下脚本插入要跟踪的每一页。
    将此代码放置在紧挨结尾 </head> 标记前的位置，
    并放置在其他任何脚本之前。第一个数据
    将在几秒内自动出现。
    >
    <script type="text/javascript">
        var appInsights = window.appInsights || function (a) {
            function b(a) { c[a] = function () { var b = arguments; c.queue.push(function () { c[a].apply(c, b) }) } } var c = { config: a }, d = document, e = window; setTimeout(function () { var b = d.createElement("script"); b.src = a.url || "https://az416426.vo.msecnd.net/scripts/a/ai.0.js", d.getElementsByTagName("script")[0].parentNode.appendChild(b) }); try { c.cookie = d.cookie } catch (a) { } c.queue = []; for (var f = ["Event", "Exception", "Metric", "PageView", "Trace", "Dependency"]; f.length;)b("track" + f.pop()); if (b("setAuthenticatedUserContext"), b("clearAuthenticatedUserContext"), b("startTrackEvent"), b("stopTrackEvent"), b("startTrackPage"), b("stopTrackPage"), b("flush"), !a.disableExceptionTracking) { f = "onerror", b("_" + f); var g = e[f]; e[f] = function (a, b, d, e, h) { var i = g && g(a, b, d, e, h); return !0 !== i && c["_" + f](a, b, d, e, h), i } } return c
        }({
            instrumentationKey: "0ed5ef8f-22f4-4ca8-875b-5c9c63d33407"
        });

        window.appInsights = appInsights, appInsights.queue && 0 === appInsights.queue.length && appInsights.trackPageView();
    </script-->
    <script type="text/javascript" src="~/Content/js/utils.js"></script>

    @RenderSection("scripts", false)
    <script type="text/javascript">
    window.addEventListener('keydown', function (e) {
        if (e.keyCode == 83 && (navigator.platform.match('Mac') ? e.metaKey : e.ctrlKey)) {
            e.preventDefault();
        }
    })
    var str = '@(logUser.RealName)';
    var layer, urlPath = '@(urlPath)';
    layui.use(['layer'], function () {
        var layer = layui.layer;
    });

    $(function () {
        //onload时触发水印绘制
        window.onload = function () {
            watermark({ watermark_txt: str });
        };
        //onresize时触发水印绘制
        //window.onresize = function () {
        //    watermark({ watermark_txt: str });
        //};

        var node = $("[href='" + urlPath + "']");
        node.addClass('active');
        node.parent().parent().parent('li').addClass('open');

        $('#logout').on('click', function () {
            $.ajax({
                type: 'POST',
                url: '@(Url.Action("logout", "adminapi"))',
                success: function (data) {
                    if (data.code == 0) {
                        layer.msg('注销成功！');
                        setTimeout(function () {
                            window.location.href = '@(Url.Action("index", "login"))';
                        }, 1500);
                    } else {
                        layer.msg(data.msg);
                    }
                },
                error: function () {
                    layer.msg("操作失败！");
                }
            });
        });
    });

    function openmodal(title, url, width, height) {
        width = width || "75%";
        height = height || "100%";
        var index = layui.layer.open({
            type: 2,
            title: title,
            content: url,
            area: [width, height]
        });
    }


    function watermark(settings) {
        //debugger;
        //默认设置
        var defaultSettings = {
            watermark_txt: "text",
            watermark_x: 100,//水印起始位置x轴坐标
            watermark_y: 100,//水印起始位置Y轴坐标
            watermark_rows: 0,//水印行数
            watermark_cols: 0,//水印列数
            watermark_x_space: 60,//水印x轴间隔
            watermark_y_space: 60,//水印y轴间隔
            watermark_color: '#aaa',//水印字体颜色
            watermark_alpha: 0.4,//水印透明度
            watermark_fontsize: '15px',//水印字体大小
            watermark_font: '微软雅黑',//水印字体
            watermark_width: 100,//水印宽度
            watermark_height: 100,//水印长度
            watermark_angle: 30//水印倾斜度数
        };
        //采用配置项替换默认值，作用类似jquery.extend
        if (arguments.length === 1 && typeof arguments[0] === "object") {
            var src = arguments[0] || {};
            for (key in src) {
                if (src[key] && defaultSettings[key] && src[key] === defaultSettings[key])
                    continue;
                else if (src[key])
                    defaultSettings[key] = src[key];
            }
        }

        var oTemp = document.createDocumentFragment();

        //获取页面最大宽度
        var page_width = Math.max(document.body.scrollWidth, document.body.clientWidth);
        var cutWidth = page_width * 0.0150;
        var page_width = page_width - cutWidth;
        //获取页面最大高度
        var page_height = Math.max(document.body.scrollHeight, document.body.clientHeight);
        // var page_height = document.body.scrollHeight+document.body.scrollTop;
        //如果将水印列数设置为0，或水印列数设置过大，超过页面最大宽度，则重新计算水印列数和水印x轴间隔
        if (defaultSettings.watermark_cols == 0 || (parseInt(defaultSettings.watermark_x + defaultSettings.watermark_width * defaultSettings.watermark_cols + defaultSettings.watermark_x_space * (defaultSettings.watermark_cols - 1)) > page_width)) {
            defaultSettings.watermark_cols = parseInt((page_width - defaultSettings.watermark_x + defaultSettings.watermark_x_space) / (defaultSettings.watermark_width + defaultSettings.watermark_x_space));
            defaultSettings.watermark_x_space = parseInt((page_width - defaultSettings.watermark_x - defaultSettings.watermark_width * defaultSettings.watermark_cols) / (defaultSettings.watermark_cols - 1));
        }
        //如果将水印行数设置为0，或水印行数设置过大，超过页面最大长度，则重新计算水印行数和水印y轴间隔
        if (defaultSettings.watermark_rows == 0 || (parseInt(defaultSettings.watermark_y + defaultSettings.watermark_height * defaultSettings.watermark_rows + defaultSettings.watermark_y_space * (defaultSettings.watermark_rows - 1)) > page_height)) {
            defaultSettings.watermark_rows = parseInt((defaultSettings.watermark_y_space + page_height - defaultSettings.watermark_y) / (defaultSettings.watermark_height + defaultSettings.watermark_y_space));
            defaultSettings.watermark_y_space = parseInt(((page_height - defaultSettings.watermark_y) - defaultSettings.watermark_height * defaultSettings.watermark_rows) / (defaultSettings.watermark_rows - 1));
        }
        var x;
        var y;
        for (var i = 0; i < defaultSettings.watermark_rows; i++) {
            y = defaultSettings.watermark_y + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i;
            for (var j = 0; j < defaultSettings.watermark_cols; j++) {
                x = defaultSettings.watermark_x + (defaultSettings.watermark_width + defaultSettings.watermark_x_space) * j;

                var mask_div = document.createElement('div');
                mask_div.id = 'mask_div' + i + j;
                mask_div.className = 'water-mark';
                mask_div.appendChild(document.createTextNode(defaultSettings.watermark_txt));
                //设置水印div倾斜显示

                mask_div.style.visibility = "";
                mask_div.style.left = x + 'px';
                mask_div.style.top = y + 'px';
                mask_div.style.zIndex = "9999"; // 弹窗为19891016左右
              //mask_div.style.border="solid #eee 1px";
                mask_div.style.textAlign = "center";
                mask_div.style.width = defaultSettings.watermark_width + 'px';
                mask_div.style.height = defaultSettings.watermark_height + 'px';
                mask_div.style.display = "block";
                oTemp.appendChild(mask_div);
            };
        };
        document.body.appendChild(oTemp);

        if (!!window.ActiveXObject || "ActiveXObject" in window)
            document.getElementById("ie-warn").style.display = "block";

    }
    </script>

    <script type="text/javascript">
        !function (T, l, y) {
            var S = T.location, u = "script", k = "instrumentationKey", D = "ingestionendpoint",
                C = "disableExceptionTracking", E = "ai.device.", I = "toLowerCase", b = "crossOrigin",
                w = "POST", e = "appInsightsSDK", t = y.name || "appInsights";
            (y.name || T[e]) && (T[e] = t); var n = T[t] || function (d) {
                var g = !1, f = !1, m = { initialize: !0, queue: [], sv: "4", version: 2, config: d };
                function v(e, t) { var n = {}, a = "Browser"; return n[E + "id"] = a[I](), n[E + "type"] = a, n["ai.operation.name"] = S && S.pathname || "_unknown_", n["ai.internal.sdkVersion"] = "javascript:snippet_" + (m.sv || m.version), { time: function () { var e = new Date; function t(e) { var t = "" + e; return 1 === t.length && (t = "0" + t), t } return e.getUTCFullYear() + "-" + t(1 + e.getUTCMonth()) + "-" + t(e.getUTCDate()) + "T" + t(e.getUTCHours()) + ":" + t(e.getUTCMinutes()) + ":" + t(e.getUTCSeconds()) + "." + ((e.getUTCMilliseconds() / 1e3).toFixed(3) + "").slice(2, 5) + "Z" }(), iKey: e, name: "Microsoft.ApplicationInsights." + e.replace(/-/g, "") + "." + t, sampleRate: 100, tags: n, data: { baseData: { ver: 2 } } } } var h = d.url || y.src; if (h) { function a(e) { var t, n, a, i, r, o, s, c, p, l, u; g = !0, m.queue = [], f || (f = !0, t = h, s = function () { var e = {}, t = d.connectionString; if (t) for (var n = t.split(";"), a = 0; a < n.length; a++) { var i = n[a].split("="); 2 === i.length && (e[i[0][I]()] = i[1]) } if (!e[D]) { var r = e.endpointsuffix, o = r ? e.location : null; e[D] = "https://" + (o ? o + "." : "") + "dc." + (r || "services.visualstudio.com") } return e }(), c = s[k] || d[k] || "", p = s[D], l = p ? p + "/v2/track" : config.endpointUrl, (u = []).push((n = "SDK LOAD Failure: Failed to load Application Insights SDK script (See stack for details)", a = t, i = l, (o = (r = v(c, "Exception")).data).baseType = "ExceptionData", o.baseData.exceptions = [{ typeName: "SDKLoadFailed", message: n.replace(/\./g, "-"), hasFullStack: !1, stack: n + "\nSnippet failed to load [" + a + "] -- Telemetry is disabled\nHelp Link: https://go.microsoft.com/fwlink/?linkid=2128109\nHost: " + (S && S.pathname || "_unknown_") + "\nEndpoint: " + i, parsedStack: [] }], r)), u.push(function (e, t, n, a) { var i = v(c, "Message"), r = i.data; r.baseType = "MessageData"; var o = r.baseData; return o.message = 'AI (Internal): 99 message:"' + ("SDK LOAD Failure: Failed to load Application Insights SDK script (See stack for details) (" + n + ")").replace(/\"/g, "") + '"', o.properties = { endpoint: a }, i }(0, 0, t, l)), function (e, t) { if (JSON) { var n = T.fetch; if (n && !y.useXhr) n(t, { method: w, body: JSON.stringify(e), mode: "cors" }); else if (XMLHttpRequest) { var a = new XMLHttpRequest; a.open(w, t), a.setRequestHeader("Content-type", "application/json"), a.send(JSON.stringify(e)) } } }(u, l)) } function i(e, t) { f || setTimeout(function () { !t && m.core || a() }, 500) } var e = function () { var n = l.createElement(u); n.src = h; var e = y[b]; return !e && "" !== e || "undefined" == n[b] || (n[b] = e), n.onload = i, n.onerror = a, n.onreadystatechange = function (e, t) { "loaded" !== n.readyState && "complete" !== n.readyState || i(0, t) }, n }(); y.ld < 0 ? l.getElementsByTagName("head")[0].appendChild(e) : setTimeout(function () { l.getElementsByTagName(u)[0].parentNode.appendChild(e) }, y.ld || 0) } try { m.cookie = l.cookie } catch (p) { } function t(e) { for (; e.length;)!function (t) { m[t] = function () { var e = arguments; g || m.queue.push(function () { m[t].apply(m, e) }) } }(e.pop()) } var n = "track", r = "TrackPage", o = "TrackEvent"; t([n + "Event", n + "PageView", n + "Exception", n + "Trace", n + "DependencyData", n + "Metric", n + "PageViewPerformance", "start" + r, "stop" + r, "start" + o, "stop" + o, "addTelemetryInitializer", "setAuthenticatedUserContext", "clearAuthenticatedUserContext", "flush"]), m.SeverityLevel = { Verbose: 0, Information: 1, Warning: 2, Error: 3, Critical: 4 }; var s = (d.extensionConfig || {}).ApplicationInsightsAnalytics || {}; if (!0 !== d[C] && !0 !== s[C]) { method = "onerror", t(["_" + method]); var c = T[method]; T[method] = function (e, t, n, a, i) { var r = c && c(e, t, n, a, i); return !0 !== r && m["_" + method]({ message: e, url: t, lineNumber: n, columnNumber: a, error: i }), r }, d.autoExceptionInstrumented = !0 } return m
            }(y.cfg); (T[t] = n).queue && 0 === n.queue.length && n.trackPageView({})
        }(window, document, {
            src: "https://az416426.vo.msecnd.net/scripts/b/ai.2.min.js", // The SDK URL Source
            //name: "appInsights", // Global SDK Instance name defaults to "appInsights" when not supplied
            //ld: 0, // Defines the load delay (in ms) before attempting to load the sdk. -1 = block page load and add to head. (default) = 0ms load after timeout,
            //useXhr: 1, // Use XHR instead of fetch to report failures (if available),
            //crossOrigin: "anonymous", // When supplied this will add the provided value as the cross origin attribute on the script tag
            cfg: { // Application Insights Configuration
                instrumentationKey: "fe66cd04-ba9c-c9b4-8f6e-27746969021c",
                endpointUrl: "https://dc.applicationinsights.azure.cn/v2/track",
                enableDebug: true
                /* ...Other Configuration Options... */
            }
        });
    </script>
</body>
</html>
