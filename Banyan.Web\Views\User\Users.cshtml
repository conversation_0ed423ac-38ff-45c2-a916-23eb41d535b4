﻿@{ Layout = "/Views/Shared/_LayoutAdmin.cshtml";
                var roleList = (List<Banyan.Domain.Role>)ViewData["rolelist"];
                var manager = ViewData["manager"] as Banyan.Domain.Member; }
<link rel="stylesheet" href="~/Content/css/interest-tags.css" />

<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li>
                    <i class="si si-pointer"></i>
                </li>
                <li>用户管理</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <input type="hidden" id="super" value="@(manager == null ? "false" : (manager.Levels == (byte)Banyan.Domain.MemberLevels.Administrator).ToString())" />
            <div class="tab-pane active">
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="pull-right search-bar"></div>
                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                                <div class="form-group">
                                    <select class="form-control" id="role" name="role" size="1">
                                        <option value="">全部</option>
                                        @if (roleList != null && roleList.Count() > 0)
                                        {
                                            foreach (var rli in roleList)
                                            {
                            <option value="@(rli.Id)">@(rli.RoleName)</option>
}
                    }
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="title-name">用户名称</label>
                                    <input class="form-control" type="text" id="uname" name="uname" placeholder="请输入用户名称">
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-default" id="dosearch">搜索</button>
                                    <div class="btn" href="void(0)" target="_blank" title="用户注册后进入underReview组等待审核，审批后需从该组删除；根据权限将用户加入相应系统子分组；leave组为禁用">使用说明</div>
                                    <a class="btn btn-primary" href="javascript:;" id="new-user"><i class="fa fa-plus"></i>&nbsp;&nbsp;新建用户</a>
                                    <a class="btn btn-primary" href="https://ldap.gaorongvc.cn" target="_blank" title="cn=姓名,ou=users,dc=gaorongvc,dc=cn">编辑用户</a>
                                    <a class="btn btn-success" href="javascript:;" id="batch-update-profile"><i class="fa fa-refresh"></i>&nbsp;&nbsp;更新当前页面用户画像</a>
                                    <label for="filter-leave">过滤已禁用</label><input type="CheckBox" id="filter-leave" name="filter-leave" checked />
                                    <label for="all-users">显示全部用户</label><input type="CheckBox" id="all-users" name="all-users" />
                                    <label for="show-interest-tags">显示兴趣标签</label><input type="CheckBox" id="show-interest-tags" name="show-interest-tags" />
                                    @*<a class="btn btn-primary" href="javascript:;" onclick="parent.openmodal('新建用户','@(Url.Action("UserSet","User"))','400px','570px')"><i class="fa fa-plus"></i>&nbsp;&nbsp;新建用户</a>*@
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <table class="layui-hide" id="table-list" lay-filter="list-filter"></table>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="bartpl">
    {{#  if(d.Levels != 1){ }}
    @*<button class="btn btn-xs btn-default {{d.Status == 3 ? '':'hidden'}}" style="display:none;" type="button" title="审核" lay-event="verify"><i class="fa fa-sign-in"></i></button>*@
    <button class="btn btn-xs {{d.Status == 2 ? 'btn-warning' : 'btn-default'}}" style="display:none;" type="button" title="{{d.Status == 2 ? '启用' : '禁用'}}" lay-event="enable"><i class="fa fa-toggle-off"></i></button>
    <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="编辑" data-original-title="编辑" lay-event="modify"><i class="fa fa-pencil"></i></button>
    @*<button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="删除" data-original-title="删除" lay-event="delete"><i class="fa fa-times"></i></button>*@
    {{#  } }}
</script>

@section scripts{
    <script type="text/javascript" src="/content/js/commonhp2.js"></script>
    <script type="text/javascript">
        var layeridx;
        function getParameter(el) {
            var obj = {};
            $(el).each(function (index, item) {
                // 判断元素的类型
                if (item.type == "text" || item.type == "password" || item.type == "select-one" || item.type == "tel" ||
                    item.type == "search" || item.type == "range" || item.type == "number" || item.type == "month" ||
                    item.type == "email" || item.type == "datetime-local" || item.type == "datetime" || item.type == "date" ||
                    item.type == "color") {
                    //获取到name的值,name的值就是向后台传递的数据
                    obj[$(this).attr("name")] = $(this).val();
                } else if (item.type == "checkbox") {
                    var stamp = false;
                    if ($(this).attr("name") && !stamp) {
                        stamp = false;
                        // 获取到复选框选中的元素
                        var checkboxEl = $("input[name='" + $(item).attr('name') + "']:checked");
                        if (checkboxEl) {
                            var checkboxArr = [];
                            // 取出复选框选中的值
                            checkboxEl.each(function (idx, itm) {
                                checkboxArr.push($(itm).val());
                            });
                            obj[$(this).attr("name")] = checkboxArr.join(",");
                        }

                    }
                } else if (item.type == "radio") {
                    // 获取到单选框选中的值
                    var radio_val = $("input[name=" + $(item).attr('name') + "]:checked").val();
                    if (radio_val) {
                        obj[$(item).attr("name")] = radio_val;
                    }
                }
            });
            return obj;
        }
        saveUser = function (isMock = false) {
            // document.forms[1].elements
            // 通过description 去修改组员
            var parameter = getParameter(".query");
            console.log(parameter);
            if (parameter["cn=underReview"] == "on" && (parameter["ims-user"] != "待审核" || parameter["research-user"] != "待审核")) {
                return alert("请取消待审核状态")
            }
            if (parameter["cn=underReview"] == "" && (parameter["ims-user"] == "待审核" || parameter["research-user"] == "待审核")) {
                return alert("请审核所有权限")
            }
            if (!currentUser) {
                return alert("用户未选择错误")
            }
            res = {}

            for (key in parameter) {
                if (parameter[key] && parameter[key] != "待审核" && parameter[key] != "normal") {
                    res[key] = parameter[key]
                }
            }
            if (res.mobile && !isphone(res.mobile)) {
                alert('请填写有效的手机号');
                return false;
            }

            if (!ismail(res.mail)) {
                alert('请填写有效的邮箱');
                return false;
            }
            var mobile = res.mobile
            var mail = res.mail
            var description = res.description
            delete res["mobile"]
            delete res["mail"]
            delete res["description"]
            var url = '@(Url.Action("usersavegroup", "adminapi"))'
            if (isMock) {
                url =  '@(Url.Action("usermock", "adminapi"))'
            }
            $.ajax({
                type: 'POST',
                url,
                data: { name: currentUser, groups: JSON.stringify(res), mobile, mail, description },
                success: function (data) {
                    if (data) {
                        layer.msg('操作成功！');
                        tableIns.reload();
                        layer.close(layeridx);
                    } else {
                        layer.msg('操作失败');
                    }
                },
                error: function () {
                    layui.layer.msg("很抱歉，请求异常！");
                }
            });

        }

        function GetStringDate(dateStr) {
            if (!dateStr) return ''
            var date = eval('new ' + dateStr.substr(1, dateStr.length - 2));
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            var res = year + "-" + month + "-" + day;
            if (res == "1-1-1") return "";
            return res;
        }
        currentUser = ''
        var layer, date = new Date();
        var tableIns;
        var userInterestTags = {}; // 全局变量存储用户兴趣标签
        var showInterestTags = false; // 是否显示兴趣标签
        layui.use(['laypage', 'layer', 'table'], function () {
            var laypage = layui.laypage,
                table = layui.table;
            layer = layui.layer

            tableIns = table.render({
                id: 'table-list',
                elem: '#table-list'
                , height: 700
                , url: '@(Url.Action("userlistdetail", "adminapi"))'
                , page: {limit: 200, limits:[200]}
                , method: 'post'
                , where: {
                    filterleave: "on"  // 默认过滤已禁用用户
                }
                , cols: [[
                    //{ field: 'Id', title: 'ID', width: 80, sort: true, fixed: 'left' }
                    { field: 'select', title: '编辑', width: 50, fixed: 'left', type: "radio" }
                    , { field: 'RealName', title: '姓名', width: 90, fixed: 'left', sort: true }


                    //, { field: 'CompanyName', title: '公司名称', width: 160 }
                    , {
                        field: 'RoleName', title: 'IMS权限', sort: true, width: 220, templet: function (d) {
                            var label = ''
                            if (d.Status == 4) {
                                label += '<span class="label label-danger">审核不通过</span>';
                            };
                            if (d.limitedJoinTime) {
                                label += '<span class="label label-warning">加入时间受限用户：</span>&nbsp;';
                            }
                            if (d.limitedMedical) {
                                label += '<span class="label label-danger">医疗组内可见受限</span>&nbsp;';
                            }
                            if (d.Levels == 0 ) {
                            }
                            else if (d.Levels == 5) {
                                label += '<span class="label label-danger">投资合伙人</span>&nbsp;';
                            }
                            else if (d.Levels == 9) {
                                label += '<span class="label label-danger">Deal美元</span>&nbsp;';
                            }
                            else if (d.Levels == 10) {
                                label += '<span class="label label-danger">Deal人民币</span>&nbsp;';
                            }
                            else if (d.Levels == 11) {
                                label += '<span class="label label-danger">Deal所有</span>&nbsp;';
                            }
                            else if (d.Levels == 21) {
                                label += '<span class="label label-danger">仅可查看美元项目</span>&nbsp;';
                            }
                            else if (d.Levels == 22) {
                                label += '<span class="label label-danger">仅可查看人民币项目</span>&nbsp;';
                            }
                            else if (d.Levels == 1) {
                                label = '<span class="label label-danger">管理员</span>';
                            } else if (d.Levels == 2) {
                                label += '<span class="label label-danger">超级用户：</span>&nbsp;';
                            }
                            else if (d.Levels == 3) {
                                label += '<span class="label label-warning">受限用户：</span>&nbsp;';
                            }
                            var labelArr = d.RoleName.split(',');
                            labelArr.forEach(function (v, i) {
                                label += '<span class="label label-info">' + v + '</span>&nbsp;';
                            });
                            return label;
                        }
                    }
                    , {
                        field: 'RoleName', title: 'Research权限', width: 130, sort: true, templet: function (d) {
                            var label = '';
                            if (d.StatusResearch == 14) {
                                label += '<span class="label label-danger">审核不通过</span>';
                            }

                            if (d.Status == 1 && d.StatusResearch != 14 && d.LevelResearch == 10) {
                                label += '<span class="label label-info">普通用户</span>';
                            } else if (d.LevelResearch == 11) {
                                label += '<span class="label label-success">普通编辑</span>';
                            } else if (d.LevelResearch == 12) {
                                label += '<span class="label label-success">编辑管理员</span>';
                            } else if (d.LevelResearch == 13) {
                                label += '<span class="label label-info">投资团队成员</span>';
                            } else if (d.LevelResearch == 14) {
                                label += '<span class="label label-danger">合伙人</span>';
                            } else if (d.LevelResearch == 15) {
                                label += '<span class="label label-danger">超级管理员</span>';
                            } else if (d.LevelResearch == 16) {
                                label += '<span class="label label-warning">受限实习生</span>';
                            }

                            return label;
                        }
                    }
                    , { field: 'Telephone', title: '手机', width: 110 }
                    , { field: 'Mail', title: '邮箱', width: 180 }
                    , {
                        field: 'Avatar', title: '头像', width: 80, sort: true, templet: function (d) {
                            return `<image src="${d.Avatar}"/>`
                        }
                    }
                    , {
                        field: 'OpenId', title: '使用ims小程序', sort: true, width: 150, templet: function (d) {
                            return d.OpenId ? "是" : "";
                       }
                    }
                    //, {
                    //    field: 'AddTime', title: '创建时间', width: 160, templet: function (d) {
                    //        return (new Date(parseInt(d.AddTime.replace("/Date(", "").replace(")/", "").split("+")[0]))).pattern("yyyy-MM-dd HH:mm:ss");
                    //    }}
                    , {
                        field: 'Status', title: '用户状态', width: 100,  sort: true, templet: function (d) {
                            var statusHtml = '';
                            if (d.Levels == 1)
                                return statusHtml;

                            //if (d.Status == 1) {
                            //    statusHtml = '<span class="label label-success">已通过</span>';
                            //} else
                            if (d.Status == 2) {
                                statusHtml = '<span class="label label-danger">已禁用</span>';
                            } else if (d.Status == 3) {
                                statusHtml = '<span class="label label label-warning">待审核</span>';
                            }
                            return statusHtml;
                        }
                    }
                    , {
                        field: 'AddTime', title: '添加时间', width: 180, sort:true,templet: function (d) {
                            return GetStringDate(d.AddTime);
                        }
                    }, {
                        field: 'Description', title: '描述', width: 120
                    }
                    , {
                        field: 'InterestTags', title: '兴趣标签', width: 250, sort: true, templet: function (d) {
                            console.log('Template function called for user:', d.Id, 'showInterestTags:', showInterestTags);

                            if (!showInterestTags) {
                                return '<span class="text-muted">请勾选"显示兴趣标签"加载数据</span>';
                            }

                            // 从全局变量获取用户的兴趣标签
                            var userTags = userInterestTags[d.Id.toString()];
                            console.log('User', d.Id, 'tags from global var:', userTags);

                            if (userTags && userTags.length > 0) {
                                var tagsHtml = '';
                                userTags.forEach(function(tag) {
                                    var weight = Math.round(tag.Weight * 100);
                                    var tagClass = weight >= 70 ? 'label-danger' :
                                                  weight >= 40 ? 'label-warning' : 'label-info';
                                    tagsHtml += '<span class="label ' + tagClass + '" title="权重: ' + weight + '%">' +
                                                tag.Name + ' (' + weight + '%)</span>&nbsp;';
                                });
                                return tagsHtml;
                            } else {
                                return '<span class="text-muted">无标签数据</span>';
                            }
                        }
                    }
                    //, { fixed: 'right', title: '操作', width: 160, align: 'center', toolbar: '#bartpl' }
                ]],
                done: function () {
                    if ($('#super').val().toLowerCase() == 'true') {
                        $('[lay-event="verify"]').show();
                        $('[lay-event="enable"]').show();
                    }
                }
            });

            createUser = function (e) {
                // document.forms[1].elements
                // 通过description 去修改组员
                var parameter = getParameter(".query");

                res = {}
                for (key in parameter) {
                    if (parameter[key]) {
                        res[key] = parameter[key]
                    }
                }
                var parameterGroup = getParameter(".query-group");
                resGroup = {}
                for (key in parameterGroup) {
                    if (parameterGroup[key]) {
                        resGroup[key] = parameterGroup[key]
                    }
                }

                if (!res.cn || !res.mobile || !res.mail) {
                    alert("姓名，手机，邮箱必填填写！")
                    return
                }

                if (res.mobile && !isphone(res.mobile)) {
                    alert('请填写有效的手机号');
                    return false;
                }

                if (!ismail(res.mail)) {
                    alert('请填写有效的邮箱');
                    return false;
                }
                $.ajax({
                    type: 'POST',
                    url: '@(Url.Action("usercreate", "adminapi"))',
                    data: { name: currentUser, attrs: JSON.stringify(res), groups: JSON.stringify(resGroup) },
                    success: function (data) {
                        if (data) {
                            layer.msg('操作成功！');
                            tableIns.reload();
                            layer.close(layeridx);
                        } else {
                            layer.msg('操作失败');
                        }
                    },
                    error: function () {
                        layui.layer.msg("很抱歉，请求异常！");
                    }
                });
            }

            function genInput(name, value, label, type = "text") {
                if (value) {
                    value = `value=${value}`
                }
                return `<div style="margin-top:10px"> <label for=${name} style="margin-right: 10px">${label}</label>
                            <input style="width: 250px" type=${type} class="query" name=${name} ${value} id=${name}></input>
                         </div>`
            }

               $('#new-user').on('click', function () {
                var str = `<form class="edit-modal" style='margin: 20px'>
                          <div class="roles" style="margin-top: 10px">`
                str += genInput("cn", "", "姓名")
                str += genInput("mobile", "", "手机")
                str += genInput("mail", "", "邮箱", "mail")
                str += `<div class="layui-input-block" style="margin-top: 50px">
                        <button type="button" class="layui-btn" onclick="createUser()">保 存</button>
                    </div>`
                str += "</form>"
                layui.layer.open({
                    type: 1,
                    content: str,
                    title: "添加用户",
                    area: ['360px', '290px']
                })
            })

            table.on('radio(list-filter)', function (obj) {

                console.log(obj.checked); //当前是否选中状态
                console.log(obj.data); //选中行的相关数据
                var user = obj.data
                currentUser = user.RealName
                console.log(encodeURI(JSON.stringify(obj.data)));
                //parent.openmodal('编辑用户', '@(Url.Action("userset", "user"))?data=' + encodeURI(JSON.stringify(obj.data)), '400px', '570px')
                var str = `<form class="edit-modal" style='margin: 20px'>
                       <div > 综合权限：` + genGroup("禁用", "cn=leave", user.Status == 2) + genGroup("待审核", "cn=underReview", user.Status == 3)
                    + genGroup("加入时间受限用户", "cn=limitedJoinTime,ou=invest", user.limitedJoinTime)
                    + genGroup("医疗组内可见受限", "cn=limitedMedical,ou=invest", user.limitedMedical)
                    + genInput("mobile", user.Telephone, "手机")
                    + genInput("mail", user.Mail, "邮箱", "mail")
                    + genInput("description", user.Description, "描述")
                    + `<HR> IMS系统：<select class="query" name="ims-user">
                            <option value="待审核" ${user.Status == 3 && "selected"}>待审核（请先勾除待审核）</option>
                            <option value="cn=limited,ou=invest" ${user.Status != 3 && user.Levels == 3 && "selected"}>受限用户</option>
                            <option value="normal" ${user.Status != 3 && user.Status != 4 && user.Levels == 0 && "selected"}>普通用户（加入分组才可登陆）</option>
                            <option value="cn=investPartner,ou=invest" ${user.Status != 3 && user.Levels == 5 && "selected"}>投资合伙人</option>
                                  <option value="cn=onlyrmb,ou=invest" ${user.Status != 3 && user.Levels == 22 && "selected"}>仅可查看人民币项目</option>
                               <option value="cn=onlyusd,ou=invest" ${user.Status != 3 && user.Levels == 21 && "selected"}>仅可查看美元项目</option>
                              <option value="cn=dealrmb,ou=invest" ${user.Status != 3 && user.Levels == 10 && "selected"}>Deal人民币</option>
                               <option value="cn=dealusd,ou=invest" ${user.Status != 3 && user.Levels == 9 && "selected"}>Deal美元</option>
                              <option value="cn=dealall,ou=invest" ${user.Status != 3 && user.Levels == 11 && "selected"}>Deal所有</option>
                            <option value="cn=super,ou=invest" ${user.Status != 3 && user.Levels == 2 && "selected"}>超级用户</option>
                            <option value="cn=admin,ou=invest" ${user.Status != 3 && user.Levels == 1 && "selected"}>管理员</option>
                            <option value="cn=reviewDeny,ou=invest" ${user.Status == 4 && "selected"}>IMS审核不通过</option>
                       </select><div class="roles" style="margin-top: 10px">`



               @foreach (var role in roleList) {
                    <text> str += genGroup('@(role.RoleName)', "cn=" +'@(role.RoleLdap)' +",ou=invest",user.RoleName.indexOf('@(role.RoleName)') != -1 ) </text>
                }
                //str += genGroup('onlyusd', "cn=onlyusd" + ",ou=invest", user.RoleName.indexOf("onlyusd") != -1)
                //str += genGroup('onlyrmb', "cn=onlyrmb" + ",ou=invest", user.RoleName.indexOf("onlyrmb") != -1)
                str += `</div> <HR>
                        <div style="margin-top: 20px"> Research系统：
                          <select  class="query" name="research-user">
                            <option value="待审核" ${user.Status == 3 && "selected"}>待审核(请先勾除待审核)</option>
                            <option value="cn=normal,ou=research" ${user.Status != 3 && user.StatusResearch != 14 && user.LevelResearch == 10 && "selected"}>普通用户</option>
                            <option value="cn=editor,ou=research" ${user.Status != 3 && user.StatusResearch != 14 && user.LevelResearch == 11 && "selected"}>普通编辑</option>
                            <option value="cn=editorAdmin,ou=research" ${user.Status != 3 && user.StatusResearch != 14 && user.LevelResearch == 12 && "selected"}>编辑管理员</option>
                            <option value="cn=investment,ou=research" ${user.Status != 3 && user.StatusResearch != 14 && user.LevelResearch == 13 && "selected"}>投资团队成员</option>
                            <option value="cn=partner,ou=research" ${user.Status != 3 && user.StatusResearch != 14 && user.LevelResearch == 14 && "selected"}>合伙人</option>
                            <option value="cn=superAdmin,ou=research" ${user.Status != 3 && user.StatusResearch != 14 && user.LevelResearch == 15 && "selected"}>超级管理员</option>
                            <option value="cn=limitedIntern,ou=research" ${user.Status != 3 && user.StatusResearch != 14 && user.LevelResearch == 16 && "selected"}>受限实习生</option>
                            <option value="cn=reviewDenied,ou=research" ${user.Status != 3 && user.StatusResearch == 14 && "selected"}>Research审核不通过</option>
                       </select></div>`

                str += `<div class="layui-input-block" style="margin-top: 100px">
                        <button type="button" class="layui-btn" onclick="saveUser()">保 存</button>
                        <button type="button" class="layui-btn" onclick="saveUser(true)">模拟IMS权限</button>
                    </div>`
                str += "</form>"
               layeridx =  layui.layer.open({
                    type: 1,
                    content: str,
                    title: `编辑用户权限：${obj.data.RealName}`,
                    area: ['400px', '690px']
                })
            });
            function genGroup(name,value, checked) {
                return `<div><input type="checkbox" class="query" name=${value} id=${name} ${checked && "checked"}></input>
                          <label for=${name} style="margin-right: 10px">${name}</label></div>`
            }


            table.on('tool(list-filter)', function (obj) {
                var data = obj.data
                    , layEvent = obj.event;

                if (layEvent === 'enable') {
                    fieldset(data.Id, 'enable', data.Status);
                } else if (layEvent === 'modify') {
                    parent.openmodal('编辑用户', '@(Url.Action("userset", "user"))?id=' + data.Id, '400px', '570px')
                }
                return;
            });

            laypage.render({
                elem: 'pageBar'
                , count: 100
                , skin: '#208aee'
                , jump: function (obj, first) {
                    if (!first) {
                        layer.msg('第' + obj.curr + '页');
                    }
                }
            });

            $('#dosearch').on('click', function () {
                queryParams = {
                    role: $('#role').val(),
                    title: $('#uname').val(),
                    all: $('#all-users').prop('checked') ? "on" : "",
                    filterleave: $('#filter-leave').prop('checked') ? "on" : "",
                }
                table.reload('table-list', {
                    where: queryParams,
                });
            });
            
            // 处理显示兴趣标签的复选框
            $('#show-interest-tags').on('change', function() {
                var showTags = $(this).prop('checked');
                
                // 获取当前表格数据
                var currentData = table.cache['table-list'] || [];
                var userIds = [];
                
                // 如果选中了显示标签，则获取所有用户ID
                if (showTags) {
                    for (var key in currentData) {
                        if (currentData.hasOwnProperty(key) && currentData[key].LAY_TABLE_INDEX !== undefined && currentData[key].Id) {
                            userIds.push(currentData[key].Id);
                        }
                    }
                    
                    if (userIds.length > 0) {
                        // 显示加载提示
                        var loadIndex = layer.load(1, {
                            shade: [0.1, '#fff']
                        });
                        
                        // 调用API获取所有用户的兴趣标签
                        $.ajax({
                            type: 'POST',
                            url: '@(Url.Action("GetMultipleUserInterestTags", "UserProfile"))',
                            data: { 
                                userIds: JSON.stringify(userIds)
                            },
                            success: function(response) {
                                layer.close(loadIndex);

                                console.log('Interest tags response:', response); // 添加调试日志

                                if (response.code == 0 && response.data) {
                                    console.log('Processing interest tags data...', response.data);

                                    // 更新全局变量
                                    userInterestTags = response.data;
                                    showInterestTags = true;

                                    console.log('Updated global userInterestTags:', userInterestTags);

                                    // 重新渲染表格以触发模板函数
                                    tableIns.reload();

                                    layer.msg('兴趣标签加载成功', {icon: 1});
                                } else {
                                    layer.msg('获取兴趣标签失败：' + (response.msg || '未知错误'), {icon: 2});
                                }
                            },
                            error: function(xhr, status, error) {
                                layer.close(loadIndex);
                                layer.msg('请求失败：' + error, {icon: 2});
                            }
                        });
                    }
                } else {
                    // 如果取消选中，则清空标签数据
                    userInterestTags = {};
                    showInterestTags = false;

                    // 重新加载表格，使用原始的服务器数据源
                    tableIns.reload();
                }
            });

            // 批量更新用户画像
            $('#batch-update-profile').on('click', function () {
                // 获取当前页面所有用户数据
                var currentData = table.cache['table-list'] || [];
                var allUsers = [];
                
                // 从缓存中提取用户数据
                for (var key in currentData) {
                    if (currentData.hasOwnProperty(key) && currentData[key].LAY_TABLE_INDEX !== undefined) {
                        allUsers.push(currentData[key]);
                    }
                }
                
                if (allUsers.length === 0) {
                    layer.msg('当前页面没有用户数据！', {icon: 2});
                    return;
                }
                
                // 过滤掉没有ID的用户
                var validUsers = allUsers.filter(function(user) {
                    return user.Id && user.Id > 0;
                });
                
                if (validUsers.length === 0) {
                    layer.msg('当前页面没有有效的用户数据！', {icon: 2});
                    return;
                }
                
                var userIds = validUsers.map(function(user) {
                    return user.Id;
                });
                
                var userNames = validUsers.map(function(user) {
                    return user.RealName || '未知用户';
                });
                
                var confirmMsg = validUsers.length === 1 
                    ? `确定要更新用户"${userNames[0]}"的画像吗？`
                    : `确定要批量更新当前页面的${validUsers.length}个用户的画像吗？`;
                
                layer.confirm(confirmMsg, {
                    btn: ['确定', '取消']
                }, function () {
                    // 显示加载提示
                    var loadIndex = layer.load(1, {
                        shade: [0.1, '#fff']
                    });
                    
                    $.ajax({
                        type: 'POST',
                        url: '@(Url.Action("BatchUpdateProfiles", "UserProfile"))',
                        data: { 
                            userIds: JSON.stringify(userIds),
                            userNames: JSON.stringify(userNames)
                        },
                        success: function (data) {
                            layer.close(loadIndex);
                            if (data.code == 0) {
                                layer.msg('批量更新用户画像成功！', {icon: 1});
                            } else {
                                layer.msg('批量更新失败：' + (data.msg || '未知错误'), {icon: 2});
                            }
                        },
                        error: function (xhr, status, error) {
                            layer.close(loadIndex);
                            layer.msg('请求失败：' + error, {icon: 2});
                        }
                    });
                });
            });

            function fieldset(id, field, state) {
            $.ajax({
                    type: 'POST',
                    url: '@(Url.Action("memberset", "adminapi"))',
                    data: {id: id, field: field, state: state },
                    success: function (data) {
                        if (data.code == 0) {
                            layer.msg('操作成功！');
                            $('#dosearch').click();
                        } else {
                            layer.msg(data.msg);
                        }
                    },
                    error: function () {
                        layui.layer.msg("很抱歉，请求异常！");
                    }
                });
            }
        });
    </script>
}
