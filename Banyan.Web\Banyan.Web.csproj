﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{06C08EAD-C0C4-44E7-952E-BF0CBB5546C7}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Banyan.Web</RootNamespace>
    <AssemblyName>Banyan.Web</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>
    </IISExpressSSLPort>
    <IISExpressAnonymousAuthentication>
    </IISExpressAnonymousAuthentication>
    <IISExpressWindowsAuthentication>
    </IISExpressWindowsAuthentication>
    <IISExpressUseClassicPipelineMode>
    </IISExpressUseClassicPipelineMode>
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <ApplicationInsightsResourceId>/subscriptions/04808348-8a69-4cfa-bb5d-e212fb95064d/resourcegroups/banyanVM/providers/microsoft.insights/components/banyanvc</ApplicationInsightsResourceId>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <IncludeSetAclProviderOnDestination>False</IncludeSetAclProviderOnDestination>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <IncludeSetAclProviderOnDestination>False</IncludeSetAclProviderOnDestination>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DAL.Base">
      <HintPath>..\lib\DAL.Base.dll</HintPath>
    </Reference>
    <Reference Include="Gelf4Net.Core, Version=1.0.0.17, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Gelf4Net.Core.1.0.0.17\lib\net451\Gelf4Net.Core.dll</HintPath>
    </Reference>
    <Reference Include="Gelf4Net.UdpAppender, Version=1.0.0.17, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Gelf4Net.UdpAppender.1.0.0.17\lib\net451\Gelf4Net.UdpAppender.dll</HintPath>
    </Reference>
    <Reference Include="LitJSON, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Content\js\plugins\kindeditor\asp.net\bin\LitJSON.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.8.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.8\lib\net45-full\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.Agent.Intercept, Version=2.4.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Agent.Intercept.2.4.0\lib\net45\Microsoft.AI.Agent.Intercept.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.DependencyCollector, Version=2.12.0.21496, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.12.0\lib\net45\Microsoft.AI.DependencyCollector.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.PerfCounterCollector, Version=2.12.0.21496, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.12.0\lib\net45\Microsoft.AI.PerfCounterCollector.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.ServerTelemetryChannel, Version=2.12.0.21496, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.12.0\lib\net45\Microsoft.AI.ServerTelemetryChannel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.Web, Version=2.12.0.21496, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Web.2.12.0\lib\net45\Microsoft.AI.Web.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.WindowsServer, Version=2.12.0.21496, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.2.12.0\lib\net45\Microsoft.AI.WindowsServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights, Version=2.13.1.12554, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.2.13.1\lib\net46\Microsoft.ApplicationInsights.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights.Log4NetAppender, Version=2.13.1.12554, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Log4NetAppender.2.13.1\lib\net45\Microsoft.ApplicationInsights.Log4NetAppender.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.TelemetryCorrelation, Version=1.0.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.TelemetryCorrelation.1.0.7\lib\net45\Microsoft.AspNet.TelemetryCorrelation.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Newtonsoft.Json.11.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.4.0\lib\netstandard2.0\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.4.6.0\lib\net46\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.3\lib\netstandard2.0\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.4.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.5.3\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.Web.Razor">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.3\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Webpages">
      <HintPath>..\packages\Microsoft.AspNet.Webpages.3.2.3\lib\net45\System.Web.Webpages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Webpages.Deployment">
      <HintPath>..\packages\Microsoft.AspNet.Webpages.3.2.3\lib\net45\System.Web.Webpages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Webpages.Razor">
      <HintPath>..\packages\Microsoft.AspNet.Webpages.3.2.3\lib\net45\System.Web.Webpages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Helpers">
      <HintPath>..\packages\Microsoft.AspNet.Webpages.3.2.3\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.3\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.3\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.3\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\css\bootstrap.min.css" />
    <Content Include="Content\css\common.css" />
    <Content Include="Content\css\interest-tags.css" />
    <Content Include="Content\css\login2.css" />
    <Content Include="Content\css\oneui.min.css" />
    <Content Include="Content\css\preview.css" />
    <Content Include="Content\css\recommendation.css" />
    <Content Include="Content\css\themes\amethyst.min.css" />
    <Content Include="Content\css\themes\city.min.css" />
    <Content Include="Content\css\themes\flat.min.css" />
    <Content Include="Content\css\themes\modern.min.css" />
    <Content Include="Content\css\themes\smooth.min.css" />
    <Content Include="Content\css\views\meet\meet-edit.css" />
    <Content Include="Content\fonts\fontawesome-webfont.svg" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Content\fonts\Simple-Line-Icons.svg" />
    <Content Include="Content\img\favicons\favicon.ico" />
    <Content Include="Content\img\logo.jpg" />
    <Content Include="Content\interest-tags.css" />
    <Content Include="Content\js\app.js" />
    <Content Include="Content\js\commonhp2.js" />
    <Content Include="Content\js\component\fileuploadbtn.js" />
    <Content Include="Content\js\core\ajaxfileupload.js" />
    <Content Include="Content\js\core\jquery.min.js" />
    <Content Include="Content\js\core\js.cookie.min.js" />
    <Content Include="Content\js\engagement-tracker.js" />
    <Content Include="Content\js\marked.js" />
    <Content Include="Content\js\oneui.min.js" />
    <Content Include="Content\js\plugins\bootstrap-notify\bootstrap-notify.js" />
    <Content Include="Content\js\plugins\bootstrap-notify\bootstrap-notify.min.js" />
    <Content Include="Content\js\plugins\card\jquery.card.js" />
    <Content Include="Content\js\plugins\card\jquery.card.min.js" />
    <Content Include="Content\js\plugins\city-select\css\cityStyle.css" />
    <Content Include="Content\js\plugins\city-select\js\cityScript.js" />
    <Content Include="Content\js\plugins\city-select\js\lazyload-min.js" />
    <Content Include="Content\js\plugins\ckeditor.js" />
    <Content Include="Content\js\plugins\datatables\jquery.dataTables.css" />
    <Content Include="Content\js\plugins\datatables\jquery.dataTables.js" />
    <Content Include="Content\js\plugins\datatables\jquery.dataTables.min.css" />
    <Content Include="Content\js\plugins\datatables\jquery.dataTables.min.js" />
    <Content Include="Content\js\plugins\echarts.min.js" />
    <Content Include="Content\js\plugins\jquery-ui\jquery-ui.js" />
    <Content Include="Content\js\plugins\jquery-ui\jquery-ui.min.js" />
    <Content Include="Content\js\plugins\jquery-validation\additional-methods.js" />
    <Content Include="Content\js\plugins\jquery-validation\additional-methods.min.js" />
    <Content Include="Content\js\plugins\jquery-validation\jquery.validate.js" />
    <Content Include="Content\js\plugins\jquery-validation\jquery.validate.min.js" />
    <Content Include="Content\js\plugins\jquery-vide\jquery.vide.js" />
    <Content Include="Content\js\plugins\jquery-vide\jquery.vide.min.js" />
    <Content Include="Content\js\plugins\jquery.fileupload.js" />
    <Content Include="Content\js\plugins\jquery.iframe-transport.js" />
    <Content Include="Content\js\plugins\jquery.ui.widget.js" />
    <Content Include="Content\js\plugins\kindeditor\asp.net\bin\LitJSON.dll" />
    <Content Include="Content\js\plugins\kindeditor\asp.net\demo.aspx" />
    <Content Include="Content\js\plugins\kindeditor\asp.net\README.txt" />
    <Content Include="Content\js\plugins\kindeditor\kindeditor-all-min.js" />
    <Content Include="Content\js\plugins\kindeditor\lang\ar.js" />
    <Content Include="Content\js\plugins\kindeditor\lang\en.js" />
    <Content Include="Content\js\plugins\kindeditor\lang\ko.js" />
    <Content Include="Content\js\plugins\kindeditor\lang\ru.js" />
    <Content Include="Content\js\plugins\kindeditor\lang\zh-CN.js" />
    <Content Include="Content\js\plugins\kindeditor\lang\zh-TW.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\anchor\anchor.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\autoheight\autoheight.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\baidumap\baidumap.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\baidumap\index.html" />
    <Content Include="Content\js\plugins\kindeditor\plugins\baidumap\map.html" />
    <Content Include="Content\js\plugins\kindeditor\plugins\clearhtml\clearhtml.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\code\code.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\code\prettify.css" />
    <Content Include="Content\js\plugins\kindeditor\plugins\code\prettify.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\emoticons.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\0.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\1.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\10.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\100.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\101.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\102.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\103.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\104.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\105.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\106.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\107.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\108.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\109.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\11.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\110.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\111.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\112.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\113.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\114.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\115.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\116.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\117.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\118.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\119.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\12.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\120.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\121.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\122.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\123.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\124.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\125.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\126.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\127.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\128.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\129.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\13.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\130.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\131.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\132.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\133.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\134.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\14.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\15.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\16.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\17.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\18.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\19.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\2.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\20.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\21.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\22.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\23.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\24.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\25.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\26.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\27.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\28.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\29.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\3.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\30.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\31.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\32.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\33.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\34.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\35.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\36.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\37.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\38.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\39.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\4.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\40.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\41.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\42.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\43.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\44.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\45.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\46.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\47.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\48.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\49.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\5.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\50.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\51.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\52.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\53.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\54.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\55.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\56.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\57.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\58.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\59.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\6.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\60.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\61.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\62.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\63.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\64.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\65.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\66.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\67.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\68.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\69.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\7.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\70.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\71.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\72.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\73.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\74.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\75.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\76.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\77.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\78.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\79.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\8.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\80.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\81.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\82.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\83.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\84.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\85.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\86.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\87.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\88.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\89.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\9.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\90.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\91.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\92.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\93.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\94.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\95.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\96.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\97.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\98.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\99.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\emoticons\images\static.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\filemanager\filemanager.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\filemanager\images\file-16.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\filemanager\images\file-64.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\filemanager\images\folder-16.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\filemanager\images\folder-64.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\filemanager\images\go-up.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\fixtoolbar\fixtoolbar.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\flash\flash.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\image\image.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\image\images\align_left.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\image\images\align_right.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\image\images\align_top.gif" />
    <Content Include="Content\js\plugins\kindeditor\plugins\image\images\refresh.png" />
    <Content Include="Content\js\plugins\kindeditor\plugins\insertfile\insertfile.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\lineheight\lineheight.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\link\link.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\map\map.html" />
    <Content Include="Content\js\plugins\kindeditor\plugins\map\map.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\media\media.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\multiimage\images\image.png" />
    <Content Include="Content\js\plugins\kindeditor\plugins\multiimage\images\select-files-en.png" />
    <Content Include="Content\js\plugins\kindeditor\plugins\multiimage\images\select-files-zh-CN.png" />
    <Content Include="Content\js\plugins\kindeditor\plugins\multiimage\images\swfupload.swf" />
    <Content Include="Content\js\plugins\kindeditor\plugins\multiimage\multiimage.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\pagebreak\pagebreak.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\plainpaste\plainpaste.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\preview\preview.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\quickformat\quickformat.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\table\table.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\template\html\1.html" />
    <Content Include="Content\js\plugins\kindeditor\plugins\template\html\2.html" />
    <Content Include="Content\js\plugins\kindeditor\plugins\template\html\3.html" />
    <Content Include="Content\js\plugins\kindeditor\plugins\template\template.js" />
    <Content Include="Content\js\plugins\kindeditor\plugins\wordpaste\wordpaste.js" />
    <Content Include="Content\js\plugins\kindeditor\themes\common\anchor.gif" />
    <Content Include="Content\js\plugins\kindeditor\themes\common\blank.gif" />
    <Content Include="Content\js\plugins\kindeditor\themes\common\flash.gif" />
    <Content Include="Content\js\plugins\kindeditor\themes\common\loading.gif" />
    <Content Include="Content\js\plugins\kindeditor\themes\common\media.gif" />
    <Content Include="Content\js\plugins\kindeditor\themes\common\rm.gif" />
    <Content Include="Content\js\plugins\kindeditor\themes\default\background.png" />
    <Content Include="Content\js\plugins\kindeditor\themes\default\default.css" />
    <Content Include="Content\js\plugins\kindeditor\themes\default\default.png" />
    <Content Include="Content\js\plugins\kindeditor\themes\qq\editor.gif" />
    <Content Include="Content\js\plugins\kindeditor\themes\qq\qq.css" />
    <Content Include="Content\js\plugins\kindeditor\themes\simple\simple.css" />
    <Content Include="Content\js\plugins\layui\css\layui.css" />
    <Content Include="Content\js\plugins\layui\css\layui.mobile.css" />
    <Content Include="Content\js\plugins\layui\css\modules\code.css" />
    <Content Include="Content\js\plugins\layui\css\modules\laydate\default\laydate.css" />
    <Content Include="Content\js\plugins\layui\css\modules\layer\default\icon-ext.png" />
    <Content Include="Content\js\plugins\layui\css\modules\layer\default\icon.png" />
    <Content Include="Content\js\plugins\layui\css\modules\layer\default\layer.css" />
    <Content Include="Content\js\plugins\layui\css\modules\layer\default\loading-0.gif" />
    <Content Include="Content\js\plugins\layui\css\modules\layer\default\loading-1.gif" />
    <Content Include="Content\js\plugins\layui\css\modules\layer\default\loading-2.gif" />
    <Content Include="Content\js\plugins\layui\font\iconfont.svg" />
    <Content Include="Content\js\plugins\layui\images\face\0.gif" />
    <Content Include="Content\js\plugins\layui\images\face\1.gif" />
    <Content Include="Content\js\plugins\layui\images\face\10.gif" />
    <Content Include="Content\js\plugins\layui\images\face\11.gif" />
    <Content Include="Content\js\plugins\layui\images\face\12.gif" />
    <Content Include="Content\js\plugins\layui\images\face\13.gif" />
    <Content Include="Content\js\plugins\layui\images\face\14.gif" />
    <Content Include="Content\js\plugins\layui\images\face\15.gif" />
    <Content Include="Content\js\plugins\layui\images\face\16.gif" />
    <Content Include="Content\js\plugins\layui\images\face\17.gif" />
    <Content Include="Content\js\plugins\layui\images\face\18.gif" />
    <Content Include="Content\js\plugins\layui\images\face\19.gif" />
    <Content Include="Content\js\plugins\layui\images\face\2.gif" />
    <Content Include="Content\js\plugins\layui\images\face\20.gif" />
    <Content Include="Content\js\plugins\layui\images\face\21.gif" />
    <Content Include="Content\js\plugins\layui\images\face\22.gif" />
    <Content Include="Content\js\plugins\layui\images\face\23.gif" />
    <Content Include="Content\js\plugins\layui\images\face\24.gif" />
    <Content Include="Content\js\plugins\layui\images\face\25.gif" />
    <Content Include="Content\js\plugins\layui\images\face\26.gif" />
    <Content Include="Content\js\plugins\layui\images\face\27.gif" />
    <Content Include="Content\js\plugins\layui\images\face\28.gif" />
    <Content Include="Content\js\plugins\layui\images\face\29.gif" />
    <Content Include="Content\js\plugins\layui\images\face\3.gif" />
    <Content Include="Content\js\plugins\layui\images\face\30.gif" />
    <Content Include="Content\js\plugins\layui\images\face\31.gif" />
    <Content Include="Content\js\plugins\layui\images\face\32.gif" />
    <Content Include="Content\js\plugins\layui\images\face\33.gif" />
    <Content Include="Content\js\plugins\layui\images\face\34.gif" />
    <Content Include="Content\js\plugins\layui\images\face\35.gif" />
    <Content Include="Content\js\plugins\layui\images\face\36.gif" />
    <Content Include="Content\js\plugins\layui\images\face\37.gif" />
    <Content Include="Content\js\plugins\layui\images\face\38.gif" />
    <Content Include="Content\js\plugins\layui\images\face\39.gif" />
    <Content Include="Content\js\plugins\layui\images\face\4.gif" />
    <Content Include="Content\js\plugins\layui\images\face\40.gif" />
    <Content Include="Content\js\plugins\layui\images\face\41.gif" />
    <Content Include="Content\js\plugins\layui\images\face\42.gif" />
    <Content Include="Content\js\plugins\layui\images\face\43.gif" />
    <Content Include="Content\js\plugins\layui\images\face\44.gif" />
    <Content Include="Content\js\plugins\layui\images\face\45.gif" />
    <Content Include="Content\js\plugins\layui\images\face\46.gif" />
    <Content Include="Content\js\plugins\layui\images\face\47.gif" />
    <Content Include="Content\js\plugins\layui\images\face\48.gif" />
    <Content Include="Content\js\plugins\layui\images\face\49.gif" />
    <Content Include="Content\js\plugins\layui\images\face\5.gif" />
    <Content Include="Content\js\plugins\layui\images\face\50.gif" />
    <Content Include="Content\js\plugins\layui\images\face\51.gif" />
    <Content Include="Content\js\plugins\layui\images\face\52.gif" />
    <Content Include="Content\js\plugins\layui\images\face\53.gif" />
    <Content Include="Content\js\plugins\layui\images\face\54.gif" />
    <Content Include="Content\js\plugins\layui\images\face\55.gif" />
    <Content Include="Content\js\plugins\layui\images\face\56.gif" />
    <Content Include="Content\js\plugins\layui\images\face\57.gif" />
    <Content Include="Content\js\plugins\layui\images\face\58.gif" />
    <Content Include="Content\js\plugins\layui\images\face\59.gif" />
    <Content Include="Content\js\plugins\layui\images\face\6.gif" />
    <Content Include="Content\js\plugins\layui\images\face\60.gif" />
    <Content Include="Content\js\plugins\layui\images\face\61.gif" />
    <Content Include="Content\js\plugins\layui\images\face\62.gif" />
    <Content Include="Content\js\plugins\layui\images\face\63.gif" />
    <Content Include="Content\js\plugins\layui\images\face\64.gif" />
    <Content Include="Content\js\plugins\layui\images\face\65.gif" />
    <Content Include="Content\js\plugins\layui\images\face\66.gif" />
    <Content Include="Content\js\plugins\layui\images\face\67.gif" />
    <Content Include="Content\js\plugins\layui\images\face\68.gif" />
    <Content Include="Content\js\plugins\layui\images\face\69.gif" />
    <Content Include="Content\js\plugins\layui\images\face\7.gif" />
    <Content Include="Content\js\plugins\layui\images\face\70.gif" />
    <Content Include="Content\js\plugins\layui\images\face\71.gif" />
    <Content Include="Content\js\plugins\layui\images\face\8.gif" />
    <Content Include="Content\js\plugins\layui\images\face\9.gif" />
    <Content Include="Content\js\plugins\layui\layui.all.js" />
    <Content Include="Content\js\plugins\layui\layui.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\carousel.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\code.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\element.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\flow.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\form.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\jquery.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\laydate.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\layedit.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\layer.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\laypage.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\laytpl.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\mobile.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\slider.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\table.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\tree.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\upload.js" />
    <Content Include="Content\js\plugins\layui\lay\modules\util.js" />
    <Content Include="Content\js\plugins\lightgallery\css\default.css" />
    <Content Include="Content\js\plugins\lightgallery\css\lightgallery.min.css" />
    <Content Include="Content\js\plugins\lightgallery\css\normalize.css" />
    <Content Include="Content\js\plugins\lightgallery\fonts\lg.svg" />
    <Content Include="Content\js\plugins\lightgallery\img\loading.gif" />
    <Content Include="Content\js\plugins\lightgallery\img\video-play.png" />
    <Content Include="Content\js\plugins\lightgallery\img\vimeo-play.png" />
    <Content Include="Content\js\plugins\lightgallery\img\youtube-play.png" />
    <Content Include="Content\js\plugins\lightgallery\img\zoom-black.png" />
    <Content Include="Content\js\plugins\lightgallery\img\zoom-black.svg" />
    <Content Include="Content\js\plugins\lightgallery\img\zoom.png" />
    <Content Include="Content\js\plugins\lightgallery\js\jquery.mousewheel.min.js" />
    <Content Include="Content\js\plugins\lightgallery\js\lg-autoplay.min.js" />
    <Content Include="Content\js\plugins\lightgallery\js\lg-fullscreen.min.js" />
    <Content Include="Content\js\plugins\lightgallery\js\lg-hash.min.js" />
    <Content Include="Content\js\plugins\lightgallery\js\lg-pager.min.js" />
    <Content Include="Content\js\plugins\lightgallery\js\lg-thumbnail.min.js" />
    <Content Include="Content\js\plugins\lightgallery\js\lg-video.min.js" />
    <Content Include="Content\js\plugins\lightgallery\js\lg-zoom.min.js" />
    <Content Include="Content\js\plugins\lightgallery\js\lightgallery.min.js" />
    <Content Include="Content\js\plugins\lightgallery\js\picturefill.min.js" />
    <Content Include="Content\js\plugins\nice-validator\images\loading.gif" />
    <Content Include="Content\js\plugins\nice-validator\images\validator_default.png" />
    <Content Include="Content\js\plugins\nice-validator\images\validator_simple.png" />
    <Content Include="Content\js\plugins\nice-validator\jquery.validator.css" />
    <Content Include="Content\js\plugins\nice-validator\jquery.validator.min.js" />
    <Content Include="Content\js\plugins\nice-validator\local\en.js" />
    <Content Include="Content\js\plugins\nice-validator\local\ja.js" />
    <Content Include="Content\js\plugins\nice-validator\local\zh-CN.js" />
    <Content Include="Content\js\plugins\nice-validator\local\zh-TW.js" />
    <Content Include="Content\js\plugins\select2\i18n\ar.js" />
    <Content Include="Content\js\plugins\select2\i18n\az.js" />
    <Content Include="Content\js\plugins\select2\i18n\bg.js" />
    <Content Include="Content\js\plugins\select2\i18n\ca.js" />
    <Content Include="Content\js\plugins\select2\i18n\cs.js" />
    <Content Include="Content\js\plugins\select2\i18n\da.js" />
    <Content Include="Content\js\plugins\select2\i18n\de.js" />
    <Content Include="Content\js\plugins\select2\i18n\en.js" />
    <Content Include="Content\js\plugins\select2\i18n\es.js" />
    <Content Include="Content\js\plugins\select2\i18n\et.js" />
    <Content Include="Content\js\plugins\select2\i18n\eu.js" />
    <Content Include="Content\js\plugins\select2\i18n\fa.js" />
    <Content Include="Content\js\plugins\select2\i18n\fi.js" />
    <Content Include="Content\js\plugins\select2\i18n\fr.js" />
    <Content Include="Content\js\plugins\select2\i18n\gl.js" />
    <Content Include="Content\js\plugins\select2\i18n\he.js" />
    <Content Include="Content\js\plugins\select2\i18n\hi.js" />
    <Content Include="Content\js\plugins\select2\i18n\hr.js" />
    <Content Include="Content\js\plugins\select2\i18n\hu.js" />
    <Content Include="Content\js\plugins\select2\i18n\id.js" />
    <Content Include="Content\js\plugins\select2\i18n\is.js" />
    <Content Include="Content\js\plugins\select2\i18n\it.js" />
    <Content Include="Content\js\plugins\select2\i18n\ja.js" />
    <Content Include="Content\js\plugins\select2\i18n\ko.js" />
    <Content Include="Content\js\plugins\select2\i18n\lt.js" />
    <Content Include="Content\js\plugins\select2\i18n\lv.js" />
    <Content Include="Content\js\plugins\select2\i18n\mk.js" />
    <Content Include="Content\js\plugins\select2\i18n\ms.js" />
    <Content Include="Content\js\plugins\select2\i18n\nb.js" />
    <Content Include="Content\js\plugins\select2\i18n\nl.js" />
    <Content Include="Content\js\plugins\select2\i18n\pl.js" />
    <Content Include="Content\js\plugins\select2\i18n\pt-BR.js" />
    <Content Include="Content\js\plugins\select2\i18n\pt.js" />
    <Content Include="Content\js\plugins\select2\i18n\ro.js" />
    <Content Include="Content\js\plugins\select2\i18n\ru.js" />
    <Content Include="Content\js\plugins\select2\i18n\sk.js" />
    <Content Include="Content\js\plugins\select2\i18n\sr-Cyrl.js" />
    <Content Include="Content\js\plugins\select2\i18n\sr.js" />
    <Content Include="Content\js\plugins\select2\i18n\sv.js" />
    <Content Include="Content\js\plugins\select2\i18n\th.js" />
    <Content Include="Content\js\plugins\select2\i18n\tr.js" />
    <Content Include="Content\js\plugins\select2\i18n\uk.js" />
    <Content Include="Content\js\plugins\select2\i18n\vi.js" />
    <Content Include="Content\js\plugins\select2\i18n\zh-CN.js" />
    <Content Include="Content\js\plugins\select2\i18n\zh-TW.js" />
    <Content Include="Content\js\plugins\select2\select2-bootstrap.css" />
    <Content Include="Content\js\plugins\select2\select2-bootstrap.min.css" />
    <Content Include="Content\js\plugins\select2\select2.css" />
    <Content Include="Content\js\plugins\select2\select2.full.min.js" />
    <Content Include="Content\js\plugins\select2\select2.min.css" />
    <Content Include="Content\js\plugins\select2\select2.min.js" />
    <Content Include="Content\js\plugins\slick\ajax-loader.gif" />
    <Content Include="Content\js\plugins\slick\fonts\slick.svg" />
    <Content Include="Content\js\plugins\slick\slick-theme.css" />
    <Content Include="Content\js\plugins\slick\slick-theme.min.css" />
    <Content Include="Content\js\plugins\slick\slick.css" />
    <Content Include="Content\js\plugins\slick\slick.js" />
    <Content Include="Content\js\plugins\slick\slick.min.css" />
    <Content Include="Content\js\plugins\slick\slick.min.js" />
    <Content Include="Content\js\plugins\sparkline\jquery.sparkline.js" />
    <Content Include="Content\js\plugins\sparkline\jquery.sparkline.min.js" />
    <Content Include="Content\js\form-component.js" />
    <Content Include="Content\js\recommendation-widget-loader.js" />
    <Content Include="Content\js\recommendation.js" />
    <Content Include="Content\js\textfit.js" />
    <Content Include="Content\js\utils.js" />
    <Content Include="Content\js\views\meet\edit-common-v2.js" />
    <Content Include="Content\js\vue\vue.min.js" />
    <Content Include="Content\recommendation.css" />
    <Content Include="Global.asax" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\Routes\CustomRouteUrl.cs" />
    <Compile Include="App_Start\Routes\CustomRouteUrlMapHelper.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="Controllers\AdminApiController.cs" />
    <Compile Include="Controllers\ApiExtController.cs" />
    <Compile Include="Controllers\ApiController.cs" />
    <Compile Include="Controllers\ArticleController.cs" />
    <Compile Include="Controllers\BaseController.cs" />
    <Compile Include="Controllers\EmailDigestController.cs" />
    <Compile Include="Controllers\EngagementAnalyticsController.cs" />
    <Compile Include="Controllers\FBaseController.cs" />
    <Compile Include="Controllers\LLMController.cs" />
    <Compile Include="Controllers\LoginController.cs" />
    <Compile Include="Controllers\MeetController.cs" />
    <Compile Include="Controllers\NewsVectorSearchController.cs" />
    <Compile Include="Controllers\ProjectController.cs" />
    <Compile Include="Controllers\RecommendationController.cs" />
    <Compile Include="Controllers\StaffKPIController.cs" />
    <Compile Include="Controllers\SummaryController.cs" />
    <Compile Include="Controllers\IndexController.cs" />
    <Compile Include="Controllers\UserController.cs" />
    <Compile Include="Controllers\UserProfileController.cs" />
    <Compile Include="Controllers\NewsVectorizationController.cs" />
    <Compile Include="ErrorHandler\AiHandleErrorAttribute.cs" />
    <Compile Include="FilterConfig.cs" />
    <Compile Include="Filters\ExternalAuthFilters.cs" />
    <Compile Include="Filters\WechatAuthFilters.cs" />
    <Compile Include="Filters\AuthFilters.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\web.config" />
    <Content Include="Content\fonts\fontawesome-webfont.eot" />
    <Content Include="Content\fonts\fontawesome-webfont.ttf" />
    <Content Include="Content\fonts\fontawesome-webfont.woff" />
    <Content Include="Content\fonts\fontawesome-webfont.woff2" />
    <Content Include="Content\fonts\FontAwesome.otf" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="Content\fonts\Simple-Line-Icons.eot" />
    <Content Include="Content\fonts\Simple-Line-Icons.ttf" />
    <Content Include="Content\fonts\Simple-Line-Icons.woff" />
    <Content Include="Content\js\plugins\kindeditor\asp.net\file_manager_json.ashx" />
    <Content Include="Content\js\plugins\kindeditor\asp.net\upload_json.ashx" />
    <Content Include="Content\js\plugins\layui\font\iconfont.eot" />
    <Content Include="Content\js\plugins\layui\font\iconfont.ttf" />
    <Content Include="Content\js\plugins\layui\font\iconfont.woff" />
    <Content Include="Content\js\plugins\layui\font\iconfont.woff2" />
    <Content Include="Content\js\plugins\slick\fonts\slick.eot" />
    <Content Include="Content\js\plugins\slick\fonts\slick.ttf" />
    <Content Include="Content\js\plugins\slick\fonts\slick.woff" />
    <Content Include="Configs\log4net.config" />
    <Content Include="Content\js\plugins\kindeditor\asp.net\upload.ashx" />
    <Content Include="Content\js\plugins\lightgallery\fonts\lg.eot" />
    <Content Include="Content\js\plugins\lightgallery\fonts\lg.ttf" />
    <Content Include="Content\js\plugins\lightgallery\fonts\lg.woff" />
    <Content Include="ApplicationInsights.config" />
    <Content Include="Connected Services\Application Insights\ConnectedService.json" />
    <Content Include="Content\project_template.docx" />
    <None Include="packages.config" />
    <None Include="Properties\PublishProfiles\banyanproject - Web Deploy %282%29.pubxml" />
    <None Include="Properties\PublishProfiles\banyanproject - Web Deploy.pubxml" />
    <Content Include="Views\Article\Articles.cshtml" />
    <Content Include="Views\Article\ArticleSet.cshtml" />
    <Content Include="Views\Article\Comments.cshtml" />
    <Content Include="Views\Article\Preview.cshtml" />
    <Content Include="Views\Article\RoleSet.cshtml" />
    <Content Include="Views\Classify\Role.cshtml" />
    <Content Include="Views\Classify\RoleSet.cshtml" />
    <Content Include="Views\Index\Preview.cshtml" />
    <Content Include="Views\Index\Index.cshtml" />
    <Content Include="Views\Index\ProjectSet.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\User\Users.cshtml" />
    <Content Include="Views\User\UserSet.cshtml" />
    <Content Include="Views\Login\Index.cshtml" />
    <None Include="Properties\PublishProfiles\banyanvc - Web Deploy.pubxml" />
    <Content Include="Views\Index\MyProjects.cshtml" />
    <Content Include="Views\Article\MyArticles.cshtml" />
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <None Include="Properties\PublishProfiles\gaorong - Web Deploy.pubxml" />
    <Content Include="Views\Index\Score.cshtml" />
    <Content Include="Views\Index\ScoreSet.cshtml" />
    <Content Include="Views\Summary\Summary.cshtml" />
    <Content Include="Views\StaffKPI\StaffKPI.cshtml" />
    <Content Include="Views\Index\PicPreview.cshtml" />
    <Content Include="Views\Shared\_LayoutAdmin.cshtml" />
    <Content Include="Views\Shared\_LayoutSuper.cshtml" />
    <Content Include="Views\Meet\Meets.cshtml" />
    <Content Include="Views\Meet\MeetSet.cshtml" />
    <Content Include="Views\Meet\ResearchPreview.cshtml" />
    <Content Include="Views\Meet\Detail.cshtml" />
    <Content Include="Views\Meet\Drafts.cshtml" />
    <Content Include="Views\Meet\ReadOnly.cshtml" />
    <Content Include="Views\Shared\_LayoutAdminWithResearch.cshtml" />
    <Content Include="Views\User\UsersResearch.cshtml" />
    <Content Include="Views\Summary\Charts.cshtml" />
    <Content Include="Views\Index\Attachments.cshtml" />
    <Content Include="Views\Login\Wechat.cshtml" />
    <Content Include="Views\Summary\PerformanceReport.cshtml" />
    <Content Include="Views\Index\ProjectContribution.cshtml" />
    <Content Include="Views\Index\ExplainVectorSearch.cshtml" />
    <Content Include="Views\User\AnnualreportSetInvest.cshtml" />
    <Content Include="Views\User\AnnualReport.cshtml" />
    <Content Include="Views\User\AnnualReportPreview.cshtml" />
    <Content Include="Views\Login\News.cshtml" />
    <Content Include="Views\Index\PortfolioExit.cshtml" />
    <Content Include="Views\Index\PortfolioExitSet.cshtml" />
    <Content Include="Views\Index\ExitScore.cshtml" />
    <Content Include="Views\Index\ExitScoreSet.cshtml" />
    <Content Include="Views\Index\Chat.cshtml" />
    <Content Include="Views\Shared\_LayoutClean.cshtml" />
    <Content Include="Views\Index\NewsVector.cshtml" />
    <Content Include="Views\Index\NewsVectorDetails.cshtml" />
    <Content Include="Views\NewsVectorSearch\Index.cshtml" />
    <Content Include="Views\NewsVectorSearch\Recommendations.cshtml" />
    <Content Include="Views\NewsVectorSearch\SimilarNews.cshtml" />
    <Content Include="Views\NewsVectorSearch\History.cshtml" />
    <Content Include="Views\EngagementAnalytics\Index.cshtml" />
    <Content Include="Views\EngagementAnalytics\Records.cshtml" />
    <Content Include="Views\EngagementAnalytics\UserDetails.cshtml" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Content\attached\" />
    <Folder Include="Content\img\photos\" />
    <Folder Include="Views\Project\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Banyan.Apps\Banyan.Apps.csproj">
      <Project>{8a3c6556-408c-4e9f-93f1-6fcc666f79b8}</Project>
      <Name>Banyan.Apps</Name>
    </ProjectReference>
    <ProjectReference Include="..\Banyan.Code\Banyan.Code.csproj">
      <Project>{a2469fa5-ee0a-40df-ba22-2ed091b6e251}</Project>
      <Name>Banyan.Code</Name>
    </ProjectReference>
    <ProjectReference Include="..\Banyan.Domain\Banyan.Domain.csproj">
      <Project>{fa32fef4-2ab2-4031-b78e-1c9ebd219784}</Project>
      <Name>Banyan.Domain</Name>
    </ProjectReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup>
    <AutoGenerateBindingRedirects>false</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>59348</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:59348/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.12.0\build\Microsoft.ApplicationInsights.DependencyCollector.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.12.0\build\Microsoft.ApplicationInsights.DependencyCollector.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.12.0\build\Microsoft.ApplicationInsights.PerfCounterCollector.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.12.0\build\Microsoft.ApplicationInsights.PerfCounterCollector.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.12.0\build\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.12.0\build\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.ApplicationInsights.WindowsServer.2.12.0\build\Microsoft.ApplicationInsights.WindowsServer.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.ApplicationInsights.WindowsServer.2.12.0\build\Microsoft.ApplicationInsights.WindowsServer.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.ApplicationInsights.Web.2.12.0\build\Microsoft.ApplicationInsights.Web.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.ApplicationInsights.Web.2.12.0\build\Microsoft.ApplicationInsights.Web.targets'))" />
  </Target>
  <Import Project="..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.12.0\build\Microsoft.ApplicationInsights.DependencyCollector.targets" Condition="Exists('..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.12.0\build\Microsoft.ApplicationInsights.DependencyCollector.targets')" />
  <Import Project="..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.12.0\build\Microsoft.ApplicationInsights.PerfCounterCollector.targets" Condition="Exists('..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.12.0\build\Microsoft.ApplicationInsights.PerfCounterCollector.targets')" />
  <Import Project="..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.12.0\build\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.targets" Condition="Exists('..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.12.0\build\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.targets')" />
  <Import Project="..\packages\Microsoft.ApplicationInsights.WindowsServer.2.12.0\build\Microsoft.ApplicationInsights.WindowsServer.targets" Condition="Exists('..\packages\Microsoft.ApplicationInsights.WindowsServer.2.12.0\build\Microsoft.ApplicationInsights.WindowsServer.targets')" />
  <Import Project="..\packages\Microsoft.ApplicationInsights.Web.2.12.0\build\Microsoft.ApplicationInsights.Web.targets" Condition="Exists('..\packages\Microsoft.ApplicationInsights.Web.2.12.0\build\Microsoft.ApplicationInsights.Web.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>