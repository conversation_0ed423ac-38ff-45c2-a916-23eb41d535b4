# 联网搜索和MCP同时开启时无响应问题诊断

## 问题描述
当同时开启联网搜索和MCP智能工具时，系统可能出现无响应的情况。

## 已实施的修复

### 1. 添加超时保护机制
- 在`askStreamPost`方法中添加了30秒超时保护
- 在`autoWebSearch`方法中添加了25秒超时保护
- 确保即使某个环节卡住，也不会无限等待

### 2. 增强错误处理和日志
- 添加了详细的控制台日志输出
- 改进了错误状态显示
- 提供了更好的用户反馈

## 诊断步骤

### 第一步：检查浏览器控制台
1. 打开浏览器开发者工具（F12）
2. 切换到"控制台"选项卡
3. 发送一条同时开启联网和MCP的消息
4. 观察控制台输出的日志信息

**期望看到的日志序列：**
```
开始调用后端搜索API...
后端搜索API响应状态: 200
后端搜索API响应结果: {code: 0, data: [...]}
联网搜索完成，结果数量: X
用户消息内容已更新，联网搜索内容长度: XXX
等待联网搜索完成...
联网搜索已完成
最终发送给AI的Prompt: [包含搜索结果的完整内容]
```

### 第二步：识别卡住的环节

#### 如果卡在联网搜索阶段
- 看到"开始调用后端搜索API..."但没有后续日志
- **可能原因：** 后端WebSearch接口响应慢或卡死
- **解决方案：** 检查后端服务器负载，重启后端服务

#### 如果卡在等待联网搜索完成
- 看到搜索完成日志但没有"联网搜索已完成"
- **可能原因：** performWebSearchAndUpdate方法中的异步更新有问题
- **解决方案：** 使用修复后的版本会自动超时并继续

#### 如果卡在MCP处理阶段
- 看到"最终发送给AI的Prompt"但AI没有响应
- **可能原因：** 后端MCP处理逻辑有性能问题
- **解决方案：** 检查项目数据库查询性能，优化MCP搜索逻辑

### 第三步：验证修复效果

**测试场景1：正常情况**
1. 开启联网搜索和MCP
2. 输入："分析一下最近的AI投资项目趋势"
3. 应该看到搜索状态 → 搜索结果 → MCP工具状态 → AI回答

**测试场景2：超时情况**
1. 开启联网搜索和MCP
2. 输入复杂查询
3. 如果搜索超时，应该看到超时提示并继续AI对话

## 临时解决方案

如果问题依然存在，可以采用以下临时方案：

### 方案1：分步操作
1. 先单独使用联网搜索，等待搜索完成
2. 然后关闭联网搜索，开启MCP，继续对话

### 方案2：降低搜索量
1. 将联网搜索结果数量调低（如3-5条）
2. 减少搜索内容的复杂度

### 方案3：使用极速模型
1. 选择"qwen3-30b-a3b (极速)"模型
2. 该模型处理大量内容时响应更快

## 性能优化建议

### 后端优化
1. **WebSearch接口优化**
   - 添加缓存机制
   - 并行处理多个搜索引擎
   - 优化内容抓取超时时间

2. **MCP处理优化**
   - 优化项目搜索SQL查询
   - 添加索引提升查询速度
   - 限制单次返回的项目数量

### 前端优化
1. **用户体验改进**
   - 显示更详细的进度信息
   - 提供取消操作的选项
   - 预估处理时间并告知用户

## 监控和日志

### 关键日志监控点
1. 联网搜索API响应时间
2. MCP工具调用耗时
3. 总体对话响应时间
4. 错误率和超时率

### 性能指标
- 联网搜索平均耗时：< 10秒
- MCP工具处理平均耗时：< 5秒
- 总体响应时间：< 30秒

## 联系支持

如果问题依然无法解决，请提供：
1. 浏览器控制台的完整日志
2. 具体的输入内容和期望结果
3. 问题发生的时间和频率
4. 网络环境和浏览器版本信息 