﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using Banyan.Code;
using Banyan.Domain;
using Newtonsoft.Json;
using Banyan.Apps.Configs;

namespace Banyan.Apps
{
    #region 数据模型

    /// <summary>
    /// 向量化结果
    /// </summary>
    public class VectorizationResult
    {
        private readonly ICache _cache = CacheFactory.Cache();
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 总处理数量
        /// </summary>
        public int TotalProcessed { get; set; }

        /// <summary>
        /// 成功数量
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败数量
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// 错误信息列表
        /// </summary>
        public List<string> ErrorMessages { get; set; }

        /// <summary>
        /// 处理耗时
        /// </summary>
        public TimeSpan Duration => EndTime - StartTime;
    

    #endregion

    #region 辅助方法

        /// <summary>
        /// 从标题创建简单的分析结果（当新闻内容为空时使用）
        /// </summary>
        /// <param name="title">新闻标题</param>
        /// <returns>标签分析结果</returns>
        private NewsTagAnalysis CreateSimpleAnalysisFromTitle(string title)
        {
            try
            {
                if (string.IsNullOrEmpty(title))
                {
                    return null;
                }

                Logger.Info($"从标题创建简单分析结果: {title}");

                var analysis = new NewsTagAnalysis
                {
                    MainTags = new List<NewsTag>(),
                    SecondaryTags = new List<NewsTag>(),
                    SemanticKeywords = new List<NewsTag>()
                };

                // 简单分词，提取关键词
                var words = title.Split(new[] { ' ', ',', '.', '，', '。', '、', '：', ':', ';', '；', '!', '！', '?', '？', '(', ')', '（', '）', '[', ']', '【', '】' },
                    StringSplitOptions.RemoveEmptyEntries);

                // 过滤掉停用词和短词
                var filteredWords = words.Where(w => w.Length > 1 && !IsStopWord(w)).ToList();

                // 添加主要标签（取前2个词）
                for (int i = 0; i < Math.Min(2, filteredWords.Count); i++)
                {
                    analysis.MainTags.Add(new NewsTag
                    {
                        Name = filteredWords[i],
                        Weight = 0.8,
                        Category = "其他"
                    });
                }

                // 添加次要标签（取后2个词，如果有的话）
                for (int i = 2; i < Math.Min(4, filteredWords.Count); i++)
                {
                    analysis.SecondaryTags.Add(new NewsTag
                    {
                        Name = filteredWords[i],
                        Weight = 0.5,
                        Category = "其他"
                    });
                }

                // 添加语义关键词（取整个标题）
                analysis.SemanticKeywords.Add(new NewsTag
                {
                    Name = title.Length > 20 ? title.Substring(0, 20) : title,
                    Weight = 0.3,
                    Category = "其他"
                });

                return analysis;
            }
            catch (Exception ex)
            {
                Logger.Error($"从标题创建简单分析结果失败: {title}", ex);
                return null;
            }
        }

        /// <summary>
        /// 判断是否为停用词
        /// </summary>
        /// <param name="word">单词</param>
        /// <returns>是否为停用词</returns>
        private bool IsStopWord(string word)
        {
            // 简单的停用词列表，实际应用中可以使用更完整的停用词表
            var stopWords = new[] { "的", "了", "和", "与", "或", "是", "在", "有", "为", "以", "及", "等", "对", "the", "a", "an", "and", "or", "is", "in", "to", "for", "with", "by", "on", "at" };
            return stopWords.Contains(word.ToLower());
        }

    

        /// <summary>
        /// 提取文本中的关键词
        /// </summary>
        /// <param name="text">文本</param>
        /// <returns>关键词列表</returns>
        private List<string> ExtractKeywords(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return new List<string>();
            }

            // 简单分词
            var words = text.Split(new[] { ' ', ',', '.', '，', '。', '、', '：', ':', ';', '；', '!', '！', '?', '？', '(', ')', '（', '）', '[', ']', '【', '】', '\r', '\n', '\t' },
                StringSplitOptions.RemoveEmptyEntries);

            // 过滤停用词和短词
            var filteredWords = words.Where(w => w.Length > 1 && !IsStopWord(w)).ToList();

            // 统计词频
            var wordFrequency = new Dictionary<string, int>();
            foreach (var word in filteredWords)
            {
                if (wordFrequency.ContainsKey(word))
                {
                    wordFrequency[word]++;
                }
                else
                {
                    wordFrequency[word] = 1;
                }
            }

            // 按词频排序
            return wordFrequency.OrderByDescending(kv => kv.Value).Select(kv => kv.Key).ToList();
        }

        /// <summary>
        /// 根据关键词和新闻分类确定标签分类
        /// </summary>
        /// <param name="keyword">关键词</param>
        /// <param name="newsClassify">新闻分类</param>
        /// <returns>标签分类</returns>
        private string DetermineCategory(string keyword, string newsClassify)
        {
            // 技术相关词汇
            var techWords = new[] { "技术", "科技", "AI", "人工智能", "算法", "区块链", "云计算", "大数据", "物联网", "5G", "互联网" };

            // 行业相关词汇
            var industryWords = new[] { "行业", "产业", "市场", "企业", "公司", "集团", "制造", "服务", "零售", "医疗", "教育", "金融" };

            // 投资相关词汇
            var investmentWords = new[] { "投资", "融资", "股权", "股票", "基金", "风投", "PE", "VC", "IPO", "并购", "回报", "收益" };

            // 政策相关词汇
            var policyWords = new[] { "政策", "法规", "监管", "规定", "措施", "文件", "通知", "条例", "法律", "合规" };

            // 判断关键词属于哪个分类
            if (techWords.Any(w => keyword.Contains(w) || w.Contains(keyword)))
            {
                return "技术";
            }
            else if (industryWords.Any(w => keyword.Contains(w) || w.Contains(keyword)))
            {
                return "行业";
            }
            else if (investmentWords.Any(w => keyword.Contains(w) || w.Contains(keyword)))
            {
                return "投资";
            }
            else if (policyWords.Any(w => keyword.Contains(w) || w.Contains(keyword)))
            {
                return "政策";
            }

            // 根据新闻分类判断
            if (!string.IsNullOrEmpty(newsClassify))
            {
                if (newsClassify.Contains("技术") || newsClassify.Contains("科技"))
                {
                    return "技术";
                }
                else if (newsClassify.Contains("行业") || newsClassify.Contains("产业"))
                {
                    return "行业";
                }
                else if (newsClassify.Contains("投资") || newsClassify.Contains("融资"))
                {
                    return "投资";
                }
                else if (newsClassify.Contains("政策") || newsClassify.Contains("法规"))
                {
                    return "政策";
                }
            }

            return "其他";
        }

        /// <summary>
        /// 验证标签分析结果
        /// </summary>
        /// <param name="analysis">标签分析结果</param>
        /// <returns>是否有效</returns>
        private bool ValidateTagAnalysis(NewsTagAnalysis analysis)
        {
            if (analysis == null)
            {
                return false;
            }

            // 检查主要标签
            if (analysis.MainTags == null || analysis.MainTags.Count == 0)
            {
                return false;
            }

            // 检查次要标签
            if (analysis.SecondaryTags == null)
            {
                return false;
            }

            // 检查语义关键词
            if (analysis.SemanticKeywords == null)
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 修复标签分析结果
        /// </summary>
        /// <param name="analysis">标签分析结果</param>
        /// <returns>修复后的标签分析结果</returns>
        private NewsTagAnalysis FixTagAnalysis(NewsTagAnalysis analysis)
        {
            if (analysis == null)
            {
                return new NewsTagAnalysis
                {
                    MainTags = new List<NewsTag>(),
                    SecondaryTags = new List<NewsTag>(),
                    SemanticKeywords = new List<NewsTag>()
                };
            }

            // 修复主要标签
            if (analysis.MainTags == null)
            {
                analysis.MainTags = new List<NewsTag>();
            }

            // 确保至少有一个主要标签
            if (analysis.MainTags.Count == 0 && analysis.SecondaryTags != null && analysis.SecondaryTags.Count > 0)
            {
                // 将最高权重的次要标签提升为主要标签
                var topTag = analysis.SecondaryTags.OrderByDescending(t => t.Weight).First();
                analysis.MainTags.Add(new NewsTag
                {
                    Name = topTag.Name,
                    Weight = Math.Max(0.7, topTag.Weight), // 确保权重至少为0.7
                    Category = topTag.Category
                });

                // 从次要标签中移除
                analysis.SecondaryTags.Remove(topTag);
            }

            // 修复次要标签
            if (analysis.SecondaryTags == null)
            {
                analysis.SecondaryTags = new List<NewsTag>();
            }

            // 修复语义关键词
            if (analysis.SemanticKeywords == null)
            {
                analysis.SemanticKeywords = new List<NewsTag>();
            }

            // 确保权重在合理范围内
            foreach (var tag in analysis.MainTags)
            {
                tag.Weight = Math.Max(0.7, Math.Min(1.0, tag.Weight)); // 0.7-1.0
                if (string.IsNullOrEmpty(tag.Category))
                {
                    tag.Category = "其他";
                }
            }

            foreach (var tag in analysis.SecondaryTags)
            {
                tag.Weight = Math.Max(0.3, Math.Min(0.6, tag.Weight)); // 0.3-0.6
                if (string.IsNullOrEmpty(tag.Category))
                {
                    tag.Category = "其他";
                }
            }

            foreach (var tag in analysis.SemanticKeywords)
            {
                tag.Weight = Math.Max(0.1, Math.Min(0.3, tag.Weight)); // 0.1-0.3
                if (string.IsNullOrEmpty(tag.Category))
                {
                    tag.Category = "其他";
                }
            }

            return analysis;
        }

        /// <summary>
        /// 增强的解析AI响应方法
        /// </summary>
        /// <param name="response">AI响应</param>
        /// <returns>标签分析结果</returns>
        private NewsTagAnalysis ParseAIResponse(string response)
        {
            if (string.IsNullOrEmpty(response))
            {
                Logger.Error("AI响应为空");
                return null;
            }

            try
            {
                Logger.Info("开始解析AI响应");

                // 清理响应内容，移除可能的markdown格式
                var cleanResponse = response.Trim();
                if (cleanResponse.StartsWith("```json"))
                {
                    cleanResponse = cleanResponse.Substring(7);
                }
                if (cleanResponse.EndsWith("```"))
                {
                    cleanResponse = cleanResponse.Substring(0, cleanResponse.Length - 3);
                }
                cleanResponse = cleanResponse.Trim();

                // 尝试直接反序列化
                var analysis = JsonConvert.DeserializeObject<NewsTagAnalysis>(cleanResponse);

                if (analysis == null)
                {
                    Logger.Error("反序列化结果为空");
                    return null;
                }

                // 初始化集合（如果为null）
                if (analysis.MainTags == null)
                {
                    analysis.MainTags = new List<NewsTag>();
                }
                if (analysis.SecondaryTags == null)
                {
                    analysis.SecondaryTags = new List<NewsTag>();
                }
                if (analysis.SemanticKeywords == null)
                {
                    analysis.SemanticKeywords = new List<NewsTag>();
                }

                // 验证和修复标签数据
                ValidateAndFixTags(analysis.MainTags, 0.7, 1.0);
                ValidateAndFixTags(analysis.SecondaryTags, 0.3, 0.6);
                ValidateAndFixTags(analysis.SemanticKeywords, 0.1, 0.3);

                Logger.Info($"AI响应解析成功，主要标签: {analysis.MainTags.Count}，次要标签: {analysis.SecondaryTags.Count}，语义关键词: {analysis.SemanticKeywords.Count}");
                return analysis;
            }
            catch (JsonException jsonEx)
            {
                Logger.Error($"JSON解析失败: {jsonEx.Message}，响应内容: {response}");

                // 尝试使用正则表达式提取标签信息
                return TryParseWithRegex(response);
            }
            catch (Exception ex)
            {
                Logger.Error($"解析AI响应失败: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 验证和修复标签数据
        /// </summary>
        /// <param name="tags">标签列表</param>
        /// <param name="minWeight">最小权重</param>
        /// <param name="maxWeight">最大权重</param>
        private void ValidateAndFixTags(List<NewsTag> tags, double minWeight, double maxWeight)
        {
            if (tags == null)
            {
                return;
            }

            for (int i = tags.Count - 1; i >= 0; i--)
            {
                var tag = tags[i];

                // 移除无效标签
                if (tag == null || string.IsNullOrEmpty(tag.Name))
                {
                    tags.RemoveAt(i);
                    continue;
                }

                // 修复权重
                if (tag.Weight < minWeight || tag.Weight > maxWeight)
                {
                    tag.Weight = Math.Max(minWeight, Math.Min(maxWeight, tag.Weight));
                }

                // 修复分类
                if (string.IsNullOrEmpty(tag.Category))
                {
                    tag.Category = "其他";
                }
                else
                {
                    // 确保分类在允许的范围内
                    var validCategories = new[] { "技术", "行业", "投资", "政策", "其他" };
                    if (!validCategories.Contains(tag.Category))
                    {
                        tag.Category = "其他";
                    }
                }

                // 清理标签名称
                tag.Name = tag.Name.Trim();
                if (tag.Name.Length > 50) // 限制标签长度
                {
                    tag.Name = tag.Name.Substring(0, 50);
                }
            }
        }

        /// <summary>
        /// 使用正则表达式尝试解析响应（备用方法）
        /// </summary>
        /// <param name="response">AI响应</param>
        /// <returns>标签分析结果</returns>
        private NewsTagAnalysis TryParseWithRegex(string response)
        {
            try
            {
                Logger.Info("尝试使用正则表达式解析AI响应");

                var analysis = new NewsTagAnalysis
                {
                    MainTags = new List<NewsTag>(),
                    SecondaryTags = new List<NewsTag>(),
                    SemanticKeywords = new List<NewsTag>()
                };

                // 简单的正则表达式匹配标签
                var tagPattern = @"""name"":\s*""([^""]+)""\s*,\s*""weight"":\s*([0-9.]+)\s*,\s*""category"":\s*""([^""]+)""";
                var matches = Regex.Matches(response, tagPattern);

                foreach (Match match in matches)
                {
                    if (match.Groups.Count >= 4)
                    {
                        var name = match.Groups[1].Value;
                        var weightStr = match.Groups[2].Value;
                        var category = match.Groups[3].Value;

                        if (double.TryParse(weightStr, out double weight))
                        {
                            var tag = new NewsTag
                            {
                                Name = name,
                                Weight = weight,
                                Category = category
                            };

                            // 根据权重分配到不同类别
                            if (weight >= 0.7)
                            {
                                analysis.MainTags.Add(tag);
                            }
                            else if (weight >= 0.3)
                            {
                                analysis.SecondaryTags.Add(tag);
                            }
                            else
                            {
                                analysis.SemanticKeywords.Add(tag);
                            }
                        }
                    }
                }

                if (analysis.MainTags.Count > 0 || analysis.SecondaryTags.Count > 0 || analysis.SemanticKeywords.Count > 0)
                {
                    Logger.Info($"正则表达式解析成功，主要标签: {analysis.MainTags.Count}，次要标签: {analysis.SecondaryTags.Count}，语义关键词: {analysis.SemanticKeywords.Count}");
                    return analysis;
                }

                return null;
            }
            catch (Exception ex)
            {
                Logger.Error("正则表达式解析失败", ex);
                return null;
            }
        }

        /// <summary>
        /// 创建备用分析结果
        /// </summary>
        /// <param name="news">新闻对象</param>
        /// <returns>标签分析结果</returns>
        private NewsTagAnalysis CreateFallbackAnalysis(News news)
        {
            try
            {
                Logger.Info($"创建备用分析结果，新闻ID: {news.Id}");

                // 使用标题创建简单分析
                var analysis = CreateSimpleAnalysisFromTitle(news.Title);

                // 如果有内容，尝试提取更多信息
                if (!string.IsNullOrEmpty(news.Content))
                {
                    var contentWords = news.Content.Split(new[] { ' ', ',', '.', '，', '。', '、', '：', ':', ';', '；' },
                        StringSplitOptions.RemoveEmptyEntries)
                        .Where(w => w.Length > 1 && !IsStopWord(w))
                        .Take(5)
                        .ToList();

                    foreach (var word in contentWords)
                    {
                        if (analysis.SecondaryTags.Count < 3)
                        {
                            analysis.SecondaryTags.Add(new NewsTag
                            {
                                Name = word,
                                Weight = 0.4,
                                Category = "其他"
                            });
                        }
                    }
                }

                return analysis;
            }
            catch (Exception ex)
            {
                Logger.Error($"创建备用分析结果失败，新闻ID: {news.Id}", ex);
                return CreateSimpleAnalysisFromTitle(news.Title);
            }
        }


        /// <summary>
        /// 清理AI响应内容
        /// </summary>
        /// <param name="response">原始响应</param>
        /// <returns>清理后的响应</returns>
        private string CleanAIResponse(string response)
        {
            if (string.IsNullOrEmpty(response))
            {
                return response;
            }

            // 移除markdown代码块标记
            response = response.Replace("```json", "").Replace("```", "");

            // 移除可能的前后空白
            response = response.Trim();

            // 查找JSON开始和结束位置
            var startIndex = response.IndexOf('{');
            var endIndex = response.LastIndexOf('}');

            if (startIndex >= 0 && endIndex > startIndex)
            {
                response = response.Substring(startIndex, endIndex - startIndex + 1);
            }

            return response;
        }

        /// <summary>
        /// 简化版AI响应解析
        /// </summary>
        /// <param name="response">AI响应</param>
        /// <returns>标签分析结果</returns>
        private NewsTagAnalysis ParseAIResponseSimple(string response)
        {
            Logger.Info("使用简化版AI响应解析");

            var analysis = new NewsTagAnalysis
            {
                MainTags = new List<NewsTag>(),
                SecondaryTags = new List<NewsTag>(),
                SemanticKeywords = new List<NewsTag>()
            };

            // 使用正则表达式提取标签信息
            var tagPattern = @"""name"":\s*""([^""]+)"",\s*""weight"":\s*([0-9.]+),\s*""category"":\s*""([^""]+)""";
            var matches = Regex.Matches(response, tagPattern);

            foreach (Match match in matches)
            {
                if (match.Groups.Count >= 4)
                {
                    var name = match.Groups[1].Value;
                    var weight = double.Parse(match.Groups[2].Value);
                    var category = match.Groups[3].Value;

                    var tag = new NewsTag
                    {
                        Name = name,
                        Weight = weight,
                        Category = category
                    };

                    // 根据权重分配到不同类别
                    if (weight >= 0.7)
                    {
                        analysis.MainTags.Add(tag);
                    }
                    else if (weight >= 0.4)
                    {
                        analysis.SecondaryTags.Add(tag);
                    }
                    else
                    {
                        analysis.SemanticKeywords.Add(tag);
                    }
                }
            }

            Logger.Info($"简化解析完成，提取标签总数: {matches.Count}");
            return analysis;
        }
 

        /// <summary>
        /// 修复JSON格式
        /// </summary>
        /// <param name="response">原始响应</param>
        /// <returns>修复后的JSON</returns>
        private string FixJsonFormat(string response)
        {
            try
            {
                // 简单的JSON修复逻辑
                response = response.Trim();

                // 确保以{开始，以}结束
                if (!response.StartsWith("{"))
                {
                    int startIndex = response.IndexOf('{');
                    if (startIndex > 0)
                    {
                        response = response.Substring(startIndex);
                    }
                }

                if (!response.EndsWith("}"))
                {
                    int endIndex = response.LastIndexOf('}');
                    if (endIndex > 0)
                    {
                        response = response.Substring(0, endIndex + 1);
                    }
                }

                // 移除可能的注释
                response = Regex.Replace(response, @"//.*$", "", RegexOptions.Multiline);

                return response;
            }
            catch (Exception ex)
            {
                Logger.Error("修复JSON格式时发生错误", ex);
                return null;
            }
        }

        /// <summary>
        /// 验证和修复标签
        /// </summary>
        /// <param name="analysis">标签分析结果</param>
        /// <returns>修复后的标签分析结果</returns>
        private NewsTagAnalysis ValidateAndFixTags(NewsTagAnalysis analysis)
        {
            if (analysis == null)
            {
                return null;
            }

            // 验证主要标签
            if (analysis.MainTags != null)
            {
                for (int i = analysis.MainTags.Count - 1; i >= 0; i--)
                {
                    var tag = analysis.MainTags[i];
                    if (string.IsNullOrEmpty(tag.Name))
                    {
                        analysis.MainTags.RemoveAt(i);
                        continue;
                    }

                    // 修复权重
                    if (tag.Weight < 0.7 || tag.Weight > 1.0)
                    {
                        tag.Weight = Math.Max(0.7, Math.Min(1.0, tag.Weight));
                    }

                    // 修复分类
                    if (string.IsNullOrEmpty(tag.Category) || !IsValidCategory(tag.Category))
                    {
                        tag.Category = "其他";
                    }

                    // 清理标签名称
                    tag.Name = tag.Name.Trim();
                    if (tag.Name.Length > 50) // 限制标签长度
                    {
                        tag.Name = tag.Name.Substring(0, 50);
                    }
                }
            }

            // 验证次要标签
            if (analysis.SecondaryTags != null)
            {
                for (int i = analysis.SecondaryTags.Count - 1; i >= 0; i--)
                {
                    var tag = analysis.SecondaryTags[i];
                    if (string.IsNullOrEmpty(tag.Name))
                    {
                        analysis.SecondaryTags.RemoveAt(i);
                        continue;
                    }

                    // 修复权重
                    if (tag.Weight < 0.3 || tag.Weight > 0.6)
                    {
                        tag.Weight = Math.Max(0.3, Math.Min(0.6, tag.Weight));
                    }

                    // 修复分类
                    if (string.IsNullOrEmpty(tag.Category) || !IsValidCategory(tag.Category))
                    {
                        tag.Category = "其他";
                    }

                    // 清理标签名称
                    tag.Name = tag.Name.Trim();
                    if (tag.Name.Length > 50)
                    {
                        tag.Name = tag.Name.Substring(0, 50);
                    }
                }
            }

            // 验证语义关键词
            if (analysis.SemanticKeywords != null)
            {
                for (int i = analysis.SemanticKeywords.Count - 1; i >= 0; i--)
                {
                    var keyword = analysis.SemanticKeywords[i];
                    if (string.IsNullOrEmpty(keyword.Name))
                    {
                        analysis.SemanticKeywords.RemoveAt(i);
                        continue;
                    }

                    // 修复权重
                    if (keyword.Weight < 0.1 || keyword.Weight > 0.3)
                    {
                        keyword.Weight = Math.Max(0.1, Math.Min(0.3, keyword.Weight));
                    }

                    // 修复分类
                    if (string.IsNullOrEmpty(keyword.Category) || !IsValidCategory(keyword.Category))
                    {
                        keyword.Category = "其他";
                    }

                    // 清理关键词名称
                    keyword.Name = keyword.Name.Trim();
                    if (keyword.Name.Length > 50)
                    {
                        keyword.Name = keyword.Name.Substring(0, 50);
                    }
                }
            }

            return analysis;
        }

        /// <summary>
        /// 验证分类是否有效
        /// </summary>
        /// <param name="category">分类</param>
        /// <returns>是否有效</returns>
        private bool IsValidCategory(string category)
        {
            var validCategories = new[] { "技术", "行业", "投资", "政策", "其他" };
            return validCategories.Contains(category);
        }


        #region 向量转换和辅助方法

        /// <summary>
        /// 将向量数组转换为字符串
        /// </summary>
        /// <param name="vector">向量数组</param>
        /// <returns>字符串</returns>
        private string VectorToString(double[] vector)
        {
            if (vector == null || vector.Length == 0)
            {
                return null;
            }

            try
            {
                return string.Join(",", vector.Select(v => v.ToString("G17")));
            }
            catch (Exception ex)
            {
                Logger.Error("向量转字符串失败", ex);
                return null;
            }
        }

        /// <summary>
        /// 将字符串转换为向量数组
        /// </summary>
        /// <param name="vectorString">向量字符串</param>
        /// <returns>向量数组</returns>
        private double[] StringToVector(string vectorString)
        {
            if (string.IsNullOrEmpty(vectorString))
            {
                return null;
            }

            try
            {
                var values = vectorString.Split(',');
                if (values.Length != VectorServiceConfig.VECTOR_DIMENSION)
                {
                    Logger.Warn($"向量维度不匹配，期望: {VectorServiceConfig.VECTOR_DIMENSION}，实际: {values.Length}");

                    // 如果维度不匹配，尝试调整
                    var vector = new double[VectorServiceConfig.VECTOR_DIMENSION];

                    // 复制可用的维度
                    int minDimension = Math.Min(values.Length, VectorServiceConfig.VECTOR_DIMENSION);
                    for (int i = 0; i < minDimension; i++)
                    {
                        if (double.TryParse(values[i], out double value))
                        {
                            vector[i] = value;
                        }
                    }

                    return vector;
                }
                else
                {
                    return values.Select(v => double.TryParse(v, out double value) ? value : 0.0).ToArray();
                }
            }
            catch (Exception ex)
            {
                Logger.Error("字符串转向量失败", ex);
                return null;
            }
        }

        /// <summary>
        /// 计算两个向量的余弦相似度
        /// </summary>
        /// <param name="vector1">向量1</param>
        /// <param name="vector2">向量2</param>
        /// <returns>相似度（0-1之间）</returns>
        public double CalculateCosineSimilarity(double[] vector1, double[] vector2)
        {
            if (vector1 == null || vector2 == null || vector1.Length != vector2.Length)
            {
                return 0;
            }

            try
            {
                double dotProduct = 0;
                double magnitude1 = 0;
                double magnitude2 = 0;

                for (int i = 0; i < vector1.Length; i++)
                {
                    dotProduct += vector1[i] * vector2[i];
                    magnitude1 += Math.Pow(vector1[i], 2);
                    magnitude2 += Math.Pow(vector2[i], 2);
                }

                magnitude1 = Math.Sqrt(magnitude1);
                magnitude2 = Math.Sqrt(magnitude2);

                if (magnitude1 == 0 || magnitude2 == 0)
                {
                    return 0;
                }

                double similarity = dotProduct / (magnitude1 * magnitude2);

                // 确保相似度在0-1之间
                return Math.Max(0, Math.Min(1, similarity));
            }
            catch (Exception ex)
            {
                Logger.Error("计算向量相似度失败", ex);
                return 0;
            }
        }

        /// <summary>
        /// 归一化向量
        /// </summary>
        /// <param name="vector">输入向量</param>
        /// <returns>归一化后的向量</returns>
        public double[] NormalizeVector(double[] vector)
        {
            if (vector == null || vector.Length == 0)
            {
                return null;
            }

            try
            {
                // 计算向量的模长
                double magnitude = Math.Sqrt(vector.Sum(v => v * v));

                // 如果模长为0，返回原向量
                if (magnitude == 0)
                {
                    return vector;
                }

                // 归一化
                return vector.Select(v => v / magnitude).ToArray();
            }
            catch (Exception ex)
            {
                Logger.Error("向量归一化失败", ex);
                return vector;
            }
        }

        

       
        #endregion
        #region 向量搜索和相似度计算

        /// <summary>
        /// 查找与指定向量相似的新闻
        /// </summary>
        /// <param name="queryVector">查询向量</param>
        /// <param name="topN">返回结果数量</param>
        /// <param name="similarityThreshold">相似度阈值</param>
        /// <param name="newsBLL">新闻业务逻辑对象</param>
        /// <returns>相似新闻列表</returns>
        //public async Task<List<NewsVectorSimilarity>> FindSimilarNewsAsync(double[] queryVector, int topN = 10, double similarityThreshold = VectorServiceConfig.VECTOR_SIMILARITY_THRESHOLD, NewsBLL newsBLL = null)
        //{
        //    try
        //    {
        //        if (queryVector == null || queryVector.Length != VectorServiceConfig.VECTOR_DIMENSION)
        //        {
        //            Logger.Error("查询向量为空或维度不匹配");
        //            return new List<NewsVectorSimilarity>();
        //        }

        //        Logger.Info($"开始查找相似新闻，topN: {topN}, 相似度阈值: {similarityThreshold}");

        //        // 归一化查询向量
        //        var normalizedQueryVector = NormalizeVector(queryVector);

        //        // 获取所有已向量化的新闻ID
        //        newsBLL = newsBLL ?? new NewsBLL();
        //        var where = $"VectorStatus = {VectorServiceConfig.VECTOR_STATUS_SUCCESS} AND NewsVector IS NOT NULL";
        //        var fields = "Id";
        //        var newsIds = newsBLL.GetList(where, 1000, 1, fields, "Id DESC").Select(n => n.Id).ToList();

        //        if (newsIds.Count() == 0)
        //        {
        //            Logger.Warn("没有找到已向量化的新闻");
        //            return new List<NewsVectorSimilarity>();
        //        }

        //        Logger.Info($"找到 {newsIds.Count()} 篇已向量化的新闻");

        //        // 批量获取新闻向量
        //        var newsVectors = await GetNewsVectorsBatchAsync(newsIds, newsBLL);
        //        if (newsVectors.Count() == 0)
        //        {
        //            Logger.Warn("没有获取到有效的新闻向量");
        //            return new List<NewsVectorSimilarity>();
        //        }

        //        // 计算相似度并排序
        //        var similarities = new List<NewsVectorSimilarity>();
        //        foreach (var entry in newsVectors)
        //        {
        //            var newsId = entry.Key;
        //            var newsVector = entry.Value;

        //            // 计算余弦相似度
        //            var similarity = CalculateCosineSimilarity(normalizedQueryVector, newsVector);

        //            // 如果相似度超过阈值，添加到结果集
        //            if (similarity >= similarityThreshold)
        //            {
        //                similarities.Add(new NewsVectorSimilarity
        //                {
        //                    NewsId = newsId,
        //                    Similarity = similarity
        //                });
        //            }
        //        }

        //        // 按相似度降序排序并取前topN个
        //        var result = similarities
        //            .OrderByDescending(s => s.Similarity)
        //            .Take(topN)
        //            .ToList();

        //        Logger.Info($"找到 {result.Count} 篇相似新闻");
        //        return result;
        //    }
        //    catch (Exception ex)
        //    {
        //        Logger.Error("查找相似新闻失败", ex);
        //        return new List<NewsVectorSimilarity>();
        //    }
        //}

        ///// <summary>
        ///// 查找与指定新闻相似的新闻
        ///// </summary>
        ///// <param name="newsId">新闻ID</param>
        ///// <param name="topN">返回结果数量</param>
        ///// <param name="similarityThreshold">相似度阈值</param>
        ///// <param name="newsBLL">新闻业务逻辑对象</param>
        ///// <returns>相似新闻列表</returns>
        //public async Task<List<NewsVectorSimilarity>> FindSimilarNewsByIdAsync(int newsId, int topN = 10, double similarityThreshold = VectorServiceConfig.VECTOR_SIMILARITY_THRESHOLD, NewsBLL newsBLL = null)
        //{
        //    try
        //    {
        //        Logger.Info($"开始查找与新闻ID {newsId} 相似的新闻");

        //        // 获取源新闻向量
        //        var sourceVector = await GetNewsVectorAsync(newsId, newsBLL);
        //        if (sourceVector == null)
        //        {
        //            Logger.Warn($"未找到新闻ID {newsId} 的向量");
        //            return new List<NewsVectorSimilarity>();
        //        }

        //        // 查找相似新闻
        //        var similarities = await FindSimilarNewsAsync(sourceVector, topN + 1, similarityThreshold, newsBLL);

        //        // 移除源新闻自身（如果存在）
        //        var result = similarities.Where(s => s.NewsId != newsId).Take(topN).ToList();

        //        return result;
        //    }
        //    catch (Exception ex)
        //    {
        //        Logger.Error($"查找与新闻ID {newsId} 相似的新闻失败", ex);
        //        return new List<NewsVectorSimilarity>();
        //    }
        //}

        /// <summary>
        /// Calls the embedding service to generate a vector for the given text
        /// </summary>
        /// <param name="text">Input text</param>
        /// <param name="retryCount">Current retry count</param>
        /// <returns>Vector representation</returns>
        private async Task<double[]> CallEmbeddingServiceAsync(string text, int retryCount = 0)
        {
            try
            {
                if (string.IsNullOrEmpty(text))
                {
                    Logger.Error("Embedding service call failed: Input text is empty");
                    return null;
                }

                Logger.Info($"Calling embedding service, text length: {text.Length}, retry count: {retryCount}");

                // Build cache key
                string cacheKey = $"embedding:{text.GetHashCode()}:{VectorServiceConfig.EMBEDDING_MODEL_NAME}";

                // Try to get from cache
                var cachedVector = _cache.GetCache<double[]>(cacheKey);
                if (cachedVector != null)
                {
                    Logger.Info("Retrieved vector from cache successfully");
                    return cachedVector;
                }

                // Use VectorService to call embedding service
                var vectorService = new VectorService();
                var vector = await vectorService.GetTextEmbeddingAsync(text);

                if (vector == null || vector.Length == 0)
                {
                    throw new Exception("Embedding service returned empty vector");
                }

                // Validate vector dimension
                if (vector.Length != VectorServiceConfig.VECTOR_DIMENSION)
                {
                    throw new Exception($"Vector dimension mismatch, expected: {VectorServiceConfig.VECTOR_DIMENSION}, actual: {vector.Length}");
                }

                // Cache the vector
                _cache.WriteCache(vector, cacheKey, DateTime.Now.AddDays(VectorServiceConfig.EMBEDDING_CACHE_DAYS));
                Logger.Info($"Embedding service call successful, vector dimension: {vector.Length}");

                return vector;
            }
            catch (Exception ex)
            {
                Logger.Error($"Embedding service call failed: {ex.Message}", ex);

                // Implement retry logic
                if (retryCount < VectorServiceConfig.EMBEDDING_MAX_RETRIES)
                {
                    // Calculate backoff time (exponential backoff strategy)
                    int delayMs = (int)Math.Pow(2, retryCount) * 1000;
                    Logger.Info($"Waiting {delayMs}ms before retry, retry count: {retryCount + 1}/{VectorServiceConfig.EMBEDDING_MAX_RETRIES}");

                    // Wait before retrying
                    await Task.Delay(delayMs);
                    return await CallEmbeddingServiceAsync(text, retryCount + 1);
                }

                return null;
            }
        }


        ///// <summary>
        ///// 根据文本查询相似新闻
        ///// </summary>
        ///// <param name="queryText">查询文本</param>
        ///// <param name="topN">返回结果数量</param>
        ///// <param name="similarityThreshold">相似度阈值</param>
        ///// <param name="newsBLL">新闻业务逻辑对象</param>
        ///// <returns>相似新闻列表</returns>
        //public async Task<List<NewsVectorSimilarity>> SearchNewsByTextAsync(string queryText, int topN = 10, double similarityThreshold = VectorServiceConfig.VECTOR_SIMILARITY_THRESHOLD, NewsBLL newsBLL = null)
        //{
        //    try
        //    {
        //        if (string.IsNullOrEmpty(queryText))
        //        {
        //            Logger.Error("查询文本为空");
        //            return new List<NewsVectorSimilarity>();
        //        }

        //        Logger.Info($"开始根据文本查询相似新闻，查询文本: {queryText}");

        //        // 生成查询文本的向量
        //        var queryVector = await CallEmbeddingServiceAsync(queryText);
        //        if (queryVector == null)
        //        {
        //            Logger.Error("生成查询文本向量失败");
        //            return new List<NewsVectorSimilarity>();
        //        }

        //        // 查找相似新闻
        //        return await FindSimilarNewsAsync(queryVector, topN, similarityThreshold, newsBLL);
        //    }
        //    catch (Exception ex)
        //    {
        //        Logger.Error($"根据文本查询相似新闻失败，查询文本: {queryText}", ex);
        //        return new List<NewsVectorSimilarity>();
        //    }
        //}

        #endregion
    }
}
#endregion