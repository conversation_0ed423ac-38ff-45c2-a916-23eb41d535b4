using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Banyan.Code;
using Banyan.Domain;
using DAL.Base;

namespace Banyan.Apps
{
    /// <summary>
    /// 预计算测试服务
    /// 用于测试和验证预计算系统的功能
    /// </summary>
    public class PrecomputeTestService
    {
        private readonly NewsPrecomputeService _precomputeService;
        private readonly NewsRecommendationEngine _recommendationEngine;
        private readonly NewsVectorSearch _newsVectorSearch;
        private readonly MemberBLL _memberBLL;
        
        // 单例实例
        private static readonly Lazy<PrecomputeTestService> _instance = 
            new Lazy<PrecomputeTestService>(() => new PrecomputeTestService());
        
        /// <summary>
        /// 获取PrecomputeTestService的单例实例
        /// </summary>
        public static PrecomputeTestService Instance => _instance.Value;
        
        /// <summary>
        /// 私有构造函数，确保单例模式
        /// </summary>
        private PrecomputeTestService()
        {
            _precomputeService = NewsPrecomputeService.Instance;
            _recommendationEngine = new NewsRecommendationEngine();
            _newsVectorSearch = new NewsVectorSearch();
            _memberBLL = new MemberBLL();
            
            Logger.Info("PrecomputeTestService initialized");
        }
        
        /// <summary>
        /// 运行完整的预计算系统测试
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<PrecomputeTestResult> RunCompleteTestAsync()
        {
            var testResult = new PrecomputeTestResult
            {
                StartTime = DateTime.Now
            };
            
            try
            {
                Logger.Info("开始运行完整的预计算系统测试");
                
                // 1. 测试预计算功能
                testResult.PrecomputeTest = await TestPrecomputeFunctionalityAsync();
                
                // 2. 测试缓存使用
                testResult.CacheUsageTest = await TestCacheUsageAsync();
                
                // 3. 测试推荐引擎集成
                testResult.RecommendationEngineTest = await TestRecommendationEngineIntegrationAsync();
                
                // 4. 测试性能对比
                testResult.PerformanceTest = await TestPerformanceComparisonAsync();
                
                testResult.EndTime = DateTime.Now;
                testResult.Duration = testResult.EndTime - testResult.StartTime;
                testResult.OverallSuccess = DetermineOverallSuccess(testResult);
                
                Logger.Info($"预计算系统测试完成，总耗时: {testResult.Duration.TotalSeconds:F2}秒，结果: {(testResult.OverallSuccess ? "成功" : "失败")}");
                return testResult;
            }
            catch (Exception ex)
            {
                testResult.EndTime = DateTime.Now;
                testResult.Duration = testResult.EndTime - testResult.StartTime;
                testResult.OverallSuccess = false;
                testResult.ErrorMessage = ex.Message;
                
                Logger.Error($"预计算系统测试失败: {ex.Message}", ex);
                return testResult;
            }
        }
        
        /// <summary>
        /// 测试预计算功能
        /// </summary>
        /// <returns>测试结果</returns>
        private async Task<PrecomputeFunctionalityTest> TestPrecomputeFunctionalityAsync()
        {
            var test = new PrecomputeFunctionalityTest
            {
                StartTime = DateTime.Now
            };
            
            try
            {
                Logger.Info("开始测试预计算功能");
                
                // 获取测试用户
                var testUserId = await GetTestUserIdAsync();
                if (testUserId <= 0)
                {
                    test.Success = false;
                    test.ErrorMessage = "无法找到测试用户";
                    return test;
                }
                
                // 清除现有缓存
                _precomputeService.ClearUserPrecomputedRecommendations(testUserId);
                
                // 执行预计算
                var precomputeResult = await _precomputeService.PrecomputeUserRecommendationsAsync(10, 0.4);
                test.PrecomputeExecuted = precomputeResult.SuccessCount > 0;
                
                // 检查缓存是否生成
                var cachedRecommendations = _precomputeService.GetPrecomputedUserRecommendations(testUserId);
                test.CacheGenerated = cachedRecommendations != null && cachedRecommendations.Count > 0;
                test.CachedRecommendationCount = cachedRecommendations?.Count ?? 0;
                
                test.Success = test.PrecomputeExecuted && test.CacheGenerated;
                test.EndTime = DateTime.Now;
                
                Logger.Info($"预计算功能测试完成，成功: {test.Success}，缓存推荐数量: {test.CachedRecommendationCount}");
                return test;
            }
            catch (Exception ex)
            {
                test.Success = false;
                test.ErrorMessage = ex.Message;
                test.EndTime = DateTime.Now;
                Logger.Error($"预计算功能测试失败: {ex.Message}", ex);
                return test;
            }
        }
        
        /// <summary>
        /// 测试缓存使用
        /// </summary>
        /// <returns>测试结果</returns>
        private async Task<CacheUsageTest> TestCacheUsageAsync()
        {
            var test = new CacheUsageTest
            {
                StartTime = DateTime.Now
            };
            
            try
            {
                Logger.Info("开始测试缓存使用");
                
                var testUserId = await GetTestUserIdAsync();
                if (testUserId <= 0)
                {
                    test.Success = false;
                    test.ErrorMessage = "无法找到测试用户";
                    return test;
                }
                
                // 确保有预计算缓存
                await _precomputeService.ForceRefreshUserRecommendationsAsync(testUserId, 10, 0.4);
                
                // 测试NewsRecommendationEngine是否使用预计算缓存
                var startTime = DateTime.Now;
                var recommendations = await _recommendationEngine.GetPersonalizedRecommendationsAsync(testUserId, 10, 0.4);
                var engineLatency = (DateTime.Now - startTime).TotalMilliseconds;
                
                test.RecommendationEngineUsesCache = recommendations != null && recommendations.Count > 0;
                test.RecommendationEngineLatency = engineLatency;
                
                // 测试NewsVectorSearch是否使用预计算缓存
                startTime = DateTime.Now;
                var searchResults = await _newsVectorSearch.GetRecommendedNewsByInterest(testUserId, 10, 0.4);
                var searchLatency = (DateTime.Now - startTime).TotalMilliseconds;
                
                test.NewsVectorSearchUsesCache = searchResults != null && searchResults.Count > 0;
                test.NewsVectorSearchLatency = searchLatency;
                
                test.Success = test.RecommendationEngineUsesCache && test.NewsVectorSearchUsesCache;
                test.EndTime = DateTime.Now;
                
                Logger.Info($"缓存使用测试完成，成功: {test.Success}，引擎延迟: {engineLatency:F2}ms，搜索延迟: {searchLatency:F2}ms");
                return test;
            }
            catch (Exception ex)
            {
                test.Success = false;
                test.ErrorMessage = ex.Message;
                test.EndTime = DateTime.Now;
                Logger.Error($"缓存使用测试失败: {ex.Message}", ex);
                return test;
            }
        }
        
        /// <summary>
        /// 测试推荐引擎集成
        /// </summary>
        /// <returns>测试结果</returns>
        private async Task<RecommendationEngineIntegrationTest> TestRecommendationEngineIntegrationAsync()
        {
            var test = new RecommendationEngineIntegrationTest
            {
                StartTime = DateTime.Now
            };
            
            try
            {
                Logger.Info("开始测试推荐引擎集成");
                
                var testUserId = await GetTestUserIdAsync();
                if (testUserId <= 0)
                {
                    test.Success = false;
                    test.ErrorMessage = "无法找到测试用户";
                    return test;
                }
                
                // 清除缓存，测试无缓存情况
                _precomputeService.ClearUserPrecomputedRecommendations(testUserId);
                
                var startTime = DateTime.Now;
                var recommendationsWithoutCache = await _recommendationEngine.GetPersonalizedRecommendationsAsync(testUserId, 10, 0.4);
                var latencyWithoutCache = (DateTime.Now - startTime).TotalMilliseconds;
                
                // 生成预计算缓存
                await _precomputeService.ForceRefreshUserRecommendationsAsync(testUserId, 10, 0.4);
                
                startTime = DateTime.Now;
                var recommendationsWithCache = await _recommendationEngine.GetPersonalizedRecommendationsAsync(testUserId, 10, 0.4);
                var latencyWithCache = (DateTime.Now - startTime).TotalMilliseconds;
                
                test.WithoutCacheCount = recommendationsWithoutCache?.Count ?? 0;
                test.WithCacheCount = recommendationsWithCache?.Count ?? 0;
                test.LatencyWithoutCache = latencyWithoutCache;
                test.LatencyWithCache = latencyWithCache;
                test.PerformanceImprovement = latencyWithoutCache > 0 ? 
                    ((latencyWithoutCache - latencyWithCache) / latencyWithoutCache) * 100 : 0;
                
                test.Success = test.WithCacheCount > 0 && test.LatencyWithCache < test.LatencyWithoutCache;
                test.EndTime = DateTime.Now;
                
                Logger.Info($"推荐引擎集成测试完成，成功: {test.Success}，性能提升: {test.PerformanceImprovement:F1}%");
                return test;
            }
            catch (Exception ex)
            {
                test.Success = false;
                test.ErrorMessage = ex.Message;
                test.EndTime = DateTime.Now;
                Logger.Error($"推荐引擎集成测试失败: {ex.Message}", ex);
                return test;
            }
        }
        
        /// <summary>
        /// 测试性能对比
        /// </summary>
        /// <returns>测试结果</returns>
        private async Task<PerformanceComparisonTest> TestPerformanceComparisonAsync()
        {
            var test = new PerformanceComparisonTest
            {
                StartTime = DateTime.Now
            };
            
            try
            {
                Logger.Info("开始测试性能对比");
                
                var testUserIds = await GetMultipleTestUserIdsAsync(5);
                if (testUserIds.Count == 0)
                {
                    test.Success = false;
                    test.ErrorMessage = "无法找到测试用户";
                    return test;
                }
                
                // 测试多个用户的性能
                var totalLatencyWithoutCache = 0.0;
                var totalLatencyWithCache = 0.0;
                var successfulTests = 0;
                
                foreach (var userId in testUserIds)
                {
                    try
                    {
                        // 清除缓存
                        _precomputeService.ClearUserPrecomputedRecommendations(userId);
                        
                        // 测试无缓存性能
                        var startTime = DateTime.Now;
                        await _recommendationEngine.GetPersonalizedRecommendationsAsync(userId, 10, 0.4);
                        totalLatencyWithoutCache += (DateTime.Now - startTime).TotalMilliseconds;
                        
                        // 生成缓存
                        await _precomputeService.ForceRefreshUserRecommendationsAsync(userId, 10, 0.4);
                        
                        // 测试有缓存性能
                        startTime = DateTime.Now;
                        await _recommendationEngine.GetPersonalizedRecommendationsAsync(userId, 10, 0.4);
                        totalLatencyWithCache += (DateTime.Now - startTime).TotalMilliseconds;
                        
                        successfulTests++;
                    }
                    catch (Exception ex)
                    {
                        Logger.Warn($"用户 {userId} 性能测试失败: {ex.Message}");
                    }
                }
                
                if (successfulTests > 0)
                {
                    test.AverageLatencyWithoutCache = totalLatencyWithoutCache / successfulTests;
                    test.AverageLatencyWithCache = totalLatencyWithCache / successfulTests;
                    test.AveragePerformanceImprovement = test.AverageLatencyWithoutCache > 0 ? 
                        ((test.AverageLatencyWithoutCache - test.AverageLatencyWithCache) / test.AverageLatencyWithoutCache) * 100 : 0;
                    test.TestedUserCount = successfulTests;
                    test.Success = test.AverageLatencyWithCache < test.AverageLatencyWithoutCache;
                }
                else
                {
                    test.Success = false;
                    test.ErrorMessage = "所有用户测试都失败了";
                }
                
                test.EndTime = DateTime.Now;
                
                Logger.Info($"性能对比测试完成，成功: {test.Success}，平均性能提升: {test.AveragePerformanceImprovement:F1}%");
                return test;
            }
            catch (Exception ex)
            {
                test.Success = false;
                test.ErrorMessage = ex.Message;
                test.EndTime = DateTime.Now;
                Logger.Error($"性能对比测试失败: {ex.Message}", ex);
                return test;
            }
        }
        
        /// <summary>
        /// 获取测试用户ID
        /// </summary>
        /// <returns>用户ID</returns>
        private async Task<int> GetTestUserIdAsync()
        {
            var allUsers = _memberBLL.GetAllList();
            var activeUser = allUsers.FirstOrDefault(u => u.Status == (int)MemberStatus.enable);
            return activeUser?.Id ?? 0;
        }
        
        /// <summary>
        /// 获取多个测试用户ID
        /// </summary>
        /// <param name="count">用户数量</param>
        /// <returns>用户ID列表</returns>
        private async Task<List<int>> GetMultipleTestUserIdsAsync(int count)
        {
            var allUsers = _memberBLL.GetAllList();
            return allUsers.Where(u => u.Status == (int)MemberStatus.enable)
                          .Take(count)
                          .Select(u => u.Id)
                          .ToList();
        }
        
        /// <summary>
        /// 确定整体测试成功状态
        /// </summary>
        /// <param name="testResult">测试结果</param>
        /// <returns>是否成功</returns>
        private bool DetermineOverallSuccess(PrecomputeTestResult testResult)
        {
            return testResult.PrecomputeTest?.Success == true &&
                   testResult.CacheUsageTest?.Success == true &&
                   testResult.RecommendationEngineTest?.Success == true &&
                   testResult.PerformanceTest?.Success == true;
        }
    }

    #region 测试数据模型

    /// <summary>
    /// 预计算测试结果
    /// </summary>
    public class PrecomputeTestResult
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public bool OverallSuccess { get; set; }
        public string ErrorMessage { get; set; }

        public PrecomputeFunctionalityTest PrecomputeTest { get; set; }
        public CacheUsageTest CacheUsageTest { get; set; }
        public RecommendationEngineIntegrationTest RecommendationEngineTest { get; set; }
        public PerformanceComparisonTest PerformanceTest { get; set; }
    }

    /// <summary>
    /// 预计算功能测试
    /// </summary>
    public class PrecomputeFunctionalityTest
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }

        public bool PrecomputeExecuted { get; set; }
        public bool CacheGenerated { get; set; }
        public int CachedRecommendationCount { get; set; }
    }

    /// <summary>
    /// 缓存使用测试
    /// </summary>
    public class CacheUsageTest
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }

        public bool RecommendationEngineUsesCache { get; set; }
        public double RecommendationEngineLatency { get; set; }
        public bool NewsVectorSearchUsesCache { get; set; }
        public double NewsVectorSearchLatency { get; set; }
    }

    /// <summary>
    /// 推荐引擎集成测试
    /// </summary>
    public class RecommendationEngineIntegrationTest
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }

        public int WithoutCacheCount { get; set; }
        public int WithCacheCount { get; set; }
        public double LatencyWithoutCache { get; set; }
        public double LatencyWithCache { get; set; }
        public double PerformanceImprovement { get; set; }
    }

    /// <summary>
    /// 性能对比测试
    /// </summary>
    public class PerformanceComparisonTest
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }

        public int TestedUserCount { get; set; }
        public double AverageLatencyWithoutCache { get; set; }
        public double AverageLatencyWithCache { get; set; }
        public double AveragePerformanceImprovement { get; set; }
    }

    #endregion
}
