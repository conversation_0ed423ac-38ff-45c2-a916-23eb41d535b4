﻿@using Banyan.Domain
@{ ViewBag.Name = "我的项目约见";
                Layout = "/Views/Shared/_Layout.cshtml";
                var rolelist = (List<Banyan.Domain.Role>)ViewData["rolelist"];
                var manager = ViewData["manager"] as Banyan.Domain.Member; }

<style>
    td .layui-table-cell, .layui-table-cell img {
        height: 35px !important;
        line-height: 35px !important;
    }
    a {
        color: #4E6EF2;
    }
</style>
<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li>我的项目约见</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <div class="tab-pane active">
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="pull-right search-bar"></div>
                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                                <div class="form-group">
                                    <select class="form-control" id="keyclass" name="keyclass" size="1">
                                        <option value="0">全部</option>
                                        @if (rolelist != null && rolelist.Count() > 0)
                                        {
                                            foreach (var ci in rolelist)
                                            {
                            <option value="@(ci.Id)">@(ci.RoleName)</option>}
                                        }
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="title-name">搜索</label>
                                    <input class="form-control" type="text" id="keyname" name="keyname" placeholder="搜索">
                                </div>
                                <div class="form-group">
                                    <div class="input-daterange input-group">
                                        <input class="form-control" type="text" id="startdate" name="startdate" placeholder="开始日期">
                                        <span class="input-group-addon" style="border-left:none;border-right:none;"><i class="fa fa-chevron-right"></i></span>
                                        <input class="form-control" type="text" id="enddate" name="enddate" placeholder="结束日期">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <a class="btn btn-default" id="dosearch">搜索</a>
                                    <a class="btn btn-minw btn-warning" id="doexport">导出Excel</a>
                                    <a class="btn btn-default" href="@(Url.Action("ProjectSet","Index"))"><i class="fa fa-plus"></i>&nbsp;&nbsp;新建项目约见</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <table class="layui-hide" id="table-list" lay-filter="list-filter"></table>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="bartpl">
    <div class="btn-group" role="group" data-toggle="buttons">
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="预览" lay-event="preview" data-original-title="预览"><i class="fa fa-eye"></i></button>
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="编辑" lay-event="modify" data-original-title="编辑"><i class="fa fa-pencil"></i></button>
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="删除" lay-event="delete" data-original-title="删除"><i class="fa fa-times"></i></button>
    </div>
</script>

@section scripts{
    <script type="text/javascript">
        var queryParams = {}, date = new Date();
        layui.use(['laypage', 'layer', 'table', 'laydate'], function () {
            var laypage = layui.laypage,
                layer = layui.layer,
                laydate = layui.laydate,
                table = layui.table;

            table.render({
                elem: '#table-list'
                , height: 570
                , url: '@(Url.Action("myprojectlist", "adminapi"))'
                , where: { myproject: true }
                , page: true
                , method: 'post'
                , cols: [[
                    { field: 'RoleName', title: '项目组', fixed: 'left', width: 90 }
                    , { field: 'EditorName', title: '编辑人', fixed: 'left', width: 80 }
                    , {
                        field: 'Name', title: '项目名', fixed: 'left', width: 120, templet: function (d) {
                            return `<a href='javascript:preview(${d.Id})'>${d.Name}</a>`
                        }
                    }
                      , { field: 'ProjectManager', fixed: 'left', title: '项目负责人', width: 100 }
                    , { field: 'groupMember', title: '项目组成员', width: 100 }
                    , { field: 'Source', title: '项目来源', width: 90 }
                    , { field: 'Introducer', title: '介绍人', width: 90 }
                    , { field: 'nextStepStatus', title: '项目状态', width: 120 }
                    , {
                        field: 'PubTime', title: '日期',  width: 100, templet: function (d) {
                            return (new Date(parseInt(d.PubTime.replace("/Date(", "").replace(")/", "").split("+")[0]))).pattern("yyyy-MM-dd");
                        }
                    }
                    , { field: 'InteralPTCP', title: '高榕参会人', width: 100 }
                    , { field: 'Participant', title: '外部参会人', width: 100 }

                    , { field: 'finder', title: '联系者', width: 90 }
                    , { field: 'DDManager', title: 'DD负责人', width: 90 }
                    , { field: 'UpdatedNews', title: '新发现', width: 180 }
                    , { field: 'city', title: '城市', width: 80 }
                    , { field: 'foundedYear', title: '年份', width: 60 }
                    , { field: 'HeadCount', title: '员工人数', width: 120 }
                    , { field: 'TotalAsset', title: '总资产规模', width: 150 }
                    , { field: 'Summary', title: '简介', width: 200 }
                    , { field: 'Background', title: '团队背景', width: 180 }
                    , { field: 'BusinessData', title: '业务数据', width: 180 }
                    , { field: 'FinancialData', title: '财务数据', width: 180 }
                    , { field: 'ShareStructure', title: '股权结构', width: 180 }
                    , { field: 'InvestHistory', title: '历史及当前融资方案', width: 250 }
                    , { field: 'CompareProduct', title: '提到的同行/供应商', width: 250 }
                    , { field: 'HighLight', title: '项目亮点', width: 250 }
                    , { field: 'Risk', title: '项目风险', width: 250 }

                    , { field: 'NextStep', title: '数据包分析/后续工作', width: 230 }

                    //, { field: 'ViewCount', title: '浏览量', width: 80 }
                   // , { field: 'CommentCount', title: '评论数', width: 80 }
                   // , { field: 'CollectCount', title: '收藏量', width: 80 }
                    , { fixed: 'right', width: 100, align: 'center', toolbar: '#bartpl' }
        ]],
                done: function () { }
            });

            table.on('tool(list-filter)', function (obj) {
            var data = obj.data
                , layEvent = obj.event;

            if (layEvent === 'issue') {
                fieldset(data.Id, 'issue', data.Status);
            }else if (layEvent === 'delete') {
                layer.confirm('确认删除该项目吗？', function (index) {
                    layer.close(index);
                    fieldset(data.Id, 'delete', data.Status)
                });
            } else if (layEvent === 'modify') {
                window.location.href = "projectset?id=" + data.Id;
            } else if (layEvent === 'recomend') {
                fieldset(data.Id, 'recomend', data.IsRecommend ? 1 : 0);
            } else if (layEvent === 'top') {
                fieldset(data.Id, 'top', data.IsStick ? 1 : 0);
            } else if (layEvent === 'sort') {
                layer.prompt({ title: '排序值设置' }, function (text, index) {
                    if (!/^(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9])$/.test(text)) {
                        layer.msg('请输入0-255的正整数！');
                        return;
                    }
                    layer.close(index);
                    fieldset(data.Id, 'sort', parseInt(text));
                });
            } else if (layEvent === 'preview') {
                preview(data.Id);
            }
            return;
        });

            laypage.render({
            elem: 'pageBar'
            , count: 100
            , jump: function (obj, first) {
                if (!first) {
                    layer.msg('第' + obj.curr + '页');
                }
            }
            });
            table.on('rowDouble(list-filter)', function (obj) {
                console.log(obj)
                preview(obj.data.Id);
                //obj 同上
            });
            laydate.render({
            elem: '#startdate'
        });

            laydate.render({
            elem: '#enddate'
        });
            $('#keyname').on('keypress', function (event) {
                if (event.keyCode === 13) {
                    $('#dosearch').trigger('click');
                }
            });
            $('#dosearch').on('click', function () {
            queryParams = {
                ToRoleId: $('#keyclass').val(),
                Name: $('#keyname').val(),
                startdate: $('#startdate').val(),
                enddate: $('#enddate').val(),
            }
            table.reload('table-list',{
                where: queryParams, page: { curr: 1 },
            });
            });

            $('#doexport').on('click', function () {
                var querystr = 'ToRoleId=' + $('#keyclass').val() + '&Name=' + $('#keyname').val() + '&startdate=' + $('#startdate').val() + '&enddate=' + $('#enddate').val() + '&myproject=true';;
                if ($('#downloadcsv').length <= 0)
                    $('body').append("<iframe id=\"downloadcsv\" style=\"display:none\"></iframe>");
                $('#downloadcsv').attr('src', "/adminapi/exportprojects?" + encodeURI(querystr.trim('&')));
            });
        });

        function fieldset(id, field, state) {
            $.ajax({
                type: 'POST',
                url: '@(Url.Action("projectset", "adminapi"))',
                data: { id: id, field: field, state: state },
                success: function (data) {
                    if (data.code == 0) {
                        layer.msg('操作成功！');
                        $('#dosearch').click();
                    } else {
                        layer.msg(data.msg);
                    }
                },
                error: function () {
                    layui.layer.msg("很抱歉，请求异常！");
                }
            });
        }

        function preview(id) {
            layer.open({
                type: 2,
                area: ['850px', '667px'],
                fix: false,
                maxmin: true,
                anim: 5,
                shade: 0,
                title: "项目预览",
                content: '/index/preview?id=' + id,
            });
        }
    </script>
}
