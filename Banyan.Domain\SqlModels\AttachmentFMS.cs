﻿using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.BusinessCenterPortal)]
    public class AttachmentFMS
    {
        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        [SqlField]
        public int SourceId { get; set; }

        [SqlField]
        public Byte SourceType { get; set; } = (Byte)SourceTypeEnumFMS.Exit;

        [SqlField]
        public string AtName { get; set; }

        [SqlField]
        public string AtSuffix { get; set; }

        [SqlField]
        public string AtUrl { get; set; }

        [SqlField]
        public string Creator { get; set; }

        [SqlField]
        public string Content { get; set; }

        [SqlField]
        public int AtLength { get; set; }

        [SqlField]
        public DateTime AddTime { get; set; } = DateTime.Now;

        [SqlField]
        public string Remark { get; set; }

        [SqlField]
        public string Path { get; set; }

    }
    public enum SourceTypeEnumFMS
    {
        Exit = 1,
    }
}
