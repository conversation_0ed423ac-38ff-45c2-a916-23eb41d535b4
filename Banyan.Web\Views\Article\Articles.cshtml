﻿@using Banyan.Domain
@{
    ViewBag.Title = "专家访谈管理";
    Layout = "/Views/Shared/_Layout.cshtml";
    var classList = (List<Banyan.Domain.Role>)ViewData["rolelist"];
    var manager = ViewData["manager"] as Banyan.Domain.Member;
}

<style>
    td .layui-table-cell, .layui-table-cell img {
        height: 35px !important;
        line-height: 35px !important;
    }
</style>

<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li>专家访谈管理</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <div class="tab-pane active">
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="pull-right search-bar"></div>
                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                                <div class="form-group">
                                    <select class="form-control" id="keyclass" name="keyclass" size="1">
                                        <option value="0">项目组</option>
                                        @if (classList != null && classList.Count() > 0)
                                        {
                                            foreach (var ci in classList)
                                            {
                                                <option value="@(ci.Id)">@(ci.RoleName)</option>
                                            }
                                        }
                                    </select>
                                </div>
                                <div class="form-group">
                                    <select class="form-control" id="creator" name="creator" size="1">
                                        <option value="0">创建人</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="title-name">关键词</label>
                                    <input class="form-control" type="text" id="keyname" name="keyname" placeholder="请输入关键词">
                                </div>
                                <div class="form-group">
                                    <div class="input-daterange input-group">
                                        <input class="form-control" type="text" id="startdate" name="startdate" placeholder="开始日期">
                                        <span class="input-group-addon" style="border-left:none;border-right:none;"><i class="fa fa-chevron-right"></i></span>
                                        <input class="form-control" type="text" id="enddate" name="enddate" placeholder="结束日期">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-default" id="dosearch">搜索</button>
@if (manager.Levels == (byte)Banyan.Domain.MemberLevels.Administrator || manager.Levels == (byte)Banyan.Domain.MemberLevels.SuperUser)
{
                                    <a class="btn btn-minw btn-warning" id="doexport">导出Excel</a>
}
                                    <a class="btn btn-primary" href="@(Url.Action("ArticleSet","Article"))"><i class="fa fa-plus"></i>&nbsp;&nbsp;新建专家访谈</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <table class="layui-hide" id="table-list" lay-filter="list-filter"></table>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="bartpl">
    <div class="btn-group" role="group" data-toggle="buttons">
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="预览" lay-event="preview" data-original-title="预览"><i class="fa fa-eye"></i></button>
        {{#  if(d.IsOperate){ }}
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="编辑" lay-event="modify" data-original-title="编辑"><i class="fa fa-pencil"></i></button>
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="删除" lay-event="delete" data-original-title="删除"><i class="fa fa-times"></i></button>
        {{#  } }}
    </div>
</script>

@section scripts{
    <script type="text/javascript">
        var queryParams = {}, date = new Date();
        layui.use(['laypage', 'layer', 'table', 'laydate'], function () {
            var laypage = layui.laypage,
                layer = layui.layer,
                laydate = layui.laydate,
                table = layui.table;

            table.render({
                elem: '#table-list'
                , height: 570
                , url: '@(Url.Action("articlelist", "adminapi"))'
                , page: true
                , method: 'post'
                , cols: [[
                    { field: 'RoleName', title: '项目组', width: 100, fixed: 'left' },
                    { field: 'EditorName', title: '编辑人', width: 90, fixed: 'left' },
                    { field: 'Title', title: '访谈对象', width: 120, fixed: 'left' }
                    , {
                        field: 'PubTime', title: '日期', fixed: 'left', width: 100, templet: function (d) {
                            return (new Date(parseInt(d.PubTime.replace("/Date(", "").replace(")/", "").split("+")[0]))).pattern("yyyy-MM-dd");
                        }
                    }
                    , { field: 'Summary', title: '访谈对象背景', width: 300 }
                    , { field: 'Content', title: 'Key Takeaway', width: 450 }
                    , { field: 'ViewCount', title: '浏览量', width: 80 }
                    , { field: 'CommentCount', title: '评论数', width: 80 }
                    , { field: 'CollectCount', title: '收藏量', width: 80 }
                    , { fixed: 'right', width: 100, align: 'center', toolbar: '#bartpl' }
                ]]
                , done: function () { }
            });
            table.on('tool(list-filter)', function (obj) {
            var data = obj.data
                , layEvent = obj.event;

            if (layEvent === 'issue') {
                fieldset(data.Id, 'issue', data.Status);
            }else if (layEvent === 'delete') {
                layer.confirm('确认删除该专家访谈吗？', function (index) {
                    layer.close(index);
                    fieldset(data.Id, 'delete', data.Status)
                });
            } else if (layEvent === 'modify') {
                window.location.href = "articleset?id=" + data.Id;
            } else if (layEvent === 'recomend') {
                fieldset(data.Id, 'recomend', data.IsRecommend ? 1 : 0);
            } else if (layEvent === 'top') {
                fieldset(data.Id, 'top', data.IsStick ? 1 : 0);
            } else if (layEvent === 'sort') {
                layer.prompt({ title: '排序值设置' }, function (text, index) {
                    if (!/^(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9])$/.test(text)) {
                        layer.msg('请输入0-255的正整数！');
                        return;
                    }
                    layer.close(index);
                    fieldset(data.Id, 'sort', parseInt(text));
                });
            } else if (layEvent === 'preview') {
                preview(data.Id);
            }
            return;
        });

            laypage.render({
            elem: 'pageBar'
            , count: 100
            , jump: function (obj, first) {
                if (!first) {
                    layer.msg('第' + obj.curr + '页');
                }
            }
            });
            laydate.render({
                elem: '#startdate'
            });
            laydate.render({
                elem: '#enddate'
            });
            $('#keyname').on('keypress', function(event) {
                if (event.keyCode === 13) {
                    $('#dosearch').trigger('click');
                }
            });
            $('#dosearch').on('click', function () {
                queryParams = {
                    classid: $('#keyclass').val(),
                    title: $('#keyname').val(),
                    startdate: $('#startdate').val(),
                    enddate: $('#enddate').val(),
                    Creator: $('#creator').val(),
                }
                table.reload('table-list',{
                    where: queryParams,
                });
            });

            getCreators();

            $('#doexport').on('click', function () {
                var querystr = 'ToRoleId=' + $('#keyclass').val() + '&title=' + $('#keyname').val() + '&startdate=' + $('#startdate').val() + '&enddate=' + $('#enddate').val();
                if ($('#downloadcsv').length <= 0)
                    $('body').append("<iframe id=\"downloadcsv\" style=\"display:none\"></iframe>");
                $('#downloadcsv').attr('src', "/adminapi/ExportArticles?" + encodeURI(querystr.trim('&')));
            });
        });

        function getCreators() {
            $.ajax({
                type: 'POST',
                url: '@(Url.Action("getcreators", "adminapi"))',
                data: { uid:@manager.Id,},
                success: function (data) {
                    console.log(data);
                    var htmlCode = '<option value="0">创建人</option>';
                    if (data && data.code == 0) {
                        var list = data.data || [];
                        $.each(list, function (index, item) {
                            htmlCode += '<option value="' + item.Id + '">' + item.RealName + '</option>';
                        });
                        $('#creator').html(htmlCode);
                    }
                },
                error: function () {
                    layui.layer.msg("很抱歉，请求异常！");
                }
            });
        }

        function fieldset(id, field, state) {
            $.ajax({
                type: 'POST',
                url: '@(Url.Action("articleset", "adminapi"))',
                data: { id: id, field: field, state: state },
                success: function (data) {
                    if (data.code == 0) {
                        layer.msg('操作成功！');
                        $('#dosearch').click();
                    } else {
                        layer.msg(data.msg);
                    }
                },
                error: function () {
                    layui.layer.msg("很抱歉，请求异常！");
                }
            });
        }

        function preview(id) {
            var h = document.documentElement.clientHeight || document.body.clientHeight;
            layer.open({
                type: 2,
                area: ['850px', h * 0.8 + 'px'],
                fix: false,
                maxmin: false,
                anim: 5,
                shade: 0,
                title: "访谈预览",
                content: '/article/preview?id=' + id,
            });
        }
    </script>
}
