﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Diagnostics.DiagnosticSource</name>
  </assembly>
  <members>
    <member name="T:System.Diagnostics.Activity">
      <summary>Represents an operation with context to be used for logging.</summary>
    </member>
    <member name="M:System.Diagnostics.Activity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.Activity" /> class.</summary>
      <param name="operationName">The name of the operation.</param>
    </member>
    <member name="P:System.Diagnostics.Activity.ActivityTraceFlags">
      <summary>Gets the flags (defined by the W3C ID specification) associated with the activity.</summary>
      <returns>the flags associated with the activity.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.AddBaggage(System.String,System.String)">
      <summary>Updates the <see cref="T:System.Diagnostics.Activity" /> to have a new baggage item with the specified key and value.</summary>
      <param name="key">The baggage key.</param>
      <param name="value">The baggage value.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.AddTag(System.String,System.String)">
      <summary>Updates the <see cref="T:System.Diagnostics.Activity" /> to have a new tag with the provided <paramref name="key" /> and <paramref name="value" />. .</summary>
      <param name="key">The tag key.</param>
      <param name="value">The tag value.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Baggage">
      <summary>Gets a collection of key/value pairs that represents information that is passed to children of this <see cref="T:System.Diagnostics.Activity" />.</summary>
      <returns>An enumeration of string-string key-value pairs.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Current">
      <summary>Gets or sets the current operation (<see cref="T:System.Diagnostics.Activity" />) for the current thread.  This flows across async calls.</summary>
      <returns>The current operation for the current thread.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.DefaultIdFormat">
      <summary>Gets or sets the default ID format for the <see cref="T:System.Diagnostics.Activity" />.</summary>
    </member>
    <member name="P:System.Diagnostics.Activity.Duration">
      <summary>Gets the duration of the operation.</summary>
      <returns>The delta between <see cref="P:System.Diagnostics.Activity.StartTimeUtc" /> and the end time if the <see cref="T:System.Diagnostics.Activity" /> has ended (<see cref="M:System.Diagnostics.Activity.Stop" /> or <see cref="M:System.Diagnostics.Activity.SetEndTime(System.DateTime)" /> was called), or <see cref="F:System.TimeSpan.Zero" /> if the <see cref="T:System.Diagnostics.Activity" /> has not ended and <see cref="M:System.Diagnostics.Activity.SetEndTime(System.DateTime)" /> was not called.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.ForceDefaultIdFormat">
      <summary>Gets or sets a valud that detrmines if the <see cref="P:System.Diagnostics.Activity.DefaultIdFormat" /> is always used to define the default ID format.</summary>
      <returns>
        <see langword="true" /> to always use the <see cref="P:System.Diagnostics.Activity.DefaultIdFormat" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.GetBaggageItem(System.String)">
      <summary>Returns the value of a key-value pair added to the activity with <see cref="M:System.Diagnostics.Activity.AddBaggage(System.String,System.String)" />.</summary>
      <param name="key">The baggage key.</param>
      <returns>The value of the key-value-pair item if it exists, or <see langword="null" /> if it does not exist.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Id">
      <summary>Gets an identifier that is specific to a particular request.</summary>
      <returns>The activity ID.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.IdFormat">
      <summary>Gets the format for the <see cref="P:System.Diagnostics.Activity.Id" />.</summary>
      <returns>The format for the <see cref="P:System.Diagnostics.Activity.Id" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.OperationName">
      <summary>Gets the operation name.</summary>
      <returns>The name of the operation.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Parent">
      <summary>Gets the parent <see cref="T:System.Diagnostics.Activity" /> that created this activity.</summary>
      <returns>The parent of this <see cref="T:System.Diagnostics.Activity" />, if it is from the same process, or <see langword="null" /> if this instance has no parent (it is a root activity) or if the parent is from outside the process.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.ParentId">
      <summary>Gets the ID of this activity's parent.</summary>
      <returns>The parent ID, if one exists, or <see langword="null" /> if it does not.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.ParentSpanId">
      <summary>Gets the parent's <see cref="P:System.Diagnostics.Activity.SpanId" />.</summary>
      <returns>The parent's <see cref="P:System.Diagnostics.Activity.SpanId" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.Recorded">
      <summary>Gets a value that indicates whether the W3CIdFlags.Recorded flag is set.</summary>
      <returns>
        <see langword="true" /> if the W3CIdFlags.Recorded flag is set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.RootId">
      <summary>Gets the root ID of this <see cref="T:System.Diagnostics.Activity" />.</summary>
      <returns>The root ID, or <see langword="null" /> if the current instance has either a <see cref="P:System.Diagnostics.Activity.ParentId" /> or an <see cref="P:System.Diagnostics.Activity.Id" />.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.SetEndTime(System.DateTime)">
      <summary>Updates the <see cref="T:System.Diagnostics.Activity" /> to set its <see cref="P:System.Diagnostics.Activity.Duration" /> as the difference between <see cref="P:System.Diagnostics.Activity.StartTimeUtc" /> and the specified stop time.</summary>
      <param name="endTimeUtc">The UTC stop time.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.SetIdFormat(System.Diagnostics.ActivityIdFormat)">
      <summary>Sets the ID format on this <see cref="T:System.Diagnostics.Activity" /> before it is started.</summary>
      <param name="format">One of the enumeration values that specifies the format of the <see cref="P:System.Diagnostics.Activity.Id" /> property.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.SetParentId(System.Diagnostics.ActivityTraceId,System.Diagnostics.ActivitySpanId,System.Diagnostics.ActivityTraceFlags)">
      <summary>Sets the parent ID using the W3C convention of a TraceId and a SpanId.</summary>
      <param name="traceId">The parent activity's TraceId.</param>
      <param name="spanId">The parent activity's SpanId.</param>
      <param name="activityTraceFlags">One of the enumeration values that specifies flags defined by the W3C standard that are associated with an activity.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.SetParentId(System.String)">
      <summary>Updates this <see cref="T:System.Diagnostics.Activity" /> to indicate that the <see cref="T:System.Diagnostics.Activity" /> with an ID of <paramref name="parentId" /> caused this <see cref="T:System.Diagnostics.Activity" />.</summary>
      <param name="parentId">The ID of the parent operation.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.SetStartTime(System.DateTime)">
      <summary>Sets the start time of this <see cref="T:System.Diagnostics.Activity" />.</summary>
      <param name="startTimeUtc">The <see cref="T:System.Diagnostics.Activity" /> start time in UTC.</param>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.SpanId">
      <summary>Gets the SPAN part of the <see cref="P:System.Diagnostics.Activity.Id" />.</summary>
      <returns>The ID for the SPAN part of <see cref="P:System.Diagnostics.Activity.Id" />, if the <see cref="T:System.Diagnostics.Activity" /> has the W3C format; otherwise, a zero <see langword="SpanId" />.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.Start">
      <summary>Starts the activity.</summary>
      <returns>
        <see langword="this" /> for convenient chaining.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.StartTimeUtc">
      <summary>Gets the time when the operation started.</summary>
      <returns>The UTC time that the operation started.</returns>
    </member>
    <member name="M:System.Diagnostics.Activity.Stop">
      <summary>Stops the activity.</summary>
    </member>
    <member name="P:System.Diagnostics.Activity.Tags">
      <summary>Gets a collection of key/value pairs that represent information that will be logged along with the <see cref="T:System.Diagnostics.Activity" /> to the logging system.</summary>
      <returns>An enumeration of string-string key-value-pairs.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.TraceId">
      <summary>Gets the TraceId part of the <see cref="P:System.Diagnostics.Activity.Id" />.</summary>
      <returns>The ID for the TraceId part of the <see cref="P:System.Diagnostics.Activity.Id" />, if the ID has the W3C format; otherwise, a zero TraceId.</returns>
    </member>
    <member name="P:System.Diagnostics.Activity.TraceStateString">
      <summary>Gets the W3C <see langword="tracestate" /> header.</summary>
      <returns>The W3C <see langword="tracestate" /> header.</returns>
    </member>
    <member name="T:System.Diagnostics.ActivityIdFormat">
      <summary>Specifies the format of the <see cref="P:System.Diagnostics.Activity.Id" /> property.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivityIdFormat.Hierarchical">
      <summary>The hierarchical format.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivityIdFormat.Unknown">
      <summary>An unknown format.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivityIdFormat.W3C">
      <summary>The W3C format.</summary>
    </member>
    <member name="T:System.Diagnostics.ActivitySpanId">
      <summary>Represents a <see cref="P:System.Diagnostics.Activity.SpanId" /> formatted based on a W3C standard.</summary>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.CopyTo(System.Span{System.Byte})">
      <summary>Copies the 8 bytes of the current <see cref="T:System.Diagnostics.ActivitySpanId" /> to a specified span.</summary>
      <param name="destination">The span to which the 8 bytes of the SpanID are to be copied.</param>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.CreateFromBytes(System.ReadOnlySpan{System.Byte})">
      <summary>Creates a new <see cref="T:System.Diagnostics.ActivitySpanId" /> value from a read-only span of eight bytes.</summary>
      <param name="idData">A read-only span of eight bytes.</param>
      <returns>The new span ID.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="idData" /> does not contain eight bytes.</exception>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.CreateFromString(System.ReadOnlySpan{System.Char})">
      <summary>Creates a new <see cref="T:System.Diagnostics.ActivitySpanId" /> value from a read-only span of 16 hexadecimal characters.</summary>
      <param name="idData">A span that contains 16 hexadecimal characters.</param>
      <returns>The new span ID.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="idData" /> does not contain 16 hexadecimal characters.
-or-
The characters in <paramref name="idData" /> are not all lower-case hexadecimal characters or all zeros.</exception>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.CreateFromUtf8String(System.ReadOnlySpan{System.Byte})">
      <summary>Creates a new <see cref="T:System.Diagnostics.ActivitySpanId" /> value from a read-only span of UTF8-encoded bytes.</summary>
      <param name="idData">A read-only span of UTF8-encoded bytes.</param>
      <returns>The new span ID.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.CreateRandom">
      <summary>Creates a new <see cref="T:System.Diagnostics.ActivitySpanId" /> based on a random number (that is very likely to be unique).</summary>
      <returns>The new span ID.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.Equals(System.Diagnostics.ActivitySpanId)">
      <summary>Determines whether this instance and the specified <see cref="T:System.Diagnostics.ActivitySpanId" /> instance have the same value.</summary>
      <param name="spanId">The instance to compare.</param>
      <returns>
        <see langword="true" /> if <paramref name="spanId" /> has the same hex value as the current instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.Equals(System.Object)">
      <summary>the current instance and a specified object, which also must be an <see cref="T:System.Diagnostics.ActivitySpanId" /> instance, have the same value.</summary>
      <param name="obj">The object to compare.</param>
      <returns>
        <see langword="true" /> if <paramref name="obj" /> is an instance of <see cref="T:System.Diagnostics.ActivitySpanId" /> and has the same hex value as the current instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.GetHashCode">
      <summary>Returns the hash code of the SpanId.</summary>
      <returns>The hash code of the SpanId.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.op_Equality(System.Diagnostics.ActivitySpanId,System.Diagnostics.ActivitySpanId)">
      <summary>Determines whether two specified <see cref="T:System.Diagnostics.ActivitySpanId" /> instances have the same value.</summary>
      <param name="spanId1">The first instance to compare.</param>
      <param name="spandId2">The second instance to compare.</param>
      <returns>
        <see langword="true" /> if the SpanId of <paramref name="spanId1" /> is the same as the SpanId of <paramref name="spandId2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.op_Inequality(System.Diagnostics.ActivitySpanId,System.Diagnostics.ActivitySpanId)">
      <summary>Determine whether two specified <see cref="T:System.Diagnostics.ActivitySpanId" /> instances have unequal values.</summary>
      <param name="spanId1">The first instance to compare.</param>
      <param name="spandId2">The second instance to compare.</param>
      <returns>
        <see langword="true" /> if the SpanId of <paramref name="spanId1" /> is different from the SpanId of <paramref name="spandId2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.ToHexString">
      <summary>Returns a 16-character hexadecimal string that represents this span ID.</summary>
      <returns>The 16-character hecxadecimal string representation of this span ID.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivitySpanId.ToString">
      <summary>Returns a 16-character hexadecimal string that represents this span ID.</summary>
      <returns>The 16-character hexadecimal string representation of this span ID.</returns>
    </member>
    <member name="T:System.Diagnostics.ActivityTraceFlags">
      <summary>Specifies flags defined by the W3C standard that are associated with an activity.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivityTraceFlags.None">
      <summary>The activity has not been marked.</summary>
    </member>
    <member name="F:System.Diagnostics.ActivityTraceFlags.Recorded">
      <summary>The activity (or more likely its parents) has been marked as useful to record.</summary>
    </member>
    <member name="T:System.Diagnostics.ActivityTraceId">
      <summary>Represents a <see cref="P:System.Diagnostics.Activity.TraceId" /> whose format is based on a W3C standard.</summary>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.CopyTo(System.Span{System.Byte})">
      <summary>Copies the 16 bytes of the current <see cref="T:System.Diagnostics.ActivityTraceId" /> to a specified span.</summary>
      <param name="destination">The span to which the 16 bytes of the trace ID are to be copied.</param>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.CreateFromBytes(System.ReadOnlySpan{System.Byte})">
      <summary>Creates a new <see cref="T:System.Diagnostics.ActivityTraceId" /> value from a read-only span of 16 bytes.</summary>
      <param name="idData">A read-only span of 16 bytes.</param>
      <returns>The new trace ID.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="idData" /> does not contain eight bytes.</exception>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.CreateFromString(System.ReadOnlySpan{System.Char})">
      <summary>Creates a new <see cref="T:System.Diagnostics.ActivityTraceId" /> value from a read-only span of 32 hexadecimal characters.</summary>
      <param name="idData">A span that contains 32 hexadecimal characters.</param>
      <returns>The new trace ID.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="idData" /> does not contain 16 hexadecimal characters.
-or-
The characters in <paramref name="idData" /> are not all lower-case hexadecimal characters or all zeros.</exception>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.CreateFromUtf8String(System.ReadOnlySpan{System.Byte})">
      <summary>Creates a new <see cref="T:System.Diagnostics.ActivityTraceId" /> value from a read-only span of UTF8-encoded bytes.</summary>
      <param name="idData">A read-only span of UTF8-encoded bytes.</param>
      <returns>The new trace ID.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.CreateRandom">
      <summary>Creates a new <see cref="T:System.Diagnostics.ActivitySpanId" /> based on a random number (that is very likely to be unique).</summary>
      <returns>The new span ID.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.Equals(System.Diagnostics.ActivityTraceId)">
      <summary>Determines whether the current instance and a specified <see cref="T:System.Diagnostics.ActivityTraceId" /> are equal.</summary>
      <param name="traceId">The instance to compare.</param>
      <returns>
        <see langword="true" /> if <paramref name="traceId" /> has the same hex value as the current instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.Equals(System.Object)">
      <summary>Determines whether this instance and a specified object, which must also be an <see cref="T:System.Diagnostics.ActivityTraceId" /> instance, have the same value.</summary>
      <param name="obj">The object to compare.</param>
      <returns>
        <see langword="true" /> if <paramref name="obj" /> is an instance of <see cref="T:System.Diagnostics.ActivityTraceId" /> and has the same hex value as the current instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.GetHashCode">
      <summary>Returns the hash code of the TraceId.</summary>
      <returns>The hash code of the TraceId.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.op_Equality(System.Diagnostics.ActivityTraceId,System.Diagnostics.ActivityTraceId)">
      <summary>Determines whether two specified <see cref="T:System.Diagnostics.ActivityTraceId" /> instances have the same value.</summary>
      <param name="traceId1">The first instance to compare.</param>
      <param name="traceId2">The second instance to compare.</param>
      <returns>
        <see langword="true" /> if the TraceId of <paramref name="traceId1" /> is the same as the TraceId of <paramref name="traceId2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.op_Inequality(System.Diagnostics.ActivityTraceId,System.Diagnostics.ActivityTraceId)">
      <summary>Determines whether two specified <see cref="T:System.Diagnostics.ActivityTraceId" /> instances have the same value.</summary>
      <param name="traceId1">The first instance to compare.</param>
      <param name="traceId2">The second instance to compare.</param>
      <returns>
        <see langword="true" /> if the TraceId of <paramref name="traceId1" /> is different from the TraceId of <paramref name="traceId2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.ToHexString">
      <summary>Returns a 16-character hexadecimal string that represents this span ID.</summary>
      <returns>The 32-character hexadecimal string representation of this trace ID.</returns>
    </member>
    <member name="M:System.Diagnostics.ActivityTraceId.ToString">
      <summary>Returns a 32-character hexadecimal string that represents this trace ID.</summary>
      <returns>The 32-character hecxadecimal string representation of this trace ID.</returns>
    </member>
    <member name="T:System.Diagnostics.DiagnosticListener">
      <summary>Provides an implementation of the abstract <see cref="T:System.Diagnostics.DiagnosticSource" /> class that represents a named place to which a source sends its information (events).</summary>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.#ctor(System.String)">
      <summary>Creates a new <see cref="T:System.Diagnostics.DiagnosticListener" />.</summary>
      <param name="name">The name of this <see cref="T:System.Diagnostics.DiagnosticListener" />.</param>
    </member>
    <member name="P:System.Diagnostics.DiagnosticListener.AllListeners">
      <summary>Gets the collection of listeners for this <see cref="T:System.Diagnostics.DiagnosticListener" />.</summary>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.Dispose">
      <summary>Disposes the NotificationListeners.</summary>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.IsEnabled">
      <summary>Determines whether there are any registered subscribers.</summary>
      <returns>
        <see langword="true" /> if there are any registered subscribers, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.IsEnabled(System.String)">
      <summary>Checks whether the <see cref="T:System.Diagnostics.DiagnosticListener" /> is enabled.</summary>
      <param name="name" />
      <returns>
        <see langword="true" /> if notifications are enabled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.IsEnabled(System.String,System.Object,System.Object)">
      <summary>Checks if the DiagnosticListener is enabled.</summary>
      <param name="name" />
      <param name="arg1" />
      <param name="arg2" />
      <returns>
        <see langword="true" /> if it is enabled, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="P:System.Diagnostics.DiagnosticListener.Name">
      <summary>Gets the name of this <see cref="T:System.Diagnostics.DiagnosticListener" />.</summary>
      <returns>The name of the <see cref="T:System.Diagnostics.DiagnosticListener" />.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.OnActivityExport(System.Diagnostics.Activity,System.Object)">
      <summary>Invokes the OnActivityExport method of all the subscribers.</summary>
      <param name="activity">The activity affected by an external event.</param>
      <param name="payload">An object that represents the outgoing request.</param>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.OnActivityImport(System.Diagnostics.Activity,System.Object)">
      <summary>Invokes the OnActivityImport method of all the subscribers.</summary>
      <param name="activity">The activity affected by an external event.</param>
      <param name="payload">An object that represents the incoming request.</param>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.Subscribe(System.IObserver{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
      <summary>Adds a subscriber.</summary>
      <param name="observer">A subscriber.</param>
      <returns>A reference to an interface that allows the listener to stop receiving notifications before the <see cref="T:System.Diagnostics.DiagnosticSource" /> has finished sending them.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.Subscribe(System.IObserver{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Func{System.String,System.Object,System.Object,System.Boolean})">
      <summary>Adds a subscriber, and optionally filters events based on their name and up to two context objects.</summary>
      <param name="observer">A subscriber.</param>
      <param name="isEnabled">A delegate that filters events based on their name and up to two context objects (which can be <see langword="null" />), or <see langword="null" /> to if an event filter is not desirable.</param>
      <returns>A reference to an interface that allows the listener to stop receiving notifications before the <see cref="T:System.Diagnostics.DiagnosticSource" /> has finished sending them.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.Subscribe(System.IObserver{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Func{System.String,System.Object,System.Object,System.Boolean},System.Action{System.Diagnostics.Activity,System.Object},System.Action{System.Diagnostics.Activity,System.Object})">
      <summary>Adds a subscriber, optionally filters events based on their name and up to two context objects, and specifies methods to call when providers import or export activites from outside the process.</summary>
      <param name="observer">A subscriber.</param>
      <param name="isEnabled">A delegate that filters events based on their name and up to two context objects (which can be <see langword="null" />), or <see langword="null" /> if an event filter is not desirable.</param>
      <param name="onActivityImport">An action delegate that receives the activity affected by an external event and an object that represents the incoming request.</param>
      <param name="onActivityExport">An action delegate that receives the activity affected by an external event and an object that represents the outgoing request.</param>
      <returns>A reference to an interface that allows the listener to stop receiving notifications before the <see cref="T:System.Diagnostics.DiagnosticSource" /> has finished sending them.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.Subscribe(System.IObserver{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Predicate{System.String})">
      <summary>Adds a subscriber, and optionally filters events based on their name.</summary>
      <param name="observer">A subscriber.</param>
      <param name="isEnabled">A delegate that filters events based on their name (<see cref="T:System.String" />). The delegate should return <see langword="true" /> if the event is enabled.</param>
      <returns>A reference to an interface that allows the listener to stop receiving notifications before the <see cref="T:System.Diagnostics.DiagnosticSource" /> has finished sending them.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.ToString">
      <summary>Returns a string with the name of this DiagnosticListener.</summary>
      <returns>The name of this DiagnosticListener.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticListener.Write(System.String,System.Object)">
      <summary>Logs a notification.</summary>
      <param name="name">The name of the event to log.</param>
      <param name="value">An object that represents the payload for the event.</param>
    </member>
    <member name="T:System.Diagnostics.DiagnosticSource">
      <summary>An abstract class that allows code to be instrumented for production-time logging of rich data payloads for consumption within the process that was instrumented.</summary>
    </member>
    <member name="M:System.Diagnostics.DiagnosticSource.#ctor">
      <summary>Initializes an instance of the <see cref="T:System.Diagnostics.DiagnosticSource" /> class.</summary>
    </member>
    <member name="M:System.Diagnostics.DiagnosticSource.IsEnabled(System.String)">
      <summary>Verifies if the notification event is enabled.</summary>
      <param name="name">The name of the event being written.</param>
      <returns>
        <see langword="true" /> if the notification event is enabled, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticSource.IsEnabled(System.String,System.Object,System.Object)">
      <summary>Verifies it the notification event is enabled.</summary>
      <param name="name">The name of the event being written.</param>
      <param name="arg1">An object that represents the additional context for IsEnabled. Consumers should expect to receive <see langword="null" /> which may indicate that producer called pure IsEnabled(string) to check if consumer wants to get notifications for such events at all. Based on that, producer may call IsEnabled(string, object, object) again with non-<see langword="null" /> context.</param>
      <param name="arg2">Optional. An object that represents the additional context for IsEnabled. <see langword="null" /> by default. Consumers should expect to receive <see langword="null" /> which may indicate that producer called pure IsEnabled(string) or producer passed all necessary context in <paramref name="arg1" />.</param>
      <returns>
        <see langword="true" /> if the notification event is enabled, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticSource.OnActivityExport(System.Diagnostics.Activity,System.Object)">
      <summary>Transfers state from an activity to some event or operation, such as an outgoing HTTP request, that will occur outside the process.</summary>
      <param name="activity">The activity affected by an external event.</param>
      <param name="payload">An object that represents the outgoing request.</param>
    </member>
    <member name="M:System.Diagnostics.DiagnosticSource.OnActivityImport(System.Diagnostics.Activity,System.Object)">
      <summary>Transfers state to an activity from some event or operation, such as an incoming request, that occurred outside the process.</summary>
      <param name="activity">The activity affected by an external event.</param>
      <param name="payload">A payload that represents the incoming request.</param>
    </member>
    <member name="M:System.Diagnostics.DiagnosticSource.StartActivity(System.Diagnostics.Activity,System.Object)">
      <summary>Starts an <see cref="T:System.Diagnostics.Activity" /> and writes a start event.</summary>
      <param name="activity">The <see cref="T:System.Diagnostics.Activity" /> to be started.</param>
      <param name="args">An object that represent the value being passed as a payload for the event.</param>
      <returns>The started activity for convenient chaining.</returns>
    </member>
    <member name="M:System.Diagnostics.DiagnosticSource.StopActivity(System.Diagnostics.Activity,System.Object)">
      <summary>Stops the given <see cref="T:System.Diagnostics.Activity" />, maintains the global <see cref="P:System.Diagnostics.Activity.Current" /> activity, and notifies consumers that the <see cref="T:System.Diagnostics.Activity" /> was stopped.</summary>
      <param name="activity">The activity to be stopped.</param>
      <param name="args">An object that represents the value passed as a payload for the event.</param>
    </member>
    <member name="M:System.Diagnostics.DiagnosticSource.Write(System.String,System.Object)">
      <summary>Provides a generic way of logging complex payloads.</summary>
      <param name="name">The name of the event being written.</param>
      <param name="value">An object that represents the value being passed as a payload for the event. This is often an anonymous type which contains several sub-values.</param>
    </member>
  </members>
</doc>