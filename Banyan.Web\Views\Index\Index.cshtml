﻿@using Banyan.Domain
@{
    ViewBag.Name = "项目约见管理";
    Layout = "/Views/Shared/_Layout.cshtml";
    var rolelist = (List<Banyan.Domain.Role>)ViewData["rolelist"];
    var creatorlist = (List<Banyan.Domain.Member>)ViewData["creatorlist"];
    var manager = ViewData["manager"] as Banyan.Domain.Member;
    var isAdmin = manager.Levels == (byte)Banyan.Domain.MemberLevels.Administrator || manager.Levels == (byte)Banyan.Domain.MemberLevels.SuperUser;
}
<style>

        a {
            color: #4E6EF2;
        }
        .ripple {
            display: inline-block;
            color: red;
            font-weight: bold; 
            animation: ripple 3s linear infinite;
        }
        @@keyframes ripple {
          0% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.17);
          }
          100% {
            transform: scale(1);
          }
        }
</style>
<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li>项目约见管理</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <div class="tab-pane active">
                @*<template v-for="item in waterArray">
                        <p class="water-mark" :style="{left: item.wid + '%', top: item.hei + '%'}">@(manager.Id)</p>
                    </template>*@
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="pull-right search-bar"></div>
                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                                <div class="form-group">
                                    <select class="form-control" id="keyclass" name="keyclass" size="1">
                                        <option value="0">项目组</option>
                                        @if (rolelist != null && rolelist.Count() > 0)
                                        {
                                            foreach (var ci in rolelist)
                                            {
                                                <option value="@(ci.Id)">@(ci.RoleName)</option>
                                            }
                                        }
                                    </select>
                                </div>
                                <div class="form-group">
                                    <select class="form-control" id="creator" name="creator" size="1">
                                        <option value="">编辑人</option>
                                        @if (creatorlist != null && creatorlist.Count() > 0)
                                        {
                                            foreach (var creator in creatorlist)
                                            {
                                                <option value="@(creator.RealName)">@(creator.RealName)</option>
                                            }
                                        }
                                    </select>
                                </div>
                                @if (isAdmin)
                                {
                            <div style="display:none" id="special">
                                <div class="form-group">
                                    <select class="form-control" id="isPrivate" name="isPrivate" size="1">
                                        <option value="0">私密性</option>
                                        <option value="1">私密</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <select class="form-control" id="isSilver" name="isSilver" size="1">
                                        <option value="0">是否银子弹</option>
                                        <option value="1">银子弹</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <select class="form-control" id="isMemo" name="isMemo" size="1">
                                        <option value="0">是否纪要</option>
                                        <option value="1">纪要</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <select class="form-control" id="isScore" name="isScore" size="1">
                                        <option value="0">是否打分（唯一筛选）</option>
                                        <option value="1">已打分</option>
                                    </select>
                                </div>
                            </div>
                                }
                                <div class="form-group">
                                    <select class="form-control" id="projectStatus" name="projectStatus" size="1">
                                        <option value="">项目状态</option>
                                        <option value="已投项目新一轮">已投项目新一轮</option>
                                        <option value="TS已签署">TS已签署</option>
                                        <option value="Pre-DD">Pre-DD</option>
                                        <option value="安排合伙人见面">安排合伙人见面</option>
                                        <option value="小组讨论">小组讨论</option>
                                        <option value="不推进但持续关注">不推进但持续关注</option>
                                        <option value="Pass">Pass</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="title-name">搜索</label>
                                    <input style="width: 370px" class="form-control col-md-3" type="text" id="keyname" name="keyname" placeholder="回车搜索，同时包含多关键字搜索模式为A 与 B">
                                </div>

                                <div class="form-group">
                                    <a class="btn btn-default" id="dosearch">搜索</a>
                                    <div style="width: 130px" class="form-group">
                                        <select class="search-type form-control">
                                        </select>
                                    </div>
                                    <!--div style="width: 330px" class="form-group">
                                        <select class="search-type-milvus form-control">
                                        </select>
                                    </div>
                                    <a onclick="explainvectorsearch()" style="text-decoration: underline; cursor: pointer;">向量搜索？</a-->
                                </div>
                                <div class="form-group">
                                    <div class="input-daterange input-group">
                                        <input class="form-control" type="text" id="startdate" name="startdate" placeholder="开始日期" size="7">
                                        <span class="input-group-addon" style="border-left:none;border-right:none;"><i class="fa fa-chevron-right"></i></span>
                                        <input class="form-control" type="text" id="enddate" name="enddate" placeholder="结束日期" size="7">
                                    </div>
                                </div>

                                <div class="form-group">
                                    @if (isAdmin)
                                    {
                                        <a class="btn btn-minw btn-warning" id="doexport">导出Excel</a>
                                    }
                                    <a class="btn btn-default" href="@(Url.Action("ProjectSet","Index"))"><i class="fa fa-plus"></i>&nbsp;&nbsp;新建项目约见</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <table class="layui-hide" id="table-list" lay-filter="list-filter"></table>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="bartpl">
    <div class="btn-group" role="group" data-toggle="buttons">
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="预览" lay-event="preview" data-original-title="预览"><i class="fa fa-eye"></i></button>
        {{#  if(d.IsOperate){ }}
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="编辑" lay-event="modify" data-original-title="编辑"><i class="fa fa-pencil"></i></button>
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="删除" lay-event="delete" data-original-title="删除"><i class="fa fa-times"></i></button>
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="评分" lay-event="score" data-original-title="评分"><i class="fa fa-bullseye"></i></button>
        {{#  } }}
    </div>
</script>

@section scripts{
    <style type="text/css">
        .select2-container .selection .select2-selection--single {
            height: 32px;
            line-height: 32px;
        }

        .select2-container--default .selection .select2-selection--single .select2-selection__rendered {
            line-height: 32px;
        }

        .select2-container--default .selection .select2-selection--single .select2-selection__arrow {
            height: 32px;
        }
        .select2-container--default .selection .select2-selection--single {
            border: 1px solid #dcdee2;
        }
    </style>
    <link href="/content/js/plugins/select2/select2.min.css" rel="stylesheet" />
    <link href="/content/js/plugins/select2/select2-bootstrap.min.css" rel="stylesheet" />
    <script type="text/javascript" src="/content/js/plugins/select2/select2.min.js"></script>
    <script type="text/javascript">
        $(function () {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == "admin" && pair[1] == "true") {
                    $("#special").css("display", "inline")
                }
            }
        })
        var queryParams = {}, date = new Date();
        layui.use(['laypage', 'layer', 'table', 'laydate'], function () {
            var laypage = layui.laypage,
                layer = layui.layer,
                laydate = layui.laydate,
                table = layui.table;


            $('.search-type').select2({
                language: "zh-CN",
                width: "100%",
                minimumResultsForSearch: 1,
                minimumInputLength: 1,
                placeholder: '项目名快速匹配',
                ajax: {
                    url: '/adminapi/searchProjectName2',
                    dataType: 'json',
                    type: 'POST',
                    processResults: function (data) {
                        // Transforms the top-level key of the response object from 'items' to 'results'
                        return {
                            results: data.data.map(val => {
                                val.id = val.Id
                                val.text = val.Name
                                return val
                            })
                        };
                    },
                    data: function (params) {
                        var query = {
                            Name: params.term,
                        }

                        // Query parameters will be ?search=[term]&type=public
                        return query;
                    }
                }
            });
            $('.search-type-milvus').select2({
                language: "zh-CN",
                width: "100%",
                minimumResultsForSearch: 1,
                minimumInputLength: 1,
                placeholder: '向量语义模糊搜索(不含项目名)',
                ajax: {
                    url: '/adminapi/searchProjectMilvus',
                    dataType: 'json',
                    type: 'POST',
                    processResults: function (data) {
                        // Transforms the top-level key of the response object from 'items' to 'results'
                        if (data.data == null) {
                            return;
                        }
                        return {
                            results: data.data.map(val => {
                                val.id = val.Id
                                val.text = val.Summary + " "  +val.Name + " " + val.ProjectManager
                                return val
                            })
                        };
                    },
                    data: function (params) {
                        var query = {
                            Name: params.term,
                        }

                        // Query parameters will be ?search=[term]&type=public
                        return query;
                    }
                }
            });
            $('.search-type-milvus').on('select2:select', function (e) {
                var data = e.params.data;
                if (data.HasRight) {
                    return preview(data.Id)
                }
                return alert("无权访问，请联系项目负责人")
            })

            $('.search-type').on('select2:select', function (e) {
                var data = e.params.data;
                queryParams = {
                    ToRoleId: $('#keyclass').val(),
                    Name: data.Name,
                    startdate: $('#startdate').val(),
                    enddate: $('#enddate').val(),
                    Creator: $('#creator').val(),
                    projectStatus: $('#projectStatus').val(),
                    isPrivate: $('#isPrivate').val(),
                    isSilver: $('#isSilver').val(),
                    isScore: $('#isScore').val(),
                    isMemo: $('#isMemo').val()
                }
                table.reload('table-list', {
                    where: queryParams, page: { curr: 1 },
                    url: '@(Url.Action("projectlistbyname", "adminapi"))'
                });
            })

            table.render({
                elem: '#table-list'
                , height: 570
                , url: '@(Url.Action("projectlist", "adminapi"))'
                , page: true
                , method: 'post'
                , cols: [[
                    { field: 'RoleName', title: '项目组', fixed: 'left', width: 90 }
                    , { field: 'EditorName', title: '编辑人', fixed: 'left', width: 80 }
                    , {
                        field: 'Name', title: '项目名', fixed: 'left', width: 120, templet: function (d) {
                            return `<a href='javascript:preview(${d.Id})'>${d.Name}</a>`
                        }
                    }
                    , { field: 'ProjectManager', fixed: 'left',title: '项目负责人', width: 100 }
                    , { field: 'groupMember', title: '其他项目组员', width: 120 }
                    , { field: 'nextStepStatus', title: '项目状态', width: 120 }
                    , { field: 'Source', title: '项目来源', sort: true, width: 130 }
                    , { field: 'Introducer', title: '介绍人', width: 90 }
                    , {
                        field: 'PubTime', title: '日期',  width: 110, templet: function (d) {
                            return (new Date(parseInt(d.PubTime.replace("/Date(", "").replace(")/", "").split("+")[0]))).pattern("yyyy-MM-dd");
                        }
                    }

                    , { field: 'InteralPTCP', title: '高榕参会人', width: 100 }
                    , { field: 'Participant', title: '外部参会人', width: 100 }

                    , { field: 'UpdatedNews', title: '新发现', width: 180 }
                    , { field: 'city', title: '城市', width: 80 }
                    , { field: 'foundedYear', title: '年份',  width: 70 }
                    , { field: 'HeadCount', title: '员工人数', width: 120 }
                    , { field: 'TotalAsset', title: '总资产规模', width: 150 }
                    , { field: 'Summary', title: '简介', width: 200 }
                    , { field: 'Founder', title: '创始人姓名', width: 100 }
                    , { field: 'Background', title: '团队背景', width: 180 }
                    , { field: 'BusinessData', title: '业务数据', width: 180 }
                    , { field: 'FinancialData', title: '财务数据', width: 180 }
                    , { field: 'ShareStructure', title: '股权结构', width: 180 }
                    , { field: 'InvestHistory', title: '历史及当前融资方案', width: 250 }
                    , { field: 'CompareProduct', title: '提到的同行/供应商', width: 250 }
                    , { field: 'HighLight', title: '项目亮点', width: 250 }
                    , { field: 'Risk', title: '项目风险', width: 250 }

                    , { field: 'DDManager', title: 'DD负责人', width: 90 }
                    , { field: 'finder', title: '联系者', width: 90 }
                    , { field: 'Currency', title: '融资币种', width: 90 }
                    , { field: 'ViewCount', title: '浏览量', width: 80 }
                    , { field: 'CommentCount', title: '评论数', width: 80 }
                    , { field: 'CollectCount', title: '收藏量', width: 80 }
                    , {
                        field: 'AddTime', title: '创建日期', width: 110, templet: function (d) {
                            var tmpTime = (new Date(parseInt(d.AddTime.replace("/Date(", "").replace(")/", "").split("+")[0]))).pattern("yyyy-MM-dd");
                            return tmpTime == "1-01-01" ? "" : tmpTime ;
                        }
                    }
                    , {
                        field: 'LastTime', title: '修改日期', width: 110, templet: function (d) {
                            var tmpTime = (new Date(parseInt(d.LastTime.replace("/Date(", "").replace(")/", "").split("+")[0]))).pattern("yyyy-MM-dd");
                            return tmpTime == "1-01-01" ? "" : tmpTime;
                        }
                    }
                    , { fixed: 'right', width: 120, align: 'center', toolbar: '#bartpl' }
                ]],
                done: function (res) {
                    if (res.msg == 'nologin') {
                        window.location.href = '/login/index'
                    }
                }
            });

            table.on('tool(list-filter)', function (obj) {
            var data = obj.data
                , layEvent = obj.event;

            if (layEvent === 'issue') {
                fieldset(data.Id, 'issue', data.Status);
            }else if (layEvent === 'delete') {
                layer.confirm('确认删除该项目吗？', function (index) {
                    layer.close(index);
                    fieldset(data.Id, 'delete', data.Status)
                });
            } else if (layEvent === 'modify') {
                window.location.href = "/index/projectset?id=" + data.Id;
            } else if (layEvent === 'recomend') {
                fieldset(data.Id, 'recomend', data.IsRecommend ? 1 : 0);
            } else if (layEvent === 'top') {
                fieldset(data.Id, 'top', data.IsStick ? 1 : 0);
            } else if (layEvent === 'sort') {
                layer.prompt({ title: '排序值设置' }, function (text, index) {
                    if (!/^(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9])$/.test(text)) {
                        layer.msg('请输入0-255的正整数！');
                        return;
                    }
                    layer.close(index);
                    fieldset(data.Id, 'sort', parseInt(text));
                });
            } else if (layEvent === 'preview') {
                preview(data.Id);
            } else if (layEvent === 'score') {
                window.location.href = '/index/score/' + data.Id;
            }
            return;
        });

        laypage.render({
            elem: 'pageBar'
            , count: 100
            , jump: function (obj, first) {
                if (!first) {
                    layer.msg('第' + obj.curr + '页');
                }
            }
        });
        table.on('rowDouble(list-filter)', function (obj) {
            console.log(obj)
            preview(obj.data.Id);
                //obj 同上
        });
        laydate.render({
            elem: '#startdate',
            done: dosearch
        });

        laydate.render({
            elem: '#enddate',
            done: dosearch
        });
        $('#keyname').on('keypress', function(event) {
            if (event.keyCode === 13) {
                $('#dosearch').trigger('click');
            }
        });
        $('#dosearch').on('click', dosearch);
        function dosearch() {
            queryParams = {
                ToRoleId: $('#keyclass').val(),
                Name: $('#keyname').val(),
                startdate: $('#startdate').val(),
                enddate: $('#enddate').val(),
                Creator: $('#creator').val(),
                projectStatus: $('#projectStatus').val(),
                isPrivate: $('#isPrivate').val(),
                isSilver: $('#isSilver').val(),
                isScore: $('#isScore').val(),
                isMemo: $('#isMemo').val()
            }
            table.reload('table-list', {
                where: queryParams, page: { curr: 1 },
            });
            $(".search-type").select2("val", " ");
            $(".search-type-milvus").select2("val", " ");
        }

        $('#creator').on('change', dosearch);
        $('#keyclass').on('change', dosearch);
        $('#projectStatus').on('change', dosearch);
        $('#startdate').on('change', dosearch);



        timer = false;
        debounce = function () {
            clearTimeout(timer);
            timer = setTimeout(dosearch, 500);
        }


        //$('#keyname').on('input', debounce); 顺序无法保证

    //getCreators();

        $('#doexport').on('click', function () {
            var querystr = 'ToRoleId=' + $('#keyclass').val() + '&Name=' + $('#keyname').val() + '&startdate=' + $('#startdate').val() + '&enddate=' + $('#enddate').val();
            if ($('#downloadcsv').length <= 0)
                $('body').append("<iframe id=\"downloadcsv\" style=\"display:none\"></iframe>");
            $('#downloadcsv').attr('src', "/adminapi/exportprojects?" + encodeURI(querystr.trim('&')));
        });
    });

        @*function getCreators() {
            $.ajax({
                type: 'POST',
                url: '@(Url.Action("getcreators", "adminapi"))',
                data: { uid:@manager.Id,},
                success: function (data) {
                    var htmlCode = '<option value="">编辑人</option>';
                    if (data && data.code == 0) {
                        var list = data.data || [];
                        $.each(list, function (index, item) {
                            htmlCode += '<option value="' + item.RealName + '">' + item.RealName + '</option>';
                        });
                        $('#creator').html(htmlCode);
                    }
                },
                error: function () {
                    layui.layer.msg("很抱歉，请求异常！");
                }
            });
        }*@

        function fieldset(id, field, state) {
            $.ajax({
                type: 'POST',
                url: '@(Url.Action("projectset", "adminapi"))',
                data: { id: id, field: field, state: state },
                success: function (data) {
                    if (data.code == 0) {
                        layer.msg('操作成功！');
                        $('#dosearch').click();
                    } else {
                        layer.msg(data.msg);
                    }
                },
                error: function () {
                    layui.layer.msg("很抱歉，请求异常！");
                }
            });
        }

        function explainvectorsearch(id) {
            var w = document.documentElement.clientWidth || document.body.clientWidth;
            var idx = layer.open({
                type: 2,
                area: [w*0.9+'px', 800+ 'px'],
                fixed: false,
                maxmin: true,
                anim: 5,
                shade: 0,
                title: "词向量、句子向量、向量语义搜索",
                content: '/index/explainvectorsearch',
                scrollbar: true
            });
            //layer.full(idx);
        }

        function preview(id) {
            var h = document.documentElement.clientHeight || document.body.clientHeight;
            layer.open({
                type: 2,
                area: ['850px', h*0.82 + 'px'],
                fix: false,
                maxmin: true,
                anim: 5,
                shade: 0,
                title: "项目预览",
                content: '/index/preview?id=' + id,
            });
        }
    </script>
}
