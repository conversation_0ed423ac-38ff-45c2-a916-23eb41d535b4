﻿namespace Banyan.Code
{
    public class AjaxResult
    {
        /// <summary>
        /// 返回状态码
        /// </summary>
        public int code { get; set; } = (int)ResultCode.exception;

        /// <summary>
        /// 返回数据
        /// </summary>
        public object data { get; set; } = null;

        /// <summary>
        /// 返回数据条数
        /// </summary>
        public int count { get; set; } = 0;

        /// <summary>
        /// 返回消息
        /// </summary>
        /// 
        public string msg { get; set; } = string.Empty;
    }

    public static class AjaxHelper
    {
        private static AjaxResult ajaxObj = null;

        public static AjaxResult ajaxResult
        {
            get
            {
                return new AjaxResult();  // 可使用原型模式
            }
            set
            {
                ajaxObj = value;
            }
        }

        public static AjaxResult JOk(this AjaxResult ajaxResult, object data)
        {
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = data;
            return ajaxResult;
        }

        public static AjaxResult JFail(this AjaxResult ajaxResult, ResultCode code, string message = "")
        {
            ajaxResult.code = (int)code;
            ajaxResult.msg = string.IsNullOrEmpty(message) ? CodeMessage(code) : message;
            return ajaxResult;
        }

        /// <summary>
        /// 状态码中文提示
        /// </summary>
        /// <param name="ac"></param>
        /// <returns></returns>
        public static string CodeMessage(ResultCode ac)
        {
            string messgae = string.Empty;
            switch (ac)
            {
                case ResultCode.failed:
                    messgae = "请求失败！";
                    break;
                default:
                    break;
            }
            return messgae;
        }

        public static object JFail(string msg, int code = (int)ResultCode.exception, object data = null)
        {
            return new { code, msg, data };
        }

        public static object JOk(object data, string msg = "")
        {
            return new { data, msg, code = (int)ResultCode.success };
        }
    }

    public static class ResultHelper
    {
        private static readonly object locks = new object();
        private static AjaxResult ajaxObj = null;
        public static AjaxResult ajaxResult
        {
            get
            {
                return new AjaxResult(); // 此处可使用原型模式进行优化
            }
            set
            {
                ajaxObj = value;
            }
        }

        /// <summary>
        /// 状态描述
        /// </summary>
        /// <param name="rc"></param>
        /// <returns></returns>
        public static string ResultMsg(ResultCode rc)
        {
            string resultMsg = string.Empty;
            switch (rc)
            {
                case ResultCode.paramerror:
                    resultMsg = "参数错误！";
                    break;
                case ResultCode.success:
                    resultMsg = "请求成功！";
                    break;
                case ResultCode.noright:
                    resultMsg = "非法访问！";
                    break;
                case ResultCode.exception:
                    resultMsg = "请求异常！";
                    break;
                case ResultCode.unlogin:
                    resultMsg = "暂未登录！";
                    break;
                case ResultCode.verifycode:
                    resultMsg = "验证码错误！";
                    break;
                default:
                    break;
            }
            return resultMsg;
        }
    }

    /// <summary>
    /// 操作返回数据类型
    /// </summary>
    public enum ResultType
    {
        /// <summary>
        /// 消息结果类型
        /// </summary>
        info,
        /// <summary>
        /// 成功结果类型
        /// </summary>
        success,
        /// <summary>
        /// 警告结果类型
        /// </summary>
        warning,
        /// <summary>
        /// 异常结果类型
        /// </summary>
        error
    }

    public enum ResultCode
    {
        /// <summary>
        /// 请求成功
        /// </summary>
        success = 0,

        /// <summary>
        /// 异常
        /// </summary>
        exception = 1,

        /// <summary>
        /// 参数错误
        /// </summary>
        paramerror = 2,

        /// <summary>
        /// 验证码错误
        /// </summary>
        verifycode = 3,

        /// <summary>
        /// 未登录
        /// </summary>
        unlogin = 4,

        /// <summary>
        /// 无权限
        /// </summary>
        noright = 5,

        /// <summary>
        /// 没有数据
        /// </summary>
        notdata = 6,

        /// <summary>
        /// 已发送
        /// </summary>
        hasdone = 7,

        /// <summary>
        /// 失败
        /// </summary>
        failed = 8,

        /// <summary>
        /// 未找到匹配的身份信息
        /// </summary>
        nomatchidentity = 9,

        /// <summary>
        /// 数据不村存在
        /// </summary>
        notexist = 10,

        /// <summary>
        /// 账号不可用（状态异常）
        /// </summary>
        accountdisable = 11,
    }

}
