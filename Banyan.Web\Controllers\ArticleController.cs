﻿using Banyan.Apps;
using Banyan.Code;
using Banyan.Domain;
using Banyan.Web.Filters;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

namespace Banyan.Web.Controllers
{
    public class ExcelClass
    {
        public string Age { get; set; }

        public string Addr { get; set; }

        public string Name { get; set; }
    }

    public class ArticleController : BaseController
    {
        private NewsBLL newsBll = new NewsBLL();
        /// <summary>
        /// 文章列表
        /// </summary>
        /// <returns></returns>
        public ActionResult Articles()
        {
            var rolelist = new RoleBLL().GetList();
            ViewData["rolelist"] = rolelist;
            return View();
        }
        /// <summary>
        /// My文章列表
        /// </summary>
        /// <returns></returns>
        public ActionResult MyArticles()
        {
            var rolelist = new RoleBLL().GetList();
            ViewData["rolelist"] = rolelist;
            return View();
        }
        /// <summary>
        /// 添加/编辑文章
        /// </summary>
        /// <returns></returns>
        public ActionResult ArticleSet(int id = 0)
        {
            var artBll = new ArticleBLL();
            Article model = null;
            if (id > 0)
            {
                model = artBll.GetModel(id);
            }
            if (model == null)
            {
                model = new Article();
            }

            //var classList = new ClassifyBLL().GetTreeList($"Status={(int)ClassifyStatus.enable}", 0);
            //var colnumId = model.ColumnId > 0 ? model.ColumnId : (int)ColumnId.cases;
            var rolelist = new RoleBLL().GetList();
            ViewData["rolelist"] = rolelist;
            return View(model);
        }

        /// <summary>
        /// 评论列表
        /// </summary>
        /// <returns></returns>
        public ActionResult Comments()
        {
            return View();
        }

        /// <summary>
        /// 设置阅读角色
        /// </summary>
        /// <returns></returns>
        public ActionResult RoleSet(int id = 0)
        {
            var artBll = new ArticleBLL();
            Article model = null;
            if (id > 0)
            {
                model = artBll.GetModel(id);
            }
            if (model == null)
            {
                model = new Article();
            }

            ViewData["roleList"] = new RoleBLL().GetList();
            return View(model);
        }

        /// <summary>
        /// 预览
        /// </summary>
        /// <returns></returns>
        public ActionResult Preview(int id = 0)
        {
            ViewBag.id = id;
            return View();
        }

        public ActionResult ExportExcel()
        {
            List<ExcelClass> excelList = new List<ExcelClass>();
            excelList.Add(new ExcelClass()
            {
                Age = "18",
                Name = "Simonc",
                Addr = "Unkown",
            });
            string fileName = "测试EPPlus";
            string[] columnsName = { "姓名", "住址", "年龄" };
            string[] columns = { "Name", "Addr", "Age" };
            byte[] filecontent = ExcelHelper.ExportExcel<ExcelClass>(excelList, columnsName.ToList(), "", false, columns);
            return File(filecontent, ExcelHelper.ExcelContentType, $"{fileName}.xlsx");
        }

        public ActionResult News(int id = 0, string highlight = "")
        {
            if (id == 0)
            {
                return null;
            }
            ViewBag.id = id;
            ViewBag.model = newsBll.GetModel(loginUser, id);
            ViewBag.name = loginUser.RealName;
            ViewBag.imgWidth = "980px";
            ViewBag.highlight = highlight.Replace("(revisit)", "").Trim(); ;
            return View("~/Views/Login/News.cshtml");
        }
    }
}