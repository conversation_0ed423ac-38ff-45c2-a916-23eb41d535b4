﻿using System.Web.Routing;

namespace Banyan.Web
{
    public class CustomRouteUrl : Route
    {
        public CustomRouteUrl(string url, IRouteHandler routeHandler) : base(url, routeHandler)
        {
        }

        public CustomRouteUrl(string url, RouteValueDictionary defaults, IRouteHandler routeHandler) : base(url, defaults, routeHandler)
        {
        }

        public CustomRouteUrl(string url, RouteValueDictionary defaults, RouteValueDictionary constraints, IRouteHandler routeHandler) : base(url, defaults, constraints, routeHandler)
        {
        }

        public CustomRouteUrl(string url, RouteValueDictionary defaults, RouteValueDictionary constraints, RouteValueDictionary dataTokens, IRouteHandler routeHandler) : base(url, defaults, constraints, dataTokens, routeHandler)
        {
        }

        /// <summary>
        /// 自定义虚拟路由
        /// </summary>
        /// <param name="requestContext"></param>
        /// <param name="values"></param>
        /// <returns></returns>
        public override VirtualPathData GetVirtualPath(RequestContext requestContext, RouteValueDictionary values)
        {
            var routeValueDic = new RouteValueDictionary();

            foreach (var v in values)
            {
                switch (v.Key.ToUpperInvariant())
                {
                    case "ACTION":
                    case "AREA":
                    case "CONTROLLER":
                        routeValueDic.Add(v.Key, ((string)v.Value).ToLowerInvariant());
                        break;
                    default:
                        routeValueDic.Add(v.Key.ToLowerInvariant(), v.Value);
                        break;
                }
            }

            return base.GetVirtualPath(requestContext, values);
        }
    }
}