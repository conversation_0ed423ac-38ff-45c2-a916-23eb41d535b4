using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;
using Banyan.Apps;
using Banyan.Code;
using Banyan.Domain;

namespace Banyan.Web.Controllers
{
    /// <summary>
    /// Controller for managing email digest operations
    /// </summary>
    public class EmailDigestController : Controller
    {
        private readonly EmailDigestService _emailDigestService;
        private readonly MemberBLL _memberBLL;
        private readonly EmailDigestRecordsBLL _emailDigestRecordsBLL;

        /// <summary>
        /// Constructor
        /// </summary>
        public EmailDigestController()
        {
            _emailDigestService = new EmailDigestService();
            _memberBLL = new MemberBLL();
            _emailDigestRecordsBLL = new EmailDigestRecordsBLL();
        }

        /// <summary>
        /// Index page for email digest management
        /// </summary>
        /// <returns>View</returns>
        public ActionResult Index()
        {
            // Check if the current user has admin rights
            var currentUser = _memberBLL.GetLogOnUser();
            if (currentUser == null || (currentUser.Levels != (int)MemberLevels.Administrator && currentUser.Levels != (int)MemberLevels.SuperUser))
            {
                return RedirectToAction("NoPermission", "Error");
            }
            
            return View();
        }

        /// <summary>
        /// Generates and sends weekly digest emails to all users
        /// </summary>
        /// <returns>JSON result with number of emails sent</returns>
        [HttpPost]
        public async Task<JsonResult> GenerateAndSendWeeklyDigest()
        {
            var result = new AjaxResult();

            try
            {
                Logger.Info("Manual trigger of weekly email digest generation");
                
                // Check if the current user has admin rights
                var currentUser = _memberBLL.GetLogOnUser();
                if (currentUser == null || (currentUser.Levels != (int)MemberLevels.Administrator && currentUser.Levels != (int)MemberLevels.SuperUser))
                {
                    result.code = (int)ResultCode.noright;
                    result.msg = "您没有权限执行此操作";
                    return Json(result);
                }
                
                // Generate and send digests
                int sentCount = await _emailDigestService.GenerateAndSendWeeklyDigestAsync();
                
                result.code = (int)ResultCode.success;
                result.msg = $"成功发送 {sentCount} 封推荐邮件";
                result.data = sentCount;
                
                return Json(result);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in GenerateAndSendWeeklyDigest: {ex.Message}", ex);
                result.code = (int)ResultCode.failed;
                result.msg = $"发送推荐邮件失败: {ex.Message}";
                return Json(result);
            }
        }

        /// <summary>
        /// Sends a test digest email to a specific user
        /// </summary>
        /// <param name="userId">User ID to send test email to</param>
        /// <returns>JSON result indicating success or failure</returns>
        [HttpPost]
        public async Task<JsonResult> SendTestDigest(int userId)
        {
            var result = new AjaxResult();

            try
            {
                Logger.Info($"Sending test digest email to user {userId}");
                
                // Check if the current user has admin rights
                var currentUser = _memberBLL.GetLogOnUser();
                if (currentUser == null || (currentUser.Levels != (int)MemberLevels.Administrator && currentUser.Levels != (int)MemberLevels.SuperUser))
                {
                    result.code = (int)ResultCode.noright;
                    result.msg = "您没有权限执行此操作";
                    return Json(result);
                }
                
                // Get the target user
                var targetUser = _memberBLL.GetModelByCache(userId);
                if (targetUser == null)
                {
                    result.code = (int)ResultCode.failed;
                    result.msg = $"用户ID {userId} 不存在";
                    return Json(result);
                }
                
                // Check if the user has an email address
                if (string.IsNullOrEmpty(targetUser.Mail))
                {
                    result.code = (int)ResultCode.failed;
                    result.msg = $"用户 {targetUser.RealName} 没有邮箱地址";
                    return Json(result);
                }
                
                // Send test digest
                bool success = await _emailDigestService.GenerateAndSendDigestForUserAsync(
                    targetUser.Id, targetUser.RealName, targetUser.Mail);
                
                if (success)
                {
                    result.code = (int)ResultCode.success;
                    result.msg = $"成功发送测试邮件到 {targetUser.RealName} ({targetUser.Mail})";
                }
                else
                {
                    result.code = (int)ResultCode.failed;
                    result.msg = $"发送测试邮件到 {targetUser.RealName} ({targetUser.Mail}) 失败";
                }
                
                return Json(result);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in SendTestDigest for user {userId}: {ex.Message}", ex);
                result.code = (int)ResultCode.failed;
                result.msg = $"发送测试邮件失败: {ex.Message}";
                return Json(result);
            }
        }

        /// <summary>
        /// Gets the email digest history for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="limit">Maximum number of records to return</param>
        /// <returns>JSON result with digest history</returns>
        [HttpGet]
        public async Task<JsonResult> GetDigestHistory(int userId, int limit = 10)
        {
            var result = new AjaxResult();

            try
            {
                Logger.Info($"Getting digest history for user {userId}, limit {limit}");
                
                // Check if the current user has admin rights or is the target user
                var currentUser = _memberBLL.GetLogOnUser();
                if (currentUser == null || 
                    (currentUser.Id != userId && 
                     currentUser.Levels != (int)MemberLevels.Administrator && 
                     currentUser.Levels != (int)MemberLevels.SuperUser))
                {
                    result.code = (int)ResultCode.noright;
                    result.msg = "您没有权限查看此用户的邮件历史";
                    return Json(result, JsonRequestBehavior.AllowGet);
                }
                
                // Get digest history
                var history = await _emailDigestService.GetUserDigestHistoryAsync(userId, limit);
                
                // Enhance with news details
                var newsBLL = new NewsBLL();
                foreach (var digest in history)
                {
                    if (!string.IsNullOrEmpty(digest.NewsIds))
                    {
                        var newsIds = digest.NewsIds.Split(',')
                            .Select(id => int.TryParse(id, out int parsedId) ? parsedId : 0)
                            .Where(id => id > 0)
                            .ToList();
                        
                        // Get news details (limit to first 5 for performance)
                        var newsDetails = new List<object>();
                        foreach (var newsId in newsIds.Take(5))
                        {
                            var news = newsBLL.GetModel(newsId);
                            if (news != null)
                            {
                                newsDetails.Add(new
                                {
                                    Id = news.Id,
                                    Title = news.Title,
                                    Source = news.Source,
                                    PubTime = news.PubTime
                                });
                            }
                        }
                    }
                }
                
                result.code = (int)ResultCode.success;
                result.data = history;
                
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in GetDigestHistory for user {userId}: {ex.Message}", ex);
                result.code = (int)ResultCode.failed;
                result.msg = $"获取邮件历史失败: {ex.Message}";
                return Json(result, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// Schedules the weekly digest to run on a specific day and time
        /// </summary>
        /// <param name="dayOfWeek">Day of week (0-6, where 0 is Sunday)</param>
        /// <param name="hour">Hour of day (0-23)</param>
        /// <returns>JSON result indicating success or failure</returns>
        [HttpPost]
        public JsonResult ScheduleWeeklyDigest(int dayOfWeek, int hour)
        {
            var result = new AjaxResult();

            try
            {
                Logger.Info($"Scheduling weekly digest for day {dayOfWeek} at hour {hour}");
                
                // Check if the current user has admin rights
                var currentUser = _memberBLL.GetLogOnUser();
                if (currentUser == null || (currentUser.Levels != (int)MemberLevels.Administrator && currentUser.Levels != (int)MemberLevels.SuperUser))
                {
                    result.code = (int)ResultCode.noright;
                    result.msg = "您没有权限执行此操作";
                    return Json(result);
                }
                
                // Validate inputs
                if (dayOfWeek < 0 || dayOfWeek > 6)
                {
                    result.code = (int)ResultCode.failed;
                    result.msg = "星期几必须是0-6之间的数字（0表示星期日）";
                    return Json(result);
                }
                
                if (hour < 0 || hour > 23)
                {
                    result.code = (int)ResultCode.failed;
                    result.msg = "小时必须是0-23之间的数字";
                    return Json(result);
                }
                
                // Schedule the digest
                bool success = _emailDigestService.ScheduleWeeklyDigest((DayOfWeek)dayOfWeek, hour);
                
                if (success)
                {
                    result.code = (int)ResultCode.success;
                    result.msg = $"成功设置每周邮件推送时间为 {GetDayOfWeekName((DayOfWeek)dayOfWeek)} {hour}:00";
                }
                else
                {
                    result.code = (int)ResultCode.failed;
                    result.msg = "设置每周邮件推送时间失败";
                }
                
                return Json(result);
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in ScheduleWeeklyDigest: {ex.Message}", ex);
                result.code = (int)ResultCode.failed;
                result.msg = $"设置每周邮件推送时间失败: {ex.Message}";
                return Json(result);
            }
        }

        /// <summary>
        /// Gets the Chinese name for a day of the week
        /// </summary>
        /// <param name="dayOfWeek">Day of week</param>
        /// <returns>Chinese name</returns>
        private string GetDayOfWeekName(DayOfWeek dayOfWeek)
        {
            switch (dayOfWeek)
            {
                case DayOfWeek.Sunday: return "星期日";
                case DayOfWeek.Monday: return "星期一";
                case DayOfWeek.Tuesday: return "星期二";
                case DayOfWeek.Wednesday: return "星期三";
                case DayOfWeek.Thursday: return "星期四";
                case DayOfWeek.Friday: return "星期五";
                case DayOfWeek.Saturday: return "星期六";
                default: return dayOfWeek.ToString();
            }
        }
    }
}