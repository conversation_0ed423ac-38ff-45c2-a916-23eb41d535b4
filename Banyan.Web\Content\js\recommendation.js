/**
 * Recommendation System Client-Side Handling
 * 
 * This script provides client-side functionality for the recommendation system,
 * including dynamic loading of recommendations, click tracking, and UI interactions.
 */

// Recommendation module using IIFE pattern for encapsulation
var RecommendationSystem = (function () {
    // Private variables
    var _config = {
        defaultLimit: 10,
        loadMoreIncrement: 6,
        defaultThreshold: 0.4,
        defaultInterestRatio: 0.7,
        animationDuration: 300,
        trackingEnabled: true
    };
    
    var _cache = {
        recommendations: {},
        currentType: 'personalized',
        currentFilters: {},
        currentPage: 1,
        hasMoreItems: true,
        isLoading: false
    };
    
    var _endpoints = {
        personalized: '/AdminApi/GetNewsRecommendations',
        hybrid: '/AdminApi/GetNewsRecommendations',
        popular: '/Index/GetPopularNews',
        trackClick: '/NewsVectorSearch/RecordRead'
    };
    
    // Private methods
    
    /**
     * Initialize the recommendation system
     * @param {Object} options - Configuration options
     */
    function _init(options) {
        // Merge options with default config
        if (options) {
            _config = Object.assign(_config, options);
        }
        
        // Attach event handlers
        _attachEventHandlers();
        
        // Initial load
        _loadRecommendations();
    }
    
    /**
     * Attach event handlers to UI elements
     */
    function _attachEventHandlers() {
        // Filter form submission
        $('#filterForm').on('submit', function (e) {
            e.preventDefault();
            _resetPagination();
            _loadRecommendations();
        });
        
        // Reset filters
        $('#resetFilters').on('click', function () {
            $('#filterForm')[0].reset();
            _resetPagination();
            _loadRecommendations();
        });
        
        // Recommendation type buttons
        $('#personalizedBtn').on('click', function () {
            _setActiveButton($(this));
            _cache.currentType = 'personalized';
            _resetPagination();
            _loadRecommendations();
        });
        
        $('#hybridBtn').on('click', function () {
            _setActiveButton($(this));
            _cache.currentType = 'hybrid';
            _resetPagination();
            _loadRecommendations();
        });
        
        $('#popularBtn').on('click', function () {
            _setActiveButton($(this));
            _cache.currentType = 'popular';
            _resetPagination();
            _loadRecommendations();
        });
        
        // Load more button
        $('#loadMoreBtn').on('click', function () {
            _cache.currentPage++;
            _loadRecommendations(true);
        });
        
        // Global click handler for dynamically added recommendation links
        $(document).on('click', '.view-news', function (e) {
            var newsId = $(this).data('news-id');
            if (newsId && _config.trackingEnabled) {
                _trackClick(newsId);
            }
        });
    }
    
    /**
     * Set active button in button group
     * @param {jQuery} button - Button to set as active
     */
    function _setActiveButton(button) {
        $('.btn-group .btn').removeClass('btn-primary active').addClass('btn-outline-primary');
        button.removeClass('btn-outline-primary').addClass('btn-primary active');
    }
    
    /**
     * Reset pagination state
     */
    function _resetPagination() {
        _cache.currentPage = 1;
        _cache.hasMoreItems = true;
        $('#recommendationsList').empty();
        $('#loadMoreBtn').hide();
    }
    
    /**
     * Load recommendations from server
     * @param {boolean} append - Whether to append results or replace existing ones
     */
    function _loadRecommendations(append) {
        if (_cache.isLoading || !_cache.hasMoreItems) return;
        
        _cache.isLoading = true;
        _showLoading(true);
        _hideError();
        
        // Get filter values
        var filters = {
            limit: _config.defaultLimit,
            category: $('#category').val() || '',
            source: $('#source').val() || '',
            startDate: $('#startDate').val() || '',
            endDate: $('#endDate').val() || '',
            tag: $('#tag').val() || ''
        };
        
        // Store current filters
        _cache.currentFilters = filters;
        
        // Determine endpoint based on recommendation type
        var url;
        switch (_cache.currentType) {
            case 'personalized':
                url = _endpoints.personalized;
                filters.threshold = _config.defaultThreshold;
                break;
            case 'hybrid':
                url = _endpoints.hybrid;
                filters.interestRatio = _config.defaultInterestRatio;
                break;
            case 'popular':
                url = _endpoints.popular;
                break;
        }
        
        // Add page parameter for load more functionality
        if (append) {
            filters.page = _cache.currentPage;
        }
        
        // Determine request method and data format based on endpoint
        var requestConfig = {
            url: url,
            success: function (response) {
                _showLoading(false);
                _cache.isLoading = false;
                
                if (response.code === 0 && response.data) {
                    var recommendations = response.data.data || response.data; // Handle both formats

                    // Check if we have more items
                    _cache.hasMoreItems = recommendations.length === _config.defaultLimit;
                    $('#loadMoreBtn').toggle(_cache.hasMoreItems);
                    
                    // Render recommendations
                    if (!append) {
                        $('#recommendationsList').empty();
                    }
                    
                    if (recommendations.length === 0 && !append) {
                        _showNoResultsMessage();
                    } else {
                        _renderRecommendations(recommendations, append);
                    }
                    
                    // Cache the results
                    var cacheKey = _getCacheKey();
                    _cache.recommendations[cacheKey] = recommendations;
                } else {
                    _showError(response.msg || '获取推荐失败');
                }
            },
            error: function (xhr, status, error) {
                _showLoading(false);
                _cache.isLoading = false;
                _showError('获取推荐时发生错误: ' + error);
            }
        };

        // Configure request based on endpoint type
        if (url.includes('/AdminApi/') || url.includes('/Api/')) {
            // Use POST for API endpoints
            requestConfig.type = 'POST';
            requestConfig.contentType = 'application/json';
            requestConfig.data = JSON.stringify(filters);
        } else {
            // Use GET for other endpoints
            requestConfig.type = 'GET';
            requestConfig.data = filters;
        }

        $.ajax(requestConfig);
    }
    
    /**
     * Render recommendations to the UI
     * @param {Array} recommendations - List of recommendation objects
     * @param {boolean} append - Whether to append or replace existing content
     */
    function _renderRecommendations(recommendations, append) {
        var template = document.getElementById('newsCardTemplate');
        var container = document.getElementById('recommendationsList');
        
        // If not appending, clear the container first
        if (!append) {
            $(container).empty();
        }
        
        // Create a document fragment for better performance
        var fragment = document.createDocumentFragment();
        
        recommendations.forEach(function (news) {
            var clone = document.importNode(template.content, true);

            // Set news data - use lowercase field names from API response
            clone.querySelector('.news-title').textContent = news.title || news.Title || '无标题';
            clone.querySelector('.news-source').textContent = news.source || news.Source || '未知来源';

            // Use subject field from API response, fallback to content if needed
            var summary = news.subject || news.Subject || news.content || news.Content || '';
            if (summary && summary.length > 100) {
                summary = summary.substring(0, 100) + '...';
            }
            clone.querySelector('.news-summary').textContent = summary || '无内容摘要';

            // Format date - handle both API response format and legacy format
            var pubDate;
            if (news.publishTime) {
                // API response format: "2024-01-15 10:30"
                pubDate = new Date(news.publishTime);
            } else if (news.PubTime) {
                // Legacy format: "/Date(1642204800000)/"
                pubDate = new Date(parseInt(news.PubTime.substr(6)));
            } else {
                pubDate = new Date();
            }
            clone.querySelector('.news-date').textContent = pubDate.toLocaleDateString();

            // Set category
            clone.querySelector('.news-category').textContent = news.category || news.Classify || '未分类';

            // Set relevance score if available - handle both API response and legacy formats
            var score = news.finalScore || news.similarity || news.MatchScore;
            if (score !== undefined) {
                var scorePercent = Math.round(score * 100);
                var progressBar = clone.querySelector('.progress-bar');
                progressBar.style.width = scorePercent + '%';
                progressBar.setAttribute('aria-valuenow', scorePercent);
                clone.querySelector('.relevance-text').textContent = '相关度: ' + scorePercent + '%';
            } else {
                clone.querySelector('.relevance-score').style.display = 'none';
            }

            // Set view link with click tracking
            var viewLink = clone.querySelector('.view-news');
            var newsId = news.id || news.Id;
            viewLink.href = '/Recommendation/ViewNews/' + newsId;
            viewLink.setAttribute('data-news-id', newsId);

            // Add to fragment
            fragment.appendChild(clone);
        });
        
        // Add all cards to the container at once
        container.appendChild(fragment);
        
        // Animate new cards
        if (append) {
            var newCards = $('#recommendationsList .news-card').slice(-recommendations.length);
            newCards.hide().fadeIn(_config.animationDuration);
        } else {
            $('#recommendationsList .news-card').hide().fadeIn(_config.animationDuration);
        }
    }
    
    /**
     * Track news click
     * @param {number} newsId - ID of the clicked news
     */
    function _trackClick(newsId) {
        $.ajax({
            url: _endpoints.trackClick,
            type: 'POST',
            data: {
                newsId: newsId,
                source: 'web'
            },
            success: function (response) {
                console.log('Click tracked:', response);
            },
            error: function (xhr, status, error) {
                console.error('Error tracking click:', error);
            }
        });
    }
    
    /**
     * Generate a cache key based on current filters and type
     * @returns {string} Cache key
     */
    function _getCacheKey() {
        var filters = _cache.currentFilters;
        return _cache.currentType + '_' + 
               filters.category + '_' + 
               filters.source + '_' + 
               filters.startDate + '_' + 
               filters.endDate + '_' + 
               filters.tag + '_' + 
               _cache.currentPage;
    }
    
    /**
     * Show loading indicator
     * @param {boolean} show - Whether to show or hide the indicator
     */
    function _showLoading(show) {
        $('#loadingIndicator').toggle(show);
    }
    
    /**
     * Show error message
     * @param {string} message - Error message to display
     */
    function _showError(message) {
        $('#errorMessage').text(message).show();
    }
    
    /**
     * Hide error message
     */
    function _hideError() {
        $('#errorMessage').hide();
    }
    
    /**
     * Show no results message
     */
    function _showNoResultsMessage() {
        $('#recommendationsList').html('<div class="col-12 text-center"><p>没有找到符合条件的推荐内容</p></div>');
    }
    
    // Public API
    return {
        init: _init,
        loadRecommendations: function(append) {
            _loadRecommendations(append);
        },
        setRecommendationType: function(type) {
            if (['personalized', 'hybrid', 'popular'].indexOf(type) !== -1) {
                _cache.currentType = type;
                var buttonId = type + 'Btn';
                _setActiveButton($('#' + buttonId));
                _resetPagination();
                _loadRecommendations();
            }
        },
        trackClick: _trackClick
    };
})();

/**
 * Recommendation Widget Module
 * For embedding recommendation widgets in other pages
 */
var RecommendationWidget = (function () {
    // Private variables
    var _widgets = {};
    
    /**
     * Initialize a recommendation widget
     * @param {string} widgetId - ID of the widget container
     * @param {Object} options - Widget configuration options
     */
    function _initWidget(widgetId, options) {
        var defaultOptions = {
            limit: 3,
            type: 'personalized', // personalized, hybrid, or popular
            interestRatio: 0.7,
            threshold: 0.4,
            category: '',
            source: '',
            autoRefresh: false,
            refreshInterval: 300000, // 5 minutes
            trackClicks: true
        };
        
        // Merge options
        var config = Object.assign({}, defaultOptions, options);
        
        // Store widget configuration
        _widgets[widgetId] = {
            config: config,
            refreshTimer: null
        };
        
        // Load initial recommendations
        _loadWidgetRecommendations(widgetId);
        
        // Set up auto-refresh if enabled
        if (config.autoRefresh) {
            _widgets[widgetId].refreshTimer = setInterval(function() {
                _loadWidgetRecommendations(widgetId);
            }, config.refreshInterval);
        }
        
        // Set up click tracking
        if (config.trackClicks) {
            $('#' + widgetId).on('click', '.recommendation-link', function(e) {
                var newsId = $(this).data('news-id');
                _trackWidgetClick(newsId, widgetId);
            });
        }
    }
    
    /**
     * Load recommendations for a widget
     * @param {string} widgetId - ID of the widget container
     */
    function _loadWidgetRecommendations(widgetId) {
        var widget = _widgets[widgetId];
        if (!widget) return;
        
        var config = widget.config;
        var $widget = $('#' + widgetId);
        
        // Show loading state
        $widget.addClass('loading');
        
        // Determine endpoint based on recommendation type
        var url;
        var data = {
            limit: config.limit,
            category: config.category,
            source: config.source
        };
        
        switch (config.type) {
            case 'personalized':
                url = '/AdminApi/GetNewsRecommendations';
                data.threshold = config.threshold;
                break;
            case 'hybrid':
                url = '/AdminApi/GetNewsRecommendations';
                data.interestRatio = config.interestRatio;
                break;
            case 'popular':
                url = '/Index/GetPopularNews';
                break;
        }
        
        // Configure request based on endpoint type
        var requestConfig = {
            url: url,
            success: function(response) {
                $widget.removeClass('loading');

                if (response.code === 0 && response.data) {
                    var recommendations = response.data.data || response.data; // Handle both formats
                    _renderWidgetRecommendations(widgetId, recommendations);
                } else {
                    _showWidgetError(widgetId, response.msg || '获取推荐失败');
                }
            },
            error: function(xhr, status, error) {
                $widget.removeClass('loading');
                _showWidgetError(widgetId, '获取推荐时发生错误');
            }
        };

        if (url.includes('/AdminApi/') || url.includes('/Api/')) {
            // Use POST for API endpoints
            requestConfig.type = 'POST';
            requestConfig.contentType = 'application/json';
            requestConfig.data = JSON.stringify(data);
        } else {
            // Use GET for other endpoints
            requestConfig.type = 'GET';
            requestConfig.data = data;
        }

        $.ajax(requestConfig);
    }
    
    /**
     * Render recommendations in a widget
     * @param {string} widgetId - ID of the widget container
     * @param {Array} recommendations - List of recommendation objects
     */
    function _renderWidgetRecommendations(widgetId, recommendations) {
        var widget = _widgets[widgetId];
        var $container = $('#' + widgetId + ' .recommendation-list');
        
        if (!$container.length || !recommendations.length) {
            return;
        }
        
        // Clear existing content
        $container.empty();
        
        // Limit the number of recommendations
        var limit = widget.config.limit;
        var items = recommendations.slice(0, limit);
        
        // Create recommendation items
        items.forEach(function(news) {
            var $item = $('<div class="recommendation-item"></div>');

            // Format date - handle both API response format and legacy format
            var pubDate;
            if (news.publishTime) {
                // API response format: "2024-01-15 10:30"
                pubDate = new Date(news.publishTime);
            } else if (news.PubTime) {
                // Legacy format: "/Date(1642204800000)/"
                pubDate = new Date(parseInt(news.PubTime.substr(6)));
            } else {
                pubDate = new Date();
            }
            var formattedDate = pubDate.toLocaleDateString();

            // Get field values with fallbacks for both API response and legacy formats
            var newsId = news.id || news.Id;
            var title = news.title || news.Title || '无标题';
            var source = news.source || news.Source || '未知来源';

            // Build item content
            $item.html(`
                <div class="recommendation-content">
                    <h6 class="recommendation-title">
                        <a href="/Recommendation/ViewNews/${newsId}" class="recommendation-link" data-news-id="${newsId}">
                            ${title}
                        </a>
                    </h6>
                    <div class="recommendation-meta">
                        <span class="recommendation-source">${source}</span>
                        <span class="recommendation-date">${formattedDate}</span>
                    </div>
                </div>
            `);

            // Add relevance score if available - handle both API response and legacy formats
            var score = news.finalScore || news.similarity || news.MatchScore;
            if (score !== undefined) {
                var scorePercent = Math.round(score * 100);
                var $score = $(`
                    <div class="recommendation-score">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar"
                                 style="width: ${scorePercent}%"
                                 aria-valuenow="${scorePercent}"
                                 aria-valuemin="0"
                                 aria-valuemax="100"></div>
                        </div>
                    </div>
                `);

                $item.find('.recommendation-content').append($score);
            }

            // Add to container
            $container.append($item);
        });
        
        // Show "more" link if there are more recommendations than the limit
        var $footer = $('#' + widgetId + ' .widget-footer');
        if (recommendations.length > limit) {
            if (!$footer.length) {
                $footer = $('<div class="widget-footer"></div>');
                $('#' + widgetId).append($footer);
            }
            $footer.html('<a href="/Recommendation" class="more-link">查看更多推荐</a>');
        } else {
            $footer.remove();
        }
    }
    
    /**
     * Show error message in widget
     * @param {string} widgetId - ID of the widget container
     * @param {string} message - Error message to display
     */
    function _showWidgetError(widgetId, message) {
        var $container = $('#' + widgetId + ' .recommendation-list');
        if (!$container.length) return;
        
        $container.html(`
            <div class="recommendation-error">
                <p>${message}</p>
            </div>
        `);
    }
    
    /**
     * Track click on recommendation in widget
     * @param {number} newsId - ID of the clicked news
     * @param {string} widgetId - ID of the widget container
     */
    function _trackWidgetClick(newsId, widgetId) {
        $.ajax({
            url: '/NewsVectorSearch/RecordRead',
            type: 'POST',
            data: {
                newsId: newsId,
                source: 'widget_' + widgetId
            },
            success: function(response) {
                console.log('Widget click tracked:', response);
            },
            error: function(xhr, status, error) {
                console.error('Error tracking widget click:', error);
            }
        });
    }
    
    /**
     * Destroy a widget and clean up resources
     * @param {string} widgetId - ID of the widget to destroy
     */
    function _destroyWidget(widgetId) {
        var widget = _widgets[widgetId];
        if (!widget) return;
        
        // Clear refresh timer if exists
        if (widget.refreshTimer) {
            clearInterval(widget.refreshTimer);
        }
        
        // Remove click handlers
        $('#' + widgetId).off('click', '.recommendation-link');
        
        // Remove from widgets collection
        delete _widgets[widgetId];
    }
    
    // Public API
    return {
        init: _initWidget,
        refresh: _loadWidgetRecommendations,
        destroy: _destroyWidget
    };
})();