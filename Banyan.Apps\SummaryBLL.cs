﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.Linq;

namespace Banyan.Apps
{
    [Serializable]
    class ProjectMini
    {

        public int Id { get; set; }


        public int Creator { get; set; }


        public string Name { get; set; } = string.Empty;


        public string Participant { get; set; } = string.Empty;


        public string InteralPTCP { get; set; } = string.Empty;

        /// 0：主动覆盖人脉
        /// 1：同事提及
        /// 2：同事介绍
        /// 3：FA介绍
        /// 4：主动覆盖研究
        public string Source { get; set; } = string.Empty;


        public string NextStep { get; set; } = string.Empty;


        public string ProjectManager { get; set; } = string.Empty;


        public int ToRoleId { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        public string RoleName { get; set; } = string.Empty;



        public DateTime PubTime { get; set; } = DateTime.Now;


        /// 是否允许操作
        /// </summary>
        public bool IsOperate { get; set; } = false;


    }
    [Serializable]
    public class style
    {
        public string color;
        int r = 250;
        int g = 142;
        int b = 0;
        public style(int idx, int depth)
        {
            this.color = $"rgb({r - idx * 15}, {g - idx * 1 + depth * 17}, {b + idx * 1 + depth * 25})";
        }
    }
    [Serializable]
    public class Node
    {
        public string name { get; set; } = string.Empty;
        public int value { get; set; }
        public style itemStyle { get; set; }

        public string link { get; set; }

        public List<Node> children { get; set; } = new List<Node>();

        [NonSerialized]
        public List<Project> p = new List<Project>();

    }
    public class SummaryBLL
    {
        private readonly AjaxResult ajaxResult = null;
        private ProjectBLL projectbll = new ProjectBLL();
        public SummaryBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }


        public List<Node> convertToNodeLink(List<Project> projectlist)
        {
            return getChildren(projectlist, "ProjectManager", "nextStepStatus", "Name");

        }
        public Dictionary<string, Node> convertProjectListToNodeDic(List<Project> list, string attr, int depth)
        {
            var newdic = new Dictionary<string, Node>();
            list.Select((project, idx) =>
            {
                string key = (string)project.GetType().GetProperty(attr).GetValue(project);
                if (newdic.ContainsKey(key))
                {
                    newdic[key].p.Add(project);
                }
                else
                {
                    var tmpNode = new Node();
                    tmpNode.name = key;
                    tmpNode.itemStyle = new style(newdic.Count, depth);
                    tmpNode.p.Add(project);
                    newdic.Add(key, tmpNode);
                }
                return key;
            }).ToList();
            return newdic;
        }
        public decimal calcPercent(int x, int all)
        {
            decimal A = (decimal)x * 100;
            decimal B = (decimal)all;

            decimal t = decimal.Parse((A / B).ToString("0.000"));
            return Math.Round(t, 2);
        }

        public List<Node> getChildren(List<Project> list, string attr, string attr2, string attr3)
        {
            var dic = convertProjectListToNodeDic(list, attr, 1);
            return dic.Values.Select(node =>
            {
                var newdic = convertProjectListToNodeDic(node.p, attr2, 2);
                var sumcount = newdic.Values.Aggregate(0, (sumtmp, statusNodes) =>
                    sumtmp + statusNodes.p.Count
                );
                node.children = newdic.Values.Select(v =>
                {
                    var newdic2 = convertProjectListToNodeDic(v.p, attr3, 3);
                    v.children = newdic2.Values.Select(v2 =>
                    {
                        v2.children = null;
                        v2.value = 1;
                        v2.link = "/index/preview?id=" + v2.p[0].Id;
                        v2.p = null;
                        return v2;
                    }).ToList();
                    v.value = v.children.Count <= 0 ? 1 : v.children.Count;
                    var percent = calcPercent(v.value, sumcount);
                    v.name = $"{v.name}({v.value}, 占比{percent}%)";
                    v.p = null;
                    return v;
                }).ToList();
                node.p = null;
                if (node.children.Count > 0)
                {
                    node.value = node.children.Aggregate(0, (sum, val) => sum + val.value);
                }
                else
                {
                    node.value = 0;
                }
                var t1 = calcPercent(node.value, list.Count);

                node.name = $"{node.name}({node.value}, 占比{t1}%)";
                return node;
            }).ToList();
        }

        public List<Node> convertToNode(List<Project> projectlist)
        {
            var d = new Dictionary<string, Node>();
            projectlist.Select((val, idx) =>
            {
                if (d.ContainsKey(val.ProjectManager))
                {
                    d[val.ProjectManager].p.Add(val);
                }
                else
                {
                    var tmpNode = new Node();
                    tmpNode.name = val.ProjectManager;
                    tmpNode.itemStyle = new style(idx, 1);
                    tmpNode.p.Add(val);
                    d.Add(val.ProjectManager, tmpNode);
                }
                return val;
            }).ToList();
            var res = d.Values.Select(v =>
            {
                var statusDic = new Dictionary<string, Node>();
                v.p.Select((v1, idx) =>
                {
                    if (statusDic.ContainsKey(v1.nextStepStatus))
                    {
                        statusDic[v1.nextStepStatus].p.Add(v1);
                    }
                    else
                    {
                        var tmpNode = new Node();
                        tmpNode.name = v1.nextStepStatus;
                        tmpNode.itemStyle = new style(idx, 2);
                        tmpNode.p.Add(v1);
                        statusDic.Add(v1.nextStepStatus, tmpNode);
                    }
                    return v1;
                }).ToList();
                v.children = statusDic.Values.Select(v2 =>
                {
                    v2.children = v2.p.Select((v3, idx) =>
                    {
                        var tmpNode = new Node();
                        tmpNode.name = v3.Name;
                        tmpNode.value = 1;
                        tmpNode.itemStyle = new style(idx, 3);
                        return tmpNode;
                    }).ToList();
                    v2.value = v2.children.Count;
                    v2.p = null;
                    v2.name = $"{v2.name}({v2.children.Count})";
                    v2.children = null;
                    return v2;
                }).ToList();
                v.p = null;
                if (v.children.Count > 0)
                {
                    v.value = v.children.Aggregate(0, (sum, val) => sum + val.value);
                }
                else
                {
                    v.value = 0;
                }
                decimal A = (decimal)v.value * 100;
                decimal B = (decimal)projectlist.Count;

                decimal t = decimal.Parse((A / B).ToString("0.000"));
                var t1 = Math.Round(t, 2);

                v.name = $"{v.name}({v.value}, {t1}%)";
                return v;
            }).ToList();
            return res;

        }
        public List<Node> convertToMonth(List<Project> projectlist)
        {
            var monthDic = new Dictionary<string, Dictionary<string, int>>();
            var res = new Dictionary<string, List<Node>>();
            projectlist.ForEach(p =>
            {
              
                if (!monthDic.ContainsKey(p.PubTimeStr))
                {
                    var tmpdic = new Dictionary<string, int> { { p.Source, 1 } };
                    monthDic.Add(p.PubTimeStr, tmpdic);
                }
                else
                {
                    var tmpdic = monthDic[p.PubTimeStr];
                    if (!tmpdic.ContainsKey(p.Source))
                    {
                        tmpdic.Add(p.Source, 1);
                    }
                    else
                    {
                        tmpdic[p.Source] += 1;
                    }
                }
            });
            return monthDic.Select(val =>
            {
                var tmpnode = new Node();
                tmpnode.name = val.Key; // month
                tmpnode.children = val.Value.Select(v =>
                {
                    var projectnode = new Node();
                    projectnode.name = v.Key; // project status
                    projectnode.value = v.Value; // count
                    return projectnode;
                }).ToList();
                return tmpnode;
            }).ToList();
        }
        public AjaxResult GetData(NameValueCollection paramValues)
        {
            Member user = new MemberBLL().GetLogOnUser();
            int pageIndex = 1;
            int pageSize = int.MaxValue;
            int count;
            var ProjectList = projectbll.searchCommon(paramValues, user, pageIndex, pageSize, out count, false, true);

            try
            {
                var res = convertToNode(ProjectList);
                var res2 = convertToMonth(ProjectList);
                ajaxResult.data = new Dictionary<string, List<Node>> { { "node", res }, { "month", res2 } };
                ajaxResult.code = (int)ResultCode.success;
            }
            catch (Exception e)
            {
                Logger.Error(e.Message, e, user.RealName);
                ajaxResult.msg = e.Message;
                ajaxResult.code = (int)ResultCode.exception;
            }

            return ajaxResult;
        }

        public AjaxResult GetDataLink(NameValueCollection paramValues)
        {
            Member user = new MemberBLL().GetLogOnUser();
            int pageIndex = 1;
            int pageSize = int.MaxValue;
            int count;
            var ProjectList = projectbll.searchCommon(paramValues, user, pageIndex, pageSize, out count, false, true);

            try
            {
                var res = convertToNodeLink(ProjectList);
                var res2 = convertToMonth(ProjectList);
                ajaxResult.data = new Dictionary<string, List<Node>> { { "node", res }, { "month", res2 } };
                ajaxResult.code = (int)ResultCode.success;
            }
            catch (Exception e)
            {
                Logger.Error(e.Message, e, user.RealName);
                ajaxResult.msg = e.Message;
                ajaxResult.code = (int)ResultCode.exception;
            }

            return ajaxResult;
        }

    }
}
