﻿using Banyan.Apps;
using Banyan.Domain;
using Banyan.Web.Filters;
using System.Web.Mvc;
using Newtonsoft.Json;


namespace Banyan.Web.Controllers
{
    public class UserController : BaseController
    {
        /// <summary>
        /// 用户列表
        /// </summary>
        /// <returns></returns>
        public ActionResult Users()
        {
            ViewData["rolelist"] = new RoleBLL().GetList();
            return View();
        }
        public ActionResult UsersResearch()
        {
            ViewData["rolelist"] = new RoleBLL().GetList();
            return View();
        }
        /// <summary>
        /// 用户新增、编辑
        /// </summary>
        /// <returns></returns>
        public ActionResult UserSet(string data)
        {
            //var memberBll = new MemberBLL();
            //Member model = null;
            //if (id > 0)
            //{
            //    model = memberBll.GetModelByCache(id);
            //}
            //if (model == null)
            //{
            //    model = new Member();
            //}
            string user = System.Web.HttpUtility.UrlDecode(data, System.Text.Encoding.GetEncoding("GB2312"));
            Member model =  JsonConvert.DeserializeObject<Member>(user);

            ViewData["rolelist"] = new RoleBLL().GetList();
            ViewData["model"] = data;
            return View(model);
        }

        /// <summary>
        /// 管理员列表
        /// </summary>
        /// <returns></returns>
        public ActionResult Managers()
        {
            return View();
        }

        /// <summary>
        /// 管理员添加、编辑
        /// </summary>
        /// <returns></returns>
        public ActionResult ManagerSet()
        {
            return View();
        }
        public ActionResult AnnualReportSetInvest(int id = 0)
        {
            ViewData["id"] = id;
            return View();
        }
        public ActionResult AnnualReportPreview(int id = 0)
        {
            ViewData["id"] = id;
            return View();
        }
        public ActionResult AnnualReport()
        {
            var yearList = new System.Collections.Generic.List<int>() { };
            for (int i = System.DateTime.Now.Year; i >= 2023; i--)
            {
                yearList.Add(i);
            }
            ViewData["yearList"] = yearList;

            var rolelist = new RoleBLL().GetList();
            ViewData["rolelist"] = rolelist;
            ViewData["creatorlist"] = new MemberBLL().GetAllList();
            return View();
        }
    }
}