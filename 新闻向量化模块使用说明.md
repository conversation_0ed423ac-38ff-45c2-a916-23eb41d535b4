# 新闻向量化模块使用说明

## 一、功能概述

新闻向量化模块是智能新闻推荐系统的核心组件，负责将新闻内容转换为数值向量，为推荐引擎提供高质量的向量数据。

### 主要功能
- **AI分析**：使用大模型分析新闻内容，提取标签和权重
- **向量化处理**：将新闻内容转换为1024维向量
- **批量处理**：支持批量向量化处理，提高效率
- **定时任务**：自动扫描并向量化未处理的新闻
- **管理界面**：提供可视化的管理界面

## 二、数据模型更新

### 2.1 News表字段扩展
在原有的News表中添加了以下字段：

```sql
-- 新闻向量（1024维，逗号分隔的浮点数字符串）
NewsVector NVARCHAR(MAX)

-- 向量更新时间
VectorUpdateTime DATETIME

-- 标签分析结果（JSON格式）
TagAnalysis NVARCHAR(MAX)

-- 向量化状态（0：未向量化，1：已向量化，2：向量化失败）
VectorStatus INT

-- 向量化错误信息
VectorError NVARCHAR(500)
```

### 2.2 向量化状态说明
- **0 - 未向量化**：新闻尚未进行向量化处理
- **1 - 已向量化**：新闻已成功向量化
- **2 - 向量化失败**：向量化处理失败，需要重新处理

## 三、核心组件

### 3.1 NewsVectorizationService
新闻向量化服务类，负责核心的向量化处理逻辑。

**主要方法：**
- `ScanAndVectorizeNewsAsync()` - 扫描并向量化新闻
- `VectorizeSingleNewsAsync()` - 向量化单篇新闻
- `GetNewsVectorAsync()` - 获取新闻向量
- `GetNewsVectorsBatchAsync()` - 批量获取新闻向量

### 3.2 NewsVectorizationScheduler
定时任务调度器，定期扫描并处理未向量化的新闻。

**配置参数：**
- 扫描间隔：30分钟
- 批量大小：100篇新闻
- 最大重试次数：3次

### 3.3 NewsVectorizationController
API控制器，提供向量化相关的Web接口。

**主要接口：**
- `GetVectorizationStats()` - 获取向量化统计信息
- `TriggerVectorization()` - 手动触发向量化
- `GetFailedVectorizationNews()` - 获取失败新闻列表
- `GetPendingVectorizationNews()` - 获取待向量化新闻列表

## 四、使用流程

### 4.1 自动向量化流程
1. **系统启动**：应用启动时自动启动向量化定时任务
2. **定期扫描**：每30分钟扫描一次未向量化的新闻
3. **批量处理**：每次处理100篇新闻，避免API限流
4. **状态更新**：更新新闻的向量化状态和错误信息

### 4.2 手动向量化流程
1. **访问管理页面**：`/NewsVectorization/Index`
2. **查看统计信息**：了解当前向量化进度
3. **手动触发**：点击"手动触发向量化"按钮
4. **监控进度**：查看处理结果和错误信息

### 4.3 向量化处理流程
1. **AI分析**：使用大模型分析新闻内容，提取标签
2. **标签权重**：计算主要标签、次要标签、语义关键词的权重
3. **向量生成**：基于标签向量生成新闻向量
4. **数据存储**：将向量和分析结果存储到数据库

## 五、API接口说明

### 5.1 获取向量化统计
```
POST /NewsVectorization/GetVectorizationStats
```

**返回数据：**
```json
{
    "code": 0,
    "data": {
        "TotalNews": 1000,
        "VectorizedNews": 800,
        "PendingNews": 150,
        "FailedNews": 50,
        "VectorizationRate": 80.0
    }
}
```

### 5.2 手动触发向量化
```
POST /NewsVectorization/TriggerVectorization
参数：batchSize=100
```

**返回数据：**
```json
{
    "code": 0,
    "msg": "开始向量化处理..."
}
```

### 5.3 获取失败新闻列表
```
POST /NewsVectorization/GetFailedVectorizationNews
参数：pageIndex=1, pageSize=10
```

**返回数据：**
```json
{
    "code": 0,
    "data": {
        "List": [...],
        "TotalCount": 50,
        "PageSize": 10,
        "PageIndex": 1
    }
}
```

## 六、配置说明

### 6.1 向量化配置
```csharp
// 向量维度
private const int VECTOR_DIMENSION = 1024;

// 缓存时间
private const int VECTOR_CACHE_DAYS = 30;

// 批量处理大小
private const int BATCH_SIZE = 100;

// 扫描间隔（分钟）
private const int SCAN_INTERVAL_MINUTES = 30;
```

### 6.2 AI服务配置
```csharp
// AI模型名称
private const string AI_MODEL_NAME = "deepseek-r1-0528";

// Embedding模型名称
private const string EMBEDDING_MODEL_NAME = "text-embedding-bge-m3";

// 超时时间
private const int AI_TIMEOUT = 60;
private const int EMBEDDING_TIMEOUT = 30;
```

## 七、监控和维护

### 7.1 关键指标监控
- **向量化覆盖率**：已向量化的新闻比例
- **处理成功率**：向量化成功的比例
- **处理延迟**：向量化处理的响应时间
- **错误率**：向量化失败的比例

### 7.2 常见问题处理

#### 问题1：向量化失败率高
**可能原因：**
- AI服务不可用
- Embedding服务异常
- 网络连接问题

**解决方案：**
- 检查AI服务状态
- 查看错误日志
- 重新触发向量化

#### 问题2：处理速度慢
**可能原因：**
- API调用频率限制
- 批量大小设置过大
- 网络延迟高

**解决方案：**
- 调整批量大小
- 增加处理间隔
- 优化网络配置

#### 问题3：向量质量差
**可能原因：**
- AI分析不准确
- 标签提取错误
- 向量维度不匹配

**解决方案：**
- 优化AI提示词
- 调整标签权重算法
- 检查向量维度配置

## 八、部署说明

### 8.1 数据库更新
需要执行以下SQL语句更新News表结构：

```sql
-- 添加向量化相关字段
ALTER TABLE News ADD NewsVector NVARCHAR(MAX);
ALTER TABLE News ADD VectorUpdateTime DATETIME DEFAULT GETDATE();
ALTER TABLE News ADD TagAnalysis NVARCHAR(MAX);
ALTER TABLE News ADD VectorStatus INT DEFAULT 0;
ALTER TABLE News ADD VectorError NVARCHAR(500);

-- 创建索引
CREATE INDEX IX_News_VectorStatus ON News(VectorStatus);
CREATE INDEX IX_News_VectorUpdateTime ON News(VectorUpdateTime);
```

### 8.2 代码部署
1. 部署更新后的代码文件
2. 重启Web应用程序
3. 检查定时任务是否正常启动
4. 验证API接口是否可用

### 8.3 初始向量化
1. 访问管理页面：`/NewsVectorization/Index`
2. 查看当前向量化状态
3. 手动触发批量向量化
4. 监控处理进度和结果

## 九、性能优化建议

### 9.1 批量处理优化
- 根据API限制调整批量大小
- 合理设置处理间隔
- 使用异步处理避免阻塞

### 9.2 缓存优化
- 缓存AI分析结果
- 缓存Embedding向量
- 设置合理的缓存过期时间

### 9.3 错误处理优化
- 实现重试机制
- 记录详细错误信息
- 提供降级处理方案

## 十、扩展功能

### 10.1 支持的功能扩展
- 多语言新闻向量化
- 实时向量化处理
- 向量压缩存储
- 向量索引查询

### 10.2 监控功能扩展
- 实时监控面板
- 性能指标统计
- 告警通知机制
- 日志分析工具

## 十一、注意事项

### 11.1 数据安全
- 向量数据包含敏感信息，需要妥善保护
- 定期备份向量数据
- 实现数据加密存储

### 11.2 性能考虑
- 向量数据占用较大存储空间
- 向量计算消耗较多CPU资源
- 需要合理配置服务器资源

### 11.3 维护建议
- 定期清理过期的向量数据
- 监控系统资源使用情况
- 及时处理向量化失败的情况 