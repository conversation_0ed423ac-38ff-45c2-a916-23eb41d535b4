﻿namespace Banyan.Apps
{
    #region 数据模型

    /// <summary>
    /// 新闻标签
    /// </summary>
    public class NewsTag
    {
        /// <summary>
        /// 标签名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 标签权重
        /// </summary>
        public double Weight { get; set; }

        /// <summary>
        /// 标签分类
        /// </summary>
        public string Category { get; set; }
    }
}
#endregion