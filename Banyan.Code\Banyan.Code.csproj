﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A2469FA5-EE0A-40DF-BA22-2ED091B6E251}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Banyan.Code</RootNamespace>
    <AssemblyName>Banyan.Code</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <AutoGenerateBindingRedirects>false</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="aliyun-net-sdk-core">
      <HintPath>..\lib\aliyun-net-sdk-core.dll</HintPath>
    </Reference>
    <Reference Include="aliyun-net-sdk-dysmsapi">
      <HintPath>..\lib\aliyun-net-sdk-dysmsapi.dll</HintPath>
    </Reference>
    <Reference Include="Aspose.Pdf">
      <HintPath>..\lib\Aspose.Pdf.dll</HintPath>
    </Reference>
    <Reference Include="Aspose.Slides">
      <HintPath>..\lib\Aspose.Slides.dll</HintPath>
    </Reference>
    <Reference Include="Aspose.Words">
      <HintPath>..\lib\Aspose.Words.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=1.8.8.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\Portable.BouncyCastle.1.8.8\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus, Version=4.5.2.1, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.4.5.2.1\lib\net40\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="hyjiacan.py4n">
      <HintPath>..\packages\hyjiacan.py4n.4.0.0\lib\net45\hyjiacan.py4n.dll</HintPath>
    </Reference>
    <Reference Include="LdapForNet, Version=2.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\LdapForNet.2.3.0\lib\netstandard2.0\LdapForNet.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.8.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.8\lib\net45-full\log4net.dll</HintPath>
    </Reference>
    <Reference Include="MailKit, Version=2.8.0.0, Culture=neutral, PublicKeyToken=4e064fe7c44a8f1b, processorArchitecture=MSIL">
      <HintPath>..\packages\MailKit.2.8.0\lib\net46\MailKit.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.KeyVault.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.KeyVault.Core.1.0.0\lib\net40\Microsoft.Azure.KeyVault.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Storage.Common, Version=11.2.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Storage.Common.11.2.2\lib\net452\Microsoft.Azure.Storage.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Storage, Version=9.3.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WindowsAzure.Storage.9.3.3\lib\net45\Microsoft.WindowsAzure.Storage.dll</HintPath>
    </Reference>
    <Reference Include="MimeKit, Version=2.9.0.0, Culture=neutral, PublicKeyToken=bede1c8a46c66814, processorArchitecture=MSIL">
      <HintPath>..\packages\MimeKit.2.9.1\lib\net46\MimeKit.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=11.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.11.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="Utility">
      <HintPath>..\lib\Utility.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Azure\AzureBlob.cs" />
    <Compile Include="Cache\Cache.cs" />
    <Compile Include="Cache\CacheFactory.cs" />
    <Compile Include="Cache\ICache.cs" />
    <Compile Include="Cache\RedisKey.cs" />
    <Compile Include="DBConnection.cs" />
    <Compile Include="igos_data.cs" />
    <Compile Include="RGHelper.cs" />
    <Compile Include="ReportsGeneratorMini.cs" />
    <Compile Include="ReportsGenerator.cs" />
    <Compile Include="WordDocumentTable.cs" />
    <Compile Include="ImageConvert.cs" />
    <Compile Include="Common.cs" />
    <Compile Include="Configs\Configs.cs" />
    <Compile Include="Configs\DBConnection.cs" />
    <Compile Include="Excel\ExcelHelper.cs" />
    <Compile Include="Extend\Ext.Convert.cs" />
    <Compile Include="Extend\Ext.DateTime.cs" />
    <Compile Include="Extend\Ext.Format.cs" />
    <Compile Include="Extend\ExtLinq.cs" />
    <Compile Include="Extend\ExtLinq.SortBy.cs" />
    <Compile Include="Extend\ExtList.Comparint.cs" />
    <Compile Include="Extend\ExtList.cs" />
    <Compile Include="Extend\ExtTable.cs" />
    <Compile Include="File\FileDownHelper.cs" />
    <Compile Include="File\FileHelper.cs" />
    <Compile Include="GZip.cs" />
    <Compile Include="Json\Json.cs" />
    <Compile Include="LDAP\Ldap.cs" />
    <Compile Include="Log\Log.cs" />
    <Compile Include="Log\LogFactory.cs" />
    <Compile Include="Log\Logger.cs" />
    <Compile Include="Mail\Mail.cs" />
    <Compile Include="Net\HttpMethods.cs" />
    <Compile Include="Net\Net.cs" />
    <Compile Include="Net\Utils.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Security\DESEncrypt.cs" />
    <Compile Include="Security\Md5.cs" />
    <Compile Include="Security\RSAEncrypt.cs" />
    <Compile Include="Serialize.cs" />
    <Compile Include="SMSHelper.cs" />
    <Compile Include="Validate\Validate.cs" />
    <Compile Include="VerifyCode.cs" />
    <Compile Include="Web\AjaxResult.cs" />
    <Compile Include="Web\WebHelper.cs" />
    <Compile Include="Wechat\Mina\TemplateHelper.cs" />
    <Compile Include="Wechat\WechatUtils.cs" />
    <Compile Include="XIRR.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>