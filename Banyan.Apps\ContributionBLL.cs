﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;

namespace Banyan.Apps
{
    public class ContributionBLL : BaseDAL<Contribution>
    {
        private readonly AjaxResult ajaxResult = null;
        private SysLogBLL logBLL = new SysLogBLL();
        private ProjectBLL projectBll = new ProjectBLL();
        public ContributionBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }
        public void updateLog(string page, string action, string description, Member user, string project = "")
        {
            SysLog log = new SysLog
            {
                Page = page,
                Action = action,
                Project = project,
                Description = description,
                CreatedBy = user == null ? "" : user.RealName,
                Ip = Utility.WebHelper.GetIP(),
                CreatorId = user == null ? 0 : user.Id,
            };
            logBLL.Add(log);
        }
        public AjaxResult GetContributions(int projectId)
        {
            ajaxResult.code = (int)ResultCode.success;
            List<Contribution> list = GetList($"projectId={projectId}", int.MaxValue, 0, "*", "ID Asc");
            ajaxResult.data = list;
            return ajaxResult;
        }
        public bool SyncProjectName(int projectID, string newName, string oldName, Member user)
        {
            var contribution = new Contribution();
            contribution.projectName = newName;
            var model = GetModel($"projectID={projectID}");
            var res = true;
            if(model != null)
            {
                res = Update(contribution, "projectName", $"projectID={projectID}");
                if (res)
                {
                    Logger.Info($"contribution sync projectName {oldName} to {newName} success", user.RealName);
                }
                else
                {
                    Logger.Error($"contribution sync projectName {oldName} to {newName} failed", user.RealName);
                }
            }
            return res;
        }
       
        public AjaxResult Save(Contribution model)
        {
            Member user = new MemberBLL().GetLogOnUser();
            ajaxResult.code = (int)ResultCode.exception;
            int id;
            bool success;
            if (model.ID != 0)
            {
                id = model.ID;
                model.modifier = user.RealName;
                model.modifiedDate = DateTime.Now;
                success = Update(model, "percentage, modifier, modifiedDate");
            }
            else
            {
                if (model.projectID <= 0)
                {
                    ajaxResult.msg = $"关联记录不存在";
                    return ajaxResult;
                }
                else
                {
                    var project = projectBll.GetModel(model.projectID);
                    model.projectName = project.Name;
                }
                model.creator = user.RealName;
                id = Convert.ToInt32(Add(model));
                success = id > 0;
            }
            if (success)
            {
                var m = new Project();
                m.Id = model.projectID;
                m.contributionManagerConfirm = false;
                m.contributionPartnerConfirm = false;
                projectBll.Update(m, "contributionManagerConfirm, contributionPartnerConfirm");
            }
            updateLog("Web, New Project Contribution", "add", "project ID:" + model.projectID + " " + $" {model.username}  {model.percentage} {model.description}", user, "");
            ajaxResult.data = id;
            ajaxResult.code = success ? (int)ResultCode.success : (int)ResultCode.exception;

            return ajaxResult;
        }
       
        public AjaxResult Delete(int id)
        {
            ajaxResult.code = (int)ResultCode.exception;
            Member user = new MemberBLL().GetLogOnUser();
            if (id <= 0)
            {
                ajaxResult.msg = $"参数不合法";
                return ajaxResult;
            }
            var model = GetModel(id);
            ajaxResult.code = base.Delete(id) > 0 ? (int)ResultCode.success : (int)ResultCode.exception;
            if (ajaxResult.code == (int)ResultCode.success)
            {
                var m = new Project();
                m.Id = model.projectID;
                m.contributionManagerConfirm = false;
                m.contributionPartnerConfirm = false;
                projectBll.Update(m, "contributionManagerConfirm, contributionPartnerConfirm");
            }
            updateLog("Web, Project Contribution", "delete", "ID:" + id, user, "");
            return ajaxResult;
        }

    }
}
