﻿
@{
    Layout = null;
    Banyan.Domain.Member member = ViewData["manager"] as Banyan.Domain.Member;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="divport" content="width=device-width" />
    <title>预览</title>
    <link type="text/css" href="/content/js/plugins/layui/css/layui.css" rel="stylesheet" />
    <link href="~/content/css/preview.css?v=@(DateTime.Now.Ticks)" rel="stylesheet" />
    <style>
        body{
            line-height: 1.5;
        }
        pre {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", Tahoma, Arial, sans-serif;
        }
        .memo-select {
            border-color: #e6e6e6;
            height: 34px;
            border-radius: 2px;
        }
.water-mark {
            font-size: 14px;
            color: #c2c2c2; /* 颜色会动态重写，根据配置 */
            position: fixed;
            padding: 0 15px;
            transform: translate(-50%, -50%);
            transform: rotate(-30deg);
            -ms-transform: rotate(-30deg); /* IE 9 */
            -moz-transform: rotate(-30deg); /* Firefox */
            -webkit-transform: rotate(-30deg); /* Safari 和 Chrome */
            -o-transform: rotate(-30deg);
            opacity: 0.3;
            overflow: hidden;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .air-name,.air-time,.airbubble{
            font-size:14px;
            font-size:inherit !important;
        }

        .weui-table_tr .weui-table_td{
            width: 176px;
        }

        .font-normal {
            font-size: 14px;
        }
        .font-middle {
            font-size: 24px;
        }
        .font-bold {
            font-size: 30px;
        }
        .news-wrapper {
            padding: 4px 10px;
            color: rgba(59, 133, 236, 0.925);
            box-shadow: rgba(26, 26, 26, 0.1) 4px 4px 6px;
        }
        .news-title {
            font-weight: bold;
        }
        .highlight-news {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="app" style="display:none;">
        <template v-for="item in waterArray">
            <p class="water-mark" :style="{left: item.wid + '%', top: item.hei + '%'}">@(member.RealName)</p>
        </template>
        <div class="page-bd_scroll page-bd_fixedbt font-normal" :style="isAdmin ? 'margin-bottom:130px;' : 'margin-bottom:50px'" v-on:scroll="loadScroll">
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    项目名称
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.Name}}</label>
                </div>
                <div v-if="newsList.length > 0" v-on:click="toggleNews">
                    <span style="color: rgb(32, 138, 238); margin-right: 10px; cursor: pointer;">{{ newsList.length}}个相关资讯</span>
                </div>
            </div>
            <div v-show="showNews">
                <div v-for="(item,index) in newsList" class="news-wrapper">
                    <a :href="'/article/news?id=' + item.Id + '&highlight=' + objData.Name" target="_blank">
                        <div class="news-title">{{item.Title}}</div>
                        <div v-html="item.Content"></div>
                    </a>
                </div>

            </div>
            <div v-if="revisitName" class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    原项目
                </div>
                <div class="weui-flex__item">
                    <a class="link" style="color: #208aee;cursor:pointer" v-on:click="preview">{{revisitName}}</a>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    项目组
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.RoleName}}</label>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    项目负责人
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.ProjectManager}}</label>
                    @*<pre v-html="objData.ProjectManager"></pre>*@
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    项目组成员
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.groupMember}}</label>
                    @*<pre v-html="objData.ProjectManager"></pre>*@
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    项目来源
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.Source}}</label>
                </div>
            </div>
            <div v-if="objData.Introducer" class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    <div v-if="objData.Source == 'FA介绍'">FA名称</div>
                    <div v-else=>项目介绍人</div>
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.Introducer}}</label>
                </div>
            </div>
            <div v-if="objData.finder && objData.finder != objData.ProjectManager" class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    <div v-if="objData.Source == 'FA介绍' ">FA推荐同事</div>
                    <div v-else>联系方式提供人</div>
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.finder}}</label>
                </div>
            </div>
            <div v-if="objData.ContributionDetail" class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    其他形式同事贡献
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.ContributionDetail}}</label>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    项目状态
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.nextStepStatus}}</label>
                    @*<pre v-html="objData.nextStepStatus"></pre>*@
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    日期
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.PubTime}}</label>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    高榕参会人
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.InteralPTCP}}</label>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    外部参会人
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.Participant}}</label>
                </div>
            </div>

            @*<div class="weui-flex weui-table_tr" >
            <div class="weui-table_td">
                项目介绍人
            </div>
            <div class="weui-flex__item">
                <label>{{objData.finder}}</label>
            </div>
        </div>*@
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    DD负责人
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.DDManager}}</label>
                    @*<pre v-html="objData.DDManager"></pre>*@
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    成立年份
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.foundedYear}}</label>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    项目所在地
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.city}}</label>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    员工人数
                </div>
                <div class="weui-flex__item">
                    <pre v-html="objData.HeadCount"></pre>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    融资币种
                </div>
                <div class="weui-flex__item">
                    <pre v-html="objData.Currency"></pre>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    总资产规模
                </div>
                <div class="weui-flex__item">
                    <pre v-html="objData.TotalAsset"></pre>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    新发现
                </div>
                <div class="weui-flex__item">
                    <pre v-html="objData.UpdatedNews"></pre>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    项目简介
                </div>
                <div class="weui-flex__item">
                    <pre v-html="objData.Summary"></pre>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    项目创始人
                </div>
                <div class="weui-flex__item">
                    <label>{{objData.Founder}}</label>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    团队背景
                </div>
                <div class="weui-flex__item">
                    <pre v-html="objData.Background"></pre>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    业务数据
                </div>
                <div class="weui-flex__item">
                    <pre v-html="objData.BusinessData"></pre>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    财务数据
                </div>
                <div class="weui-flex__item">
                    <pre v-html="objData.FinancialData"></pre>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    股权结构
                </div>
                <div class="weui-flex__item">
                    <pre v-html="objData.ShareStructure"></pre>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    历史及当前融资方案
                </div>
                <div class="weui-flex__item">
                    <pre v-html="objData.InvestHistory"></pre>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    提到的主要同行/供应商
                </div>
                <div class="weui-flex__item">
                    <pre v-html="objData.CompareProduct"></pre>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    项目亮点
                </div>
                <div class="weui-flex__item">
                    <pre v-html="objData.HighLight"></pre>
                </div>
            </div>
            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    项目风险
                </div>
                <div class="weui-flex__item">
                    <pre v-html="objData.Risk"></pre>
                </div>
            </div>

            <div class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    数据包分析/后续工作
                </div>
                <div class="weui-flex__item">
                    <pre v-html="objData.NextStep"></pre>
                </div>
            </div>

            <div v-if="(isAdminOrSuper || objData.ProjectManager == '@(member.RealName)') && contributions.length > 0 && objData.contributionManagerConfirm && objData.contributionPartnerConfirm && scorePass" class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    项目贡献
                </div>
                <div class="weui-flex__item">
                    <table class="table table-striped" style="width:100%;">
                        <thead>
                            <tr>
                                <th>贡献者</th>
                                <th>贡献百分比</th>
                                <th>贡献说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(item,index) in contributions" style="width:100%;">
                                <td style="text-align:center">
                                    {{item.username}}
                                </td>
                                <td style="text-align:center">
                                    {{item.percentage}}%
                                </td>
                                <td style="text-align:center">
                                    {{item.description}}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div v-if="objData.RoleName =='消费组'" class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    login质量评分（最高3分）
                </div>
                <div class="weui-flex__item">
                    <pre v-html="completeScore"></pre>
                </div>
                <div v-if="groupManager=='consume' || isAdminOrSuper" class="weui-flex__item">
                    <select id="complete-score" class="memo-select">
                        <option value="">- 请选择评分 -</option>
                        <option value="3">3</option>
                        <option value="2">2</option>
                        <option value="1">1</option>
                    </select>
                </div>
                <div v-if="groupManager=='consume' || isAdminOrSuper" class="section-comment__send" @@click="sendCompleteScore">提交评分</div>
            </div>

            <div v-else-if="objData.RoleName =='技术组'" class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    login质量评分（最高3分）
                </div>
                <div class="weui-flex__item">
                    <pre v-html="completeScore"></pre>
                </div>
                <div v-if="groupManager=='tech' || isAdminOrSuper" class="weui-flex__item">
                    <select id="complete-score" class="memo-select">
                        <option value="">- 请选择评分 -</option>
                        <option value="3">3</option>
                        <option value="2">2</option>
                        <option value="1">1</option>
                    </select>
                </div>
                <div v-if="groupManager=='tech' || isAdminOrSuper" class="section-comment__send" @@click="sendCompleteScore">提交评分</div>
            </div>

            <template v-if="(fileList && fileList.length > 0) || BP.length > 0 || DD.length > 0">
                <div class="weui-cells__title">附件</div>
                <div v-if="BP.length > 0" class="weui-cell">
                    <div v-for="(item, idx) in BP" style="margin-right: 10px">
                        <a :href=`javascript:void(preview(${item.Id}))`>
                            <div class="weui-cell__bd">{{item.AtName}}</div>
                        </a>
                    </div>
                </div>
                <div v-if="DD.length > 0" class="weui-cell">
                    <div v-for="(item, idx) in DD" style="margin-right: 10px">
                        <a :href=`javascript:void(preview(${item.Id}))`>
                            <div class="weui-cell__bd">(DD) {{item.AtName}}</div>
                        </a>
                    </div>
                </div>
                <div class="weui-cells">
                    <div class="weui-cell weui-cell_access" v-for="(item, index) in fileList">
                        <div v-if="item.AtSuffix=='Image'">
                            {{item.AtName}}
                            <img :src="item.AtUrl" style="width: 92vw;" />
                        </div>
                        <a v-else :href="item.AtUrl">
                            <div class="weui-cell__bd">{{item.AtName}}</div>
                            <div class="weui-cell__ft"></div>
                        </a>
                    </div>
                </div>
            </template>

            <div v-if="objData.ai_question" class="weui-flex weui-table_tr">
                <div class="weui-table_td">
                    本地AI参考提问
                </div>
                <div class="weui-flex__item" style="line-height:1.4">
                    <details v-if="objData.ai_reasoning" style=" background: rgba(237, 246, 253, 0.8); padding: 4px; margin-bottom: 5px; border-radius: 4px">
                        <summary style="cursor: pointer; color: #43a3e5;">点击展开/折叠思维链</summary>
                        <div id="posit">
                            <div style="margin-top: 4px; white-space: break-spaces; color: #333; line-height: 1.4 " v-html="objData.ai_reasoning"></div>
                        </div>
                    </details>
                    <pre id="ai-question-content" v-html="objData.ai_question"></pre>
                </div>
            </div>
            <div v-if="isAdminOrSuper" class="weui-cell weui-cell_access box-reply_expert" v-for="(item,index) in memoList">
                <div class="weui-cell__hd">
                    <img class="box-avatar_mini" :src="item.UserAvatar" />
                </div>
                <div class="weui-cell__bd weui-cell_primary">
                    <div class="pd-l-5">
                        <div class="air-name">
                            {{item.Type}}&nbsp;&nbsp;
                            <a href="javascript:;" style="color:#208aee;" @@click="editMemo(index)" v-if="isAdmin">编辑</a>
                            <a href="javascript:;" style="color:#208aee;" @@click="deleteMemo(index)" v-if="isAdmin">删除</a>
                        </div>
                        <div class="air-time">{{item.PubTime}}（{{item.UserName}}）</div>
                        <div class="box-quiz_item airbubble" width="100%"><pre v-html="item.Memo"></pre></div>
                    </div>
                </div>
            </div>
            <div class="weui-cell weui-cell_access box-reply_expert" v-for="item in dataList">
                <div class="weui-cell__hd">
                    <img class="box-avatar_mini" :src="item.UserAvatar" />
                </div>
                <div class="weui-cell__bd weui-cell_primary">
                    <div class="pd-l-5">
                        <div class="air-name">{{item.UserName}} <span class="air-time" style="margin-left: 10px">{{item.AddTime}}</span></div>

                        <div class="box-quiz_item airbubble"><pre>{{item.Content}}</pre></div>
                    </div>
                </div>
            </div>
            <div class="section-comment section-comment_fixed" style="height: 130px;" v-if="isAdmin">
                <div class="weui-flex section-comment-box" v-show="memoOper == 0">
                    <div class="weui-flex weui-table_tr">
                        <div class="weui-table_td" style="font-weight:normal">
                            <div class="weui-flex__item">
                                <select id="memo-type" class="memo-select">
                                    <option value="">- 请选择类型 -</option>
                                    <option value="组会">组会</option>
                                    <option value="周会">周会</option>
                                    <option value="IC会">IC会</option>
                                </select>
                            </div>
                            <div class="weui-flex__item">
                                <div class="layui-inline">
                                    <input type="text" class="layui-input" id="memo-date" placeholder="日期" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="weui-flex__item">
                        <textarea class="layui-textarea" id="memo-content" placeholder="会议纪要"></textarea>
                    </div>
                    <div class="section-comment__send" @@click="sendMemo">发送</div>
                </div>
                <div class="weui-flex section-comment-box" v-show="memoOper == 1">
                    <div class="weui-flex weui-table_tr">
                        <div class="weui-table_td" style="font-weight:normal">
                            <div class="weui-flex__item">
                                <select v-model="memoModel.Type" class="memo-select">
                                    <option value="">- 请选择类型 -</option>
                                    <option value="组会">组会</option>
                                    <option value="周会">周会</option>
                                    <option value="IC会">IC会</option>
                                </select>
                            </div>
                            <div class="weui-flex__item">
                                <div class="layui-inline">
                                    <input type="text" class="layui-input" id="memo-date1" v-model="memoModel.PubTime" placeholder="日期" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="weui-flex__item">
                        <textarea class="layui-textarea" placeholder="会议纪要" v-model="memoModel.Memo"></textarea>
                    </div>
                    <div class="section-comment__send">
                        <a href="javascript:;" @@click="saveMemo">保存</a>&nbsp;&nbsp;
                        <a href="javascript:;" @@click="saveMemo(-1)">取消</a>
                    </div>
                </div>
            </div>
            <div class="section-comment section-comment_fixed" v-else>
                <div class="weui-flex section-comment-box">
                    <div class="weui-flex__item">
                        <input type="text" class="section-comment_input" v-model="commentIpt" placeholder="发表评论..." />
                    </div>
                    <div class="section-comment__send" @@click="sendComment">发送</div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="~/content/js/core/jquery.min.js"></script>
    <script type="text/javascript" src="~/content/js/plugins/layui/layui.js"></script>
    <script type="text/javascript" src="~/content/js/vue/vue.min.js"></script>
    <script type="text/javascript">

    
          window.addEventListener('keydown', function (e) {
            if (e.keyCode == 83 && (navigator.platform.match('Mac') ? e.metaKey : e.ctrlKey)) {
                e.preventDefault();
            }
        })
        var id = @(ViewBag.id), layer, laydate, currMemo;

        layui.use(['layer','laydate'], function () {
            layer = layui.layer;
            laydate = layui.laydate;
            laydate.render({
                elem: '#memo-date',
            });
            laydate.render({
                elem: '#memo-date1',
            });
        });

        function preview(id) {
            var h = document.documentElement.clientHeight || document.body.clientHeight;
            var w = document.documentElement.clientWidth || document.body.clientWidth;
            var idx =layer.open({
                type: 2,
                area: [w + 'px', h + 'px'],
                fix: false,
                maxmin: true,
                anim: 5,
                shade: 0,
                title: "文件预览",
                content: '/index/picpreview?id=' + id,
            });
        }

        window.addEventListener('load', function () {
            window.addEventListener('resize', function () {
                console.log(window.innerWidth);
                if (window.innerWidth <= 850) {
                    if ($('.page-bd_fixedbt').hasClass("font-bold")) {
                        $('.page-bd_fixedbt').removeClass("font-bold");
                    }

                    $('.page-bd_fixedbt').removeClass("font-middle");

                } else if (window.innerWidth > 960 && window.innerWidth <= 1160) {
                    //size = Math.round((window.innerWidth - 960) / 960 * 16 + 14);
                    //$('.page-bd_fixedbt').css({ "fontSize": size + "px" });
                    $('.page-bd_fixedbt').removeClass("font-bold").addClass("font-middle");
                } else if (window.innerWidth > 1160) {
                    if ($('.page-bd_fixedbt').hasClass("font-bold"))
                        return;
                    $('.page-bd_fixedbt').addClass("font-bold");
                }
            });
        });

        var app = new Vue({
            el: '#app',
            data: {
                query: {
                    id: id,
                    aid: id,
                    pid: 1,
                    page: 1,
                },
                objData: {},
                BP: [],
                DD: [],
                dataList: [],
                memoList: [],
                fileList: [],
                completeScore: '',
                memoOper: 0, @* 0：添加，1：编辑 *@
                memoModel: {
                    id: 0,
                    content: '',
                    aid: 0,
                },
                loadding: false,
                loadState: -1,
                commentIpt: '',
                isAdminOrSuper: @(member.Levels) == 1 || @(member.Levels) == 2,
                isAdmin: @(member.Levels) == 1,
                viewMemo: @(member.Levels) != 0,
                waterArray: [],
                groupManager: '',
                revisitName: undefined,
                revisitManger: undefined,
                showRevisitLink: false,
                contributions: [],
                scoreStageList: [],
                scorePass: false,
                newsList: [],
                showNews: false
            },
            methods: {
                loadScroll: function (event) {
                    var that = this;

                    nodeScrollTotalHeigth = event.target.scrollHeight;
                    nodeScrollTop = event.target.scrollTop;
                    nodeHight = $(event.target).height();
                    (nodeScrollTotalHeigth - nodeScrollTop - nodeHight) < 25 && that.loadMore();
                },
                revisit: function(id) {
                    var that = this;
                    $.post('/adminapi/getrevisitright', { id: id }, function (res) {
                        if (res.data.Id) {
                            that.revisitName = res.data.Name;
                            that.revisitManager = res.data.ProjectManager
                            that.showRevisitLink = true;
                        } else {
                            that.revisitName = res.data.Name;
                            that.revisitManager = res.data.ProjectManager
                        }
                    });
                },
                initAIObserver() {
                    var that = this
                    const observer = new IntersectionObserver((entries) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting) {
                                console.log("in")
                                that.startAITimer();
                            } else {
                                console.log("out")
                                that.resetAITimer();
                            }
                        });
                    }, {
                        root: null, // 相对于视口
                        rootMargin: '0px',
                        threshold: 0.3 // 当10%的元素可见时触发
                    });
                    setTimeout(function () {
                        const element = document.querySelector('#ai-question-content');
                        if (element) {
                            observer.observe(element);
                            that.aiObserver = observer;
                        }
                    }, 1000)
                },

                startAITimer() {
                    var that = this
                    const startTime = Date.now();
                    that.aiDisplayStartTime = startTime;

                    that.aiDisplayTimer = setInterval(() => {
                        const currentTime = Date.now();
                        if (currentTime - that.aiDisplayStartTime >= 5000) {
                            that.logAIDisplay();
                            that.resetAITimer();
                        }
                    }, 1000);
                },
                logAIDisplay() {
                    var that = this
                    $.post('/adminapi/logset',
                        {
                            Project: that.objData.Name,
                            Page: 'ai_question web project set',
                            Action: 'visit',
                        },
                        function (res) { }
                    );
                },
                resetAITimer() {
                    var that = this
                    if (that.aiDisplayTimer) {
                        clearInterval(that.aiDisplayTimer);
                        that.aiDisplayTimer = null;
                        that.aiDisplayStartTime = 0;
                    }
                },
                toggleNews: function () {
                    var that = this;
                    that.showNews = !that.showNews;
                    console.log(that.showNews)
                },
                preview: function() {
                    var that = this;
                    if (!that.showRevisitLink) {
                        layer.msg("无权访问，请联系项目负责人" + that.revisitManager + "分享");
                        return;
                     }
                     window.open('/index/preview?id=' + that.objData.RevisitId, '_blank');
                },
                loadMemo: function() {
                    var that = this;
                    $.post("/adminapi/getmemos", { pid: that.query.id }, function (res) {
                        if (res.code == 0) {
                            that.memoList = res.data || [];
                        }
                    });
                },
                relatedNews: function () {
                    var that = this;
                    $.post("/adminapi/ProjectRelatedNews", { pid: that.query.id }, function (res) {
                        console.log(res)
                        if (res.data.length > 0) {
                            that.newsList = res.data
                        }
                    })
                },
                loadMore: function () {
                    var that = this;

                    if (!that.loadding) {
                        that.loadding = true;
                        $.post("/adminapi/getcomments", that.query, function (res) {
                            that.loadding = false;

                            if (res.code != 0 || !res.data || res.data.length == 0) {
                                if (that.query.page == 1) {
                                    that.dataList = [];
                                }
                                that.loadding = true;
                            }
                            else {
                                if (that.query.page == 1) {
                                    that.dataList = res.data;
                                }
                                else {
                                    that.dataList = that.dataList.concat(res.data);
                                }
                                that.query.page++;
                            }
                        });
                    }
                },
                sendComment: function() {
                    var that = this;
                    if (that.loadState > -1) {
                        return;
                    }
                    if ($.trim(that.commentIpt) == '') {
                        layer.msg('请输入评论内容');
                        return;
                    }
                    that.loadState = 0;
                    var pdata = {
                        content: that.commentIpt,
                        aid: that.query.aid,
                        pid: that.query.pid,
                    };
                    $.post('/adminapi/usercomment', pdata, function (data) {
                        that.loadState = -1;
                        if (data.code == 0) {
                            layer.msg('评论成功');
                            that.dataList.unshift(data.data);
                            that.commentIpt = '';
                        } else {
                            layer.msg(data.msg);
                        }
                    });
                },
                sendCompleteScore: function () {
                    var that = this;
                    var pdata = {
                        pid: that.query.id,
                        score: $('#complete-score').val()
                    };
                     $.post('/adminapi/completescore', pdata, function (data) {
                        that.loadState = -1;
                        if (data.code == 0) {
                            layer.msg('评分成功');
                            that.completeScore = pdata.score;
                        } else {
                            layer.msg("评分失败");
                        }
                    });
                },
                sendMemo: function() {
                    var that = this;
                    if (that.loadState > -1) {
                        return;
                    }
                    var pubdate = $('#memo-date').val();
                    if ($.trim(pubdate) == '') {
                        layer.msg('请选择日期');
                        return;
                    }
                    var mmtype = $('#memo-type').val();
                    if ($.trim(mmtype) == '') {
                        layer.msg('请选择类型');
                        return;
                    }
                    var mmcontent = $('#memo-content').val();
                    if ($.trim(mmcontent) == '') {
                        layer.msg('请输入内容');
                        return;
                    }
                    that.loadState = 0;
                    var pdata = {
                        pid: that.query.id,
                        memo: mmcontent,
                        mtype: mmtype,
                        mdate: pubdate,
                    };
                    $.post('/adminapi/addmemo', pdata, function (data) {
                        that.loadState = -1;
                        if (data.code == 0) {
                            layer.msg('发布成功');
                            $('#memo-date').val('');
                            $('#memo-type').val('');
                            $('#memo-content').val('');
                            that.loadMemo();
                        } else {
                            layer.msg(data.msg);
                        }
                    });
                },
                editMemo: function(index) {
                    var that = this;
                    var model = that.memoList[index];
                    currMemo = {
                        AddTime: model.AddTime,
                        Id: model.Id,
                        Memo: model.Memo,
                        ProjectId: model.ProjectId,
                        PubTime: model.PubTime,
                        Type: model.Type,
                        UserAvatar: model.UserAvatar,
                        UserName: model.UserName,
                    };
                    that.memoModel = currMemo;
                    that.memoOper = 1;
                },
                deleteMemo: function (index) {
                    var that = this;

                    $.post('/adminapi/deletememo', that.memoList[index], function (data) {
                        that.loadState = -1;
                        if (data.code == 0) {
                            layer.msg('删除成功');
                            that.loadMemo();
                        } else {
                            layer.msg(data.msg);
                        }
                    });
                },
                saveMemo: function(value) {
                    var that = this;
                    if (value < 0) {
                        that.memoModel = { id: 0 };
                        that.memoOper = 0;
                        return;
                    }
                    $.post('/adminapi/savememo', that.memoModel, function (data) {
                        that.loadState = -1;
                        if (data.code == 0) {
                            layer.msg('保存成功');
                            that.memoModel = { id: 0 };
                            that.memoOper = 0;
                            that.loadMemo();
                        } else {
                            layer.msg(data.msg);
                        }
                    });
                },
                initAttachment: function() {
                    var that = this;
                    $.post('/adminapi/getcontributions', { projectID: id }, function (res) {
                        if (res.code == 0) {
                            that.contributions = []
                            res.data.map(function (data) {
                                that.contributions.push(data)
                            })
                        }
                    })
                    $.post('/adminapi/projectdocs', { id: id }, function (res) {
                        if (res.code == 0) {
                            that.fileList = res.data || [];
                            that.fileList = that.fileList.filter(function (file) {
                                if (file.AtSuffix === "BP") {
                                    that.BP.push(file)
                                    return false
                                } else if (file.AtSuffix === "DD") {
                                    that.DD.push(file)
                                    return false
                                }
                                return true
                            })
                        } else {
                            that.loadState = -999;
                        }
                    }).error(function (xhr, errorText, errorType) {
                        that.loadState = -999;
                    });
                },
                initGroupManager: function () {
                    var that = this;
                    var cookies = document.cookie.split(';');
                    cookies.map(function (val) {
                        if (val.indexOf('groupmanager') > 0) {
                            that.groupManager = val.split('=')[1]
                        }
                    })

                    if (!that.groupManager) {
                        that.groupManager = "mark"
                        $.post('/adminapi/getgroupmanager', function (res) {
                            if (res.code == 0 && res.data) {
                                that.groupManager = res.data;
                            }
                            var d = new Date();
                            d.setTime(d.getTime() + 24 * 60 * 60 * 1000);
                            var expires = "expires=" + d.toUTCString();
                            document.cookie = 'groupmanager=' + that.groupManager + ";" + expires + ";path=/";
                        })
                    }
                },
                initWaterArr: function () {
                    var that = this;
                    var waterArr = [], wid = 5, hei = 5;
                    for (var i = 0; i < 5; i++) {
                        for (var j = 0; j < 5; j++) {
                            waterArr.push({
                                wid: wid + i * 19,
                                hei: hei + j * 16
                            });
                        }
                    }
                    that.waterArray = waterArr;
                },
                initProjectScore: function () {
                    var that = this;
                    if (that.objData.PubTime < "2023-01-17") {
                        return
                    }
                    $.post('/adminapi/getstagelist', { id }, function (res) {
                        if (res.code == 0) {
                            that.scoreStageList = res.data.data.scoreStageList || [];
                            if (that.scoreStageList.length > 0) {
                                that.scorePass = (that.scoreStageList[0].ProjectManagerScore > 6 || that.scoreStageList[0].EndTime < '2022-04-27') && that.scoreStageList[0].PartnerScoreAvg >= 6 && that.scoreStageList[0].State == 0
                            }

                        }
                    }).error(function (xhr, errorText, errorType) {
                        console.log(errorText);
                    });
                },
            },
            created: function () {
                var that = this;

                $("#app").show();
                $.post('/adminapi/projectdetail', that.query, function (data) {
                    if (data.code == 0) {
                        that.objData = data.data;
                        that.completeScore = that.objData.CompleteScore
                        if (that.objData.RevisitId) {
                                that.revisit(that.objData.RevisitId);
                        }
                        //that.initProjectScore();
                        that.loadMore();
                        that.relatedNews();
                    }
                });
               
                that.loadMemo();
                that.initAttachment();
                that.initWaterArr();
                that.initGroupManager();
                that.initAIObserver();
            }
        });
    </script>
</body>
</html>
