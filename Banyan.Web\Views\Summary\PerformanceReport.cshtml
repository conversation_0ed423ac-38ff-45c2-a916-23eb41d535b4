﻿@using Banyan.Domain
@{
    ViewBag.Name = "投资团队报表";
    Layout = "/Views/Shared/_LayoutSuper.cshtml";
    List<Banyan.Domain.Member> memberlist = (List<Banyan.Domain.Member>)ViewData["creatorList"];
    var manager = ViewData["manager"] as Banyan.Domain.Member;
}

<style>
    td .layui-table-cell, .layui-table-cell img {
        height: 35px !important;
        line-height: 35px !important;

        padding: 0 15px;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
        box-sizing: border-box;
    }
</style>

<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li>投资团队报表</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <div class="tab-pane active">
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="pull-right search-bar"></div>
                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                                <div class="form-group">
                                    <select class="form-control" id="member" name="keyclass" size="1">
                                        <option value="">成员</option>
                                        @if (memberlist != null && memberlist.Count() > 0)
                                        {
                                            foreach (var ci in memberlist)
                                            {
                                                <option value="@(ci.RealName)">@(ci.RealName)</option>
                                            }
                                        }
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="" for="currencyRate">USD/CNY汇率</label>
                                    <input type="number" class="form-control" value="7" min="0.0" step="0.0001" id="currencyRate" placeholder="USD/CNY汇率" style="width:150px;">
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="title-name"></label>
                                    <input class="form-control" type="number" min="0.0" step="0.01" id="PManager" placeholder="负责项目权重" style="width:150px;">
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="title-name"></label>
                                    <input class="form-control" type="number" min="0.0" step="0.01" id="recommend" placeholder="推荐项目权重" style="width:150px;">
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="title-name"></label>
                                    <input class="form-control" type="number" min="0.0" step="0.01" id="PMember" placeholder="参与项目权重" style="width:150px;">
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="title-name"></label>
                                    <input class="form-control" type="number" min="0.0" step="0.01" id="PIManager" placeholder="负责投后权重" style="width:150px;">
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="title-name"></label>
                                    <input class="form-control" type="number" min="0.0" step="0.01" id="PIMember" placeholder="参与投后权重" style="width:150px;">
                                </div>
                                <div class="form-group">
                                    <label class="slick-prev" for="title-name">贡献价值计算</label>
                                    <input class="form-control" type="checkbox" value="false" id="CombineCheck" placeholder="贡献价值计算">
                                </div>
                                <div class="form-group">
                                    <a class="btn btn-default" id="dosearch" onclick="dosearch()">生成</a>
                                </div>
                            </form>
                        </div>

                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                                
                            </form>
                        </div>

                        <pre id="pre"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="toolbar-btn">
</script>
<script type="text/html" id="bartpl">
    <div class="btn-group" role="group" data-toggle="buttons">
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="预览" lay-event="preview" data-original-title="预览"><i class="fa fa-eye"></i></button>
    </div>
</script>
<script type="text/html" id="titleTpl">
    <button type="button"  lay-event="preview" class="layui-btn layui-btn-primary" style="width:100%">{{d.RealName}}</button>
</script>
@section scripts{
    <script type="text/javascript">

        function dosearch() {
            if ($('#fund').val() == '') {
                return alert("请先选择基金")
            }
             $.ajax({
                type: 'POST',
                url: '@(Url.Action("PerformanceReport", "adminapi"))',
                 data: {
                     PMName: $("#member").val(),
                     USDtoRMBRate: $("#currencyRate").val(),
                     recommendWeight: $("#recommend").val(),
                     PManagerWeight: $("#PManager").val(),
                     PIManagerWeight: $("#PIManager").val(),
                     PMemberWeight: $("#PMember").val(),
                     PIMemberWeight: $("#PIMember").val(),
                     combine: document.getElementById('CombineCheck').checked
                 },
                success: function (data) {
                    $("#pre").html(data)
                },
                error: function () {
                    confirm("Sorry, an exception occured！");
                }
            });
        }
        var queryParams = {}, date = new Date();
        layui.use(['laypage', 'layer', 'table', 'laydate'], function () {
            var laypage = layui.laypage,
                layer = layui.layer,
                laydate = layui.laydate,
                table = layui.table;

        });

        function preview(RealName) {
            queryParams = {
                PMName: RealName,
                startdate: $('#startdate').val(),
                enddate: $('#enddate').val(),
            }

            $.post('/Summary/PerformanceReport', queryParams, function (str) {
                layer.open({
                    type: 1,
                    area: ['1250px', '667px'],
                    fix: false,
                    maxmin: true,
                    anim: 5,
                    shade: 0,
                    title: "业绩汇总-" + RealName,
                    content: str //注意，如果str是object，那么需要字符拼接。
                });

            })
            //var querystr = 'https://fms.gaorongvc.com/staffperformance.ashx?PMName=' + RealName + '&startDate=' + $('#startdate').val() + '&endDate=' + $('#enddate').val() + '&OpenId=' + $('#OpenId').val();
            //querystr += '&currencyRate=' + $('#currencyRate').val();

            //    querystr += '&type=row';

            //    layer.open({
            //        type: 2,
            //        area: ['1250px', '667px'],
            //        fix: false,
            //        maxmin: true,
            //        anim: 5,
            //        shade: 0,
            //        title: "业绩汇总-" + RealName,
            //        content: querystr //这里content是一个URL，如果你不想让iframe出现滚动条，你还可以content: ['http://sentsin.com', 'no']
            //    });
           
        }
    </script>
}
