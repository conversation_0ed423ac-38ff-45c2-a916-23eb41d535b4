﻿using Entity.Base;
using System;
using Utility;


namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public class Meet
    {

        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        /// <summary>
        /// 会议标题
        /// </summary>
        [SqlField]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 附件
        /// </summary>
        [SqlField]
        public String Name { get; set; } = string.Empty;
        /// <summary>
        /// 编辑人用户ID
        /// </summary>
        [SqlField]
        public int Creator { get; set; }

        /// <summary>
        /// 编辑人名字
        /// </summary>
        [SqlField]
        public string CreatorName { get; set; } = string.Empty;

        /// <summary>
        /// 负责人
        /// </summary>
        [SqlField]
        public String Manager { get; set; } = string.Empty;

        /// <summary>
        /// 开始时间
        /// </summary>
        [SqlField]
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 时长
        /// </summary>
        [SqlField]
        public int Duration { get; set; }


        /// <summary>
        /// 会议简述
        /// </summary>
        [SqlField]
        public string Summary { get; set; } = string.Empty;

        /// <summary>
        /// 会议纪要
        /// </summary>
        [SqlField]
        public string Comment { get; set; } = string.Empty;

        /// <summary>
        /// 是否重复
        /// </summary>
        [SqlField]
        public byte IsRepeat { get; set; } = 0;


        /// <summary>
        /// 参会人
        /// </summary>
        [SqlField]
        public string InternalPTCP { get; set; } = string.Empty;

        /// <summary>
        /// 外部参会人
        /// </summary>
        [SqlField]
        public string Participant { get; set; } = string.Empty;

        /// <summary>
        /// 是否是私密项目
        /// </summary>
        [SqlField]
        public byte IsPrivate { get; set; } = 1;

        /// <summary>
        /// 会议添加时间
        /// </summary>
        [SqlField]
        public DateTime PubTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后编辑时间
        /// </summary>
        [SqlField]
        public DateTime LastTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后重复时间
        /// </summary>
        [SqlField]
        public DateTime RepeatLastTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否允许操作
        /// </summary>
        public bool IsOperate { get; set; } = false;

        /// <summary>
        /// 开始时间字符串
        /// </summary>
        public string StartTimeStr { get; set; } = string.Empty;

        /// <summary>
        /// 状态
        /// </summary>
        [SqlField]
        public byte Status { get; set; }
    }

    public class MeetWithAttach : Meet
    {
        /// <summary>
        /// 附件
        /// </summary>
        [SqlField]
        public string Attach { get; set; }
    }


    public enum MeetStatus
    {
        /// <summary>
        /// 编辑中
        /// </summary>
        editing = 2,

        /// <summary>
        /// 已发布
        /// </summary>
        normal = 1,

        /// <summary>
        /// 已删除
        /// </summary>
        delete = 0,
    }

}


//drop table if exists  meet;
//create table meet(
//  "id" int primary key identity(1,1),
//  "title" varchar(255) not null,
//  "creator" int not null,
//  "creatorname" varchar(255) not null,
//  "manager" varchar(255),
//  "starttime" datetime not null,
//  "duration" int not null default 60,
//  "summary" ntext,
//  "comment" ntext,
//  "name" ntext,
//  "internalptcp" varchar(255),
//  "participant" varchar(255),
//  "isrepeat" tinyint default 0,
//  "isprivate" tinyint default 1,
//  "pubtime" datetime not null,
//  "lasttime" datetime not null default getutcdate(),
//  "repeatlasttime" datetime,
//  "status" tinyint not null default 1
//);

//begin transaction;
//insert into meet("title", "creator", "creatorname", "manager", internalptcp, participant, pubtime, starttime) values
//('组会', 319, 'lq', 'lq', '1,319','1,319', getutcdate(), getutcdate()),
//('组会2', 319, 'lq', 'lq', '1,319', '1,319', getutcdate(), getutcdate());
//commit;

//select* from meet;