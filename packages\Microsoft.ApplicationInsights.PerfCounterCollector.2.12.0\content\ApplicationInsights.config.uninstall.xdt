﻿<ApplicationInsights xmlns="http://schemas.microsoft.com/ApplicationInsights/2013/Settings" xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <TelemetryModules>
    <Add xdt:Transform="Remove" xdt:Locator="Match(Type)" Type="Microsoft.ApplicationInsights.Extensibility.PerfCounterCollector.PerformanceCollectorModule, Microsoft.AI.PerfCounterCollector" />
    <Add xdt:Transform="Remove" xdt:Locator="Match(Type)" Type="Microsoft.ApplicationInsights.Extensibility.PerfCounterCollector.QuickPulse.QuickPulseTelemetryModule, Microsoft.AI.PerfCounterCollector" />
  </TelemetryModules>
  <TelemetryModules xdt:Transform="Remove" xdt:Locator="Condition(count(*)=0)"/>

  <TelemetrySinks>
    <Add Name="default">
      <TelemetryProcessors>
        <Add xdt:Transform="Remove" xdt:Locator="Match(Type)" Type="Microsoft.ApplicationInsights.Extensibility.PerfCounterCollector.QuickPulse.QuickPulseTelemetryProcessor, Microsoft.AI.PerfCounterCollector" />
      </TelemetryProcessors>
      <TelemetryProcessors xdt:Transform="Remove" xdt:Locator="Condition(count(*)=0)"/>
    </Add>
  </TelemetrySinks>

  <TelemetrySinks>
    <Add Name="default" xdt:Transform="Remove" xdt:Locator="Condition(count(*)=0)">
    </Add>
  </TelemetrySinks>

  <TelemetrySinks xdt:Transform="Remove" xdt:Locator="Condition(count(*)=0)"/>
  
</ApplicationInsights>