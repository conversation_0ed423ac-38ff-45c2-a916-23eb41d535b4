﻿using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.BusinessCenterPortal)]
    public class Project_Active
    {
        [SqlField(IsPrimaryKey = true)]
        public string ProjectID { get; set; }

        [SqlField]
        public string abbName { get; set; } = string.Empty;

        [SqlField]
        public string fullName { get; set; } = string.Empty;

        [SqlField]
        public string projectType { get; set; } = string.Empty;

        [SqlField]
        public string investFund { get; set; } = string.Empty;

        [SqlField]
        public string firstInvest { get; set; } = string.Empty;

        [SqlField]
        public string ownership { get; set; } = string.Empty;

        [SqlField]
        public string otherInvestor { get; set; } = string.Empty;

        [SqlField]
        public string BoardSeatType { get; set; } = string.Empty;
        [SqlField]
        public string oneLineDesc { get; set; } = string.Empty;
        [SqlField]
        public string currency { get; set; } = string.Empty;

        [SqlField]
        public string projectManager { get; set; } = string.Empty;
        [SqlField]
        public string projectManager2 { get; set; } = string.Empty;
        [SqlField]
        public string remark { get; set; } = string.Empty;
        [SqlField]
        public string InhouseCounsel { get; set; } = string.Empty;
        [SqlField]
        public decimal investedAmount { get; set; }

        [SqlField]
        public string shares { get; set; }= string.Empty;

        [SqlField]
        public decimal totalInvestAmount { get; set; }

        [SqlField]
        public decimal preMoney { get; set; }

        [SqlField]
        public decimal postMoney { get; set; }
        [SqlField]
        public String portfolio { get; set; } = String.Empty;

        [SqlField]
        public bool closed { get; set; }
        /// <summary>
        /// 项目最后编辑时间
        /// </summary>
        [SqlField]
        public DateTime modifiedDate { get; set; } = DateTime.Now;
        /// <summary>
        /// 创建时间
        /// </summary>
        [SqlField]
        public DateTime createdDate { get; set; } = DateTime.Now;

        [SqlField]
        public bool buyback { get; set; }


        public String modifiedDateStr { get; set; } = string.Empty;
        /// <summary>
        /// 创建时间
        /// </summary>
    
        public String createdDateStr { get; set; } = string.Empty;
    }

}
