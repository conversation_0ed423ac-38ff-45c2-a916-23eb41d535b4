﻿using System;
using MailKit.Net.Imap;
using MailKit.Search;
using MailKit.Net.Smtp;
using MailKit;
using MimeKit;

namespace Banyan.Code
{
	public class Mail
	{
		private IImapClient client;
		public static void test()
		{
			var message = new MimeMessage();
			message.From.Add(new MailboxAddress("test", "<EMAIL>"));
			message.To.Add(new MailboxAddress("test", "<EMAIL>"));
			message.Subject = "How you doin'?";

			message.Body = new TextPart("plain")
			{
				Text = @"Hey <PERSON>,

I just wanted to let you know that <PERSON> and I were going to go play some paintball, you in?

-- Joey"
			};

			using (var client = new SmtpClient())
			{
				client.Connect("smtp.qiye.aliyun.com", 25, false);

				// Note: only needed if the SMTP server requires authentication
				client.Authenticate("<EMAIL>", "ITit.123");

				client.Send(message);
				client.Disconnect(true);
			}
		}

		public static bool validCodeSend(string mail, string code)
		{
			var message = new MimeMessage();
			message.From.Add(new MailboxAddress("高榕创投投后服务平台", "<EMAIL>"));
			message.To.Add(new MailboxAddress("user", mail));
			message.Subject = "请查收您的登录验证码";

			message.Body = new TextPart("plain")
			{
				Text =
@"您好，
						  
        请及时使用您的登录验证码

        " + code + @"

        请不要直接回复此邮件。若遇到技术问题，请发邮件至 <EMAIL>，谢谢。



高榕创投


"
			};

			using (var client = new SmtpClient())
			{
				try
				{
					client.Connect("smtp.qiye.aliyun.com", 25, false);

					// Note: only needed if the SMTP server requires authentication
					client.Authenticate("<EMAIL>", "ITit.123");

					client.Send(message);
					client.Disconnect(true);
					return true;
				}
				catch (Exception e)
				{
					Logger.Error("验证码发送失败", e);
					return false;
				}
			}
		}


		public static bool botSendMailToAdmin(string subject, string content, string receiver = "")
		{
			var message = new MimeMessage();
			message.From.Add(new MailboxAddress("高榕创投IMS平台", "<EMAIL>"));
			if (!receiver.IsEmpty())
			{
				message.To.Add(new MailboxAddress("user", receiver));
			}
			message.Cc.Add(new MailboxAddress("user", "<EMAIL>"));
			message.Subject = subject;

			message.Body = new TextPart("plain")
			{
				Text = content

			};

			using (var client = new SmtpClient())
			{
				try
				{
					client.Connect("smtp.qiye.aliyun.com", 25, false);

					// Note: only needed if the SMTP server requires authentication
					client.Authenticate("<EMAIL>", "ITit.123");

					client.Send(message);
					client.Disconnect(true);
					return true;
				}
				catch (Exception e)
				{
					Logger.Error("邮件发送失败", e);
					return false;
				}
			}
		}
	}
}
