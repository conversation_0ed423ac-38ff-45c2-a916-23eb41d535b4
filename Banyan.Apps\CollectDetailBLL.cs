﻿using Banyan.Code;
using Banyan.Domain;
using System;
using System.Collections.Specialized;
using System.Linq;

namespace Banyan.Apps
{
    public class CollectDetailBLL : DAL.Base.BaseDAL<CollectDetail>
    {
        private readonly AjaxResult ajaxResult = null;

        public CollectDetailBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        /// <summary>
        /// 设置收藏
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public AjaxResult CollectSet(NameValueCollection paramValues)
        {
            AjaxResult ajaxResult = new AjaxResult();

            var aid = WebHelper.GetValueInt("articleid", 0, paramValues);
            var uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var mode = WebHelper.GetValueInt("mode", 0, paramValues);
            var setType = WebHelper.GetValue("setType", string.Empty, paramValues);

            if (mode == 0)
            {
                ArticleBLL articleBll = new ArticleBLL();
                var articleModel = articleBll.GetModel($"Id={aid}", null, "Id,CollectCount", "Id");
                if (articleModel == null)
                {
                    ajaxResult.code = (int)ResultCode.paramerror;
                    ajaxResult.msg = "文章不存在！";
                    return ajaxResult;
                }
                articleModel.CollectCount += setType.Equals("cancel") ? -1 : 1;
                if (articleModel.CollectCount >= 0)
                    articleBll.Update(articleModel, "CollectCount");
            }
            else if (mode == 1)
            {
                ProjectBLL projectBll = new ProjectBLL();
                var project = projectBll.GetModel($"Id={aid}", null, "Id,CollectCount", "Id");
                if (project == null)
                {
                    ajaxResult.code = (int)ResultCode.paramerror;
                    ajaxResult.msg = "文章不存在！";
                    return ajaxResult;
                }
                project.CollectCount += setType.Equals("cancel") ? -1 : 1;
                if (project.CollectCount >= 0)
                    projectBll.Update(project, "CollectCount");
            }

            if (setType.Equals("cancel"))
            {
                ajaxResult.code = DeleteByWhere($"UserId={uid} AND ArticleId={aid}") > 0 ? (int)ResultCode.success : (int)ResultCode.failed;
            }
            else if (setType.Equals("add"))
            {
                var collect = new CollectDetail
                {
                    UserId = uid,
                    ArticleId = aid,
                    Mode = (byte)mode,
                };
                ajaxResult.code = Convert.ToInt32(Add(collect)) > 0 ? (int)ResultCode.success : (int)ResultCode.failed;
            }
            else
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.paramerror);
                return ajaxResult;
            }

            return ajaxResult;
        }

        /// <summary>
        /// 判断用户是否收藏该文章
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="articleId"></param>
        /// <returns></returns>
        public bool IsCollect(int userId, int articleId, int mode = (int)CollectModeEnum.Interview)
        {
            return Exists($"UserId={userId} AND ArticleId={articleId} AND Mode={mode}");
        }
        public string GetCollects(int userId, int mode)
        {
            var userModel = new MemberBLL().GetModelByCache(userId);
            if (userModel == null || userModel.Status != (int)MemberStatus.enable)
            {
                return "";
            }

            string strSql = string.Empty;
            //if (!(userModel.Levels == (int)MemberLevels.Administrator || userModel.Levels == (int)MemberLevels.SuperUser))
            //{
            //    string roleIds = string.IsNullOrEmpty(userModel.Groups) ? "" : userModel.Groups;
            //    whereSql += $" AND CHARINDEX(',{roleIds},', CONCAT(',', ToRoleId, ',')) > 0";
            //}
            string IDList = "";
            if (mode == 0)
            {
                strSql = $"SELECT ArticleId FROM CollectDetail WHERE Mode = 0 AND UserId={userId} ";
            }
            else if (mode == 1)
            {
                strSql = $"SELECT ArticleId FROM CollectDetail WHERE Mode = 1 AND UserId={userId} ";
            }
            else
                return "";
            var articleIDList = new CollectDetailBLL().GetListBySql(strSql);

            if (articleIDList != null && articleIDList.Count() > 0)
            {
                for (int i = 0; i < articleIDList.Count(); i++)
                {
                    if (i == 0)
                        IDList = articleIDList[i].ArticleId.ToString();
                    else
                        IDList += "," + articleIDList[i].ArticleId.ToString();
                }
            }
            return IDList;
        }
        /// <summary>
        /// 收藏列表
        /// </summary>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        //public AjaxResult GetCollects_pass(NameValueCollection paramValues)
        //{
        //    int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
        //    int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
        //    int uid = WebHelper.GetValueInt("uid", 0, paramValues);

        //    int colId = 0;
        //    string classType = WebHelper.GetValue("classType", string.Empty, paramValues);
        //    if (classType.Equals("cases"))  //案例
        //    {
        //        colId = (int)ColumnId.cases;
        //    }
        //    else if (classType.Equals("weekly")) //周报
        //    {
        //        colId = (int)ColumnId.weekly;
        //    }
        //    else if (classType.Equals("subject")) //专题
        //    {
        //        colId = (int)ColumnId.subject;
        //    }
        //    if (colId <= 0) {
        //        ajaxResult.code = (int)ResultCode.paramerror;
        //        ajaxResult.msg = ResultHelper.ResultMsg(ResultCode.paramerror);
        //        return ajaxResult;
        //    }

        //    string sqlStr = $"SELECT a.Id,a.Title,a.Summary,a.CoverUrl,a.PriseCount,a.PubTime FROM CollectDetail as c INNER JOIN Article AS a ON a.Status={(int)ArticleStatus.normal} AND c.UserId = {uid} AND c.ArticleId = a.Id AND a.ColumnId = {colId}";

        //    var articleList = new ArticleBLL().GetListBySql(sqlStr);
        //    ajaxResult.code = (int)ResultCode.success;
        //    if (articleList != null && articleList.Count() > 0)
        //    {
        //        ajaxResult.data = articleList.Select(item =>
        //        {
        //            return new { item.Id, item.Title, item.Summary, item.CoverUrl, item.PriseCount, AddTime = item.AddTime.ToString("yyyy-MM-dd HH:mm") };
        //        });
        //    }

        //    return ajaxResult;
        //}

        public AjaxResult GetCollects(NameValueCollection paramValues)
        {
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            string keyWords = WebHelper.GetValue("keywords", string.Empty, paramValues);
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            int mode = WebHelper.GetValueInt("mode", 0, paramValues);

            var userModel = new MemberBLL().GetModelByCache(uid);
            if (userModel == null || userModel.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = userModel == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = userModel == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }

            string whereSql = string.Empty;
            string strSql = string.Empty;
            if (!string.IsNullOrEmpty(keyWords))
            {
                whereSql += mode == 0 ? $" AND Title LIKE '%{keyWords}%' " : $" AND Name LIKE '%{keyWords}%' ";
            }

            if (!(userModel.Levels == (int)MemberLevels.Administrator || userModel.Levels == (int)MemberLevels.SuperUser))
            {
                string roleIds = string.IsNullOrEmpty(userModel.Groups) ? "" : userModel.Groups;
                whereSql += $" AND CHARINDEX(',{roleIds},', CONCAT(',', ToRoleId, ',')) > 0";
            }

            if (mode == 0)
            {
                strSql = $"SELECT * FROM Article WHERE Id in(SELECT ArticleId FROM (SELECT ROW_NUMBER() OVER(ORDER BY T.Id DESC)AS Row, T.* FROM CollectDetail T WHERE Mode = 0 AND UserId={uid}) TT WHERE TT.Row BETWEEN {(pageIndex - 1) * pageSize + 1} AND {pageIndex * pageSize}) {whereSql}";
                var articleList = new ArticleBLL().GetListBySql(strSql);
                ajaxResult.code = (int)ResultCode.success;
                if (articleList != null && articleList.Count() > 0)
                {
                    ajaxResult.data = articleList.Select(item =>
                    {
                        return new { item.Id, item.Title, item.Summary, item.EditorName, item.PriseCount, AddTime = item.PubTime.ToString("yyyy-MM-dd") };
                    });
                }
            }
            else if (mode == 1)
            {
                strSql = $"SELECT * FROM Project WHERE Id in(SELECT ArticleId FROM (SELECT ROW_NUMBER() OVER(ORDER BY T.Id DESC)AS Row, T.* FROM CollectDetail T WHERE Mode = 1 AND UserId={uid}) TT WHERE TT.Row BETWEEN {(pageIndex - 1) * pageSize + 1} AND {pageIndex * pageSize}) {whereSql}";
                var articleList = new ProjectBLL().GetListBySql(strSql);
                ajaxResult.code = (int)ResultCode.success;
                if (articleList != null && articleList.Count() > 0)
                {
                    ajaxResult.data = articleList.Select(item =>
                    {
                        return new { item.Id, Title = item.Name, item.Summary, item.EditorName, item.PriseCount, AddTime = item.PubTime.ToString("yyyy-MM-dd") };
                    });
                }
            }

            return ajaxResult;
        }
    }
}
