<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod">
            <summary>
            用于执行的 TestMethod。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestMethodName">
            <summary>
            获取测试方法的名称。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestClassName">
            <summary>
            获取测试类的名称。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ReturnType">
            <summary>
            获取测试方法的返回类型。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ParameterTypes">
            <summary>
            获取测试方法的参数。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.MethodInfo">
            <summary>
            获取测试方法的 methodInfo。
            </summary>
            <remarks>
            This is just to retrieve additional information about the method.
            Do not directly invoke the method using MethodInfo. Use ITestMethod.Invoke instead.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.Invoke(System.Object[])">
            <summary>
            调用测试方法。
            </summary>
            <param name="arguments">
            传递到测试方法的参数(例如，对于数据驱动)
            </param>
            <returns>
            测试方法调用的结果。
            </returns>
            <remarks>
            This call handles asynchronous test methods as well.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAllAttributes(System.Boolean)">
            <summary>
            获取测试方法的所有属性。
            </summary>
            <param name="inherit">
            父类中定义的任何属性都有效。
            </param>
            <returns>
            所有特性。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAttributes``1(System.Boolean)">
            <summary>
            获取特定类型的属性。
            </summary>
            <typeparam name="AttributeType"> System.Attribute type. </typeparam>
            <param name="inherit">
            父类中定义的任何属性都有效。
            </param>
            <returns>
           指定类型的属性。
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Helper">
            <summary>
            帮助程序。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNull(System.Object,System.String,System.String)">
            <summary>
            非 null 的检查参数。
            </summary>
            <param name="param">
            参数。
            </param>
            <param name="parameterName">
            参数名称。
            </param>
            <param name="message">
            消息。
            </param>
            <exception cref="T:System.ArgumentNullException"> Throws argument null exception when parameter is null. </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNullOrEmpty(System.String,System.String,System.String)">
            <summary>
            不为 null 或不为空的检查参数。
            </summary>
            <param name="param">
            参数。
            </param>
            <param name="parameterName">
            参数名称。
            </param>
            <param name="message">
            消息。
            </param>
            <exception cref="T:System.ArgumentException"> Throws ArgumentException when parameter is null. </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod">
            <summary>
            枚举在数据驱动测试中访问数据行的方式。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Sequential">
            <summary>
            按连续顺序返回行。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random">
            <summary>
            按随机顺序返回行。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute">
            <summary>
            用于定义测试方法内联数据的属性。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> 类的新实例。
            </summary>
            <param name="data1"> 数据对象。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object,System.Object[])">
            <summary>
            初始化采用参数数组的 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> 类的新实例。
            </summary>
            <param name="data1"> 一个数据对象。 </param>
            <param name="moreData"> 更多数据。 </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.Data">
            <summary>
            获取数据以调用测试方法。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.DisplayName">
            <summary>
            在测试结果中为自定义获取或设置显示名称。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            <summary>
            断言无结论异常。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String,System.Exception)">
             <summary>
           初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> 类的新实例。
            </summary>
            <param name="msg"> 消息。 </param>
            <param name="ex"> 异常。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String)">
             <summary>
           初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> 类的新实例。
            </summary>
            <param name="msg"> 消息。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor">
             <summary>
           初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> 类的新实例。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException">
            <summary>
            InternalTestFailureException 类。用来指示测试用例的内部错误
            </summary>
            <remarks>
            This class is only added to preserve source compatibility with the V1 framework.
            For all practical purposes either use AssertFailedException/AssertInconclusiveException.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String,System.Exception)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> 类的新实例。
            </summary>
            <param name="msg"> 异常消息。</param>
            <param name="ex"> 异常。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> 类的新实例。
            </summary>
            <param name="msg"> 异常消息。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> 类的新实例。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute">
            <summary>
            指定引发指定类型异常的属性
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type)">
            <summary>
            初始化含有预期类型的 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> 类的新实例
            </summary>
            <param name="exceptionType">预期异常的类型</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type,System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> 类的新实例，
            测试未引发异常时，该类中会包含预期类型和消息。
            </summary>
            <param name="exceptionType">预期异常的类型</param>
            <param name="noExceptionMessage">
            测试由于未引发异常而失败时测试结果中要包含的消息
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.ExceptionType">
            <summary>
            获取指示预期异常类型的值
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.AllowDerivedTypes">
            <summary>
            获取或设置一个值，指示是否允许将派生自预期异常类型的类型
            作为预期类型
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.NoExceptionMessage">
            <summary>
            如果由于未引发异常导致测试失败，获取该消息以将其附加在测试结果中
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.Verify(System.Exception)">
            <summary>
            验证由单元测试引发的异常类型是否为预期类型
            </summary>
            <param name="exception">由单元测试引发的异常</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute">
            <summary>
            指定应从单元测试引发异常的属性基类
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor">
            <summary>
            初始化含有默认无异常消息的 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> 类的新实例
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor(System.String)">
            <summary>
            初始化含有一条无异常消息的 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> 类的新实例
            </summary>
            <param name="noExceptionMessage">
            测试由于未引发异常而失败时测试结果中要包含的
            消息
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.NoExceptionMessage">
            <summary>
            如果由于未引发异常导致测试失败，获取该消息以将其附加在测试结果中
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.SpecifiedNoExceptionMessage">
            <summary>
            如果由于未引发异常导致测试失败，获取该消息以将其附加在测试结果中
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.GetDefaultNoExceptionMessage(System.String)">
            <summary>
            获取默认无异常消息
            </summary>
            <param name="expectedExceptionAttributeTypeName">ExpectedException 特性类型名称</param>
            <returns>默认非异常消息</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.Verify(System.Exception)">
            <summary>
            确定该异常是否为预期异常。如果返回了方法，则表示
            该异常为预期异常。如果方法引发异常，则表示
            该异常不是预期异常，且引发的异常消息
            包含在测试结果中。为了方便，
            可使用 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert"/> 类。如果使用了 <see cref="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive"/> 且断言失败，
            则表示测试结果设置为了“无结论”。
            </summary>
            <param name="exception">由单元测试引发的异常</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.RethrowIfAssertException(System.Exception)">
            <summary>
            如果异常为 AssertFailedException 或 AssertInconclusiveException，则再次引发该异常
            </summary>
            <param name="exception">如果是断言异常则要重新引发的异常</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper">
            <summary>
            此类旨在帮助用户使用泛型类型为类型执行单元测试。
            GenericParameterHelper 满足某些常见的泛型类型限制，
            如:
            1.公共默认构造函数 
            2.实现公共接口: IComparable,IEnumerable
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> 类的新实例，
            该类满足 C# 泛型中的“可续订”约束。
            </summary>
            <remarks>
            This constructor initializes the Data property to a random value.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor(System.Int32)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> 类的新实例，
            该类将数据属性初始化为用户提供的值。
            </summary>
            <param name="data">任意整数值</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Data">
            <summary>
            获取或设置数据
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Equals(System.Object)">
            <summary>
            比较两个 GenericParameterHelper 对象的值
            </summary>
            <param name="obj">要进行比较的对象</param>
            <returns>如果 obj 与“此”GenericParameterHelper 对象具有相同的值，则为 true。
            反之则为 false。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetHashCode">
            <summary>
            为此对象返回哈希代码。
            </summary>
            <returns>哈希代码。</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.CompareTo(System.Object)">
            <summary>
            比较两个 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> 对象的数据。
            </summary>
            <param name="obj">要比较的对象。</param>
            <returns>
            有符号的数字表示此实例和值的相对值。
            </returns>
            <exception cref="T:System.NotSupportedException">
            Thrown when the object passed in is not an instance of <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetEnumerator">
            <summary>
            返回一个 IEnumerator 对象，该对象的长度派生自
            数据属性。
            </summary>
            <returns>IEnumerator 对象</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Clone">
            <summary>
            返回与当前对象相同的 GenericParameterHelper
            对象。
            </summary>
            <returns>克隆对象。</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger">
            <summary>
            允许用户记录/编写单元测试的跟踪以进行诊断。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessageHandler">
            <summary>
            用于 LogMessage 的处理程序。
            </summary>
            <param name="message">要记录的消息。</param>
        </member>
        <member name="E:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.OnLogMessage">
            <summary>
            要侦听的事件。单元测试编写器编写某些消息时引发。
            主要供适配器使用。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessage(System.String,System.Object[])">
            <summary>
            测试编写器要将其调用到日志消息的 API。
            </summary>
            <param name="format">带占位符的字符串格式。</param>
            <param name="args">占位符的参数。</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute">
            <summary>
            TestCategory 属性；用于指定单元测试的分类。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.#ctor(System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute"/> 类的新实例并将分类应用到该测试。
            </summary>
            <param name="testCategory">
            测试类别。
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.TestCategories">
            <summary>
            获取已应用到测试的测试类别。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute">
            <summary>
            "Category" 属性的基类
            </summary>
            <remarks>
            The reason for this attribute is to let the users create their own implementation of test categories.
            - test framework (discovery, etc) deals with TestCategoryBaseAttribute.
            - The reason that TestCategories property is a collection rather than a string,
              is to give more flexibility to the user. For instance the implementation may be based on enums for which the values can be OR'ed
              in which case it makes sense to have single attribute rather than multiple ones on the same test.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.#ctor">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute"/> 类的新实例。
            将分类应用到测试。TestCategories 返回的字符串
            与 /category 命令一起使用，以筛选测试
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.TestCategories">
            <summary>
            获取已应用到测试的测试分类。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            <summary>
            AssertFailedException 类。用于指示测试用例失败
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String,System.Exception)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> 类的新实例。
            </summary>
            <param name="msg"> 消息。 </param>
            <param name="ex"> 异常。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> 类的新实例。
            </summary>
            <param name="msg"> 消息。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> 类的新实例。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert">
            <summary>
            帮助程序类的集合，用于测试单元测试中
            的各种条件。如果不满足被测条件，则引发
            一个异常。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.That">
            <summary>
            获取 Assert 功能的单一实例。
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void IsOfType&lt;T&gt;(this Assert assert, object obj)"
            Users could then use a syntax similar to the default assertions which in this case is "Assert.That.IsOfType&lt;Dog&gt;(animal);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean)">
            <summary>
            测试指定条件是否为 true，
            如果该条件为 false，则引发一个异常。
            </summary>
            <param name="condition">
            测试预期为 true 的条件。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String)">
            <summary>
            测试指定条件是否为 true，
            如果该条件为 false，则引发一个异常。
            </summary>
            <param name="condition">
            测试预期为 true 的条件。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="condition"/>
            为 false。消息显示在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String,System.Object[])">
            <summary>
            测试指定条件是否为 true，
            如果该条件为 false，则引发一个异常。
            </summary>
            <param name="condition">
            测试预期为 true 的条件。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="condition"/>
            为 false。消息显示在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean)">
            <summary>
            测试指定条件是否为 false，如果条件为 true，
            则引发一个异常。
            </summary>
            <param name="condition">
            测试预期为 false 的条件。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String)">
            <summary>
            测试指定条件是否为 false，如果条件为 true，
            则引发一个异常。
            </summary>
            <param name="condition">
            测试预期为 false 的条件。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="condition"/>
            为 true。消息显示在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String,System.Object[])">
            <summary>
            测试指定条件是否为 false，如果条件为 true，
            则引发一个异常。
            </summary>
            <param name="condition">
            测试预期为 false 的条件。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="condition"/>
            为 true。消息显示在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object)">
            <summary>
            测试指定的对象是否为 null，如果不是，
            则引发一个异常。
            </summary>
            <param name="value">
            测试预期为 null 的对象。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String)">
            <summary>
            测试指定的对象是否为 null，如果不是，
            则引发一个异常。
            </summary>
            <param name="value">
            测试预期为 null 的对象。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="value"/>
            不为 null。消息显示在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String,System.Object[])">
            <summary>
            测试指定的对象是否为 null，如果不是，
            则引发一个异常。
            </summary>
            <param name="value">
            测试预期为 null 的对象。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="value"/>
            不为 null。消息显示在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object)">
            <summary>
            测试指定对象是否非 null，如果为 null，
            则引发一个异常。
            </summary>
            <param name="value">
            测试预期不为 null 的对象。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String)">
            <summary>
            测试指定对象是否非 null，如果为 null，
            则引发一个异常。
            </summary>
            <param name="value">
            测试预期不为 null 的对象。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="value"/>
            为 null。消息显示在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String,System.Object[])">
            <summary>
            测试指定对象是否非 null，如果为 null，
            则引发一个异常。
            </summary>
            <param name="value">
            测试预期不为 null 的对象。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="value"/>
            为 null。消息显示在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object)">
            <summary>
            测试指定的两个对象是否引用同一对象，
            如果两个输入不引用同一对象，则引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个对象。这是测试预期的值。
            </param>
            <param name="actual">
            要比较的第二个对象。这是测试下代码生成的值。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String)">
            <summary>
            测试指定的两个对象是否引用同一对象，
            如果两个输入不引用同一对象，则引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个对象。这是测试预期的值。
            </param>
            <param name="actual">
            要比较的第二个对象。这是测试下代码生成的值。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不相同 <paramref name="expected"/>。消息显示
            在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            测试指定的两个对象是否引用同一对象，
            如果两个输入不引用同一对象，则引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个对象。这是测试预期的值。
            </param>
            <param name="actual">
            要比较的第二个对象。这是测试下代码生成的值。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不相同 <paramref name="expected"/>。消息显示
            在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object)">
            <summary>
            测试指定的对象是否引用了不同对象，
           如果两个输入引用同一对象，则引发一个异常。
            </summary>
            <param name="notExpected">
            要比较的第一个对象。这是测试预期与
            以下内容不匹配的值: <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个对象。这是测试下代码生成的值。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String)">
            <summary>
            测试指定的对象是否引用了不同对象，
           如果两个输入引用同一对象，则引发一个异常。
            </summary>
            <param name="notExpected">
            要比较的第一个对象。这是测试预期与
            以下内容不匹配的值: <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个对象。这是测试下代码生成的值。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            相同 <paramref name="notExpected"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            测试指定的对象是否引用了不同对象，
           如果两个输入引用同一对象，则引发一个异常。
            </summary>
            <param name="notExpected">
            要比较的第一个对象。这是测试预期与
            以下内容不匹配的值: <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个对象。这是测试下代码生成的值。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            相同 <paramref name="notExpected"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0)">
            <summary>
            测试指定值是否相等，
            如果两个值不相等，则引发一个异常。即使逻辑值相等，不同的数字类型也被视为
            不相等。42L 不等于 42。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            要比较的第一个值。这是测试预期的值。
            </param>
            <param name="actual">
            要比较的第二个值。这是测试下代码生成的值。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String)">
            <summary>
            测试指定值是否相等，
            如果两个值不相等，则引发一个异常。即使逻辑值相等，不同的数字类型也被视为
            不相等。42L 不等于 42。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            要比较的第一个值。这是测试预期的值。
            </param>
            <param name="actual">
            要比较的第二个值。这是测试下代码生成的值。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不等于 <paramref name="expected"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            测试指定值是否相等，
            如果两个值不相等，则引发一个异常。即使逻辑值相等，不同的数字类型也被视为
            不相等。42L 不等于 42。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            要比较的第一个值。这是测试预期的值。
            </param>
            <param name="actual">
            要比较的第二个值。这是测试下代码生成的值。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不等于 <paramref name="expected"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0)">
            <summary>
            测试指定的值是否不相等，
            如果两个值相等，则引发一个异常。即使逻辑值相等，不同的数字类型也被视为
            不相等。42L 不等于 42。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            要比较的第一个值。这是测试预期不匹配
            的值 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个值。这是测试下代码生成的值。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String)">
            <summary>
            测试指定的值是否不相等，
            如果两个值相等，则引发一个异常。即使逻辑值相等，不同的数字类型也被视为
            不相等。42L 不等于 42。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            要比较的第一个值。这是测试预期不匹配
            的值 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个值。这是测试下代码生成的值。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            等于 <paramref name="notExpected"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            测试指定的值是否不相等，
            如果两个值相等，则引发一个异常。即使逻辑值相等，不同的数字类型也被视为
            不相等。42L 不等于 42。
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            要比较的第一个值。这是测试预期不匹配
            的值 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个值。这是测试下代码生成的值。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            等于 <paramref name="notExpected"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object)">
            <summary>
            测试指定对象是否相等，
            如果两个对象不相等，则引发一个异常。即使逻辑值相等，
            不同的数字类型也被视为不相等。42L 不等于 42。
            </summary>
            <param name="expected">
            要比较的第一个对象。这是测试预期的对象。
            </param>
            <param name="actual">
            要比较的第二个对象。这是在测试下由代码生成的对象。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String)">
            <summary>
            测试指定对象是否相等，
            如果两个对象不相等，则引发一个异常。即使逻辑值相等，
            不同的数字类型也被视为不相等。42L 不等于 42。
            </summary>
            <param name="expected">
            要比较的第一个对象。这是测试预期的对象。
            </param>
            <param name="actual">
            要比较的第二个对象。这是在测试下由代码生成的对象。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不等于 <paramref name="expected"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            测试指定对象是否相等，
            如果两个对象不相等，则引发一个异常。即使逻辑值相等，
            不同的数字类型也被视为不相等。42L 不等于 42。
            </summary>
            <param name="expected">
            要比较的第一个对象。这是测试预期的对象。
            </param>
            <param name="actual">
            要比较的第二个对象。这是在测试下由代码生成的对象。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不等于 <paramref name="expected"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object)">
            <summary>
            测试指定对象是否不相等，
            如果相等，则引发一个异常。即使逻辑值相等，不同的数字类型也被视为
            不相等。42L 不等于 42。
            </summary>
            <param name="notExpected">
            要比较的第一个对象。这是测试预期与
            以下内容不匹配的值: <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个对象。这是在测试下由代码生成的对象。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String)">
            <summary>
            测试指定对象是否不相等，
            如果相等，则引发一个异常。即使逻辑值相等，不同的数字类型也被视为
            不相等。42L 不等于 42。
            </summary>
            <param name="notExpected">
            要比较的第一个对象。这是测试预期与
            以下内容不匹配的值: <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个对象。这是在测试下由代码生成的对象。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            等于 <paramref name="notExpected"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            测试指定对象是否不相等，
            如果相等，则引发一个异常。即使逻辑值相等，不同的数字类型也被视为
            不相等。42L 不等于 42。
            </summary>
            <param name="notExpected">
            要比较的第一个对象。这是测试预期与
            以下内容不匹配的值: <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个对象。这是在测试下由代码生成的对象。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            等于 <paramref name="notExpected"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single)">
            <summary>
            测试指定的浮点型是否相等，
            如果不相等，则引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个浮点型。这是测试预期的浮点型。
            </param>
            <param name="actual">
            要比较的第二个浮点型。这是测试下代码生成的浮点型。
            </param>
            <param name="delta">
            所需准确度。仅在以下情况下引发异常:
            <paramref name="actual"/> 不同于 <paramref name="expected"/>
            超过 <paramref name="delta"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            测试指定的浮点型是否相等，
            如果不相等，则引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个浮点型。这是测试预期的浮点型。
            </param>
            <param name="actual">
            要比较的第二个浮点型。这是测试下代码生成的浮点型。
            </param>
            <param name="delta">
            所需准确度。仅在以下情况下引发异常:
            <paramref name="actual"/> 不同于 <paramref name="expected"/>
            超过 <paramref name="delta"/>。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不同于 <paramref name="expected"/> 多于
            <paramref name="delta"/>。消息显示在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            测试指定的浮点型是否相等，
            如果不相等，则引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个浮点型。这是测试预期的浮点型。
            </param>
            <param name="actual">
            要比较的第二个浮点型。这是测试下代码生成的浮点型。
            </param>
            <param name="delta">
            所需准确度。仅在以下情况下引发异常:
            <paramref name="actual"/> 不同于 <paramref name="expected"/>
            超过 <paramref name="delta"/>。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不同于 <paramref name="expected"/> 多于
            <paramref name="delta"/>。消息显示在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single)">
            <summary>
            测试指定的浮点型是否不相等，
            如果相等，则引发一个异常。
            </summary>
            <param name="notExpected">
            要比较的第一个浮动。这是测试预期与
            以下内容匹配的浮动: <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个浮点型。这是测试下代码生成的浮点型。
            </param>
            <param name="delta">
            所需准确度。仅在以下情况下引发异常:
            <paramref name="actual"/> 不同于 <paramref name="notExpected"/>
            最多 <paramref name="delta"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            测试指定的浮点型是否不相等，
            如果相等，则引发一个异常。
            </summary>
            <param name="notExpected">
            要比较的第一个浮动。这是测试预期与
            以下内容匹配的浮动: <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个浮点型。这是测试下代码生成的浮点型。
            </param>
            <param name="delta">
            所需准确度。仅在以下情况下引发异常:
            <paramref name="actual"/> 不同于 <paramref name="notExpected"/>
            最多 <paramref name="delta"/>。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            等于 <paramref name="notExpected"/> 或相差少于
            <paramref name="delta"/>。消息显示在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            测试指定的浮点型是否不相等，
            如果相等，则引发一个异常。
            </summary>
            <param name="notExpected">
            要比较的第一个浮动。这是测试预期与
            以下内容匹配的浮动: <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个浮点型。这是测试下代码生成的浮点型。
            </param>
            <param name="delta">
            所需准确度。仅在以下情况下引发异常:
            <paramref name="actual"/> 不同于 <paramref name="notExpected"/>
            最多 <paramref name="delta"/>。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            等于 <paramref name="notExpected"/> 或相差少于
            <paramref name="delta"/>。消息显示在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double)">
            <summary>
            测试指定的双精度型是否相等。如果不相等，
            则引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个双精度型。这是测试预期的双精度型。
            </param>
            <param name="actual">
            要比较的第二个双精度型。这是测试下代码生成的双精度型。
            </param>
            <param name="delta">
            所需准确度。仅在以下情况下引发异常:
            <paramref name="actual"/> 不同于 <paramref name="expected"/>
            超过 <paramref name="delta"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            测试指定的双精度型是否相等。如果不相等，
            则引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个双精度型。这是测试预期的双精度型。
            </param>
            <param name="actual">
            要比较的第二个双精度型。这是测试下代码生成的双精度型。
            </param>
            <param name="delta">
            所需准确度。仅在以下情况下引发异常:
            <paramref name="actual"/> 不同于 <paramref name="expected"/>
            超过 <paramref name="delta"/>。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不同于 <paramref name="expected"/> 多于
            <paramref name="delta"/>。消息显示在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            测试指定的双精度型是否相等。如果不相等，
            则引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个双精度型。这是测试预期的双精度型。
            </param>
            <param name="actual">
            要比较的第二个双精度型。这是测试下代码生成的双精度型。
            </param>
            <param name="delta">
            所需准确度。仅在以下情况下引发异常:
            <paramref name="actual"/> 不同于 <paramref name="expected"/>
            超过 <paramref name="delta"/>。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不同于 <paramref name="expected"/> 多于
            <paramref name="delta"/>。消息显示在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double)">
            <summary>
            测试指定的双精度型是否不相等，
            如果相等，则引发一个异常。
            </summary>
            <param name="notExpected">
            要比较的第一个双精度型。这是测试预期不匹配
            的双精度型<paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个双精度型。这是测试下代码生成的双精度型。
            </param>
            <param name="delta">
            所需准确度。仅在以下情况下引发异常:
            <paramref name="actual"/> 不同于 <paramref name="notExpected"/>
            最多 <paramref name="delta"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            测试指定的双精度型是否不相等，
            如果相等，则引发一个异常。
            </summary>
            <param name="notExpected">
            要比较的第一个双精度型。这是测试预期不匹配
            的双精度型<paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个双精度型。这是测试下代码生成的双精度型。
            </param>
            <param name="delta">
            所需准确度。仅在以下情况下引发异常:
            <paramref name="actual"/> 不同于 <paramref name="notExpected"/>
            最多 <paramref name="delta"/>。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            等于 <paramref name="notExpected"/> 或相差少于
            <paramref name="delta"/>。消息显示在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            测试指定的双精度型是否不相等，
            如果相等，则引发一个异常。
            </summary>
            <param name="notExpected">
            要比较的第一个双精度型。这是测试预期不匹配
            的双精度型<paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个双精度型。这是测试下代码生成的双精度型。
            </param>
            <param name="delta">
            所需准确度。仅在以下情况下引发异常:
            <paramref name="actual"/> 不同于 <paramref name="notExpected"/>
            最多 <paramref name="delta"/>。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            等于 <paramref name="notExpected"/> 或相差少于
            <paramref name="delta"/>。消息显示在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean)">
            <summary>
            测试指定的字符串是否相等，
            如果不相等，则引发一个异常。使用固定区域性进行比较。
            </summary>
            <param name="expected">
            要比较的第一个字符串。这是测试预期的字符串。
            </param>
            <param name="actual">
            要比较的第二个字符串。这是在测试下由代码生成的字符串。
            </param>
            <param name="ignoreCase">
            指示区分大小写或不区分大小写的比较的布尔。 (true
            指示区分大小写的比较。)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            测试指定的字符串是否相等，
            如果不相等，则引发一个异常。使用固定区域性进行比较。
            </summary>
            <param name="expected">
            要比较的第一个字符串。这是测试预期的字符串。
            </param>
            <param name="actual">
            要比较的第二个字符串。这是在测试下由代码生成的字符串。
            </param>
            <param name="ignoreCase">
            指示区分大小写或不区分大小写的比较的布尔。 (true
            指示区分大小写的比较。)
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不等于 <paramref name="expected"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            测试指定的字符串是否相等，
            如果不相等，则引发一个异常。使用固定区域性进行比较。
            </summary>
            <param name="expected">
            要比较的第一个字符串。这是测试预期的字符串。
            </param>
            <param name="actual">
            要比较的第二个字符串。这是在测试下由代码生成的字符串。
            </param>
            <param name="ignoreCase">
            指示区分大小写或不区分大小写的比较的布尔。 (true
            指示区分大小写的比较。)
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不等于 <paramref name="expected"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            测试指定的字符串是否相等，如果不相等，
            则引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个字符串。这是测试预期的字符串。
            </param>
            <param name="actual">
            要比较的第二个字符串。这是在测试下由代码生成的字符串。
            </param>
            <param name="ignoreCase">
            指示区分大小写或不区分大小写的比较的布尔。 (true
            指示区分大小写的比较。)
            </param>
            <param name="culture">
            提供区域性特定比较信息的 CultureInfo 对象。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            测试指定的字符串是否相等，如果不相等，
            则引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个字符串。这是测试预期的字符串。
            </param>
            <param name="actual">
            要比较的第二个字符串。这是在测试下由代码生成的字符串。
            </param>
            <param name="ignoreCase">
            指示区分大小写或不区分大小写的比较的布尔。 (true
            指示区分大小写的比较。)
            </param>
            <param name="culture">
            提供区域性特定比较信息的 CultureInfo 对象。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不等于 <paramref name="expected"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            测试指定的字符串是否相等，如果不相等，
            则引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个字符串。这是测试预期的字符串。
            </param>
            <param name="actual">
            要比较的第二个字符串。这是在测试下由代码生成的字符串。
            </param>
            <param name="ignoreCase">
            指示区分大小写或不区分大小写的比较的布尔。 (true
            指示区分大小写的比较。)
            </param>
            <param name="culture">
            提供区域性特定比较信息的 CultureInfo 对象。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不等于 <paramref name="expected"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean)">
            <summary>
            测试指定字符串是否不相等，
            如果相等，则引发一个异常。使用固定区域性进行比较。
            </summary>
            <param name="notExpected">
            要比较的第一个字符串。 这是测试预期不匹配的
            字符串 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个字符串。这是在测试下由代码生成的字符串。
            </param>
            <param name="ignoreCase">
            指示区分大小写或不区分大小写的比较的布尔。 (true
            指示区分大小写的比较。)
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            测试指定字符串是否不相等，
            如果相等，则引发一个异常。使用固定区域性进行比较。
            </summary>
            <param name="notExpected">
            要比较的第一个字符串。 这是测试预期不匹配的
            字符串 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个字符串。这是在测试下由代码生成的字符串。
            </param>
            <param name="ignoreCase">
            指示区分大小写或不区分大小写的比较的布尔。 (true
            指示区分大小写的比较。)
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            等于 <paramref name="notExpected"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            测试指定字符串是否不相等，
            如果相等，则引发一个异常。使用固定区域性进行比较。
            </summary>
            <param name="notExpected">
            要比较的第一个字符串。 这是测试预期不匹配的
            字符串 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个字符串。这是在测试下由代码生成的字符串。
            </param>
            <param name="ignoreCase">
            指示区分大小写或不区分大小写的比较的布尔。 (true
            指示区分大小写的比较。)
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            等于 <paramref name="notExpected"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            测试指定的字符串是否不相等，
            如果相等，则引发一个异常。
            </summary>
            <param name="notExpected">
            要比较的第一个字符串。 这是测试预期不匹配的
            字符串 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个字符串。这是在测试下由代码生成的字符串。
            </param>
            <param name="ignoreCase">
            指示区分大小写或不区分大小写的比较的布尔。 (true
            指示区分大小写的比较。)
            </param>
            <param name="culture">
            提供区域性特定比较信息的 CultureInfo 对象。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            测试指定的字符串是否不相等，
            如果相等，则引发一个异常。
            </summary>
            <param name="notExpected">
            要比较的第一个字符串。 这是测试预期不匹配的
            字符串 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个字符串。这是在测试下由代码生成的字符串。
            </param>
            <param name="ignoreCase">
            指示区分大小写或不区分大小写的比较的布尔。 (true
            指示区分大小写的比较。)
            </param>
            <param name="culture">
            提供区域性特定比较信息的 CultureInfo 对象。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            等于 <paramref name="notExpected"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            测试指定的字符串是否不相等，
            如果相等，则引发一个异常。
            </summary>
            <param name="notExpected">
            要比较的第一个字符串。 这是测试预期不匹配的
            字符串 <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个字符串。这是在测试下由代码生成的字符串。
            </param>
            <param name="ignoreCase">
            指示区分大小写或不区分大小写的比较的布尔。 (true
            指示区分大小写的比较。)
            </param>
            <param name="culture">
            提供区域性特定比较信息的 CultureInfo 对象。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            等于 <paramref name="notExpected"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type)">
            <summary>
            测试指定的对象是否是预期类型的一个实例，
            如果预期类型不位于对象的继承分层中，
            则引发一个异常。
            </summary>
            <param name="value">
            测试预期为指定类型的对象。
            </param>
            <param name="expectedType">
            预期类型<paramref name="value"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            测试指定的对象是否是预期类型的一个实例，
            如果预期类型不位于对象的继承分层中，
            则引发一个异常。
            </summary>
            <param name="value">
            测试预期为指定类型的对象。
            </param>
            <param name="expectedType">
            预期类型<paramref name="value"/>。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="value"/>
            不是一个实例<paramref name="expectedType"/>。消息
            显示在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            测试指定的对象是否是预期类型的一个实例，
            如果预期类型不位于对象的继承分层中，
            则引发一个异常。
            </summary>
            <param name="value">
            测试预期为指定类型的对象。
            </param>
            <param name="expectedType">
            预期类型<paramref name="value"/>。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="value"/>
            不是一个实例<paramref name="expectedType"/>。消息
            显示在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type)">
            <summary>
            测试指定对象是否不是一个错误
            类型实例，如果指定类型位于对象的
            继承层次结构中，则引发一个异常。
            </summary>
            <param name="value">
            测试预期不是指定类型的对象。
            </param>
            <param name="wrongType">
            类型 <paramref name="value"/> 不应。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            测试指定对象是否不是一个错误
            类型实例，如果指定类型位于对象的
            继承层次结构中，则引发一个异常。
            </summary>
            <param name="value">
            测试预期不是指定类型的对象。
            </param>
            <param name="wrongType">
            类型 <paramref name="value"/> 不应。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="value"/>
            是一个实例<paramref name="wrongType"/>。消息显示
            在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            测试指定对象是否不是一个错误
            类型实例，如果指定类型位于对象的
            继承层次结构中，则引发一个异常。
            </summary>
            <param name="value">
            测试预期不是指定类型的对象。
            </param>
            <param name="wrongType">
            类型 <paramref name="value"/> 不应。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="value"/>
            是一个实例<paramref name="wrongType"/>。消息显示
            在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail">
            <summary>
            引发 AssertFailedException。
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String)">
            <summary>
            引发 AssertFailedException。
            </summary>
            <param name="message">
            包含在异常中的消息。信息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String,System.Object[])">
            <summary>
            引发 AssertFailedException。
            </summary>
            <param name="message">
            包含在异常中的消息。信息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive">
            <summary>
            引发 AssertInconclusiveException。
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String)">
            <summary>
            引发 AssertInconclusiveException。
            </summary>
            <param name="message">
            包含在异常中的消息。信息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String,System.Object[])">
            <summary>
            引发 AssertInconclusiveException。
            </summary>
            <param name="message">
            包含在异常中的消息。信息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Equals(System.Object,System.Object)">
            <summary>
            静态相等重载用于比较两种类型实例的引用
            相等。此方法<b>不</b>应用于比较两个实例的
            相等。此对象<b>始终</b>会引发 Assert.Fail。请在单元测试中使用
            Assert.AreEqual 和关联的重载。
            </summary>
            <param name="objA"> 对象 A </param>
            <param name="objB"> 对象 B </param>
            <returns> 始终为 False。 </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action)">
            <summary>
            测试委托 <paramref name="action"/> 指定的代码是否能准确引发指定类型 <typeparamref name="T"/> 异常(非派生类型异常)，
            且
            如果代码不引发异常或引发非 <typeparamref name="T"/> 类型的异常，则引发
            <code>
            AssertFailedException
            </code>。
            </summary>
            <param name="action">
            委托到要进行测试且预期将引发异常的代码。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            应该引发的异常类型。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String)">
            <summary>
            测试委托 <paramref name="action"/> 指定的代码是否能准确引发指定类型 <typeparamref name="T"/> 异常(非派生类型异常)，
            且
            如果代码不引发异常或引发非 <typeparamref name="T"/> 类型的异常，则引发
            <code>
            AssertFailedException
            </code>。
            </summary>
            <param name="action">
            委托到要进行测试且预期将引发异常的代码。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="action"/>
            不引发类型的异常 <typeparamref name="T"/>。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            应该引发的异常类型。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object})">
            <summary>
            测试委托 <paramref name="action"/> 指定的代码是否能准确引发指定类型 <typeparamref name="T"/> 异常(非派生类型异常)，
            且
            如果代码不引发异常或引发非 <typeparamref name="T"/> 类型的异常，则引发
            <code>
            AssertFailedException
            </code>。
            </summary>
            <param name="action">
            委托到要进行测试且预期将引发异常的代码。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            应该引发的异常类型。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String)">
            <summary>
            测试委托 <paramref name="action"/> 指定的代码是否能准确引发指定类型 <typeparamref name="T"/> 异常(非派生类型异常)，
            且
            如果代码不引发异常或引发非 <typeparamref name="T"/> 类型的异常，则引发
            <code>
            AssertFailedException
            </code>。
            </summary>
            <param name="action">
            委托到要进行测试且预期将引发异常的代码。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="action"/>
            不引发类型的异常 <typeparamref name="T"/>。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            应该引发的异常类型。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String,System.Object[])">
            <summary>
            测试委托 <paramref name="action"/> 指定的代码是否能准确引发指定类型 <typeparamref name="T"/> 异常(非派生类型异常)，
            且
            如果代码不引发异常或引发非 <typeparamref name="T"/> 类型的异常，则引发
            <code>
            AssertFailedException
            </code>。
            </summary>
            <param name="action">
            委托到要进行测试且预期将引发异常的代码。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="action"/>
            不引发类型的异常 <typeparamref name="T"/>。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throw exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            应该引发的异常类型。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String,System.Object[])">
            <summary>
            测试委托 <paramref name="action"/> 指定的代码是否能准确引发指定类型 <typeparamref name="T"/> 异常(非派生类型异常)，
            且
            如果代码不引发异常或引发非 <typeparamref name="T"/> 类型的异常，则引发
            <code>
            AssertFailedException
            </code>。
            </summary>
            <param name="action">
            委托到要进行测试且预期将引发异常的代码。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="action"/>
            不引发类型的异常 <typeparamref name="T"/>。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            应该引发的异常类型。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task})">
            <summary>
            测试委托 <paramref name="action"/> 指定的代码是否能准确引发指定类型 <typeparamref name="T"/> 异常(非派生类型异常)，
            且
            如果代码不引发异常或引发非 <typeparamref name="T"/> 类型的异常，则引发
            <code>
            AssertFailedException
            </code>。
            </summary>
            <param name="action">
            委托到要进行测试且预期将引发异常的代码。
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            该 <see cref="T:System.Threading.Tasks.Task"/>执行委托。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            测试委托 <paramref name="action"/> 指定的代码是否能准确引发指定类型 <typeparamref name="T"/> 异常(非派生类型异常)，
            如果代码不引发异常或引发非 <typeparamref name="T"/> 类型的异常，则引发 <code>AssertFailedException</code>。
            </summary>
            <param name="action">委托到要进行测试且预期将引发异常的代码。</param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="action"/>
            不引发异常类型<typeparamref name="T"/>。
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            该 <see cref="T:System.Threading.Tasks.Task"/>执行委托。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String,System.Object[])">
            <summary>
            测试委托 <paramref name="action"/> 指定的代码是否能准确引发指定类型 <typeparamref name="T"/> 异常(非派生类型异常)，
            如果代码不引发异常或引发非 <typeparamref name="T"/> 类型的异常，则引发 <code>AssertFailedException</code>。
            </summary>
            <param name="action">委托到要进行测试且预期将引发异常的代码。</param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="action"/>
            不引发异常类型<typeparamref name="T"/>。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            该 <see cref="T:System.Threading.Tasks.Task"/>执行委托。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNullChars(System.String)">
            <summary>
            将 null 字符("\0")替换为 "\\0"。
            </summary>
            <param name="input">
            要搜索的字符串。
            </param>
            <returns>
            其中 null 字符替换为 "\\0" 的转换字符串。
            </returns>
            <remarks>
            This is only public and still present to preserve compatibility with the V1 framework.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.HandleFail(System.String,System.String,System.Object[])">
            <summary>
            用于创建和引发 AssertionFailedException 的帮助程序函数
            </summary>
            <param name="assertionName">
            引发异常的断言名称
            </param>
            <param name="message">
            描述断言失败条件的消息
            </param>
            <param name="parameters">
            参数。
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.CheckParameterNotNull(System.Object,System.String,System.String,System.String,System.Object[])">
            <summary>
            检查有效条件的参数
            </summary>
            <param name="param">
            参数。
            </param>
            <param name="assertionName">
            断言名称。
            </param>
            <param name="parameterName">
            参数名称
            </param>
            <param name="message">
            无效参数异常的消息
            </param>
            <param name="parameters">
            参数。
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNulls(System.Object)">
            <summary>
            将对象安全地转换为字符串，处理 null 值和 null 字符。
            将 null 值转换为 "(null)"。将 null 字符转换为 "\\0"。
            </summary>
            <param name="input">
            要转换为字符串的对象。
            </param>
            <returns>
            转换的字符串。
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert">
            <summary>
            字符串断言。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.That">
            <summary>
            获取 CollectionAssert 功能的单一实例。
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void ContainsWords(this StringAssert cusomtAssert, string value, ICollection substrings)"
            Users could then use a syntax similar to the default assertions which in this case is "StringAssert.That.ContainsWords(value, substrings);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String)">
            <summary>
            测试指定字符串是否包含指定子字符串，
            如果子字符串未出现在
            测试字符串中，则引发一个异常。
            </summary>
            <param name="value">
            预期要包含的字符串 <paramref name="substring"/>。
            </param>
            <param name="substring">
            字符串，预期出现在 <paramref name="value"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String)">
            <summary>
            测试指定字符串是否包含指定子字符串，
            如果子字符串未出现在
            测试字符串中，则引发一个异常。
            </summary>
            <param name="value">
            预期要包含的字符串 <paramref name="substring"/>。
            </param>
            <param name="substring">
            字符串，预期出现在 <paramref name="value"/>。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="substring"/>
            未处于 <paramref name="value"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String,System.Object[])">
            <summary>
            测试指定字符串是否包含指定子字符串，
            如果子字符串未出现在
            测试字符串中，则引发一个异常。
            </summary>
            <param name="value">
            预期要包含的字符串 <paramref name="substring"/>。
            </param>
            <param name="substring">
            字符串，预期出现在 <paramref name="value"/>。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="substring"/>
            未处于 <paramref name="value"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String)">
            <summary>
            测试指定的字符串是否以指定的子字符串开头，
           如果测试字符串不以该子字符串开头，
           则引发一个异常。
            </summary>
            <param name="value">
            字符串，预期开头为<paramref name="substring"/>。
            </param>
            <param name="substring">
            预期是前缀的字符串<paramref name="value"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String)">
            <summary>
            测试指定的字符串是否以指定的子字符串开头，
           如果测试字符串不以该子字符串开头，
           则引发一个异常。
            </summary>
            <param name="value">
            字符串，预期开头为<paramref name="substring"/>。
            </param>
            <param name="substring">
            预期是前缀的字符串<paramref name="value"/>。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="value"/>
            开头不为 <paramref name="substring"/>。消息
            显示在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            测试指定的字符串是否以指定的子字符串开头，
           如果测试字符串不以该子字符串开头，
           则引发一个异常。
            </summary>
            <param name="value">
            字符串，预期开头为<paramref name="substring"/>。
            </param>
            <param name="substring">
            预期是前缀的字符串<paramref name="value"/>。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="value"/>
            开头不为 <paramref name="substring"/>。消息
            显示在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String)">
            <summary>
            测试指定字符串是否以指定子字符串结尾，
            如果测试字符串不以子字符串结尾，
            则引发一个异常。
            </summary>
            <param name="value">
            字符串，其结尾应为<paramref name="substring"/>。
            </param>
            <param name="substring">
            预期是后缀的字符串<paramref name="value"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String)">
            <summary>
            测试指定字符串是否以指定子字符串结尾，
            如果测试字符串不以子字符串结尾，
            则引发一个异常。
            </summary>
            <param name="value">
            字符串，其结尾应为<paramref name="substring"/>。
            </param>
            <param name="substring">
            预期是后缀的字符串<paramref name="value"/>。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="value"/>
            结尾不为 <paramref name="substring"/>。消息
            显示在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            测试指定字符串是否以指定子字符串结尾，
            如果测试字符串不以子字符串结尾，
            则引发一个异常。
            </summary>
            <param name="value">
            字符串，其结尾应为<paramref name="substring"/>。
            </param>
            <param name="substring">
            预期是后缀的字符串<paramref name="value"/>。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="value"/>
            结尾不为 <paramref name="substring"/>。消息
            显示在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            测试指定的字符串是否匹配正则表达式，如果字符串不匹配正则表达式，则
            引发一个异常。
            </summary>
            <param name="value">
            预期匹配的字符串 <paramref name="pattern"/>。
            </param>
            <param name="pattern">
            正则表达式 <paramref name="value"/> 应
            匹配。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            测试指定的字符串是否匹配正则表达式，如果字符串不匹配正则表达式，则
            引发一个异常。
            </summary>
            <param name="value">
            预期匹配的字符串 <paramref name="pattern"/>。
            </param>
            <param name="pattern">
            正则表达式 <paramref name="value"/> 应
            匹配。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="value"/>
            不匹配 <paramref name="pattern"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            测试指定的字符串是否匹配正则表达式，如果字符串不匹配正则表达式，则
            引发一个异常。
            </summary>
            <param name="value">
            预期匹配的字符串 <paramref name="pattern"/>。
            </param>
            <param name="pattern">
            正则表达式 <paramref name="value"/> 应
            匹配。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="value"/>
            不匹配 <paramref name="pattern"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            测试指定字符串是否与正则表达式不匹配，
            如果字符串与表达式匹配，则引发一个异常。
            </summary>
            <param name="value">
            预期不匹配的字符串<paramref name="pattern"/>。
            </param>
            <param name="pattern">
            正则表达式 <paramref name="value"/>预期
            为不匹配。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            测试指定字符串是否与正则表达式不匹配，
            如果字符串与表达式匹配，则引发一个异常。
            </summary>
            <param name="value">
            预期不匹配的字符串<paramref name="pattern"/>。
            </param>
            <param name="pattern">
            正则表达式 <paramref name="value"/>预期
            为不匹配。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="value"/>
            匹配 <paramref name="pattern"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            测试指定字符串是否与正则表达式不匹配，
            如果字符串与表达式匹配，则引发一个异常。
            </summary>
            <param name="value">
            预期不匹配的字符串<paramref name="pattern"/>。
            </param>
            <param name="pattern">
            正则表达式 <paramref name="value"/>预期
            为不匹配。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="value"/>
            匹配 <paramref name="pattern"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert">
            <summary>
            帮助程序类的集合，用于测试与单元测试内的集合相关联的
           多种条件。如果不满足被测条件，
           则引发一个异常。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.That">
            <summary>
            获取 CollectionAssert 功能的单一实例。
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void AreEqualUnordered(this CollectionAssert cusomtAssert, ICollection expected, ICollection actual)"
            Users could then use a syntax similar to the default assertions which in this case is "CollectionAssert.That.AreEqualUnordered(list1, list2);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object)">
            <summary>
            测试指定集合是否包含指定元素，
            如果集合不包含该元素，则引发一个异常。
            </summary>
            <param name="collection">
            要在其中搜索元素的集合。
            </param>
            <param name="element">
            预期位于集合中的元素。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            测试指定集合是否包含指定元素，
            如果集合不包含该元素，则引发一个异常。
            </summary>
            <param name="collection">
            要在其中搜索元素的集合。
            </param>
            <param name="element">
            预期位于集合中的元素。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="element"/>
            未处于 <paramref name="collection"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            测试指定集合是否包含指定元素，
            如果集合不包含该元素，则引发一个异常。
            </summary>
            <param name="collection">
            要在其中搜索元素的集合。
            </param>
            <param name="element">
            预期位于集合中的元素。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="element"/>
            未处于 <paramref name="collection"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object)">
            <summary>
            测试指定的集合是否不包含指定
            元素，如果集合包含该元素，则引发一个异常。
            </summary>
            <param name="collection">
            要在其中搜索元素的集合。
            </param>
            <param name="element">
            预期不在集合中的元素。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            测试指定的集合是否不包含指定
            元素，如果集合包含该元素，则引发一个异常。
            </summary>
            <param name="collection">
            要在其中搜索元素的集合。
            </param>
            <param name="element">
            预期不在集合中的元素。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="element"/>
            位于<paramref name="collection"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            测试指定的集合是否不包含指定
            元素，如果集合包含该元素，则引发一个异常。
            </summary>
            <param name="collection">
            要在其中搜索元素的集合。
            </param>
            <param name="element">
            预期不在集合中的元素。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="element"/>
            位于<paramref name="collection"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection)">
            <summary>
            测试指定的集合中所有项是否都为非 null，
            如果有元素为 null，则引发一个异常。
            </summary>
            <param name="collection">
            在其中搜索 null 元素的集合。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String)">
            <summary>
            测试指定的集合中所有项是否都为非 null，
            如果有元素为 null，则引发一个异常。
            </summary>
            <param name="collection">
            在其中搜索 null 元素的集合。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="collection"/>
            包含一个 null 元素。消息显示在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            测试指定的集合中所有项是否都为非 null，
            如果有元素为 null，则引发一个异常。
            </summary>
            <param name="collection">
            在其中搜索 null 元素的集合。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="collection"/>
            包含一个 null 元素。消息显示在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection)">
            <summary>
            测试指定集合中的所有项是否都唯一，
            如果集合中有任何两个元素相等，则引发异常。
            </summary>
            <param name="collection">
            要在其中搜索重复元素的集合。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String)">
            <summary>
            测试指定集合中的所有项是否都唯一，
            如果集合中有任何两个元素相等，则引发异常。
            </summary>
            <param name="collection">
            要在其中搜索重复元素的集合。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="collection"/>
            包含至少一个重复元素。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            测试指定集合中的所有项是否都唯一，
            如果集合中有任何两个元素相等，则引发异常。
            </summary>
            <param name="collection">
            要在其中搜索重复元素的集合。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="collection"/>
            包含至少一个重复元素。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            测试一个集合是否是另一集合的子集，
            如果子集中的任何元素都不是超集中的元素，
            则引发一个异常。
            </summary>
            <param name="subset">
            预期为一个子集的集合<paramref name="superset"/>。
            </param>
            <param name="superset">
            预期为以下对象的超集的集合: <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            测试一个集合是否是另一集合的子集，
            如果子集中的任何元素都不是超集中的元素，
            则引发一个异常。
            </summary>
            <param name="subset">
            预期为一个子集的集合<paramref name="superset"/>。
            </param>
            <param name="superset">
            预期为以下对象的超集的集合: <paramref name="subset"/>
            </param>
            <param name="message">
            包括在异常中的消息，此时元素位于
            <paramref name="subset"/> 未找到 <paramref name="superset"/>.
            消息显示在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            测试一个集合是否是另一集合的子集，
            如果子集中的任何元素都不是超集中的元素，
            则引发一个异常。
            </summary>
            <param name="subset">
            预期为一个子集的集合<paramref name="superset"/>。
            </param>
            <param name="superset">
            预期为以下对象的超集的集合: <paramref name="subset"/>
            </param>
            <param name="message">
            包括在异常中的消息，此时元素位于
            <paramref name="subset"/> 未找到 <paramref name="superset"/>.
            消息显示在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            测试一个集合是否不是另一个集合的子集，
            如果子集中的所有元素同时位于超集中，
            则引发一个异常.
            </summary>
            <param name="subset">
            预期不是一个子集的集合 <paramref name="superset"/>。
            </param>
            <param name="superset">
            预期不为超集的集合 <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            测试一个集合是否不是另一个集合的子集，
            如果子集中的所有元素同时位于超集中，
            则引发一个异常.
            </summary>
            <param name="subset">
            预期不是一个子集的集合 <paramref name="superset"/>。
            </param>
            <param name="superset">
            预期不为超集的集合 <paramref name="subset"/>
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当每个元素
            <paramref name="subset"/> 还存在于<paramref name="superset"/>.
            消息显示在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            测试一个集合是否不是另一个集合的子集，
            如果子集中的所有元素同时位于超集中，
            则引发一个异常.
            </summary>
            <param name="subset">
            预期不是一个子集的集合 <paramref name="superset"/>。
            </param>
            <param name="superset">
            预期不为超集的集合 <paramref name="subset"/>
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当每个元素
            <paramref name="subset"/> 还存在于<paramref name="superset"/>.
            消息显示在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            测试两个集合是否包含相同的元素，如果
            任一集合包含的元素不在另一
            集合中，则引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个集合。它包含测试预期的
            元素。
            </param>
            <param name="actual">
            要比较的第二个集合。这是在测试下
            由代码生成的集合。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            测试两个集合是否包含相同的元素，如果
            任一集合包含的元素不在另一
            集合中，则引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个集合。它包含测试预期的
            元素。
            </param>
            <param name="actual">
            要比较的第二个集合。这是在测试下
            由代码生成的集合。
            </param>
            <param name="message">
            当某个元素仅可在其中一个集合内找到时
            要包含在异常中的消息。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            测试两个集合是否包含相同的元素，如果
            任一集合包含的元素不在另一
            集合中，则引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个集合。它包含测试预期的
            元素。
            </param>
            <param name="actual">
            要比较的第二个集合。这是在测试下
            由代码生成的集合。
            </param>
            <param name="message">
            当某个元素仅可在其中一个集合内找到时
            要包含在异常中的消息。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            测试两个集合是否包含不同元素，
            如果这两个集合中包含相同元素，则不管
            顺序如何，均引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个集合。这包含测试
            预期与实际集合不同的元素。
            </param>
            <param name="actual">
            要比较的第二个集合。这是在测试下
            由代码生成的集合。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            测试两个集合是否包含不同元素，
            如果这两个集合中包含相同元素，则不管
            顺序如何，均引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个集合。这包含测试
            预期与实际集合不同的元素。
            </param>
            <param name="actual">
            要比较的第二个集合。这是在测试下
            由代码生成的集合。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            包含相同的元素 <paramref name="expected"/>。消息
            显示在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            测试两个集合是否包含不同元素，
            如果这两个集合中包含相同元素，则不管
            顺序如何，均引发一个异常。
            </summary>
            <param name="expected">
            要比较的第一个集合。这包含测试
            预期与实际集合不同的元素。
            </param>
            <param name="actual">
            要比较的第二个集合。这是在测试下
            由代码生成的集合。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            包含相同的元素 <paramref name="expected"/>。消息
            显示在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type)">
            <summary>
            测试指定集合中的所有元素是否是预期类型的
            实例，如果预期类型
            不在一个或多个这些元素的继承层次结构中，则引发一个异常。
            </summary>
            <param name="collection">
            包含测试预期为指定类型的
            元素的集合。
            </param>
            <param name="expectedType">
            每个元素的预期类型 <paramref name="collection"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String)">
            <summary>
            测试指定集合中的所有元素是否是预期类型的
            实例，如果预期类型
            不在一个或多个这些元素的继承层次结构中，则引发一个异常。
            </summary>
            <param name="collection">
            包含测试预期为指定类型的
            元素的集合。
            </param>
            <param name="expectedType">
            每个元素的预期类型 <paramref name="collection"/>。
            </param>
            <param name="message">
            包括在异常中的消息，此时元素位于
            <paramref name="collection"/> 不是实例
            <paramref name="expectedType"/>。消息显示在测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String,System.Object[])">
            <summary>
            测试指定集合中的所有元素是否是预期类型的
            实例，如果预期类型
            不在一个或多个这些元素的继承层次结构中，则引发一个异常。
            </summary>
            <param name="collection">
            包含测试预期为指定类型的
            元素的集合。
            </param>
            <param name="expectedType">
            每个元素的预期类型 <paramref name="collection"/>。
            </param>
            <param name="message">
            包括在异常中的消息，此时元素位于
            <paramref name="collection"/> 不是实例
            <paramref name="expectedType"/>。消息显示在测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            测试指定的集合是否相等，如果两个集合
            不相等，则引发一个异常。相等被定义为具有相同的元素，并且元素的
           顺序和数量也相同。
            对同一值的不同引用也视为相等。
            </summary>
            <param name="expected">
            要比较的第一个集合。这是测试预期的集合。
            </param>
            <param name="actual">
            要比较的第二个集合。这是测试西下代码
            生成的集合。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            测试指定的集合是否相等，如果两个集合
            不相等，则引发一个异常。相等被定义为具有相同的元素，并且元素的
           顺序和数量也相同。
            对同一值的不同引用也视为相等。
            </summary>
            <param name="expected">
            要比较的第一个集合。这是测试预期的集合。
            </param>
            <param name="actual">
            要比较的第二个集合。这是测试西下代码
            生成的集合。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不等于 <paramref name="expected"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            测试指定的集合是否相等，如果两个集合
            不相等，则引发一个异常。相等被定义为具有相同的元素，并且元素的
           顺序和数量也相同。
            对同一值的不同引用也视为相等。
            </summary>
            <param name="expected">
            要比较的第一个集合。这是测试预期的集合。
            </param>
            <param name="actual">
            要比较的第二个集合。这是测试西下代码
            生成的集合。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不等于 <paramref name="expected"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            测试指定的集合是否不相等，
            如果两个集合相等，则引发一个异常。相等被定义为具有相同的元素，并且元素的顺序和数量
            都相同。
            对同一值的不同引用也视为相等。
            </summary>
            <param name="notExpected">
            要比较的第一个集合。这是测试预期与
            以下内容不匹配的集合: <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个集合。这是测试西下代码
            生成的集合。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            测试指定的集合是否不相等，
            如果两个集合相等，则引发一个异常。相等被定义为具有相同的元素，并且元素的顺序和数量
            都相同。
            对同一值的不同引用也视为相等。
            </summary>
            <param name="notExpected">
            要比较的第一个集合。这是测试预期与
            以下内容不匹配的集合: <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个集合。这是测试西下代码
            生成的集合。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            等于 <paramref name="notExpected"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            测试指定的集合是否不相等，
            如果两个集合相等，则引发一个异常。相等被定义为具有相同的元素，并且元素的顺序和数量
            都相同。
            对同一值的不同引用也视为相等。
            </summary>
            <param name="notExpected">
            要比较的第一个集合。这是测试预期与
            以下内容不匹配的集合: <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个集合。这是测试西下代码
            生成的集合。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            等于 <paramref name="notExpected"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            测试指定的集合是否相等，如果两个集合
            不相等，则引发一个异常。相等被定义为具有相同的元素，并且元素的
           顺序和数量也相同。
            对同一值的不同引用也视为相等。
            </summary>
            <param name="expected">
            要比较的第一个集合。这是测试预期的集合。
            </param>
            <param name="actual">
            要比较的第二个集合。这是测试西下代码
            生成的集合。
            </param>
            <param name="comparer">
            比较集合的元素时使用的比较实现。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            测试指定的集合是否相等，如果两个集合
            不相等，则引发一个异常。相等被定义为具有相同的元素，并且元素的
           顺序和数量也相同。
            对同一值的不同引用也视为相等。
            </summary>
            <param name="expected">
            要比较的第一个集合。这是测试预期的集合。
            </param>
            <param name="actual">
            要比较的第二个集合。这是测试西下代码
            生成的集合。
            </param>
            <param name="comparer">
            比较集合的元素时使用的比较实现。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不等于 <paramref name="expected"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            测试指定的集合是否相等，如果两个集合
            不相等，则引发一个异常。相等被定义为具有相同的元素，并且元素的
           顺序和数量也相同。
            对同一值的不同引用也视为相等。
            </summary>
            <param name="expected">
            要比较的第一个集合。这是测试预期的集合。
            </param>
            <param name="actual">
            要比较的第二个集合。这是测试西下代码
            生成的集合。
            </param>
            <param name="comparer">
            比较集合的元素时使用的比较实现。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是当<paramref name="actual"/>
            不等于 <paramref name="expected"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组 <paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            测试指定的集合是否不相等，
            如果两个集合相等，则引发一个异常。相等被定义为具有相同的元素，并且元素的顺序和数量
            都相同。
            对同一值的不同引用也视为相等。
            </summary>
            <param name="notExpected">
            要比较的第一个集合。这是测试预期与
            以下内容不匹配的集合: <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个集合。这是测试西下代码
            生成的集合。
            </param>
            <param name="comparer">
            比较集合的元素时使用的比较实现。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            测试指定的集合是否不相等，
            如果两个集合相等，则引发一个异常。相等被定义为具有相同的元素，并且元素的顺序和数量
            都相同。
            对同一值的不同引用也视为相等。
            </summary>
            <param name="notExpected">
            要比较的第一个集合。这是测试预期与
            以下内容不匹配的集合: <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个集合。这是测试西下代码
            生成的集合。
            </param>
            <param name="comparer">
            比较集合的元素时使用的比较实现。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是:<paramref name="actual"/>
            等于 <paramref name="notExpected"/>。消息显示在
            测试结果中。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            测试指定的集合是否不相等，
            如果两个集合相等，则引发一个异常。相等被定义为具有相同的元素，并且元素的顺序和数量
            都相同。
            对同一值的不同引用也视为相等。
            </summary>
            <param name="notExpected">
            要比较的第一个集合。这是测试预期与
            以下内容不匹配的集合: <paramref name="actual"/>。
            </param>
            <param name="actual">
            要比较的第二个集合。这是测试西下代码
            生成的集合。
            </param>
            <param name="comparer">
            比较集合的元素时使用的比较实现。
            </param>
            <param name="message">
            要包含在异常中的消息，条件是:<paramref name="actual"/>
            等于 <paramref name="notExpected"/>。消息显示在
            测试结果中。
            </param>
            <param name="parameters">
            在格式化时使用的参数数组<paramref name="message"/>。
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOfHelper(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            确定第一个集合是否为第二个
            集合的子集。如果任一集合包含重复元素，则子集中元素
            出现的次数必须小于或
            等于在超集中元素出现的次数。
            </summary>
            <param name="subset">
            测试预期包含在以下对象中的集合: <paramref name="superset"/>。
            </param>
            <param name="superset">
            测试预期要包含的集合 <paramref name="subset"/>。
            </param>
            <returns>
            为 True，如果 <paramref name="subset"/>是一个子集
            <paramref name="superset"/>，反之则为 False。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.GetElementCounts(System.Collections.ICollection,System.Int32@)">
            <summary>
            构造包含指定集合中每个元素的出现次数
            的字典。
            </summary>
            <param name="collection">
            要处理的集合。
            </param>
            <param name="nullCount">
            集合中 null 元素的数量。
            </param>
            <returns>
            包含指定集合中每个元素的发生次数
            的字典。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.FindMismatchedElement(System.Collections.ICollection,System.Collections.ICollection,System.Int32@,System.Int32@,System.Object@)">
            <summary>
            在两个集合之间查找不匹配的元素。不匹配的元素是指
            在预期集合中显示的次数与
            在实际集合中显示的次数不相同的元素。假定
            集合是具有相同元素数目
            的不同非 null 引用。 调用方负责此级别的验证。
            如果存在不匹配的元素，函数将返回
            false，并且不会使用 out 参数。
            </summary>
            <param name="expected">
            要比较的第一个集合。
            </param>
            <param name="actual">
            要比较的第二个集合。
            </param>
            <param name="expectedCount">
            预期出现次数
            <paramref name="mismatchedElement"/> 或者如果没有匹配的元素，
            则为 0。
            </param>
            <param name="actualCount">
            实际出现次数
            <paramref name="mismatchedElement"/> 或者如果没有匹配的元素，
            则为 0。
            </param>
            <param name="mismatchedElement">
            不匹配元素(可能为 null)，或者如果没有不匹配元素，
            则为 null。
            </param>
            <returns>
            如果找到不匹配的元素，则为 True；反之则为 False。
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.ObjectComparer">
            <summary>
            使用 Object.Equals 比较对象
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException">
            <summary>
            框架异常的基类。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> 类的新实例。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String,System.Exception)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> 类的新实例。
            </summary>
            <param name="msg"> 消息。 </param>
            <param name="ex"> 异常。 </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> 类的新实例。
            </summary>
            <param name="msg"> 消息。 </param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages">
            <summary>
              一个强类型资源类，用于查找已本地化的字符串等。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ResourceManager">
            <summary>
              返回此类使用的缓存的 ResourceManager 实例。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Culture">
            <summary>
              使用此强类型资源类为所有资源查找替代
              当前线程的 CurrentUICulture 属性。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AccessStringInvalidSyntax">
            <summary>
              查找类似于“访问字符串具有无效语法。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ActualHasMismatchedElements">
            <summary>
              查找类似于“预期集合包含 {1} 个 &lt;{2}&gt; 的匹配项。实际集合包含 {3} 个匹配项。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AllItemsAreUniqueFailMsg">
            <summary>
              查找类似于“找到了重复项: &lt;{1}&gt;。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualCaseFailMsg">
            <summary>
              查找类似于“预期为: &lt;{1}&gt;。实际值的大小写有所不同: &lt;{2}&gt;。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDeltaFailMsg">
            <summary>
              查找类似于“预期值 &lt;{1}&gt; 和实际值 &lt;{2}&gt; 之间的预期差异应不大于 &lt;{3}&gt;。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDifferentTypesFailMsg">
            <summary>
              查找类似于“预期为: &lt;{1} ({2})&gt;。实际为: &lt;{3} ({4})&gt;。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualFailMsg">
            <summary>
              查找类似于“预期为: &lt;{1}&gt;。实际为: &lt;{2}&gt;。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualDeltaFailMsg">
            <summary>
              查找类似于“预期值 &lt;{1}&gt; 和实际值 &lt;{2}&gt; 之间的预期差异应大于 &lt;{3}&gt;。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualFailMsg">
            <summary>
              查找类似于“预期为除 &lt;{1}&gt;外的任何值。实际为: &lt;{2}&gt;。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreSameGivenValues">
            <summary>
              查找类似于“不要向 AreSame() 传递值类型。转换为对象的值永远不会相同。请考虑使用 AreEqual()。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AssertionFailed">
            <summary>
              查找类似于“{0} 失败。{1}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AsyncUITestMethodNotSupported">
            <summary>
              查找类似于“不支持具有 UITestMethodAttribute 的异步 TestMethod。请删除异步或使用 TestMethodAttribute。” 的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsEmpty">
            <summary>
              查找类似于“这两个集合都为空。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameElements">
            <summary>
              查找类似于“这两个集合包含相同元素。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameReference">
            <summary>
              查找类似于“这两个集合引用指向同一个集合对象。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothSameElements">
            <summary>
              查找类似于“这两个集合包含相同的元素。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.CollectionEqualReason">
            <summary>
              查找类似于“{0}({1})”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_NullInMessages">
            <summary>
              查找类似于 "(null)" 的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_ObjectString">
            <summary>
              查找类似于“(对象)”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ContainsFail">
            <summary>
              查找类似于“字符串“{0}”不包含字符串“{1}”。{2}。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DataDrivenResultDisplayName">
            <summary>
              查找类似于“{0} ({1})”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DoNotUseAssertEquals">
            <summary>
              查找类似于“Assert.Equals 不应用于断言。请改用 Assert.AreEqual 和重载。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementNumbersDontMatch">
            <summary>
              查找类似于“集合中的元素数目不匹配。预期为: &lt;{1}&gt;。实际为: &lt;{2}&gt;。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementsAtIndexDontMatch">
            <summary>
              查找类似于“索引 {0} 处的元素不匹配。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch">
            <summary>
              查找类似于“索引 {1} 处的元素不是预期类型。预期类型为: &lt;{2}&gt;。实际类型为: &lt;{3}&gt;。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch2">
            <summary>
              查找类似于“索引 {1} 处的元素为 (null)。预期类型: &lt;{2}&gt;。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EndsWithFail">
            <summary>
              查找类似于“字符串“{0}”不以字符串“{1}”结尾。{2}。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EqualsTesterInvalidArgs">
            <summary>
              查找类似于“参数无效 - EqualsTester 不能使用 null。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ErrorInvalidCast">
            <summary>
              查找类似于“无法将类型 {0} 的对象转换为 {1}。”的本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InternalObjectNotValid">
            <summary>
              查找类似于“引用的内部对象不再有效。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidParameterToAssert">
            <summary>
              查找类似于“参数 {0} 无效。{1}。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidPropertyType">
            <summary>
              查找类似于“属性 {0} 具有类型 {1}；预期类型为 {2}。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsInstanceOfFailMsg">
            <summary>
              查找类似于“{0} 预期类型: &lt;{1}&gt;。实际类型: &lt;{2}&gt;。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsMatchFail">
            <summary>
              查找类似于“字符串“{0}”与模式“{1}”不匹配。{2}。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotInstanceOfFailMsg">
            <summary>
              查找类似于“错误类型: &lt;{1}&gt;。实际类型: &lt;{2}&gt;。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotMatchFail">
            <summary>
              查找类似于“字符串“{0}”与模式“{1}”匹配。{2}。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoDataRow">
            <summary>
              查找类似于“未指定 DataRowAttribute。DataTestMethodAttribute 至少需要一个 DataRowAttribute。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoExceptionThrown">
            <summary>
              查找类似于“未引发异常。预期为 {1} 异常。{0}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NullParameterToAssert">
            <summary>
              查找类似于“参数 {0} 无效。值不能为 null。{1}。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NumberOfElementsDiff">
            <summary>
              查找类似于“不同元素数。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorConstructorNotFound">
            <summary>
              查找类似于
                 “找不到具有指定签名的构造函数。可能需要重新生成专用访问器，
                 或者成员可能为专用且在基类上进行了定义。如果后者为 true，则需将定义成员的类型传递到
                 PrivateObject 的构造函数中。”
               的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorMemberNotFound">
            <summary>
              查找类似于 
                 “找不到指定成员({0})。可能需要重新生成专用访问器，
                 或者成员可能为专用且在基类上进行了定义。如果后者为 true，则需将定义成员的类型
                 传递到 PrivateObject 的构造函数中。”
              的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.StartsWithFail">
            <summary>
              查找类似于“字符串“{0}”不以字符串“{1}”开头。{2}。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_ExpectedExceptionTypeMustDeriveFromException">
            <summary>
              查找类似于“预期异常类型必须是 System.Exception 或派生自 System.Exception 的类型。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_FailedToGetExceptionMessage">
            <summary>
              查找类似于“(由于出现异常，未能获取 {0} 类型异常的消息。)”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoException">
            <summary>
              查找类似于“测试方法未引发预期异常 {0}。{1}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoExceptionDefault">
            <summary>
              查找类似于“测试方法未引发异常。预期测试方法上定义的属性 {0} 会引发异常。”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongException">
            <summary>
              查找类似于“测试方法引发异常 {0}，但预期为异常 {1}。异常消息: {2}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongExceptionDerivedAllowed">
            <summary>
              查找类似于“测试方法引发异常 {0}，但预期为异常 {1} 或从其派生的类型。异常消息: {2}”的已本地化字符串。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.WrongExceptionThrown">
             <summary>
               查找类似于“引发异常 {2}，但预期为异常 {1}。{0}
            异常消息: {3}
            堆栈跟踪: {4}”的已本地化字符串。
             </summary> 
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome">
            <summary>
            单元测试结果
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Failed">
            <summary>
            测试已执行，但出现问题。
            问题可能涉及异常或失败的断言。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Inconclusive">
            <summary>
            测试已完成，但无法确定它是已通过还是失败。
            可用于已中止的测试。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Passed">
            <summary>
            测试已执行，未出现任何问题。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.InProgress">
            <summary>
            当前正在执行测试。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Error">
            <summary>
            尝试执行测试时出现了系统错误。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Timeout">
            <summary>
            测试已超时。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Aborted">
            <summary>
            用户中止了测试。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Unknown">
            <summary>
            测试处于未知状态
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper">
            <summary>
            为单元测试框架提供帮助程序功能
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper.GetExceptionMsg(System.Exception)">
            <summary>
            以递归方式获取包括所有内部异常消息在内的
            异常消息
            </summary>
            <param name="ex">获取消息的异常</param>
            <returns>包含错误消息信息的字符串</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout">
            <summary>
            超时枚举，可与 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> 类共同使用。
            枚举类型必须相符
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout.Infinite">
            <summary>
            无限。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute">
            <summary>
            测试类属性。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute.GetTestMethodAttribute(Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute)">
            <summary>
            获取可运行此测试的测试方法属性。
            </summary>
            <param name="testMethodAttribute">在此方法上定义的测试方法属性实例。</param>
            <returns>该<see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/> 将用于运行此测试。</returns>
            <remarks>Extensions can override this method to customize how all methods in a class are run.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute">
            <summary>
            测试方法属性。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            执行测试方法。
            </summary>
            <param name="testMethod">要执行的测试方法。</param>
            <returns>表示测试结果的 TestResult 对象数组。</returns>
            <remarks>Extensions can override this method to customize running a TestMethod.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestInitializeAttribute">
            <summary>
            测试初始化属性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCleanupAttribute">
            <summary>
            测试清理属性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute">
            <summary>
            忽略属性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute">
            <summary>
            测试属性特性。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.#ctor(System.String,System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute"/> 类的新实例。
            </summary>
            <param name="name">
            名称。
            </param>
            <param name="value">
            值。
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Name">
            <summary>
            获取名称。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Value">
            <summary>
            获取值。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute">
            <summary>
            类初始化属性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute">
            <summary>
           类清理属性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyInitializeAttribute">
            <summary>
            程序集初始化属性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyCleanupAttribute">
            <summary>
            程序集清理属性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute">
            <summary>
            测试所有者
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.#ctor(System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute"/> 类的新实例。
            </summary>
            <param name="owner">
            所有者。
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.Owner">
            <summary>
            获取所有者。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute">
            <summary>
            优先级属性；用于指定单元测试的优先级。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.#ctor(System.Int32)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute"/> 类的新实例。
            </summary>
            <param name="priority">
            属性。
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.Priority">
            <summary>
            获取属性。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute">
            <summary>
            测试的描述
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.#ctor(System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute"/> 类的新实例，描述测试。
            </summary>
            <param name="description">说明。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.Description">
            <summary>
            获取测试的说明。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute">
            <summary>
            CSS 项目结构 URI
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.#ctor(System.String)">
            <summary>
            为 CSS 项目结构 URI 初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute"/> 类的新实例。
            </summary>
            <param name="cssProjectStructure">CSS 项目结构 URI。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.CssProjectStructure">
            <summary>
            获取 CSS 项目结构 URI。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute">
            <summary>
            CSS 迭代 URI
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.#ctor(System.String)">
            <summary>
            为 CSS 迭代 URI 初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute"/> 类的新实例。
            </summary>
            <param name="cssIteration">CSS 迭代 URI。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.CssIteration">
            <summary>
            获取 CSS 迭代 URI。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute">
            <summary>
            工作项属性；用来指定与此测试关联的工作项。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.#ctor(System.Int32)">
            <summary>
            为工作项属性初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute"/> 类的新实例。
            </summary>
            <param name="id">工作项的 ID。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.Id">
            <summary>
            获取关联工作项的 ID。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute">
            <summary>
            超时属性；用于指定单元测试的超时。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(System.Int32)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> 类的新实例。
            </summary>
            <param name="timeout">
            超时。
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout)">
            <summary>
            初始化含有预设超时的 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> 类的新实例
            </summary>
            <param name="timeout">
            超时
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.Timeout">
            <summary>
            获取超时。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult">
            <summary>
            要返回到适配器的 TestResult 对象。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.#ctor">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/> 类的新实例。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DisplayName">
            <summary>
            获取或设置结果的显示名称。这在返回多个结果时很有用。
            如果为 null，则表示方法名用作了 DisplayName。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Outcome">
            <summary>
            获取或设置测试执行的结果。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestFailureException">
            <summary>
           获取或设置测试失败时引发的异常。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogOutput">
            <summary>
            获取或设置由测试代码记录的消息输出。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogError">
            <summary>
            获取或设置由测试代码记录的消息输出。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DebugTrace">
            <summary>
            通过测试代码获取或设置调试跟踪。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestContextMessages">
            <summary>
            Gets or sets the debug traces by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Duration">
            <summary>
            获取或设置测试执行的持续时间。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DatarowIndex">
            <summary>
            获取或设置数据源中的数据行索引。仅对数据驱动测试的数据行单次运行结果
            进行设置。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ReturnValue">
            <summary>
           获取或设置测试方法的返回值。(当前始终为 null)。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ResultFiles">
            <summary>
            获取或设置测试附加的结果文件。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute">
            <summary>
            为数据驱动测试指定连接字符串、表名和行访问方法。
            </summary>
            <example>
            [DataSource("Provider=SQLOLEDB.1;Data Source=source;Integrated Security=SSPI;Initial Catalog=EqtCoverage;Persist Security Info=False", "MyTable")]
            [DataSource("dataSourceNameFromConfigFile")]
            </example>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultProviderName">
            <summary>
            DataSource 的默认提供程序名称。
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultDataAccessMethod">
            <summary>
            默认数据访问方法。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String,System.String,Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> 类的新实例。将使用数据提供程序、连接字符串、数据表和访问数据源的数据访问方法初始化此实例。
            </summary>
            <param name="providerInvariantName">不变的数据提供程序名称，例如 System.Data.SqlClient</param>
            <param name="connectionString">
            特定于数据提供程序的连接字符串。
            警告: 连接字符串可能包含敏感数据(例如密码)。
            连接字符串以纯文本形式存储在源代码和编译程序集中。
            限制对源代码和程序集的访问以保护此敏感信息。
            </param>
            <param name="tableName">数据表的名称。</param>
            <param name="dataAccessMethod">指定访问数据的顺序。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> 类的新实例。将使用连接字符串和表名初始化此实例。
            指定连接字符串和数据表，访问 OLEDB 数据源。
            </summary>
            <param name="connectionString">
            特定于数据提供程序的连接字符串。
            警告: 连接字符串可能包含敏感数据(例如密码)。
            连接字符串以纯文本形式存储在源代码和编译程序集中。
            限制对源代码和程序集的访问以保护此敏感信息。
            </param>
            <param name="tableName">数据表的名称。</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String)">
            <summary>
            初始化 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> 类的新实例。将使用数据提供程序和与设置名称关联的连接字符串初始化此实例。
            </summary>
            <param name="dataSourceSettingName">在 app.config 文件中 &lt;microsoft.visualstudio.qualitytools&gt; 部分找到的数据源的名称。</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ProviderInvariantName">
            <summary>
            获取表示数据源的数据提供程序的值。
            </summary>
            <returns>
            数据提供程序名称。如果数据提供程序未在对象初始化时进行指定，则将返回 System.Data.OleDb 的默认提供程序。
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ConnectionString">
            <summary>
            获取表示数据源的连接字符串的值。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.TableName">
            <summary>
            获取指示提供数据的表名的值。
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataAccessMethod">
             <summary>
            获取用于访问数据源的方法。
             </summary>
            
             <returns>
             其中一个 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod"/>值。如果 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>未初始化，这将返回默认值<see cref="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random"/>。
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataSourceSettingName">
            <summary>
            获取 app.config 文件的 &lt;microsoft.visualstudio.qualitytools&gt; 部分中找到的数据源的名称。
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute">
            <summary>
            可在其中将数据指定为内联的数据驱动测试的属性。
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            查找所有数据行并执行。
            </summary>
            <param name="testMethod">
            测试方法。
            </param>
            <returns>
            一系列<see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>。
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.RunDataDrivenTest(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod,Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute[])">
            <summary>
            运行数据驱动测试方法。
            </summary>
            <param name="testMethod"> 要执行的测试方法。 </param>
            <param name="dataRows"> 数据行。 </param>
            <returns> 执行的结果。 </returns>
        </member>
    </members>
</doc>
