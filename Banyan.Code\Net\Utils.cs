﻿using Newtonsoft.Json;
using System;
using System.Web;

namespace Banyan.Code
{
    public class Utils
    {
        /// <summary>
        /// 请求公共函数
        /// </summary>
        /// <param name="name"></param>
        /// <param name="defaultVal"></param>
        /// <returns></returns> 

        public static int GetRequestInt(string name, int defaultVal)
        {
            int val = 0;
            try
            {
                if (!int.TryParse(HttpContext.Current.Request[name], out val))
                {
                    val = defaultVal;
                }
            }
            catch (Exception ex)
            {
                Logger.Error("get request fail! name:" + name, ex);
            }
            return val;
        }

        public static bool GetRequestBool(string name, bool defaultVal)
        {
            bool val = false;
            try
            {
                if (!bool.TryParse(HttpContext.Current.Request[name], out val))
                {
                    val = defaultVal;
                }
            }
            catch (Exception ex)
            {
                Logger.Error("get request fail! name:" + name, ex);
            }
            return val;
        }

        public static float GetRequestFloat(string name, float defaultVal)
        {
            float val = 0;
            try
            {
                if (!float.TryParse(HttpContext.Current.Request[name], out val))
                {
                    val = defaultVal;
                }
            }
            catch (Exception ex)
            {
                Logger.Error("get request fail! name:" + name, ex);
            }
            return val;
        }

        public static double GetRequestDouble(string name, double defaultVal)
        {
            double val = 0;
            try
            {
                if (!double.TryParse(HttpContext.Current.Request[name], out val))
                {
                    val = defaultVal;
                }
            }
            catch (Exception ex)
            {
                Logger.Error("get request fail! name:" + name, ex);
            }
            return val;
        }

        /// <summary>
        /// 请求公共函数
        /// </summary>
        /// <param name="name"></param>
        /// <param name="defaultVal"></param>
        /// <returns></returns>
        public static string GetRequest(string name, string defaultVal)
        {
            string str = string.Empty;
            try
            {
                if (HttpContext.Current.Request[name] != null)
                {
                    str = HttpContext.Current.Request[name].Trim();
                    if (string.IsNullOrEmpty(str) || str.ToLower() == "null" || str.ToLower() == "undefined")
                        str = string.Empty;
                }
                else
                    str = defaultVal;
            }
            catch (Exception ex)
            {
                Logger.Error("get request fail! name:" + name, ex);
            }
            return str;
        }

        public static T DeserializeObjectByJson<T>(string str)
        {
            return string.IsNullOrEmpty(str) ? Activator.CreateInstance<T>() : JsonConvert.DeserializeObject<T>(str);
        }
    }
}
