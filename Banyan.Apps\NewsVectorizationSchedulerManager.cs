using System;
using System.Threading.Tasks;
using Banyan.Code;

namespace Banyan.Apps
{
    /// <summary>
    /// 新闻向量化调度器管理器
    /// 提供对新闻向量化调度器的全局访问和管理
    /// </summary>
    public static class NewsVectorizationSchedulerManager
    {
        private static readonly object _lock = new object();
        private static NewsVectorizationScheduler _instance;

        /// <summary>
        /// 获取新闻向量化调度器实例
        /// </summary>
        public static NewsVectorizationScheduler Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new NewsVectorizationScheduler();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 初始化并启动调度器
        /// </summary>
        public static void Initialize()
        {
            try
            {
                Logger.Info("初始化新闻向量化调度器");
                Instance.Start();
                Logger.Info("新闻向量化调度器已启动");
            }
            catch (Exception ex)
            {
                Logger.Error("初始化新闻向量化调度器失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 关闭调度器
        /// </summary>
        public static void Shutdown()
        {
            try
            {
                if (_instance != null)
                {
                    Logger.Info("关闭新闻向量化调度器");
                    _instance.Stop();
                    _instance.Dispose();
                    _instance = null;
                    Logger.Info("新闻向量化调度器已关闭");
                }
            }
            catch (Exception ex)
            {
                Logger.Error("关闭新闻向量化调度器失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 重启调度器
        /// </summary>
        public static void Restart()
        {
            try
            {
                Logger.Info("重启新闻向量化调度器");
                Shutdown();
                Initialize();
                Logger.Info("新闻向量化调度器已重启");
            }
            catch (Exception ex)
            {
                Logger.Error("重启新闻向量化调度器失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 获取调度器状态
        /// </summary>
        /// <param name="includeDetailedStats">是否包含详细统计信息</param>
        /// <returns>调度器状态</returns>
        public static object GetStatus(bool includeDetailedStats = false)
        {
            try
            {
                if (_instance == null)
                {
                    return new
                    {
                        IsRunning = false,
                        CurrentStatus = "Not Initialized"
                    };
                }

                return _instance.GetStatus(includeDetailedStats);
            }
            catch (Exception ex)
            {
                Logger.Error("获取新闻向量化调度器状态失败", ex);
                return new
                {
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                };
            }
        }

        /// <summary>
        /// 手动触发向量化任务
        /// </summary>
        /// <param name="batchSize">批量大小</param>
        /// <param name="includeFailedOnly">是否只处理失败的新闻</param>
        /// <param name="maxRetryCount">最大重试次数</param>
        /// <param name="parallelProcessing">是否启用并行处理</param>
        /// <param name="maxDegreeOfParallelism">最大并行度</param>
        /// <param name="newsIds">指定的新闻ID列表，如果为null则处理所有符合条件的新闻</param>
        /// <returns>处理结果</returns>
        public static async Task<VectorizationResult> TriggerVectorizationAsync(
            int batchSize = 100,
            bool includeFailedOnly = false,
            int maxRetryCount = 3,
            bool parallelProcessing = false,
            int maxDegreeOfParallelism = 3,
            System.Collections.Generic.List<int> newsIds = null)
        {
            try
            {
                return await Instance.TriggerVectorizationAsync(
                    batchSize,
                    includeFailedOnly,
                    maxRetryCount,
                    parallelProcessing,
                    maxDegreeOfParallelism,
                    newsIds);
            }
            catch (Exception ex)
            {
                Logger.Error("手动触发新闻向量化任务失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 优先处理指定的新闻
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <returns>处理结果</returns>
        public static async Task<bool> PrioritizeNewsAsync(int newsId)
        {
            try
            {
                return await Instance.PrioritizeNewsAsync(newsId);
            }
            catch (Exception ex)
            {
                Logger.Error($"优先处理新闻失败，新闻ID: {newsId}", ex);
                throw;
            }
        }
    }
}