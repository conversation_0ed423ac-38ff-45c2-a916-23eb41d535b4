﻿using Banyan.Apps;
using Banyan.Code;
using Banyan.Domain;
using System;
using System.Web.Mvc;
using Banyan.Web.Filters;

namespace Banyan.Web.Controllers
{
    [AuthFilter]
    public class BaseController : Controller
    {
        public MemberBLL memberBll = new MemberBLL();
        public ArticleBLL articleBll = new ArticleBLL();

        protected Member loginUser
        {
            get
            {

                try
                {
                    //var user = WebHelper.GetSession<Member>("banyan_user_session");
                    //if (user != null && user.Id > 0)
                    //    return user;

                    string currUserDes = WebHelper.GetCookie("banyan_logon_user");
                    if (!string.IsNullOrEmpty(currUserDes))
                    {
                        string[] desInfo = DESEncrypt.Decrypt(currUserDes).Split(',');
                        int userId = Convert.ToInt32(desInfo[0]);

                        return userId <= 0 ? null : memberBll.GetModelByCache(userId);
                    }
                    return null;
                }
                catch (Exception ex)
                {
                    Logger.Info(ex.Message + " " + ex.StackTrace);
                    return null;
                }
            }
        }

        public BaseController()
        {
            ViewData["manager"] = loginUser;
        }
    }
}