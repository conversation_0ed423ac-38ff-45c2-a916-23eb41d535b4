using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    public partial class Role
    {
        public Role() { }

        public int Id { get; set; }

        /// <summary>
        /// 角色名称
        /// </summary>
        public string RoleName { get; set; } = string.Empty;

        public string RoleLdap { get; set; } = string.Empty;

        public string Owner { get; set; } = string.Empty;
        /// <summary>
        /// 状态
        /// </summary>
        public byte status { get; set; } = (int)RoleStatus.enable;


        public bool UserSet { get; set; } = false;
    }

    public enum RoleStatus
    {
        ///// <summary>
        ///// 已删除
        ///// </summary>
        //delete = 0,

        /// <summary>
        /// 已启用
        /// </summary>
        enable = 1,

        /// <summary>
        /// 已禁用
        /// </summary>
        disable = 2,
    }
}
