using Entity.Base;
using System;
using System.Collections.Generic;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public class EngagementRecords
    {
        /// <summary>
        /// Unique identifier for the engagement record
        /// </summary>
        public int Id { get; set; }

        [SqlField]
        public string UserName { get; set; }

        [SqlField]
        public int NewsId { get; set; }

        [SqlField]
        public string NewsTitle { get; set; }

        [SqlField]
        public DateTime Timestamp { get; set; }

        [SqlField]
        public string Source { get; set; }
    }
}