﻿@using Banyan.Domain
@{
    ViewBag.Name = "投资团队报表";
    Layout = "/Views/Shared/_LayoutSuper.cshtml";
    var rolelist = (List<Banyan.Domain.Role>)ViewData["rolelist"];
    var manager = ViewData["manager"] as Banyan.Domain.Member;
}

<style>
    td .layui-table-cell, .layui-table-cell img {
        height: 35px !important;
        line-height: 35px !important;

        padding: 0 15px;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
        box-sizing: border-box;
    }
</style>

<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li>投资团队报表</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <div class="tab-pane active">
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="pull-right search-bar"></div>
                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                                <div class="form-group">
                                    <select class="form-control" id="keyclass" name="keyclass" size="1">
                                        <option value="0">项目组</option>
                                        @if (rolelist != null && rolelist.Count() > 0)
                                        {
                                            foreach (var ci in rolelist)
                                            {
                                                <option value="@(ci.Id)">@(ci.RoleName)</option>
                                            }
                                        }
                                    </select>
                                </div>
                                @*<div class="form-group">
                                    <select class="form-control" id="creator" name="creator" size="1">
                                        <option value="0">创建人</option>
                                    </select>
                                </div>*@
                                <input type="hidden" id="OpenId" name="OpenId" value="@(manager.OpenId)">
                                <div class="form-group">
                                    <label class="sr-only" for="title-name">搜索</label>
                                    <input class="form-control" type="text" id="keyname" name="keyname" placeholder="搜索">
                                </div>
                                <div class="form-group">
                                    <div class="input-daterange input-group">
                                        <input class="form-control" type="text" id="startdate" name="startdate" placeholder="开始日期">
                                        <span class="input-group-addon" style="border-left:none;border-right:none;"><i class="fa fa-chevron-right"></i></span>
                                        <input class="form-control" type="text" id="enddate" name="enddate" placeholder="结束日期">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="currencyRate">USD/CNY汇率</label>
                                    <input type="number" class="form-control" value="7" min="0.0" step="0.0001" id="currencyRate" placeholder="USD/CNY汇率" style="width:150px;">
                                </div>
                                <div class="form-group">
                                    <a class="btn btn-default" id="dosearch">搜索</a>
                                    @*@if (manager.Levels == (byte)Banyan.Domain.MemberLevels.Administrator || manager.Levels == (byte)Banyan.Domain.MemberLevels.SuperUser)
                                    {
                                        <a class="btn btn-minw btn-warning" id="doexport">导出Excel</a>
                                    }*@
                                </div>
                            </form>
                        </div>
                        @*<div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                               
                                <div class="form-group">
                                    <label class="sr-only" for="title-name"></label>
                                    <input class="form-control" type="number" min="0.0" step="0.01" id="PManager" placeholder="负责项目权重" style="width:150px;">
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="title-name"></label>
                                    <input class="form-control" type="number" min="0.0" step="0.01" id="recommend" placeholder="推荐项目权重" style="width:150px;">
                                </div>

                                <div class="form-group">
                                    <label class="sr-only" for="title-name"></label>
                                    <input class="form-control" type="number" min="0.0" step="0.01" id="PMember" placeholder="参与项目权重" style="width:150px;">
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="title-name"></label>
                                    <input class="form-control" type="number" min="0.0" step="0.01" id="PIManager" placeholder="负责投后权重" style="width:150px;">
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="title-name"></label>
                                    <input class="form-control" type="number" min="0.0" step="0.01" id="PIMember" placeholder="参与投后权重" style="width:150px;">
                                </div>
                                <div class="form-group">
                                    <label class="slick-prev" for="title-name">贡献价值计算</label>
                                    <input class="form-control" type="checkbox" value="false" id="CombineCheck" placeholder="贡献价值计算">
                                </div>
                            </form>
                        </div>*@
                    </div>
                </div>
                <table class="layui-hide" id="table-list" lay-filter="list-filter"></table>
                <table class="layui-hide" id="staffperformance" lay-filter="list-filter"></table>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="toolbar-btn">
</script>
<script type="text/html" id="bartpl">
    <div class="btn-group" role="group" data-toggle="buttons">
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="预览" lay-event="preview" data-original-title="预览"><i class="fa fa-eye"></i></button>
    </div>
</script>
<script type="text/html" id="titleTpl">
    <button type="button"  lay-event="preview" class="layui-btn layui-btn-primary" style="width:100%">{{d.RealName}}</button>
</script>
@section scripts{
    <script type="text/javascript">
        var queryParams = {}, date = new Date();
        layui.use(['laypage', 'layer', 'table', 'laydate'], function () {
            var laypage = layui.laypage,
                layer = layui.layer,
                laydate = layui.laydate,
                table = layui.table;

            table.render({
                elem: '#table-list'
                , height: 570
                , defaultToolbar: ['filter', 'exports']
                , toolbar: '#toolbar-btn'
                , url: '@(Url.Action("GetCreatorsSummary", "adminapi"))'
                , page: {limit: 50, limits: [50]}
                , method: 'post'
                , cols: [[
                    //{field:'RealName', title: '编辑人', width: 70, templet: '#titleTpl'} //这里的templet值是模板元素的选择器
                    { field: 'RealName', title: '编辑人', fixed: 'left', width: 75 }
                    , { field: 'EditorCount', title: '创建数', width: 87, sort: true }
                    , { field: 'ProjectManagerCount', title: '负责数', width: 87 , sort: true }
                    , { field: 'groupMemberCount', title: '参与数', width: 87 , sort: true }
                    , { field: 'PTCPCount', title: '参会数', width: 87 , sort: true }
                    , { field: 'IntroducerCount', title: '介绍数', width: 87, sort: true } // 项目介绍人/提及人/联系方式提供人/FA推荐同事
                    , { field: 'IntroducedProjects', title: '介绍项目', width: 87, sort: true }
                    , { field: 'DDManagerCount', title: 'DD数', width: 82 , sort: true }
                    , { field: 'FugaiCount', title: '覆盖数', width: 87, sort: true }
                    // 人脉类 项目负责人
                    , { field: 'YanjiuCount', title: '研究数', width: 87, sort: true }
                    , { field: 'TongshitijiCount', title: '提及数', width: 87, sort: true }
                    , { field: 'TongshijieshaoCount', title: '同事介绍数', width: 105, sort: true }
                    , { field: 'FaCount', title: 'FA数', width: 82, sort: true }
                    , { field: 'FUGAIP', title: '覆盖%', width: 85 , sort: true }
                    , { field: 'FAP', title: 'FA%', width: 82 , sort: true }
                    , { field: 'TSCount', title: 'TS数', width: 82 , sort: true }
                     , { field: 'TSP', title: 'TS%', width: 82 , sort: true }
                      , { field: 'DDCount', title: 'Pre-DD数', width: 109 , sort: true }
                     , { field: 'DDP', title: 'Pre-DD%', width: 105 , sort: true }
                    , { field: 'PartnerCount', title: '合伙人数', width: 102 , sort: true }
                    , { field: 'TeamDiscussCount', title: '小组讨论数', width: 112 , sort: true }

                //, { fixed: 'right', title: '业绩汇总', width: 90, align: 'center', toolbar: '#bartpl' }
                ]],
                done: function () { }
            });

            table.on('tool(list-filter)', function (obj) {
            var data = obj.data
                , layEvent = obj.event;

            if (layEvent === 'preview') {
                
                preview(data.RealName);
            } 
            return;
        });

        laypage.render({
            elem: 'pageBar'
            , count: 100
            , jump: function (obj, first) {
                if (!first) {
                    layer.msg('第' + obj.curr + '页');
                }
            }
        });

        laydate.render({
            elem: '#startdate'
        });

        laydate.render({
            elem: '#enddate'
        });
        //$('#keyname').on('keypress', function(event) {
        //    if (event.keyCode === 13) {
        //        $('#dosearch').trigger('click');
        //    }
        //});
        $('#dosearch').on('click', function () {
            queryParams = {
                ToRoleId: $('#keyclass').val(),
                Name: $('#keyname').val(),
                startdate: $('#startdate').val(),
                enddate: $('#enddate').val(),
            }
            table.reload('table-list',{
                where: queryParams,
            });
        });
        $('#CombineCheck').on('click', function () {
            if ($('#CombineCheck').val() == "false")
                $('#CombineCheck').val(true);
            else
                $('#CombineCheck').val(false);
        });
            //$('#doexport').on('click', function () {
            //    var querystr = 'ToRoleId=' + $('#keyclass').val() + '&Name=' + $('#keyname').val() + '&startdate=' + $('#startdate').val() + '&enddate=' + $('#enddate').val();
            //    if ($('#downloadcsv').length <= 0)
            //        $('body').append("<iframe id=\"downloadcsv\" style=\"display:none\"></iframe>");
            //    $('#downloadcsv').attr('src', "/adminapi/exportprojects?" + encodeURI(querystr.trim('&')));
            //});
        });

        function preview(RealName) {
            queryParams = {
                PMName: RealName,
                USDtoRMBRate: $("#currencyRate").val(),
                startdate: $('#startdate').val(),
                enddate: $('#enddate').val(),
            }

            $.post('/Adminapi/PerformanceReport', queryParams, function (str) {
                layer.open({
                    type: 1,
                    area: ['1250px', '667px'],
                    fix: false,
                    maxmin: true,
                    anim: 5,
                    shade: 0,
                    title: "业绩汇总-" + RealName,
                    content: str //注意，如果str是object，那么需要字符拼接。
                });

            })

            //var querystr = 'https://fms.gaorongvc.com/staffperformance.ashx?PMName=' + RealName + '&startDate=' + $('#startdate').val() + '&endDate=' + $('#enddate').val() + '&OpenId=' + $('#OpenId').val();
            //querystr += '&currencyRate=' + $('#currencyRate').val();
            //querystr += '&PManager=' + $('#PManager').val();
            //querystr += '&recommend=' + $('#recommend').val();
            //querystr += '&PMember=' + $('#PMember').val();
            //querystr += '&PIManager=' + $('#PIManager').val();
            //querystr += '&PIMember=' + $('#PIMember').val();

            //if ($('#CombineCheck').val() == "true")
            //    querystr += '&type=cal';
            //else
                //querystr += '&type=row';
            //function todo(data) {
            //    layer.open({
            //        type: 2,
            //        area: ['1450px', '667px'],
            //        fix: false,
            //        maxmin: false,
            //        anim: 5,
            //        shade: 0,
            //        title: "投资业绩-" + RealName,
            //        content: data //这里content是一个URL，如果你不想让iframe出现滚动条，你还可以content: ['http://sentsin.com', 'no']
            //    });
            //}
            //var script = document.createElement('script');
            //script.src = querystr;//向服务器www.jianshu.com发出请求。注意，该请求的查询字符串有一个callback参数，用来指定回调函数的名字。
            //document.body.appendChild(script);
            //服务器收到这个请求以后，会将数据放在回调函数的参数位置返回。

            //由于<script>元素请求的脚本，直接作为代码运行。这时，只要浏览器定义了todo函数，该函数就会立即调用。作为参数的JSON数据被视为JavaScript对象。

            //while(true) {
            //    try {
            //        win.document.title = '投资业绩-' + RealName;
            //        if(win.document.title ==  '投资业绩-' + RealName) {
            //            break;
            //        }
            //    } catch(e) {
            //    }
            //}
            //$.ajax({
            //    url: querystr,
            //    type: 'GET',
            //    dataType: 'JSONP',
            //    jsonp: "callback",//传递给请求处理程序或页面的，用以获得jsonp回调函数名的参数名(一般默认为:callback)
            //    jsonpCallback: "?",//自定义的jsonp回调函数名称，默认为jQuery自动生成的随机函数名，也可以写"?"，jQuery会自动为你处理数据
            //    success: function (data) {
            //        layer.open({
            //            type: 2,
            //            area: ['1250px', '667px'],
            //            fix: false,
            //            maxmin: false,
            //            anim: 5,
            //            shade: 0,
            //            title: "投资业绩-" + RealName,
            //            content: data //这里content是一个URL，如果你不想让iframe出现滚动条，你还可以content: ['http://sentsin.com', 'no']
            //        });
            //    },
            //    error: function () {
            //        window.open(querystr, '投资业绩-' + RealName, 'height=700,width=1400,top=300,left=300,toolbar=no,menubar=no,scrollbars=no,resizable=no,location=no,status=no');
            //        //请求出错处理
            //    }
            //});
            
                //layer.open({
                //    type: 2,
                //    area: ['1250px', '667px'],
                //    fix: false,
                //    maxmin: true,
                //    anim: 5,
                //    shade: 0,
                //    title: "业绩汇总-" + RealName,
                //    content: querystr //这里content是一个URL，如果你不想让iframe出现滚动条，你还可以content: ['http://sentsin.com', 'no']
                //});
           
        }
    </script>
}
