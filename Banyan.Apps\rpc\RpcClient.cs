﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using Banyan.Code;
using Banyan.Domain;
using Grpc;
using Grpc.Core;
using Milvus;

// https://blog.csdn.net/rockytech/article/details/108908524
namespace Banyan.Apps
{
    public class RpcClient
    {
        public RpcClient() { }
        private static milvus.milvusClient _client = null;
        private static List<ChannelOption> opts = null;
        private static Channel channel = null;
        public milvus.milvusClient client
        {
            get {
                if (opts == null)
                {
                    opts = new List<ChannelOption>();
                    opts.Add(new ChannelOption("grpc.keepalive_time_ms", 10000));
                    opts.Add(new ChannelOption("grpc.keepalive_timeout_ms", 20000));
                    opts.Add(new ChannelOption("grpc.keepalive_permit_without_calls", 1));
                    opts.Add(new ChannelOption("grpc.http2.min_time_between_pings_ms", 5000));
                    opts.Add(new ChannelOption("grpc.http2.max_pings_without_data", 0));
                }
                if (_client == null || (channel?.State != ChannelState.Ready && channel?.State != ChannelState.Idle))
                {
                    // log.gaorongvc.com_public.crt
                    channel = new Channel("log.gaorongvc.com:5001", new Grpc.Core.SslCredentials("-----BEGIN CERTIFICATE-----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\r\n-----END CERTIFICATE-----\r\n"), opts);
                    _client = new milvus.milvusClient(channel);
                }
                return _client;
            }
        }

        public bool hasRight(Milvus.Project p, Member user)
        {
            if (user.Levels == (int)MemberLevels.Administrator || user.Levels == (int)MemberLevels.SuperUser)
            {
                return true;
            }

            if (p.EditorName == user.RealName || p.DDManager == user.RealName ||
                p.ProjectManager == user.RealName || p.GroupMember == user.RealName ||
                p.InteralPTCP == user.RealName)
            {
                return true;
            }
            if (user.Levels == (int)MemberLevels.LimitedUser)
            {
                return false;
            }
            if (p.IsPrivate) { return false; }

            string roleIds = string.IsNullOrEmpty(user.Groups) ? "0" : $"{user.Groups}";
            if (user.Levels != (int)MemberLevels.OnlyRMB && user.Levels != (int)MemberLevels.OnlyUSD)
            {
                roleIds += ",6";//其他组均可见,除仅人民币和仅美元的外部受限情况
            }
            if (!roleIds.Contains(p.ToRoleId + ""))
            {
                return false;
            }
            if (user.Levels == (int)MemberLevels.OnlyRMB && p.Currency != "人民币")
            {
                return false;
            }
            if (user.Levels == (int)MemberLevels.OnlyUSD && p.Currency != "美元")
            {
                return false;
            }
            return true;
        }
        public List<Milvus.Project> search(string content)
        {
            Member user = new MemberBLL().GetLogOnUser();
            Logger.Info("milvus search str: " + content, user.RealName);
            try
            {
                var reply = client.similarSearch(new SearchRequest { Content = content });
                var list = reply.Project.ToList();
                if (list != null && list.Count > 0)
                {
                    list = list.Select(x =>
                    {
                        x.HasRight = hasRight(x, user);
                        if (!x.HasRight)
                        {
                            x.Name = "无权限项目";
                        }
                        return x;
                    }).ToList();
                }
                return list;
            }
            catch (Exception ex)
            {
                Logger.Error(ex.Message, ex);
                return null;
            }
        }
        public Milvus.Project convertProject(Banyan.Domain.Project model)
        {
          
            var p = new Milvus.Project();
            try
            {
                p.ProjectManager = model.ProjectManager??"";
                p.DDManager = model.DDManager??"";
                p.EditorName = model.EditorName;
                p.GroupMember = model.groupMember??"";
                p.Currency = model.Currency??"";
                p.Name = model.Name;
                p.PubTime = model.PubTime.ToString("yyyy-MM-dd");
                p.Summary = string.Join("\n", new[] { model.Summary, model.Background, model.HighLight, model.Risk, model.UpdatedNews });
                p.InteralPTCP = model.InteralPTCP??"";
                p.IsPrivate = model.IsPrivate;
                p.Id = model.Id;
                p.ToRoleId = model.ToRoleId;
            }catch(Exception e)
            {
                Logger.Error(e.Message, e);
            }
            return p;
        }
        public Milvus.AjaxReply Add(Milvus.Project project)
        {
            try
            {
                var reply = client.add(new AddUpdateRequest { Project = project });
                return reply;
            }
            catch (Exception ex)
            {
                Logger.Error(ex.Message, ex);
                return null;
            }
        }
        public Milvus.AjaxReply Update(Milvus.Project project)
        {
            try
            {
                var reply = client.update(new AddUpdateRequest { Project = project });
                return reply;
            }
            catch (Exception ex)
            {
                Logger.Error(ex.Message, ex);
                return null;
            }
        }
        public Milvus.AjaxReply Delete(string id)
        {
            try
            {
                var reply = client.delete(new DeleteRequest { Id = id });
                return reply;
            }
            catch (Exception ex)
            {
                Logger.Error(ex.Message, ex);
                return null;
            }
        }
    }
}
