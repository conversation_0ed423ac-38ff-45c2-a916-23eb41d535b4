using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Banyan.Code;
using Banyan.Domain;
using DAL.Base;

namespace Banyan.Apps
{
    /// <summary>
    /// 预计算诊断服务
    /// 提供预计算系统的监控、诊断和性能分析功能
    /// </summary>
    public class PrecomputeDiagnosticService
    {
        private readonly NewsPrecomputeService _precomputeService;
        private readonly NewsBLL _newsBLL;
        private readonly MemberBLL _memberBLL;
        
        // 单例实例
        private static readonly Lazy<PrecomputeDiagnosticService> _instance = 
            new Lazy<PrecomputeDiagnosticService>(() => new PrecomputeDiagnosticService());
        
        /// <summary>
        /// 获取PrecomputeDiagnosticService的单例实例
        /// </summary>
        public static PrecomputeDiagnosticService Instance => _instance.Value;
        
        /// <summary>
        /// 私有构造函数，确保单例模式
        /// </summary>
        private PrecomputeDiagnosticService()
        {
            _precomputeService = NewsPrecomputeService.Instance;
            _newsBLL = new NewsBLL();
            _memberBLL = new MemberBLL();
            
            Logger.Info("PrecomputeDiagnosticService initialized");
        }
        
        /// <summary>
        /// 执行完整的系统诊断
        /// </summary>
        /// <returns>诊断报告</returns>
        public async Task<DiagnosticReport> RunFullDiagnosticAsync()
        {
            var report = new DiagnosticReport
            {
                StartTime = DateTime.Now
            };
            
            try
            {
                Logger.Info("开始执行预计算系统完整诊断");
                
                // 1. 基础统计信息
                report.Statistics = await _precomputeService.GetPrecomputeStatisticsAsync();
                
                // 2. Redis连接测试
                report.RedisConnectionTest = await TestRedisConnectionAsync();
                
                // 3. 缓存命中率分析
                report.CacheHitRateAnalysis = await AnalyzeCacheHitRateAsync();
                
                // 4. 性能测试
                report.PerformanceTest = await RunPerformanceTestAsync();
                
                // 5. 数据质量检查
                report.DataQualityCheck = await CheckDataQualityAsync();
                
                report.EndTime = DateTime.Now;
                report.Duration = report.EndTime - report.StartTime;
                report.OverallStatus = DetermineOverallStatus(report);
                
                Logger.Info($"预计算系统诊断完成，总耗时: {report.Duration.TotalSeconds:F2}秒，状态: {report.OverallStatus}");
                return report;
            }
            catch (Exception ex)
            {
                report.EndTime = DateTime.Now;
                report.Duration = report.EndTime - report.StartTime;
                report.OverallStatus = DiagnosticStatus.Error;
                report.ErrorMessage = ex.Message;
                
                Logger.Error($"预计算系统诊断失败: {ex.Message}", ex);
                return report;
            }
        }
        
        /// <summary>
        /// 测试Redis连接
        /// </summary>
        /// <returns>连接测试结果</returns>
        private async Task<RedisConnectionTestResult> TestRedisConnectionAsync()
        {
            var result = new RedisConnectionTestResult
            {
                StartTime = DateTime.Now
            };
            
            try
            {
                // 基础连接测试
                result.BasicConnectionTest = _precomputeService.CheckRedisConnection();
                
                // 写入性能测试
                var writeStartTime = DateTime.Now;
                string testKey = $"diagnostic_write_test_{Guid.NewGuid()}";
                string testValue = new string('A', 1024); // 1KB测试数据
                
                RedisUtil.Set(testKey, testValue, TimeSpan.FromMinutes(1));
                result.WriteLatency = (DateTime.Now - writeStartTime).TotalMilliseconds;
                
                // 读取性能测试
                var readStartTime = DateTime.Now;
                string retrievedValue = RedisUtil.GetValue(testKey);
                result.ReadLatency = (DateTime.Now - readStartTime).TotalMilliseconds;
                
                result.ReadWriteConsistency = testValue.Equals(retrievedValue);
                
                // 清理测试数据
                RedisUtil.Remove(testKey);
                
                result.Status = result.BasicConnectionTest && result.ReadWriteConsistency ? 
                    DiagnosticStatus.Healthy : DiagnosticStatus.Warning;
                    
                result.EndTime = DateTime.Now;
                return result;
            }
            catch (Exception ex)
            {
                result.Status = DiagnosticStatus.Error;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.Now;
                return result;
            }
        }
        
        /// <summary>
        /// 分析缓存命中率
        /// </summary>
        /// <returns>缓存命中率分析结果</returns>
        private async Task<CacheHitRateAnalysis> AnalyzeCacheHitRateAsync()
        {
            var analysis = new CacheHitRateAnalysis();
            
            try
            {
                // 获取活跃用户列表
                var activeUsers = await GetSampleActiveUsersAsync(10); // 取样10个用户
                
                int totalRequests = 0;
                int cacheHits = 0;
                
                foreach (var userId in activeUsers)
                {
                    totalRequests++;
                    
                    // 检查用户推荐缓存
                    var precomputedRecommendations = _precomputeService.GetPrecomputedUserRecommendations(userId);
                    if (precomputedRecommendations != null && precomputedRecommendations.Count > 0)
                    {
                        cacheHits++;
                    }
                }
                
                analysis.TotalRequests = totalRequests;
                analysis.CacheHits = cacheHits;
                analysis.HitRate = totalRequests > 0 ? (double)cacheHits / totalRequests * 100 : 0;
                analysis.Status = analysis.HitRate >= 80 ? DiagnosticStatus.Healthy : 
                                 analysis.HitRate >= 50 ? DiagnosticStatus.Warning : DiagnosticStatus.Error;
                
                return analysis;
            }
            catch (Exception ex)
            {
                analysis.Status = DiagnosticStatus.Error;
                analysis.ErrorMessage = ex.Message;
                return analysis;
            }
        }
        
        /// <summary>
        /// 运行性能测试
        /// </summary>
        /// <returns>性能测试结果</returns>
        private async Task<PerformanceTestResult> RunPerformanceTestAsync()
        {
            var result = new PerformanceTestResult
            {
                StartTime = DateTime.Now
            };
            
            try
            {
                // 测试预计算性能
                var testUserId = await GetSampleUserIdAsync();
                if (testUserId > 0)
                {
                    var precomputeStartTime = DateTime.Now;
                    await _precomputeService.ForceRefreshUserRecommendationsAsync(testUserId, 10, 0.4);
                    result.PrecomputeLatency = (DateTime.Now - precomputeStartTime).TotalMilliseconds;
                    
                    // 测试缓存读取性能
                    var cacheReadStartTime = DateTime.Now;
                    var cachedResult = _precomputeService.GetPrecomputedUserRecommendations(testUserId);
                    result.CacheReadLatency = (DateTime.Now - cacheReadStartTime).TotalMilliseconds;
                    
                    result.CacheReadSuccess = cachedResult != null && cachedResult.Count > 0;
                }
                
                result.Status = result.PrecomputeLatency < 5000 && result.CacheReadLatency < 100 ? 
                    DiagnosticStatus.Healthy : DiagnosticStatus.Warning;
                    
                result.EndTime = DateTime.Now;
                return result;
            }
            catch (Exception ex)
            {
                result.Status = DiagnosticStatus.Error;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.Now;
                return result;
            }
        }
        
        /// <summary>
        /// 检查数据质量
        /// </summary>
        /// <returns>数据质量检查结果</returns>
        private async Task<DataQualityCheckResult> CheckDataQualityAsync()
        {
            var result = new DataQualityCheckResult();
            
            try
            {
                // 检查向量化新闻数量
                var thirtyDaysAgo = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd");
                var vectorizedNewsWhere = $"VectorStatus = 1 AND PubTime >= '{thirtyDaysAgo}'";
                var vectorizedNews = _newsBLL.GetList(vectorizedNewsWhere, 1000, 1, "Id", "Id");
                result.VectorizedNewsCount = vectorizedNews.Count;
                
                // 检查总新闻数量
                var totalNewsWhere = $"PubTime >= '{thirtyDaysAgo}'";
                var totalNews = _newsBLL.GetList(totalNewsWhere, 1000, 1, "Id", "Id");
                result.TotalNewsCount = totalNews.Count;
                
                result.VectorizationRate = result.TotalNewsCount > 0 ? 
                    (double)result.VectorizedNewsCount / result.TotalNewsCount * 100 : 0;
                
                // 检查用户向量质量
                var allUsers = _memberBLL.GetAllList();
                var activeUsers = allUsers.Where(u => u.Status == (int)MemberStatus.enable).ToList();
                result.TotalActiveUsers = activeUsers.Count;
                
                int usersWithVectors = 0;
                foreach (var user in activeUsers.Take(50)) // 取样检查
                {
                    var userInterestVectorRetrieval = new UserInterestVectorRetrieval();
                    var userVector = await userInterestVectorRetrieval.GetUserInterestVectorAsync(user.Id);
                    if (userVector != null && userVector.Length > 0)
                    {
                        usersWithVectors++;
                    }
                }
                
                result.UsersWithVectorsRate = activeUsers.Count > 0 ? 
                    (double)usersWithVectors / Math.Min(50, activeUsers.Count) * 100 : 0;
                
                result.Status = result.VectorizationRate >= 70 && result.UsersWithVectorsRate >= 50 ? 
                    DiagnosticStatus.Healthy : DiagnosticStatus.Warning;
                
                return result;
            }
            catch (Exception ex)
            {
                result.Status = DiagnosticStatus.Error;
                result.ErrorMessage = ex.Message;
                return result;
            }
        }
        
        /// <summary>
        /// 获取样本活跃用户
        /// </summary>
        /// <param name="count">用户数量</param>
        /// <returns>用户ID列表</returns>
        private async Task<List<int>> GetSampleActiveUsersAsync(int count)
        {
            var allUsers = _memberBLL.GetAllList();
            var activeUsers = allUsers.Where(u => u.Status == (int)MemberStatus.enable)
                                    .Take(count)
                                    .Select(u => u.Id)
                                    .ToList();
            return activeUsers;
        }
        
        /// <summary>
        /// 获取样本用户ID
        /// </summary>
        /// <returns>用户ID</returns>
        private async Task<int> GetSampleUserIdAsync()
        {
            var sampleUsers = await GetSampleActiveUsersAsync(1);
            return sampleUsers.FirstOrDefault();
        }
        
        /// <summary>
        /// 确定整体状态
        /// </summary>
        /// <param name="report">诊断报告</param>
        /// <returns>整体状态</returns>
        private DiagnosticStatus DetermineOverallStatus(DiagnosticReport report)
        {
            var statuses = new List<DiagnosticStatus>
            {
                report.RedisConnectionTest?.Status ?? DiagnosticStatus.Error,
                report.CacheHitRateAnalysis?.Status ?? DiagnosticStatus.Error,
                report.PerformanceTest?.Status ?? DiagnosticStatus.Error,
                report.DataQualityCheck?.Status ?? DiagnosticStatus.Error
            };
            
            if (statuses.Any(s => s == DiagnosticStatus.Error))
                return DiagnosticStatus.Error;
            if (statuses.Any(s => s == DiagnosticStatus.Warning))
                return DiagnosticStatus.Warning;
            return DiagnosticStatus.Healthy;
        }
    }

    #region 诊断数据模型

    /// <summary>
    /// 诊断状态枚举
    /// </summary>
    public enum DiagnosticStatus
    {
        Healthy,
        Warning,
        Error
    }

    /// <summary>
    /// 诊断报告
    /// </summary>
    public class DiagnosticReport
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public DiagnosticStatus OverallStatus { get; set; }
        public string ErrorMessage { get; set; }

        public PrecomputeStatistics Statistics { get; set; }
        public RedisConnectionTestResult RedisConnectionTest { get; set; }
        public CacheHitRateAnalysis CacheHitRateAnalysis { get; set; }
        public PerformanceTestResult PerformanceTest { get; set; }
        public DataQualityCheckResult DataQualityCheck { get; set; }
    }

    /// <summary>
    /// Redis连接测试结果
    /// </summary>
    public class RedisConnectionTestResult
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public DiagnosticStatus Status { get; set; }
        public string ErrorMessage { get; set; }

        public bool BasicConnectionTest { get; set; }
        public double WriteLatency { get; set; }
        public double ReadLatency { get; set; }
        public bool ReadWriteConsistency { get; set; }
    }

    /// <summary>
    /// 缓存命中率分析结果
    /// </summary>
    public class CacheHitRateAnalysis
    {
        public DiagnosticStatus Status { get; set; }
        public string ErrorMessage { get; set; }

        public int TotalRequests { get; set; }
        public int CacheHits { get; set; }
        public double HitRate { get; set; }
    }

    /// <summary>
    /// 性能测试结果
    /// </summary>
    public class PerformanceTestResult
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public DiagnosticStatus Status { get; set; }
        public string ErrorMessage { get; set; }

        public double PrecomputeLatency { get; set; }
        public double CacheReadLatency { get; set; }
        public bool CacheReadSuccess { get; set; }
    }

    /// <summary>
    /// 数据质量检查结果
    /// </summary>
    public class DataQualityCheckResult
    {
        public DiagnosticStatus Status { get; set; }
        public string ErrorMessage { get; set; }

        public int VectorizedNewsCount { get; set; }
        public int TotalNewsCount { get; set; }
        public double VectorizationRate { get; set; }

        public int TotalActiveUsers { get; set; }
        public double UsersWithVectorsRate { get; set; }
    }

    #endregion
}
