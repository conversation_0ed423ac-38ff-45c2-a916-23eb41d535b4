﻿using Banyan.Apps;
using Banyan.Web.Filters;
using System.Collections.Generic;
using System.Web.Mvc;

namespace Banyan.Web.Controllers
{
    public class MeetController : BaseController
    {
        public ActionResult Meets()
        {
            return View();
        }
        public ActionResult Drafts()
        {
            return View();
        }
        // GET: api/Meet
        public IEnumerable<string> Get()
        {
            return new string[] { "value1", "value2" };
        }

        // GET: api/Meet/5
        public string Get(int id)
        {
            return "value";
        }

        /// <summary>
        /// 添加/编辑项目
        /// </summary>
        /// <returns></returns>
        public ActionResult MeetSet(int id = 0)
        {
            ViewData["Id"] = id;
            ViewData["rolelist"] = new RoleBLL().GetList();
            ViewData["creatorList"] = new MemberBLL().GetAllList();
            ViewData["staffList"] = new MemberBLL().GetStaffList();

            return View();
        }

        /// <summary>
        /// 预览
        /// </summary>
        /// <returns></returns>
        public ActionResult ResearchPreview(int id = 0)
        {
            ViewBag.id = id;
            return View();
        }

        /// <summary>
        /// 预览
        /// </summary>
        /// <returns></returns>
        public ActionResult Detail(int id = 0)
        {
            ViewData["Id"] = id;
            ViewData["rolelist"] = new RoleBLL().GetList();
            ViewData["creatorList"] = new MemberBLL().GetAllList();
            ViewData["staffList"] = new MemberBLL().GetStaffList();

            return View();
        }

        /// <summary>
        /// 预览
        /// </summary>
        /// <returns></returns>
        public ActionResult Readonly(int id = 0)
        {
            ViewData["Id"] = id;
            ViewData["rolelist"] = new RoleBLL().GetList();
            ViewData["creatorList"] = new MemberBLL().GetAllList();
            ViewData["staffList"] = new MemberBLL().GetStaffList();

            return View();
        }
    }
}
