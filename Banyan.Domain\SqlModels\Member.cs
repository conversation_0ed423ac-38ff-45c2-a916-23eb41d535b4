using Entity.Base;
using System;
using System.Collections.Generic;
using Utility;
using System.Globalization;


namespace Banyan.Domain
{
    public class MemberSortAddTime : IComparer<Member>/*实现 IComparer<T> 接口中的 Compare 方法，
                                       在使用Sort排序时会根据Compare方法体的规定进行排序*/
    {
        public int Compare(Member x, Member y)
        {
            return DateTime.Compare(y.AddTime, x.AddTime); // (x.AddTime.CompareTo(y.AddTime));//（-x.age.CompareTo(y.age）降序
        }
    }

    [Serializable]

    public class Member : IComparable<Member>
    {
        CultureInfo ch = new CultureInfo("zh-CN");

        public Member() { }

        public int Id { get; set; }

        /// <summary>
        /// 头像
        /// </summary>
        public string Avatar { get; set; }


        /// <summary>
        /// 真实姓名
        /// </summary>
        public string RealName { get; set; }

        /// <summary>
        /// 用户OpenId
        /// </summary>
        public string OpenId { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string Telephone { get; set; }

        public string Mail { get; set; } = string.Empty;
        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

  
        public string RoleName { get; set; }

        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime AddTime { get; set; } = DateTime.Now;
        public string Description { get; set; } = string.Empty;

        public bool limitedMedical { get; set; } = false;
        /// <summary>
        /// 状态
        /// </summary>
        public Byte Status { get; set; } = (int)MemberStatus.enable;

        /// <summary>
        /// 状态
        /// </summary>
        public Byte StatusResearch { get; set; } = (int)MemberStatus.enable;

        /// <summary>
        /// 账号等级
        /// </summary>
        public byte Levels { get; set; } = (byte)MemberLevels.Normal;

        /// <summary>
        /// 账号等级
        /// </summary>
        public byte LevelResearch { get; set; } = (byte)MemberLevelResearch.undefined;
        // homePostalAddress 字段记录
        public string Funds { get; set; } = string.Empty;
        /// <summary>
        /// 用户组
        /// </summary>
        /// 1 消费组 2 技术组 3互联网组 5医疗组 6其他
        public string Groups { get; set; } = string.Empty;

        public bool limitedJoinTime { get; set; } = false;

        public int Compare(Member a, Member b )
        {
            return string.Compare(a.RealName, b.RealName, ch, CompareOptions.None);  
        }
        public int CompareTo(Member b)
        {
            return string.Compare(this.RealName, b.RealName, ch, CompareOptions.None);
        }
        public override bool Equals(object obj)
        {
            if (obj == null)
            {
                return false;
            }
            if ((obj.GetType().Equals(this.GetType())) == false)
            {
                return false;
            }
            Member tmp = (Member)obj;
            return this.RealName == tmp.RealName;
        }
        public override int GetHashCode()
        {
            return this.RealName.GetHashCode();
        }
    }

    public enum MemberStatus
    {
        /// <summary>
        /// 正常
        /// </summary>
        enable = 1,

        /// <summary>
        /// 禁用
        /// </summary>
        disable = 2,

        /// <summary>
        /// 待审核
        /// </summary>
        review = 3,

        /// <summary>
        /// 审核未通过
        /// </summary>
        notpass = 4,

        /// <summary>
        /// Research审核未通过
        /// </summary>
        notpassResearch = 14,

    }

    public enum MemberLevels
    {
        /// <summary>
        /// 普通用户
        /// </summary>
        Normal = 0,

        /// <summary>
        /// 超级管理员
        /// </summary>
        Administrator = 1,

        /// <summary>
        /// 超级用户
        /// </summary>
        SuperUser = 2,

        /// <summary>
        /// 受限制用户，只能查看自己的项目
        /// </summary>
        LimitedUser = 3,

        Partner = 5, //投资合伙人，可以看到打分结果
        DealUSD = 9, // 可以看到对应基金的项目进展
        DealRMB = 10,
        DealALL = 11,

        OnlyUSD = 21, // 仅可查看美元项目
        OnlyRMB = 22, // 仅可查看人民币项目
    }

    public enum MemberLevelResearch
    {
        undefined = 0,
        /// <summary>
        /// 受限实习生
        /// </summary>
        limitedIntern = 16,
        /// <summary>
        /// 普通用户
        /// </summary>
        normal = 10,

        /// <summary>
        /// 普通编辑用户
        /// </summary>
        editor = 11,

        /// <summary>
        /// 编辑管理员
        /// </summary>
        admin = 12,

        /// <summary>
        /// 投资团队成员
        /// </summary>
        investment = 13,

        /// <summary>
        /// 合伙人
        /// </summary>
        partner = 14,
        /// <summary>
        /// 超级管理员
        /// </summary>
        superAdmin = 15,
    }

}
