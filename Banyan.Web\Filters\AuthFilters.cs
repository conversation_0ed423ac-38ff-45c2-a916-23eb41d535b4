﻿
using Banyan.Apps;
using System;
using System.Linq;
using System.Web.Mvc;
using Banyan.Code;

namespace Banyan.Web.Filters
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, Inherited = true, AllowMultiple = false)]
    public class AuthFilterAttribute : ActionFilterAttribute
    {
        public AuthFilterAttribute() { }

        /// <summary>
        /// 是否允许匿名
        /// </summary>
        public bool Allowanonymous { get; set; }

        public sealed override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            if (Allowanonymous) return;
            if (IsAuth()) return;
            bool flag = filterContext.ActionDescriptor.IsDefined(typeof(AllowAnonymousAttribute), true);
            if (flag) return;
            UnauthorizedRequest(filterContext);
        }

        public sealed override void OnActionExecuted(ActionExecutedContext filterContext)
        {
            base.OnActionExecuted(filterContext);
        }

        public sealed override void OnResultExecuting(ResultExecutingContext filterContext)
        {
            base.OnResultExecuting(filterContext);
        }

        public sealed override void OnResultExecuted(ResultExecutedContext filterContext)
        {
            base.OnResultExecuted(filterContext);
        }

        private bool IsAuth()
        {
            var model = new MemberBLL().GetLogOnUser();
          
            if (model != null)
            {
                WebHelper.WriteCookie("banyan_logon_user", DESEncrypt.Encrypt($"{model.Id},{model.RealName.Replace(",", "，")},{model.Levels == (byte)Banyan.Domain.MemberLevels.Administrator}"), 360, true);
                return AuthorizeCore(model.RealName, model.Status);
            }
            else
            {
                return false;
            }
        }

        private void UnauthorizedRequest(ActionExecutingContext filterContext)
        {
            if (filterContext.HttpContext.Request.IsAjaxRequest())
                UnauthorizedAjaxRequest(filterContext);
            else
                UnauthorizedGenericRequest(filterContext);
        }

        /// <summary>
        /// 权限校验
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="userRoles"></param>
        /// <returns></returns>
        protected virtual bool AuthorizeCore(string userName, byte userStatus)
        {
            return userStatus == (int)Banyan.Domain.MemberStatus.enable;
        }

        /// <summary>
        /// 未授权处理
        /// </summary>
        /// <param name="filterContext"></param>
        protected virtual void UnauthorizedGenericRequest(ActionExecutingContext filterContext)
        {
            filterContext.Result = new RedirectResult("/login/index?returnurl=" + filterContext.HttpContext.Request.Url.PathAndQuery.ToLower());
        }

        protected virtual void UnauthorizedAjaxRequest(ActionExecutingContext filterContext)
        {
            var acceptTypes = filterContext.HttpContext.Request.AcceptTypes;
            if (acceptTypes.Contains("*/*") || acceptTypes.Contains("application/json"))
            {
                filterContext.Result = new JsonResult { Data = new { code = 0, msg = "nologin" }, JsonRequestBehavior = JsonRequestBehavior.AllowGet };
            }
            else
            {
                filterContext.Result = new ContentResult { Content = "nologin" };
            }
        }

    }
}