﻿@using Banyan.Domain
@{
    ViewBag.Name = "会议管理";
    Layout = "/Views/Shared/_Layout.cshtml";
    var rolelist = (List<Banyan.Domain.Role>)ViewData["rolelist"];
    var manager = ViewData["manager"] as Banyan.Domain.Member;
}
 
<style>
    a {
        color: #4E6EF2;
    }
</style>
<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li>查看会议</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <div class="tab-pane active">
                @*<template v-for="item in waterArray">
                        <p class="water-mark" :style="{left: item.wid + '%', top: item.hei + '%'}">@(manager.Id)</p>
                    </template>*@
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="pull-right search-bar"></div>
                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                                <div class="form-group">
                                    <select class="form-control" id="creator" name="creator" size="1">
                                    </select>
                                </div>
                                <div class="form-group">
                                    <div class="input-daterange input-group">
                                        <input class="form-control" type="text" id="startdate" name="startdate" placeholder="开始时间">
                                        <span class="input-group-addon" style="border-left:none;border-right:none;"><i class="fa fa-chevron-right"></i></span>
                                        <input class="form-control" type="text" id="enddate" name="enddate" placeholder="截止时间">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <input class="form-control" type="checkbox" id="isRepeat" name="isRepeat" value="1" />
                                    <div class="btn">重复会议模板</div>
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="title-name">搜索</label>
                                    <input class="form-control" type="text" id="keyname" name="keyname" placeholder="搜索">
                                </div>

                                <div class="form-group">
                                    <a class="btn btn-default" id="dosearch">搜索</a>
                                    <a class="btn btn-default" href="@(Url.Action("MeetSet","Meet"))"><i class="fa fa-plus"></i>&nbsp;&nbsp;新建会议</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <table class="layui-hide" id="table-list" lay-filter="list-filter"></table>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="bartpl">
    <div class="btn-group" role="group" data-toggle="buttons">
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="查看" lay-event="readonly" data-original-title="查看"><i class="fa fa-eye"></i></button>
        {{#  if(d.IsOperate){ }}
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="编辑" lay-event="modify" data-original-title="编辑"><i class="fa fa-pencil"></i></button>
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="删除" lay-event="delete" data-original-title="删除"><i class="fa fa-times"></i></button>
        {{#  } }}
    </div>
</script>

@section scripts{
    <script type="text/javascript">
        var queryParams = {}, date = new Date();
        layui.use(['laypage', 'layer', 'table', 'laydate'], function () {
            var laypage = layui.laypage,
                layer = layui.layer,
                laydate = layui.laydate,
                table = layui.table;

            table.render({
                elem: '#table-list'
                //, height: 570
                , url: '@(Url.Action("meetlist", "adminapi"))'
                , where: {status: @((int)MeetStatus.normal)}
                , page: true
                , method: 'post'
                , cols: [[
                    {
                        field: 'Title', title: '会议名', fixed: 'left', width: 160, templet: function (d) {
                            return `<a href='readonly?id=${d.Id}'>${d.Title}</a>`
                         }
                    }
                    , {
                        field: 'StartTimeStr', title: '开始时间', width: 105, templet: function (d) {
                            return '<span '+ (parseInt(d.StartTime.match(/\d+/)[0]) >= Date.now() ? 'style="color: blue">' : '>') +d.StartTimeStr+'</span>';
                        }
                    },
                    { field: 'Name', title: '讨论事项'  }
                    //, { field: 'Participant', title: '外部参会人', width: 100 }
                    , { field: 'InternalPTCP', title: '高榕参会人', width: 100 }
                    ,{
                        field: 'Duration', title: '时长',  width: 75, templet: function (d) {
                            return  d.Duration + '分钟';
                        }
                    }
                    , { field: 'CreatorName', title: '创建人', width: 75 }
                    ////, { field: 'Manager', title: '负责人',  width: 80 }
                    , { fixed: 'right', width: 120, align: 'center', toolbar: '#bartpl' }
                ]],
                done: function () { }
            });

            table.on('tool(list-filter)', function (obj) {
            var data = obj.data
                , layEvent = obj.event;

            if (layEvent === 'issue') {
                fieldset(data.Id, 'issue', data.Status);
            }else if (layEvent === 'delete') {
                layer.confirm('确认删除该项目吗？', function (index) {
                    layer.close(index);
                    fieldset(data.Id, 'delete', data.Status)
                });
            } else if (layEvent === 'modify') {
                window.location.href = "meetset?id=" + data.Id;
            } else if (layEvent === 'readonly') {
                window.location.href = "readonly?id=" + data.Id;
            }else if (layEvent === 'recomend') {
                fieldset(data.Id, 'recomend', data.IsRecommend ? 1 : 0);
            } else if (layEvent === 'top') {
                fieldset(data.Id, 'top', data.IsStick ? 1 : 0);
            } else if (layEvent === 'sort') {
                layer.prompt({ title: '排序值设置' }, function (text, index) {
                    if (!/^(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9])$/.test(text)) {
                        layer.msg('请输入0-255的正整数！');
                        return;
                    }
                    layer.close(index);
                    fieldset(data.Id, 'sort', parseInt(text));
                });
            }
            return;
        });

        laypage.render({
            elem: 'pageBar'
            , count: 100
            , jump: function (obj, first) {
                if (!first) {
                    layer.msg('第' + obj.curr + '页');
                }
            }
        });
        table.on('rowDouble(list-filter)', function (obj) {
            console.log(obj)
            window.location.href = "readonly?id=" + obj.data.Id;
            //obj 同上
        });
        laydate.render({
            elem: '#startdate',
            type: 'datetime'
        });

        laydate.render({
            elem: '#enddate',
            type: 'datetime'
        });
        $('#keyname').on('keypress', function(event) {
            if (event.keyCode === 13) {
                $('#dosearch').trigger('click');
            }
        });
        $('#dosearch').on('click', function () {
            queryParams = {
                ToRoleId: $('#keyclass').val(),
                Name: $('#keyname').val(),
                startdate: $('#startdate').val(),
                enddate: $('#enddate').val(),
                Creator: $('#creator').val(),
                IsRepeat: document.getElementById("isRepeat").checked ? 1 : 0,
                projectStatus:$('#projectStatus').val(),
            }
            table.reload('table-list',{
                where: queryParams,page:{curr:1},
            });
        });

        getCreators();

        });

        function getCreators() {
            $.ajax({
                type: 'POST',
                url: '@(Url.Action("getcreators", "adminapi"))',
                data: { uid:@manager.Id,},
                success: function (data) {
                    var htmlCode = '<option value="">创建人</option>';
                    if (data && data.code == 0) {
                        var list = data.data || [];
                        $.each(list, function (index, item) {
                            htmlCode += '<option value="' + item.RealName + '">' + item.RealName + '</option>';
                        });
                        $('#creator').html(htmlCode);
                    }
                },
                error: function () {
                    layui.layer.msg("很抱歉，请求异常！");
                }
            });
        }

        function fieldset(id, field, state) {
            $.ajax({
                type: 'POST',
                url: '@(Url.Action("meetset", "adminapi"))',
                data: { id: id, field: field, state: state },
                success: function (data) {
                    if (data.code == 0) {
                        layer.msg('操作成功！');
                        $('#dosearch').click();
                    } else {
                        layer.msg(data.msg);
                    }
                },
                error: function () {
                    layui.layer.msg("很抱歉，请求异常！");
                }
            });
        }

    </script>
}
