﻿var layer, laydate;
layui.use(['layer', 'laydate', 'form'], function () {
    var layer = layui.layer, laydate = layui.laydate;
    laydate.render({
        elem: '#StartTime',
        type: 'datetime',
        done: function (value, date, endDate) {
            app.$data.model.StartTime = value;
        }
    });
    laydate.render({
        elem: '#EndTime',
        type: 'datetime',
        done: function (value, date, endDate) {
            app.$data.model.EndTime = value;
        }
    });
});

$(document).ready(function () {
    $('.select2').select2({
        language: "zh-CN",
        width: "100%",
        height: "32px",
        theme: "classic"
    });
    $('#searchType').on('select2:select', function (e) {
        var data = e.params.data;
        console.log(data);
        app.onChangeSearchType(data.id);
    })
    $('#groupMember').on('select2:select', function (e) {
        var data = e.params.data;
        console.log(data);
        var idx = app.speakers.indexOf(data.text);
        if (idx == -1) {
            app.speakers.push(data.text);
        } else {
            app.speakers.splice(idx, 1);
        }
    })
    $('#isRepeat').on('select2:select', function (e) {
        var data = e.params.data;
        console.log(data);
        app.model.IsRepeat = data.id;
    })
    $('#groupMember').on('select2:unselect', function (e) {
        var data = e.params.data;
        console.log(data);
        var idx = app.speakers.indexOf(data.text);
        if (idx == -1) {
            app.speakers.push(data.text);
        } else {
            app.speakers.splice(idx, 1);
        }
    })

    $('#uploadbtn').click(function () {
        $('#filesource').click();
    });
});

 

function trimVal(val) {
    return $.trim(val);
}


var app = new Vue({
    el: '#page-app',
    data: {
        id: id,
        model: { Duration: 60, IsRepeat: 0, IsOperate: true },
        progress: 0,
        yearList: [],
        fileList: [],
        dataListBP: [],
        DD: [],
        BP: [],
        dataListDD: [],
        loadState: -1,
        searchType: "-1",
        keywords: '',
        searchAdd: '',
        searchAddId: null,
        summary: '',
        attachLogin: [],
        attachDD: [],
        attachResearch: [],
        attachOther: [],
        speakers: [],
        internalPTCP: []
    },
    //computed: {
    //    searchIdx: function () {
    //        var that = this;
    //        return that.searchList.indexOf(that.keywords);
    //    }
    //},
    methods: {
        initYear: function () {
            var currYear = (new Date()).getFullYear();
            var yearList = [];
            for (var i = 0; currYear - i >= 1990; i++) {
                yearList.push(currYear - i);
            }
            this.yearList = yearList;
        },
      
        addSearchData: function() {
            var that = this;
            var res = {
                Id: that.searchAddId,
                Name: that.searchAdd
            };
            switch (that.searchType) {
                case "0": that.addAttachLogin(res, 1); break;
                case "1": that.addAttachDD(res); break;
                case "2": that.addAttachResearch(res); break;
                case "3": that.addAttachOther(); break;
                case "4": that.addAttachLoginOther(); break;
                case "5": that.addAttachLogin(res, 5); break;
                default: console.log(that.searchType);
            }
        },
        addAttachLogin: function(model, type) {
            var that = this;
            model = {
                MeetId: that.id,
                SourceId: model.Id,
                SourceType: type,
                Name: model.Name,
                Speakers: that.speakers.join(",")
            }
            that.attachLogin.unshift(model);
            if (that.id > 0) {
                $.post('/adminapi/addMeetAttach', model, function (res) {
                    if (res.code == 0) {
                        that.attachLogin[0].Id = res.data;
                        that.speakers = [];
                        $("#groupMember").val("").trigger('change');
                    }
                }).error(function (xhr, errorText, errorType) {
                    that.loadState = -999;
                });
            } else {
                that.speakers = [];
                $("#groupMember").val("").trigger('change');
            }
        },
        removeAttachLogin: function(elem) {
            var idx = +elem.currentTarget.dataset.idx
            var that = this;
            if (that.id == 0) {
                return that.attachLogin.splice(idx, 1);
            }
            $.post('/adminapi/delMeetAttach', { id: that.attachLogin[idx].Id }, function (res) {
                if (res.code != 0) {
                    layer.msg('移除失败');
                    return;
                }
                that.attachLogin.splice(idx, 1);
            }).error(function (xhr, errorText, errorType) {
                that.loadState = -999;
            });
        },
        addAttachOther: function() {
            var that = this;
            model = {
                MeetId: that.id,
                SourceId: -1,
                SourceType: 4,
                Name: that.summary,
                Speakers: that.speakers.join(",")
            }
            that.attachOther.unshift(model);
            if (that.id > 0) {
                $.post('/adminapi/addMeetAttach', model, function (res) {
                    if (res.code == 0) {
                        that.attachOther[0].Id = res.data;
                        that.speakers = [];
                        $("#groupMember").val("").trigger('change');
                    }
                }).error(function (xhr, errorText, errorType) {
                    that.loadState = -999;
                });
            } else {
                that.speakers = [];
                $("#groupMember").val("").trigger('change');
            }
        },
        addAttachLoginOther: function() {
            var that = this;
            model = {
                MeetId: that.id,
                SourceId: -1,
                SourceType: 4,
                Name: 'login项目讨论-具体待定',
                Speakers: ""
            }
            that.attachOther.unshift(model);
            if (that.id > 0) {
                $.post('/adminapi/addMeetAttach', model, function (res) {
                    if (res.code == 0) {
                        that.attachOther[0].Id = res.data;
                        that.speakers = [];
                        $("#groupMember").val("").trigger('change');
                    }
                }).error(function (xhr, errorText, errorType) {
                    that.loadState = -999;
                });
            } else {
                that.speakers = [];
                $("#groupMember").val("").trigger('change');
            }
        },
        removeAttachOther: function(elem) {
            var idx = +elem.currentTarget.dataset.idx
            var that = this;
            if (that.id == 0) {
                return that.attachOther.splice(idx, 1);
            }
            $.post('/adminapi/delMeetAttach', { id: that.attachOther[idx].Id }, function (res) {
                if (res.code != 0) {
                    layer.msg('移除失败');
                    return;
                }
                that.attachOther.splice(idx, 1);
            }).error(function (xhr, errorText, errorType) {
                that.loadState = -999;
            });
        },
        removeAttachDD: function(elem) {
            var idx = +elem.currentTarget.dataset.idx
            var that = this;
            if (that.id == 0) {
                return that.attachDD.splice(idx, 1);
            }
            $.post('/adminapi/delMeetAttach', { id: that.attachDD[idx].Id }, function (res) {
                if (res.code != 0) {
                    layer.msg('移除失败');
                    return;
                }
                that.attachDD.splice(idx, 1);
            }).error(function (xhr, errorText, errorType) {
                that.loadState = -999;
            });
        },
        removeAttachResearch: function(elem) {
            var idx = +elem.currentTarget.dataset.idx
            var that = this;
            if (that.id == 0) {
                return that.attachResearch.splice(idx, 1);
            }
            $.post('/adminapi/delMeetAttach', { id: that.attachResearch[idx].Id }, function (res) {
                if (res.code != 0) {
                    layer.msg('移除失败');
                    return;
                }
                that.attachResearch.splice(idx, 1);
            }).error(function (xhr, errorText, errorType) {
                that.loadState = -999;
            });
        },
        addAttachDD: function(model) {
            var that = this;
            model = {
                MeetId: that.id,
                SourceId: model.Id,
                SourceType: 2,
                Name: model.Name,
                Speakers: that.speakers.join(",")
            }
            that.attachDD.unshift(model);
            if (that.id > 0) {
                $.post('/adminapi/addMeetAttach', model, function (res) {
                    if (res.code == 0) {
                        that.attachDD[0].Id = res.data;
                        that.speakers = []
                        $("#groupMember").val("").trigger('change');
                    }
                }).error(function (xhr, errorText, errorType) {
                    that.loadState = -999;
                });
            } else {
                that.speakers = [];
                $("#groupMember").val("").trigger('change');
            }
        },
        addAttachResearch: function(model) {
            var that = this;
            model = {
                MeetId: that.id,
                SourceId: model.Id,
                SourceType: 3,
                Name: model.Name,
                Speakers: that.speakers.join(",")
            }
            that.attachResearch.unshift(model);
            if (that.id > 0) {
                $.post('/adminapi/addMeetAttach', model, function (res) {
                    if (res.code == 0) {
                        that.attachResearch[0].Id = res.data;
                        that.speakers = [];
                        $("#groupMember").val("").trigger('change');
                    }
                }).error(function (xhr, errorText, errorType) {
                    that.loadState = -999;
                });
            } else {
                that.speakers = [];
                $("#groupMember").val("").trigger('change');
            }
        },
        onChangeSearchType: function(data) {
            var that = this;
            that.searchType = data;
            that.searchAdd = "";
            $('.search-type').empty();
            setTimeout(function () {
                $('.search-type').select2({
                    language: "zh-CN",
                    width: "100%",
                    height: "32px",
                    theme: "classic",
                    minimumResultsForSearch: 1,
                    minimumInputLength: 1,
                    placeholder: '搜索项目名',
                    ajax: {
                        url: '/adminapi/searchType',
                        dataType: 'json',
                        type: 'POST',
                        processResults: function (data) {
                            // Transforms the top-level key of the response object from 'items' to 'results'
                            return {
                                results: data.data.map(val => {
                                    val.id = val.Id
                                    val.text = val.Name
                                    return val
                                })
                            };
                        },
                        data: function (params) {
                            var query = {
                                type: app.searchType,
                                Name: params.term,
                            }

                            // Query parameters will be ?search=[term]&type=public
                            return query;
                        }
                    }
                });

                $('.search-type').on('select2:select', function (e) {
                    var data = e.params.data;
                    console.log(data);
                    that.searchAdd = data.Name
                    that.searchAddId = data.Id
                })
            })

            
        },
        rank: function (id, order) {
            var that = this
            $.post('/adminapi/meetattachrank', {
                id, order
            }, function (res) {
                    if (res.code == 0) {
                        that.initAttachment();
                    } else {
                        layer.msg("修改排序失败！");
                    }
            })
        },
        preview: function(e) {
            console.log(e);
            var type = e.currentTarget.dataset.type;
            var id = e.currentTarget.dataset.idx;
            var h = document.documentElement.clientHeight || document.body.clientHeight;
            if (type == 'research') {
                layer.open({
                    type: 2,
                    area: ['1100px', h * 0.8 + 'px'],
                    fix: false,
                    maxmin: true,
                    anim: 5,
                    shade: 0,
                    title: "研究报告预览",
                    content: '/meet/researchpreview?id=' + id,
                });
            } else {
                layer.open({
                    type: 2,
                    area: ['1100px', h * 0.8 + 'px'],
                    fix: false,
                    maxmin: true,
                    anim: 5,
                    shade: 0,
                    title: "项目预览",
                    content: '/index/preview?id=' + id,
                });
            }
        },
        initData: function () {
            var that = this;
            $.post('/adminapi/meetdetail', { id: that.id, init: 1 }, function (res) {
                if (res.code == 0) {
                    res.data.StartTime = res.data.StartTimeStr;
                    that.internalPTCP = res.data.InternalPTCP;
                    $("#InternalPTCP").val(res.data.InternalPTCP.split(',')).trigger('change');
                    $("#manager").val(res.data.Manager.split(',')).trigger('change');
                    $("#isRepeat").val(res.data.IsRepeat).trigger('change');
                    that.model = res.data;
                    that.initAttachment();
                } else {
                    that.loadState = -999;
                }
            }).error(function (xhr, errorText, errorType) {
                that.loadState = -999;
            });
        },
      
        initAttachment: function() {
            var that = this;
            $.post('/adminapi/meetattaches', { id: that.id }, function (res) {
                if (res.code == 0) {
                    that.attachLogin = []
                    that.attachDD = []
                    that.attachResearch = []
                    that.attachOther = []
                    res.data.map(function (data) {
                        if (data.SourceType == 1 || data.SourceType == 5) {
                            that.attachLogin.push(data)
                        } else if (data.SourceType == 2) {
                            that.attachDD.push(data)
                        } else if (data.SourceType == 3) {
                            that.attachResearch.push(data)
                        } else if (data.SourceType == 4) {
                            that.attachOther.push(data)
                        }
                    })
                }
            })
            $.post('/adminapi/meetdocs', { id: that.id }, function (res) {
                if (res.code == 0) {
                    that.fileList = res.data || [];
                    if (that.fileList.length > 0) {
                        setTimeout(function () {
                            $(".lightgalleryDD").lightGallery({
                                speed: 40,
                                download: false,
                                showThumbByDefault: false
                            })
                        }, 400)
                    }
                    that.fileList = that.fileList.filter(function (file) {
                        if (file.AtSuffix === "RECORD") {
                            that.BP.push(file)
                            that.dataListBP.push(file.AtUrl.split(','));
                            return false
                        } else if (file.AtSuffix === "DD") {
                            that.DD.push(file)
                            that.dataListDD.push(file.AtUrl.split(','));
                            return false
                        }
                        return true
                    })

                 
                } else {
                    that.loadState = -999;
                }
            }).error(function (xhr, errorText, errorType) {
                that.loadState = -999;
            });
        },

        paramCheck: function(nocheck) {
            var errVal, that = this;
            if (trimVal(that.model.Title) == '') {
                errVal = '请输入会议名称';
            } else if (trimVal(that.model.InternalPTCP) == '') {
                errVal = '请选择高榕参会人';
            } else if (trimVal(that.model.StartTime) == '') {
                errVal = '请选择开始时间';
            } else if (nocheck != "True" && that.attachLogin.length == 0 &&
                that.attachDD.length == 0 &&
                that.attachResearch.length == 0 &&
                that.attachOther.length == 0) {
                errVal = '请添加讨论事项';
            }
            if (errVal) {
                layer.msg(errVal);
                return false;
            }
            return true;
        },
        paramAttach: function() {
            var list = [], that = this;
            that.fileList.forEach(function (v, i) {
                if (!v.Id) {
                    list.push(v);
                }
            });
            return list;
            //if (list.length > 0) {
            //    that.model.attach = JSON.stringify(list);
            //}
        },
        savePost: function(status) {
            var that = this;
            that.loadState = 0;
            var attach = [];
            if (that.id <= 0) {
                attach = that.paramAttach();
                String.prototype.replaceAll = function (s1, s2) {
                    return this.replace(new RegExp(s1, "gm"), s2);
                }
                for (var i = 0; i < that.BP.length; i++) {
                    if (that.BP[i].AtSuffix == "RECORD") {
                        var BP = {
                            AtUrl: that.BP[i].AtUrl.replaceAll(window.location.origin, ""),
                            AtSuffix: that.BP[i].AtSuffix,
                            AtName: that.BP[i].AtName
                        }
                        attach.push(BP)
                    }
                }
                for (var i = 0; i < that.DD.length; i++) {
                    if (that.DD[i].AtSuffix == "DD") {
                        var DD = {
                            AtUrl: that.DD[i].AtUrl.replaceAll(window.location.origin, ""),
                            AtSuffix: that.DD[i].AtSuffix,
                            AtName: that.DD[i].AtName
                        }
                        attach.push(DD)
                    }
                }
            }
            var meetAttach = []
            if (that.id <= 0) {
                meetAttach = meetAttach.concat(that.attachLogin.map(function (data) {
                    data.sourceType = 1;
                    return data;
                }));
                meetAttach = meetAttach.concat(that.attachDD.map(function (data) {
                    data.sourceType = 2;
                    return data;
                }));
                meetAttach = meetAttach.concat(that.attachResearch.map(function (data) {
                    data.sourceType = 3;
                    return data;
                }));
                meetAttach = meetAttach.concat(that.attachOther.map(function (data) {
                    data.sourceType = 4;
                    return data;
                }));
            }

            $.post('/adminapi/meetsave', { model: that.model, attach: JSON.stringify(attach), meetAttach: JSON.stringify(meetAttach) }, function (res) {
                if (!res || res.code != 0) {
                    that.loadState = -1;
                    layer.msg(res.msg || "服务器繁忙，请稍后重试...");
                    return;
                }
                if (editing == status) {
                    that.loadState = -1;
                    that.id = res.data;
                    that.model.Id = res.data;
                    layer.msg("计划会议保存成功，准备完成后请按发布按钮正式发布");
                    setTimeout(function () {
                        window.location.href = '/meet/drafts';
                    }, 1200);
                } else {
                    layer.msg("发布成功");
                    setTimeout(function () {
                        window.location.href = '/meet/meets';
                    }, 1200);

                }
            }).error(function (xhr, errorText, errorType) {
                that.loadState = -1;
                layer.msg("服务器繁忙，请稍后重试...");
            });
        },

        saveData: function(status, nocheck) {
            var that = this;
            if (that.loadState > -1)
                return;

            if (status < 0) {
                window.history.go(-1);
                return;
            }
            var oldStatus = that.model.Status;
            that.model.Status = status;
            var val1 = $("#InternalPTCP").select2("val") || [];
            that.model.InternalPTCP = val1.join(',');

            switch (that.model.Source) {
                case "0":
                    var val2 = $("#source2").select2("val");
                    that.model.Introducer = val2;
                    break;
                case "1":
                    var val2 = $("#source").select2("val") || [];
                    that.model.Introducer = val2.join(',');
                    val2 = $("#finder").select2("val") || [];
                    that.model.finder = val2.join(',');
                    break;
                case "2":
                    var val2 = $("#source").select2("val") || [];
                    that.model.Introducer = val2.join(',');
                    break;
            }

            var val3 = $("#manager").select2("val") || that.model.Manager;
            that.model.Manager = val3;
            if (that.paramCheck(nocheck)) {
                if (oldStatus == 2 && status == 1) {
                    layer.confirm('会议发布后将无法修改，若需要允许继续编辑请选择保存，确定发布吗？',
                        function (ind) {
                            layer.close(ind);
                            that.savePost(status);
                        });
                } else {
                    that.savePost(status);
                }
                
            }
        }

    },
    created: function () {
        this.initYear();
        if (this.id > 0)
            this.initData();

        $("#page-app").show();
        //this.initGallery();
    }
});