﻿using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.QLWL)]
    public class AnnualReport
    {
        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int Id { get; set; }

        [SqlField]
        public DateTime AddTime { get; set; } = DateTime.Now;
        [SqlField]
        public DateTime UpdateTime { get; set; } = DateTime.Now;

        [SqlField]
        public string Creator { get; set; } = string.Empty;
        [SqlField]
        public string Modifier { get; set; } = string.Empty;

        [SqlField]
        public string investProject { get; set; } = string.Empty;

        [SqlField]
        public string investContribution { get; set; } = string.Empty;

        [SqlField]
        public string investSummary  { get; set; } = string.Empty;

        [SqlField]
        public string postInvest { get; set; } = string.Empty;

        [SqlField]
        public string investFocus { get; set; } = string.Empty;


        [SqlField]
        public string investMiss { get; set; } = string.Empty;


        [SqlField]
        public string summary { get; set; } = string.Empty;

        [SqlField]
        public int year { get; set; }
        [SqlField]
        public int status { get; set; }
        [SqlField]
        public string type { get; set; } = string.Empty;

        public bool isOperate { get; set; }

        [SqlField]
        public string  investQuit { get; set; } = string.Empty;
        [SqlField]
        public string investStatus { get; set; } = string.Empty;

        [SqlField]
        public string companyStream { get; set; } = string.Empty;
        [SqlField]
        public string projectMiss { get; set; } = string.Empty;
        [SqlField]
        public string roleModel { get; set; } = string.Empty;
        [SqlField]
        public string companyLearn { get; set; } = string.Empty;
        [SqlField]
        public string goodStaff { get; set; } = string.Empty;
        [SqlField]
        public string nextYearWork { get; set; } = string.Empty;

        [SqlField]
        public string selfImprove { get; set; } = string.Empty;
        [SqlField]
        public string environmentFight { get; set; } = string.Empty;
        [SqlField]
        public string investSuggest { get; set; } = string.Empty;

    }

    public enum AnnualReportStatus
    {
        normal = 1,
        draft = 2, 
        delete = 0,
    }

}
