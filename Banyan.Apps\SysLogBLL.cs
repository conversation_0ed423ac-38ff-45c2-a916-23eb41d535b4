﻿using Banyan.Code;
using Banyan.Domain;
using System;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;

namespace Banyan.Apps
{


    public class SysLogBLL : DAL.Base.BaseDAL<SysLog>
    {
        private readonly AjaxResult ajaxResult = null;
        private MemberBLL _memberBLL;
        private readonly object _memberBLLLock = new object();

        public SysLogBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }

        /// <summary>
        /// 获取MemberBLL实例（延迟初始化）
        /// </summary>
        private MemberBLL MemberBLL
        {
            get
            {
                if (_memberBLL == null)
                {
                    lock (_memberBLLLock)
                    {
                        if (_memberBLL == null)
                        {
                            _memberBLL = new MemberBLL();
                        }
                    }
                }
                return _memberBLL;
            }
        }


        public AjaxResult ProjectHistory()
        {
            Member user = MemberBLL.GetLogOnUser();
            var str = DateTime.Now.AddDays(-14).ToString("yyyy-MM-dd");
            var res = GetListBySql($"select distinct description, AddTime from SysLog where createdBy='{user.RealName}' AND page IN ('MiniApp, Get Project', 'Web, Get Project') AND AddTime > '{str}' order by AddTime Desc ");

            string pattern = @"^(projectID:\d+,Name:)";
            string pattern2 = @"^projectID:(\d+),Name:";
            ajaxResult.data = res.Select(val =>
            {
                var m = Regex.Match(val.Description, pattern);
                var m2 = Regex.Match(val.Description, pattern2);
                if (m.Success && m2.Success)
                {
                    val.LogID = m2.Groups[1].Value.ToInt();
                    val.Project = val.Description.Replace(m.Groups[1].Value, "");
                    val.Page = val.AddTime.ToString("MM-dd dddd", new System.Globalization.CultureInfo("zh-CN"));
                }
                else
                {
                    Logger.Error($"history no name", user.RealName);
                }
                return val;
            }).Distinct(new SysLogComparer()).ToList();
            return ajaxResult;
        }
        public AjaxResult LogFileView(string filename, string projectid, string urls)
        {
            Member user = MemberBLL.GetLogOnUser();
            var log = new SysLog();
            log.Ip = filename;
            log.Page = projectid;
            log.Description = urls;
            log.Action = "view file pics";
            log.AddTime = DateTime.Now;
            log.CreatedBy = user.RealName;
            Add(log);
            return ajaxResult;
        }
        public AjaxResult GetFileViewLog()
        {
            Member user = MemberBLL.GetLogOnUser();
            var str = DateTime.Now.AddDays(-15).ToString("yyyy-MM-dd");
            var res = GetListBySql(@"select
	                                *
                                from
	                                (
	                                select
		                                page,
		                                action,
		                                description,
		                                IP,
		                                max(addtime) as AddTime
	                                from
		                                SysLog
	                                group by
		                                page,
		                                createdBy ,
		                                action,
		                                description,
		                                IP
	                                having
		                                createdBy = '" + user.RealName + @"'
		                                AND action = 'view file pics'
	                                ) as a
                                where
	                                AddTime > '" + str +@"'
                                order by
	                                AddTime Desc");
            ajaxResult.data = res.Select(val =>
            {
                val.Action = val.AddTime.ToString("MM-dd dddd", new System.Globalization.CultureInfo("zh-CN"));
                val.Ip = Path.GetFileNameWithoutExtension(val.Ip);
                return val;
            }).ToList();
            return ajaxResult;
        }

        public override object Add(SysLog model)
        {
            //ClearCache(model);
            Logger.syslog(model.ToString(), model.CreatedBy, model.Project);
            return base.Add(model);
            
        }

        public override bool Update(SysLog model)
        {
            return base.Update(model);
        }

        public override bool Update(SysLog model, string fldList)
        {
            return base.Update(model, fldList);
        }
    }
}
