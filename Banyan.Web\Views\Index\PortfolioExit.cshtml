﻿@using Banyan.Domain
@{
    ViewBag.Name = "退出管理";
    Layout = "/Views/Shared/_Layout.cshtml";
    var rolelist = (List<Banyan.Domain.Role>)ViewData["rolelist"];
    var creatorlist = (List<Banyan.Domain.Member>)ViewData["creatorlist"];
    var manager = ViewData["manager"] as Banyan.Domain.Member;
    var isAdmin = manager.Levels == (byte)Banyan.Domain.MemberLevels.Administrator || manager.Levels == (byte)Banyan.Domain.MemberLevels.SuperUser;
}
<style>

        a {
            color: #4E6EF2;
        }
        .ripple {
            display: inline-block;
            color: red;
            font-weight: bold; 
            animation: ripple 3s linear infinite;
        }
        @@keyframes ripple {
          0% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.17);
          }
          100% {
            transform: scale(1);
          }
        }
</style>
<div class="col-md-12">
    <div class="block">
        <div class="block-header">
            <ol class="breadcrumb">
                <li><i class="si si-pointer"></i></li>
                <li>项目退出管理</li>
            </ol>
        </div>
        <div class="block-content tab-content">
            <div class="tab-pane active">
                @*<template v-for="item in waterArray">
                        <p class="water-mark" :style="{left: item.wid + '%', top: item.hei + '%'}">@(manager.Id)</p>
                    </template>*@
                <div class="row data-table-toolbar">
                    <div class="col-sm-12">
                        <div class="pull-right search-bar"></div>
                        <div class="toolbar-btn-action">
                            <form class="form-inline" method="post" onsubmit="return false;">
                                <div class="form-group">
                                    <select class="form-control" id="keyclass" name="keyclass" size="1">
                                        <option value="0">项目组</option>
                                        @if (rolelist != null && rolelist.Count() > 0)
                                        {
                                            foreach (var ci in rolelist)
                                            {
                                                <option value="@(ci.Id)">@(ci.RoleName)</option>
                                            }
                                        }
                                    </select>
                                </div>
                                <div class="form-group">
                                    <select class="form-control" id="creator" name="creator" size="1">
                                        <option value="">项目负责人</option>
                                        @if (creatorlist != null && creatorlist.Count() > 0)
                                        {
                                            foreach (var creator in creatorlist)
                                            {
                                                <option value="@(creator.RealName)">@(creator.RealName)</option>
                                            }
                                        }
                                    </select>
                                </div>
                                <div class="form-group">
                                    <select class="form-control" id="exitStatus" name="exitStatus" size="1">
                                        <option value="-1">是否完成该退出</option>
                                        <option value="0">退出进行中</option>
                                        <option value="1">已实施退出</option>
                                        <option value="2">未按计划退出</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="title-name">搜索</label>
                                    <input style="width: 370px" class="form-control col-md-3" type="text" id="keyname" name="keyname" placeholder="回车搜索">
                                </div>

                                <div class="form-group">
                                    <a class="btn btn-default" id="dosearch">搜索</a>
                                    <div style="width: 130px" class="form-group">
                                    </div>

                                </div>
                                <div class="form-group">
                                    <div class="input-daterange input-group">
                                        <input class="form-control" type="text" id="startdate" name="startdate" placeholder="开始日期" size="7">
                                        <span class="input-group-addon" style="border-left:none;border-right:none;"><i class="fa fa-chevron-right"></i></span>
                                        <input class="form-control" type="text" id="enddate" name="enddate" placeholder="结束日期" size="7">
                                    </div>
                                </div>

                                <div class="form-group">

                                    <a class="btn btn-default" href="@(Url.Action("PortfolioExitSet","Index"))"><i class="fa fa-plus"></i>&nbsp;&nbsp;新建项目退出</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <table class="layui-hide" id="table-list" lay-filter="list-filter"></table>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="bartpl">
    <div class="btn-group" role="group" data-toggle="buttons">
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="预览" lay-event="preview" data-original-title="预览"><i class="fa fa-eye"></i></button>
        {{#  if(d.IsOperate){ }}
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="编辑" lay-event="modify" data-original-title="编辑"><i class="fa fa-pencil"></i></button>
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="删除" lay-event="delete" data-original-title="删除"><i class="fa fa-times"></i></button>
        <button class="btn btn-xs btn-default" type="button" data-toggle="tooltip" title="评分" lay-event="score" data-original-title="评分"><i class="fa fa-bullseye"></i></button>
        {{#  } }}
    </div>
</script>

@section scripts{
    <style type="text/css">
        .select2-container .selection .select2-selection--single {
            height: 32px;
            line-height: 32px;
        }

        .select2-container--default .selection .select2-selection--single .select2-selection__rendered {
            line-height: 32px;
        }

        .select2-container--default .selection .select2-selection--single .select2-selection__arrow {
            height: 32px;
        }
        .select2-container--default .selection .select2-selection--single {
            border: 1px solid #dcdee2;
        }
    </style>
    <link href="/content/js/plugins/select2/select2.min.css" rel="stylesheet" />
    <link href="/content/js/plugins/select2/select2-bootstrap.min.css" rel="stylesheet" />
    <script type="text/javascript" src="/content/js/plugins/select2/select2.min.js"></script>
    <script type="text/javascript">
        $(function () {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == "admin" && pair[1] == "true") {
                    $("#special").css("display", "inline")
                }
            }
        })
        var queryParams = {}, date = new Date();
        layui.use(['laypage', 'layer', 'table', 'laydate'], function () {
            var laypage = layui.laypage,
                layer = layui.layer,
                laydate = layui.laydate,
                table = layui.table;


            table.render({
                elem: '#table-list'
                , height: 570
                , url: '@(Url.Action("portfolioexitlist", "adminapi"))'
                , page: true
                , method: 'post'
                , cols: [[
                    { field: 'RoleName', title: '项目组', fixed: 'left', width: 80 }
                    , { field: 'creator', title: '创建人', fixed: 'left', width: 80 }
                    , {
                        field: 'Name', title: '项目名', fixed: 'left', width: 120, templet: function (d) {
                            return '<a href="/index/portfolioexitset?preview=1&id='+ d.Id + `">${d.Name}</a>`
                        }
                    }
                    , {
                        field: 'dealID', title: '退出交易', width: 100, templet: function (d) {
                            if (d.dealID) {
                                return "已关联";
                            } else {
                                return "未关联";
                            }
                        }
                    }
                    , { field: 'exitFund',  title: '退出主体', width: 100 }
                    , { field: 'exitAmount', title: '退出金额', width: 100 }
                    , { field: 'exitCost', title: '退出成本', width: 100 }
                    , { field: 'exitRatio', title: '退出部分倍数', width: 120 }
                    , { field: 'investOpinion', title: '投前团队意见', width: 200 }
                    , { field: 'exitManager', title: '退出负责人', width: 100 }
                    , { field: 'portfolioManager', title: '项目负责人', width: 100 }
                    , { field: 'postInvestManager', title: '投后负责人', width: 100 }
                    , { field: 'exitMember', title: '退出成员', width: 100 }
                    , { field: 'exitContributor', title: '退出贡献人', width: 100 }
                    , {
                        field: 'exitType', title: '退出方式', width: 200, templet: function (d) {
                            switch (d.exitType) {
                                case "1": return "老股出售";
                                case "2": return "并购";
                                case "3": return "回购、减资";
                                case "4": return "分红";
                                case "5": return "诉讼类项目";
                                case "6": return "清盘类项目";
                                default: return "";
                            }
                        }
                    }
                    , { field: 'exitPlan', title: '退出安排', width: 200 }
                    , { field: 'legalOpinion', title: '法务退出意见', width: 200 }
                    , { field: 'remark', title: '备注', width: 200 }
                    , {
                        field: 'createdDate', title: '创建日期', width: 110, templet: function (d) {
                            var tmpTime = (new Date(parseInt(d.createdDate.replace("/Date(", "").replace(")/", "").split("+")[0]))).pattern("yyyy-MM-dd");
                            return tmpTime == "1-01-01" ? "" : tmpTime ;
                        }
                    }
                    , {
                        field: 'modifiedDate', title: '修改日期', width: 110, templet: function (d) {
                            var tmpTime = (new Date(parseInt(d.modifiedDate.replace("/Date(", "").replace(")/", "").split("+")[0]))).pattern("yyyy-MM-dd");
                            return tmpTime == "1-01-01" ? "" : tmpTime;
                        }
                    }
                    , { fixed: 'right', width: 120, align: 'center', toolbar: '#bartpl' }
                ]],
                done: function (res) {
                    if (res.msg == 'nologin') {
                        window.location.href = '/login/index'
                    }
                }
            });

            table.on('tool(list-filter)', function (obj) {
            var data = obj.data
                , layEvent = obj.event;

            if (layEvent === 'delete') {
                layer.confirm('确认删除该项目吗？', function (index) {
                    layer.close(index);
                    $.ajax({
                        type: 'POST',
                        url: '@(Url.Action("portfolioExitDelete", "adminapi"))',
                        data: { id: obj.data.Id },
                        success: function (data) {
                            if (data.code == 0) {
                                layer.msg('操作成功！');
                                $('#dosearch').click();
                            } else {
                                layer.msg(data.msg);
                            }
                        },
                        error: function () {
                            layui.layer.msg("很抱歉，请求异常！");
                        }
                    });
                });
            } else if (layEvent === 'modify') {
                window.location.href = "/index/portfolioexitset?id=" + data.Id;
            } else if (layEvent === 'preview') {
                window.location.href = "/index/portfolioexitset?preview=1&id=" + data.Id;
            } else if (layEvent === 'score') {
                window.location.href = '/index/exitscore/' + data.Id;
            }
            return;
        });

        laypage.render({
            elem: 'pageBar'
            , count: 100
            , jump: function (obj, first) {
                if (!first) {
                    layer.msg('第' + obj.curr + '页');
                }
            }
        });
        table.on('rowDouble(list-filter)', function (obj) {
            console.log(obj)
            preview(obj.data.Id);
                //obj 同上
        });
        laydate.render({
            elem: '#startdate',
            done: dosearch
        });

        laydate.render({
            elem: '#enddate',
            done: dosearch
        });
        $('#keyname').on('keypress', function(event) {
            if (event.keyCode === 13) {
                $('#dosearch').trigger('click');
            }
        });
        $('#dosearch').on('click', dosearch);
        function dosearch() {
            queryParams = {
                ToRoleId: $('#keyclass').val(),
                Name: $('#keyname').val(),
                startdate: $('#startdate').val(),
                enddate: $('#enddate').val(),
                Creator: $('#creator').val(),
                exitStatus: $('#exitStatus').val(),
            }
            table.reload('table-list', {
                where: queryParams, page: { curr: 1 },
            });
        }

        $('#creator').on('change', dosearch);
        $('#keyclass').on('change', dosearch);
        $('#startdate').on('change', dosearch);
        $('#exitStatus').on('change', dosearch);

        timer = false;
        debounce = function () {
            clearTimeout(timer);
            timer = setTimeout(dosearch, 500);
        }

    });
        //function preview(id) {
        //    var h = document.documentElement.clientHeight || document.body.clientHeight;
        //    layer.open({
        //        type: 2,
        //        area: ['850px', h*0.82 + 'px'],
        //        fix: false,
        //        maxmin: true,
        //        anim: 5,
        //        shade: 0,
        //        title: "项目预览",
        //        content: '/index/preview?id=' + id,
        //    });
        //}
    </script>
}
