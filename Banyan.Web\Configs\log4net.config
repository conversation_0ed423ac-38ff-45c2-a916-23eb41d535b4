﻿<?xml version="1.0" encoding="utf-8" ?>
<log4net>
    <appender name="errorAppender" type="log4net.Appender.RollingFileAppender">
        <filter type="log4net.Filter.LevelMatchFilter">
            <levelToMatch value="ERROR" />
        </filter>
        <filter type="log4net.Filter.DenyAllFilter" />
        <File value="Logs\err.log" />
        <appendToFile value="true" />
        <rollingStyle value="Date" />
        <datePattern value="yyyyMMdd" />
        <layout type="log4net.Layout.PatternLayout">
            <conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
        </layout>
    </appender>
    <appender name="infoAppender" type="log4net.Appender.RollingFileAppender">
        <filter type="log4net.Filter.LevelMatchFilter">
            <levelToMatch value="INFO" />
        </filter>
        <filter type="log4net.Filter.DenyAllFilter" />
        <File value="Logs\info.log" />
        <appendToFile value="true" />
        <rollingStyle value="Date" />
        <datePattern value="yyyyMMdd" />
        <layout type="log4net.Layout.PatternLayout">
            <conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
        </layout>
    </appender>
    <appender name="debugAppender" type="log4net.Appender.RollingFileAppender">
        <filter type="log4net.Filter.LevelMatchFilter">
            <levelToMatch value="DEBUG" />
        </filter>
        <filter type="log4net.Filter.DenyAllFilter" />
        <File value="Logs\debug.log" />
        <appendToFile value="true" />
        <rollingStyle value="Date" />
        <datePattern value="yyyyMMdd" />
        <layout type="log4net.Layout.PatternLayout">
            <conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
        </layout>
    </appender>
    <appender name="perfAppender" type="log4net.Appender.RollingFileAppender">
        <filter type="log4net.Filter.LevelMatchFilter">
            <levelToMatch value="INFO" />
        </filter>
        <filter type="log4net.Filter.DenyAllFilter" />
        <File value="Logs\perf.log" />
        <appendToFile value="true" />
        <rollingStyle value="Date" />
        <datePattern value="yyyyMMdd" />
        <layout type="log4net.Layout.PatternLayout">
            <conversionPattern value="%date %logger - %message%newline" />
        </layout>
    </appender>
    <appender name="GelfUdpAppender" type="Gelf4Net.Appender.GelfUdpAppender, Gelf4Net.UdpAppender">
      <remoteAddress value="*************" />
      <remotePort value="12201" />
      <layout type="Gelf4Net.Layout.GelfLayout, Gelf4Net.UdpAppender">
        <param name="AdditionalFields" value="application_name:ims, Level:%level,  raw_message:%m, user:%property{username}, project:%property{project}, ip:%property{ip}" />
        <param name="IncludeLocationInformation" value="true" />
      </layout>
    </appender>
    <appender name="GelfUdpAppender2" type="Gelf4Net.Appender.GelfUdpAppender, Gelf4Net.UdpAppender">
      <remoteAddress value="**************" />
      <remotePort value="12201" />
      <layout type="Gelf4Net.Layout.GelfLayout, Gelf4Net.UdpAppender">
        <param name="AdditionalFields" value="application_name:ims, Level:%level,  raw_message:%m, user:%property{username}, project:%property{project}, ip:%property{ip}" />
        <param name="IncludeLocationInformation" value="true" />
        <param name="SendTimeStampAsString" value="false"/>
      </layout>
    </appender>

  <!--<appender name="AsyncGelfUdpAppender" type="Gelf4Net.Appender.AsyncGelfUdpAppender, Gelf4Net.UdpAppender">
    -->
  <!-- Number of log lines to buffer for async send. Defaults to 10-->
  <!--
    <bufferSize value="20" />
    -->
  <!-- Number of tasks to use for the async appender. 0 or fewer indicates one task per processor-->
  <!--
    <threads value="2" />
    <remoteAddress value="*************" />
    <remotePort value="12201" />
    <maxChunkSize value="3" />
    <layout type="Gelf4Net.Layout.GelfLayout, Gelf4Net.UdpAppender">
      <param name="AdditionalFields" value="app:AsyncGelfUdpAppender,version:1.0,Environment:Dev,Level:%level" />
      <param name="Facility" value="RandomPhrases" />
      <param name="IncludeLocationInformation" value="true" />
      <param name="SendTimeStampAsString" value="false"/>
    </layout>
  </appender>-->


  <root>
        <level value="ALL" />
        <appender-ref ref="errorAppender" />
        <appender-ref ref="infoAppender" />
        <appender-ref ref="debugAppender" />
        <appender-ref ref="GelfUdpAppender" />
        <appender-ref ref="GelfUdpAppender2" />
        <!--<appender-ref ref="AsyncGelfUdpAppender" />-->
    </root>
    <logger name="Performance" additivity="false">
      <level value="ALL" />
      <appender-ref ref="perfAppender" />
    </logger>
</log4net>