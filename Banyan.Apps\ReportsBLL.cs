﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.Linq;
using System.Text.RegularExpressions;

namespace Banyan.Apps
{
    public class ReportsBLL : BaseDAL<Reports>
    {
        private MemberBLL memberBll = new MemberBLL();
        private readonly AjaxResult ajaxResult = null;
        private SysLogBLL logBLL = new SysLogBLL();
        private ProjectBLL projectBll = new ProjectBLL();
        public ReportsBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }
        private string getStrWhere(NameValueCollection paramValues, Member user, out string sort, bool searchNameOnly = false, bool ReportsTab = true)
        {
            string strWhere = $" id <> -1 ";

            string startDate = WebHelper.GetValue("startdate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(startDate))
            {
                strWhere += $"AND PubTime>='{startDate}' ";
            }

            string endDate = WebHelper.GetValue("enddate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(endDate))
            {
                strWhere += $"AND PubTime<'{Convert.ToDateTime(endDate).AddDays(1).ToString("yyyy-MM-dd")}' ";
            }

            string industry = WebHelper.GetValue("industry", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(industry))
            {
                strWhere += $"AND industry='{industry}' ";
            }

            sort = " PubTime DESC ";

            string title = WebHelper.GetValue("keywords", string.Empty, paramValues);
            title = title.Replace("'", "");
            title = title.ToLower();

            if (!string.IsNullOrWhiteSpace(title))
            {
                //strWhere += $"AND (Title like '%{title}%' OR Content like '%{title}%') ";
                strWhere += $"AND (Title like '%{title}%' OR shortname like '%{title}%' OR contains(content, '\"{title}\"')  ";
                //if (title.Contains("."))
                //{
                //    var spaceName = title.Replace(".", " ");
                //    var connectName = title.Replace(".", "");
                //    strWhere += " or contains(content, '\"" + spaceName + "\"')";
                //    strWhere += " or contains(content, '\"" + connectName + "\"')";
                //}
                strWhere += ") ";
            }


            string type = WebHelper.GetValue("classify", string.Empty, paramValues);
            if (!string.IsNullOrWhiteSpace(type))
            {
                strWhere += $" AND classify='{type}' ";
            }
           return strWhere;
        }
        public Reports GetModel(Member user, int id)
        {
            var res = base.GetModel(id);
            updateLog("MiniApp, Get Reports", "view", res.Classify, user, res.Title);
            return res;
        }
        public AjaxResult GetReports(NameValueCollection paramValues, bool ReportsTab = true)
        {
            #region 用户信息
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var user = new MemberBLL().GetModelByCache(uid); //GetModelByCache(uid);
            if (user == null || user.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = user == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = user == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }
            else if (user.RealName == "作测试")
            {
                ajaxResult.data = null;
                ajaxResult.code = (int)ResultCode.success;
                return ajaxResult;
            }
            #endregion

            #region 交互基本参数
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);

            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            string keyWords = WebHelper.GetValue("keywords", string.Empty, paramValues);
            string searchName = WebHelper.GetValue("searchName", string.Empty, paramValues);

            var ProjectList = new List<Reports>();
            string sort;
            string strWhere = getStrWhere(paramValues, user, out sort, !searchName.IsEmpty(), ReportsTab);

            var res = new List<Reports>();
            ProjectList = GetList(strWhere, pageSize, pageIndex, "id, title, content, pubTime, createTime, classify, url, attach_url,sRatingName, orgName,researcher, shortname,industry", sort);
            //if (ReportsTab)
            //{
            if (!string.IsNullOrWhiteSpace(keyWords))
            {
                var spaceName = "";
                var connectName = "";
                //if (keyWords.Contains("."))
                //{
                //    spaceName = keyWords.Replace(".", " ");
                //    connectName = keyWords.Replace(".", "");
                //}
                foreach (var i in ProjectList)
                {
                    MatchSentence(keyWords, res, spaceName, connectName, i, false);
                }
            }
            else
            {
                res = ProjectList.Select(val =>
                {
                    val.Content = val.Content.Trim();
                    val.Subject = val.Content;
                    return val;
                }).ToList();
            }
            //}
            //else
            //{
            //    res = ProjectList;
            //}
            ajaxResult.count = GetCount(strWhere);
            #endregion

            ajaxResult.data = res;

            updateLog("MiniApp, Get Reports List res count " + ajaxResult.count, "view", strWhere, user);
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;
        }
        public AjaxResult ProjectedRelatedReportsHelper(Member user, int projectId)
        {
            if (projectId <= 0)
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                ajaxResult.msg = "参数不合法！";
                return ajaxResult;
            }
            var ProjectModel = projectBll.GetModelByCache(projectId);
            ajaxResult.data = GetRelatedProjects(ProjectModel.Name);
            if ((ajaxResult.data as List<Reports>)?.Count > 0)
            {
                updateLog("MiniApp, match project " + ProjectModel.Name + " Get Reports List res count " + (ajaxResult.data as List<Reports>)?.Count, "view", "", user);
            }
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;
        }

        public AjaxResult ProjectedRelatedReports(NameValueCollection paramValues)
        {
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var user = new MemberBLL().GetLogOnUser(uid);
            if (user == null || user.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = user == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = user == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }
            int projectId = WebHelper.GetValueInt("pid", 0, paramValues);
            return ProjectedRelatedReportsHelper(user, projectId);
        }

        public AjaxResult ProjectedRelatedReportsWeb(NameValueCollection paramValues)
        {
            Member user = memberBll.GetLogOnUser();
            if (user == null || user.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = user == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = user == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }
            int projectId = WebHelper.GetValueInt("pid", 0, paramValues);
            return ProjectedRelatedReportsHelper(user, projectId);
        }

        public List<Reports> GetRelatedProjects(string name)
        {
            var res = new List<Reports>();
            name = name.Replace("(revisit)", "").Trim();
            name = name.Replace("'", "");

            var spaceName = "";
            var connectName = "";
            var searchStr = "contains(content, '\"" + name + "\"')";
            if (name.Contains("."))
            {
                spaceName = name.Replace(".", " ");
                connectName = name.Replace(".", "");
                searchStr += " or contains(content, '\"" + spaceName + "\"')";
                searchStr += " or contains(content, '\"" + connectName + "\"')";
            }

            var tmpres = GetListBySql("select * from Reports as N  where N.classify<>'政策新闻' and " + searchStr + " ORDER BY N.id DESC");
            //GetListBySql("select * from Reports as N inner join containstable(Reports, content,'\""
            //+ name + "\"') as CT on N.id = CT.[KEY] where N.classify<>'政策新闻' ORDER BY N.id DESC"
            //);
            if (tmpres.Count > 6)
            {
                tmpres = GetListBySql("select * from Reports as N  where N.classify<>'政策新闻' and classify='融资' and " + searchStr + " ORDER BY N.id DESC");
            }
            if (tmpres.Count <= 6)
            {
                foreach (var document in tmpres)
                {
                    MatchSentence(name, res, spaceName, connectName, document);
                }
            }
            return res;
        }

        public static void MatchSentence(string name, List<Reports> res, string spaceName, string connectName, Reports document, bool addDate = true)
        {
            string content = document.Content;
            // 提取包含关键词的句子
            string[] sentences = Regex.Split(content, @"[。,，；\n!?]");
            foreach (string sentence in sentences)
            {
                if (sentence.ToLower().Contains(name.ToLower()))
                {
                    document.Subject = Regex.Replace(sentence, "^<h1>", "").Replace(name, $"<span class='highlight-Reports'>{name}</span>") + "...";
                    document.Subject = Regex.Replace(document.Subject, @"^<br><br>", "", RegexOptions.Multiline);
                    if (addDate)
                    {
                        document.Subject = $"<span class='reports-date'>{document.PubTime.ToString("yyyy/MM/dd")}</span> " + document.Subject;
                    }
                    else
                    {
                        document.Subject = "\'" + document.Subject + "\'";
                    }
                    res.Add(document);
                    break;
                }
                if (name.Contains("."))
                {
                    if (sentence.ToLower().Contains(spaceName.ToLower()) || sentence.ToLower().Contains(connectName.ToLower()))
                    {
                        document.Subject = Regex.Replace(sentence, "^<h1>", "").Replace(name, $"<span class='highlight-Reports'>{name}</span>") + "...";
                        document.Subject = Regex.Replace(document.Subject, @"^<br><br>", "", RegexOptions.Multiline);
                        if (addDate)
                        {
                            document.Subject = $"<span class='reports-date'>{document.PubTime.ToString("yyyy/MM/dd")}</span> " + document.Subject;
                        }
                        else
                        {
                            document.Subject = "\'" + document.Subject + "\'";
                        }
                        res.Add(document);
                        break;
                    }
                }

            }
        }

        public void updateLog(string page, string action, string description, Member user, string project = "")
        {
            SysLog log = new SysLog
            {
                Page = page,
                Action = action,
                Name = project.Substring(0, Math.Min(project.Length, 200)),
                Project = project,
                Description = description,
                CreatedBy = user == null ? "" : user.RealName,
                Ip = Utility.WebHelper.GetIP(),
                CreatorId = user == null ? 0 : user.Id,
            };
            logBLL.Add(log);
        }

    }
}
