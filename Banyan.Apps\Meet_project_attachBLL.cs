﻿using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Collections.Specialized;

namespace Banyan.Apps
{
    public class Meet_project_attachBLL : BaseDAL<Meet_project_attach>
    {
        private readonly AjaxResult ajaxResult = null;

        private class SearchAttachment
        {
            public int Id;
            public string Name;
            public int SourceId;
            public string AtSuffix;
            public string AddTime;
            public string Path;
        }
        public Meet_project_attachBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
        }
        public AjaxResult searchByName(NameValueCollection paramValues)
        {
            string Name = WebHelper.GetValue("Name", string.Empty, paramValues);
            var ProjectList = GetList($" AtName like '%{Name}%' ", int.MaxValue, 1, "*", "AtName DESC");
            var result = ProjectList.Select(val =>
            {
                var tmp = new SearchAttachment();
                tmp.Id = val.Id;
                tmp.Name = val.AtName;
                tmp.AtSuffix = val.AtSuffix;
                tmp.SourceId = val.SourceId;
                tmp.AddTime = val.AddTime.ToString("yyyy-MM-dd");
                tmp.Path = val.Path;
                return tmp;
            }).ToList();
            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = result;
            return ajaxResult;
        }
        private string getStrWhere(NameValueCollection paramValues, Member user, out string sort, bool withoutName = false)
        {
            string strWhere = " id <> -1 ";
            if (!withoutName)
            {
                strWhere = $" pstatus<>{(int)ProjectStatus.delete} ";
            }

            if (!(user.Levels == (int)MemberLevels.Administrator || user.Levels == (int)MemberLevels.SuperUser))
            {
                strWhere += $" AND Creator='{user.RealName}' ";
                //strWhere += $" AND AtSuffix != 'RECORD'  AND (SourceType=1 AND  ( pcreator='{user.RealName}' OR DDManager like '%{user.RealName}%' OR PManager like '%{user.RealName}%' OR pgroupMember like '%{user.RealName}%' OR PInternalPTCP like '%{user.RealName}%' ";

                //if (user.Levels != (int)MemberLevels.LimitedUser)
                //{
                //    string roleIds = string.IsNullOrEmpty(user.Groups) ? "0" : $"{user.Groups}";
                //    roleIds += ",6";//其他组均可见
                //    if (!string.IsNullOrEmpty(roleIds))
                //        strWhere += $" OR (pToRoleId in({roleIds}) AND pIsPrivate = 0) ";
                //}

                //strWhere += $" ) OR ( SourceType=2  AND (pcreator like '%{user.RealName}%' OR pInternalPTCP like '%{user.RealName}%') ) )";
            }

            string suffix = WebHelper.GetValue("suffix", string.Empty, paramValues);
            if (!string.IsNullOrWhiteSpace(suffix))
            {
                if (suffix.Equals("MEET")) // 会议附件
                {
                    strWhere += $@" AND (AtSuffix = 'DD' AND SourceType=2 ) ";
                }
                else if (suffix.Equals("DD")) // 项目附件
                {
                    strWhere += $@" AND (AtSuffix = 'DD' AND SourceType=1 ) ";
                }
                else
                {
                    strWhere += $@" AND (AtSuffix = '{suffix}' ) ";
                }

            }
            if (!withoutName)
            {
                string Name = WebHelper.GetValue("Name", string.Empty, paramValues);
                Name = Name.Replace("'", "''");

                if (!string.IsNullOrWhiteSpace(Name))
                {
                    strWhere += $@" AND (AtName like '%{Name}%' OR pname like '%{Name}%' OR Content like '%{Name}%' ) ";

                }

            }
            string startDate = WebHelper.GetValue("startdate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(startDate))
            {
                strWhere += $"AND AddTime>='{startDate}' ";
            }
            if (user.limitedJoinTime)
            {
                strWhere += $"AND AddTime>='{user.AddTime.ToString("yyyy-MM-dd")}' ";
            }

            string endDate = WebHelper.GetValue("enddate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(endDate))
            {
                strWhere += $"AND AddTime<'{Convert.ToDateTime(endDate).AddDays(1).ToString("yyyy-MM-dd")}' ";
            }

            sort = " AddTime DESC ";

            return strWhere;
        }

        public List<Meet_project_attach> searchCommon(NameValueCollection paramValues, Member user,
           int pageIndex, int pageSize, out int count)
        {
            string sort;

            string Name = WebHelper.GetValue("Name", string.Empty, paramValues);
            List<Meet_project_attach> ProjectList = null;
            if (string.IsNullOrEmpty(Name))
            {
                string strWhere = getStrWhere(paramValues, user, out sort);

                Logger.Info("attachmentsPage search str: " + strWhere, user.RealName);

                ProjectList = GetList(strWhere, pageSize, pageIndex, "*", sort);
                count = GetCount(strWhere);
            } else
            {
                string strWhere = getStrWhere(paramValues, user, out sort, true);
                var list = new AttachmentBLL().searchList(strWhere, Name);
                if(list.Count > 0)
                {
                    string ids = string.Join(",", list.Select(x => x.Id).ToList());

                    ProjectList = GetList($"id in ({ids})");
                    count = ProjectList.Count;
                } else
                {
                    count = 0;
                }
            }
            if (ProjectList != null)
            {
                ProjectList = ProjectList.Select(val =>
                {
                    if (val.Content != null && !val.Content.IsEmpty() && val.Content.Length > 1000)
                    {
                        val.Content = val.Content.Substring(0, 1000);
                    }
                    return val;
                }).ToList();
            }

            
            return ProjectList;
        }

        public AjaxResult GetPageList(NameValueCollection paramValues)
        {
            Member user = new MemberBLL().GetLogOnUser();
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);
            int pageSize = WebHelper.GetValueInt("limit", 10000, paramValues);
            int count;
            var ProjectList = searchCommon(paramValues, user, pageIndex, pageSize, out count);

            ajaxResult.code = (int)ResultCode.success;
            ajaxResult.data = ProjectList;
            ajaxResult.count = count;
            return ajaxResult;
        }
        public List<Meet_project_attach> GetDocList(int sourceId, SourceTypeEnum typeEnum)
        {
            return GetList($"SourceId={sourceId} AND SourceType={(Byte)typeEnum}");
        }
 
        public static string GetDomain()
        {
            return System.Configuration.ConfigurationManager.AppSettings["FileDomain"];
        }


        public Meet_project_attach GetAttachment(int articleId)
        {
            var model = base.GetModel(articleId);
            if (model != null && !string.IsNullOrEmpty(model.AtUrl))
            {
                List<string> fullPathList = new List<string>();
                string filePath = GetDomain();
                foreach (var item in model.AtUrl.Split(','))
                {
                    fullPathList.Add($"{filePath}{item}");
                }
                model.AtUrl = string.Join(",", fullPathList);
            }
            return model;
        }


    }
}
