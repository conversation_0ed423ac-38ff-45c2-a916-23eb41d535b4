﻿using Entity.Base;
using System;
using Utility;

namespace Banyan.Domain
{
    [Serializable]
    [SqlTable(dbEnum.BusinessCenterPortal)]
    public class Contribution
    {
        [SqlField(IsPrimaryKey = true, IsAutoId = true)]
        public int ID { get; set; }

        [SqlField]
        public int projectID { get; set; }

        [SqlField]
        public string username { get; set; }

        [SqlField]
        public string description { get; set; } = string.Empty;

        [SqlField]
        public int percentage { get; set; }

        [SqlField]
        public string creator { get; set; }

        [SqlField]
        public string modifier { get; set; } = string.Empty;
        [SqlField]
        public string projectName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SqlField]
        public DateTime createdDate { get; set; } = DateTime.Now;

        [SqlField]
        public DateTime modifiedDate { get; set; } = DateTime.Now;


    
    }

    [Serializable]
    [SqlTable(dbEnum.BusinessCenterPortal)]
    public class ContributionExt : Contribution
    {
        [SqlField]
        public string Name { get; set; } = string.Empty; // 投后项目通称
    }
}
